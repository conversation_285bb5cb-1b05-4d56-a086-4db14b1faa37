---
description: How I like to code
globs: 
alwaysApply: false
---

My Technical stack :  


## Core Framework and Runtime

- **Next.js**: Full-stack React framework with server-side rendering capabilities
- **React**: Frontend library for building user interfaces
- **TypeScript**: Strongly typed programming language that builds on JavaScript
- **Node.js**: JavaScript runtime for server-side code execution


## Frontend Architecture

### UI Framework and Components

- **React Components**: Custom components for domain-specific UI elements
- **Tailwind CSS**: Utility-first CSS framework for styling
- **Lucide React**: Icon library for consistent iconography
- **Custom UI Components**: Reusable components like buttons, dialogs, dropdowns, and toasts


### State Management

- **React Hooks**: `useState`, `useEffect`, `useCallback` for component-level state
- **Context API**: For sharing state across component trees
- **Custom Hooks**: Domain-specific hooks for reusable logic


### Routing

- **Next.js App Router**: File-system based routing with dynamic segments
- **Dynamic Routes**: Support for parameters like `[id]`, `[countryId]`, `[specialty]`


## Backend Architecture

### API Layer

- **Next.js API Routes**: Server-side API endpoints
- **Server Actions**: For handling form submissions and data mutations


### Database

- **Supabase**: Backend-as-a-Service with PostgreSQL database


### Authentication

- **Supabase Auth**: Email/password authentication
- **Role-based Access Control**: Different flows for doctors vs patients
-SQL databases, never JSON file storage
- Separate databases for dev, test, and prod
- Elasticsearch for search, using elastic.co hosting
- Elastic.co will have dev and prod indexes


## Data Management

### Data Access Patterns

- **Hybrid Data Service**: Combines remote and local data sources
- **Circuit Breaker Pattern**: For resilient API calls to Supabase
- **Static Data Fallbacks**: For offline or degraded operation


### Data Fetching

- **Server-side Data Fetching**: For SEO and performance
- **Client-side Data Fetching**: For interactive components
- **Caching**: In-memory caching for frequently accessed data



## Key Features and Domain-specific Components

- **Doctor Registration System**: Multi-step registration flow
- **Patient Registration System**: User account creation
- **Doctor Rating System**: Allows patients to rate doctors
- **Doctor Profiles**: Detailed information about medical professionals
- **League Tables**: Rankings of doctors by specialty and region
- **Geographic Organization**: Country and specialty-based navigation
- **Review System**: Detailed doctor reviews with multiple criteria


## Performance Optimizations

- **Connection Status Monitoring**: Real-time monitoring of backend connectivity
- **Fallback Data**: Static data when backend is unavailable
- **Rate Limiting Protection**: Circuit breaker to prevent API rate limit issues
- **Timeout Handling**: For unresponsive API calls


## Security Features

- **Email Validation**: Comprehensive email format validation
- **Password Security**: Secure password handling via Supabase Auth
- **Data Sanitization**: Input validation and sanitization


## Design System

- **Medical-themed Components**: Specialized UI elements like ECG dividers
- **Responsive Design**: Mobile-first approach with responsive layouts
- **Accessibility**: Semantic HTML and ARIA attributes



- Python tests
