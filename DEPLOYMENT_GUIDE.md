# Deploy AI Content Generation Edge Function

Since the CLI is having configuration issues, here's how to deploy the function manually:

## Option 1: Deploy via Supabase Dashboard (Recommended)

### Step 1: Login to Supabase Dashboard
1. Go to [supabase.com](https://supabase.com)
2. Login to your account
3. Navigate to your "Doctors League" project

### Step 2: Create the Edge Function
1. In your project dashboard, go to **Edge Functions** (in the left sidebar)
2. Click **Create Function**
3. Function name: `generate-article`
4. Copy and paste the contents of `supabase/functions/generate-article/index.ts` into the editor
5. Click **Deploy Function**

### Step 3: Set Environment Variables
Since we have the Gemini API keys hardcoded, no environment variables are needed for now.

## Option 2: Fix CLI and Deploy (Alternative)

### Step 1: Initialize Supabase Project
```bash
# Remove existing config if corrupted
rm supabase/config.toml

# Initialize new Supabase project
npx supabase init

# Login to Supabase
npx supabase login

# Link to your existing project (replace with your project reference)
npx supabase link --project-ref YOUR_PROJECT_REFERENCE
```

### Step 2: Deploy Function
```bash
# Deploy the specific function
npx supabase functions deploy generate-article
```

## Option 3: Quick Test Without Deployment

For immediate testing, you can also:

1. Temporarily comment out the AI generation call in the frontend
2. Add mock data to test the UI
3. Deploy the function later

### Testing the Function

Once deployed, test it by:

1. Going to `/admin/blog/ai-generate`
2. Enter a specialty (e.g., "Cardiology")
3. Click "Generate Article"
4. Check the Supabase logs for any errors

### Getting Your Project Reference

To find your project reference:
1. Go to your Supabase dashboard
2. Click on your project
3. Go to **Settings** → **General**
4. Copy the **Reference ID**

## Troubleshooting

### Function Not Found Error
- Make sure the function is deployed with the exact name `generate-article`
- Check the function is active in your dashboard

### API Errors
- Gemini API keys are hardcoded in the function
- Check the Supabase logs for detailed error messages

### Database Errors  
- Make sure you've run the SQL script to add AI columns to `blog_posts`
- Check your database permissions

Let me know which option you'd like to try! 