# Doctor's Leagues Application

A modern medical professional ranking and networking platform built with Next.js, Tailwind CSS, and Supabase.

## Overview

Doctor's Leagues is a comprehensive platform that allows medical professionals to compete in specialty-based divisions, connect with patients, and access specialty-specific resources. The application features separate interfaces for doctors and patients, with authentication, profile management, and data visualization.

## Key Features

### For Doctors
- **Registration & Authentication**: Email verification system ensures account security
- **Professional Profile**: Complete profile with specialty, hospital, experience, and country information
- **Dashboard Analytics**: Track patient reviews, statistics, and performance rankings
- **Medical Leagues**: Compete in specialty divisions based on country of practice
- **Resource Library**: Access to specialty-specific medical resources and references
- **Appointment Management**: (Coming soon) View and manage patient appointments

### For Patients
- **Doctor Discovery**: Find and evaluate doctors by specialty, country, and ratings
- **Review System**: Leave reviews and ratings for medical professionals
- **Appointment Booking**: (Coming soon) Schedule appointments with doctors

## Technical Architecture

### Frontend
- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: React Context API
- **Authentication**: Supabase Auth with JWT

### Backend
- **Database**: PostgreSQL via Supabase
- **API**: Supabase REST API
- **File Storage**: Supabase Storage

### Database Schema
- **auth_credentials**: User authentication information
- **countries**: Country information with regional settings
- **specialties**: Medical specialties with descriptions
- **hospitals**: Hospital/clinic information with country association
- **doctors**: Doctor profiles with comprehensive professional information
- **reviews**: Patient reviews and ratings of doctors
- **divisions**: League divisions based on specialty and country

## Design Patterns & System Organization

### Authentication Flow
1. Registration collects credentials and sends verification email
2. Upon verification, user is directed to complete profile
3. Login requires verified email status

### Country-Based Divisions
- Each doctor is associated with a country_id
- Leagues are organized by country and specialty
- Dashboard views and competition are tailored to the doctor's country

### Hospital/Facility Selection
- Hospitals are filtered by selected country during registration
- Each hospital has a country_id foreign key
- Doctors are associated with hospitals in their country

## Development Notes

### Environment Variables
The application requires the following environment variables:
```
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=
```

### Running Locally
```bash
npm install
npm run dev
```

### Deployment
The application is designed to be deployed on Vercel with Supabase integration.

## Future Development

- **Appointment System**: Full appointment scheduling and management
- **Telemedicine Integration**: Video consultations between doctors and patients
- **Enhanced Analytics**: More detailed performance metrics and visualizations
- **Mobile Application**: Native mobile experience for both doctors and patients

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request 