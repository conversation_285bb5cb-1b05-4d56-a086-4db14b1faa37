# Setup Guide: Review Verification and Moderation System

This guide will help you set up the missing database tables and storage bucket for the review verification and moderation system.

## Problem Summary

The admin moderation page shows "No pending verifications" because:
1. The `verification_proofs` table doesn't exist in the database
2. The `review_flags` table doesn't exist in the database  
3. The `verification_status` column might be missing from the `reviews` table
4. The `appointment-verification` storage bucket might not exist

## Step 1: Create Database Tables

1. **Open Supabase Dashboard**
   - Go to your Supabase project dashboard
   - Navigate to the "SQL Editor" section

2. **Run the SQL Script**
   - Copy the contents of `create-verification-tables.sql`
   - Paste it into the SQL Editor
   - Click "Run" to execute the script

   This will create:
   - `verification_proofs` table for storing appointment receipt images
   - `review_flags` table for storing flagged reviews
   - `verification_status` column in the `reviews` table
   - All necessary indexes and Row Level Security policies

## Step 2: Create Storage Bucket

1. **Navigate to Storage**
   - In your Supabase dashboard, go to "Storage"
   - Click "Create a new bucket"

2. **Create the Bucket**
   - Bucket name: `appointment-verification`
   - Make it **Private** (not public)
   - Click "Create bucket"

3. **Configure Bucket Policies**
   - Click on the `appointment-verification` bucket
   - Go to "Policies" tab
   - Add these policies:

   **Policy 1: Allow authenticated users to upload**
   ```sql
   CREATE POLICY "Users can upload verification images" ON storage.objects
       FOR INSERT WITH CHECK (
           bucket_id = 'appointment-verification' 
           AND auth.role() = 'authenticated'
           AND (storage.foldername(name))[1] = 'proofs'
       );
   ```

   **Policy 2: Allow service role to read all**
   ```sql
   CREATE POLICY "Service role can read verification images" ON storage.objects
       FOR SELECT USING (
           bucket_id = 'appointment-verification'
           AND auth.role() = 'service_role'
       );
   ```

   **Policy 3: Allow service role to delete**
   ```sql
   CREATE POLICY "Service role can delete verification images" ON storage.objects
       FOR DELETE USING (
           bucket_id = 'appointment-verification'
           AND auth.role() = 'service_role'
       );
   ```

## Step 3: Test the System

1. **Submit a Review with Verification**
   - Go to a doctor's profile page
   - Submit a review and upload an appointment receipt
   - Check that the review is saved with `verification_status = 'pending_verification'`

2. **Check Admin Moderation**
   - Go to `/admin/login` and log in with admin credentials
   - Navigate to `/admin/moderation`
   - You should now see pending verification requests

## Step 4: Verify Everything Works

Run this SQL query in Supabase SQL Editor to check if everything is set up:

```sql
-- Check if tables exist and have data
SELECT 'verification_proofs' as table_name, count(*) as record_count 
FROM verification_proofs
UNION ALL
SELECT 'review_flags' as table_name, count(*) as record_count 
FROM review_flags
UNION ALL
SELECT 'reviews with verification_status' as table_name, count(*) as record_count 
FROM reviews WHERE verification_status IS NOT NULL;

-- Check recent reviews
SELECT review_id, verification_status, review_date 
FROM reviews 
ORDER BY review_date DESC 
LIMIT 5;
```

## Troubleshooting

**If admin moderation still shows empty:**
1. Check browser console for JavaScript errors
2. Verify that the admin authentication is working
3. Check server logs for any database connection issues

**If file uploads fail:**
1. Verify the storage bucket exists and is named exactly `appointment-verification`
2. Check that the bucket policies are correctly set
3. Ensure the bucket is private (not public)

**If reviews don't save verification status:**
1. Check that the `verification_status` column exists in the `reviews` table
2. Verify that the review submission code is using the updated schema

## Next Steps

After completing this setup:
1. Test submitting a review with an appointment receipt
2. Check that it appears in the admin moderation queue
3. Test approving/rejecting verification requests
4. Verify that doctor scores are updated correctly

The system should now be fully functional for review verification and moderation!
