// Registration Test Script
// This script tests both doctor and patient registration functionality by simulating
// the registration process and verifying database entries.

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Hardcoded values from .env.local as fallback
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || "https://uapbzzscckhtptliynyj.supabase.co";
const SUPABASE_SERVICE_ROLE_KEY = process.env.NEXT_PUBLIC_service_role || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q";

// Create Supabase admin client with service role
const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Test user data
const testDoctorData = {
  username: `testdoctor_${Date.now()}`,
  email: `testdoctor_${Date.now()}@example.com`,
  password: 'TestPassword123!',
  fullname: 'Test Doctor',
  medical_title: 'MD',
  specialty: 'Cardiology',
  facility: 'Test Hospital',
  phone_number: '**********',
  languages_spoken: 'English',
  educational_background: 'Medical School',
  experience: '10'
};

const testPatientData = {
  username: `testpatient_${Date.now()}`,
  email: `testpatient_${Date.now()}@example.com`,
  password: 'TestPassword123!',
  firstName: 'Test',
  lastName: 'Patient',
  age: '35',
  gender: 'male',
  phoneNumber: '**********',
  medicalCondition: 'None',
  city: 'Test City',
  state: 'Test State',
  country: 'Test Country'
};

// Helper function to log test results
function logResult(testName, passed, details = '') {
  if (passed) {
    console.log(`✅ PASSED: ${testName}`);
  } else {
    console.error(`❌ FAILED: ${testName} - ${details}`);
  }
}

// Test doctor registration
async function testDoctorRegistration() {
  console.log('\n=== TESTING DOCTOR REGISTRATION ===');
  let userId = null;
  
  try {
    // Step 1: Create auth user with admin privileges
    console.log('Creating test doctor user in auth...');
    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: testDoctorData.email,
      password: testDoctorData.password,
      email_confirm: true,
      user_metadata: {
        username: testDoctorData.username,
        role: 'doctor',
      }
    });

    if (authError) {
      throw authError;
    }

    userId = authUser.user.id;
    logResult('Create doctor auth user', true, `User ID: ${userId}`);

    // Step 2: Insert into users table
    console.log('Inserting doctor into users table...');
    const userData = {
      username: testDoctorData.username,
      email: testDoctorData.email,
      auth_id: userId,
      user_type: "doctor",
      first_name: testDoctorData.fullname.split(" ")[0],
      last_name: testDoctorData.fullname.split(" ").slice(1).join(" ") || " ",
      gender: "not_specified",
      password: testDoctorData.password,
      "Registration date": new Date().toISOString()
    };

    const { error: userError } = await supabaseAdmin
      .from('users')
      .insert(userData);

    if (userError) {
      throw userError;
    }
    
    logResult('Insert doctor into users table', true);

    // Step 3: Insert into doctors_registrations table (instead of doctors)
    console.log('Inserting into doctors_registrations table...');
    const doctorData = {
      auth_id: userId,
      email: testDoctorData.email,
      fullname: testDoctorData.fullname,
      medical_title: testDoctorData.medical_title,
      specialty: testDoctorData.specialty,
      facility: testDoctorData.facility,
      languages_spoken: testDoctorData.languages_spoken,
      educational_background: testDoctorData.educational_background,
      experience: parseInt(testDoctorData.experience) || 0,
      phone_number: testDoctorData.phone_number, // Include phone_number which is needed in the form
      rating: 0,
      review_count: 0,
      wins: 0,
      draws: 0,
      losses: 0,
      form: "",
      last_updated: new Date().toISOString(),
      country_id: 1,
      specialty_id: 1
    };

    const { error: doctorError } = await supabaseAdmin
      .from('doctors_registration') // Use the correct table name
      .insert(doctorData);

    if (doctorError) {
      throw doctorError;
    }
    
    logResult('Insert doctor into doctors_registration table', true);

    // Step 4: Verify doctor data in database
    console.log('Verifying doctor data in database...');
    const { data: verifyDoctor, error: verifyError } = await supabaseAdmin
      .from('doctors_registration') // Use the correct table for verification
      .select('*')
      .eq('auth_id', userId)
      .single();

    if (verifyError) {
      throw verifyError;
    }

    if (!verifyDoctor) {
      throw new Error('Doctor not found in database after insertion');
    }

    // Verify essential fields
    const fieldsMatch = 
      verifyDoctor.email === testDoctorData.email &&
      verifyDoctor.fullname === testDoctorData.fullname &&
      verifyDoctor.specialty === testDoctorData.specialty;

    if (!fieldsMatch) {
      throw new Error('Doctor data mismatch in database');
    }
    
    logResult('Verify doctor data in database', true);
    console.log('Doctor test data:', verifyDoctor);
    
    return true;
  } catch (error) {
    console.error('Doctor registration test error:', error.message);
    logResult('Doctor registration', false, error.message);
    
    // Clean up if needed
    if (userId) {
      try {
        await cleanupTestUser(userId);
      } catch (cleanupError) {
        console.error('Cleanup error:', cleanupError.message);
      }
    }
    
    return false;
  }
}

// Test patient registration
async function testPatientRegistration() {
  console.log('\n=== TESTING PATIENT REGISTRATION ===');
  let userId = null;
  
  try {
    // Step 1: Create auth user with admin privileges
    console.log('Creating test patient user in auth...');
    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: testPatientData.email,
      password: testPatientData.password,
      email_confirm: true,
      user_metadata: {
        username: testPatientData.username,
        first_name: testPatientData.firstName,
        last_name: testPatientData.lastName,
        role: 'patient',
      }
    });

    if (authError) {
      throw authError;
    }

    userId = authUser.user.id;
    logResult('Create patient auth user', true, `User ID: ${userId}`);

    // Step 2: Insert into users table
    console.log('Inserting patient into users table...');
    
    // First, check the actual structure of the users table to see the available columns
    console.log('Checking users table structure...');
    const { data: tableInfo, error: tableError } = await supabaseAdmin
      .from('users')
      .select('*')
      .limit(1);
      
    if (tableError) {
      console.error('Error checking table structure:', tableError.message);
    } else if (tableInfo && tableInfo.length > 0) {
      console.log('Users table structure:', Object.keys(tableInfo[0]));
    }
    
    // Create user data based on known columns with the new Phone_Number and State fields
    const userData = {
      username: testPatientData.username,
      email: testPatientData.email,
      password: testPatientData.password,
      first_name: testPatientData.firstName,
      last_name: testPatientData.lastName,
      gender: testPatientData.gender,
      city: testPatientData.city,
      country: testPatientData.country,
      user_type: "patient",
      age: parseInt(testPatientData.age) || 0,
      auth_id: userId,
      "Medical Condition": testPatientData.medicalCondition || "",
      "Registration date": new Date().toISOString(),
      // Add new columns if they exist in the table schema
      "Phone_Number": testPatientData.phoneNumber,
      "State": testPatientData.state
    };

    const { error: userError } = await supabaseAdmin
      .from('users')
      .insert(userData);

    if (userError) {
      console.log('Detailed error:', userError);
      
      // If error occurs, try again without the new fields
      console.log('Trying without new fields...');
      const basicUserData = {
        username: testPatientData.username,
        email: testPatientData.email,
        password: testPatientData.password,
        first_name: testPatientData.firstName,
        last_name: testPatientData.lastName,
        gender: testPatientData.gender,
        city: testPatientData.city,
        country: testPatientData.country,
        user_type: "patient",
        age: parseInt(testPatientData.age) || 0,
        auth_id: userId,
        "Medical Condition": testPatientData.medicalCondition || "",
        "Registration date": new Date().toISOString()
      };
      
      const { error: retryError } = await supabaseAdmin
        .from('users')
        .insert(basicUserData);
        
      if (retryError) {
        throw retryError;
      }
    }
    
    logResult('Insert patient into users table', true);

    // Step 3: Verify patient data in database
    console.log('Verifying patient data in database...');
    const { data: verifyPatient, error: verifyError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('auth_id', userId)
      .single();

    if (verifyError) {
      throw verifyError;
    }

    if (!verifyPatient) {
      throw new Error('Patient not found in database after insertion');
    }

    // Verify essential fields
    const fieldsMatch = 
      verifyPatient.email === testPatientData.email &&
      verifyPatient.first_name === testPatientData.firstName &&
      verifyPatient.last_name === testPatientData.lastName &&
      verifyPatient.user_type === 'patient';

    if (!fieldsMatch) {
      throw new Error('Patient data mismatch in database');
    }
    
    logResult('Verify patient data in database', true);
    console.log('Patient test data:', verifyPatient);
    
    // Check if Phone_Number and State fields were added to the database
    if (verifyPatient.Phone_Number) {
      console.log('✅ Phone_Number field exists and was saved successfully');
    }
    
    if (verifyPatient.State) {
      console.log('✅ State field exists and was saved successfully');
    }
    
    return true;
  } catch (error) {
    console.error('Patient registration test error:', error.message);
    logResult('Patient registration', false, error.message);
    
    // Clean up if needed
    if (userId) {
      try {
        await cleanupTestUser(userId);
      } catch (cleanupError) {
        console.error('Cleanup error:', cleanupError.message);
      }
    }
    
    return false;
  }
}

// Helper function to clean up test users
async function cleanupTestUser(userId) {
  console.log(`Cleaning up test user ${userId}...`);
  
  try {
    // Remove from doctors_registration table if exists
    await supabaseAdmin
      .from('doctors_registration')
      .delete()
      .eq('auth_id', userId);
    
    // Also try to remove from doctors table for compatibility
    await supabaseAdmin
      .from('doctors')
      .delete()
      .eq('auth_id', userId);
      
    // Remove from users table
    await supabaseAdmin
      .from('users')
      .delete()
      .eq('auth_id', userId);
      
    // Remove from auth
    await supabaseAdmin.auth.admin.deleteUser(userId);
    
    console.log(`User ${userId} cleaned up successfully`);
  } catch (error) {
    console.error(`Cleanup failed for user ${userId}:`, error.message);
    throw error;
  }
}

// Run all tests
async function runAllTests() {
  console.log('===============================================');
  console.log('REGISTRATION TESTS');
  console.log('===============================================');
  
  try {
    // Test doctor registration
    const doctorResult = await testDoctorRegistration();
    
    // Test patient registration
    const patientResult = await testPatientRegistration();
    
    // Overall result
    if (doctorResult && patientResult) {
      console.log('\n✅ ALL TESTS PASSED');
    } else {
      console.log('\n❌ SOME TESTS FAILED');
    }
  } catch (error) {
    console.error('Test suite error:', error.message);
  }
  
  console.log('\n===============================================');
}

// Execute tests
runAllTests().catch(console.error); 