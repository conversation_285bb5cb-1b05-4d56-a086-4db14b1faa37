'use server'

import { createServerActionClient } from '@supabase/auth-helpers-nextjs'
import { createClient } from '@supabase/supabase-js' // Import standard createClient
import { cookies } from 'next/headers'
import { revalidatePath } from 'next/cache'
import { v4 as uuidv4 } from 'uuid'; // For unique file names
import { Database } from '@/lib/database.types' // Assuming this path is correct

// Define the type for an Ad based on the updated database schema
export type Ad = Database['public']['Tables']['ads']['Row'] & {
  size?: string | null;
  placements?: string[] | null; // Array of placement strings from the database
  target_specialty_name?: string | null;
  // Add custom position fields (already in Row type via Database['public']['Tables']['ads']['Row'])
  custom_top?: string | null;
  custom_bottom?: string | null;
  custom_left?: string | null;
  custom_right?: string | null;
};

// Ensure Insert/Update types reflect the new schema
export type AdInsert = Database['public']['Tables']['ads']['Insert'] & {
  size?: string | null;
  placement: string | string[] | null; // Form input handling
  placements?: string[];
  // Add custom position fields
  custom_top?: string | null;
  custom_bottom?: string | null;
  custom_left?: string | null;
  custom_right?: string | null;
};
// Modify AdUpdate to accept the form's placement type, but omit the DB placement field initially
export type AdUpdate = Omit<Database['public']['Tables']['ads']['Update'], 'placement' | 'placements'> & {
  id: string; // Ensure ID is always present for updates
  size?: string | null;
  placement?: string | string[] | null; // Accept form input type
  // placements?: string[]; // We derive this in the action
  // Add custom position fields
  custom_top?: string | null;
  custom_bottom?: string | null;
  custom_left?: string | null;
  custom_right?: string | null;
};

// Helper function to get Supabase client for server actions
const getSupabaseClient = async (useServiceRole = false) => {
  if (useServiceRole) {
    // Use service role client to bypass RLS for admin operations
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('[getSupabaseClient] Missing required environment variables for service role client')
      throw new Error('Server configuration error: Missing Supabase URL or Service Role Key')
    }
    
    return createClient<Database>(supabaseUrl, supabaseServiceKey, {
      auth: { autoRefreshToken: false, persistSession: false }
    })
  }
  
  // Use regular auth client for normal operations
  return createServerActionClient<Database>({ cookies: () => cookies() })
}

// --- Action: Fetch All Ads (for Admin) ---
export async function getAds(): Promise<{ data: Ad[] | null; error: any }> {
  // Use service role client to bypass RLS policies for admin dashboard
  const supabase = await getSupabaseClient(true)
  console.log("[getAds] Fetching all ads for admin...");
  try {
    const { data, error } = await supabase
      .from('ads')
      .select('*') // Select all columns
      .order('created_at', { ascending: false });

    if (error) {
      console.error('[getAds] Error fetching ads:', error);
      return { data: null, error };
    }
    console.log(`[getAds] Fetched ${data?.length ?? 0} ads.`);
    return { data, error: null };
  } catch (error) {
    console.error('[getAds] Unexpected error fetching ads:', error);
    return { data: null, error };
  }
}

// --- Action: Fetch Ads for Page ---
// Generic function to fetch active ads for a specific page and position
// Queries both the old 'placement' column and the new 'placements' array
export async function getAdsForPage(pageName: string, position: string): Promise<{ data: Ad[] | null; error: any }> {
  // Use service role client to ensure ads are properly fetched with no RLS restrictions
  const supabase = await getSupabaseClient(true);

  // Map page names to possible variations in the database
  const pageVariations: Record<string, string[]> = {
    'home': ['home', 'homepage', 'home-page'],
    'about': ['about', 'about-us', 'aboutus'],
    'standings': ['standings', 'standing'],
    'divisions': ['divisions', 'division'],
    'specialties': ['specialties', 'specialty'],
    'teams': ['teams', 'team'],
    'head-to-head': ['head-to-head', 'headtohead', 'head_to_head'],
    'doctor-profile': ['doctor-profile', 'doctorprofile', 'doctor_profile'],
    'ratings': ['ratings', 'rating']
  };

  // Get all possible variations for this page name
  const pageNames = pageVariations[pageName] || [pageName];

  // Create all possible placement strings
  const placementStrings = pageNames.map(page => `${page}:${position}`);
  console.log(`[getAdsForPage] Attempting to fetch active ads for pageName='${pageName}', position='${position}'. Target placement strings:`, placementStrings);

  try {
    // Build the OR condition for all possible placement strings
    const orConditions = placementStrings.map(str => {
      return `placement.eq.${str},placements.cs.{${str}}`;
    }).join(',');

    const { data, error } = await supabase
      .from('ads')
      .select('*')
      .eq('status', 'active')
      // Use .or() to check all possible placement strings
      .or(orConditions)
      .order('created_at', { ascending: false }); // Consider adding priority sorting later

    // Log the query attempt (optional, Supabase might log this too)
    // console.log(`[getAdsForPage] Executing query: SELECT * FROM ads WHERE status = 'active' AND (${orConditions}) ORDER BY created_at DESC`);

    if (error) {
      console.error(`[getAdsForPage] Supabase error fetching ads for ${placementStrings.join(', ')}:`, JSON.stringify(error, null, 2));
      return { data: null, error };
    }

    console.log(`[getAdsForPage] Successfully fetched ${data?.length ?? 0} active ad(s) for ${placementStrings.join(', ')}.`);
    // Log fetched ad IDs for verification
    if (data && data.length > 0) {
        console.log(`[getAdsForPage] Fetched Ad IDs: ${data.map(ad => ad.id).join(', ')}`);
    }
    return { data, error: null };
  } catch (error) {
    console.error(`[getAdsForPage] Unexpected error fetching ads for ${placementStrings.join(', ')}:`, error);
    return { data: null, error };
  }
}


// --- Specific Placement Fetchers (Updated to use getAdsForPage) ---

// --- Homepage ---
export async function getHomepageBannerAds(): Promise<{ data: Ad[] | null; error: any }> {
  return getAdsForPage('home', 'banner');
}
export async function getHomepageSidebarAds(): Promise<{ data: Ad[] | null; error: any }> {
  return getAdsForPage('home', 'sidebar');
}
export async function getHomepageSideLeftAds(): Promise<{ data: Ad[] | null; error: any }> {
  return getAdsForPage('home', 'side-left');
}
export async function getHomepageSideRightAds(): Promise<{ data: Ad[] | null; error: any }> {
  return getAdsForPage('home', 'side-right');
}
export async function getHomepageBottomAds(): Promise<{ data: Ad[] | null; error: any }> {
    return getAdsForPage('home', 'bottom');
}
export async function getHomepageInContentAds(): Promise<{ data: Ad[] | null; error: any }> {
    return getAdsForPage('home', 'in-content');
}

// --- About Us ---
export async function getAboutUsBannerAds(): Promise<{ data: Ad[] | null; error: any }> {
  return getAdsForPage('about', 'banner');
}
export async function getAboutUsSidebarAds(): Promise<{ data: Ad[] | null; error: any }> {
  return getAdsForPage('about', 'sidebar');
}

// --- Standings ---
export async function getStandingsBannerAds(): Promise<{ data: Ad[] | null; error: any }> {
  return getAdsForPage('standings', 'banner');
}
export async function getStandingsSidebarAds(): Promise<{ data: Ad[] | null; error: any }> {
  return getAdsForPage('standings', 'sidebar');
}

// --- Divisions ---
export async function getDivisionsBannerAds(): Promise<{ data: Ad[] | null; error: any }> {
  return getAdsForPage('divisions', 'banner');
}
export async function getDivisionsSidebarAds(): Promise<{ data: Ad[] | null; error: any }> {
  return getAdsForPage('divisions', 'sidebar');
}

// --- Specialties ---
export async function getSpecialtiesBannerAds(): Promise<{ data: Ad[] | null; error: any }> {
    return getAdsForPage('specialties', 'banner');
}
export async function getSpecialtiesSidebarAds(): Promise<{ data: Ad[] | null; error: any }> {
    return getAdsForPage('specialties', 'sidebar');
}

// --- Teams ---
export async function getTeamsBannerAds(): Promise<{ data: Ad[] | null; error: any }> {
  return getAdsForPage('teams', 'banner');
}
export async function getTeamsSidebarAds(): Promise<{ data: Ad[] | null; error: any }> {
  return getAdsForPage('teams', 'sidebar');
}

// --- Head to Head ---
export async function getHeadToHeadBannerAds(): Promise<{ data: Ad[] | null; error: any }> {
  return getAdsForPage('head-to-head', 'banner');
}
export async function getHeadToHeadSidebarAds(): Promise<{ data: Ad[] | null; error: any }> {
    return getAdsForPage('head-to-head', 'sidebar');
}

// --- Doctor Profile ---
export async function getDoctorProfileTopAds(): Promise<{ data: Ad[] | null; error: any }> {
  return getAdsForPage('doctor-profile', 'banner'); // Or 'top' if that's the exact string
}
export async function getDoctorProfileSidebarAds(): Promise<{ data: Ad[] | null; error: any }> {
  return getAdsForPage('doctor-profile', 'sidebar');
}
export async function getDoctorProfileBottomAds(): Promise<{ data: Ad[] | null; error: any }> {
    return getAdsForPage('doctor-profile', 'bottom');
}

// --- Ratings ---
export async function getRatingsBannerAds(): Promise<{ data: Ad[] | null; error: any }> {
  return getAdsForPage('ratings', 'banner');
}
export async function getRatingsSidebarAds(): Promise<{ data: Ad[] | null; error: any }> {
    return getAdsForPage('ratings', 'sidebar');
}

// --- Legacy / Other ---
export async function getDoctorsListSidebarAds(): Promise<{ data: Ad[] | null; error: any }> {
  return getAdsForPage('doctors_list', 'sidebar');
}


// --- Action: Create Ad ---
export async function createAd(adData: AdInsert): Promise<{ data: Ad | null; error: any }> {
  console.log("[createAd Action] Received data:", JSON.stringify(adData, null, 2));
  // Use service role client to bypass RLS policies
  const supabase = await getSupabaseClient(true)
  try {
    // Prepare data, ensuring correct handling of placements
    const dataToInsert: Omit<Database['public']['Tables']['ads']['Insert'], 'placement'> & { placement: string } = { // Type assertion for workaround
        title: adData.title,
        target_url: adData.target_url,
        start_date: adData.start_date,
        status: adData.status ?? 'draft',
        description: adData.description ?? null,
        end_date: adData.end_date ?? null,
        budget: adData.budget ?? null,
        size: adData.size ?? null,
        target_specialty_id: adData.target_specialty_id ?? null,
        target_locations: adData.target_locations ?? null,
        media_url: adData.media_url ?? null,
        media_type: adData.media_type ?? null,
        placement: 'unknown:unknown', // Workaround: Initialize with default string
        placements: [], // Initialize as empty array
        // Add custom position fields to insert data
        custom_top: adData.custom_top ?? null,
        custom_bottom: adData.custom_bottom ?? null,
        custom_left: adData.custom_left ?? null,
        custom_right: adData.custom_right ?? null,
    };

    // Process placement data from the form (which might be string or string[] or null)
    if (Array.isArray(adData.placement) && adData.placement.length > 0) {
        dataToInsert.placements = adData.placement;
        dataToInsert.placement = adData.placement[0]; // Assign first string
    } else if (typeof adData.placement === 'string' && adData.placement.length > 0) {
        dataToInsert.placements = [adData.placement];
        dataToInsert.placement = adData.placement; // Assign the string
    }
    // If neither condition met, placement remains 'unknown:unknown', placements remains []

    console.log("[createAd Action] Data prepared for insertion:", JSON.stringify(dataToInsert, null, 2));

    console.log("[createAd Action] Attempting Supabase insert...");
    const { data, error } = await supabase
      .from('ads')
      .insert(dataToInsert as Database['public']['Tables']['ads']['Insert']) // Cast back for insert
      .select()
      .single();

    if (error) {
      console.error('[createAd Action] Supabase insert error:', error);
      console.error('[createAd Action] Supabase error details:', JSON.stringify(error, null, 2));
      return { data: null, error };
    }

    console.log("[createAd Action] Supabase insert successful. Data:", JSON.stringify(data, null, 2));
    revalidatePath('/admin/ads');
    revalidatePath('/');
    console.log("[createAd Action] Revalidated paths.");
    return { data, error: null };
  } catch (catchError: any) {
    console.error('[createAd Action] Unexpected exception caught:', catchError);
    console.error('[createAd Action] Exception details:', JSON.stringify(catchError, Object.getOwnPropertyNames(catchError), 2));
    return { data: null, error: catchError };
  }
}

// --- Action: Update Ad ---
export async function updateAd(adId: string, adData: AdUpdate): Promise<{ data: Ad | null; error: any }> {
  console.log(`[updateAd Action] Received data for ID ${adId}:`, JSON.stringify(adData, null, 2));
  // Use service role client to bypass RLS policies
  const supabase = await getSupabaseClient(true)
  try {
    // Prepare data for update by selectively adding fields that exist in adData
    const dataToUpdate: Database['public']['Tables']['ads']['Update'] = {};

    // Add fields only if they are present in the input adData
    if (adData.title !== undefined) dataToUpdate.title = adData.title;
    if (adData.description !== undefined) dataToUpdate.description = adData.description ?? null;
    if (adData.target_url !== undefined) dataToUpdate.target_url = adData.target_url;
    if (adData.start_date !== undefined) dataToUpdate.start_date = adData.start_date;
    if (adData.end_date !== undefined) dataToUpdate.end_date = adData.end_date ?? null;
    if (adData.status !== undefined) dataToUpdate.status = adData.status;
    if (adData.budget !== undefined) dataToUpdate.budget = adData.budget ?? null;
    if (adData.size !== undefined) dataToUpdate.size = adData.size ?? null;
    if (adData.target_specialty_id !== undefined) dataToUpdate.target_specialty_id = adData.target_specialty_id ?? null;
    if (adData.target_locations !== undefined) dataToUpdate.target_locations = adData.target_locations ?? null;
    if (adData.media_url !== undefined) dataToUpdate.media_url = adData.media_url ?? null;
    if (adData.media_type !== undefined) dataToUpdate.media_type = adData.media_type ?? null;
    // Add custom position fields to update data
    if (adData.custom_top !== undefined) dataToUpdate.custom_top = adData.custom_top ?? null;
    if (adData.custom_bottom !== undefined) dataToUpdate.custom_bottom = adData.custom_bottom ?? null;
    if (adData.custom_left !== undefined) dataToUpdate.custom_left = adData.custom_left ?? null;
    if (adData.custom_right !== undefined) dataToUpdate.custom_right = adData.custom_right ?? null;


    // Handle placement/placements conversion if 'placement' field is present in adData
    if (adData.placement !== undefined && adData.placement !== null) {
        if (Array.isArray(adData.placement) && adData.placement.length > 0) {
            // If form sends an array, use it for DB 'placements' and take the first for DB 'placement'
            dataToUpdate.placements = adData.placement;
            dataToUpdate.placement = adData.placement[0];
        } else if (typeof adData.placement === 'string' && adData.placement.trim() !== '') {
            // If form sends a single string, use it for DB 'placement' and wrap in array for DB 'placements'
            dataToUpdate.placements = [adData.placement];
            dataToUpdate.placement = adData.placement;
        } else {
            // Handle empty string or other invalid cases if necessary
            dataToUpdate.placements = [];
            dataToUpdate.placement = 'unknown:unknown'; // Or set to null if DB allows
        }
    }
    // If 'placement' is not in adData, the existing DB values for placement/placements remain unchanged.

    console.log(`[updateAd Action] Data prepared for update (ID: ${adId}):`, JSON.stringify(dataToUpdate, null, 2));

    const { data, error } = await supabase
      .from('ads')
      .update(dataToUpdate)
      .eq('id', adId)
      .select()
      .single();

    if (error) {
      console.error(`[updateAd Action] Error updating ad ${adId}:`, error);
      return { data: null, error };
    }

    console.log(`[updateAd Action] Successfully updated ad ${adId}.`);
    revalidatePath('/admin/ads');
    revalidatePath('/');
    console.log("[updateAd Action] Revalidated paths.");
    return { data, error: null };
  } catch (error) {
    console.error(`[updateAd Action] Unexpected error updating ad ${adId}:`, error);
    return { data: null, error };
  }
}

// --- Action: Delete Ad ---
export async function deleteAd(adId: string): Promise<{ error: any }> {
    // Use service role client to bypass RLS policies
    const supabase = await getSupabaseClient(true);
    try {
        console.log(`[deleteAd] Attempting to delete ad with ID: ${adId}`);
        const { error } = await supabase
            .from('ads')
            .delete()
            .eq('id', adId);

        if (error) {
            console.error(`[deleteAd] Error deleting ad ${adId}:`, error);
            return { error };
        }
        console.log(`[deleteAd] Successfully deleted ad with ID: ${adId}`);

        revalidatePath('/admin/ads');
        revalidatePath('/');
        console.log("[deleteAd] Revalidated paths.");
        return { error: null };
    } catch (error) {
        console.error(`[deleteAd] Unexpected error deleting ad ${adId}:`, error);
        return { error };
    }
}

// --- Action: Update Ad Status ---
export async function updateAdStatus(adId: string, newStatus: Ad['status']): Promise<{ data: Ad | null; error: any }> {
    console.log(`[updateAdStatus] Updating status for ad ${adId} to ${newStatus}`);
    // Pass the adId along with the status in the update object
    return updateAd(adId, { id: adId, status: newStatus });
}


// --- Action: Upload Ad Media ---
const BUCKET_NAME = 'ad-media'; // As confirmed

export async function uploadAdMedia(formData: FormData): Promise<{ mediaUrl: string | null; mediaType: string | null; error: any }> {
  console.log("[uploadAdMedia] Received FormData.");
  const file = formData.get('mediaFile') as File | null;

  if (!file) {
    console.error("[uploadAdMedia] No file found in FormData.");
    return { mediaUrl: null, mediaType: null, error: { message: 'No file provided.' } };
  }
  console.log(`[uploadAdMedia] File found - Name: ${file.name}, Size: ${file.size}, Type: ${file.type}`);

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !serviceRoleKey) {
      const errorPayload = { message: 'Server configuration error: Missing Supabase URL or Service Role Key.' };
      console.error("[uploadAdMedia] Returning error payload:", JSON.stringify(errorPayload));
    return { mediaUrl: null, mediaType: null, error: errorPayload };
  }

  const supabaseAdmin = createClient<Database>(supabaseUrl, serviceRoleKey, {
    auth: { autoRefreshToken: false, persistSession: false }
  });
  console.log("[uploadAdMedia] Supabase service role client initialized for storage.");

  const fileExt = file.name.split('.').pop();
  const mediaType = file.type.startsWith('image/') ? 'image' : file.type.startsWith('video/') ? 'video' : 'other';
  console.log(`[uploadAdMedia] Determined media type: ${mediaType}`);

  if (mediaType === 'other') {
      const errorPayload = { message: `Invalid file type detected: ${file.type}. Only images and videos allowed.` };
      console.error("[uploadAdMedia] Returning error payload:", JSON.stringify(errorPayload));
      return { mediaUrl: null, mediaType: null, error: errorPayload };
  }

  const filePath = `${uuidv4()}.${fileExt}`;
  console.log(`[uploadAdMedia] Generated file path: ${filePath}`);

  try {
    console.log(`[uploadAdMedia] Attempting upload to bucket "${BUCKET_NAME}" with path "${filePath}" using service role.`);
    const { error: uploadError } = await supabaseAdmin.storage
      .from(BUCKET_NAME)
      .upload(filePath, file);

    if (uploadError) {
      console.error('[uploadAdMedia] Error during Supabase upload:', uploadError);
      const errorMessage = typeof uploadError === 'object' && uploadError !== null && 'message' in uploadError
                           ? String(uploadError.message)
                           : 'Unknown Supabase upload error.';
      const errorPayload = { message: errorMessage, details: uploadError };
      console.error("[uploadAdMedia] Returning Supabase upload error payload:", JSON.stringify(errorPayload));
      return { mediaUrl: null, mediaType: null, error: errorPayload };
    }
    console.log(`[uploadAdMedia] File uploaded successfully to Supabase storage at ${filePath}.`);

    console.log(`[uploadAdMedia] Attempting to get public URL for: ${filePath} using service role.`);
    const { data } = supabaseAdmin.storage.from(BUCKET_NAME).getPublicUrl(filePath);

    if (!data?.publicUrl) {
        const errorPayload = { message: 'Failed to get public URL after upload.', details: data };
        console.error("[uploadAdMedia] Returning public URL error payload:", JSON.stringify(errorPayload));
        return { mediaUrl: null, mediaType: null, error: errorPayload };
    }
    console.log(`[uploadAdMedia] Successfully retrieved public URL: ${data.publicUrl}`);

    return { mediaUrl: data.publicUrl, mediaType: mediaType, error: null };

  } catch (error) {
    console.error('[uploadAdMedia] Unexpected error during upload process:', error);
    const errorMessage = error instanceof Error
                         ? error.message
                         : typeof error === 'string'
                           ? error
                           : 'An unexpected error occurred during media upload.';
    const errorPayload = { message: errorMessage, details: error };
    console.error("[uploadAdMedia] Returning caught exception error payload:", JSON.stringify(errorPayload));
    return { mediaUrl: null, mediaType: null, error: errorPayload };
  }
}
