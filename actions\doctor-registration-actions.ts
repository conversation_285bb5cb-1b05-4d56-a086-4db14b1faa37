'use server'

import { createClient } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types'; // Assuming this path is correct
import { v4 as uuidv4 } from 'uuid';

// Helper function to get the service role client (ensure this is ONLY used server-side)
const getServiceRoleClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !serviceRoleKey) {
    console.error("Service Role Client Error: Missing Supabase URL or Service Role Key");
    throw new Error("Server configuration error for service role client.");
  }

  return createClient<Database>(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

// Action to create the authentication user using Admin privileges
export async function createDoctorAuthUser(email: string, password: string, fullName: string) {
  console.log("Action: createDoctor<PERSON><PERSON><PERSON><PERSON> called for email:", email);
  const supabaseAdmin = getServiceRoleClient();
  try {
    const { data, error } = await supabaseAdmin.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: false, // Start with email_confirm false
      user_metadata: {
        full_name: fullName,
        role: "doctor"
      }
    });

    if (error) {
      console.error("Error in createDoctorAuthUser action:", error);
      // Handle specific errors like "already registered"
      if (error.message?.includes("already registered")) {
         return { user: null, error: { message: "Email already registered. Please sign in instead.", code: 'email_exists' } };
      }
      return { user: null, error: { message: error.message || "Failed to create auth user.", details: error } };
    }
    if (!data.user) {
       return { user: null, error: { message: "No user data returned after creation." } };
    }

    console.log("Action: createDoctorAuthUser successful for user ID:", data.user.id);
    return { user: data.user, error: null };
  } catch (e) {
    console.error("Unexpected error in createDoctorAuthUser action:", e);
    return { user: null, error: { message: e instanceof Error ? e.message : "An unexpected error occurred.", details: e } };
  }
}

// Action to resend verification email (using standard client, as user needs to be logged out)
// Note: This might need adjustment based on Supabase's current best practices for triggering verification
// for admin-created users. Sometimes updating the user triggers it, sometimes resend is needed.
export async function resendDoctorVerificationEmail(email: string) {
    console.log("Action: resendDoctorVerificationEmail called for:", email);
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
        console.error("Resend Email Error: Missing Supabase URL or Anon Key");
        return { error: { message: "Server configuration error for resend email." } };
    }

    // Use a standard client with Anon key for this public operation
    const supabaseClient = createClient<Database>(supabaseUrl, supabaseAnonKey);

    try {
        const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'; // Get base URL from env or default
        const redirectUrl = `${baseUrl}/auth/verification-success?type=doctor&email=${encodeURIComponent(email)}`;

        const { error } = await supabaseClient.auth.resend({
            type: 'signup', // Use 'signup' type for initial verification
            email: email,
            options: {
                emailRedirectTo: redirectUrl
            }
        });

        if (error) {
            console.error("Error in resendDoctorVerificationEmail action:", error);
            return { error: { message: error.message || "Failed to resend verification email.", details: error } };
        }

        console.log("Action: resendDoctorVerificationEmail successful for:", email);
        return { error: null };
    } catch (e) {
        console.error("Unexpected error in resendDoctorVerificationEmail action:", e);
        return { error: { message: e instanceof Error ? e.message : "An unexpected error occurred.", details: e } };
    }
}


// Action to upload profile image using Service Role
export async function uploadDoctorProfileImage(userId: string, file: File): Promise<{ profileImageUrl: string | null; error: any }> {
  console.log("Action: uploadDoctorProfileImage called for user:", userId);
  const supabaseAdmin = getServiceRoleClient();
  const BUCKET_NAME = 'doctor-profiles'; // Changed to 'doctor-profiles' as per user's request
  const SUB_FOLDER = 'real_photos'; // New subfolder as per user's request

  try {
    // Step 1: Optimize the image before uploading
    let optimizedFile = file;
    
    // Only optimize if we're in a browser environment where the optimization APIs are available
    if (typeof window !== 'undefined' && file.type.startsWith('image/')) {
      try {
        // Create an image element to load the file
        const img = document.createElement('img');
        const originalFileSizeMB = file.size / (1024 * 1024);
        console.log(`Original image size: ${originalFileSizeMB.toFixed(2)}MB`);
        
        // Convert to a data URL to manipulate with canvas
        const objectUrl = URL.createObjectURL(file);
        
        // Wait for the image to load
        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = reject;
          img.src = objectUrl;
        });
        
        // Create a canvas and resize the image 
        // Maximum dimensions for a profile photo (adjust as needed)
        const MAX_WIDTH = 800;
        const MAX_HEIGHT = 800;
        
        let width = img.width;
        let height = img.height;
        
        // Calculate new dimensions while maintaining aspect ratio
        if (width > height) {
          if (width > MAX_WIDTH) {
            height = Math.round(height * (MAX_WIDTH / width));
            width = MAX_WIDTH;
          }
        } else {
          if (height > MAX_HEIGHT) {
            width = Math.round(width * (MAX_HEIGHT / height));
            height = MAX_HEIGHT;
          }
        }
        
        // Create canvas and draw resized image
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');
        if (!ctx) throw new Error("Couldn't get canvas context");
        
        ctx.drawImage(img, 0, 0, width, height);
        
        // Clean up the object URL
        URL.revokeObjectURL(objectUrl);
        
        // Convert to WebP with decent quality (0.8 = 80%)
        // Lower for more compression, higher for better quality
        const QUALITY = 0.8;
        
        // Get WebP data from canvas
        const webpDataUrl = canvas.toDataURL('image/webp', QUALITY);
        
        // Convert data URL back to blob/file
        const byteString = atob(webpDataUrl.split(',')[1]);
        const mimeString = webpDataUrl.split(',')[0].split(':')[1].split(';')[0];
        const ab = new ArrayBuffer(byteString.length);
        const ia = new Uint8Array(ab);
        
        for (let i = 0; i < byteString.length; i++) {
          ia[i] = byteString.charCodeAt(i);
        }
        
        // Create optimized file with WebP extension
        const optimizedBlob = new Blob([ab], { type: mimeString });
        const optimizedName = file.name.split('.')[0] + '.webp';
        optimizedFile = new File([optimizedBlob], optimizedName, { 
          type: mimeString,
          lastModified: file.lastModified 
        });
        
        const optimizedFileSizeMB = optimizedFile.size / (1024 * 1024);
        console.log(`Optimized to WebP: ${optimizedFileSizeMB.toFixed(2)}MB (${Math.round((1 - optimizedFile.size / file.size) * 100)}% reduction)`);
      } catch (optimizationError) {
        console.warn("Error optimizing image, will proceed with original:", optimizationError);
        // Continue with the original file if optimization fails
      }
    }
    
    // Step 2: Upload the optimized image
    const timestamp = new Date().getTime();
    const fileExt = optimizedFile.name.split('.').pop() || 'webp'; // Default to webp
    const fileName = `${userId}-${timestamp}.${fileExt}`;
    const filePath = `${SUB_FOLDER}/${fileName}`; // Path within the bucket, including subfolder

    console.log(`Uploading profile image to bucket "${BUCKET_NAME}" at path "${filePath}"`);

    const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
      .from(BUCKET_NAME)
      .upload(filePath, optimizedFile, {
        cacheControl: '3600',
        upsert: false // Don't upsert, create unique names
      });

    if (uploadError) {
      console.error("Error in uploadDoctorProfileImage action (upload):", uploadError);
      return { profileImageUrl: null, error: { message: uploadError.message || "Failed to upload image.", details: uploadError } };
    }

    if (!uploadData?.path) {
        return { profileImageUrl: null, error: { message: "Upload successful but no path returned." } };
    }

    console.log("Profile image uploaded, getting public URL for path:", uploadData.path);
    const { data: urlData } = supabaseAdmin.storage.from(BUCKET_NAME).getPublicUrl(uploadData.path);

    if (!urlData?.publicUrl) {
        console.error("Error getting public URL for profile image.");
        // Consider cleanup? await supabaseAdmin.storage.from(BUCKET_NAME).remove([uploadData.path]);
        return { profileImageUrl: null, error: { message: "Failed to get public URL after upload." } };
    }

    console.log("Action: uploadDoctorProfileImage successful. URL:", urlData.publicUrl);
    
    // Try to update the doctor profile with the image URL
    try {
      // Important: Pass JUST the upload path, not prefixed with bucket name
      // The path already includes the subfolder (doctor-profiles)
      const updateResult = await updateDoctorProfileImage(userId, uploadData.path);
      if (updateResult.error) {
        console.warn("Warning: Uploaded image but failed to update profile:", updateResult.error);
        // Continue and return the URL even if the profile update failed
      }

      // Also try to update the doctors_registration table if the doctor is in pending status
      try {
        const registrationUpdateResult = await updateDoctorRegistrationImage(userId, uploadData.path);
        if (registrationUpdateResult.error) {
          console.warn("Warning: Failed to update registration image:", registrationUpdateResult.error);
          // Continue - this is not critical as the main profile was updated
        }
      } catch (regUpdateError) {
        console.warn("Warning: Error updating registration image:", regUpdateError);
        // Continue - this is not critical
      }
    } catch (updateError) {
      console.warn("Warning: Error updating profile with image:", updateError);
      // Continue and return the URL even if the profile update failed
    }
    
    return { profileImageUrl: urlData.publicUrl, error: null };

  } catch (e) {
    console.error("Unexpected error in uploadDoctorProfileImage action:", e);
    return { profileImageUrl: null, error: { message: e instanceof Error ? e.message : "An unexpected error occurred.", details: e } };
  }
}

// New action to update a doctor's profile with the profile image path
export async function updateDoctorProfileImage(userId: string, imagePath: string) {
  console.log("Action: updateDoctorProfileImage called for user:", userId);
  const supabaseAdmin = getServiceRoleClient();
  
  try {
    // IMPORTANT: We need to store the EXACT path as it exists in storage
    // Do NOT modify the path here - we need the exact structure to match the storage path
    // This ensures the utils.getSupabaseProfileImageUrl function can properly construct URLs
    
    // Log the path we're using to update the database
    console.log("Updating doctor profile with exact image path:", imagePath);
    
    // Use type assertion to tell TypeScript this is a valid update
    const updateData = { profile_image: imagePath } as any;
    
    // First try to update by auth_id (UUID)
    const { data, error } = await supabaseAdmin
      .from('doctors')
      .update(updateData)
      .eq('auth_id', userId);
      
    if (error) {
      console.error("Error updating doctor profile image by auth_id:", error);
      
      // If the first update failed, try to find the doctor by numeric ID
      // This is needed because sometimes the userID might be the numeric ID
      const parsedId = parseInt(userId);
      if (!isNaN(parsedId)) {
        console.log("Trying to update doctor profile with numeric ID:", parsedId);
        const { error: secondError } = await supabaseAdmin
          .from('doctors')
          .update(updateData)
          .eq('doctor_id', parsedId);
          
        if (secondError) {
          console.error("Error updating doctor profile image by numeric ID:", secondError);
          return { success: false, error: { message: "Failed to update profile image in database", details: secondError } };
        }
        
        console.log("Successfully updated doctor profile image by numeric ID");
        return { success: true, error: null };
      }
      
      return { success: false, error: { message: "Failed to update profile image in database", details: error } };
    }
    
    console.log("Successfully updated doctor profile image by auth_id");
    return { success: true, error: null };
  } catch (e) {
    console.error("Unexpected error in updateDoctorProfileImage action:", e);
    return { success: false, error: { message: e instanceof Error ? e.message : "An unexpected error occurred.", details: e } };
  }
}

// New action to update a doctor's registration record with the profile image path
export async function updateDoctorRegistrationImage(userId: string, imagePath: string) {
  console.log("=== updateDoctorRegistrationImage START ===");
  console.log("User ID:", userId);
  console.log("Image path:", imagePath);
  const supabaseAdmin = getServiceRoleClient();

  try {
    // First, let's check if the registration record exists
    console.log("Checking if registration record exists...");
    const { data: existingRecord, error: checkError } = await supabaseAdmin
      .from('doctors_registration')
      .select('doctor_id, auth_id, fullname, profile_image')
      .eq('auth_id', userId)
      .single();

    if (checkError) {
      console.error("Error checking existing registration record:", checkError);
      return { success: false, error: { message: "Registration record not found", details: checkError } };
    }

    console.log("Found registration record:", existingRecord);
    console.log("Current profile_image value:", existingRecord.profile_image);

    // Use type assertion to tell TypeScript this is a valid update
    const updateData = { profile_image: imagePath } as any;
    console.log("Update data:", updateData);

    // First try to update by auth_id (UUID)
    console.log("Attempting to update by auth_id...");
    const { data, error } = await supabaseAdmin
      .from('doctors_registration')
      .update(updateData)
      .eq('auth_id', userId)
      .select();

    if (error) {
      console.error("Error updating doctor registration image by auth_id:", error);

      // If the first update failed, try to find the doctor by numeric ID
      // This is needed because sometimes the userID might be the numeric ID
      const parsedId = parseInt(userId);
      if (!isNaN(parsedId)) {
        console.log("Trying to update doctor registration with numeric ID:", parsedId);
        const { data: numericData, error: secondError } = await supabaseAdmin
          .from('doctors_registration')
          .update(updateData)
          .eq('doctor_id', parsedId)
          .select();

        if (secondError) {
          console.error("Error updating doctor registration image by numeric ID:", secondError);
          return { success: false, error: { message: "Failed to update registration image in database", details: secondError } };
        }

        console.log("Successfully updated doctor registration image by numeric ID");
        console.log("Updated record:", numericData);
        return { success: true, error: null };
      }

      return { success: false, error: { message: "Failed to update registration image in database", details: error } };
    }

    console.log("Successfully updated doctor registration image by auth_id");
    console.log("Updated record:", data);
    console.log("=== updateDoctorRegistrationImage SUCCESS ===");
    return { success: true, error: null };
  } catch (e) {
    console.error("=== updateDoctorRegistrationImage EXCEPTION ===");
    console.error("Exception details:", e);
    return { success: false, error: { message: e instanceof Error ? e.message : "An unexpected error occurred.", details: e } };
  }
}

// Action to create the doctor profile in the database using Service Role
// Define the type for the data expected by this action
type DoctorProfileData = Omit<Database['public']['Tables']['doctors_registration']['Insert'], 'auth_id' | 'doctor_id' | 'last_updated'> & {
    // Add any fields that might be calculated or transformed before insertion if needed
};

export async function createDoctorProfile(userId: string, profileData: DoctorProfileData) {
  console.log("Action: createDoctorProfile called for user:", userId);
  const supabaseAdmin = getServiceRoleClient();

  try {
    // Get the maximum doctor_id from the doctors_registration table to generate sequential ID
    let nextDoctorId: number;

    try {
      const { data: maxIdData, error: maxIdError } = await supabaseAdmin
        .from('doctors_registration')
        .select('doctor_id')
        .order('doctor_id', { ascending: false })
        .limit(1)
        .single();
      
      if (maxIdError) {
        if (maxIdError.code === 'PGRST116') {
          // No rows found - start with ID 1
          console.log("No existing doctors found, starting with ID 1");
          nextDoctorId = 1;
        } else {
          console.error("Error getting max doctor_id:", maxIdError);
          return { error: { message: "Failed to generate sequential ID: " + maxIdError.message, details: maxIdError } };
        }
      } else {
        // Use the next ID in sequence
        nextDoctorId = (maxIdData?.doctor_id || 0) + 1;
        console.log(`Generated sequential doctor_id: ${nextDoctorId}`);
      }
    } catch (idError) {
      console.error("Exception getting max doctor_id:", idError);
      // Fallback to a timestamp-based ID in case of error
      nextDoctorId = Math.floor(Date.now() / 1000);
      console.log(`Fallback to timestamp-based ID: ${nextDoctorId}`);
    }

    const dataToInsert: Database['public']['Tables']['doctors_registration']['Insert'] = {
      ...profileData,
      doctor_id: nextDoctorId, // Set the sequential doctor ID
      auth_id: userId, // Link to the auth user
      last_updated: new Date().toISOString(),
      // Ensure all required fields in doctors_registration are present in profileData or added here
      // Example: Set default values if needed
      // approval_status: 'pending', 
    };

    console.log("Inserting doctor profile data:", JSON.stringify(dataToInsert, null, 2));

    const { error } = await supabaseAdmin
      .from('doctors_registration')
      .insert(dataToInsert);

    if (error) {
      console.error("Error in createDoctorProfile action:", error);
      return { error: { message: error.message || "Failed to create doctor profile.", details: error } };
    }

    console.log("Action: createDoctorProfile successful for user:", userId);
    return { error: null };
  } catch (e) {
    console.error("Unexpected error in createDoctorProfile action:", e);
    return { error: { message: e instanceof Error ? e.message : "An unexpected error occurred.", details: e } };
  }
}
