"use server"

import { createServiceRoleClient } from "@/lib/supabase-client"
import { revalidatePath } from "next/cache"

/**
 * Updates the ranking scores for a specific doctor based on verified and unverified reviews
 * @param doctorId - The ID of the doctor to update scores for
 * @returns Promise<{ success: boolean, error?: string }>
 */
export async function updateDoctorScores(doctorId: string) {
  console.log("=== SERVER ACTION: updateDoctorScores STARTED ===")
  console.log(`Updating scores for doctor ID: ${doctorId}`)
  
  try {
    // Create a service role client with full privileges
    const supabase = createServiceRoleClient()
    console.log("Created service role Supabase client")

    // Validate doctorId
    if (!doctorId) {
      console.error("Missing doctorId parameter")
      return { success: false, error: "Doctor ID is required" }
    }

    // Convert doctorId to number for database query
    const doctorIdNum = Number.parseInt(doctorId)
    if (isNaN(doctorIdNum)) {
      console.error("Invalid doctorId format:", doctorId)
      return { success: false, error: "Invalid doctor ID format" }
    }

    console.log("Fetching verified review data...")
    
    // Fetch verified review data
    const { data: verifiedData, error: verifiedError } = await supabase
      .from('reviews')
      .select('rating')
      .eq('doctor_id', doctorIdNum)
      .eq('verification_status', 'verified')
      .not('rating', 'is', null)

    if (verifiedError) {
      console.error("Error fetching verified reviews:", verifiedError)
      return { success: false, error: "Failed to fetch verified review data" }
    }

    console.log("Fetching unverified review data...")
    
    // Fetch unverified review data
    const { data: unverifiedData, error: unverifiedError } = await supabase
      .from('reviews')
      .select('rating')
      .eq('doctor_id', doctorIdNum)
      .eq('verification_status', 'unverified')
      .not('rating', 'is', null)

    if (unverifiedError) {
      console.error("Error fetching unverified reviews:", unverifiedError)
      return { success: false, error: "Failed to fetch unverified review data" }
    }

    console.log(`Found ${verifiedData.length} verified reviews and ${unverifiedData.length} unverified reviews`)

    // Calculate verified review statistics
    const verifiedCount = verifiedData.length
    const verifiedSum = verifiedData.reduce((sum, review) => sum + (review.rating || 0), 0)
    const verifiedAvg = verifiedCount > 0 ? verifiedSum / verifiedCount : 0

    // Calculate unverified review statistics
    const unverifiedCount = unverifiedData.length
    const unverifiedSum = unverifiedData.reduce((sum, review) => sum + (review.rating || 0), 0)
    const unverifiedAvg = unverifiedCount > 0 ? unverifiedSum / unverifiedCount : 0

    console.log("Verified stats:", { count: verifiedCount, avg: verifiedAvg })
    console.log("Unverified stats:", { count: unverifiedCount, avg: unverifiedAvg })

    // Calculate weighted ranking score
    // Formula: (Verified Average Rating * Verified Review Count * 5) + (Unverified Average Rating * Unverified Review Count * 1)
    const rankingScore = Math.round(
      (verifiedAvg * verifiedCount * 5) + (unverifiedAvg * unverifiedCount * 1)
    )

    // Calculate community rating (average of all reviews)
    const totalCount = verifiedCount + unverifiedCount
    const totalSum = verifiedSum + unverifiedSum
    const communityRating = totalCount > 0 ? totalSum / totalCount : 0

    console.log("Calculated scores:", {
      verifiedRating: verifiedAvg,
      communityRating: communityRating,
      rankingScore: rankingScore
    })

    // Update the doctor's record with new scores
    const { error: updateError } = await supabase
      .from("doctors")
      .update({
        verified_rating: verifiedCount > 0 ? Number(verifiedAvg.toFixed(1)) : null,
        rating: totalCount > 0 ? Number(communityRating.toFixed(1)) : null,
        ranking_score: rankingScore,
      })
      .eq("doctor_id", doctorIdNum)

    if (updateError) {
      console.error("Error updating doctor scores:", updateError)
      return { success: false, error: "Failed to update doctor scores" }
    }

    console.log("Doctor scores updated successfully")
    
    // Revalidate relevant paths
    revalidatePath(`/doctors/${doctorId}`)
    revalidatePath(`/doctors`)
    revalidatePath(`/search`)

    console.log("=== SERVER ACTION: updateDoctorScores COMPLETED SUCCESSFULLY ===")
    return { success: true }

  } catch (error: any) {
    console.error("=== SERVER ACTION: updateDoctorScores FAILED ===")
    console.error("Error updating doctor scores:", error)
    return { 
      success: false, 
      error: error.message || "An unexpected error occurred while updating doctor scores" 
    }
  }
}
