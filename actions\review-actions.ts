"use server"

import { createClient } from "@supabase/supabase-js"
import { revalidatePath } from "next/cache"
import { verifyAuth } from "@/app/actions/server-auth-utils"
import { updateDoctorScores } from "./ranking-actions"
import { createServiceRoleClient } from "@/lib/supabase-client"
import { v4 as uuidv4 } from 'uuid'

interface ReviewData {
  doctor_id: number
  clinical_competence: number
  communication_stats: number
  empathy_compassion: number
  time_management: number
  follow_up_care: number
  overall_satisfaction: number
  additional_comments?: string
}

// Create a service role client specifically for this action with proper env credentials
const createServiceRoleClient = () => {
  // Get environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
  
  // Log for debugging
  console.log("Environment variable checks:")
  console.log(`- NEXT_PUBLIC_SUPABASE_URL exists: ${!!supabaseUrl}`)
  console.log(`- SUPABASE_SERVICE_ROLE_KEY exists: ${!!supabaseServiceKey}`)
  console.log(`- URL length: ${supabaseUrl?.length || 0}`)
  console.log(`- Service key length: ${supabaseServiceKey?.length || 0}`)
  
  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error("Supabase URL or service role key is missing in environment variables")
  }
  
  console.log("Creating service role client with env variables")
  // Create client with service role key to bypass RLS
  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

export async function submitReview(formData: FormData) {
  console.log("=== SERVER ACTION: submitReview STARTED ===")
  try {
    // Create a service role client with full privileges
    const supabase = createServiceRoleClient()
    console.log("Created service role Supabase client with env variables")

    // No longer checking for Supabase Auth session
    // We'll trust the user_id passed from the client, which was already verified there
    
    // Get user_id directly from form data
    const userIdStr = formData.get("user_id") as string
    if (!userIdStr) {
      console.error("Missing user_id in form data")
      return { success: false, error: "User information missing. Please try again." }
    }
    
    // Handle both numeric and UUID formats for user_id
    let userId: string | number = userIdStr;
    
    // If it looks like a number, convert it
    if (/^\d+$/.test(userIdStr)) {
      userId = parseInt(userIdStr, 10);
      if (isNaN(userId as number)) {
        console.error("Invalid numeric user_id format:", userIdStr);
        return { success: false, error: "Invalid user information format. Please try again." };
      }
    }
    
    console.log("Using user_id from form data:", userId)

    // Extract form data and ensure doctor_id is a number
    const doctorIdStr = formData.get("doctor_id") as string
    console.log("Received doctor_id:", doctorIdStr)
    
    if (!doctorIdStr) {
      console.error("Invalid doctor_id - empty or undefined:", doctorIdStr)
      return { success: false, error: "Invalid doctor ID. Please try again." }
    }
    
    // Try to parse as integer, allowing for both numeric strings and potential string with hyphen format
    const doctorId = parseInt(doctorIdStr.replace(/-/g, ''), 10)
    if (isNaN(doctorId)) {
      console.error("Failed to parse doctor_id as integer:", doctorIdStr)
      return { success: false, error: "Invalid doctor ID format. Please try again." }
    }
    
    console.log("Using doctor_id:", doctorId)
    
    const clinicalCompetence = Number.parseInt(formData.get("clinical_competence") as string)
    const communicationStats = Number.parseInt(formData.get("communication_stats") as string)
    const empathyCompassion = Number.parseInt(formData.get("empathy_compassion") as string)
    const timeManagement = Number.parseInt(formData.get("time_management") as string)
    const followUpCare = Number.parseInt(formData.get("follow_up_care") as string)
    const overallSatisfaction = Number.parseInt(formData.get("overall_satisfaction") as string)
    const additionalComments = formData.get("additional_comments") as string
    
    // Parse recommendationRating for validation
    const recommendationRating = Number.parseInt(formData.get("recommendation_rating") as string)
    if (isNaN(recommendationRating) || recommendationRating < 1 || recommendationRating > 5) {
      console.log("Invalid recommendation rating:", recommendationRating)
      return { success: false, error: "Please provide a valid recommendation rating" }
    }

    // Calculate average rating
    const rating =
      [
        clinicalCompetence,
        communicationStats,
        empathyCompassion,
        timeManagement,
        followUpCare,
        overallSatisfaction,
      ].reduce((sum, val) => sum + val, 0) / 6

    console.log("Calculated average rating:", rating)

    // Check for existing reviews
    console.log("Checking for existing reviews by this user for this doctor")
    const { data: existingReviews, error: existingReviewsError } = await supabase
      .from("reviews")
      .select("review_id")
      .eq("user_id", userId)
      .eq("doctor_id", doctorId)

    if (existingReviewsError) {
      console.error("Error checking for existing reviews:", existingReviewsError)
    } else if (existingReviews && existingReviews.length > 0) {
      console.log("User has already submitted a review for this doctor:", existingReviews)
      return { success: false, error: "You have already submitted a review for this doctor" }
    }

    console.log("Using Supabase URL:", process.env.NEXT_PUBLIC_SUPABASE_URL || 'Not found')
    
    // Current date in ISO format (YYYY-MM-DD)
    const reviewDate = new Date().toISOString().split('T')[0]
    
    // Insert data with the review into the database
    const { data, error } = await supabase
      .from('reviews')
      .insert({
        user_id: userId,
        doctor_id: doctorId,
        clinical_competence: clinicalCompetence,
        communication_stats: communicationStats,
        empathy_compassion: empathyCompassion,
        time_management: timeManagement,
        follow_up_care: followUpCare,
        overall_satisfaction: overallSatisfaction,
        additional_comments: additionalComments,
        // Only include the column that exists in the database - either Recommendation or recommendation_rating
        Recommendation: recommendationRating, // Use "Recommendation" as the primary column
        rating: rating,
        review_date: reviewDate,
        verification_status: 'unverified' // Set default verification status
      })
      .select('review_id')
      .single()

    if (error) {
      console.error("Error inserting review:", error)
      console.error("Error details:", error.message, error.details, error.hint, error.code)
      
      // Handle specific error codes
      if (error.code === '23505') {
        return { success: false, error: "You have already submitted a review for this doctor" }
      }
      
      if (error.code === '23503') {
        return { success: false, error: "Invalid doctor or user reference. Please try again." }
      }
      
      // Generic error
      console.error("====== DETAILED INSERT ERROR ======")
      console.error("Message:", error.message)
      console.error("Details:", error.details)
      console.error("Hint:", error.hint)
      console.error("Code:", error.code)
      console.error("Full Error Object:", JSON.stringify(error, null, 2))
      console.error("===================================")
      return { success: false, error: `Database error: ${error.message}` }
    }

    console.log("Review successfully inserted:", data)
    const reviewId = data.review_id

    // Handle verification file upload if provided
    const verificationFile = formData.get("verification_file") as File
    if (verificationFile && verificationFile.size > 0) {
      console.log("Processing verification file upload:", verificationFile.name)

      try {
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
        if (!allowedTypes.includes(verificationFile.type)) {
          console.error("Invalid verification file type:", verificationFile.type)
          // Continue with review submission but log the error
        } else if (verificationFile.size > 5 * 1024 * 1024) {
          console.error("Verification file too large:", verificationFile.size)
          // Continue with review submission but log the error
        } else {
          // Upload verification file
          const fileExtension = verificationFile.name.split('.').pop() || 'jpg'
          const uniqueFileName = `${reviewId}-${userId}-${uuidv4()}.${fileExtension}`
          const filePath = `proofs/${uniqueFileName}`

          console.log(`Uploading verification file to: ${filePath}`)

          const { data: uploadData, error: uploadError } = await supabase.storage
            .from('appointment-verification')
            .upload(filePath, verificationFile, {
              cacheControl: '3600',
              upsert: false
            })

          if (uploadError) {
            console.error("Error uploading verification file:", uploadError)
            // Continue with review submission but log the error
          } else {
            console.log("Verification file uploaded successfully:", uploadData.path)

            // Insert record into verification_proofs table
            const { error: insertError } = await supabase
              .from('verification_proofs')
              .insert({
                review_id: reviewId,
                image_path: uploadData.path
              })

            if (insertError) {
              console.error("Error inserting verification proof record:", insertError)
              // Clean up uploaded file if database insert fails
              await supabase.storage
                .from('appointment-verification')
                .remove([uploadData.path])
            } else {
              console.log("Verification proof record inserted successfully")

              // Update review verification status to pending
              const { error: updateError } = await supabase
                .from('reviews')
                .update({ verification_status: 'pending_verification' })
                .eq('review_id', reviewId)

              if (updateError) {
                console.error("Error updating review verification status:", updateError)
              } else {
                console.log("Review verification status updated to pending_verification")
              }
            }
          }
        }
      } catch (verificationError) {
        console.error("Error processing verification file:", verificationError)
        // Continue with review submission even if verification fails
      }
    }

    // Update doctor's community_rating and ranking scores
    console.log("Updating doctor community_rating and ranking scores")
    await updateDoctorRating(doctorId)
    await updateDoctorScores(doctorId.toString())

    // Revalidate the doctor page
    console.log("Revalidating doctor pages")
    revalidatePath(`/doctors/${doctorId}`)
    revalidatePath(`/doctors/${doctorId}/reviews`)

    console.log("=== SERVER ACTION: submitReview COMPLETED SUCCESSFULLY ===")
    return { success: true }
  } catch (error: any) {
    console.error("=== SERVER ACTION: submitReview FAILED ===")
    console.error("Error submitting review:", error)
    // Add more detailed debugging information
    if (error.code) {
      console.error("Error code:", error.code)
    }
    if (error.details) {
      console.error("Error details:", error.details)
    }
    return { success: false, error: error.message || "An unexpected error occurred" }
  }
}

// Helper function to update doctor's community_rating
async function updateDoctorRating(doctorId: number) {
  try {
    // Create a service role client for this operation too
    const supabase = createServiceRoleClient()

    // Get all reviews for this doctor
    const { data: reviews, error: reviewsError } = await supabase
      .from("reviews")
      .select("rating")
      .eq("doctor_id", doctorId)

    if (reviewsError) throw reviewsError

    // Make sure we have valid reviews (filter out any null ratings)
    const validReviews = reviews.filter(review => review.rating !== null && review.rating !== undefined);

    // Calculate new average community_rating
    const totalRating = validReviews.reduce((sum, review) => sum + review.rating, 0)
    const averageRating = validReviews.length > 0 ? totalRating / validReviews.length : 0

    // Update doctor's community_rating and review count
    const { error: updateError } = await supabase
      .from("doctors")
      .update({
        community_rating: averageRating,
        review_count: validReviews.length,
      })
      .eq("doctor_id", doctorId)

    if (updateError) throw updateError
  } catch (error) {
    console.error("Error updating doctor community_rating:", error)
  }
}

/**
 * Server Action for users to upload their appointment receipt for verification
 * @param reviewId - The ID of the review to verify
 * @param formData - FormData containing the image file
 * @returns Promise<{ success: boolean, error?: string }>
 */
export async function requestReviewVerification(reviewId: string, formData: FormData) {
  console.log("=== SERVER ACTION: requestReviewVerification STARTED ===")
  console.log(`Requesting verification for review ID: ${reviewId}`)

  try {
    // Step 1: Verify user authentication
    const { isAuthenticated, user } = await verifyAuth()
    if (!isAuthenticated || !user) {
      console.error("User not authenticated")
      return { success: false, error: "Authentication required" }
    }

    const userId = user.userId
    console.log(`Authenticated user ID: ${userId}`)

    // Step 2: Get the image file from FormData
    const imageFile = formData.get('proof_image') as File
    if (!imageFile || imageFile.size === 0) {
      console.error("No image file provided")
      return { success: false, error: "Please provide a proof image" }
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(imageFile.type)) {
      console.error("Invalid file type:", imageFile.type)
      return { success: false, error: "Please upload a valid image file (JPEG, PNG, or WebP)" }
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (imageFile.size > maxSize) {
      console.error("File too large:", imageFile.size)
      return { success: false, error: "Image file must be smaller than 5MB" }
    }

    console.log(`Processing image file: ${imageFile.name}, size: ${imageFile.size} bytes`)

    // Step 3: Create service role client for storage operations
    const supabase = createServiceRoleClient()

    // Step 4: Generate unique file path
    const fileExtension = imageFile.name.split('.').pop() || 'jpg'
    const uniqueFileName = `${reviewId}-${userId}-${uuidv4()}.${fileExtension}`
    const filePath = `proofs/${uniqueFileName}`

    console.log(`Generated file path: ${filePath}`)

    // Step 5: Upload file to Supabase storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('appointment-verification')
      .upload(filePath, imageFile, {
        cacheControl: '3600',
        upsert: false
      })

    if (uploadError) {
      console.error("Error uploading file to storage:", uploadError)
      return { success: false, error: "Failed to upload proof image. Please try again." }
    }

    console.log("File uploaded successfully:", uploadData.path)

    // Step 6: Insert record into verification_proofs table
    const { error: insertError } = await supabase
      .from('verification_proofs')
      .insert({
        review_id: parseInt(reviewId),
        image_path: uploadData.path
      })

    if (insertError) {
      console.error("Error inserting verification proof record:", insertError)

      // Clean up uploaded file if database insert fails
      await supabase.storage
        .from('appointment-verification')
        .remove([uploadData.path])

      return { success: false, error: "Failed to save verification request. Please try again." }
    }

    console.log("Verification proof record inserted successfully")

    // Step 7: Update review verification status to pending
    const { error: updateError } = await supabase
      .from('reviews')
      .update({ verification_status: 'pending_verification' })
      .eq('review_id', parseInt(reviewId))

    if (updateError) {
      console.error("Error updating review verification status:", updateError)
      return { success: false, error: "Failed to update review status. Please contact support." }
    }

    console.log("Review verification status updated to pending")

    // Revalidate relevant paths
    revalidatePath(`/doctors`)
    revalidatePath(`/reviews`)

    console.log("=== SERVER ACTION: requestReviewVerification COMPLETED SUCCESSFULLY ===")
    return { success: true }

  } catch (error: any) {
    console.error("=== SERVER ACTION: requestReviewVerification FAILED ===")
    console.error("Error requesting review verification:", error)
    return {
      success: false,
      error: error.message || "An unexpected error occurred while requesting verification"
    }
  }
}

