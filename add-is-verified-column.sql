-- Add is_verified column to auth_credentials table
ALTER TABLE auth_credentials ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT false;

-- Create function to verify a user by user_profile_id
CREATE OR REPLACE FUNCTION verify_user(user_id_param TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    success BOOLEAN;
BEGIN
    -- Update the auth_credentials table
    UPDATE auth_credentials
    SET is_verified = true
    WHERE user_profile_id::TEXT = user_id_param;
    
    -- Return success if rows were affected
    GET DIAGNOSTICS success = ROW_COUNT;
    RETURN success > 0;
END;
$$ LANGUAGE plpgsql; 