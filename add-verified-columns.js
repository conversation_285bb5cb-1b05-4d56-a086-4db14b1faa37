// Script to add verified column to users and doctors tables
const { createClient } = require('@supabase/supabase-js');

// Create Supabase client
const supabaseUrl = 'https://uapbzzscckhtptliynyj.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q';
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkColumn(tableName, columnName) {
  try {
    // Get a row to check if the column exists
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);
      
    if (error) {
      console.error(`Error querying ${tableName}:`, error);
      return false;
    }
    
    if (data && data.length > 0) {
      // Check if the column exists
      return columnName in data[0];
    }
    
    console.log(`No data in ${tableName}, assuming column doesn't exist`);
    return false;
  } catch (err) {
    console.error(`Error checking ${columnName} in ${tableName}:`, err);
    return false;
  }
}

async function addVerifiedColumn() {
  console.log('Checking and adding verified columns to tables...');
  
  // Check doctors table
  const doctorsHasVerified = await checkColumn('doctors', 'verified');
  if (!doctorsHasVerified) {
    console.log('Adding verified column to doctors table...');
    try {
      // Use PostgreSQL's ALTER TABLE to add the column
      const query = `
        ALTER TABLE doctors
        ADD COLUMN IF NOT EXISTS verified BOOLEAN DEFAULT false;
      `;
      
      // Execute the query
      // Note: This requires either a direct PostgreSQL connection or a Supabase function with appropriate permissions
      console.log('Please execute this SQL in your database:');
      console.log(query);
    } catch (err) {
      console.error('Error adding verified column to doctors:', err);
    }
  } else {
    console.log('doctors table already has verified column');
  }
  
  // Check users table
  const usersHasVerified = await checkColumn('users', 'verified');
  if (!usersHasVerified) {
    console.log('Adding verified column to users table...');
    try {
      // Use PostgreSQL's ALTER TABLE to add the column
      const query = `
        ALTER TABLE users
        ADD COLUMN IF NOT EXISTS verified BOOLEAN DEFAULT false;
      `;
      
      // Execute the query
      console.log('Please execute this SQL in your database:');
      console.log(query);
    } catch (err) {
      console.error('Error adding verified column to users:', err);
    }
  } else {
    console.log('users table already has verified column');
  }
  
  console.log('Done checking and adding columns');
}

addVerifiedColumn(); 