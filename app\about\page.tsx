import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription, CardFooter } from "@/components/ui/card"
import { Heart, Trophy, Users, Star, Award } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { generateMetadata } from "@/lib/seo-config"
import { 
  Breadcrumbs, 
  FAQSchema, 
  SEOImage, 
  SchemaMarkup,
  SocialMetaTags,
  type FAQItem 
} from "@/components/seo"
import Link from "next/link"
import { generateMedicalWebPageSchema, generateFaqSchema } from "@/lib/schema-utils"

// Add Route Segment Config for static rendering with revalidation
export const revalidate = 86400; // Revalidate this page once per day (in seconds)

// SEO metadata for the About page
export const metadata = generateMetadata(
  "About Doctor's Leagues | Our Mission and Values", 
  "Learn about Doctor's Leagues, the premier platform for ranking medical professionals based on peer reviews, patient outcomes, and research contributions.",
  [
    "medical professional rankings", "doctor ranking platform", "physician comparison website", 
    "medical league standings", "healthcare professional ratings", "doctor's league about"
  ],
  "/about"
)

// Structured data for the About page using schema generator
const aboutPageSchema = generateMedicalWebPageSchema(
  "About Doctor's Leagues",
  "Doctor's Leagues is the premier platform for ranking medical professionals based on peer reviews, patient outcomes, and research contributions.",
  "https://doctorsleagues.com/about",
  "2023-01-01",
  new Date().toISOString().split('T')[0]
)

// Organization schema
const organizationSchema = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  'name': "Doctor's Leagues",
  'description': "Where Healthcare Heroes Compete for Your Trust",
  'url': 'https://doctorsleagues.com',
  'logo': 'https://doctorsleagues.com/logo.png',
  'foundingDate': '2023-01-01',
  'foundingLocation': 'Bahrain',
  'sameAs': [
    'https://twitter.com/DoctorsLeague',
    'https://facebook.com/DoctorsLeague',
    'https://linkedin.com/company/doctors-league',
    'https://instagram.com/doctorsleague'
  ]
}

// FAQ items for the about page
const aboutFaqItems: FAQItem[] = [
  {
    question: "What is Doctor's Leagues?",
    answer: "Doctor's Leagues is a comprehensive platform that ranks medical professionals based on peer reviews, patient outcomes, and research contributions, helping patients make informed decisions about their healthcare providers."
  },
  {
    question: "How are doctors ranked in the leagues?",
    answer: "Doctors are ranked based on a proprietary algorithm that considers three primary factors: peer reviews from other medical professionals, patient outcomes and satisfaction scores, and research contributions to the medical field."
  },
  {
    question: "How can I find doctors in my country?",
    answer: "You can browse doctors by country through our Leagues dropdown menu, which organizes medical professionals by their location. We currently cover all GCC countries including Bahrain, Saudi Arabia, UAE, Kuwait, Oman, and Qatar."
  },
  {
    question: "How can doctors join the platform?",
    answer: "Medical professionals can register through our 'Be a Player' option on the platform. After registration, they'll need to verify their credentials and complete their professional profile to be included in the rankings."
  },
  {
    question: "How can I compare multiple doctors?",
    answer: "You can use our Head-to-Head feature to directly compare up to 5 doctors side by side. This allows you to evaluate their professional metrics, patient reviews, and league standings in a comprehensive way."
  }
]

// Generate FAQ schema
const faqSchema = generateFaqSchema(
  aboutFaqItems.map(item => ({ 
    question: item.question, 
    answer: typeof item.answer === 'string' ? item.answer : 'Please visit our website for a detailed answer.'
  }))
)

export default async function AboutPage() {
  // All schemas for SEO health check
  const allSchemas = [aboutPageSchema, organizationSchema, faqSchema]

  return (
    <div className="relative bg-gradient-to-b from-background via-background to-primary/20 -mt-20 pt-20">
      <div className="container mx-auto px-4 py-8">
        {/* Structured data for SEO */}
        <SchemaMarkup schema={aboutPageSchema} id="about-page-schema" />
        <SchemaMarkup schema={organizationSchema} id="organization-schema" />
        <SchemaMarkup schema={faqSchema} id="faq-schema" />
        
        {/* Social Metadata Tags for sharing */}
        <SocialMetaTags
          title="About Doctor's Leagues | Our Mission and Values"
          description="Learn how Doctor's Leagues is transforming healthcare through our ranking system based on peer reviews, patient outcomes, and research contributions."
          canonicalUrl="https://doctorsleagues.com/about"
          ogImageUrl="https://images.unsplash.com/photo-1504439468489-c8920d796a29?q=80&w=2071"
          ogImageAlt="Medical professionals collaborating in a modern hospital setting"
          ogType="website"
          twitterCardType="summary_large_image"
          siteName="Doctor's Leagues"
        />
        
        {/* Breadcrumbs */}
        <Breadcrumbs
          items={[
            { label: 'About', href: '/about' }
          ]}
          className="mb-8"
        />
        
        {/* Hero section */}
        <div className="relative overflow-hidden rounded-lg mb-8">
          <div className="absolute inset-0 image-overlay z-10"></div>
          <SEOImage 
            src="https://images.unsplash.com/photo-1504439468489-c8920d796a29?q=80&w=2071"
            alt="Medical professionals collaborating in a modern hospital setting"
            width={1200}
            height={400}
            className="w-full h-64 object-cover"
            priority={true}
          />
          <div className="absolute inset-0 z-20 flex flex-col justify-center px-8">
            <h1 className="text-3xl md:text-4xl font-bold about-overlay-text about-overlay-text-custom mb-4" style={{ color: '#ffffff', textShadow: '3px 3px 12px rgba(0, 0, 0, 0.95), 0 0 8px rgba(0, 0, 0, 0.9)', WebkitTextStroke: '1.5px hsl(142, 76%, 36%)', fontWeight: 800 }}>About Doctor's Leagues</h1>
            <p className="about-overlay-text about-overlay-text-custom text-lg max-w-2xl" style={{ color: '#ffffff', textShadow: '2px 2px 8px rgba(0, 0, 0, 0.9), 0 0 6px rgba(0, 0, 0, 0.8)', WebkitTextStroke: '1px hsl(142, 76%, 36%)', fontWeight: 600 }}>
              Where Healthcare Heroes Compete for Your Trust
            </p>
          </div>
        </div>

        {/* Main content */}
        <div className="max-w-4xl mx-auto space-y-8">
          <Card className="border-primary/10">
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2 mb-2">
                <Trophy className="h-5 w-5 text-primary" />
                <CardTitle>Our Mission</CardTitle>
              </div>
              <CardDescription>Transforming how healthcare quality is measured and presented</CardDescription>
            </CardHeader>
            <CardContent className="prose dark:prose-invert">
              <p>
                At Doctor's Leagues, we believe in transparency, excellence, and empowerment in healthcare. Our mission is to create a platform where medical professionals can be recognized for their expertise and patients can make informed decisions about their healthcare providers.
              </p>
              <p>
                By organizing doctors into leagues based on rigorous performance metrics, we're establishing a new standard for medical excellence that benefits both patients and the healthcare community.
              </p>
              <blockquote>
                "We're not just ranking doctors; we're elevating the entire healthcare ecosystem through healthy competition and transparent metrics."
              </blockquote>
            </CardContent>
          </Card>

          <Card className="border-primary/10">
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2 mb-2">
                <Trophy className="h-5 w-5 text-primary" />
                <CardTitle>How We Rank</CardTitle>
                </div>
              <CardDescription>Our comprehensive ranking methodology</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>Our proprietary ranking algorithm evaluates doctors based on three key metrics:</p>
              <div className="grid gap-4 md:grid-cols-3">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Users className="h-4 w-4 text-primary" />
                      Peer Reviews
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm">
                      Evaluations from other medical professionals who have worked with or observed the doctor's practice.
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Heart className="h-4 w-4 text-primary" />
                      Patient Outcomes
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm">
                      Measurable improvements in patient health, satisfaction ratings, and recovery statistics.
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Star className="h-4 w-4 text-primary" />
                      Research Contributions
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm">
                      Published papers, clinical trials, and other contributions to medical advancement.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>

          <Card className="border-primary/10">
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2 mb-2">
                <Users className="h-5 w-5 text-primary" />
                <CardTitle>Our Team</CardTitle>
                </div>
              <CardDescription>Meet the experts behind Doctor's Leagues</CardDescription>
              </CardHeader>
              <CardContent>
              <p className="mb-6">
                Doctor's Leagues was founded by a diverse team of medical professionals, data scientists, and healthcare advocates who share a common vision: to transform how healthcare quality is measured and presented.
              </p>
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                <Card>
                  <CardHeader className="p-4 pb-2">
                    <CardTitle className="text-base">Dr. Sarah Ahmed</CardTitle>
                    <CardDescription>Founder & Chief Medical Officer</CardDescription>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <p className="text-sm text-muted-green">
                      Board-certified cardiologist with 15+ years of experience in healthcare quality metrics.
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="p-4 pb-2">
                    <CardTitle className="text-base">Malik Al-Farsi</CardTitle>
                    <CardDescription>CEO & Co-founder</CardDescription>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <p className="text-sm text-muted-green">
                      Healthcare technology entrepreneur with a background in medical informatics and AI.
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="p-4 pb-2">
                    <CardTitle className="text-base">Dr. Lisa Chen</CardTitle>
                    <CardDescription>Head of Research & Analytics</CardDescription>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <p className="text-sm text-muted-green">
                      PhD in Biostatistics with expertise in healthcare outcome measurements and predictive modeling.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
            <CardFooter className="flex justify-center pb-6">
              <div className="flex flex-wrap gap-2 justify-center">
                <Badge variant="outline" className="flex items-center gap-1 px-3 py-1 border-primary/50 text-foreground">
                  <Award className="w-3.5 h-3.5" />
                  Excellence
                </Badge>
                <Badge variant="outline" className="flex items-center gap-1 px-3 py-1 border-primary/50 text-foreground">
                  <Heart className="w-3.5 h-3.5" />
                  Patient-Centered
                </Badge>
                <Badge variant="outline" className="flex items-center gap-1 px-3 py-1 border-primary/50 text-foreground">
                  <Star className="w-3.5 h-3.5" />
                  Quality-Focused
                </Badge>
              </div>
            </CardFooter>
          </Card>
        </div>

        {/* FAQ Section with structured data */}
        <div className="max-w-4xl mx-auto mt-12 mb-16">
          <FAQSchema 
            items={aboutFaqItems}
            title="Frequently Asked Questions"
            className="mx-auto"
          />
        </div>

        {/* Call to action */}
        <div className="text-center mt-12 mb-8">
          <h2 className="text-2xl font-bold mb-4">Ready to explore healthcare excellence?</h2>
          <p className="text-muted-green max-w-2xl mx-auto mb-6">
            Discover top-rated medical professionals and make informed healthcare decisions with Doctor's Leagues.
          </p>
          <div className="flex justify-center gap-4 flex-wrap">
            <Link href="/standings">
              <Button size="lg" className="px-8">
                Explore Rankings
              </Button>
            </Link>
            <Link href="/head-to-head">
              <Button variant="outline" size="lg" className="px-8 border-2 dark:border-[hsl(120,70%,70%)]">
                Compare Doctors
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
