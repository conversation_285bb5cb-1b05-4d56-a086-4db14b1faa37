"use server"

import { createClient } from "@supabase/supabase-js"

// Define the expected data structure for patient profile
interface PatientProfileData {
  username: string;
  email: string;
  password?: string; // Make password optional
  firstName: string;
  lastName: string;
  gender: string;
  city: string;
  country: string;
  user_type: "patient";
  age: number;
  auth_id: string; // This is crucial, comes from the auth user created client-side
  medicalCondition: string;
  registrationDate: string;
  phoneNumber: string;
  state: string;
}

export async function createPatientProfile(
  profileData: PatientProfileData
): Promise<{ success: boolean; error?: string }> {
  
  // Validate essential data
  if (!profileData.auth_id) {
    return { success: false, error: "Authentication ID is missing." };
  }
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
    console.error("Server Action Error: Missing Supabase environment variables.");
    return { success: false, error: "Server configuration error." };
  }
  
  // Create Supabase client with SERVICE ROLE
  const supabaseAdmin = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );

  try {
    console.log("Server Action: Attempting to insert patient profile for auth_id:", profileData.auth_id);
    
    // Get the maximum user_id from the users table to generate sequential ID
    let nextUserId: number;
    
    try {
      const { data: maxIdData, error: maxIdError } = await supabaseAdmin
        .from('users')
        .select('user_id')
        .order('user_id', { ascending: false })
        .limit(1)
        .single();
      
      if (maxIdError) {
        if (maxIdError.code === 'PGRST116') {
          // No rows found - start with ID 1
          console.log("No existing users found, starting with ID 1");
          nextUserId = 1;
        } else {
          console.error("Error getting max user_id:", maxIdError);
          return { success: false, error: "Failed to generate sequential ID: " + maxIdError.message };
        }
      } else {
        // Use the next ID in sequence
        nextUserId = (maxIdData?.user_id || 0) + 1;
        console.log(`Generated sequential user_id: ${nextUserId}`);
      }
    } catch (idError) {
      console.error("Exception getting max user_id:", idError);
      // Fallback to a timestamp-based ID in case of error
      nextUserId = Math.floor(Date.now() / 1000);
      console.log(`Fallback to timestamp-based ID: ${nextUserId}`);
    }
    
    // Map input data to the database schema, ensuring correct column names
    const userData = {
      user_id: nextUserId, // Set the sequential user ID
      username: profileData.username,
      email: profileData.email,
      first_name: profileData.firstName,
      last_name: profileData.lastName,
      gender: profileData.gender || "not_specified",
      city: profileData.city || "Not Specified",
      country_id: profileData.country_id || null, // Use country_id instead of country
      user_type: "patient", // Explicitly set
      age: profileData.age || 0,
      auth_id: profileData.auth_id, // Link to the auth user
      "medical condition": profileData.medicalCondition || "", // Fixed: as per schema docs
      "Registration date": profileData.registrationDate,
      "Phone_Number": profileData.phoneNumber ? (() => {
        const phoneStr = profileData.phoneNumber.toString().replace(/[^\d.]/g, ''); // Remove non-numeric characters except decimal
        const phoneNum = parseFloat(phoneStr);
        return isNaN(phoneNum) ? null : phoneNum;
      })() : null, // Convert to numeric safely with validation
      "State": profileData.state || ""
    };
    
    console.log("Server Action: User data for insertion:", JSON.stringify(userData, null, 2));

    // Try to use the direct API endpoint if available
    try {
      // Prepare data for the custom API endpoint
      const apiData = {
        email: profileData.email,
        password: profileData.password || "tempPassword123", // Use provided password or fallback
        userType: "patient",
        profileData: {
          username: profileData.username,
          firstName: profileData.firstName,
          lastName: profileData.lastName,
          gender: profileData.gender,
          city: profileData.city,
          countryId: profileData.country_id, // Pass countryId for API
          age: profileData.age,
          medical_condition: profileData.medicalCondition,
          phone_number: profileData.phoneNumber,
          state_province_region: profileData.state,
        }
      };
      
      console.log("Attempting to register via API endpoint...");
      const response = await fetch('/api/auth/custom/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiData),
      });
      
      const result = await response.json();
      
      if (result.success) {
        console.log("Registration successful via API endpoint");
        return { success: true };
      } else {
        console.warn("API endpoint registration failed:", result.error);
        // Continue with direct database insertion as fallback
      }
    } catch (apiError) {
      console.warn("Error using API endpoint:", apiError);
      // Continue with direct database insertion as fallback
    }

    // Fallback to direct database insertion
    const { error } = await supabaseAdmin
      .from('users') // Target the 'users' table
      .insert(userData);

    if (error) {
      console.error("Server Action Error: Supabase insert failed:", error);
      // Provide a more specific error message if possible
      let errorMessage = "Failed to save patient profile.";
      if (error.message.includes("duplicate key value violates unique constraint")) {
          if (error.message.includes("users_username_key")) {
              errorMessage = "This username is already taken.";
          } else if (error.message.includes("users_pkey") || error.message.includes("auth_id")) {
               errorMessage = "A profile for this user already exists.";
          }
      } else if (error.message.includes("violates check constraint")) {
          errorMessage = "Invalid data provided for profile.";
      }
      return { success: false, error: errorMessage };
    }

    console.log("Server Action: Patient profile inserted successfully for auth_id:", profileData.auth_id);
    return { success: true };

  } catch (err) {
    console.error("Server Action Exception: ", err);
    return { success: false, error: "An unexpected server error occurred." };
  }
} 