import { cookies } from "next/headers";
import { jwtDecode } from "jwt-decode";
import { ReadonlyRequestCookies } from "next/dist/server/web/spec-extension/adapters/request-cookies";

// Define token payload type
export interface JwtTokenPayload {
  userId: number;
  email: string;
  userType: 'patient' | 'doctor';
  exp: number; // Expiration timestamp
  iat: number; // Issued at timestamp
}

// Token cookie name
const TOKEN_COOKIE_NAME = 'doctors_league_auth_token';

/**
 * Gets the JWT token from the request cookies
 */
export async function getTokenFromCookies(): Promise<string | undefined> {
  const cookieStore: ReadonlyRequestCookies = await cookies();
  const cookie = cookieStore.get(TOKEN_COOKIE_NAME);
  return cookie?.value;
}

/**
 * Decodes and verifies the JWT token from cookies
 * Returns the decoded payload if valid, null otherwise
 */
export async function verifyAuth(): Promise<{ isAuthenticated: boolean; user: JwtTokenPayload | null }> {
  const token = await getTokenFromCookies();

  if (!token) {
    return { isAuthenticated: false, user: null };
  }

  try {
    const decoded = jwtDecode<JwtTokenPayload>(token);

    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (decoded.exp < currentTime) {
      return { isAuthenticated: false, user: null };
    }

    return { isAuthenticated: true, user: decoded };
  } catch (error) {
    console.error('Error decoding token:', error);
    return { isAuthenticated: false, user: null };
  }
}

/**
 * Checks if the authenticated user is a patient
 */
export async function isPatient(): Promise<boolean> {
  const { isAuthenticated, user } = await verifyAuth();
  return isAuthenticated && user?.userType === 'patient';
}

/**
 * Checks if the authenticated user is a doctor
 */
export async function isDoctor(): Promise<boolean> {
  const { isAuthenticated, user } = await verifyAuth();
  return isAuthenticated && user?.userType === 'doctor';
}