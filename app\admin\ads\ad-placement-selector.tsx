"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Define the available pages for ad placement
const PAGES = [
  { id: 'home', name: 'Home Page' },
  { id: 'about', name: 'About Us' },
  { id: 'standings', name: 'Standings' },
  { id: 'divisions', name: 'Divisions' },
  { id: 'specialties', name: 'Specialties' },
  { id: 'teams', name: 'Teams' },
  { id: 'head-to-head', name: 'Head to Head' },
  { id: 'doctor-profile', name: 'Doctor <PERSON>' },
  { id: 'ratings', name: 'Ratings' }
]

// Define the available positions for each page
const POSITIONS = {
  'banner': { name: 'Banner (Top/Center)', description: 'Displayed at the top of the page' },
  'sidebar': { name: 'Sidebar', description: 'Displayed on the side of the page' },
  'side-left': { name: 'Left Side', description: 'Floating on the left side of the page' },
  'side-right': { name: 'Right Side', description: 'Floating on the right side of the page' },
  'bottom': { name: 'Bottom', description: 'Displayed at the bottom of the page' },
  'in-content': { name: 'In Content', description: 'Displayed within the page content' }
}

interface AdPlacementSelectorProps {
  onPlacementChange: (placement: string) => void
  initialPlacement?: string
}

export function AdPlacementSelector({ onPlacementChange, initialPlacement = 'home:banner' }: AdPlacementSelectorProps) {
  const [selectedPage, setSelectedPage] = useState(initialPlacement.split(':')[0] || 'home')
  const [selectedPosition, setSelectedPosition] = useState(initialPlacement.split(':')[1] || 'banner')
  const [customPosition, setCustomPosition] = useState({
    top: '',
    left: '',
    right: '',
    bottom: '',
    width: '',
    height: ''
  })
  const [isCustomPosition, setIsCustomPosition] = useState(false)

  // Handle page selection
  const handlePageChange = (page: string) => {
    setSelectedPage(page)
    updatePlacement(page, selectedPosition)
  }

  // Handle position selection
  const handlePositionChange = (position: string) => {
    setSelectedPosition(position)
    setIsCustomPosition(position === 'custom')
    updatePlacement(selectedPage, position)
  }

  // Update the placement value and notify parent
  const updatePlacement = (page: string, position: string) => {
    const placement = `${page}:${position}`
    onPlacementChange(placement)
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Ad Placement</CardTitle>
        <CardDescription>Select where you want your ad to appear</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue={selectedPage} onValueChange={handlePageChange}>
          <div className="mb-6">
            <Label>Select Page</Label>
            <TabsList className="grid grid-cols-3 mt-2">
              {PAGES.map(page => (
                <TabsTrigger key={page.id} value={page.id}>{page.name}</TabsTrigger>
              ))}
            </TabsList>
          </div>

          {PAGES.map(page => (
            <TabsContent key={page.id} value={page.id} className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label>Position on {page.name}</Label>
                  <RadioGroup 
                    defaultValue={selectedPosition} 
                    onValueChange={handlePositionChange}
                    className="grid grid-cols-2 gap-4 mt-2"
                  >
                    {Object.entries(POSITIONS).map(([key, { name, description }]) => (
                      <div key={key} className="flex items-start space-x-2">
                        <RadioGroupItem value={key} id={`${page.id}-${key}`} />
                        <div className="grid gap-1">
                          <Label htmlFor={`${page.id}-${key}`} className="font-medium">{name}</Label>
                          <p className="text-sm text-muted-green">{description}</p>
                        </div>
                      </div>
                    ))}
                    <div className="flex items-start space-x-2">
                      <RadioGroupItem value="custom" id={`${page.id}-custom`} />
                      <div className="grid gap-1">
                        <Label htmlFor={`${page.id}-custom`} className="font-medium">Custom Position</Label>
                        <p className="text-sm text-muted-green">Specify exact position coordinates</p>
                      </div>
                    </div>
                  </RadioGroup>
                </div>

                {isCustomPosition && (
                  <div className="border rounded-md p-4 space-y-4">
                    <h3 className="text-sm font-medium">Custom Position Settings</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="top">Top</Label>
                        <Input 
                          id="top" 
                          placeholder="e.g., 20px or 10%" 
                          value={customPosition.top}
                          onChange={(e) => setCustomPosition({...customPosition, top: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="left">Left</Label>
                        <Input 
                          id="left" 
                          placeholder="e.g., 20px or 10%" 
                          value={customPosition.left}
                          onChange={(e) => setCustomPosition({...customPosition, left: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="right">Right</Label>
                        <Input 
                          id="right" 
                          placeholder="e.g., 20px or 10%" 
                          value={customPosition.right}
                          onChange={(e) => setCustomPosition({...customPosition, right: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="bottom">Bottom</Label>
                        <Input 
                          id="bottom" 
                          placeholder="e.g., 20px or 10%" 
                          value={customPosition.bottom}
                          onChange={(e) => setCustomPosition({...customPosition, bottom: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="width">Width</Label>
                        <Input 
                          id="width" 
                          placeholder="e.g., 300px or 50%" 
                          value={customPosition.width}
                          onChange={(e) => setCustomPosition({...customPosition, width: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="height">Height</Label>
                        <Input 
                          id="height" 
                          placeholder="e.g., 250px or auto" 
                          value={customPosition.height}
                          onChange={(e) => setCustomPosition({...customPosition, height: e.target.value})}
                        />
                      </div>
                    </div>
                    <Button 
                      onClick={() => {
                        const customPlacement = `${selectedPage}:custom:${JSON.stringify(customPosition)}`
                        onPlacementChange(customPlacement)
                      }}
                      size="sm"
                    >
                      Apply Custom Position
                    </Button>
                  </div>
                )}

                {/* Visual preview of the selected position */}
                <div className="mt-6">
                  <Label>Preview</Label>
                  <div className="relative border rounded-md mt-2 bg-background/10 h-[300px] overflow-hidden">
                    <div className="absolute inset-0 flex items-center justify-center text-muted-green">
                      {page.name} Content Area
                    </div>
                    
                    {/* Banner position */}
                    {selectedPosition === 'banner' && (
                      <div className="absolute top-0 left-0 right-0 h-16 bg-primary/20 flex items-center justify-center border-2 border-primary">
                        Banner Ad
                      </div>
                    )}
                    
                    {/* Sidebar position */}
                    {selectedPosition === 'sidebar' && (
                      <div className="absolute top-0 right-0 bottom-0 w-24 bg-primary/20 flex items-center justify-center border-2 border-primary">
                        <div className="rotate-90">Sidebar Ad</div>
                      </div>
                    )}
                    
                    {/* Left side position */}
                    {selectedPosition === 'side-left' && (
                      <div className="absolute top-1/4 left-4 h-32 w-16 bg-primary/20 flex items-center justify-center border-2 border-primary">
                        <div className="rotate-90">Left Ad</div>
                      </div>
                    )}
                    
                    {/* Right side position */}
                    {selectedPosition === 'side-right' && (
                      <div className="absolute top-1/4 right-4 h-32 w-16 bg-primary/20 flex items-center justify-center border-2 border-primary">
                        <div className="rotate-90">Right Ad</div>
                      </div>
                    )}
                    
                    {/* Bottom position */}
                    {selectedPosition === 'bottom' && (
                      <div className="absolute bottom-0 left-0 right-0 h-16 bg-primary/20 flex items-center justify-center border-2 border-primary">
                        Bottom Ad
                      </div>
                    )}
                    
                    {/* In-content position */}
                    {selectedPosition === 'in-content' && (
                      <div className="absolute top-1/2 left-1/4 right-1/4 h-16 bg-primary/20 flex items-center justify-center border-2 border-primary transform -translate-y-1/2">
                        In-Content Ad
                      </div>
                    )}
                    
                    {/* Custom position */}
                    {selectedPosition === 'custom' && (
                      <div 
                        className="absolute bg-primary/20 flex items-center justify-center border-2 border-primary"
                        style={{
                          top: customPosition.top || '50%',
                          left: customPosition.left || 'auto',
                          right: customPosition.right || 'auto',
                          bottom: customPosition.bottom || 'auto',
                          width: customPosition.width || '100px',
                          height: customPosition.height || '100px',
                          transform: (!customPosition.left && !customPosition.right) ? 'translateX(-50%)' : 'none'
                        }}
                      >
                        Custom Ad
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>
          ))}
        </Tabs>

        <div className="mt-6 pt-6 border-t">
          <div className="flex items-center space-x-2">
            <Checkbox id="priority" />
            <div className="grid gap-1.5">
              <Label htmlFor="priority">High Priority</Label>
              <p className="text-sm text-muted-green">
                This ad will be shown before other ads in the same position
              </p>
            </div>
          </div>
        </div>

        <div className="mt-6">
          <p className="text-sm font-medium">Selected Placement:</p>
          <code className="text-xs bg-muted-green p-1 rounded">
            {isCustomPosition 
              ? `${selectedPage}:custom:${JSON.stringify(customPosition)}` 
              : `${selectedPage}:${selectedPosition}`}
          </code>
        </div>
      </CardContent>
    </Card>
  )
}
