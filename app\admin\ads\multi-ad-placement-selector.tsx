"use client"

import { useState, useEffect } from "react"
import { Placement } from "./types" // Assuming types.ts exists in the same directory
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input" // Keep Input if needed for custom later
import { Badge } from "@/components/ui/badge"
import { Plus, X, Move, Edit } from "lucide-react"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog"

// Define the available pages for ad placement
const PAGES = [
  { id: 'home', name: 'Home Page' },
  { id: 'about', name: 'About Us' },
  { id: 'standings', name: 'Standings' },
  { id: 'divisions', name: 'Divisions' },
  { id: 'specialties', name: 'Specialties' },
  { id: 'teams', name: 'Teams' },
  { id: 'head-to-head', name: 'Head to Head' },
  { id: 'doctor-profile', name: 'Doctor Profile' },
  { id: 'ratings', name: 'Ratings' }
]

// Define the available positions for each page (Original state)
const POSITIONS = {
  'banner': { name: 'Banner (Top/Center)', description: 'Displayed at the top of the page' },
  'sidebar': { name: 'Sidebar', description: 'Displayed on the side of the page' },
  'side-left': { name: 'Left Side', description: 'Floating on the left side of the page' },
  'side-right': { name: 'Right Side', description: 'Floating on the right side of the page' },
  'bottom': { name: 'Bottom', description: 'Displayed at the bottom of the page' },
  'in-content': { name: 'In Content', description: 'Displayed within the page content' },
  'custom_position': { name: 'Custom Position', description: 'Specify exact coordinates' }
}

interface MultiAdPlacementSelectorProps {
  onPlacementsChange: (placements: Placement[]) => void; // Changed to pass Placement[]
  initialPlacements?: string[]; // Keep as string array for input
}

export function MultiAdPlacementSelector({
  onPlacementsChange,
  initialPlacements = ['home:banner'] // Default initial value
}: MultiAdPlacementSelectorProps) {

  // Function to parse initial string placements into Placement objects
  const parsePlacements = (placementStrings: string[]): Placement[] => {
    return placementStrings.map((placementString, index) => {
      const parts = placementString.split(':');
      const page = parts[0] || 'home';
      let position = parts[1] || 'banner';
      let isCustom = false;
      let customPositionData: Placement['customPosition'] = undefined; // Initialize as undefined

      // Check if this is a custom position string (e.g., "page:custom_position:{...}")
      if (position === 'custom_position' && parts.length > 2) {
        isCustom = true;
        try {
          // Attempt to parse the JSON part
          const jsonData = parts.slice(2).join(':'); // Re-join in case JSON has colons
          const parsedData = JSON.parse(jsonData);
          // Basic validation to ensure it has expected keys (optional)
          customPositionData = {
            top: parsedData.top || '',
            left: parsedData.left || '',
            right: parsedData.right || '',
            bottom: parsedData.bottom || '',
            width: parsedData.width || '',
            height: parsedData.height || '',
          };
        } catch (e) {
          console.error('Error parsing custom position JSON:', placementString, e);
          // Fallback to default empty custom position if parsing fails
          customPositionData = { top: '', left: '', right: '', bottom: '', width: '', height: '' };
        }
      }

      return {
        id: `placement-${Date.now()}-${index}`, // Generate a unique ID
        page,
        position, // Will be 'custom_position' if identified
        isCustom,
        customPosition: customPositionData, // Store parsed data or undefined
        priority: false // Default priority
      };
    });
  };

  const [placements, setPlacements] = useState<Placement[]>(parsePlacements(initialPlacements));
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [activePopover, setActivePopover] = useState<string | null>(null); // Track which popover is open
  const [currentPlacement, setCurrentPlacement] = useState<Placement | null>(null); // Placement being added/edited
  const [isEditing, setIsEditing] = useState(false); // Differentiate add/edit mode

  // Default new placement structure (original)
  const defaultPlacement: Omit<Placement, 'id'> = { // Omit ID as it's generated on add
    page: 'home',
    position: 'banner',
    isCustom: false,
    // customPosition: undefined,
    priority: false
  };

  // Update parent component when placements change
  useEffect(() => {
    // Convert Placement[] back to string[] for the form's state if needed,
    // The parent AdForm now expects Placement[] directly via its onPlacementsChange prop.
    // No need to convert back to strings here.
    onPlacementsChange(placements);
  }, [placements, onPlacementsChange]);


  // Add a new placement
  const addPlacement = () => {
    setCurrentPlacement({
      ...defaultPlacement,
      id: `placement-${Date.now()}` // Generate ID when adding
    });
    setIsEditing(false);
    setActivePopover('add-new'); // Identify the "add" popover
    setIsPopoverOpen(true);
  };

  // Edit an existing placement
  const editPlacement = (placement: Placement) => {
    setCurrentPlacement(placement); // Set the placement to be edited
    setIsEditing(true);
    setActivePopover(placement.id); // Identify the popover by placement ID
    setIsPopoverOpen(true);
  };

  // Remove a placement
  const removePlacement = (id: string) => {
    setPlacements(placements.filter(p => p.id !== id));
  };

  // Save the current placement (from popover form)
  const savePlacement = () => {
    if (!currentPlacement) return;

    if (isEditing) {
      // Update existing placement
      setPlacements(placements.map(p =>
        p.id === currentPlacement.id ? currentPlacement : p
      ));
    } else {
      // Add new placement
      setPlacements([...placements, currentPlacement]);
    }
    handlePopoverOpenChange(false); // Close popover
  };

   // Function to handle popover open/close state changes
   const handlePopoverOpenChange = (open: boolean) => {
    setIsPopoverOpen(open);
    if (!open) {
        setActivePopover(null); // Reset active popover when closing
        setCurrentPlacement(null); // Reset current placement being edited/added
        setIsEditing(false); // Reset editing mode
    }
};


  // Get page name by ID
  const getPageName = (pageId: string) => {
    const page = PAGES.find(p => p.id === pageId);
    return page ? page.name : pageId;
  };

  // Get position name by ID (original)
  const getPositionName = (positionId: string) => {
    const position = POSITIONS[positionId as keyof typeof POSITIONS];
    return position ? position.name : positionId;
  };

  // Helper function to render the form content inside the popover
  function renderPlacementForm() {
    if (!currentPlacement) return null;

    return (
      <>
        <DialogHeader className="mb-4">
          <DialogTitle>{isEditing ? 'Edit Placement' : 'Add New Placement'}</DialogTitle>
          <DialogDescription>
            Select where you want your ad to appear.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Page Selection */}
          <div>
            <Label>Select Page</Label>
            <Tabs
              value={currentPlacement.page}
              onValueChange={(value) => setCurrentPlacement(prev => prev ? { ...prev, page: value } : null)}
              className="mt-2"
            >
              <TabsList className="grid grid-cols-3 h-auto">
                {PAGES.map(page => (
                  <TabsTrigger key={page.id} value={page.id} className="h-auto py-2">{page.name}</TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          </div>

          {/* Position Selection (Original) */}
          <div>
            <Label>Position on {getPageName(currentPlacement.page)}</Label>
            <RadioGroup
              value={currentPlacement.position}
              // Update onValueChange to correctly set isCustom based on the selected value
              onValueChange={(value) => {
                const isCustomSelected = value === 'custom_position';
                setCurrentPlacement(prev => prev ? {
                  ...prev,
                  position: value,
                  isCustom: isCustomSelected,
                  // Initialize customPosition object if switching to custom
                  customPosition: isCustomSelected ? (prev.customPosition ?? { top: '', left: '', right: '', bottom: '', width: '', height: '' }) : undefined
                } : null);
              }}
              className="grid grid-cols-2 gap-4 mt-2"
            >
              {Object.entries(POSITIONS).map(([key, { name, description }]) => (
                <div key={key} className="flex items-start space-x-3 p-2 rounded hover:bg-muted-green/50">
                  <RadioGroupItem value={key} id={`position-${key}`} className="mt-1" />
                  <div className="grid gap-0.5">
                    <Label htmlFor={`position-${key}`} className="font-medium">{name}</Label>
                    <p className="text-sm text-muted-green">{description}</p>
                  </div>
                </div>
              ))}
              {/* The 'custom_position' is now part of POSITIONS, so no separate button needed */}
            </RadioGroup>
          </div>

          {/* Custom Position Settings Area - Show if position is 'custom_position' */}
          {currentPlacement.position === 'custom_position' && (
             <div className="border rounded-md p-4 space-y-4 mt-4 bg-muted-green/20">
               <h3 className="text-sm font-medium text-foreground">Custom Position Settings (CSS Values)</h3>
               <div className="grid grid-cols-2 gap-4">
                 {/* Input fields for custom position properties */}
                 {(['top', 'left', 'right', 'bottom', 'width', 'height'] as const).map((prop) => (
                   <div key={prop} className="space-y-1">
                     <Label htmlFor={prop} className="capitalize">{prop}</Label>
                     <Input
                       id={prop}
                       placeholder={`e.g., 20px or 10%${prop === 'height' || prop === 'width' ? ' or auto' : ''}`}
                       value={currentPlacement.customPosition?.[prop] || ''}
                       onChange={(e) => setCurrentPlacement(prev => prev ? {
                         ...prev,
                         customPosition: { ...prev.customPosition!, [prop]: e.target.value }
                       } : null)}
                     />
                   </div>
                 ))}
               </div>
                <p className="text-xs text-muted-green">Enter valid CSS values (e.g., '10px', '5%', 'auto'). Leave blank if not needed.</p>
             </div>
           )}


          {/* Visual Preview (Updated) */}
           <div className="mt-6">
             <Label>Preview</Label>
             <div className="relative border rounded-md mt-2 bg-background/10 h-[300px] overflow-hidden">
               <div className="absolute inset-0 flex items-center justify-center text-muted-green">
                 {getPageName(currentPlacement.page)} Content Area
               </div>
               {/* Render preview based on position, including custom */}
               {currentPlacement.position === 'banner' && <div className="absolute top-0 left-0 right-0 h-16 bg-primary/20 flex items-center justify-center border-2 border-primary">Banner Ad</div>}
               {currentPlacement.position === 'sidebar' && <div className="absolute top-0 right-0 bottom-0 w-24 bg-primary/20 flex items-center justify-center border-2 border-primary"><div className="rotate-90">Sidebar Ad</div></div>}
               {currentPlacement.position === 'side-left' && <div className="absolute top-[80px] left-4 h-32 w-16 bg-primary/20 flex items-center justify-center border-2 border-primary"><div className="rotate-90">Left Ad</div></div>}
               {currentPlacement.position === 'side-right' && <div className="absolute top-[80px] right-4 h-32 w-16 bg-primary/20 flex items-center justify-center border-2 border-primary"><div className="rotate-90">Right Ad</div></div>}
               {currentPlacement.position === 'bottom' && <div className="absolute bottom-0 left-0 right-0 h-16 bg-primary/20 flex items-center justify-center border-2 border-primary">Bottom Ad</div>}
               {currentPlacement.position === 'in-content' && <div className="absolute top-1/2 left-1/4 right-1/4 h-16 bg-primary/20 flex items-center justify-center border-2 border-primary transform -translate-y-1/2">In-Content Ad</div>}
               {currentPlacement.position === 'custom_position' && (
                 <div
                   className="absolute bg-primary/30 flex items-center justify-center border-2 border-dashed border-primary text-xs p-1 text-primary-foreground"
                   style={{
                     top: currentPlacement.customPosition?.top || 'auto',
                     left: currentPlacement.customPosition?.left || 'auto',
                     right: currentPlacement.customPosition?.right || 'auto',
                     bottom: currentPlacement.customPosition?.bottom || 'auto',
                     width: currentPlacement.customPosition?.width || '100px', // Default preview size
                     height: currentPlacement.customPosition?.height || '50px', // Default preview size
                     // Basic centering if only one horizontal/vertical is set
                     transform:
                       (currentPlacement.customPosition?.top && !currentPlacement.customPosition?.bottom && !currentPlacement.customPosition?.left && !currentPlacement.customPosition?.right) ? 'translateX(-50%)' :
                       (currentPlacement.customPosition?.left && !currentPlacement.customPosition?.right && !currentPlacement.customPosition?.top && !currentPlacement.customPosition?.bottom) ? 'translateY(-50%)' :
                       'none'
                   }}
                 >
                   Custom Ad
                 </div>
               )}
             </div>
           </div>


          {/* Priority Option */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="priority"
              checked={currentPlacement.priority || false}
              onCheckedChange={(checked) => setCurrentPlacement(prev => prev ? { ...prev, priority: checked === true } : null)}
            />
            <div className="grid gap-1.5 leading-none">
              <Label htmlFor="priority" className="font-medium">High Priority</Label>
              <p className="text-xs text-muted-green">
                This ad will be shown before other ads in the same position.
              </p>
            </div>
          </div>
        </div>

        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={() => handlePopoverOpenChange(false)}>Cancel</Button>
          <Button onClick={savePlacement}>{isEditing ? 'Update' : 'Add'} Placement</Button>
        </DialogFooter>
      </>
    );
  }

  // Main component return structure
  return (
    <div className="space-y-4">
      {/* Popover for Adding New Placement */}
      <Popover open={activePopover === 'add-new' && isPopoverOpen} onOpenChange={handlePopoverOpenChange}>
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">Ad Placements</h3>
          <PopoverTrigger asChild>
            <Button onClick={addPlacement} size="sm" className="gap-1">
              <Plus className="h-4 w-4" />
              <span>Add Placement</span>
            </Button>
          </PopoverTrigger>
        </div>
        <PopoverContent className="w-auto max-w-[600px] max-h-[80vh] overflow-y-auto p-4" sideOffset={5} align="end">
          {renderPlacementForm()}
        </PopoverContent>
      </Popover>

      {/* List of current placements */}
      <div className="space-y-2">
        {placements.length === 0 ? (
          <div className="text-center py-8 border rounded-md bg-muted-green/20">
            <p className="text-muted-green">No placements added yet.</p>
          </div>
        ) : (
          placements.map(placement => (
            <div key={placement.id} className="flex items-center justify-between p-3 border rounded-md hover:bg-accent/10">
              <div className="flex items-center gap-2">
                <Move className="h-4 w-4 text-muted-green cursor-move" />
                <div>
                  <div className="font-medium">{getPageName(placement.page)}</div>
                  <div className="text-sm text-muted-green">{getPositionName(placement.position)}</div>
                </div>
                {placement.priority && <Badge variant="outline" className="ml-2 bg-primary/10">Priority</Badge>}
              </div>
              <div className="flex items-center gap-2">
                {/* Popover for Editing Existing Placement */}
                <Popover open={activePopover === placement.id && isPopoverOpen} onOpenChange={handlePopoverOpenChange}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="sm" onClick={() => editPlacement(placement)} className="h-8 px-2">
                      <Edit className="h-4 w-4 mr-1" /> Edit
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto max-w-[600px] max-h-[80vh] overflow-y-auto p-4" sideOffset={5} align="end">
                    {renderPlacementForm()}
                  </PopoverContent>
                </Popover>
                <Button variant="ghost" size="sm" onClick={() => removePlacement(placement.id)} className="h-8 px-2 text-destructive hover:text-destructive">
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Summary of selected placements (Original) */}
      {placements.length > 0 && (
        <div className="mt-6 pt-4 border-t">
          <p className="text-sm font-medium mb-2">Selected Placements:</p>
          <div className="space-y-1">
            {placements.map((placement: Placement) => {
              // Generate the string representation, including custom position JSON if applicable
              let placementString = `${placement.page}:${placement.position}`;
              if (placement.position === 'custom_position' && placement.customPosition) {
                // Filter out empty values before stringifying
                const definedPositions = Object.entries(placement.customPosition)
                  .filter(([_, value]) => value !== '' && value !== null && value !== undefined)
                  .reduce((obj, [key, value]) => {
                    obj[key] = value;
                    return obj;
                  }, {} as Record<string, string>);

                if (Object.keys(definedPositions).length > 0) {
                   placementString += `:${JSON.stringify(definedPositions)}`;
                }
              }
              return (
                <code key={placement.id} className="text-xs bg-muted-green p-1 rounded block">
                  {placementString}
                  {placement.priority && ' (Priority)'}
                </code>
              );
            })}
            {/* Removed extra closing parenthesis */}
          </div>
        </div>
      )}
    </div>
  );
}
