"use client"

import { useState, useEffect } from "react"
import {
  Search,
  Plus,
  Edit,
  Eye,
  EyeOff,
  BarChart3,
  <PERSON><PERSON>oint<PERSON>,
  DollarSign,
  ArrowUp,
  ArrowDown,
  MoreHorizontal,
  Loader2, // Added for loading state
  Trash2 // Icon for delete
} from "lucide-react"
// Import deleteAd action
import { getAds, updateAdStatus, createAd, updateAd, deleteAd, Ad as AdType, AdInsert, AdUpdate } from "@/actions/ad-actions"
import { useToast } from "@/hooks/use-toast" // Import useToast
import { AdForm } from "@/components/admin/ads/ad-form" // Import the form component
import {
  Dialog,
  DialogContent,
  DialogDescription, // Re-added
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,      // Re-added
  DialogClose,
} from "@/components/ui/dialog"
// Import Checkbox and AlertDialog
import { Checkbox } from "@/components/ui/checkbox"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import Image from "next/image"; // Import next/image

// Use the AdType imported from actions
export default function AdsPage() {
  const [ads, setAds] = useState<AdType[]>([])
  const [filteredAds, setFilteredAds] = useState<AdType[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [placementFilter, setPlacementFilter] = useState("all")
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("all")
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false) // State for edit modal
  const [editingAd, setEditingAd] = useState<AdType | null>(null) // State for ad being edited
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedAdIds, setSelectedAdIds] = useState<Set<string>>(new Set()); // State for selected ad IDs
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false); // State for delete confirmation dialog
  const [adToDelete, setAdToDelete] = useState<string | null>(null); // State for single ad ID to delete
  const [isDeleting, setIsDeleting] = useState(false); // Loading state for delete operation
  const { toast } = useToast()

  // Function to refresh ads data
  const refreshAds = async () => {
    setLoading(true); // Show loading indicator while refreshing
    const { data, error } = await getAds();
    if (error) {
      console.error("Error fetching ads:", error);
      toast({
        title: "Error refreshing ads",
        description: error.message || "Could not reload ad data.",
        variant: "destructive",
      });
    } else if (data) {
      setAds(data);
      // Note: Filtering logic will re-run automatically due to 'ads' dependency in the filter useEffect
    }
    setLoading(false);
  };


  // Fetch ads using server action on initial load
  useEffect(() => {
    const fetchAds = async () => {
      setLoading(true);
      const { data, error } = await getAds();
      if (error) {
        console.error("Error fetching ads:", error);
        toast({
          title: "Error fetching ads",
          description: error.message || "Could not load ad data.",
          variant: "destructive",
        })
        setAds([]);
        setFilteredAds([]);
      } else if (data) {
        setAds(data);
        // Filtering logic runs in the next useEffect
      }
      setLoading(false);
    };
    fetchAds();
  }, [toast]); // Initial fetch

  // Filter ads based on state variables (no change here)
  useEffect(() => {
    let result = [...ads]

    // Apply tab filter
    if (activeTab !== "all") {
      result = result.filter(ad => ad.status === activeTab)
    }

    // Apply search query filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(ad =>
        ad.title.toLowerCase().includes(query) ||
        ad.description?.toLowerCase().includes(query)
      )
    }

    // Apply status filter if not already filtered by tab
    if (statusFilter !== "all" && activeTab === "all") {
      result = result.filter(ad => ad.status === statusFilter)
    }

    // Apply placement filter (check if the placements array contains the filter value)
    if (placementFilter !== "all") {
      result = result.filter(ad =>
        ad.placements && ad.placements.includes(placementFilter)
      )
    }

    setFilteredAds(result)
  }, [ads, searchQuery, statusFilter, placementFilter, activeTab])

  // Toggle ad status using server action
  const handleToggleAdStatus = async (ad: AdType) => {
    const originalStatus = ad.status;
    const newStatus = ad.status === 'active' ? 'paused' : 'active';

    // Optimistically update UI
    setAds(prevAds => prevAds.map(a =>
      a.id === ad.id ? { ...a, status: newStatus } : a
    ));

    const { error } = await updateAdStatus(ad.id, newStatus);

    if (error) {
      console.error("Error updating ad status:", error);
      toast({
        title: "Error updating status",
        description: error.message || "Could not update ad status.",
        variant: "destructive",
      });
      // Revert UI on error
      setAds(prevAds => prevAds.map(a =>
        a.id === ad.id ? { ...a, status: originalStatus } : a
      ));
    } else {
       toast({
         title: "Status Updated",
         description: `Ad "${ad.title}" status changed to ${newStatus}.`,
       });
    }
  }

  // Handle Create Ad Form Submission
  const handleCreateSubmit = async (formData: AdInsert | AdUpdate) => {
    console.log("[handleCreateSubmit] Attempting to create ad with data:", JSON.stringify(formData, null, 2));
    setIsSubmitting(true);
    try {
      const { data, error } = await createAd(formData as AdInsert); // Call createAd action

      if (error) {
        // Log the structured error returned by the action more thoroughly
        console.error("[handleCreateSubmit] Error returned from createAd action:", error);
        console.error("[handleCreateSubmit] Error details:", JSON.stringify(error, null, 2));
        toast({
          title: "Error Creating Ad",
          // Try to access a message property, otherwise stringify the error
          description: typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : JSON.stringify(error),
          variant: "destructive",
        });
      } else {
        console.log("[handleCreateSubmit] Ad created successfully:", data);
        toast({
          title: "Ad Created",
          description: `Ad "${data?.title}" has been created successfully.`,
        });
        setIsCreateModalOpen(false); // Close modal on success
        await refreshAds(); // Refresh the ads list
      }
    } catch (catchError: any) {
      // Catch unexpected exceptions during the action call or processing
      console.error("[handleCreateSubmit] Exception caught while creating ad:", catchError);
      console.error("[handleCreateSubmit] Exception details:", JSON.stringify(catchError, Object.getOwnPropertyNames(catchError), 2));
      toast({
        title: "Error Creating Ad",
        description: "An unexpected error occurred. Please check the console.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle Edit Ad Click
  const handleEditClick = (ad: AdType) => {
    setEditingAd(ad);
    setIsEditModalOpen(true);
  };

  // Handle Update Ad Form Submission
  const handleUpdateSubmit = async (formData: AdInsert | AdUpdate) => {
    if (!editingAd) return; // Should not happen

    setIsSubmitting(true);
    // ID should be included in formData by AdForm when in edit mode
    const { data, error } = await updateAd(editingAd.id, formData as AdUpdate);

    if (error) {
      console.error("Error updating ad:", error);
      toast({
        title: "Error Updating Ad",
        description: error.message || "Could not update the ad.",
        variant: "destructive",
      });
    } else {
      toast({
        title: "Ad Updated",
        description: `Ad "${data?.title}" has been updated successfully.`,
      });
      setIsEditModalOpen(false); // Close modal on success
      setEditingAd(null); // Clear editing state
      await refreshAds(); // Refresh the ads list
    }
    setIsSubmitting(false);
  };

  // --- Delete Logic ---

  // Function to trigger delete confirmation
  const handleDeleteClick = (adId: string | null = null) => {
    if (adId) {
      setAdToDelete(adId); // Set single ad ID for deletion
    } else {
      setAdToDelete(null); // Clear single ad ID if deleting selected
    }
    setIsDeleteDialogOpen(true);
  };

  // Function to perform the actual deletion
  const performDelete = async () => {
    setIsDeleting(true);
    const idsToDelete = adToDelete ? [adToDelete] : Array.from(selectedAdIds);

    if (idsToDelete.length === 0) {
      toast({ title: "No ads selected", variant: "destructive" });
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
      return;
    }

    console.log(`Attempting to delete ads with IDs: ${idsToDelete.join(', ')}`);

    // Call deleteAd action for each selected ID
    const deletePromises = idsToDelete.map(id => deleteAd(id));
    const results = await Promise.allSettled(deletePromises);

    let successCount = 0;
    let errorCount = 0;
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && !result.value.error) {
        successCount++;
      } else {
        errorCount++;
        const adId = idsToDelete[index];
        const errorMsg = result.status === 'rejected'
          ? result.reason?.message
          : result.value?.error?.message || 'Unknown error';
        console.error(`Failed to delete ad ${adId}: ${errorMsg}`);
      }
    });

    if (successCount > 0) {
      toast({
        title: "Deletion Successful",
        description: `${successCount} ad(s) deleted successfully.`,
      });
    }
    if (errorCount > 0) {
      toast({
        title: "Deletion Partially Failed",
        description: `${errorCount} ad(s) could not be deleted. Check console for details.`,
        variant: "destructive",
      });
    }

    setSelectedAdIds(new Set()); // Clear selection
    setAdToDelete(null); // Clear single delete target
    setIsDeleting(false);
    setIsDeleteDialogOpen(false);
    await refreshAds(); // Refresh the list
  };


  // TODO: Calculate metrics - Requires fetching from analytics tables or modifying getAds action
  // const totalImpressions = ads.reduce((sum, ad) => sum + ad.impressions, 0);
  // const totalClicks = ads.reduce((sum, ad) => sum + ad.clicks, 0)
  // const totalSpend = ads.reduce((sum, ad) => sum + ad.spend, 0)
  // const averageCTR = totalImpressions > 0
  //   ? (totalClicks / totalImpressions * 100).toFixed(2)
  //   : "0.00"
  // const costPerClick = totalClicks > 0
  //   ? (totalSpend / totalClicks).toFixed(2)
  //   : "0.00"

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Ad Management</h1>
        {/* Action Buttons */}
        <div className="flex gap-2">
          {/* Visual Placement Demo Button */}
          <Button size="sm" variant="outline" className="gap-1" onClick={() => window.location.href = '/admin/ads/visual-placement-demo'}>
            <MousePointer className="h-4 w-4" />
            <span>Visual Placement Demo</span>
          </Button>

          {/* Test Ad Placement Button */}
          <Button size="sm" variant="outline" className="gap-1" onClick={() => window.location.href = '/admin/ads/test-ad-placement'}>
            <BarChart3 className="h-4 w-4" />
            <span>Test Ad Placement</span>
          </Button>

          {/* Dialog Trigger for Create Ad */}
          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="gap-1">
                <Plus className="h-4 w-4" />
                <span>Create New Ad</span>
              </Button>
            </DialogTrigger>
            {/* Add max-height and overflow for scrolling */}
            <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New Ad</DialogTitle>
                {/* Use DialogDescription again */}
                <DialogDescription>
                  Fill in the details for the new advertising campaign.
                </DialogDescription>
              </DialogHeader>
              {/* Ad Form for Creating */}
              <AdForm
                onSubmit={handleCreateSubmit}
                onCancel={() => setIsCreateModalOpen(false)}
                isLoading={isSubmitting}
                // Pass no 'ad' prop for creation mode
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Overview Cards - TODO: Populate with real data from analytics */}
      {/* ... (Overview Cards code remains commented out) ... */}

      {/* Tabs & Filters */}
      <div className="space-y-4">
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="all">All Ads</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="paused">Paused</TabsTrigger>
            <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
            <TabsTrigger value="completed">Completed</TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="flex flex-col gap-4 md:flex-row md:items-center">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-green" />
              <Input
                placeholder="Search ads..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <Select value={placementFilter} onValueChange={setPlacementFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Placement" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Placements</SelectItem>

                {/* Homepage */}
                <SelectItem value="homepage:banner">Homepage: Banner</SelectItem>
                <SelectItem value="homepage:sidebar">Homepage: Sidebar</SelectItem>

                {/* About Us */}
                <SelectItem value="about-us:banner">About Us: Banner</SelectItem>
                <SelectItem value="about-us:sidebar">About Us: Sidebar</SelectItem>

                {/* Standings */}
                <SelectItem value="standings:banner">Standings: Banner</SelectItem>
                <SelectItem value="standings:sidebar">Standings: Sidebar</SelectItem>

                {/* Divisions/Specialties */}
                <SelectItem value="divisions:banner">Divisions: Banner</SelectItem>
                <SelectItem value="divisions:sidebar">Divisions: Sidebar</SelectItem>

                {/* Teams */}
                <SelectItem value="teams:banner">Teams: Banner</SelectItem>
                <SelectItem value="teams:sidebar">Teams: Sidebar</SelectItem>

                {/* Head to Head */}
                <SelectItem value="head-to-head:banner">Head to Head: Banner</SelectItem>
                <SelectItem value="head-to-head:sidebar">Head to Head: Sidebar</SelectItem>

                {/* Doctor Profile */}
                <SelectItem value="doctor-profile:top">Doctor Profile: Top</SelectItem>
                <SelectItem value="doctor-profile:bottom">Doctor Profile: Bottom</SelectItem>

                {/* Ratings */}
                <SelectItem value="ratings:banner">Ratings: Banner</SelectItem>
                <SelectItem value="ratings:sidebar">Ratings: Sidebar</SelectItem>

                {/* Legacy options */}
                <SelectItem value="doctors_list:sidebar">Doctors List: Sidebar</SelectItem>
                <SelectItem value="search_results">Search Results</SelectItem>
              </SelectContent>
            </Select>
            {/* Delete Selected Button */}
            {selectedAdIds.size > 0 && (
              <AlertDialog open={isDeleteDialogOpen && !adToDelete} onOpenChange={(open) => { if (!open) setAdToDelete(null); setIsDeleteDialogOpen(open); }}>
                <AlertDialogTrigger asChild>
                   <Button
                     variant="destructive"
                     size="sm"
                     className="gap-1"
                     onClick={() => handleDeleteClick()} // Trigger confirmation for selected
                     disabled={isDeleting}
                   >
                     {isDeleting ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
                     Delete ({selectedAdIds.size})
                   </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete the selected {selectedAdIds.size} ad(s).
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={performDelete} disabled={isDeleting}>
                      {isDeleting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                      Continue
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        </div>
      </div>

      {/* Ads Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[40px]">
                <Checkbox
                  checked={selectedAdIds.size > 0 && selectedAdIds.size === filteredAds.length}
                  onCheckedChange={(checked) => {
                    const newSelectedIds = new Set<string>();
                    if (checked) {
                      filteredAds.forEach(ad => newSelectedIds.add(ad.id));
                    }
                    setSelectedAdIds(newSelectedIds);
                  }}
                  aria-label="Select all rows"
                />
              </TableHead>
              <TableHead>Ad</TableHead>
              <TableHead>Performance</TableHead>
              <TableHead>Budget</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Placement</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAds.length > 0
              ? filteredAds.map(ad => <TableRow
                  key={ad.id}
                  data-state={selectedAdIds.has(ad.id) ? "selected" : ""}
                >
                    <TableCell>
                      <Checkbox
                        checked={selectedAdIds.has(ad.id)}
                        onCheckedChange={(checked) => {
                          setSelectedAdIds(prev => {
                            const next = new Set(prev);
                            if (checked) {
                              next.add(ad.id);
                            } else {
                              next.delete(ad.id);
                            }
                            return next;
                          });
                        }}
                        aria-label={`Select row for ad ${ad.title}`}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        {/* Display Media Preview */}
                        <div className="w-16 h-12 relative rounded bg-muted-green flex items-center justify-center overflow-hidden text-muted-green text-xs">
                          {ad.media_url && ad.media_type === 'image' ? (
                            <Image src={ad.media_url} alt={ad.title} width={64} height={48} className="object-cover w-full h-full" />
                          ) : ad.media_url && ad.media_type === 'video' ? (
                            // Basic video placeholder - might need a better preview later
                            <video src={ad.media_url} className="object-cover w-full h-full" />
                          ) : (
                            <span>No Media</span>
                          )}
                        </div>
                        <div>
                          <p className="font-medium leading-none">{ad.title}</p>
                          <p className="text-sm text-muted-green mt-1 line-clamp-1">
                            {ad.description || 'No description'}
                          </p>
                          <p className="text-xs text-muted-green mt-1">
                            {new Date(ad.start_date).toLocaleDateString()}
                            {ad.end_date && ` - ${new Date(ad.end_date).toLocaleDateString()}`}
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    {/* Performance Cell - TODO: Populate with real data */}
                    <TableCell>
                      <div className="space-y-1 text-muted-green text-sm">
                        <div>(Analytics TBD)</div>
                        {/* ... (Performance details commented out) ... */}
                      </div>
                    </TableCell>
                    {/* Budget Cell - TODO: Populate spend with real data */}
                    <TableCell>
                      <div className="space-y-1">
                        <p className="text-sm font-medium">${(0).toLocaleString()} spent</p> {/* Placeholder spend */}
                        <p className="text-sm text-muted-green">of ${ad.budget?.toLocaleString() ?? 'N/A'}</p>
                        <div className="w-full bg-muted-green h-2 rounded-full overflow-hidden">
                          <div
                            className="bg-primary h-full"
                            // style={{ width: `${Math.min(100, (ad.spend / ad.budget) * 100)}%` }} // Placeholder width
                            style={{ width: `0%` }}
                          ></div>
                        </div>
                      </div>
                    </TableCell>
                    {/* Status Cell */}
                    <TableCell>
                      <Badge variant={
                        ad.status === 'active' ? 'default' :
                        ad.status === 'paused' ? 'secondary' :
                        ad.status === 'scheduled' ? 'outline' :
                        'destructive' // Default for completed/draft
                      } className={
                        ad.status === 'active' ? 'bg-green-100 text-green-800 hover:bg-green-100' :
                        ad.status === 'paused' ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100' :
                        ad.status === 'scheduled' ? 'bg-blue-100 text-blue-800 hover:bg-blue-100' :
                        'bg-green-100 text-green-900 hover:bg-green-100' // Default
                      }>
                        {ad.status.charAt(0).toUpperCase() + ad.status.slice(1)}
                      </Badge>

                      {(ad.status === 'active' || ad.status === 'paused') && (
                        <div className="flex items-center mt-2">
                          <Switch
                            checked={ad.status === 'active'}
                            onCheckedChange={() => handleToggleAdStatus(ad)}
                            className="mr-2 data-[state=checked]:bg-green-600"
                            // size="sm" // size prop might not exist depending on shadcn version
                          />
                          <span className="text-xs">
                            {ad.status === 'active' ? 'Running' : 'Paused'}
                          </span>
                        </div>
                      )}
                    </TableCell>
                    {/* Placement & Targeting Cell */}
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {ad.placements && ad.placements.length > 0 ? (
                          ad.placements.map((p, index) => (
                            <Badge key={`${ad.id}-placement-${index}`} variant="outline">
                              {p.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </Badge>
                          ))
                        ) : ad.placement ? (
                          // Fallback for older data or if placements is somehow empty
                          <Badge variant="outline">
                            {ad.placement.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </Badge>
                        ) : (
                          <span className="text-xs text-muted-green">N/A</span>
                        )}
                      </div>

                      {/* TODO: Display target specialty name (requires join or separate fetch) */}
                      {ad.target_specialty_id && (
                        <p className="text-xs mt-1">
                          <span className="text-muted-green">Specialty ID:</span> {ad.target_specialty_id}
                        </p>
                      )}
                       {ad.target_locations && ad.target_locations.length > 0 && (
                        <p className="text-xs mt-1">
                          <span className="text-muted-green">Locations:</span> {ad.target_locations.join(', ')}
                        </p>
                      )}
                    </TableCell>
                    {/* Actions Cell */}
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {/* Update Edit handler */}
                          <DropdownMenuItem onClick={() => handleEditClick(ad)}>
                            <Edit className="mr-2 h-4 w-4" />
                            <span>Edit Ad</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem /* onClick={() => handleViewDetails(ad)} */ > {/* TODO: Implement View Details */}
                            <Eye className="mr-2 h-4 w-4" />
                            <span>View Details</span>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          {(ad.status === 'active' || ad.status === 'paused') && (
                            <DropdownMenuItem onClick={() => handleToggleAdStatus(ad)}>
                              {ad.status === 'active' ? (
                                <>
                                  <EyeOff className="mr-2 h-4 w-4" />
                                  <span>Pause Ad</span>
                                </>
                              ) : (
                                <>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>Activate Ad</span>
                                </>
                              )}
                            </DropdownMenuItem>
                          )}
                           {/* Add Delete option */}
                           <DropdownMenuItem
                             className="text-red-600 focus:bg-red-100 focus:text-red-700"
                             onClick={(e) => {
                               e.stopPropagation(); // Prevent dropdown from closing immediately if needed
                               handleDeleteClick(ad.id); // Trigger confirmation for single ad
                             }}
                           >
                             <Trash2 className="mr-2 h-4 w-4" />
                             <span>Delete Ad</span>
                           </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>)
            : (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center"> {/* Increased colSpan */}
                  {loading ? 'Loading ads...' : 'No ads found matching the criteria.'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Edit Ad Dialog */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
         {/* Add max-height and overflow for scrolling */}
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Ad</DialogTitle>
            {/* Use DialogDescription again */}
            <DialogDescription>
              Update the details for the ad campaign: {editingAd?.title}
            </DialogDescription>
          </DialogHeader>
          {/* Ad Form for Editing */}
          {editingAd && ( // Only render form if an ad is being edited
            <AdForm
              ad={editingAd} // Pass the ad data to the form
              onSubmit={handleUpdateSubmit}
              onCancel={() => {
                setIsEditModalOpen(false);
                setEditingAd(null); // Clear editing state on cancel
              }}
              isLoading={isSubmitting}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog (for single delete from dropdown) */}
      <AlertDialog open={isDeleteDialogOpen && !!adToDelete} onOpenChange={(open) => { if (!open) setAdToDelete(null); setIsDeleteDialogOpen(open); }}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the ad titled "{ads.find(ad => ad.id === adToDelete)?.title}".
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={performDelete} disabled={isDeleting} className="bg-red-600 hover:bg-red-700">
               {isDeleting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

    </div>
  );
}
