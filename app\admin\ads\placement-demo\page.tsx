"use client"

import { useState } from "react"
import { AdPlacementSelector } from "../ad-placement-selector"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function AdPlacementDemoPage() {
  const [placement, setPlacement] = useState("home:banner")
  const [adTitle, setAdTitle] = useState("Sample Advertisement")
  const [adDescription, setAdDescription] = useState("This is a sample ad to demonstrate the placement selector")
  const [adImage, setAdImage] = useState("")
  const [adLink, setAdLink] = useState("https://example.com")

  const handlePlacementChange = (newPlacement: string) => {
    console.log("Placement changed:", newPlacement)
    setPlacement(newPlacement)
  }

  const handleSave = () => {
    alert(`Ad saved with placement: ${placement}`)
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Ad Placement Demo</h1>
      <p className="text-muted-green mb-8">
        This page demonstrates how the ad placement selector works in the admin dashboard.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Ad Details</CardTitle>
              <CardDescription>Enter the details for your advertisement</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Ad Title</Label>
                <Input 
                  id="title" 
                  value={adTitle} 
                  onChange={(e) => setAdTitle(e.target.value)} 
                  placeholder="Enter ad title" 
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description">Ad Description</Label>
                <Textarea 
                  id="description" 
                  value={adDescription} 
                  onChange={(e) => setAdDescription(e.target.value)} 
                  placeholder="Enter ad description" 
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="image">Image URL</Label>
                <Input 
                  id="image" 
                  value={adImage} 
                  onChange={(e) => setAdImage(e.target.value)} 
                  placeholder="Enter image URL" 
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="link">Target URL</Label>
                <Input 
                  id="link" 
                  value={adLink} 
                  onChange={(e) => setAdLink(e.target.value)} 
                  placeholder="Enter target URL" 
                />
              </div>
              
              <Button onClick={handleSave} className="w-full">Save Advertisement</Button>
            </CardContent>
          </Card>
        </div>
        
        <div>
          <AdPlacementSelector 
            onPlacementChange={handlePlacementChange} 
            initialPlacement={placement} 
          />
        </div>
      </div>
      
      <div className="mt-12">
        <Card>
          <CardHeader>
            <CardTitle>Ad Preview</CardTitle>
            <CardDescription>This is how your ad will look on the selected page</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border rounded-md p-4">
              <div className="text-sm text-muted-green mb-2">
                Placement: <code>{placement}</code>
              </div>
              
              <div className="bg-background/10 rounded-md p-4 flex flex-col items-center">
                {adImage && (
                  <div className="mb-2 w-full max-w-xs">
                    <img 
                      src={adImage} 
                      alt={adTitle} 
                      className="w-full h-auto rounded-md"
                      onError={(e) => {
                        e.currentTarget.src = "https://placehold.co/300x200/00ff80/ffffff?text=Ad+Preview"
                      }}
                    />
                  </div>
                )}
                {!adImage && (
                  <div className="mb-2 w-full max-w-xs bg-primary/20 h-[200px] rounded-md flex items-center justify-center">
                    <span className="text-primary">Ad Image Placeholder</span>
                  </div>
                )}
                <h3 className="text-lg font-bold">{adTitle}</h3>
                <p className="text-sm text-center">{adDescription}</p>
                <Button size="sm" className="mt-2">
                  <a href={adLink} target="_blank" rel="noopener noreferrer">
                    Learn More
                  </a>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
