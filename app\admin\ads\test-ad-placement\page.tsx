"use client"

import { useState } from "react"
import { PageWithAds } from "@/components/layout/page-with-ads"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { useRouter } from "next/navigation"

export default function TestAdPlacementPage() {
  const router = useRouter();
  const [selectedPage, setSelectedPage] = useState("home");
  const [showBannerAd, setShowBannerAd] = useState(true);
  const [showSidebarAds, setShowSidebarAds] = useState(true);
  const [showBottomAd, setShowBottomAd] = useState(true);
  const [showTestAds, setShowTestAds] = useState(true);

  // Map of page IDs to their display names
  const pages = {
    "home": "Home Page",
    "about": "About Us",
    "standings": "Standings",
    "divisions": "Divisions",
    "specialties": "Specialties",
    "teams": "Teams",
    "head-to-head": "Head to Head",
    "doctor-profile": "Doctor Profile",
    "ratings": "Ratings"
  };

  return (
    <PageWithAds
      pageName={selectedPage}
      showBannerAd={showBannerAd}
      showSidebarAds={showSidebarAds}
      showBottomAd={showBottomAd}
      showTestAds={showTestAds}
    >
      <div className="container mx-auto py-10">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">Ad Placement Test</h1>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => router.push('/admin/ads')}>
              Back to Ad Management
            </Button>
            <Button variant="outline" onClick={() => router.push('/admin/ads/visual-placement-demo')}>
              Visual Placement Demo
            </Button>
          </div>
        </div>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Test Ad Placements</CardTitle>
            <CardDescription>
              This page demonstrates how ads will appear on different pages of the site.
              Select a page type and toggle which ad positions to display.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <Label className="mb-2 block">Select Page Type</Label>
                <Tabs value={selectedPage} onValueChange={setSelectedPage}>
                  <TabsList className="grid grid-cols-3 mb-4">
                    {Object.entries(pages).map(([id, name]) => (
                      <TabsTrigger key={id} value={id}>
                        {name}
                      </TabsTrigger>
                    ))}
                  </TabsList>
                </Tabs>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="banner-ad"
                    checked={showBannerAd}
                    onCheckedChange={setShowBannerAd}
                  />
                  <Label htmlFor="banner-ad">Show Banner Ad</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch
                    id="sidebar-ads"
                    checked={showSidebarAds}
                    onCheckedChange={setShowSidebarAds}
                  />
                  <Label htmlFor="sidebar-ads">Show Sidebar Ads</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch
                    id="bottom-ad"
                    checked={showBottomAd}
                    onCheckedChange={setShowBottomAd}
                  />
                  <Label htmlFor="bottom-ad">Show Bottom Ad</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch
                    id="test-ads"
                    checked={showTestAds}
                    onCheckedChange={setShowTestAds}
                  />
                  <Label htmlFor="test-ads">Show Test Ads</Label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="border rounded-lg p-8 min-h-[400px] bg-background/5">
          <h2 className="text-2xl font-bold mb-4">{pages[selectedPage as keyof typeof pages]} Content</h2>
          <p className="mb-4">
            This is a sample content area that represents the main content of the {pages[selectedPage as keyof typeof pages]}.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="border rounded p-4 bg-background/10">
                <h3 className="font-medium mb-2">Sample Content Block {i + 1}</h3>
                <p className="text-sm text-muted-green">
                  This is sample content to demonstrate how ads would appear alongside real content.
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </PageWithAds>
  );
}
