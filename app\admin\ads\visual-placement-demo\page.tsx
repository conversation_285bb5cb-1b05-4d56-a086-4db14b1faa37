"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { MultiAdPlacementSelector } from "@/app/admin/ads/multi-ad-placement-selector"
import { Placement } from "@/app/admin/ads/types"

// Page templates for visual demo
const PAGE_TEMPLATES = {
  'home': {
    name: 'Home Page',
    sections: [
      { type: 'header', height: '80px' },
      { type: 'hero', height: '400px' },
      { type: 'content', height: '300px' },
      { type: 'footer', height: '100px' }
    ]
  },
  'about': {
    name: 'About Us',
    sections: [
      { type: 'header', height: '80px' },
      { type: 'title', height: '100px' },
      { type: 'content', height: '500px' },
      { type: 'footer', height: '100px' }
    ]
  },
  'standings': {
    name: 'Standings',
    sections: [
      { type: 'header', height: '80px' },
      { type: 'title', height: '100px' },
      { type: 'table', height: '400px' },
      { type: 'footer', height: '100px' }
    ]
  },
  'doctor-profile': {
    name: 'Doctor Profile',
    sections: [
      { type: 'header', height: '80px' },
      { type: 'profile-header', height: '200px' },
      { type: 'profile-content', height: '400px' },
      { type: 'footer', height: '100px' }
    ]
  }
}

export default function VisualPlacementDemoPage() {
  const [placements, setPlacements] = useState<Placement[]>([])
  const [selectedPage, setSelectedPage] = useState('home')
  const [adTitle, setAdTitle] = useState("Sample Advertisement")
  const [adDescription, setAdDescription] = useState("This is a sample ad to demonstrate placement")
  const [adImage, setAdImage] = useState("")
  const [adSize, setAdSize] = useState("728x90")

  // Get ad dimensions from size string (e.g., "728x90" -> { width: 728, height: 90 })
  const getAdDimensions = (size: string) => {
    const [width, height] = size.split('x').map(Number);
    return { width: width || 300, height: height || 250 };
  }

  // Convert placement to style object for positioning
  const getPlacementStyle = (placement: Placement) => {
    if (placement.isCustom && placement.customPosition) {
      return {
        position: 'absolute' as const,
        top: placement.customPosition.top || 'auto',
        left: placement.customPosition.left || 'auto',
        right: placement.customPosition.right || 'auto',
        bottom: placement.customPosition.bottom || 'auto',
        width: placement.customPosition.width || 'auto',
        height: placement.customPosition.height || 'auto',
        transform: (!placement.customPosition.left && !placement.customPosition.right) ? 'translateX(-50%)' : 'none'
      };
    }

    const { width, height } = getAdDimensions(adSize);

    switch (placement.position) {
      case 'banner':
        return {
          position: 'absolute' as const,
          top: '90px', // Just below header
          left: '50%',
          transform: 'translateX(-50%)',
          width: `${width}px`,
          height: `${height}px`,
          maxWidth: '100%'
        };
      case 'sidebar':
        return {
          position: 'absolute' as const,
          top: '200px',
          right: '20px',
          width: '300px',
          height: '600px'
        };
      case 'side-left':
        return {
          position: 'absolute' as const,
          top: '180px',
          left: '20px',
          width: '160px',
          height: '600px'
        };
      case 'side-right':
        return {
          position: 'absolute' as const,
          top: '180px',
          right: '20px',
          width: '160px',
          height: '600px'
        };
      case 'bottom':
        return {
          position: 'absolute' as const,
          bottom: '120px',
          left: '50%',
          transform: 'translateX(-50%)',
          width: `${width}px`,
          height: `${height}px`,
          maxWidth: '100%'
        };
      case 'in-content':
        return {
          position: 'absolute' as const,
          top: '350px',
          left: '50%',
          transform: 'translateX(-50%)',
          width: `${width}px`,
          height: `${height}px`,
          maxWidth: '100%'
        };
      default:
        return {
          position: 'absolute' as const,
          top: '200px',
          left: '50%',
          transform: 'translateX(-50%)',
          width: `${width}px`,
          height: `${height}px`
        };
    }
  };

  // Render an ad based on placement
  const renderAd = (placement: Placement, index: number) => {
    if (placement.page !== selectedPage) return null;

    const style = getPlacementStyle(placement);
    const { width, height } = getAdDimensions(adSize);

    return (
      <div
        key={placement.id || index}
        style={style}
        className="bg-primary/20 border-2 border-primary rounded-md flex flex-col items-center justify-center p-2 z-10"
      >
        <div className="text-xs font-bold mb-1">{placement.position.toUpperCase()} AD</div>
        {adImage ? (
          <img
            src={adImage}
            alt={adTitle}
            style={{ maxWidth: '100%', maxHeight: '80%' }}
            className="rounded mb-1"
            onError={(e) => {
              e.currentTarget.src = `https://placehold.co/${width}x${height}/00ff80/ffffff?text=Ad+Preview`;
            }}
          />
        ) : (
          <div
            className="bg-background/30 rounded flex items-center justify-center mb-1"
            style={{ width: Math.min(width, 200) + 'px', height: Math.min(height, 100) + 'px' }}
          >
            <span className="text-xs text-foreground">Ad Preview</span>
          </div>
        )}
        <div className="text-xs font-medium truncate max-w-full">{adTitle}</div>
        {placement.priority && (
          <div className="absolute top-0 right-0 bg-yellow-500 text-black text-[8px] px-1 rounded-bl">
            PRIORITY
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Visual Ad Placement Demo</h1>
      <p className="text-muted-green mb-8">
        This page helps you visualize how ads will appear on different pages of the site.
      </p>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Ad Settings</CardTitle>
              <CardDescription>Configure your ad and placement</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Ad Title</Label>
                <Input
                  id="title"
                  value={adTitle}
                  onChange={(e) => setAdTitle(e.target.value)}
                  placeholder="Enter ad title"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={adDescription}
                  onChange={(e) => setAdDescription(e.target.value)}
                  placeholder="Enter ad description"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="image">Image URL</Label>
                <Input
                  id="image"
                  value={adImage}
                  onChange={(e) => setAdImage(e.target.value)}
                  placeholder="Enter image URL"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="size">Ad Size</Label>
                <Input
                  id="size"
                  value={adSize}
                  onChange={(e) => setAdSize(e.target.value)}
                  placeholder="e.g., 728x90, 300x250"
                />
                <p className="text-xs text-muted-green">
                  Common sizes: 728x90 (Leaderboard), 300x250 (Medium Rectangle), 160x600 (Skyscraper)
                </p>
              </div>
            </CardContent>
          </Card>

          <div className="mt-6">
            <MultiAdPlacementSelector
              onPlacementsChange={setPlacements}
              initialPlacements={[]}
            />
          </div>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Page Preview</CardTitle>
              <CardDescription>See how your ads will appear on the page</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue={selectedPage} onValueChange={setSelectedPage}>
                <TabsList className="grid grid-cols-4 mb-4">
                  {Object.keys(PAGE_TEMPLATES).map(pageId => (
                    <TabsTrigger key={pageId} value={pageId}>
                      {PAGE_TEMPLATES[pageId as keyof typeof PAGE_TEMPLATES].name}
                    </TabsTrigger>
                  ))}
                </TabsList>

                {Object.entries(PAGE_TEMPLATES).map(([pageId, pageTemplate]) => (
                  <TabsContent key={pageId} value={pageId}>
                    <div className="relative border rounded-md bg-background/10 h-[800px] overflow-hidden">
                      {/* Page Template */}
                      <div className="absolute inset-0 flex flex-col">
                        {pageTemplate.sections.map((section, index) => (
                          <div
                            key={index}
                            className={`
                              w-full flex items-center justify-center text-muted-green
                              ${section.type === 'header' ? 'bg-background/30' : ''}
                              ${section.type === 'footer' ? 'bg-background/30' : ''}
                              ${section.type === 'hero' ? 'bg-primary/5' : ''}
                              ${section.type === 'profile-header' ? 'bg-primary/5' : ''}
                            `}
                            style={{ height: section.height }}
                          >
                            {section.type.replace('-', ' ').toUpperCase()}
                          </div>
                        ))}
                      </div>

                      {/* Render Ads */}
                      {placements.map((placement, index) => renderAd(placement, index))}
                    </div>

                    <div className="mt-4 text-sm text-muted-green">
                      <p>
                        This is a simplified representation of the {pageTemplate.name}.
                        Actual layout may vary slightly.
                      </p>
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
