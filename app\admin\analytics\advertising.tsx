"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON><PERSON>2,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>p,
  <PERSON><PERSON>oint<PERSON>,
  Activity,
  CreditCard,
  Users,
  Eye
} from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  AreaChart,
  Area
} from "recharts"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { createClient } from '@supabase/supabase-js'

// Define ValueType for tooltip formatters
type ValueType = string | number | Array<string | number> | undefined | null;

// Helper function to ensure no NaN values in data
const ensureValidData = (data: any[]): any[] => {
  return data.map(item => {
    const newItem = { ...item };
    Object.keys(newItem).forEach(key => {
      // Convert NaN or undefined to 0 for numerical properties
      if (typeof newItem[key] === 'number' && (isNaN(newItem[key]) || newItem[key] === undefined)) {
        newItem[key] = 0;
      }
    });
    return newItem;
  });
};

// Function to ensure domain values are valid numbers
const safeDomain = (min: number, max: number): [number, number] => {
  return [
    isNaN(min) ? 0 : min,
    isNaN(max) ? 100 : max
  ];
};

// Mock data for visualization - with validation applied
const dailyImpressionsMock = ensureValidData([
  { date: '2023-06-01', impressions: 12500 },
  { date: '2023-06-02', impressions: 13200 },
  { date: '2023-06-03', impressions: 14500 },
  { date: '2023-06-04', impressions: 16000 },
  { date: '2023-06-05', impressions: 17500 },
  { date: '2023-06-06', impressions: 19000 },
  { date: '2023-06-07', impressions: 21000 },
]);

const adPerformanceMock = ensureValidData([
  { name: 'Medical Equipment', impressions: 12000, clicks: 350, ctr: 2.92 },
  { name: 'Health Insurance', impressions: 10500, clicks: 410, ctr: 3.90 },
  { name: 'Pharmaceutical', impressions: 8500, clicks: 280, ctr: 3.29 },
  { name: 'Medical Conference', impressions: 5000, clicks: 150, ctr: 3.00 },
  { name: 'Medical Schools', impressions: 7500, clicks: 200, ctr: 2.67 },
]);

const adRevenueMock = ensureValidData([
  { date: '2023-06-01', revenue: 850 },
  { date: '2023-06-02', revenue: 920 },
  { date: '2023-06-03', revenue: 980 },
  { date: '2023-06-04', revenue: 1050 },
  { date: '2023-06-05', revenue: 1120 },
  { date: '2023-06-06', revenue: 1180 },
  { date: '2023-06-07', revenue: 1250 },
]);

const advertiserEngagementMock = ensureValidData([
  { date: '2023-06-01', newAds: 3, adEdits: 5, adPauses: 1 },
  { date: '2023-06-02', newAds: 2, adEdits: 4, adPauses: 0 },
  { date: '2023-06-03', newAds: 5, adEdits: 3, adPauses: 2 },
  { date: '2023-06-04', newAds: 4, adEdits: 6, adPauses: 1 },
  { date: '2023-06-05', newAds: 2, adEdits: 8, adPauses: 3 },
  { date: '2023-06-06', newAds: 6, adEdits: 4, adPauses: 2 },
  { date: '2023-06-07', newAds: 4, adEdits: 7, adPauses: 1 },
]);

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

// Get Supabase URL and key from environment variables 
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
const supabaseKey = process.env.NEXT_PUBLIC_service_role || ''

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey)

export function AdvertisingDashboard() {
  const [loading, setLoading] = useState(true)
  const [statsData, setStatsData] = useState({
    totalImpressions: 0,
    averageCTR: 0,
    dailyRevenue: 0,
    revenuePerUser: 0,
  })

  useEffect(() => {
    // In a real implementation, you would fetch actual data from Supabase here
    // For now, we'll simulate loading with a timeout and set mock data
    const timer = setTimeout(() => {
      setStatsData({
        totalImpressions: 21000,
        averageCTR: 3.15,
        dailyRevenue: 1250,
        revenuePerUser: 0.48,
      })
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="space-y-4">
      {/* Summary Stats */}
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Impressions (24h)</CardTitle>
            <Eye className="h-4 w-4 text-muted-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : `${statsData.totalImpressions.toLocaleString()}`}
            </div>
            <p className="text-xs text-muted-green">
              <span className="text-green-500">+10.5%</span> from yesterday
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average CTR</CardTitle>
            <MousePointer className="h-4 w-4 text-muted-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : `${statsData.averageCTR}%`}
            </div>
            <p className="text-xs text-muted-green">
              <span className="text-green-500">+0.2%</span> from yesterday
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Daily Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : `$${statsData.dailyRevenue}`}
            </div>
            <p className="text-xs text-muted-green">
              <span className="text-green-500">+5.9%</span> from yesterday
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenue per User</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : `$${statsData.revenuePerUser}`}
            </div>
            <p className="text-xs text-muted-green">
              <span className="text-green-500">+0.03$</span> from yesterday
            </p>
          </CardContent>
        </Card>
      </div>
      
      {/* Detailed Analysis Tabs */}
      <Tabs defaultValue="impressions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="impressions">Ad Impressions</TabsTrigger>
          <TabsTrigger value="performance">Ad Performance</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="advertisers">Advertisers</TabsTrigger>
        </TabsList>
        
        {/* Ad Impressions Tab */}
        <TabsContent value="impressions" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 xl:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Daily Impressions</CardTitle>
                <CardDescription>Total ad impressions per day</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={dailyImpressionsMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="impressions" stroke="#0088FE" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Impressions by Ad Type</CardTitle>
                <CardDescription>Distribution across different ad formats</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'Banner Ads', value: 45 },
                        { name: 'Sidebar Ads', value: 25 },
                        { name: 'Doctor Profile Ads', value: 20 },
                        { name: 'Search Result Ads', value: 10 },
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {[
                        { name: 'Banner Ads', value: 45 },
                        { name: 'Sidebar Ads', value: 25 },
                        { name: 'Doctor Profile Ads', value: 20 },
                        { name: 'Search Result Ads', value: 10 },
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1 xl:col-span-2">
              <CardHeader>
                <CardTitle>Impressions by Page</CardTitle>
                <CardDescription>Ad views per page section</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={[
                    { page: 'Home Page', impressions: 7500 },
                    { page: 'Doctor Profiles', impressions: 6200 },
                    { page: 'Search Results', impressions: 4800 },
                    { page: 'Specialty Pages', impressions: 3500 },
                    { page: 'Country Pages', impressions: 2800 },
                    { page: 'Rating Pages', impressions: 2200 },
                    { page: 'About/Contact', impressions: 1000 },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="page" />
                    <YAxis />
                    <Tooltip formatter={(value: ValueType) => 
                      typeof value === 'number' ? [`${value.toLocaleString()}`, 'Impressions'] : ['N/A', 'Impressions']
                    } />
                    <Legend />
                    <Bar dataKey="impressions" fill="#0088FE" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Ad Performance Tab */}
        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 xl:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Click-Through Rate by Ad</CardTitle>
                <CardDescription>CTR percentage by advertisement type</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart layout="vertical" data={adPerformanceMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="name" type="category" />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="ctr" fill="#00C49F" label={{ position: 'right', formatter: (value: any) => `${value}%` }} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Clicks vs. Impressions</CardTitle>
                <CardDescription>Comparison by ad type</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={adPerformanceMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis yAxisId="left" orientation="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Bar yAxisId="left" dataKey="impressions" fill="#0088FE" name="Impressions" />
                    <Bar yAxisId="right" dataKey="clicks" fill="#FFBB28" name="Clicks" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1 xl:col-span-2">
              <CardHeader>
                <CardTitle>Conversion Funnel</CardTitle>
                <CardDescription>User journey through the advertising funnel</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={[
                    { stage: 'Ad Impressions', count: 21000 },
                    { stage: 'Ad Clicks', count: 630 },
                    { stage: 'Page Views', count: 520 },
                    { stage: 'Form Interactions', count: 180 },
                    { stage: 'Conversions', count: 45 },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="stage" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="count" fill="#8884D8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
              
              <CardFooter>
                <div className="w-full space-y-2">
                  <div className="grid grid-cols-5 gap-2 text-xs text-muted-green">
                    <div className="text-center">100%</div>
                    <div className="text-center">3.0%</div>
                    <div className="text-center">2.5%</div>
                    <div className="text-center">0.9%</div>
                    <div className="text-center">0.2%</div>
                  </div>
                  <div className="flex w-full gap-1">
                    <div className="h-2 w-full bg-[#8884D8]"></div>
                    <div className="h-2 w-[3%] bg-[#8884D8]"></div>
                    <div className="h-2 w-[2.5%] bg-[#8884D8]"></div>
                    <div className="h-2 w-[0.9%] bg-[#8884D8]"></div>
                    <div className="h-2 w-[0.2%] bg-[#8884D8]"></div>
                  </div>
                </div>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
        
        {/* Revenue Tab */}
        <TabsContent value="revenue" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 xl:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Daily Ad Revenue</CardTitle>
                <CardDescription>Revenue generated from ads per day</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={adRevenueMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip formatter={(value: ValueType) => 
                      typeof value === 'number' ? [`$${value.toLocaleString()}`, 'Revenue'] : ['N/A', 'Revenue']
                    } />
                    <Legend />
                    <Line type="monotone" dataKey="revenue" stroke="#00C49F" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Revenue by Ad Type</CardTitle>
                <CardDescription>Distribution of revenue sources</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'Premium Banner', value: 520 },
                        { name: 'Sidebar Ads', value: 310 },
                        { name: 'Sponsored Listings', value: 250 },
                        { name: 'Search Result Ads', value: 170 },
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {[
                        { name: 'Premium Banner', value: 520 },
                        { name: 'Sidebar Ads', value: 310 },
                        { name: 'Sponsored Listings', value: 250 },
                        { name: 'Search Result Ads', value: 170 },
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: ValueType) => 
                      typeof value === 'number' ? [`$${value.toLocaleString()}`, 'Revenue'] : ['N/A', 'Revenue']
                    } />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Revenue per User Trend</CardTitle>
                <CardDescription>Average revenue per active user</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={ensureValidData(Array.from({ length: 30 }, (_, i) => ({
                    date: `6/${i + 1}`,
                    arpu: 0.3 + parseFloat((Math.random() * 0.2).toFixed(2))
                  })))}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis domain={safeDomain(0, 0.6)} />
                    <Tooltip formatter={(value: ValueType) => 
                      typeof value === 'number' ? [`$${value.toLocaleString()}`, 'Revenue per User'] : ['N/A', 'Revenue per User']
                    } />
                    <Legend />
                    <Line type="monotone" dataKey="arpu" stroke="#FF8042" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Monthly Revenue Forecast</CardTitle>
                <CardDescription>Projected vs actual revenue</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={[
                    { month: 'Jan', actual: 18500, projected: 18000 },
                    { month: 'Feb', actual: 22000, projected: 20000 },
                    { month: 'Mar', actual: 26500, projected: 25000 },
                    { month: 'Apr', actual: 29000, projected: 28000 },
                    { month: 'May', actual: 33500, projected: 32000 },
                    { month: 'Jun', actual: 38000, projected: 36000 },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value: ValueType) => 
                      typeof value === 'number' ? [`$${value.toLocaleString()}`, 'Revenue'] : ['N/A', 'Revenue']
                    } />
                    <Legend />
                    <Line type="monotone" dataKey="projected" stroke="#8884D8" strokeWidth={2} strokeDasharray="5 5" />
                    <Line type="monotone" dataKey="actual" stroke="#0088FE" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Advertisers Tab */}
        <TabsContent value="advertisers" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 xl:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Advertiser Engagement</CardTitle>
                <CardDescription>Daily advertiser activity</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={advertiserEngagementMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="newAds" fill="#0088FE" name="New Ads Created" />
                    <Bar dataKey="adEdits" fill="#00C49F" name="Ads Edited" />
                    <Bar dataKey="adPauses" fill="#FF8042" name="Ads Paused" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Top Advertisers</CardTitle>
                <CardDescription>By spending amount (last 30 days)</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart layout="vertical" data={[
                    { name: 'Med Equipment Co.', spend: 5200 },
                    { name: 'Health Insurance Inc.', spend: 4700 },
                    { name: 'PharmaCorp Ltd.', spend: 4100 },
                    { name: 'Medical University', spend: 3800 },
                    { name: 'Healthcare Solutions', spend: 3500 },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="name" type="category" />
                    <Tooltip formatter={(value: ValueType) => 
                      typeof value === 'number' ? [`$${value.toLocaleString()}`, 'Monthly Spend'] : ['N/A', 'Monthly Spend']
                    } />
                    <Legend />
                    <Bar dataKey="spend" fill="#FFBB28" label={{ position: 'right', formatter: (value: any) => `$${value}` }} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Advertiser Retention</CardTitle>
                <CardDescription>Monthly retention rate for advertisers</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={[
                    { month: 'Jan', retention: 92 },
                    { month: 'Feb', retention: 94 },
                    { month: 'Mar', retention: 91 },
                    { month: 'Apr', retention: 95 },
                    { month: 'May', retention: 96 },
                    { month: 'Jun', retention: 97 },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis domain={safeDomain(80, 100)} />
                    <Tooltip formatter={(value: ValueType) => 
                      typeof value === 'number' ? [`${value.toLocaleString()}%`, 'Retention Rate'] : ['N/A', 'Retention Rate']
                    } />
                    <Legend />
                    <Line type="monotone" dataKey="retention" stroke="#8884D8" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Ad Conversion by Category</CardTitle>
                <CardDescription>Conversion rates by advertiser type</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={[
                    { category: 'Medical Equipment', rate: 3.2 },
                    { category: 'Pharmaceuticals', rate: 2.8 },
                    { category: 'Health Insurance', rate: 3.9 },
                    { category: 'Medical Education', rate: 4.5 },
                    { category: 'Healthcare IT', rate: 2.6 },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="category" />
                    <YAxis />
                    <Tooltip formatter={(value: ValueType) => 
                      typeof value === 'number' ? [`${value.toLocaleString()}%`, 'Conversion Rate'] : ['N/A', 'Conversion Rate']
                    } />
                    <Legend />
                    <Bar dataKey="rate" fill="#8884D8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
} 