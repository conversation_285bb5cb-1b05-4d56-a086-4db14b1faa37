"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Clock,
  AlertCircle,
  CheckCircle,
  ThumbsUp,
  ThumbsDown,
  HelpCircle,
  RefreshCw,
  UserPlus,
  CheckSquare
} from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  AreaChart,
  Area
} from "recharts"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { createClient } from '@supabase/supabase-js'

// Get Supabase URL and key from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
const supabaseKey = process.env.NEXT_PUBLIC_service_role || ''

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey)

// Define ValueType for tooltip formatters
type ValueType = string | number | Array<string | number> | undefined | null;

// Mock data for visualization
const supportTicketsMock = [
  { date: '2023-06-01', tickets: 28 },
  { date: '2023-06-02', tickets: 25 },
  { date: '2023-06-03', tickets: 32 },
  { date: '2023-06-04', tickets: 24 },
  { date: '2023-06-05', tickets: 18 },
  { date: '2023-06-06', tickets: 22 },
  { date: '2023-06-07', tickets: 20 },
];

const resolutionTimesMock = [
  { category: 'Account Issues', time: 4.2 },
  { category: 'Doctor Listings', time: 2.8 },
  { category: 'Rating Issues', time: 1.5 },
  { category: 'Payment Problems', time: 6.7 },
  { category: 'App Performance', time: 3.2 },
  { category: 'Feature Requests', time: 2.5 },
];

const ticketCategoriesMock = [
  { name: 'Account Issues', value: 35 },
  { name: 'App Bugs', value: 25 },
  { name: 'Doctor Listings', value: 15 },
  { name: 'Rating System', value: 18 },
  { name: 'Other', value: 7 },
];

const userSatisfactionMock = [
  { date: '2023-05', very_satisfied: 45, satisfied: 38, neutral: 10, unsatisfied: 5, very_unsatisfied: 2 },
  { date: '2023-06', very_satisfied: 48, satisfied: 36, neutral: 11, unsatisfied: 4, very_unsatisfied: 1 },
  { date: '2023-07', very_satisfied: 52, satisfied: 35, neutral: 8, unsatisfied: 4, very_unsatisfied: 1 },
];

const featureRequestsMock = [
  { feature: 'Multi-Doctor Comparison', votes: 156 },
  { feature: 'Mobile App Notifications', votes: 124 },
  { feature: 'Video Consultations', votes: 98 },
  { feature: 'Patient Records Access', votes: 87 },
  { feature: 'Appointment Booking', votes: 75 },
  { feature: 'Medical History Upload', votes: 62 },
];

const sentimentAnalysisMock = [
  { month: 'Jan', positive: 68, neutral: 22, negative: 10 },
  { month: 'Feb', positive: 65, neutral: 25, negative: 10 },
  { month: 'Mar', positive: 70, neutral: 20, negative: 10 },
  { month: 'Apr', positive: 72, neutral: 20, negative: 8 },
  { month: 'May', positive: 75, neutral: 18, negative: 7 },
  { month: 'Jun', positive: 78, neutral: 16, negative: 6 },
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#d80000'];

export function FeedbackDashboard() {
  const [loading, setLoading] = useState(true)
  const [statsData, setStatsData] = useState({
    averageResolutionTime: 0,
    satisfactionRate: 0,
    openTickets: 0,
    feedbackResponseRate: 0,
  })

  useEffect(() => {
    // In a real implementation, you would fetch actual data from Supabase here
    // For now, we'll simulate loading with a timeout and set mock data
    const timer = setTimeout(() => {
      setStatsData({
        averageResolutionTime: 3.5,
        satisfactionRate: 88,
        openTickets: 24,
        feedbackResponseRate: 94,
      })
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="space-y-4">
      {/* Summary Stats */}
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Open Tickets</CardTitle>
            <HelpCircle className="h-4 w-4 text-muted-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : statsData.openTickets}
            </div>
            <p className="text-xs text-muted-green">
              <span className="text-green-500">-15%</span> from last week
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Resolution Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : `${statsData.averageResolutionTime} hours`}
            </div>
            <p className="text-xs text-muted-green">
              <span className="text-green-500">-0.5 hours</span> from last week
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Satisfaction Rate</CardTitle>
            <ThumbsUp className="h-4 w-4 text-muted-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : `${statsData.satisfactionRate}%`}
            </div>
            <p className="text-xs text-muted-green">
              <span className="text-green-500">+3%</span> from last month
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Feedback Response Rate</CardTitle>
            <CheckSquare className="h-4 w-4 text-muted-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : `${statsData.feedbackResponseRate}%`}
            </div>
            <p className="text-xs text-muted-green">
              <span className="text-green-500">+2%</span> from last month
            </p>
          </CardContent>
        </Card>
      </div>
      
      {/* Detailed Analysis Tabs */}
      <Tabs defaultValue="support" className="space-y-4">
        <TabsList>
          <TabsTrigger value="support">Support Tickets</TabsTrigger>
          <TabsTrigger value="satisfaction">User Satisfaction</TabsTrigger>
          <TabsTrigger value="requests">Feature Requests</TabsTrigger>
          <TabsTrigger value="sentiment">Review Sentiment</TabsTrigger>
        </TabsList>
        
        {/* Support Tickets Tab */}
        <TabsContent value="support" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 xl:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Daily Support Tickets</CardTitle>
                <CardDescription>Number of support tickets per day</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={supportTicketsMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="tickets" stroke="#0088FE" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Support Ticket Categories</CardTitle>
                <CardDescription>Distribution of ticket types</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={ticketCategoriesMock}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {ticketCategoriesMock.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Average Resolution Time</CardTitle>
                <CardDescription>By support ticket category (hours)</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart layout="vertical" data={resolutionTimesMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="category" type="category" />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="time" fill="#00C49F" label={{ position: 'right', formatter: (value: ValueType) => `${value}h` }} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Current Ticket Status</CardTitle>
                <CardDescription>Distribution of open tickets by status</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'New', value: 35 },
                        { name: 'In Progress', value: 40 },
                        { name: 'Awaiting User', value: 15 },
                        { name: 'Pending Review', value: 10 },
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {[
                        { name: 'New', value: 35 },
                        { name: 'In Progress', value: 40 },
                        { name: 'Awaiting User', value: 15 },
                        { name: 'Pending Review', value: 10 },
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Recent Support Tickets</CardTitle>
              <CardDescription>Last 5 support tickets received</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Array.from({ length: 5 }, (_, i) => (
                  <Alert key={i} variant={i === 0 ? "default" : "default"}>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium">
                            {i === 0 ? 'Unable to submit doctor rating' : 
                             i === 1 ? 'Doctor profile information inaccurate' :
                             i === 2 ? 'Cannot reset password' :
                             i === 3 ? 'Doctor ranking seems incorrect' :
                             'Mobile view shows broken layout'}
                          </p>
                          <p className="text-sm text-muted-green">
                            {i === 0 ? 'User reported error when submitting rating form for Dr. Smith' : 
                             i === 1 ? 'Specialty information does not match doctor profile' :
                             i === 2 ? 'Reset password email never received' :
                             i === 3 ? 'Rankings dont match actual ratings and reviews' :
                             'Layout breaks on iPhone when viewing specialty pages'}
                          </p>
                        </div>
                        <Badge variant={
                          i === 0 ? "outline" : 
                          i === 1 ? "secondary" :
                          i === 2 ? "destructive" :
                          i === 3 ? "outline" :
                          "secondary"
                        }>
                          {i === 0 ? 'New' : 
                           i === 1 ? 'In Progress' :
                           i === 2 ? 'Urgent' :
                           i === 3 ? 'New' :
                           'In Progress'}
                        </Badge>
                      </div>
                    </AlertDescription>
                  </Alert>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* User Satisfaction Tab */}
        <TabsContent value="satisfaction" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 xl:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>User Satisfaction Survey Results</CardTitle>
                <CardDescription>Distribution of satisfaction ratings</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={userSatisfactionMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="very_satisfied" stackId="a" fill="#00C49F" name="Very Satisfied" />
                    <Bar dataKey="satisfied" stackId="a" fill="#0088FE" name="Satisfied" />
                    <Bar dataKey="neutral" stackId="a" fill="#FFBB28" name="Neutral" />
                    <Bar dataKey="unsatisfied" stackId="a" fill="#FF8042" name="Unsatisfied" />
                    <Bar dataKey="very_unsatisfied" stackId="a" fill="#d80000" name="Very Unsatisfied" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Satisfaction by User Type</CardTitle>
                <CardDescription>Distribution across different user groups</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={[
                    { type: 'New Users', score: 87 },
                    { type: 'Returning Users', score: 92 },
                    { type: 'Power Users', score: 95 },
                    { type: 'Doctors', score: 89 },
                    { type: 'Patients', score: 91 },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="type" />
                    <YAxis domain={[70, 100]} />
                    <Tooltip formatter={(value) => [`${value}%`, 'Satisfaction Score']} />
                    <Legend />
                    <Bar dataKey="score" fill="#0088FE" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1 xl:col-span-2">
              <CardHeader>
                <CardTitle>Satisfaction by Feature</CardTitle>
                <CardDescription>User satisfaction ratings for different app features</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart layout="vertical" data={[
                    { feature: 'Doctor Search', score: 92 },
                    { feature: 'Doctor Profiles', score: 88 },
                    { feature: 'Rating System', score: 85 },
                    { feature: 'User Registration', score: 80 },
                    { feature: 'Mobile Experience', score: 78 },
                    { feature: 'Review Submission', score: 90 },
                    { feature: 'Rankings & Leaderboards', score: 95 },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" domain={[70, 100]} />
                    <YAxis dataKey="feature" type="category" />
                    <Tooltip formatter={(value: ValueType) => [`${value}%`, 'Satisfaction Score']} />
                    <Legend />
                    <Bar dataKey="score" fill="#00C49F" label={{ position: 'right', formatter: (value: ValueType) => `${value}%` }} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Feature Requests Tab */}
        <TabsContent value="requests" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 xl:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Top Feature Requests</CardTitle>
                <CardDescription>Most requested features by user votes</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart layout="vertical" data={featureRequestsMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="feature" type="category" />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="votes" fill="#FFBB28" label={{ position: 'right' }} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Feature Requests by User Type</CardTitle>
                <CardDescription>Distribution of requests by user segment</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'Patients', value: 55 },
                        { name: 'Doctors', value: 25 },
                        { name: 'Medical Students', value: 12 },
                        { name: 'Other Healthcare', value: 8 },
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {[
                        { name: 'Patients', value: 55 },
                        { name: 'Doctors', value: 25 },
                        { name: 'Medical Students', value: 12 },
                        { name: 'Other Healthcare', value: 8 },
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1 xl:col-span-2">
              <CardHeader>
                <CardTitle>Recent Feature Suggestions</CardTitle>
                <CardDescription>Latest feature requests from users</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { title: 'Multi-Doctor Comparison', description: 'Allow users to compare multiple doctors side-by-side', votes: 156, status: 'Under Review' },
                    { title: 'Mobile App Notifications', description: 'Send push notifications for new reviews and ratings', votes: 124, status: 'Planned' },
                    { title: 'Video Consultations', description: 'Enable video consultations through the platform', votes: 98, status: 'Considering' },
                    { title: 'Patient Records Access', description: 'Allow patients to securely access medical records', votes: 87, status: 'Under Review' },
                    { title: 'Appointment Booking', description: 'Integrate appointment booking system with doctor calendars', votes: 75, status: 'Planned' },
                  ].map((request, i) => (
                    <div key={i} className="flex justify-between items-center p-4 border rounded-md">
                      <div>
                        <p className="font-medium">{request.title}</p>
                        <p className="text-sm text-muted-green">{request.description}</p>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center">
                          <ThumbsUp className="h-4 w-4 mr-1 text-muted-green" />
                          <span className="text-sm">{request.votes}</span>
                        </div>
                        <Badge variant={
                          request.status === 'Planned' ? "default" : 
                          request.status === 'Under Review' ? "secondary" :
                          "outline"
                        }>
                          {request.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Review Sentiment Tab */}
        <TabsContent value="sentiment" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 xl:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Review Sentiment Analysis</CardTitle>
                <CardDescription>Distribution of sentiment in user reviews</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={sentimentAnalysisMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area type="monotone" dataKey="positive" stackId="1" stroke="#00C49F" fill="#00C49F" fillOpacity={0.8} name="Positive" />
                    <Area type="monotone" dataKey="neutral" stackId="1" stroke="#FFBB28" fill="#FFBB28" fillOpacity={0.8} name="Neutral" />
                    <Area type="monotone" dataKey="negative" stackId="1" stroke="#FF8042" fill="#FF8042" fillOpacity={0.8} name="Negative" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Common Words in Reviews</CardTitle>
                <CardDescription>Most frequently used terms in user reviews</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart layout="vertical" data={[
                    { word: 'Professional', count: 245 },
                    { word: 'Helpful', count: 198 },
                    { word: 'Knowledgeable', count: 187 },
                    { word: 'Caring', count: 156 },
                    { word: 'Experienced', count: 145 },
                    { word: 'Recommended', count: 132 },
                    { word: 'Excellent', count: 120 },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="word" type="category" />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="count" fill="#8884D8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1 xl:col-span-2">
              <CardHeader>
                <CardTitle>Sentiment by Doctor Specialty</CardTitle>
                <CardDescription>Positive sentiment percentage across medical specialties</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={[
                    { specialty: 'Cardiology', positive: 82, neutral: 12, negative: 6 },
                    { specialty: 'Dermatology', positive: 88, neutral: 8, negative: 4 },
                    { specialty: 'Neurology', positive: 75, neutral: 15, negative: 10 },
                    { specialty: 'Pediatrics', positive: 90, neutral: 7, negative: 3 },
                    { specialty: 'Orthopedics', positive: 78, neutral: 14, negative: 8 },
                    { specialty: 'Psychiatry', positive: 72, neutral: 18, negative: 10 },
                    { specialty: 'Oncology', positive: 80, neutral: 13, negative: 7 },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="specialty" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="positive" stackId="a" fill="#00C49F" name="Positive" />
                    <Bar dataKey="neutral" stackId="a" fill="#FFBB28" name="Neutral" />
                    <Bar dataKey="negative" stackId="a" fill="#FF8042" name="Negative" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
} 