"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { createClient } from '@supabase/supabase-js'
import { CheckCircle, AlertCircle, Loader2 } from "lucide-react"

// Get Supabase URL and key from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
const supabaseKey = process.env.NEXT_PUBLIC_service_role || ''

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey)

export default function InitDatabasePage() {
  const [loading, setLoading] = useState(false)
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [message, setMessage] = useState("")

  async function initializeAnalyticsTables() {
    setLoading(true)
    setStatus('idle')
    setMessage("")
    
    try {
      // Create analytics_daily_metrics table if it doesn't exist
      const { error: dailyMetricsError } = await supabase.from('analytics_daily_metrics').select('*').limit(1)
      
      if (dailyMetricsError) {
        console.log('Creating analytics_daily_metrics table...')
        const { error: createError } = await supabase.rpc('create_analytics_daily_metrics_table')
        
        if (createError) {
          throw new Error(`Error creating daily metrics table: ${createError.message}`)
        }
        
        // Insert sample data
        await insertSampleDailyMetrics()
      } else {
        console.log('analytics_daily_metrics table already exists')
      }
      
      // Create analytics_monthly_metrics table if it doesn't exist
      const { error: monthlyMetricsError } = await supabase.from('analytics_monthly_metrics').select('*').limit(1)
      
      if (monthlyMetricsError) {
        console.log('Creating analytics_monthly_metrics table...')
        const { error: createError } = await supabase.rpc('create_analytics_monthly_metrics_table')
        
        if (createError) {
          throw new Error(`Error creating monthly metrics table: ${createError.message}`)
        }
        
        // Insert sample data
        await insertSampleMonthlyMetrics()
      } else {
        console.log('analytics_monthly_metrics table already exists')
      }
      
      setStatus('success')
      setMessage("Analytics tables initialized successfully! You can now view the analytics dashboard.")
    } catch (error) {
      console.error('Error initializing tables:', error)
      setStatus('error')
      setMessage(error instanceof Error ? error.message : "Unknown error occurred")
      
      // Create the stored procedures that will help with table creation
      await createHelperFunctions()
    } finally {
      setLoading(false)
    }
  }
  
  async function createHelperFunctions() {
    try {
      // Function to create the daily metrics table
      await supabase.rpc('exec_sql', {
        sql_string: `
          CREATE OR REPLACE FUNCTION create_analytics_daily_metrics_table()
          RETURNS void AS $$
          BEGIN
            CREATE TABLE IF NOT EXISTS analytics_daily_metrics (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              date DATE UNIQUE NOT NULL,
              daily_active_users INTEGER NOT NULL DEFAULT 0,
              registered_users INTEGER NOT NULL DEFAULT 0,
              new_users INTEGER NOT NULL DEFAULT 0,
              registered_users_percent DECIMAL(5,2) NOT NULL DEFAULT 0,
              avg_session_duration DECIMAL(10,2) NOT NULL DEFAULT 0,
              ad_impressions INTEGER NOT NULL DEFAULT 0,
              ad_clicks INTEGER NOT NULL DEFAULT 0,
              ad_ctr DECIMAL(5,2) NOT NULL DEFAULT 0,
              ad_revenue DECIMAL(10,2) NOT NULL DEFAULT 0,
              error_count INTEGER NOT NULL DEFAULT 0,
              page_views INTEGER NOT NULL DEFAULT 0,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
          END;
          $$ LANGUAGE plpgsql;
        `
      })
      
      // Function to create the monthly metrics table
      await supabase.rpc('exec_sql', {
        sql_string: `
          CREATE OR REPLACE FUNCTION create_analytics_monthly_metrics_table()
          RETURNS void AS $$
          BEGIN
            CREATE TABLE IF NOT EXISTS analytics_monthly_metrics (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              month DATE UNIQUE NOT NULL,
              monthly_active_users INTEGER NOT NULL DEFAULT 0,
              total_sessions INTEGER NOT NULL DEFAULT 0,
              avg_session_duration DECIMAL(10,2) NOT NULL DEFAULT 0,
              user_growth_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
              retention_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
              avg_daily_active_users INTEGER NOT NULL DEFAULT 0,
              total_page_views INTEGER NOT NULL DEFAULT 0,
              ad_revenue DECIMAL(10,2) NOT NULL DEFAULT 0,
              new_users INTEGER NOT NULL DEFAULT 0,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
          END;
          $$ LANGUAGE plpgsql;
        `
      })
      
      // Function to execute arbitrary SQL (admin only)
      await supabase.rpc('exec_sql', {
        sql_string: `
          CREATE OR REPLACE FUNCTION exec_sql(sql_string text)
          RETURNS void AS $$
          BEGIN
            EXECUTE sql_string;
          END;
          $$ LANGUAGE plpgsql SECURITY DEFINER;
        `
      })
      
      console.log('Helper functions created successfully')
    } catch (error) {
      console.error('Error creating helper functions:', error)
      setMessage((prev) => `${prev}\n\nError creating helper functions: ${error instanceof Error ? error.message : "Unknown error"}`)
    }
  }
  
  async function insertSampleDailyMetrics() {
    try {
      const today = new Date()
      
      // Insert 30 days of sample data
      for (let i = 0; i < 30; i++) {
        const date = new Date(today)
        date.setDate(date.getDate() - i)
        
        // Create some realistic looking random data
        const dailyActiveUsers = Math.floor(1000 + Math.random() * 1000)
        const registeredUsers = Math.floor(dailyActiveUsers * (0.6 + Math.random() * 0.2))
        const registeredPercent = (registeredUsers / dailyActiveUsers) * 100
        const sessionDuration = 4 + Math.random() * 2 // 4-6 minutes
        
        await supabase.from('analytics_daily_metrics').insert({
          date: date.toISOString().split('T')[0],
          daily_active_users: dailyActiveUsers,
          registered_users: registeredUsers,
          new_users: Math.floor(50 + Math.random() * 100),
          registered_users_percent: registeredPercent,
          avg_session_duration: sessionDuration,
          ad_impressions: Math.floor(5000 + Math.random() * 2000),
          ad_clicks: Math.floor(100 + Math.random() * 100),
          ad_ctr: Math.random() * 5,
          ad_revenue: Math.floor(200 + Math.random() * 300),
          error_count: Math.floor(Math.random() * 20),
          page_views: Math.floor(8000 + Math.random() * 4000)
        })
      }
      
      console.log('Sample daily metrics data inserted')
    } catch (error) {
      console.error('Error inserting sample daily metrics:', error)
    }
  }
  
  async function insertSampleMonthlyMetrics() {
    try {
      const today = new Date()
      
      // Insert 6 months of sample data
      for (let i = 0; i < 6; i++) {
        const date = new Date(today)
        date.setMonth(date.getMonth() - i)
        date.setDate(1) // First day of month
        
        // Create some realistic looking random data
        const monthlyActiveUsers = Math.floor(20000 + Math.random() * 10000)
        
        await supabase.from('analytics_monthly_metrics').insert({
          month: date.toISOString().split('T')[0],
          monthly_active_users: monthlyActiveUsers,
          total_sessions: Math.floor(monthlyActiveUsers * (5 + Math.random() * 2)),
          avg_session_duration: 4.5 + Math.random() * 2,
          user_growth_rate: 5 + Math.random() * 5,
          retention_rate: 60 + Math.random() * 20,
          avg_daily_active_users: Math.floor(monthlyActiveUsers / 30),
          total_page_views: Math.floor(monthlyActiveUsers * 10 * (1 + Math.random() * 0.5)),
          ad_revenue: Math.floor(5000 + Math.random() * 2000),
          new_users: Math.floor(monthlyActiveUsers * 0.2)
        })
      }
      
      console.log('Sample monthly metrics data inserted')
    } catch (error) {
      console.error('Error inserting sample monthly metrics:', error)
    }
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8">Initialize Analytics Tables</h1>
      
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Setup Analytics Database</CardTitle>
          <CardDescription>
            This will create the required tables for the analytics dashboard if they don't exist yet,
            and populate them with sample data.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            Click the button below to initialize the analytics tables. This is required before you can view
            the analytics dashboard. This process will:
          </p>
          <ul className="list-disc ml-5 mb-4 space-y-1">
            <li>Create analytics_daily_metrics table</li>
            <li>Create analytics_monthly_metrics table</li>
            <li>Insert sample data to demonstrate the dashboard</li>
          </ul>
          <p className="text-sm text-muted-green">
            Note: If the tables already exist, this process will not modify them.
          </p>
        </CardContent>
        <CardFooter>
          <Button 
            onClick={initializeAnalyticsTables} 
            disabled={loading}
            className="flex items-center gap-2"
          >
            {loading && <Loader2 className="h-4 w-4 animate-spin" />}
            {!loading && status === 'idle' && 'Initialize Analytics Tables'}
            {!loading && status === 'success' && 'Tables Initialized'}
            {!loading && status === 'error' && 'Try Again'}
          </Button>
        </CardFooter>
      </Card>
      
      {status === 'success' && (
        <Alert className="bg-green-50 border-green-500">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>{message}</AlertDescription>
        </Alert>
      )}
      
      {status === 'error' && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{message}</AlertDescription>
        </Alert>
      )}
    </div>
  )
} 