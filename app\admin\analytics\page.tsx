"use client"

import { useState, useEffect } from "react"
import { 
  Users, 
  BarChart2, 
  Activity, 
  Star, 
  LineChart, 
  Globe, 
  Upload,
  AlertCircle,
  Calendar,
  Clock,
  UserCheck,
  UserPlus,
  CreditCard,
  MousePointer,
  Smartphone,
  Server,
  MessageSquare,
  PieChart,
  TrendingUp
} from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { 
  Line, 
  Bar, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer, 
  Pie<PERSON>hart as RechartsPC, 
  Pie, 
  Cell 
} from "recharts"

import { UserEngagementDashboard } from "./user-engagement"
import { PerformanceDashboard } from "./performance"
import { AdvertisingDashboard } from "./advertising"
import { FeedbackDashboard } from "./feedback"
import { AnalyticsDashboard } from "./analytics"
import { createClient } from '@supabase/supabase-js'
import Link from "next/link"

// Get Supabase URL and key from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
const supabaseKey = process.env.NEXT_PUBLIC_service_role || ''

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey)

export default function AdminAnalyticsPage() {
  const [activeTab, setActiveTab] = useState("user-engagement")
  const [hasAnalyticsTables, setHasAnalyticsTables] = useState(true)
  const [checking, setChecking] = useState(true)
  
  useEffect(() => {
    // Check if analytics tables exist
    async function checkTables() {
      try {
        const { data, error } = await supabase.from('analytics_daily_metrics').select('*').limit(1)
        
        if (error) {
          console.error('Error checking analytics tables:', error.message)
          setHasAnalyticsTables(false)
        } else {
          setHasAnalyticsTables(true)
        }
      } catch (error) {
        console.error('Error checking analytics tables:', error)
        setHasAnalyticsTables(false)
      } finally {
        setChecking(false)
      }
    }
    
    checkTables()
  }, [])

  if (checking) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-900"></div>
          <p className="text-lg">Loading analytics dashboard...</p>
        </div>
      </div>
    )
  }
  
  if (!hasAnalyticsTables) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
        </div>
        
        <Alert variant="destructive" className="bg-red-50">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="py-2">
              <p className="font-medium mb-2">Analytics tables not found</p>
              <p className="mb-4">The required database tables for the analytics dashboard have not been created yet.</p>
              <Link href="/admin/analytics/init-tables">
                <Button variant="default">Initialize Analytics Tables</Button>
              </Link>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
      </div>
      
      <Tabs defaultValue="user-engagement" className="space-y-4" onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-5 w-full max-w-4xl">
          <TabsTrigger value="user-engagement">
            <Users className="h-4 w-4 mr-2" />
            User Engagement
          </TabsTrigger>
          <TabsTrigger value="performance">
            <Activity className="h-4 w-4 mr-2" />
            App Performance
          </TabsTrigger>
          <TabsTrigger value="advertising">
            <BarChart2 className="h-4 w-4 mr-2" />
            Advertising
          </TabsTrigger>
          <TabsTrigger value="feedback">
            <MessageSquare className="h-4 w-4 mr-2" />
            User Feedback
          </TabsTrigger>
          <TabsTrigger value="analytics">
            <PieChart className="h-4 w-4 mr-2" />
            Advanced Analytics
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="user-engagement" className="space-y-4">
          <UserEngagementDashboard />
        </TabsContent>
        
        <TabsContent value="performance" className="space-y-4">
          <PerformanceDashboard />
        </TabsContent>
        
        <TabsContent value="advertising" className="space-y-4">
          <AdvertisingDashboard />
        </TabsContent>
        
        <TabsContent value="feedback" className="space-y-4">
          <FeedbackDashboard />
        </TabsContent>
        
        <TabsContent value="analytics" className="space-y-4">
          <AnalyticsDashboard />
        </TabsContent>
      </Tabs>
    </div>
  )
} 