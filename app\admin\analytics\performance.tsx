"use client"

import { useState, useEffect } from "react"
import {
  Activity,
  AlertCircle,
  Clock,
  BarChart2,
  ArrowUp,
  ArrowDown,
  Server,
  Zap,
  Users
} from "lucide-react"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  AreaChart,
  Area
} from "recharts"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { createClient } from '@supabase/supabase-js'

// Get Supabase URL and key from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
const supabaseKey = process.env.NEXT_PUBLIC_service_role || ''

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey)

// Define ValueType for tooltip formatters
type ValueType = string | number | Array<string | number> | undefined | null;

// Mock data for visualization
const pageLoadTimesMock = [
  { page: 'Home', time: 0.8 },
  { page: 'Doctor Profile', time: 1.2 },
  { page: 'Doctor Search', time: 1.5 },
  { page: 'Reviews', time: 0.9 },
  { page: 'Rating Form', time: 0.7 },
  { page: 'Divisions', time: 1.3 },
  { page: 'Admin Dashboard', time: 1.8 },
];

const apiResponseTimesMock = [
  { endpoint: 'Get Doctors', time: 220 },
  { endpoint: 'Doctor Details', time: 180 },
  { endpoint: 'Submit Review', time: 320 },
  { endpoint: 'Search', time: 250 },
  { endpoint: 'User Auth', time: 150 },
  { endpoint: 'Profile Update', time: 280 },
];

const dailyErrorsMock = [
  { date: '2023-06-01', errors: 12 },
  { date: '2023-06-02', errors: 8 },
  { date: '2023-06-03', errors: 15 },
  { date: '2023-06-04', errors: 7 },
  { date: '2023-06-05', errors: 5 },
  { date: '2023-06-06', errors: 10 },
  { date: '2023-06-07', errors: 6 },
];

const featureUsageMock = [
  { name: 'Doctor Search', users: 320 },
  { name: 'Rating Submission', users: 210 },
  { name: 'Doctor Profiles', users: 290 },
  { name: 'Specialty Browse', users: 180 },
  { name: 'User Registration', users: 90 },
];

const errorTypesMock = [
  { name: 'API Errors', count: 42 },
  { name: 'UI Rendering', count: 28 },
  { name: 'Authentication', count: 15 },
  { name: 'Database', count: 8 },
  { name: 'Network', count: 12 },
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export function PerformanceDashboard() {
  const [loading, setLoading] = useState(true)
  const [statsData, setStatsData] = useState({
    averagePageLoadTime: 0,
    averageApiResponseTime: 0,
    totalErrors24h: 0,
    errorRate: 0,
  })

  useEffect(() => {
    // In a real implementation, you would fetch actual data from Supabase here
    // For now, we'll simulate loading with a timeout and set mock data
    const timer = setTimeout(() => {
      setStatsData({
        averagePageLoadTime: 1.2,
        averageApiResponseTime: 220,
        totalErrors24h: 6,
        errorRate: 0.5,
      })
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="space-y-4">
      {/* Summary Stats */}
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Page Load Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : `${statsData.averagePageLoadTime} s`}
            </div>
            <p className="text-xs text-muted-green">
              <span className="text-green-500">-0.2s</span> from yesterday
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. API Response Time</CardTitle>
            <Zap className="h-4 w-4 text-muted-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : `${statsData.averageApiResponseTime} ms`}
            </div>
            <p className="text-xs text-muted-green">
              <span className="text-green-500">-15ms</span> from yesterday
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Errors (24h)</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : statsData.totalErrors24h}
            </div>
            <p className="text-xs text-muted-green">
              <span className="text-green-500">-4</span> from yesterday
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
            <Activity className="h-4 w-4 text-muted-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : `${statsData.errorRate}%`}
            </div>
            <p className="text-xs text-muted-green">
              <span className="text-green-500">-0.2%</span> from yesterday
            </p>
          </CardContent>
        </Card>
      </div>
      
      {/* Detailed Analysis Tabs */}
      <Tabs defaultValue="load-times" className="space-y-4">
        <TabsList>
          <TabsTrigger value="load-times">Load Times</TabsTrigger>
          <TabsTrigger value="errors">Error Analysis</TabsTrigger>
          <TabsTrigger value="api">API Performance</TabsTrigger>
          <TabsTrigger value="features">Feature Usage</TabsTrigger>
        </TabsList>
        
        {/* Load Times Tab */}
        <TabsContent value="load-times" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 xl:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Page Load Times</CardTitle>
                <CardDescription>Average load time by page (seconds)</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart layout="vertical" data={pageLoadTimesMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="page" type="category" />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="time" fill="#0088FE" label={{ position: 'right', formatter: (value: ValueType) => `${value}s` }} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Load Time Distribution</CardTitle>
                <CardDescription>Percentage of pages by load time</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'Under 1s', value: 45 },
                        { name: '1-2s', value: 38 },
                        { name: '2-3s', value: 12 },
                        { name: 'Over 3s', value: 5 },
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {[
                        { name: 'Under 1s', value: 45 },
                        { name: '1-2s', value: 38 },
                        { name: '2-3s', value: 12 },
                        { name: 'Over 3s', value: 5 },
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1 xl:col-span-2">
              <CardHeader>
                <CardTitle>Load Time Trend</CardTitle>
                <CardDescription>Average load time over the past 30 days</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={Array.from({ length: 30 }, (_, i) => ({
                    date: `6/${i + 1}`,
                    time: 2 - (Math.random() * 0.8)
                  }))}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis domain={[0, 'dataMax + 0.5']} />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="time" stroke="#0088FE" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Error Analysis Tab */}
        <TabsContent value="errors" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 xl:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Daily Error Count</CardTitle>
                <CardDescription>Number of errors per day</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={dailyErrorsMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="errors" stroke="#FF8042" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Error Types</CardTitle>
                <CardDescription>Distribution of error categories</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={errorTypesMock}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {errorTypesMock.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1 xl:col-span-2">
              <CardHeader>
                <CardTitle>Recent Critical Errors</CardTitle>
                <CardDescription>Last 5 critical errors detected</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Array.from({ length: 5 }, (_, i) => (
                    <Alert key={i} variant={i === 0 ? "destructive" : "default"}>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium">
                              {i === 0 ? 'Database Connection Failure' : 
                               i === 1 ? 'API Authentication Error' :
                               i === 2 ? 'Unhandled Exception in UserService' :
                               i === 3 ? 'Component Rendering Error' :
                               'Network Request Timeout'}
                            </p>
                            <p className="text-sm text-muted-green">
                              {i === 0 ? 'Failed to connect to database. Timeout after 30s.' : 
                               i === 1 ? 'Invalid token provided for secure endpoint.' :
                               i === 2 ? 'Uncaught TypeError: Cannot read property of undefined.' :
                               i === 3 ? 'Failed to render ProfileComponent: missing required props.' :
                               'Network request timed out after 60s.'}
                            </p>
                          </div>
                          <Badge variant={i === 0 ? "destructive" : "outline"}>
                            {i === 0 ? '10 min ago' : 
                             i === 1 ? '45 min ago' :
                             i === 2 ? '2 hrs ago' :
                             i === 3 ? '3 hrs ago' :
                             '5 hrs ago'}
                          </Badge>
                        </div>
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* API Performance Tab */}
        <TabsContent value="api" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 xl:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>API Response Times</CardTitle>
                <CardDescription>Average response time by endpoint (ms)</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart layout="vertical" data={apiResponseTimesMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="endpoint" type="category" />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="time" fill="#00C49F" label={{ position: 'right', formatter: (value: ValueType) => `${value}ms` }} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>API Response Time Trend</CardTitle>
                <CardDescription>Average response time over the past 30 days</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={Array.from({ length: 30 }, (_, i) => ({
                    date: `6/${i + 1}`,
                    time: 300 - (Math.random() * 100)
                  }))}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis domain={[0, 'dataMax + 50']} />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="time" stroke="#00C49F" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1 xl:col-span-2">
              <CardHeader>
                <CardTitle>API Endpoints Usage</CardTitle>
                <CardDescription>Number of requests by endpoint</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={apiResponseTimesMock.map(item => ({ 
                    ...item, 
                    requests: Math.floor(Math.random() * 500) + 100 
                  }))}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="endpoint" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="requests" fill="#8884D8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Feature Usage Tab */}
        <TabsContent value="features" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 xl:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Most Used Features</CardTitle>
                <CardDescription>Number of users per feature</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart layout="vertical" data={featureUsageMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="name" type="category" />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="users" fill="#FFBB28" label={{ position: 'right' }} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Feature Usage Trend</CardTitle>
                <CardDescription>Usage over time</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={Array.from({ length: 30 }, (_, i) => ({
                    date: `6/${i + 1}`,
                    search: 200 + (Math.random() * 100),
                    ratings: 150 + (Math.random() * 80),
                    profiles: 180 + (Math.random() * 90),
                  }))}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area type="monotone" dataKey="search" fill="#0088FE" stroke="#0088FE" fillOpacity={0.2} />
                    <Area type="monotone" dataKey="ratings" fill="#00C49F" stroke="#00C49F" fillOpacity={0.2} />
                    <Area type="monotone" dataKey="profiles" fill="#FFBB28" stroke="#FFBB28" fillOpacity={0.2} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Feature Engagement</CardTitle>
                <CardDescription>Average time spent on features (minutes)</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={[
                    { name: 'Doctor Search', time: 4.2 },
                    { name: 'Doctor Profiles', time: 5.8 },
                    { name: 'Rating Form', time: 2.5 },
                    { name: 'Reviews Reading', time: 6.3 },
                    { name: 'Specialty Browse', time: 3.7 },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="time" fill="#8884D8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Least Used Features</CardTitle>
                <CardDescription>Features with lowest engagement</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart layout="vertical" data={[
                    { name: 'User Settings', users: 45 },
                    { name: 'Contact Form', users: 38 },
                    { name: 'Password Reset', users: 25 },
                    { name: 'Help Section', users: 20 },
                    { name: 'About Page', users: 15 },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="name" type="category" />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="users" fill="#FF8042" label={{ position: 'right' }} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
} 