"use client"

import { useState, useEffect } from "react"
import {
  <PERSON>,
  Clock,
  Calendar,
  User<PERSON>heck,
  UserPlus,
  Star,
  Activity,
  BarChart2,
  Heart,
  Eye,
  Map,
  User
} from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  AreaChart,
  Area
} from "recharts"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { createClient } from '@supabase/supabase-js'

// Get Supabase URL and key from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
const supabaseKey = process.env.NEXT_PUBLIC_service_role || ''

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey)

// Helper function to ensure no NaN values in data
const ensureValidData = (data: any[]): any[] => {
  return data.map(item => {
    const newItem = { ...item };
    Object.keys(newItem).forEach(key => {
      // Convert NaN or undefined to 0 for numerical properties
      if (typeof newItem[key] === 'number' && (isNaN(newItem[key]) || newItem[key] === undefined)) {
        newItem[key] = 0;
      }
    });
    return newItem;
  });
};

// Mock data for visualization
const dailyUsersMock = ensureValidData([
  { date: '2023-06-01', users: 1240 },
  { date: '2023-06-02', users: 1180 },
  { date: '2023-06-03', users: 1320 },
  { date: '2023-06-04', users: 1450 },
  { date: '2023-06-05', users: 1560 },
  { date: '2023-06-06', users: 1490 },
  { date: '2023-06-07', users: 1380 },
]);

const monthlyUsersMock = ensureValidData([
  { month: 'Jan', users: 18500 },
  { month: 'Feb', users: 19200 },
  { month: 'Mar', users: 21400 },
  { month: 'Apr', users: 22800 },
  { month: 'May', users: 24500 },
  { month: 'Jun', users: 26200 },
]);

const userTypesMock = ensureValidData([
  { name: 'Registered', value: 65 },
  { name: 'Non-Registered', value: 35 },
]);

const sessionDurationMock = ensureValidData([
  { date: '2023-06-01', duration: 4.8 },
  { date: '2023-06-02', duration: 5.2 },
  { date: '2023-06-03', duration: 4.9 },
  { date: '2023-06-04', duration: 5.4 },
  { date: '2023-06-05', duration: 5.7 },
  { date: '2023-06-06', duration: 5.5 },
  { date: '2023-06-07', duration: 5.3 },
]);

const newRegistrationsMock = ensureValidData([
  { date: '2023-06-01', registrations: 145 },
  { date: '2023-06-02', registrations: 132 },
  { date: '2023-06-03', registrations: 158 },
  { date: '2023-06-04', registrations: 184 },
  { date: '2023-06-05', registrations: 172 },
  { date: '2023-06-06', registrations: 168 },
  { date: '2023-06-07', registrations: 154 },
]);

const reviewsAndRatingsMock = ensureValidData([
  { date: '2023-06-01', reviews: 68, avgRating: 4.2 },
  { date: '2023-06-02', reviews: 72, avgRating: 4.3 },
  { date: '2023-06-03', reviews: 75, avgRating: 4.1 },
  { date: '2023-06-04', reviews: 82, avgRating: 4.4 },
  { date: '2023-06-05', reviews: 78, avgRating: 4.2 },
  { date: '2023-06-06', reviews: 85, avgRating: 4.3 },
  { date: '2023-06-07', reviews: 79, avgRating: 4.2 },
]);

const ratingDistributionMock = ensureValidData([
  { rating: 5, count: 42 },
  { rating: 4, count: 38 },
  { rating: 3, count: 12 },
  { rating: 2, count: 5 },
  { rating: 1, count: 3 },
]);

const demographicsMock = {
  age: ensureValidData([
    { group: '18-24', value: 15 },
    { group: '25-34', value: 32 },
    { group: '35-44', value: 28 },
    { group: '45-54', value: 16 },
    { group: '55+', value: 9 },
  ]),
  gender: ensureValidData([
    { name: 'Male', value: 48 },
    { name: 'Female', value: 51 },
    { name: 'Other', value: 1 },
  ]),
  specialtyInterest: ensureValidData([
    { name: 'Cardiology', value: 22 },
    { name: 'Dermatology', value: 18 },
    { name: 'Neurology', value: 14 },
    { name: 'Pediatrics', value: 12 },
    { name: 'Orthopedics', value: 10 },
    { name: 'Other', value: 24 },
  ]),
};

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#d80000'];

export function UserEngagementDashboard() {
  const [loading, setLoading] = useState(true)
  const [statsData, setStatsData] = useState({
    dailyActiveUsers: 0,
    monthlyActiveUsers: 0,
    avgSessionDuration: 0,
    registeredUsers: 0,
  })

  useEffect(() => {
    // In a real implementation, you would fetch actual data from Supabase here
    // For now, we'll simulate loading with a timeout and set mock data
    const timer = setTimeout(() => {
      setStatsData({
        dailyActiveUsers: 1480,
        monthlyActiveUsers: 26200,
        avgSessionDuration: 5.3,
        registeredUsers: 65,
      })
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="space-y-4">
      {/* Summary Stats */}
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Daily Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : statsData.dailyActiveUsers.toLocaleString()}
            </div>
            <p className="text-xs text-muted-green">
              <span className="text-green-500">+8.2%</span> from last week
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Active Users</CardTitle>
            <Calendar className="h-4 w-4 text-muted-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : statsData.monthlyActiveUsers.toLocaleString()}
            </div>
            <p className="text-xs text-muted-green">
              <span className="text-green-500">+6.9%</span> from last month
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Session Duration</CardTitle>
            <Clock className="h-4 w-4 text-muted-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : `${statsData.avgSessionDuration} mins`}
            </div>
            <p className="text-xs text-muted-green">
              <span className="text-green-500">+0.5 min</span> from last week
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Registered Users</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : `${statsData.registeredUsers}%`}
            </div>
            <p className="text-xs text-muted-green">
              <span className="text-green-500">+3%</span> from last month
            </p>
          </CardContent>
        </Card>
      </div>
      
      {/* Detailed Analysis Tabs */}
      <Tabs defaultValue="activity" className="space-y-4">
        <TabsList>
          <TabsTrigger value="activity">User Activity</TabsTrigger>
          <TabsTrigger value="registration">Registration Status</TabsTrigger>
          <TabsTrigger value="reviews">Reviews & Ratings</TabsTrigger>
          <TabsTrigger value="demographics">Demographics</TabsTrigger>
        </TabsList>
        
        {/* User Activity Tab */}
        <TabsContent value="activity" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 xl:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Daily Active Users</CardTitle>
                <CardDescription>Number of unique users per day</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={dailyUsersMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip formatter={(value: any) => [`${value.toLocaleString()}`, 'Users']} />
                    <Legend />
                    <Line type="monotone" dataKey="users" stroke="#0088FE" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Monthly Active Users</CardTitle>
                <CardDescription>Number of unique users per month</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={monthlyUsersMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value: any) => [`${value.toLocaleString()}`, 'Users']} />
                    <Legend />
                    <Bar dataKey="users" fill="#00C49F" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Average Session Duration</CardTitle>
                <CardDescription>Minutes spent per session</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={sessionDurationMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis domain={[0, 'dataMax + 1']} />
                    <Tooltip formatter={(value: any) => [`${value} mins`, 'Duration']} />
                    <Legend />
                    <Line type="monotone" dataKey="duration" stroke="#FFBB28" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>User Type Distribution</CardTitle>
                <CardDescription>Registered vs. Non-Registered users</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={userTypesMock}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {userTypesMock.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: any) => [`${value}%`, 'Percentage']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Registration Status Tab */}
        <TabsContent value="registration" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 xl:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>New User Registrations</CardTitle>
                <CardDescription>Daily new user sign-ups</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={newRegistrationsMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip formatter={(value: any) => [`${value.toLocaleString()}`, 'Registrations']} />
                    <Legend />
                    <Bar dataKey="registrations" fill="#0088FE" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Registration Completion Rate</CardTitle>
                <CardDescription>Percentage of completed registrations</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={[
                    { date: '2023-06-01', rate: 68 },
                    { date: '2023-06-02', rate: 72 },
                    { date: '2023-06-03', rate: 75 },
                    { date: '2023-06-04', rate: 71 },
                    { date: '2023-06-05', rate: 77 },
                    { date: '2023-06-06', rate: 79 },
                    { date: '2023-06-07', rate: 82 },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis domain={[50, 100]} />
                    <Tooltip formatter={(value: any) => [`${value}%`, 'Completion Rate']} />
                    <Legend />
                    <Line type="monotone" dataKey="rate" stroke="#00C49F" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1 xl:col-span-2">
              <CardHeader>
                <CardTitle>Registration Funnel</CardTitle>
                <CardDescription>User journey through registration process</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart layout="vertical" data={[
                    { stage: 'Visit Registration Page', count: 100 },
                    { stage: 'Start Registration', count: 85 },
                    { stage: 'Email Verification', count: 72 },
                    { stage: 'Complete Profile', count: 64 },
                    { stage: 'Specialty Selection', count: 58 },
                    { stage: 'Completed Registration', count: 52 },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" domain={[0, 100]} />
                    <YAxis dataKey="stage" type="category" />
                    <Tooltip formatter={(value: any) => [`${value}%`, 'Users']} />
                    <Legend />
                    <Bar dataKey="count" fill="#FFBB28" label={{ position: 'right', formatter: (value: any) => `${value}%` }} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
              <CardFooter>
                <div className="w-full space-y-2">
                  <div className="grid grid-cols-6 gap-2 text-xs text-muted-green">
                    <div className="text-center">100%</div>
                    <div className="text-center">85%</div>
                    <div className="text-center">72%</div>
                    <div className="text-center">64%</div>
                    <div className="text-center">58%</div>
                    <div className="text-center">52%</div>
                  </div>
                  <div className="flex w-full gap-1">
                    <div className="h-2 w-full bg-[#FFBB28]"></div>
                    <div className="h-2 w-[85%] bg-[#FFBB28]"></div>
                    <div className="h-2 w-[72%] bg-[#FFBB28]"></div>
                    <div className="h-2 w-[64%] bg-[#FFBB28]"></div>
                    <div className="h-2 w-[58%] bg-[#FFBB28]"></div>
                    <div className="h-2 w-[52%] bg-[#FFBB28]"></div>
                  </div>
                </div>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
        
        {/* Reviews & Ratings Tab */}
        <TabsContent value="reviews" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 xl:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Daily Reviews</CardTitle>
                <CardDescription>Number of reviews submitted per day</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={reviewsAndRatingsMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip formatter={(value: any) => [`${value}`, 'Reviews']} />
                    <Legend />
                    <Bar dataKey="reviews" fill="#0088FE" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Average Rating</CardTitle>
                <CardDescription>Daily average doctor rating (out of 5)</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={reviewsAndRatingsMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis domain={[3, 5]} />
                    <Tooltip formatter={(value: any) => [`${value.toFixed(1)}/5`, 'Rating']} />
                    <Legend />
                    <Line type="monotone" dataKey="avgRating" stroke="#00C49F" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Rating Distribution</CardTitle>
                <CardDescription>Breakdown of ratings by star level</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={ratingDistributionMock}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="rating" />
                    <YAxis />
                    <Tooltip formatter={(value: any) => [`${value}`, 'Ratings']} />
                    <Legend />
                    <Bar dataKey="count" fill="#FFBB28" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Sentiment Analysis</CardTitle>
                <CardDescription>Sentiment breakdown in review text</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'Positive', value: 68 },
                        { name: 'Neutral', value: 22 },
                        { name: 'Negative', value: 10 },
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {[
                        { name: 'Positive', value: 68 },
                        { name: 'Neutral', value: 22 },
                        { name: 'Negative', value: 10 },
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={index === 0 ? "#00C49F" : index === 1 ? "#FFBB28" : "#FF8042"} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: any) => [`${value}%`, 'Percentage']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Demographics Tab */}
        <TabsContent value="demographics" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 xl:grid-cols-3">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Age Distribution</CardTitle>
                <CardDescription>Users by age group</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={demographicsMock.age}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="group" />
                    <YAxis />
                    <Tooltip formatter={(value: any) => [`${value}%`, 'Percentage']} />
                    <Legend />
                    <Bar dataKey="value" fill="#0088FE" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Gender Distribution</CardTitle>
                <CardDescription>Users by gender</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={demographicsMock.gender}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {demographicsMock.gender.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: any) => [`${value}%`, 'Percentage']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Specialty Interest</CardTitle>
                <CardDescription>Most viewed medical specialties</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={demographicsMock.specialtyInterest}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {demographicsMock.specialtyInterest.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: any) => [`${value}%`, 'Percentage']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="col-span-1 xl:col-span-3">
              <CardHeader>
                <CardTitle>Geographic Distribution</CardTitle>
                <CardDescription>User locations by country/region</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart layout="vertical" data={[
                    { country: 'United States', users: 45 },
                    { country: 'United Kingdom', users: 12 },
                    { country: 'Canada', users: 8 },
                    { country: 'Australia', users: 6 },
                    { country: 'Germany', users: 5 },
                    { country: 'France', users: 4 },
                    { country: 'India', users: 3 },
                    { country: 'Other', users: 17 },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="country" type="category" />
                    <Tooltip formatter={(value: any) => [`${value}%`, 'Users']} />
                    <Legend />
                    <Bar dataKey="users" fill="#00C49F" label={{ position: 'right', formatter: (value: any) => `${value}%` }} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
} 