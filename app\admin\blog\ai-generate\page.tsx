"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { 
  Bot, 
  FileText, 
  Loader2, 
  CheckCircle, 
  AlertCircle,
  Eye,
  Edit,
  Calendar,
  User,
  Trash2
} from 'lucide-react'
import Link from 'next/link'
import { useToast } from '@/hooks/use-toast'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { convertMarkdownToHtml } from '@/lib/blog-post-structures'

interface GeneratedArticle {
  id: string
  title: string
  slug: string
  excerpt: string
  content: string
  status: string
  generation_source: string
  ai_generation_job_id: string | null
  created_at: string
  author_name?: string
  category_name?: string
  meta_keywords?: string[]
  bio?: string
}

interface BlogCategory {
  id: string
  name: string
  slug: string
  description: string
}

interface BlogAuthor {
  id: string
  name: string
  email: string
  bio?: string
}

export default function AIGeneratePage() {
  const [specialtyName, setSpecialtyName] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedAuthor, setSelectedAuthor] = useState('')
  const [customPrompt, setCustomPrompt] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationStatus, setGenerationStatus] = useState<'idle' | 'generating' | 'success' | 'error'>('idle')
  const [generatedArticle, setGeneratedArticle] = useState<GeneratedArticle | null>(null)
  const [draftArticles, setDraftArticles] = useState<GeneratedArticle[]>([])
  const [categories, setCategories] = useState<BlogCategory[]>([])
  const [authors, setAuthors] = useState<BlogAuthor[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isCleaningUp, setIsCleaningUp] = useState(false)
  
  const { toast } = useToast()
  const supabase = createClientComponentClient()

  // Fetch categories from database
  const fetchCategories = async () => {
    console.log('🔍 DEBUG: Fetching categories...')
    try {
      const { data, error } = await supabase
        .from('blog_categories')
        .select('id, name, slug, description')
        .eq('is_active', true)
        .order('sort_order', { ascending: true })

      if (error) {
        console.error('❌ ERROR: Error fetching categories:', error)
        toast({
          title: "Database Error",
          description: `Failed to load categories: ${error.message}`,
          variant: "destructive",
        })
        return
      }

      console.log(`✅ DEBUG: Categories loaded: ${data?.length || 0} items`, data)
      setCategories(data || [])
    } catch (error) {
      console.error('❌ ERROR: Exception fetching categories:', error)
      toast({
        title: "Error",
        description: "An unexpected error occurred loading categories",
        variant: "destructive",
      })
    }
  }

  // Fetch authors from database
  const fetchAuthors = async () => {
    console.log('🔍 DEBUG: Fetching authors...')
    try {
      const { data, error } = await supabase
        .from('blog_authors')
        .select('id, name, email, bio')
        .eq('is_active', true)
        .order('name', { ascending: true })

      if (error) {
        console.error('❌ ERROR: Error fetching authors:', error)
        toast({
          title: "Database Error",
          description: `Failed to load authors: ${error.message}`,
          variant: "destructive",
        })
        return
      }

      console.log(`✅ DEBUG: Authors loaded: ${data?.length || 0} items`, data)
      setAuthors(data || [])
    } catch (error) {
      console.error('❌ ERROR: Exception fetching authors:', error)
      toast({
        title: "Error",
        description: "An unexpected error occurred loading authors",
        variant: "destructive",
      })
    }
  }

  // Fetch AI-generated draft articles
  const fetchDraftArticles = async () => {
    console.log('🔍 DEBUG: Fetching AI-generated draft articles...')
    try {
      const { data, error } = await supabase
        .from('blog_posts')
        .select('*')
        .eq('status', 'draft')
        .eq('generation_source', 'ai_generated')
        .order('created_at', { ascending: false })
        .limit(10)

      if (error) {
        console.error('❌ ERROR: Error fetching draft articles:', error)
        toast({
          title: "Database Error",
          description: `Failed to load draft articles: ${error.message}`,
          variant: "destructive",
        })
        setDraftArticles([])
        return
      }

      console.log(`📋 DEBUG: Draft articles query result: ${data?.length || 0} items found`)
      
      if (!data || data.length === 0) {
        console.log('ℹ️ INFO: No draft articles found')
        setDraftArticles([])
        return
      }

      console.log('🔍 DEBUG: Sample article data:', data[0])

      // Get author and category info for each post
      console.log('🔍 DEBUG: Fetching author and category details for articles...')
      try {
        const formattedArticles = await Promise.all(
          data.map(async (article) => {
            console.log(`🔍 DEBUG: Processing article ID ${article.id}, title: ${article.title}`)
            
            // Check if author_id exists before querying
            if (!article.author_id) {
              console.warn(`⚠️ WARNING: Article ${article.id} has no author_id`)
            }
            
            let authorName = 'Unknown Author'
            try {
              if (article.author_id) {
                // Specify the exact foreign key relationship to avoid ambiguity
                const { data: authorData, error: authorError } = await supabase
                  .from('blog_authors')
                  .select('name')
                  .eq('id', article.author_id)
                  .single()
                
                if (authorError) {
                  console.error(`❌ ERROR: Failed to fetch author for article ${article.id}:`, authorError)
                } else {
                  authorName = authorData?.name || 'Unknown Author'
                  console.log(`✅ DEBUG: Found author: ${authorName} for article ${article.id}`)
                }
              }
            } catch (authorError) {
              console.error(`❌ ERROR: Exception fetching author for article ${article.id}:`, authorError)
            }

            // Check if category_id exists before querying
            if (!article.category_id) {
              console.warn(`⚠️ WARNING: Article ${article.id} has no category_id`)
            }
            
            let categoryName = 'General'
            try {
              if (article.category_id) {
                const { data: categoryData, error: categoryError } = await supabase
                  .from('blog_categories')
                  .select('name')
                  .eq('id', article.category_id)
                  .single()
                
                if (categoryError) {
                  console.error(`❌ ERROR: Failed to fetch category for article ${article.id}:`, categoryError)
                } else {
                  categoryName = categoryData?.name || 'General'
                  console.log(`✅ DEBUG: Found category: ${categoryName} for article ${article.id}`)
                }
              }
            } catch (categoryError) {
              console.error(`❌ ERROR: Exception fetching category for article ${article.id}:`, categoryError)
            }

            return {
              ...article,
              author_name: authorName,
              category_name: categoryName
            }
          })
        )

        console.log(`✅ DEBUG: Successfully processed ${formattedArticles.length} articles with author/category info`)
        setDraftArticles(formattedArticles)
      } catch (formatError) {
        console.error('❌ ERROR: Exception formatting articles with author/category data:', formatError)
        // Still try to show articles without the extra data
        setDraftArticles(data.map(article => ({
          ...article,
          author_name: 'Unknown Author',
          category_name: 'General'
        })))
      }
    } catch (error) {
      console.error('❌ ERROR: Unexpected exception fetching draft articles:', error)
      toast({
        title: "Error",
        description: "An unexpected error occurred loading articles",
        variant: "destructive",
      })
      setDraftArticles([])
    } finally {
      setIsLoading(false)
    }
  }

  // Clean up old mock/test data
  const cleanupMockData = async () => {
    setIsCleaningUp(true)
    try {
      const { error } = await supabase
        .from('blog_posts')
        .delete()
        .or('title.ilike.%mock%,title.ilike.%test%,ai_generation_job_id.ilike.%mock%')

      if (error) {
        console.error('Error cleaning up mock data:', error)
        toast({
          title: "Cleanup Failed",
          description: "Could not clean up old mock data",
          variant: "destructive",
        })
      } else {
        toast({
          title: "Cleanup Complete",
          description: "Old mock/test data has been removed",
        })
        await fetchDraftArticles()
      }
    } catch (error) {
      console.error('Error during cleanup:', error)
    } finally {
      setIsCleaningUp(false)
    }
  }

  // Initialize data
  useEffect(() => {
    const loadData = async () => {
      await Promise.all([
        fetchCategories(),
        fetchAuthors(),
        fetchDraftArticles()
      ])
    }
    loadData()
  }, [])

  const handleGenerateArticle = async () => {
    // Validation
    if (!specialtyName.trim() || !selectedCategory || !selectedAuthor) {
      toast({
        title: "Validation Error",
        description: "Please fill out all required fields: Specialty, Category, and Author.",
        variant: "destructive",
      })
      return
    }

    setIsGenerating(true)
    setGenerationStatus('generating')
    setGeneratedArticle(null)

    try {
      const selectedCategoryData = categories.find(c => c.id === selectedCategory)
      const selectedAuthorData = authors.find(a => a.id === selectedAuthor)

      if (!selectedCategoryData || !selectedAuthorData) {
        toast({ title: "Error", description: "Selected category or author not found.", variant: "destructive" })
        setIsGenerating(false)
        setGenerationStatus('error')
        return
      }

      console.log('🚀 DEBUG: Calling AI generation API with:', {
        specialtyName,
        categorySlug: selectedCategoryData.slug,
        authorName: selectedAuthorData.name,
        customInstructions: customPrompt,
      })

      // Call the new backend API route
      const response = await fetch('/api/generate-blog-post', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          specialtyName,
          categorySlug: selectedCategoryData.slug,
          authorName: selectedAuthorData.name,
          customInstructions: customPrompt,
        }),
      })

      if (!response.ok) {
        // New, more detailed error handling
        let errorMessage = 'An unknown error occurred from the API.'
        try {
          const errorData = await response.json()
          console.error('❌ ERROR: API response was not OK.', {
            status: response.status,
            statusText: response.statusText,
            errorData: errorData,
          })
          errorMessage = errorData.error || `API returned status ${response.status}`
          if (errorData.details) {
            errorMessage += `: ${errorData.details}`
          }
        } catch (e) {
          console.error('❌ ERROR: Could not parse error JSON from API response.', e)
          errorMessage = `API returned status ${response.status}, but the response was not valid JSON.`
        }
        throw new Error(errorMessage)
      }

      const aiResult = await response.json()
      console.log('✅ DEBUG: Received AI result:', aiResult)

      // Convert Markdown content to HTML
      const htmlContent = convertMarkdownToHtml(aiResult.content)
      const timestamp = Date.now()
      
      const articleData = {
        title: aiResult.title,
        slug: `${aiResult.slug}-${timestamp}`, // Ensure slug is unique
        excerpt: aiResult.excerpt,
        content: htmlContent, // Save as HTML
        status: 'draft',
        category_id: selectedCategory,
        author_id: selectedAuthor,
        generation_source: 'ai_generated',
        ai_generation_job_id: `ai-gen-${timestamp}`,
        meta_keywords: aiResult.meta_keywords,
        raw_data_json: {
          specialty: specialtyName,
          category: selectedCategoryData.name,
          author: selectedAuthorData.name,
          custom_prompt: customPrompt,
          generated_at: new Date().toISOString(),
          generation_mode: 'ai'
        },
        reading_time_minutes: Math.ceil(htmlContent.length / 1000),
        is_featured: false,
        is_trending: false,
        meta_title: aiResult.meta_title,
        meta_description: aiResult.meta_description,
      }

      console.log('📝 DEBUG: Attempting to save AI-generated article:', articleData)
      
      // Save to database
      const { data: savedArticle, error: saveError } = await supabase
        .from('blog_posts')
        .insert([articleData])
        .select()
        .single()

      if (saveError) {
        console.error('❌ ERROR: Database error saving article:', saveError)
        toast({
          title: "Save Failed",
          description: `Database error: ${saveError.message}`,
          variant: "destructive",
        })
        setGenerationStatus('error')
        return
      }
      
      console.log('✅ DEBUG: Article successfully saved:', savedArticle)

      const formattedArticle = {
        ...savedArticle,
        author_name: selectedAuthorData.name,
        category_name: selectedCategoryData.name
      }

      setGenerationStatus('success')
      setGeneratedArticle(formattedArticle)
      
      await fetchDraftArticles()
      
      toast({
        title: "Article Generated Successfully!",
        description: `Created: "${formattedArticle.title}"`,
      })

      setSpecialtyName('')
      setCustomPrompt('')

    } catch (error: any) {
      console.error('❌ ERROR: handleGenerateArticle function failed:', error)
      setGenerationStatus('error')
      toast({
        title: "Generation Failed",
        description: error.message || "An unexpected error occurred.",
        variant: "destructive",
      })
    } finally {
      setIsGenerating(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'draft': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'review': return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      default: return 'bg-background/60/20 text-muted-green border-border/30'
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">AI Content Generation</h1>
          <p className="text-foreground/70 mt-2">
            Generate medical specialty articles using AI based on platform data
          </p>
        </div>
        <Link href="/admin/blog">
          <Button variant="outline" className="border-border text-foreground hover:bg-accent">
            ← Back to Blog
          </Button>
        </Link>
      </div>

      {/* Generation Form */}
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="text-foreground flex items-center gap-2">
            <Bot className="h-5 w-5" />
            Generate New Article
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="specialty" className="text-foreground">Medical Specialty *</Label>
              <Input
                id="specialty"
                placeholder="e.g., Cardiology, Neurology, Orthopedics"
                value={specialtyName}
                onChange={(e) => setSpecialtyName(e.target.value)}
                className="bg-card border-border text-foreground placeholder:text-foreground/50"
                disabled={isGenerating}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category" className="text-foreground">Category *</Label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory} disabled={isGenerating}>
                <SelectTrigger className="bg-card border-border text-foreground">
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="author" className="text-foreground">Author *</Label>
            <Select value={selectedAuthor} onValueChange={setSelectedAuthor} disabled={isGenerating}>
              <SelectTrigger className="bg-card border-border text-foreground">
                <SelectValue placeholder="Select an author" />
              </SelectTrigger>
              <SelectContent>
                {authors.map((author) => (
                  <SelectItem key={author.id} value={author.id}>
                    {author.name} {author.email && `(${author.email})`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="custom-prompt" className="text-foreground">Additional Instructions (Optional)</Label>
            <Textarea
              id="custom-prompt"
              placeholder="Any specific requirements or topics to focus on..."
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              className="bg-card border-border text-foreground placeholder:text-foreground/50"
              disabled={isGenerating}
              rows={3}
            />
          </div>

          <div className="flex gap-2">
            <Button 
              onClick={handleGenerateArticle} 
              disabled={isGenerating || !specialtyName.trim() || !selectedCategory || !selectedAuthor}
              className="flex-1"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating Article...
                </>
              ) : (
                <>
                  <Bot className="mr-2 h-4 w-4" />
                  Generate Article
                </>
              )}
            </Button>

            <Button 
              onClick={cleanupMockData}
              disabled={isCleaningUp || isGenerating}
              variant="outline"
              size="icon"
              title="Clean up old test data"
              className="border-border text-foreground hover:bg-accent"
            >
              {isCleaningUp ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* Generation Status */}
          {generationStatus === 'success' && generatedArticle && (
            <div className="p-4 bg-green-500/20 border border-green-500/30 rounded-lg">
              <div className="flex items-center gap-2 text-green-400 mb-2">
                <CheckCircle className="h-4 w-4" />
                <span className="font-semibold">Article Generated Successfully!</span>
              </div>
              <p className="text-foreground/80 text-sm">
                <strong>Title:</strong> {generatedArticle.title}
              </p>
              <p className="text-foreground/80 text-sm">
                <strong>Slug:</strong> {generatedArticle.slug}
              </p>
              <div className="mt-3 flex gap-2">
                <a href={`/blog/${generatedArticle.slug}`} target="_blank" rel="noopener noreferrer">
                  <Button size="sm" variant="outline" className="border-green-500/30 text-green-400 hover:bg-green-500/10">
                    <Eye className="mr-1 h-3 w-3" />
                    Review
                  </Button>
                </a>
                <Link href={`/admin/blog/posts/${generatedArticle.id}`}>
                  <Button size="sm" variant="outline" className="border-green-500/30 text-green-400 hover:bg-green-500/10">
                    <Edit className="mr-1 h-3 w-3" />
                    Edit
                  </Button>
                </Link>
              </div>
            </div>
          )}

          {generationStatus === 'error' && (
            <div className="p-4 bg-red-500/20 border border-red-500/30 rounded-lg">
              <div className="flex items-center gap-2 text-red-400">
                <AlertCircle className="h-4 w-4" />
                <span className="font-semibold">Generation Failed</span>
              </div>
              <p className="text-foreground/80 text-sm mt-1">
                Please try again or check the system logs for more details.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent AI-Generated Drafts */}
      <Card className="bg-card border-border">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="text-foreground">Recent AI-Generated Drafts</CardTitle>
            <Link href="/admin/blog/posts?filter=ai-generated">
              <Button variant="outline" size="sm" className="border-border text-foreground hover:bg-accent">
                View All
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-foreground/60" />
              <span className="ml-2 text-foreground/60">Loading drafts...</span>
            </div>
          ) : draftArticles.length === 0 ? (
            <div className="text-center py-8 text-foreground/60">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No draft articles found</p>
              <p className="text-sm">Generate your first AI article to get started!</p>
            </div>
          ) : (
            <div className="space-y-4">
              {draftArticles.map((article) => (
                <div key={article.id} className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted-green/50 transition-colors">
                  <div className="flex-1">
                    <h4 className="font-semibold text-foreground mb-1">{article.title}</h4>
                    <div className="flex items-center gap-4 text-sm text-foreground/60">
                      <Badge className={getStatusColor(article.status)}>
                        {article.status}
                      </Badge>
                      <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                        AI Generated
                      </Badge>
                      <span className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        {article.author_name}
                      </span>
                      <span className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {new Date(article.created_at).toLocaleDateString()}
                      </span>
                      {article.meta_keywords && article.meta_keywords.length > 0 && (
                        <span className="text-xs">
                          Keywords: {article.meta_keywords.slice(0, 3).join(', ')}
                          {article.meta_keywords.length > 3 && '...'}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <a href={`/blog/${article.slug}`} target="_blank" rel="noopener noreferrer">
                      <Button variant="ghost" size="sm" className="text-foreground/70 hover:text-foreground hover:bg-card">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </a>
                    <Link href={`/admin/blog/posts/${article.id}`}>
                      <Button variant="ghost" size="sm" className="text-foreground/70 hover:text-foreground hover:bg-card">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 