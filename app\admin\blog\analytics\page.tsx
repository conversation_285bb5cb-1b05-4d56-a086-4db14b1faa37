"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  TrendingUp, 
  Eye, 
  Users, 
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  MoreHorizontal,
  Download,
  Filter
} from 'lucide-react'

// Mock analytics data
const analyticsData = {
  totalViews: 45620,
  viewsChange: 12.5,
  totalReaders: 8930,
  readersChange: -3.2,
  averageReadTime: 4.2,
  readTimeChange: 8.1,
  postsPublished: 32,
  postsChange: 15.4
}

const topPosts = [
  {
    id: 1,
    title: "The 2024 State of Cardiology: Rising Trends",
    views: 5240,
    change: 23.5,
    category: "Ranking Analysis"
  },
  {
    id: 2,
    title: "Understanding Robotic Surgery: Patient Guide",
    views: 4180,
    change: 18.2,
    category: "Medical Deep Dives"
  },
  {
    id: 3,
    title: "Top 10 Neurosurgeons Making Waves in 2024",
    views: 3920,
    change: -5.1,
    category: "Ranking Analysis"
  },
  {
    id: 4,
    title: "Cancer Immunotherapy Breakthrough",
    views: 3450,
    change: 12.8,
    category: "Medical Deep Dives"
  }
]

const trafficSources = [
  { source: "Direct", percentage: 45, visitors: 4020 },
  { source: "Search Engines", percentage: 32, visitors: 2857 },
  { source: "Social Media", percentage: 15, visitors: 1340 },
  { source: "Referrals", percentage: 8, visitors: 714 }
]

export default function AnalyticsPage() {
  const handleExport = () => {
    alert('Export functionality - would generate and download analytics report')
  }

  const handleFilter = () => {
    alert('Filter functionality - would open filter options modal')
  }

  const handleMoreActions = (postId: number) => {
    alert(`More actions for post ${postId} - would show detailed analytics`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-background to-primary/20 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Blog Analytics</h1>
            <p className="text-foreground/70 mt-2">Track performance and engagement metrics</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" className="border-border text-foreground hover:bg-accent" onClick={handleFilter}>
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" className="border-border text-foreground hover:bg-accent" onClick={handleExport}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-foreground/70 text-sm">Total Views</p>
                  <p className="text-2xl font-bold text-foreground">{analyticsData.totalViews.toLocaleString()}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <ArrowUpRight className="h-3 w-3 text-green-400" />
                    <span className="text-green-400 text-sm">+{analyticsData.viewsChange}%</span>
                  </div>
                </div>
                <div className="p-2 bg-blue-500/20 rounded-lg">
                  <Eye className="h-6 w-6 text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-foreground/70 text-sm">Total Readers</p>
                  <p className="text-2xl font-bold text-foreground">{analyticsData.totalReaders.toLocaleString()}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <ArrowDownRight className="h-3 w-3 text-red-400" />
                    <span className="text-red-400 text-sm">{analyticsData.readersChange}%</span>
                  </div>
                </div>
                <div className="p-2 bg-green-500/20 rounded-lg">
                  <Users className="h-6 w-6 text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-foreground/70 text-sm">Avg. Read Time</p>
                  <p className="text-2xl font-bold text-foreground">{analyticsData.averageReadTime} min</p>
                  <div className="flex items-center gap-1 mt-1">
                    <ArrowUpRight className="h-3 w-3 text-green-400" />
                    <span className="text-green-400 text-sm">+{analyticsData.readTimeChange}%</span>
                  </div>
                </div>
                <div className="p-2 bg-purple-500/20 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-purple-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-foreground/70 text-sm">Posts Published</p>
                  <p className="text-2xl font-bold text-foreground">{analyticsData.postsPublished}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <ArrowUpRight className="h-3 w-3 text-green-400" />
                    <span className="text-green-400 text-sm">+{analyticsData.postsChange}%</span>
                  </div>
                </div>
                <div className="p-2 bg-orange-500/20 rounded-lg">
                  <Calendar className="h-6 w-6 text-orange-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Top Performing Posts */}
        <div className="grid lg:grid-cols-2 gap-6">
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-foreground">Top Performing Posts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topPosts.map((post, index) => (
                  <div key={post.id} className="flex items-center justify-between p-4 bg-muted-green/50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="text-foreground/70 font-mono text-sm">#{index + 1}</div>
                      <div>
                        <h4 className="text-foreground font-medium line-clamp-1">{post.title}</h4>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="secondary" className="bg-primary/20 text-primary border-0 text-xs">
                            {post.category}
                          </Badge>
                          <span className="text-foreground/60 text-sm">{post.views.toLocaleString()} views</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {post.change > 0 ? (
                        <div className="flex items-center gap-1 text-green-400">
                          <ArrowUpRight className="h-3 w-3" />
                          <span className="text-sm">+{post.change}%</span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-1 text-red-400">
                          <ArrowDownRight className="h-3 w-3" />
                          <span className="text-sm">{post.change}%</span>
                        </div>
                      )}
                      <Button variant="ghost" size="sm" className="text-foreground/70 hover:text-foreground h-8 w-8 p-0" onClick={() => handleMoreActions(post.id)}>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Traffic Sources */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-foreground">Traffic Sources</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {trafficSources.map((source, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div>
                      <div className="text-foreground font-medium">{source.source}</div>
                      <div className="text-foreground/60 text-sm">{source.visitors.toLocaleString()} visitors</div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-24 bg-card rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full" 
                          style={{ width: `${source.percentage}%` }}
                        />
                      </div>
                      <span className="text-foreground/70 text-sm font-mono w-8">{source.percentage}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Performance Chart Placeholder */}
        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="text-foreground">Performance Over Time</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center bg-muted-green/50 rounded-lg">
              <div className="text-center">
                <TrendingUp className="h-12 w-12 text-foreground/40 mx-auto mb-4" />
                <p className="text-foreground/60">Chart visualization will be implemented</p>
                <p className="text-foreground/40 text-sm">with a charting library like Chart.js or Recharts</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 