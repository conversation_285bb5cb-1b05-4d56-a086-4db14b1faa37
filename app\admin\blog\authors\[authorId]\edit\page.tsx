"use client"

import { useState, useEffect } from 'react'
import { useR<PERSON>er, useParams } from 'next/navigation'
import { getBlogAuthors, updateBlogAuthor } from '@/lib/blog-service'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { ArrowLeft, Save, User } from 'lucide-react'
import Link from 'next/link'

export default function EditAuthorPage() {
  const router = useRouter()
  const params = useParams()
  const authorId = params?.authorId as string

  const [author, setAuthor] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    bio: '',
    medical_credentials: '',
    is_medical_reviewer: false,
    is_active: true
  })

  useEffect(() => {
    loadAuthor()
  }, [authorId])

  const loadAuthor = async () => {
    try {
      setIsLoading(true)
      const authors = await getBlogAuthors()
      const authorData = authors.find(a => a.id === authorId)
      
      if (authorData) {
        setAuthor(authorData)
        setFormData({
          name: authorData.name || '',
          email: authorData.email || '',
          bio: authorData.bio || '',
          medical_credentials: authorData.medical_credentials || '',
          is_medical_reviewer: authorData.is_medical_reviewer || false,
          is_active: authorData.is_active !== false
        })
      } else {
        // Author not found, redirect back
        router.push('/admin/blog/authors')
      }
    } catch (error) {
      console.error('Error loading author:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      setIsSaving(true)
      const result = await updateBlogAuthor(authorId, formData)
      
      if (result) {
        alert('Author updated successfully!')
        router.push('/admin/blog/authors')
      } else {
        alert('Error updating author. Please try again.')
      }
    } catch (error) {
      console.error('Error updating author:', error)
      alert('Error updating author. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-foreground">Loading author...</div>
      </div>
    )
  }

  if (!author) {
    return (
      <div className="space-y-6">
        <div className="text-foreground">Author not found.</div>
        <Link href="/admin/blog/authors">
          <Button variant="outline" className="border-border text-foreground hover:bg-accent">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Authors
          </Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/admin/blog/authors">
            <Button variant="outline" size="sm" className="border-border text-foreground hover:bg-accent">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Edit Author</h1>
            <p className="text-foreground/70 mt-2">
              Update author information and settings
            </p>
          </div>
        </div>
        <Button 
          onClick={handleSave}
          disabled={isSaving}
          className="flex items-center gap-2"
        >
          <Save className="h-4 w-4" />
          {isSaving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>

      {/* Edit Form */}
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-foreground">
            <User className="h-5 w-5" />
            Author Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-foreground">Full Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter full name..."
                className="bg-card border-border text-foreground placeholder:text-foreground/50"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email" className="text-foreground">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="Enter email address..."
                className="bg-card border-border text-foreground placeholder:text-foreground/50"
              />
            </div>
          </div>

          {/* Medical Credentials */}
          <div className="space-y-2">
            <Label htmlFor="credentials" className="text-foreground">Medical Credentials</Label>
            <Input
              id="credentials"
              value={formData.medical_credentials}
              onChange={(e) => handleInputChange('medical_credentials', e.target.value)}
              placeholder="e.g., MD, PhD, Board Certified Internal Medicine..."
              className="bg-card border-border text-foreground placeholder:text-foreground/50"
            />
          </div>

          {/* Bio */}
          <div className="space-y-2">
            <Label htmlFor="bio" className="text-foreground">Bio</Label>
            <Textarea
              id="bio"
              value={formData.bio}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              placeholder="Brief author biography..."
              rows={4}
              className="bg-card border-border text-foreground placeholder:text-foreground/50"
            />
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-foreground">Author Settings</h3>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_medical_reviewer"
                checked={formData.is_medical_reviewer}
                onCheckedChange={(checked) => handleInputChange('is_medical_reviewer', checked)}
                className="border-border"
              />
              <Label htmlFor="is_medical_reviewer" className="text-foreground">
                Medical Reviewer
              </Label>
              <span className="text-sm text-foreground/60">
                (Can review and approve medical content)
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_active"
                checked={formData.is_active}
                onCheckedChange={(checked) => handleInputChange('is_active', checked)}
                className="border-border"
              />
              <Label htmlFor="is_active" className="text-foreground">
                Active Author
              </Label>
              <span className="text-sm text-foreground/60">
                (Can be assigned to new posts)
              </span>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end pt-4">
            <Button 
              onClick={handleSave}
              disabled={isSaving || !formData.name.trim()}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 