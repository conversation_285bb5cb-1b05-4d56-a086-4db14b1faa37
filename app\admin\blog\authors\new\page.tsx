"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { ArrowLeft, UserPlus, Save } from 'lucide-react'
import Link from 'next/link'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { createBlogAuthor } from '@/lib/blog-service'

interface AuthorFormData {
  name: string
  email: string
  bio: string
  medical_credentials: string
  is_medical_reviewer: boolean
  is_active: boolean
}

export default function NewAuthorPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState<AuthorFormData>({
    name: '',
    email: '',
    bio: '',
    medical_credentials: '',
    is_medical_reviewer: false,
    is_active: true
  })

  const handleInputChange = (field: keyof AuthorFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim()
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const authorPayload = {
        ...formData,
        slug: generateSlug(formData.name)
      }

      const newAuthor = await createBlogAuthor(authorPayload)
      
      if (newAuthor) {
        alert('Author created successfully!')
        router.push('/admin/blog/authors')
      } else {
        throw new Error('Failed to create author')
      }
    } catch (error) {
      console.error('Error creating author:', error)
      alert('Error creating author. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/admin/blog/authors">
          <Button variant="outline" size="sm" className="border-border text-foreground hover:bg-card">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Authors
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-foreground">Add New Author</h1>
          <p className="text-foreground/70 mt-2">
            Add a new medical expert or content creator to your blog
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Form */}
        <div className="lg:col-span-2">
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-foreground flex items-center gap-2">
                <UserPlus className="h-5 w-5" />
                Author Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name" className="text-foreground">
                      Full Name <span className="text-red-400">*</span>
                    </Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="Dr. John Smith"
                      className="bg-card border-border text-card-foreground placeholder:text-foreground/60"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="email" className="text-foreground">
                      Email Address
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                      className="bg-card border-border text-card-foreground placeholder:text-foreground/60"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="medical_credentials" className="text-foreground">
                    Medical Credentials
                  </Label>
                  <Input
                    id="medical_credentials"
                    value={formData.medical_credentials}
                    onChange={(e) => handleInputChange('medical_credentials', e.target.value)}
                    placeholder="MD, FACC, FSCAI"
                    className="bg-card border-border text-card-foreground placeholder:text-foreground/60"
                  />
                  <p className="text-sm text-foreground/60 mt-1">
                    Enter medical degrees, certifications, and professional credentials
                  </p>
                </div>

                <div>
                  <Label htmlFor="bio" className="text-foreground">
                    Biography
                  </Label>
                  <Textarea
                    id="bio"
                    value={formData.bio}
                    onChange={(e) => handleInputChange('bio', e.target.value)}
                    placeholder="Dr. Smith is a board-certified cardiologist with 15+ years of experience..."
                    rows={4}
                    className="bg-card border-border text-card-foreground placeholder:text-foreground/60"
                  />
                  <p className="text-sm text-foreground/60 mt-1">
                    Brief professional biography and background
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="is_medical_reviewer"
                      checked={formData.is_medical_reviewer}
                      onCheckedChange={(checked) => handleInputChange('is_medical_reviewer', !!checked)}
                      className="border-border"
                    />
                    <Label htmlFor="is_medical_reviewer" className="text-foreground">
                      Medical Reviewer
                    </Label>
                  </div>
                  <p className="text-sm text-foreground/60 ml-6">
                    Can review and approve medical content for accuracy
                  </p>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="is_active"
                      checked={formData.is_active}
                      onCheckedChange={(checked) => handleInputChange('is_active', !!checked)}
                      className="border-border"
                    />
                    <Label htmlFor="is_active" className="text-foreground">
                      Active Author
                    </Label>
                  </div>
                  <p className="text-sm text-foreground/60 ml-6">
                    Author can publish new content and appears in author lists
                  </p>
                </div>

                <div className="flex gap-4 pt-4">
                  <Button
                    type="submit"
                    disabled={isSubmitting || !formData.name.trim()}
                    className="flex items-center gap-2"
                  >
                    <Save className="h-4 w-4" />
                    {isSubmitting ? 'Creating...' : 'Create Author'}
                  </Button>
                  
                  <Link href="/admin/blog/authors">
                    <Button variant="outline" className="border-border text-foreground hover:bg-card">
                      Cancel
                    </Button>
                  </Link>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Preview */}
        <div>
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-foreground">Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                    <span className="text-primary font-semibold">
                      {formData.name.split(' ').map(n => n[0]).join('').slice(0, 2) || 'A'}
                    </span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground">
                      {formData.name || 'Author Name'}
                    </h3>
                    {formData.medical_credentials && (
                      <p className="text-sm text-foreground/70">
                        {formData.medical_credentials}
                      </p>
                    )}
                  </div>
                </div>

                {formData.bio && (
                  <p className="text-sm text-foreground/80">
                    {formData.bio}
                  </p>
                )}

                <div className="space-y-2">
                  {formData.is_medical_reviewer && (
                    <div className="px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-md inline-block">
                      Medical Reviewer
                    </div>
                  )}
                  
                  <div className={`px-2 py-1 text-xs rounded-md inline-block ${
                    formData.is_active 
                      ? 'bg-green-500/20 text-green-400' 
                      : 'bg-background/60/20 text-muted-green'
                  }`}>
                    {formData.is_active ? 'Active' : 'Inactive'}
                  </div>
                </div>

                {formData.email && (
                  <p className="text-sm text-foreground/60">
                    {formData.email}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 