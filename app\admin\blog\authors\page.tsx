"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  PlusCircle, 
  Search,
  Edit,
  Trash2,
  Mail,
  MapPin,
  Award,
  FileText,
  MoreVertical
} from 'lucide-react'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { getBlogAuthors, deleteBlogAuthor } from '@/lib/blog-service'
import type { BlogAuthor } from '@/lib/blog-service'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

const getStatusColor = (isActive: boolean) => {
  return isActive 
    ? 'bg-green-500/20 text-green-400 border-green-500/30'
    : 'bg-background/60/20 text-muted-green border-border/30'
}

export default function BlogAuthorsPage() {
  const [authors, setAuthors] = useState<BlogAuthor[]>([])
  const [filteredAuthors, setFilteredAuthors] = useState<BlogAuthor[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  const loadAuthors = async () => {
    try {
      setIsLoading(true)
      const authorData = await getBlogAuthors()
      setAuthors(authorData)
      setFilteredAuthors(authorData)
    } catch (error) {
      console.error('Error loading authors:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadAuthors()
  }, [])

  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredAuthors(authors)
    } else {
      const filtered = authors.filter(author =>
        author.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        author.bio?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        author.medical_credentials?.toLowerCase().includes(searchTerm.toLowerCase())
      )
      setFilteredAuthors(filtered)
    }
  }, [searchTerm, authors])

  const handleAuthorAction = async (action: string, authorId: string) => {
    try {
      switch (action) {
        case 'Edit Author':
          // Navigate to edit page
          window.location.href = `/admin/blog/authors/${authorId}/edit`
          break
          
        case 'View Posts':
          // Navigate to posts by this author
          window.location.href = `/admin/blog/posts?author=${authorId}`
          break
          
        case 'Activate':
        case 'Deactivate':
          const newStatus = action === 'Activate'
          // In a real app, you'd update the database
          alert(`Author ${action.toLowerCase()}d successfully!`)
          window.location.reload()
          break
          
        case 'Delete':
          if (confirm('Are you sure you want to delete this author? This will also remove them from all their posts.')) {
            console.log('User confirmed author deletion for ID:', authorId)
            const success = await deleteBlogAuthor(authorId)
            if (success) {
              alert('Author deleted successfully!')
              // Refresh the authors list instead of reloading the entire page
              loadAuthors()
            } else {
              alert('Failed to delete author. Please check the console for details and try again.')
            }
          }
          break
          
        default:
          alert(`${action} completed successfully!`)
      }
    } catch (error) {
      console.error('Error performing action:', error)
      alert('Error performing action. Please try again.')
    }
  }

  const handleAddAuthor = () => {
    // Navigate to add author page or open modal
    window.location.href = '/admin/blog/authors/new'
  }

  const activeAuthors = authors.filter(author => author.is_active)
  const inactiveAuthors = authors.filter(author => !author.is_active)
  const medicalReviewers = authors.filter(author => author.is_medical_reviewer)

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Manage Authors</h1>
            <p className="text-foreground/70 mt-2">Loading authors...</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="bg-card border-border animate-pulse">
              <CardContent className="p-4">
                <div className="h-8 bg-accent rounded mb-2"></div>
                <div className="h-4 bg-accent rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Manage Authors</h1>
          <p className="text-foreground/70 mt-2">
            Manage medical expert authors and their credentials
          </p>
        </div>
        <Button className="flex items-center gap-2" onClick={handleAddAuthor}>
          <PlusCircle className="h-4 w-4" />
          Add New Author
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-card border-border">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-foreground">{authors.length}</div>
            <div className="text-sm text-foreground/60">Total Authors</div>
          </CardContent>
        </Card>
        <Card className="bg-card border-border">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-foreground">{activeAuthors.length}</div>
            <div className="text-sm text-foreground/60">Active Authors</div>
          </CardContent>
        </Card>
        <Card className="bg-card border-border">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-foreground">{medicalReviewers.length}</div>
            <div className="text-sm text-foreground/60">Medical Reviewers</div>
          </CardContent>
        </Card>
        <Card className="bg-card border-border">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-foreground">0</div>
            <div className="text-sm text-foreground/60">Total Posts</div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card className="bg-card border-border">
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground/60 h-4 w-4" />
            <Input
              placeholder="Search authors by name, specialty, or credentials..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-card border-border text-card-foreground placeholder:text-foreground/60"
            />
          </div>
        </CardContent>
      </Card>

      {/* Authors List */}
      {filteredAuthors.length === 0 ? (
        <Card className="bg-card border-border">
          <CardContent className="p-8 text-center">
            <div className="text-6xl opacity-40 text-foreground mb-4">👨‍⚕️</div>
            <h3 className="text-xl font-semibold text-foreground mb-2">
              {searchTerm ? 'No Authors Found' : 'No Authors Yet'}
            </h3>
            <p className="text-foreground/70 mb-4">
              {searchTerm 
                ? 'Try adjusting your search terms or filters.'
                : 'Start building your author network by adding medical experts and content creators.'
              }
            </p>
            {!searchTerm && (
              <Button onClick={handleAddAuthor}>
                <PlusCircle className="h-4 w-4 mr-2" />
                Add First Author
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {filteredAuthors.map((author) => (
            <Card key={author.id} className="bg-card border-border hover:bg-white/15 transition-colors">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4 flex-1">
                    <Avatar className="h-16 w-16">
                      <AvatarImage src={author.profile_image_url} alt={author.name} />
                      <AvatarFallback className="bg-primary/20 text-primary text-lg">
                        {author.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-xl font-semibold text-foreground">{author.name}</h3>
                        <Badge className={getStatusColor(author.is_active)}>
                          {author.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                        {author.is_medical_reviewer && (
                          <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                            Medical Reviewer
                          </Badge>
                        )}
                      </div>
                      
                      {author.medical_credentials && (
                        <div className="flex items-center gap-2 text-foreground/70 mb-2">
                          <Award className="h-4 w-4" />
                          <span className="text-sm">{author.medical_credentials}</span>
                        </div>
                      )}
                      
                      {author.email && (
                        <div className="flex items-center gap-2 text-foreground/70 mb-2">
                          <Mail className="h-4 w-4" />
                          <span className="text-sm">{author.email}</span>
                        </div>
                      )}
                      
                      {author.bio && (
                        <p className="text-foreground/80 text-sm line-clamp-2 mb-3">
                          {author.bio}
                        </p>
                      )}
                      
                      <div className="flex items-center gap-4 text-sm text-foreground/60">
                        <div className="flex items-center gap-1">
                          <FileText className="h-4 w-4" />
                          <span>0 posts</span>
                        </div>
                        <div>
                          Joined {new Date(author.created_at).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="bg-background border-border">
                      <DropdownMenuItem onClick={() => handleAuthorAction('Edit Author', author.id)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Author
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleAuthorAction('View Posts', author.id)}>
                        <FileText className="h-4 w-4 mr-2" />
                        View Posts
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleAuthorAction(author.is_active ? 'Deactivate' : 'Activate', author.id)}>
                        {author.is_active ? 'Deactivate' : 'Activate'}
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => handleAuthorAction('Delete', author.id)}
                        className="text-red-400 focus:text-red-400"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
} 