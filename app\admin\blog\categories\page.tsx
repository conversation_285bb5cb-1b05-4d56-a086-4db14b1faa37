"use client"

import { useState, useEffect } from 'react'
import { getBlogCategories, getCategoryPostCounts, getBlogTags } from '@/lib/blog-service'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { 
  PlusCircle, 
  Search,
  Edit,
  Trash2,
  FileText,
  Tag,
  MoreVertical,
  Folder
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function CategoriesTagsPage() {
  const [categories, setCategories] = useState<any[]>([])
  const [tags, setTags] = useState<any[]>([])
  const [categoryPostCounts, setCategoryPostCounts] = useState<Record<string, number>>({})
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setIsLoading(true)
      const [categoriesData, tagsData, postCounts] = await Promise.all([
        getBlogCategories(),
        getBlogTags(),
        getCategoryPostCounts()
      ])
      setCategories(categoriesData)
      setTags(tagsData)
      setCategoryPostCounts(postCounts)
    } catch (error) {
      console.error('Error loading categories and tags:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCategoryAction = (action: string, categoryId: string) => {
    try {
      switch (action) {
        case 'Edit Category':
          alert(`Edit category ${categoryId} - would open edit form`)
          break
          
        case 'View Posts':
          window.location.href = `/admin/blog/posts?category=${categoryId}`
          break
          
        case 'Activate':
        case 'Deactivate':
          alert(`Category ${action.toLowerCase()}d successfully!`)
          window.location.reload()
          break
          
        case 'Delete':
          if (confirm('Are you sure you want to delete this category? Posts in this category will become uncategorized.')) {
            alert('Category deleted successfully!')
            window.location.reload()
          }
          break
          
        default:
          alert(`${action} completed successfully!`)
      }
    } catch (error) {
      console.error('Error performing action:', error)
      alert('Error performing action. Please try again.')
    }
  }

  const handleCreateCategory = () => {
    alert('Create category functionality - would open create form or modal')
  }

  const handleCreateTag = () => {
    alert('Create tag functionality - would open create form or modal')
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-foreground">Loading categories and tags...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Categories & Tags</h1>
          <p className="text-foreground/70 mt-2">
            Organize your blog content with categories and tags
          </p>
        </div>
      </div>

      <Tabs defaultValue="categories" className="space-y-6">
        <TabsList className="bg-card border-border">
          <TabsTrigger value="categories" className="flex items-center gap-2 data-[state=active]:bg-accent data-[state=active]:text-foreground text-foreground/70">
            <Folder className="h-4 w-4" />
            Categories
          </TabsTrigger>
          <TabsTrigger value="tags" className="flex items-center gap-2 data-[state=active]:bg-accent data-[state=active]:text-foreground text-foreground/70">
            <Tag className="h-4 w-4" />
            Tags
          </TabsTrigger>
        </TabsList>

        <TabsContent value="categories" className="space-y-6">
          {/* Categories Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="bg-card border-border">
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-foreground">{categories.length}</div>
                <div className="text-sm text-foreground/60">Total Categories</div>
              </CardContent>
            </Card>
            <Card className="bg-card border-border">
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-foreground">{categories.filter(c => c.is_active).length}</div>
                <div className="text-sm text-foreground/60">Active Categories</div>
              </CardContent>
            </Card>
            <Card className="bg-card border-border">
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-foreground">
                  {Object.values(categoryPostCounts).reduce((sum: number, count: number) => sum + count, 0)}
                </div>
                <div className="text-sm text-foreground/60">Published Posts</div>
              </CardContent>
            </Card>
          </div>

          {/* Add New Category */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-foreground">
                <PlusCircle className="h-5 w-5" />
                Add New Category
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground">Category Name</label>
                  <Input placeholder="Enter category name..." className="bg-card border-border text-foreground placeholder:text-foreground/50" />
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground">Color</label>
                  <Input type="color" defaultValue="#3B82F6" className="bg-card border-border" />
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-foreground">Description</label>
                <Textarea placeholder="Describe this category..." rows={3} className="bg-card border-border text-foreground placeholder:text-foreground/50" />
              </div>
              <Button onClick={handleCreateCategory}>Create Category</Button>
            </CardContent>
          </Card>

          {/* Categories List */}
          <Card className="bg-card border-border">
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-foreground">Categories</CardTitle>
                <div className="flex items-center gap-2">
                  <Input 
                    placeholder="Search categories..." 
                    className="w-64 bg-card border-border text-foreground placeholder:text-foreground/50"
                  />
                  <Button variant="outline" size="icon" className="border-border text-foreground hover:bg-accent">
                    <Search className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {categories.map((category) => (
                  <div key={category.id} className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted-green/50 transition-colors">
                    <div className="flex items-center gap-4">
                      <div 
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: category.color }}
                      />
                      <div>
                        <h3 className="font-semibold text-foreground">{category.name}</h3>
                        <p className="text-sm text-foreground/60">{category.description || 'No description'}</p>
                        <div className="flex items-center gap-4 mt-1">
                          <span className="text-xs text-foreground/50">/{category.slug}</span>
                          <Badge variant="outline" className="border-border text-foreground/70">
                            {categoryPostCounts[category.id] || 0} published posts
                          </Badge>
                          <Badge className={category.is_active ? 'bg-green-500/20 text-green-400' : 'bg-background/60/20 text-muted-green'}>
                            {category.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="text-foreground/70 hover:text-foreground hover:bg-card">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="bg-background/90 border-border">
                        <DropdownMenuItem 
                          className="text-foreground hover:bg-card cursor-pointer"
                          onClick={() => handleCategoryAction('Edit Category', category.id)}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Category
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="text-foreground hover:bg-card cursor-pointer"
                          onClick={() => handleCategoryAction('View Posts', category.id)}
                        >
                          <FileText className="mr-2 h-4 w-4" />
                          View Posts ({categoryPostCounts[category.id] || 0})
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="text-foreground hover:bg-card cursor-pointer"
                          onClick={() => handleCategoryAction(category.is_active ? 'Deactivate' : 'Activate', category.id)}
                        >
                          {category.is_active ? 'Deactivate' : 'Activate'}
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="text-red-400 hover:bg-red-500/10 cursor-pointer"
                          onClick={() => handleCategoryAction('Delete', category.id)}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete Category
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                ))}
                {categories.length === 0 && (
                  <div className="text-center py-8 text-foreground/60">
                    No categories found. Create your first category above.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tags" className="space-y-6">
          {/* Tags Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="bg-card border-border">
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-foreground">{tags.length}</div>
                <div className="text-sm text-foreground/60">Total Tags</div>
              </CardContent>
            </Card>
            <Card className="bg-card border-border">
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-foreground">{tags.filter(t => t.usage_count > 0).length}</div>
                <div className="text-sm text-foreground/60">Used Tags</div>
              </CardContent>
            </Card>
            <Card className="bg-card border-border">
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-foreground">{tags.reduce((sum, t) => sum + (t.usage_count || 0), 0)}</div>
                <div className="text-sm text-foreground/60">Total Usage</div>
              </CardContent>
            </Card>
          </div>

          {/* Add New Tag */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-foreground">
                <PlusCircle className="h-5 w-5" />
                Add New Tag
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground">Tag Name</label>
                  <Input placeholder="Enter tag name..." className="bg-card border-border text-foreground placeholder:text-foreground/50" />
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground">Color</label>
                  <Input type="color" defaultValue="#6B7280" className="bg-card border-border" />
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground">Description</label>
                  <Input placeholder="Optional description..." className="bg-card border-border text-foreground placeholder:text-foreground/50" />
                </div>
              </div>
              <Button onClick={handleCreateTag}>Create Tag</Button>
            </CardContent>
          </Card>

          {/* Tags List */}
          <Card className="bg-card border-border">
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-foreground">Tags</CardTitle>
                <div className="flex items-center gap-2">
                  <Input 
                    placeholder="Search tags..." 
                    className="w-64 bg-card border-border text-foreground placeholder:text-foreground/50"
                  />
                  <Button variant="outline" size="icon" className="border-border text-foreground hover:bg-accent">
                    <Search className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {tags.map((tag) => (
                  <Card key={tag.id} className="bg-muted-green/50 border-border/50 hover:bg-card transition-colors">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-3 h-3 rounded"
                            style={{ backgroundColor: tag.color }}
                          />
                          <h3 className="font-semibold text-foreground">{tag.name}</h3>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="text-foreground/70 hover:text-foreground hover:bg-card">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent className="bg-background/90 border-border">
                            <DropdownMenuItem className="text-foreground hover:bg-card">
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Tag
                            </DropdownMenuItem>
                            <DropdownMenuItem className="text-red-400 hover:bg-red-500/10">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete Tag
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                      <p className="text-sm text-foreground/60 mb-2">/{tag.slug}</p>
                      <Badge variant="outline" className="border-border text-foreground/70">
                        {tag.usage_count || 0} posts
                      </Badge>
                    </CardContent>
                  </Card>
                ))}
                {tags.length === 0 && (
                  <div className="col-span-full text-center py-8 text-foreground/60">
                    No tags found. Create your first tag above.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 