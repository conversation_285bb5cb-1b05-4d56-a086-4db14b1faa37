"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Loader2, Bug } from 'lucide-react'
import { checkBlogDatabaseStructure, checkBlogRelationships } from '../debug-utils'
import { useToast } from '@/hooks/use-toast'

export default function DebugButton() {
  const [isChecking, setIsChecking] = useState(false)
  const [debugResults, setDebugResults] = useState<any>(null)
  const { toast } = useToast()

  const runDiagnostics = async () => {
    setIsChecking(true)
    setDebugResults(null)
    
    try {
      console.log('🔍 DEBUG: Starting blog system diagnostics')
      
      // Run database structure check
      const structureResults = await checkBlogDatabaseStructure()
      console.log('📋 DEBUG: Database structure check results:', structureResults)
      
      // Run relationships check
      const relationshipResults = await checkBlogRelationships()
      console.log('📋 DEBUG: Relationship check results:', relationshipResults)
      
      // Combine results
      const results = {
        structure: structureResults,
        relationships: relationshipResults,
        timestamp: new Date().toISOString()
      }
      
      setDebugResults(results)
      
      // Show toast with summary
      if (structureResults.success && relationshipResults.success) {
        toast({
          title: "Diagnostics Passed",
          description: "All database tables and relationships are working correctly.",
        })
      } else {
        toast({
          title: "Issues Detected",
          description: "Check the console for detailed diagnostic information.",
          variant: "destructive",
        })
      }
      
    } catch (error) {
      console.error('❌ ERROR: Exception during diagnostics:', error)
      toast({
        title: "Diagnostics Failed",
        description: "An unexpected error occurred. Check the console.",
        variant: "destructive",
      })
    } finally {
      setIsChecking(false)
    }
  }

  return (
    <div>
      <Button 
        onClick={runDiagnostics}
        disabled={isChecking}
        variant="outline"
        size="sm"
        className="border-border text-foreground hover:bg-accent"
      >
        {isChecking ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Running Diagnostics...
          </>
        ) : (
          <>
            <Bug className="mr-2 h-4 w-4" />
            Run Diagnostics
          </>
        )}
      </Button>
      
      {debugResults && (
        <div className="mt-2 text-xs text-foreground/60">
          Diagnostics run at {new Date(debugResults.timestamp).toLocaleTimeString()}
        </div>
      )}
    </div>
  )
} 