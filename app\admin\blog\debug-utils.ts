"use server"

import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

/**
 * Utility function to check if required blog database tables exist
 * Run this from any admin page to debug database connectivity issues
 */
export async function checkBlogDatabaseStructure() {
  console.log('🔍 DATABASE DEBUG: Running database structure check')
  const supabase = createServerComponentClient({ cookies })
  const tables = ['blog_posts', 'blog_authors', 'blog_categories']
  const results: Record<string, boolean> = {}
  
  try {
    // Check each table
    for (const table of tables) {
      try {
        console.log(`🔍 DATABASE DEBUG: Checking if ${table} table exists...`)
        const { count, error } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true })
        
        if (error) {
          console.error(`❌ DATABASE ERROR: Table ${table} check failed:`, error)
          results[table] = false
        } else {
          console.log(`✅ DATABASE DEBUG: Table ${table} exists with ${count} rows`)
          results[table] = true
        }
      } catch (tableError) {
        console.error(`❌ DATABASE ERROR: Exception checking ${table}:`, tableError)
        results[table] = false
      }
    }
    
    // If blog_posts exists, check for AI columns
    if (results['blog_posts']) {
      try {
        console.log(`🔍 DATABASE DEBUG: Checking for AI generation columns in blog_posts...`)
        const { data, error } = await supabase
          .from('blog_posts')
          .select('generation_source, ai_generation_job_id, meta_keywords')
          .limit(1)
        
        if (error) {
          console.error(`❌ DATABASE ERROR: AI columns check failed:`, error)
          results['ai_columns'] = false
        } else {
          console.log(`✅ DATABASE DEBUG: AI generation columns exist in blog_posts`)
          results['ai_columns'] = true
        }
      } catch (columnError) {
        console.error(`❌ DATABASE ERROR: Exception checking AI columns:`, columnError)
        results['ai_columns'] = false
      }
    }
    
    return {
      success: Object.values(results).every(Boolean),
      results
    }
  } catch (error) {
    console.error('❌ DATABASE ERROR: Database check failed with unexpected error:', error)
    return {
      success: false,
      error: 'Database connection failed'
    }
  }
}

/**
 * Check if all required relationships exist between tables
 */
export async function checkBlogRelationships() {
  console.log('🔍 DATABASE DEBUG: Checking table relationships')
  const supabase = createServerComponentClient({ cookies })
  
  try {
    // Check post-author relationship
    console.log('🔍 DATABASE DEBUG: Checking post-author relationship...')
    const { data: authorRelationData, error: authorRelationError } = await supabase
      .from('blog_posts')
      .select(`
        id,
        blog_authors (id, name)
      `)
      .limit(1)
    
    if (authorRelationError) {
      console.error('❌ DATABASE ERROR: Author relationship check failed:', authorRelationError)
    } else {
      console.log('✅ DATABASE DEBUG: Post-author relationship exists:', authorRelationData)
    }
    
    // Check post-category relationship
    console.log('🔍 DATABASE DEBUG: Checking post-category relationship...')
    const { data: categoryRelationData, error: categoryRelationError } = await supabase
      .from('blog_posts')
      .select(`
        id,
        blog_categories (id, name)
      `)
      .limit(1)
    
    if (categoryRelationError) {
      console.error('❌ DATABASE ERROR: Category relationship check failed:', categoryRelationError)
    } else {
      console.log('✅ DATABASE DEBUG: Post-category relationship exists:', categoryRelationData)
    }
    
    return {
      success: !authorRelationError && !categoryRelationError,
      authorRelationship: !authorRelationError,
      categoryRelationship: !categoryRelationError
    }
  } catch (error) {
    console.error('❌ DATABASE ERROR: Relationship check failed with unexpected error:', error)
    return {
      success: false,
      error: 'Database relationship check failed'
    }
  }
} 