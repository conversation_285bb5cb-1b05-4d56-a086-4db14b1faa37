"use client"

import { useState, useEffect } from 'react'
import { getBlogStats, getBlogPosts } from '@/lib/blog-service'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  PlusCircle, 
  FileText, 
  Users, 
  Tags, 
  BarChart3, 
  Eye,
  Edit,
  Trash2,
  Calendar,
  Clock,
  User,
  Bot
} from 'lucide-react'
import Link from 'next/link'
import dynamic from 'next/dynamic'

// Dynamically import the debug button to avoid server/client mismatch
const ClientDebugButton = dynamic(
  () => import('./components/DebugButton'),
  { ssr: false }
)

export default function BlogAdminPage() {
  const [blogStats, setBlogStats] = useState({
    totalPosts: 0,
    publishedPosts: 0,
    draftPosts: 0,
    totalAuthors: <AUTHORS>
    totalCategories: 0,
    totalViews: 0,
    monthlyViews: 0
  })
  const [recentPosts, setRecentPosts] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setIsLoading(true)
      const [stats, posts] = await Promise.all([
        getBlogStats(),
        getBlogPosts({ limit: 3 }) // Get 3 most recent posts
      ])
      setBlogStats(stats)
      setRecentPosts(posts)
    } catch (error) {
      console.error('Error loading blog data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="text-foreground">Loading blog dashboard...</div>
      </div>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'draft': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'review': return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      default: return 'bg-background/60/20 text-muted-green border-border/30'
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Blog Management</h1>
          <p className="text-foreground/70 mt-2">
            Manage your medical insights blog content, authors, and analytics
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Link href="/admin/blog/posts/new">
            <Button className="flex items-center gap-2">
              <PlusCircle className="h-4 w-4" />
              Create New Post
            </Button>
          </Link>
          {/* Dynamic import for debug button */}
          {process.env.NODE_ENV !== 'production' && (
            <div id="debug-button-container" suppressHydrationWarning>
              <ClientDebugButton />
            </div>
          )}
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-card border-border">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-foreground">Total Posts</CardTitle>
            <FileText className="h-4 w-4 text-foreground/60" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{blogStats.totalPosts}</div>
            <p className="text-xs text-foreground/60">
              {blogStats.publishedPosts} published, {blogStats.draftPosts} drafts
            </p>
          </CardContent>
        </Card>

        <Card className="bg-card border-border">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-foreground">Authors</CardTitle>
            <Users className="h-4 w-4 text-foreground/60" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{blogStats.totalAuthors}</div>
            <p className="text-xs text-foreground/60">
              Active medical experts
            </p>
          </CardContent>
        </Card>

        <Card className="bg-card border-border">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-foreground">Total Views</CardTitle>
            <Eye className="h-4 w-4 text-foreground/60" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{blogStats.totalViews.toLocaleString()}</div>
            <p className="text-xs text-foreground/60">
              +{blogStats.monthlyViews.toLocaleString()} this month
            </p>
          </CardContent>
        </Card>

        <Card className="bg-card border-border">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-foreground">Categories</CardTitle>
            <Tags className="h-4 w-4 text-foreground/60" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{blogStats.totalCategories}</div>
            <p className="text-xs text-foreground/60">
              Expert content areas
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Link href="/admin/blog/posts">
          <Card className="hover:shadow-md transition-shadow cursor-pointer bg-card border-border hover:bg-white/15">
            <CardContent className="p-6 text-center">
              <FileText className="h-8 w-8 mx-auto mb-2 text-primary" />
              <h3 className="font-semibold text-foreground">Manage Posts</h3>
              <p className="text-sm text-foreground/70">Create, edit, and publish articles</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/blog/ai-generate">
          <Card className="hover:shadow-md transition-shadow cursor-pointer bg-card border-border hover:bg-white/15">
            <CardContent className="p-6 text-center">
              <Bot className="h-8 w-8 mx-auto mb-2 text-primary" />
              <h3 className="font-semibold text-foreground">AI Generation</h3>
              <p className="text-sm text-foreground/70">Generate content with AI</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/blog/authors">
          <Card className="hover:shadow-md transition-shadow cursor-pointer bg-card border-border hover:bg-white/15">
            <CardContent className="p-6 text-center">
              <Users className="h-8 w-8 mx-auto mb-2 text-primary" />
              <h3 className="font-semibold text-foreground">Manage Authors</h3>
              <p className="text-sm text-foreground/70">Add and manage medical experts</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/blog/categories">
          <Card className="hover:shadow-md transition-shadow cursor-pointer bg-card border-border hover:bg-white/15">
            <CardContent className="p-6 text-center">
              <Tags className="h-8 w-8 mx-auto mb-2 text-primary" />
              <h3 className="font-semibold text-foreground">Categories & Tags</h3>
              <p className="text-sm text-foreground/70">Organize content structure</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/blog/analytics">
          <Card className="hover:shadow-md transition-shadow cursor-pointer bg-card border-border hover:bg-white/15">
            <CardContent className="p-6 text-center">
              <BarChart3 className="h-8 w-8 mx-auto mb-2 text-primary" />
              <h3 className="font-semibold text-foreground">Analytics</h3>
              <p className="text-sm text-foreground/70">View performance metrics</p>
            </CardContent>
          </Card>
        </Link>
      </div>

      {/* Recent Posts */}
      <Card className="bg-card border-border">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="text-foreground">Recent Posts</CardTitle>
            <Link href="/admin/blog/posts">
              <Button variant="outline" size="sm" className="border-border text-foreground hover:bg-accent">View All</Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentPosts.map((post) => (
              <div key={post.id} className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted-green/50 transition-colors">
                <div className="flex-1">
                  <h4 className="font-semibold text-foreground mb-1">{post.title}</h4>
                  <div className="flex items-center gap-4 text-sm text-foreground/60">
                    <Badge className={getStatusColor(post.status || 'draft')}>
                      {post.status || 'draft'}
                    </Badge>
                    <span className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      {post.author?.name || 'Unknown Author'}
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {post.published_at ? new Date(post.published_at).toLocaleDateString() : 'Draft'}
                    </span>
                    <span className="flex items-center gap-1">
                      <Eye className="h-4 w-4" />
                      {post.view_count || 0} views
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {post.reading_time_minutes || 5} min
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm" className="text-foreground/70 hover:text-foreground hover:bg-card">
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="text-foreground/70 hover:text-foreground hover:bg-card">
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 