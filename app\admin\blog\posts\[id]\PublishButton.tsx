"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Loader2, CheckCircle } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useToast } from '@/hooks/use-toast'
import { publishArticle } from './actions'

interface PublishButtonProps {
  postId: string
}

export default function PublishButton({ postId }: PublishButtonProps) {
  const [isPublishing, setIsPublishing] = useState(false)
  const router = useRouter()
  const { toast } = useToast()

  const handlePublish = async () => {
    console.log(`🔍 DEBUG: Starting publish process for article ID: ${postId}`)
    setIsPublishing(true)
    
    try {
      console.log(`🔍 DEBUG: Calling publishArticle server action with ID: ${postId}`)
      const result = await publishArticle(postId)
      
      console.log(`📋 DEBUG: Publish result:`, result)
      
      if (result.success) {
        console.log(`✅ DEBUG: Article published successfully:`, result.data)
        toast({
          title: "Article Published!",
          description: "The article has been successfully published.",
        })
        
        // Refresh the page to show updated status
        console.log(`🔄 DEBUG: Refreshing page to show updated status`)
        router.refresh()
      } else {
        console.error(`❌ ERROR: Publishing failed:`, result.error)
        toast({
          title: "Publishing Failed",
          description: result.error || "Failed to publish the article",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('❌ ERROR: Exception during article publishing:', error)
      toast({
        title: "Publishing Failed",
        description: "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setIsPublishing(false)
    }
  }

  return (
    <Button 
      onClick={handlePublish}
      disabled={isPublishing}
      className="bg-green-600 hover:bg-green-700"
    >
      {isPublishing ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Publishing...
        </>
      ) : (
        <>
          <CheckCircle className="mr-2 h-4 w-4" />
          Publish Article
        </>
      )}
    </Button>
  )
} 