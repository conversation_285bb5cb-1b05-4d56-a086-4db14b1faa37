"use server"

import { createServerActionClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { revalidatePath } from 'next/cache'

export async function publishArticle(postId: string) {
  console.log(`🔍 SERVER ACTION: Publishing article with ID: ${postId}`)
  
  try {
    const supabase = createServerActionClient({ cookies })

    console.log(`🔍 SERVER ACTION: Checking if article exists before updating`)
    const { data: existingPost, error: checkError } = await supabase
      .from('blog_posts')
      .select('id, title, slug, status')
      .eq('id', postId)
      .single()
      
    if (checkError) {
      console.error(`❌ SERVER ERROR: Article with ID ${postId} not found:`, checkError)
      return {
        success: false,
        error: `Article not found: ${checkError.message}`,
      }
    }
    
    console.log(`✅ SERVER ACTION: Found article to update:`, existingPost)

    // Update the article status to published and set published_at timestamp
    console.log(`🔍 SERVER ACTION: Updating article status to published`)
    const { data, error } = await supabase
      .from('blog_posts')
      .update({
        status: 'published',
        published_at: new Date().toISOString(),
      })
      .eq('id', postId)
      .select()
      .single()

    if (error) {
      console.error('❌ SERVER ERROR: Failed to publish article:', error)
      return {
        success: false,
        error: error.message,
      }
    }

    console.log(`✅ SERVER ACTION: Article successfully published:`, {
      id: data.id,
      title: data.title,
      slug: data.slug,
      status: data.status,
      published_at: data.published_at
    })

    // Revalidate relevant paths to refresh the data
    console.log(`🔄 SERVER ACTION: Revalidating paths`)
    revalidatePath('/admin/blog')
    revalidatePath('/admin/blog/posts')
    revalidatePath(`/admin/blog/posts/${postId}`)
    revalidatePath('/admin/blog/ai-generate')

    // Also revalidate the public blog paths if they exist
    revalidatePath('/blog')
    if (data?.slug) {
      revalidatePath(`/blog/${data.slug}`)
    }

    return {
      success: true,
      data,
    }
  } catch (error) {
    console.error('Unexpected error publishing article:', error)
    return {
      success: false,
      error: 'An unexpected error occurred while publishing the article',
    }
  }
} 