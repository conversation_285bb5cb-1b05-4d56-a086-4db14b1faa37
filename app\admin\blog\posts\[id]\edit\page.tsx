'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Save, Eye, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { toast } from 'sonner'

interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt: string | null
  content: string
  status: 'draft' | 'published' | 'archived'
  category_id: string
  author_id: string
  featured_image_url: string | null
  featured_image_alt: string | null
  meta_title: string | null
  meta_description: string | null
  reading_time_minutes: number | null
}

interface Category {
  id: string
  name: string
  slug: string
}

interface Author {
  id: string
  name: string
  email: string
}

export default function EditBlogPostPage() {
  const router = useRouter()
  const params = useParams()
  const postId = params?.id as string
  const supabase = createClientComponentClient()

  // Guard against undefined postId
  if (!postId) {
    return (
      <div className="text-center py-8">
        <h1 className="text-2xl font-bold text-foreground mb-4">Invalid Post ID</h1>
        <Link href="/admin/blog/posts">
          <Button variant="outline" className="border-border text-foreground hover:bg-accent">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Posts
          </Button>
        </Link>
      </div>
    )
  }

  const [post, setPost] = useState<BlogPost | null>(null)
  const [categories, setCategories] = useState<Category[]>([])
  const [authors, setAuthors] = useState<Author[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    status: 'draft' as 'draft' | 'published' | 'archived',
    category_id: '',
    author_id: '',
    featured_image_url: '',
    featured_image_alt: '',
    meta_title: '',
    meta_description: '',
    reading_time_minutes: 0
  })

  // Load post data and options
  useEffect(() => {
    loadData()
  }, [postId])

  const loadData = async () => {
    try {
      setLoading(true)

      // Load post data
      const { data: postData, error: postError } = await supabase
        .from('blog_posts')
        .select('*')
        .eq('id', postId)
        .single()

      if (postError) {
        console.error('Error loading post:', postError)
        toast.error('Failed to load blog post')
        return
      }

      setPost(postData)
      setFormData({
        title: postData.title || '',
        slug: postData.slug || '',
        excerpt: postData.excerpt || '',
        content: postData.content || '',
        status: postData.status || 'draft',
        category_id: postData.category_id || '',
        author_id: postData.author_id || '',
        featured_image_url: postData.featured_image_url || '',
        featured_image_alt: postData.featured_image_alt || '',
        meta_title: postData.meta_title || '',
        meta_description: postData.meta_description || '',
        reading_time_minutes: postData.reading_time_minutes || 0
      })

      // Load categories
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('blog_categories')
        .select('id, name, slug')
        .eq('is_active', true)
        .order('name')

      if (categoriesError) {
        console.error('Error loading categories:', categoriesError)
      } else {
        setCategories(categoriesData || [])
      }

      // Load authors
      const { data: authorsData, error: authorsError } = await supabase
        .from('blog_authors')
        .select('id, name, email')
        .eq('is_active', true)
        .order('name')

      if (authorsError) {
        console.error('Error loading authors:', authorsError)
      } else {
        setAuthors(authorsData || [])
      }

    } catch (error) {
      console.error('Error loading data:', error)
      toast.error('Failed to load data')
    } finally {
      setLoading(false)
    }
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: generateSlug(title)
    }))
  }

  const estimateReadingTime = (content: string) => {
    const wordsPerMinute = 200
    const wordCount = content.replace(/<[^>]*>/g, '').split(/\s+/).length
    return Math.ceil(wordCount / wordsPerMinute)
  }

  const handleContentChange = (content: string) => {
    const readingTime = estimateReadingTime(content)
    setFormData(prev => ({
      ...prev,
      content,
      reading_time_minutes: readingTime
    }))
  }

  const handleSave = async () => {
    try {
      setSaving(true)

      const updateData = {
        title: formData.title,
        slug: formData.slug,
        excerpt: formData.excerpt || null,
        content: formData.content,
        status: formData.status,
        category_id: formData.category_id || null,
        author_id: formData.author_id || null,
        featured_image_url: formData.featured_image_url || null,
        featured_image_alt: formData.featured_image_alt || null,
        meta_title: formData.meta_title || null,
        meta_description: formData.meta_description || null,
        reading_time_minutes: formData.reading_time_minutes,
        updated_at: new Date().toISOString()
      }

      const { error } = await supabase
        .from('blog_posts')
        .update(updateData)
        .eq('id', postId)

      if (error) {
        console.error('Error updating post:', error)
        toast.error('Failed to update post')
        return
      }

      toast.success('Post updated successfully!')
      router.push(`/admin/blog/posts/${postId}`)

    } catch (error) {
      console.error('Error saving post:', error)
      toast.error('Failed to save post')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  if (!post) {
    return (
      <div className="text-center py-8">
        <h1 className="text-2xl font-bold text-foreground mb-4">Post Not Found</h1>
        <Link href="/admin/blog/posts">
          <Button variant="outline" className="border-border text-foreground hover:bg-accent">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Posts
          </Button>
        </Link>
      </div>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'draft': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'archived': return 'bg-background/60/20 text-muted-green border-border/30'
      default: return 'bg-background/60/20 text-muted-green border-border/30'
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="flex items-center gap-4">
          <Link href={`/admin/blog/posts/${postId}`}>
            <Button variant="outline" size="sm" className="border-border text-foreground hover:bg-accent">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Post
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Edit Article</h1>
            <p className="text-muted-green mt-1">Update your blog post content and settings</p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <Badge className={getStatusColor(formData.status)}>
            {formData.status}
          </Badge>
          {formData.status === 'published' && (
            <Link href={`/blog/${formData.slug}`} target="_blank">
              <Button variant="outline" size="sm" className="border-border text-foreground hover:bg-accent">
                <Eye className="mr-2 h-4 w-4" />
                View Live
              </Button>
            </Link>
          )}
        </div>
      </div>

      {/* Edit Form */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title" className="text-card-foreground">Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleTitleChange(e.target.value)}
                  className="bg-background border-border text-foreground"
                  placeholder="Enter post title"
                />
              </div>

              <div>
                <Label htmlFor="slug" className="text-card-foreground">Slug</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  className="bg-background border-border text-foreground"
                  placeholder="post-url-slug"
                />
              </div>

              <div>
                <Label htmlFor="excerpt" className="text-card-foreground">Excerpt</Label>
                <Textarea
                  id="excerpt"
                  value={formData.excerpt}
                  onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
                  className="bg-background border-border text-foreground min-h-[100px]"
                  placeholder="Brief description of the post"
                />
              </div>
            </CardContent>
          </Card>

          {/* Content */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground">Content</CardTitle>
              <p className="text-muted-green text-sm">
                Estimated reading time: {formData.reading_time_minutes} minutes
              </p>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="content" className="text-card-foreground">Article Content (HTML)</Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => handleContentChange(e.target.value)}
                  className="bg-background border-border text-foreground min-h-[400px] font-mono text-sm"
                  placeholder="Enter your HTML content here..."
                />
              </div>
            </CardContent>
          </Card>

          {/* SEO */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground">SEO Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="meta_title" className="text-card-foreground">Meta Title</Label>
                <Input
                  id="meta_title"
                  value={formData.meta_title}
                  onChange={(e) => setFormData(prev => ({ ...prev, meta_title: e.target.value }))}
                  className="bg-background border-border text-foreground"
                  placeholder="SEO title (defaults to post title)"
                />
              </div>

              <div>
                <Label htmlFor="meta_description" className="text-card-foreground">Meta Description</Label>
                <Textarea
                  id="meta_description"
                  value={formData.meta_description}
                  onChange={(e) => setFormData(prev => ({ ...prev, meta_description: e.target.value }))}
                  className="bg-background border-border text-foreground min-h-[80px]"
                  placeholder="SEO description (defaults to excerpt)"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status & Publishing */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground">Publishing</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="status" className="text-card-foreground">Status</Label>
                <Select value={formData.status} onValueChange={(value: 'draft' | 'published' | 'archived') => setFormData(prev => ({ ...prev, status: value }))}>
                  <SelectTrigger className="bg-background border-border text-foreground">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="category" className="text-card-foreground">Category</Label>
                <Select value={formData.category_id} onValueChange={(value) => setFormData(prev => ({ ...prev, category_id: value }))}>
                  <SelectTrigger className="bg-background border-border text-foreground">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="author" className="text-card-foreground">Author</Label>
                <Select value={formData.author_id} onValueChange={(value) => setFormData(prev => ({ ...prev, author_id: value }))}>
                  <SelectTrigger className="bg-background border-border text-foreground">
                    <SelectValue placeholder="Select author" />
                  </SelectTrigger>
                  <SelectContent>
                    {authors.map((author) => (
                      <SelectItem key={author.id} value={author.id}>
                        {author.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Featured Image */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground">Featured Image</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="featured_image_url" className="text-card-foreground">Image URL</Label>
                <Input
                  id="featured_image_url"
                  value={formData.featured_image_url}
                  onChange={(e) => setFormData(prev => ({ ...prev, featured_image_url: e.target.value }))}
                  className="bg-background border-border text-foreground"
                  placeholder="https://..."
                />
              </div>

              <div>
                <Label htmlFor="featured_image_alt" className="text-card-foreground">Alt Text</Label>
                <Input
                  id="featured_image_alt"
                  value={formData.featured_image_alt}
                  onChange={(e) => setFormData(prev => ({ ...prev, featured_image_alt: e.target.value }))}
                  className="bg-background border-border text-foreground"
                  placeholder="Describe the image"
                />
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card className="bg-card border-border">
            <CardContent className="pt-6">
              <Button 
                onClick={handleSave}
                disabled={saving}
                className="w-full bg-primary hover:bg-primary/90"
              >
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 