import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  ArrowLeft, 
  Calendar, 
  User, 
  Tag, 
  Eye, 
  Bot,
  Clock,
  Hash,
  CheckCircle,
  Edit
} from 'lucide-react'
import Link from 'next/link'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { notFound } from 'next/navigation'

// Import the publish action directly instead of the component
import { publishArticle } from './actions'

interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt: string | null
  content: string
  status: string
  generation_source: string
  ai_generation_job_id: string | null
  created_at: string
  published_at: string | null
  meta_keywords: string[] | null
  view_count: number | null
  reading_time: number | null
  author: {
    id: string
    name: string
    email: string
  } | null
  category: {
    id: string
    name: string
    slug: string
  } | null
}

export default async function BlogPostReviewPage({
  params,
}: {
  params: { id: string }
}) {
  const { id } = await params
  const supabase = createServerComponentClient({ cookies })

  console.log(`🔍 DEBUG: Fetching blog post with ID: ${id}`)
  
  // Fetch the blog post with author and category details
  // Specify the exact foreign key relationship to avoid ambiguity
  const { data: post, error } = await supabase
    .from('blog_posts')
    .select(`
      *,
      blog_authors!blog_posts_author_id_fkey (
        id,
        name,
        email
      ),
      blog_categories (
        id,
        name,
        slug
      )
    `)
    .eq('id', id)
    .single()

  if (error) {
    console.error(`❌ ERROR: Failed to fetch blog post with ID ${id}:`, error)
    
    // Additional debugging for specific error types
    if (error.code === 'PGRST116') {
      console.error(`❌ ERROR: No post found with ID ${id}`)
    } else if (error.code === '42P01') {
      console.error(`❌ ERROR: Table does not exist - database schema issue`)
    }
    
    notFound()
  }
  
  if (!post) {
    console.error(`❌ ERROR: No post data returned for ID ${id}`)
    notFound()
  }
  
  console.log(`✅ DEBUG: Successfully fetched post: "${post.title}" (ID: ${post.id})`)
  console.log('📋 DEBUG: Post data:', {
    id: post.id,
    title: post.title,
    slug: post.slug,
    status: post.status,
    content_length: post.content?.length || 0,
    author_id: post.author_id,
    category_id: post.category_id,
    generation_source: post.generation_source,
    has_author_relation: !!post["blog_authors!blog_posts_author_id_fkey"],
    has_category_relation: !!post.blog_categories
  })

  // Transform the data to match our interface
  const blogPost: BlogPost = {
    ...post,
    // Handle optional AI generation fields with fallbacks
    generation_source: post.generation_source || 'manual',
    ai_generation_job_id: post.ai_generation_job_id || null,
    meta_keywords: post.meta_keywords || null,
    view_count: post.view_count || 0,
    reading_time: post.reading_time_minutes || null,
    author: post["blog_authors!blog_posts_author_id_fkey"] ? {
      id: (post["blog_authors!blog_posts_author_id_fkey"] as any).id,
      name: (post["blog_authors!blog_posts_author_id_fkey"] as any).name,
      email: (post["blog_authors!blog_posts_author_id_fkey"] as any).email,
    } : null,
    category: post.blog_categories ? {
      id: (post.blog_categories as any).id,
      name: (post.blog_categories as any).name,
      slug: (post.blog_categories as any).slug,
    } : null,
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'draft': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'review': return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      default: return 'bg-background/60/20 text-muted-green border-border/30'
    }
  }

  const isAIGenerated = blogPost.generation_source === 'ai_generated'

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="flex items-center gap-4">
          <Link href="/admin/blog/posts">
            <Button variant="outline" size="sm" className="border-border text-foreground hover:bg-accent">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Posts
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Review Article</h1>
            <p className="text-foreground/70 mt-1">
              {isAIGenerated ? 'AI-Generated Content' : 'Manual Content'}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <Badge className={getStatusColor(blogPost.status)}>
            {blogPost.status}
          </Badge>
          {isAIGenerated && (
            <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
              <Bot className="mr-1 h-3 w-3" />
              AI Generated
            </Badge>
          )}
          {blogPost.status === 'draft' && (
            <form action={async () => {
              'use server';
              await publishArticle(blogPost.id);
            }}>
              <Button 
                type="submit"
                className="bg-green-600 hover:bg-green-700"
              >
                <CheckCircle className="mr-2 h-4 w-4" />
                Publish Article
              </Button>
            </form>
          )}
        </div>
      </div>

      {/* Article Metadata */}
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="text-foreground">Article Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="text-xl font-semibold text-foreground mb-2">{blogPost.title}</h3>
            <p className="text-foreground/70 text-sm">Slug: <code className="bg-card px-1 rounded">{blogPost.slug}</code></p>
          </div>

          {blogPost.excerpt && (
            <div>
              <h4 className="text-foreground font-medium mb-1">Excerpt</h4>
              <p className="text-foreground/80 text-sm">{blogPost.excerpt}</p>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
            <div className="flex items-center gap-2 text-foreground/60">
              <User className="h-4 w-4" />
              <span>{blogPost.author?.name || 'Unknown Author'}</span>
            </div>
            
            <div className="flex items-center gap-2 text-foreground/60">
              <Tag className="h-4 w-4" />
              <span>{blogPost.category?.name || 'Uncategorized'}</span>
            </div>

            <div className="flex items-center gap-2 text-foreground/60">
              <Calendar className="h-4 w-4" />
              <span>{new Date(blogPost.created_at).toLocaleDateString()}</span>
            </div>

            {blogPost.view_count !== null && (
              <div className="flex items-center gap-2 text-foreground/60">
                <Eye className="h-4 w-4" />
                <span>{blogPost.view_count} views</span>
              </div>
            )}

            {blogPost.reading_time && (
              <div className="flex items-center gap-2 text-foreground/60">
                <Clock className="h-4 w-4" />
                <span>{blogPost.reading_time} min read</span>
              </div>
            )}
          </div>

          {blogPost.meta_keywords && blogPost.meta_keywords.length > 0 && (
            <div>
              <h4 className="text-foreground font-medium mb-2 flex items-center gap-1">
                <Hash className="h-4 w-4" />
                SEO Keywords
              </h4>
              <div className="flex flex-wrap gap-2">
                {blogPost.meta_keywords.map((keyword, index) => (
                  <Badge key={index} variant="outline" className="text-xs border-border text-foreground/70">
                    {keyword}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {isAIGenerated && blogPost.ai_generation_job_id && (
            <div>
              <h4 className="text-foreground font-medium mb-1">AI Generation ID</h4>
              <p className="text-foreground/60 text-xs font-mono bg-card px-2 py-1 rounded">
                {blogPost.ai_generation_job_id}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Article Content */}
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="text-foreground">Article Content</CardTitle>
          {isAIGenerated && (
            <p className="text-foreground/60 text-sm">
              {/* TODO: Use DOMPurify for better security in production */}
              ⚠️ This is AI-generated content. Please review carefully before publishing.
            </p>
          )}
        </CardHeader>
        <CardContent>
          <div 
            className="prose prose-invert max-w-none text-foreground/90"
            dangerouslySetInnerHTML={{ __html: blogPost.content }}
          />
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex justify-between items-center">
        <div className="flex gap-3">
          <Link href={`/admin/blog/posts/${blogPost.id}/edit`}>
            <Button variant="outline" className="border-border text-foreground hover:bg-accent">
              <Edit className="mr-2 h-4 w-4" />
              Edit Article
            </Button>
          </Link>
          {blogPost.status === 'published' && (
            <Link href={`/blog/${blogPost.slug}`} target="_blank">
              <Button variant="outline" className="border-border text-foreground hover:bg-accent">
                <Eye className="mr-2 h-4 w-4" />
                View Live
              </Button>
            </Link>
          )}
        </div>

        {blogPost.status === 'draft' && (
          <form action={async () => {
            'use server';
            await publishArticle(blogPost.id);
          }}>
            <Button 
              type="submit"
              className="bg-green-600 hover:bg-green-700"
            >
              <CheckCircle className="mr-2 h-4 w-4" />
              Publish Article
            </Button>
          </form>
        )}
      </div>
    </div>
  )
} 