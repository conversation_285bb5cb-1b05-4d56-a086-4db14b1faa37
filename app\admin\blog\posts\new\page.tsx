"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createBlogPost, getBlogCategories, getBlogAuthors } from '@/lib/blog-service'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  Save, 
  Eye, 
  Upload,
  X,
  Plus
} from 'lucide-react'
import Link from 'next/link'
import { Separator } from '@/components/ui/separator'

export default function NewBlogPostPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(true)
  const [currentTag, setCurrentTag] = useState('')
  const [categories, setCategories] = useState<any[]>([])
  const [authors, setAuthors] = useState<any[]>([])
  
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    categoryId: '',
    authorId: '',
    medicalReviewerId: '',
    status: 'draft',
    metaTitle: '',
    metaDescription: '',
    featuredImageUrl: '',
    featuredImageAlt: '',
    readingTimeMinutes: 5,
    isFeatured: false,
    isTrending: false,
    tags: [] as string[]
  })

  // Load categories and authors from database
  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setIsLoadingData(true)
      const [categoriesData, authorsData] = await Promise.all([
        getBlogCategories(),
        getBlogAuthors()
      ])
      setCategories(categoriesData)
      setAuthors(authorsData)
    } catch (error) {
      console.error('Error loading data:', error)
      alert('Error loading categories and authors. Please refresh the page.')
    } finally {
      setIsLoadingData(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // Auto-generate slug from title
    if (field === 'title') {
      const slug = value.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()
      setFormData(prev => ({ ...prev, slug }))
    }
  }

  const addTag = () => {
    if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, currentTag.trim()]
      }))
      setCurrentTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleSave = async (status: 'draft' | 'published') => {
    setIsLoading(true)
    try {
      // Validate required fields
      if (!formData.title || !formData.content || !formData.categoryId || !formData.authorId) {
        alert('Please fill in all required fields: Title, Content, Category, and Author.')
        return
      }

      // Prepare post data for database
      const postData = {
        title: formData.title,
        slug: formData.slug,
        excerpt: formData.excerpt,
        content: formData.content,
        category_id: formData.categoryId,
        author_id: formData.authorId,
        medical_reviewer_id: formData.medicalReviewerId || undefined,
        status: status,
        published_at: status === 'published' ? new Date().toISOString() : undefined,
        meta_title: formData.metaTitle || formData.title,
        meta_description: formData.metaDescription || formData.excerpt,
        reading_time_minutes: formData.readingTimeMinutes || 5,
        is_featured: formData.isFeatured || false,
        is_trending: formData.isTrending || false,
        featured_image_url: formData.featuredImageUrl || undefined,
        featured_image_alt: formData.featuredImageAlt || undefined,
        view_count: 0
      }

      // Create the blog post using the database service
      const newPost = await createBlogPost(postData)
      
      if (!newPost) {
        throw new Error('Failed to create blog post')
      }
      
      // Show success message
      alert(`Post ${status === 'published' ? 'published' : 'saved as draft'} successfully!`)
      
      // Redirect to blog admin
      router.push('/admin/blog/posts')
    } catch (error) {
      console.error('Error saving post:', error)
      alert('Error saving post. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePreview = () => {
    if (!formData.title || !formData.content) {
      alert('Please add a title and content before previewing.')
      return
    }
    
    // Store preview data temporarily
    localStorage.setItem('blog_preview', JSON.stringify(formData))
    
    // Open preview in new tab
    window.open('/blog/preview', '_blank')
  }

  if (isLoadingData) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background via-background to-primary/20 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <div className="text-foreground">Loading categories and authors...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-background to-primary/20 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/admin/blog">
              <Button variant="ghost" size="sm" className="text-foreground hover:bg-card">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Blog
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-foreground">Create New Blog Post</h1>
              <p className="text-foreground/70">Write and publish medical insights content</p>
            </div>
          </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handlePreview} className="border-border text-foreground hover:bg-accent">
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>
          <Button 
            variant="outline" 
            onClick={() => handleSave('draft')}
            disabled={isLoading}
            className="border-border text-foreground hover:bg-accent"
          >
            <Save className="h-4 w-4 mr-2" />
            Save Draft
          </Button>
          <Button 
            onClick={() => handleSave('published')}
            disabled={isLoading || !formData.title || !formData.content || !formData.categoryId || !formData.authorId}
            className="bg-primary hover:bg-primary/90"
          >
            {isLoading ? 'Publishing...' : 'Publish'}
          </Button>
        </div>
      </div>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-foreground">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title" className="text-foreground">Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter post title..."
                  className="mt-1 bg-card border-border text-foreground placeholder:text-foreground/50"
                />
              </div>

              <div>
                <Label htmlFor="slug" className="text-foreground">URL Slug</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => handleInputChange('slug', e.target.value)}
                  placeholder="post-url-slug"
                  className="mt-1 bg-card border-border text-foreground placeholder:text-foreground/50"
                />
              </div>

              <div>
                <Label htmlFor="excerpt" className="text-foreground">Excerpt</Label>
                <Textarea
                  id="excerpt"
                  value={formData.excerpt}
                  onChange={(e) => handleInputChange('excerpt', e.target.value)}
                  placeholder="Brief description of the post..."
                  rows={3}
                  className="mt-1 bg-card border-border text-foreground placeholder:text-foreground/50"
                />
              </div>
            </CardContent>
          </Card>

          {/* Content */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-foreground">Content *</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={formData.content}
                onChange={(e) => handleInputChange('content', e.target.value)}
                placeholder="Write your blog post content here... You can use HTML for formatting."
                rows={20}
                className="min-h-[400px] font-mono text-sm bg-card border-border text-foreground placeholder:text-foreground/50"
              />
              <p className="text-xs text-foreground/60 mt-2">
                You can use HTML tags for formatting. This will be rendered as rich content.
              </p>
            </CardContent>
          </Card>

          {/* SEO Settings */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-foreground">SEO Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="metaTitle" className="text-foreground">Meta Title</Label>
                <Input
                  id="metaTitle"
                  value={formData.metaTitle}
                  onChange={(e) => handleInputChange('metaTitle', e.target.value)}
                  placeholder="SEO title (60 characters max)"
                  maxLength={60}
                  className="mt-1 bg-card border-border text-foreground placeholder:text-foreground/50"
                />
                <p className="text-xs text-foreground/60 mt-1">
                  {formData.metaTitle.length}/60 characters
                </p>
              </div>

              <div>
                <Label htmlFor="metaDescription" className="text-foreground">Meta Description</Label>
                <Textarea
                  id="metaDescription"
                  value={formData.metaDescription}
                  onChange={(e) => handleInputChange('metaDescription', e.target.value)}
                  placeholder="SEO description (160 characters max)"
                  maxLength={160}
                  rows={3}
                  className="mt-1 bg-card border-border text-foreground placeholder:text-foreground/50"
                />
                <p className="text-xs text-foreground/60 mt-1">
                  {formData.metaDescription.length}/160 characters
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Publish Settings */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-foreground">Publish Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="category" className="text-foreground">Category *</Label>
                <Select value={formData.categoryId} onValueChange={(value) => handleInputChange('categoryId', value)}>
                  <SelectTrigger className="mt-1 bg-card border-border text-foreground">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent className="bg-background border-border">
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id} className="text-foreground hover:bg-card">
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="author" className="text-foreground">Author *</Label>
                <Select value={formData.authorId} onValueChange={(value) => handleInputChange('authorId', value)}>
                  <SelectTrigger className="mt-1 bg-card border-border text-foreground">
                    <SelectValue placeholder="Select author" />
                  </SelectTrigger>
                  <SelectContent className="bg-background border-border">
                    {authors.map((author) => (
                      <SelectItem key={author.id} value={author.id} className="text-foreground hover:bg-card">
                        {author.name} {author.medical_credentials ? `- ${author.medical_credentials}` : ''}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="reviewer" className="text-foreground">Medical Reviewer</Label>
                <Select value={formData.medicalReviewerId} onValueChange={(value) => handleInputChange('medicalReviewerId', value)}>
                  <SelectTrigger className="mt-1 bg-card border-border text-foreground">
                    <SelectValue placeholder="Select medical reviewer" />
                  </SelectTrigger>
                  <SelectContent className="bg-background border-border">
                    <SelectItem value="none" className="text-foreground hover:bg-card">No medical reviewer</SelectItem>
                    {authors.filter(author => author.is_medical_reviewer).map((author) => (
                      <SelectItem key={author.id} value={author.id} className="text-foreground hover:bg-card">
                        {author.name} {author.medical_credentials ? `- ${author.medical_credentials}` : ''}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <Separator className="bg-accent" />

              <div className="flex items-center justify-between">
                <Label htmlFor="featured" className="text-foreground">Featured Post</Label>
                <Switch
                  id="featured"
                  checked={formData.isFeatured}
                  onCheckedChange={(checked) => handleInputChange('isFeatured', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="trending" className="text-foreground">Trending Post</Label>
                <Switch
                  id="trending"
                  checked={formData.isTrending}
                  onCheckedChange={(checked) => handleInputChange('isTrending', checked)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Featured Image */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-foreground">Featured Image</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="featuredImageUrl" className="text-foreground">Image URL</Label>
                <Input
                  id="featuredImageUrl"
                  value={formData.featuredImageUrl}
                  onChange={(e) => handleInputChange('featuredImageUrl', e.target.value)}
                  placeholder="https://example.com/image.jpg"
                  className="mt-1 bg-card border-border text-foreground placeholder:text-foreground/50"
                />
              </div>

              <div>
                <Label htmlFor="featuredImageAlt" className="text-foreground">Alt Text</Label>
                <Input
                  id="featuredImageAlt"
                  value={formData.featuredImageAlt}
                  onChange={(e) => handleInputChange('featuredImageAlt', e.target.value)}
                  placeholder="Describe the image for accessibility"
                  className="mt-1 bg-card border-border text-foreground placeholder:text-foreground/50"
                />
              </div>

              <Button variant="outline" className="w-full border-border text-foreground hover:bg-accent">
                <Upload className="h-4 w-4 mr-2" />
                Upload Image
              </Button>
            </CardContent>
          </Card>

          {/* Tags */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-foreground">Tags</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={currentTag}
                  onChange={(e) => setCurrentTag(e.target.value)}
                  placeholder="Add a tag..."
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                  className="bg-card border-border text-foreground placeholder:text-foreground/50"
                />
                <Button type="button" variant="outline" size="sm" onClick={addTag} className="border-border text-foreground hover:bg-accent">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1 bg-primary/20 text-primary border-0">
                      {tag}
                      <X 
                        className="h-3 w-3 cursor-pointer hover:text-red-500" 
                        onClick={() => removeTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Reading Time */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-foreground">Reading Time</CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="readingTime" className="text-foreground">Minutes to read</Label>
                <Input
                  id="readingTime"
                  type="number"
                  min="1"
                  max="60"
                  value={formData.readingTimeMinutes}
                  onChange={(e) => handleInputChange('readingTimeMinutes', parseInt(e.target.value) || 5)}
                  className="mt-1 bg-card border-border text-foreground placeholder:text-foreground/50"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      </div>
    </div>
  )
} 