"use client"

import { useState, useEffect } from 'react'
import { getBlogPosts, deleteBlogPost, updateBlogPost } from '@/lib/blog-service'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  PlusCircle, 
  Search,
  Edit,
  Trash2,
  Eye,
  Calendar,
  Clock,
  Filter,
  MoreVertical
} from 'lucide-react'
import Link from 'next/link'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

const getStatusColor = (status: string) => {
  switch (status) {
    case 'published': return 'bg-green-500/20 text-green-400 border-green-500/30'
    case 'draft': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
    case 'review': return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
    default: return 'bg-background/60/20 text-muted-green border-border/30'
  }
}

export default function BlogPostsPage() {
  const [allPosts, setAllPosts] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadAllPosts()
  }, [])

  const loadAllPosts = async () => {
    try {
      setIsLoading(true)
      console.log('Admin posts page: Starting to load posts...')
      
      // Load all posts from database
      const posts = await getBlogPosts({
        limit: 100, // Get a reasonable number of posts
        offset: 0
      })
      
      console.log('Admin posts page: Received posts:', posts)
      console.log('Admin posts page: Number of posts:', posts.length)
      
      setAllPosts(posts)
    } catch (error) {
      console.error('Error loading posts:', error)
      alert('Error loading posts. Please refresh the page.')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePostAction = async (action: string, postId: string) => {
    try {
      switch (action) {
        case 'Edit':
          window.location.href = `/admin/blog/posts/${postId}`
          break
          
        case 'Delete':
          if (confirm('Are you sure you want to delete this post?')) {
            const success = await deleteBlogPost(postId)
            if (success) {
              alert('Post deleted successfully!')
              loadAllPosts() // Refresh the list
            } else {
              alert('Failed to delete post. Please try again.')
            }
          }
          break
          
        case 'Publish':
          const publishedPost = await updateBlogPost(postId, { status: 'published' })
          if (publishedPost) {
            alert('Post published successfully!')
            loadAllPosts() // Refresh the list
          } else {
            alert('Failed to publish post. Please try again.')
          }
          break
          
        case 'Unpublish':
          const unpublishedPost = await updateBlogPost(postId, { status: 'draft' })
          if (unpublishedPost) {
            alert('Post unpublished successfully!')
            loadAllPosts() // Refresh the list
          } else {
            alert('Failed to unpublish post. Please try again.')
          }
          break
          
        default:
          alert(`${action} action not implemented yet.`)
      }
    } catch (error) {
      console.error('Error performing action:', error)
      alert('Error performing action. Please try again.')
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-foreground">Loading posts...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Manage Posts</h1>
          <p className="text-foreground/70 mt-2">
            Create, edit, and manage all blog posts
          </p>
        </div>
        <Link href="/admin/blog/posts/new">
          <Button className="flex items-center gap-2">
            <PlusCircle className="h-4 w-4" />
            Create New Post
          </Button>
        </Link>
      </div>

      {/* Filters and Search */}
      <Card className="bg-card border-border">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4 items-center">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground/60 h-4 w-4" />
              <Input
                placeholder="Search posts by title, author, or content..."
                className="pl-10 bg-card border-border text-foreground placeholder:text-foreground/50"
              />
            </div>
            <div className="flex gap-2">
              <Button variant="outline" className="flex items-center gap-2 border-border text-foreground hover:bg-accent">
                <Filter className="h-4 w-4" />
                Filter
              </Button>
              <Button variant="outline" className="border-border text-foreground hover:bg-accent">
                Export
              </Button>
            </div>
          </div>

          <div className="flex gap-2 mt-4">
            <Button variant="outline" size="sm" className="border-border text-foreground hover:bg-accent">All Posts</Button>
            <Button variant="outline" size="sm" className="border-border text-foreground hover:bg-accent">Published</Button>
            <Button variant="outline" size="sm" className="border-border text-foreground hover:bg-accent">Drafts</Button>
            <Button variant="outline" size="sm" className="border-border text-foreground hover:bg-accent">Under Review</Button>
            <Button variant="outline" size="sm" className="border-border text-foreground hover:bg-accent">Featured</Button>
          </div>
        </CardContent>
      </Card>

      {/* Posts Table */}
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="text-foreground">All Posts ({allPosts.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {allPosts.map((post) => (
              <div key={post.id} className="border border-border rounded-lg p-4 hover:bg-muted-green/50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-semibold text-lg text-foreground hover:text-primary cursor-pointer">
                        {post.title}
                      </h3>
                      <Badge className={getStatusColor(post.status || 'draft')}>
                        {post.status || 'draft'}
                      </Badge>
                      {post.is_featured && (
                        <Badge className="bg-primary/20 text-primary border-primary/30">Featured</Badge>
                      )}
                    </div>
                    
                    <p className="text-foreground/70 text-sm mb-3 line-clamp-2">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex items-center gap-6 text-sm text-foreground/60">
                      <span className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {post.published_at 
                          ? new Date(post.published_at).toLocaleDateString()
                          : 'Not published'
                        }
                      </span>
                      <span className="flex items-center gap-1">
                        <Eye className="h-4 w-4" />
                        {(post.view_count || 0).toLocaleString()} views
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {post.reading_time_minutes || 5} min read
                      </span>
                      <span>By {post.author?.name || 'Unknown Author'}</span>
                      <span>{post.category?.name || 'Uncategorized'}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    <Link href={`/blog/${post.slug}`}>
                      <Button variant="ghost" size="sm" className="text-foreground/70 hover:text-foreground hover:bg-card">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="text-foreground/70 hover:text-foreground hover:bg-card"
                      onClick={() => handlePostAction('Edit', post.id)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="text-foreground/70 hover:text-foreground hover:bg-card">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-background border-border">
                        <DropdownMenuItem 
                          className="text-foreground hover:bg-card cursor-pointer"
                          onClick={() => handlePostAction('Edit', post.id)}
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Post
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="text-foreground hover:bg-card cursor-pointer"
                          onClick={() => handlePostAction('Duplicate', post.id)}
                        >
                          Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="text-foreground hover:bg-card cursor-pointer"
                          onClick={() => handlePostAction(post.is_featured ? 'Remove from Featured' : 'Mark as Featured', post.id)}
                        >
                          {post.is_featured ? 'Remove from Featured' : 'Mark as Featured'}
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="text-foreground hover:bg-card cursor-pointer"
                          onClick={() => handlePostAction((post.status || 'draft') === 'published' ? 'Unpublish' : 'Publish', post.id)}
                        >
                          {(post.status || 'draft') === 'published' ? 'Unpublish' : 'Publish'}
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="text-red-400 hover:bg-red-500/10 cursor-pointer"
                          onClick={() => handlePostAction('Delete', post.id)}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Pagination */}
          <div className="flex justify-between items-center mt-6">
            <p className="text-sm text-foreground/60">
              Showing 1-{allPosts.length} of {allPosts.length} posts
            </p>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" disabled className="border-border text-foreground/50">
                Previous
              </Button>
              <Button variant="outline" size="sm" disabled className="border-border text-foreground/50">
                Next
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 