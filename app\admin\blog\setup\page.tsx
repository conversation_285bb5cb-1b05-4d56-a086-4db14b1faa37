"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Database, CheckCircle, AlertCircle, Loader2 } from 'lucide-react'
import { useState } from 'react'
import { initializeBlogData } from '@/lib/blog-service'

export default function BlogSetupPage() {
  const [isSetupRunning, setIsSetupRunning] = useState(false)
  const [setupStatus, setSetupStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [errorMessage, setErrorMessage] = useState('')

  const runSetup = async () => {
    setIsSetupRunning(true)
    setSetupStatus('idle')
    setErrorMessage('')

    try {
      console.log('Initializing blog database...')
      const result = await initializeBlogData()
      
      if (result) {
        setSetupStatus('success')
        console.log('Blog database initialized successfully!')
      } else {
        throw new <PERSON><PERSON>r('Failed to initialize blog database')
      }
    } catch (error) {
      console.error('Setup failed:', error)
      setSetupStatus('error')
      setErrorMessage(error instanceof Error ? error.message : 'Unknown error occurred')
    } finally {
      setIsSetupRunning(false)
    }
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-foreground mb-4">Blog Database Setup</h1>
        <p className="text-foreground/70">
          Initialize your blog database with categories and authors
        </p>
      </div>

      {/* Setup Card */}
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="text-foreground flex items-center gap-2">
            <Database className="h-5 w-5" />
            Database Initialization
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-foreground/80">
            <p className="mb-4">
              This will set up your blog database with:
            </p>
            <ul className="list-disc list-inside space-y-2 text-sm">
              <li>4 default blog categories</li>
              <li>Editorial Team author</li>
              <li>Required database structure</li>
            </ul>
          </div>

          {/* Status Display */}
          {setupStatus === 'success' && (
            <div className="flex items-center gap-2 text-green-400 bg-green-500/20 p-3 rounded-lg">
              <CheckCircle className="h-5 w-5" />
              <span>Database setup completed successfully!</span>
            </div>
          )}

          {setupStatus === 'error' && (
            <div className="flex items-start gap-2 text-red-400 bg-red-500/20 p-3 rounded-lg">
              <AlertCircle className="h-5 w-5 mt-0.5" />
              <div>
                <div className="font-medium">Setup failed</div>
                <div className="text-sm opacity-80">{errorMessage}</div>
              </div>
            </div>
          )}

          {/* Setup Button */}
          <div className="flex gap-4">
            <Button
              onClick={runSetup}
              disabled={isSetupRunning}
              className="flex items-center gap-2"
            >
              {isSetupRunning ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Setting up...
                </>
              ) : (
                <>
                  <Database className="h-4 w-4" />
                  Initialize Database
                </>
              )}
            </Button>

            {setupStatus === 'success' && (
              <Button
                variant="outline"
                onClick={() => window.location.href = '/admin/blog'}
                className="border-border text-foreground hover:bg-card"
              >
                Go to Blog Dashboard
              </Button>
            )}
          </div>

          {/* Help Text */}
          <div className="text-sm text-foreground/60 bg-blue-500/10 p-3 rounded-lg">
            <strong>Note:</strong> If you get errors, make sure your Supabase database is properly configured 
            and you have the correct environment variables set in your <code>.env.local</code> file.
          </div>
        </CardContent>
      </Card>

      {/* Manual Setup Instructions */}
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="text-foreground text-lg">Manual Setup (Alternative)</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-foreground/80 text-sm">
          <p>If the automatic setup fails, you can manually run the SQL in your Supabase dashboard:</p>
          
          <div className="bg-background/30 p-4 rounded-lg font-mono text-xs overflow-x-auto">
            <pre>{`-- Run this in your Supabase SQL Editor

-- 1. Create blog_authors table
CREATE TABLE IF NOT EXISTS public.blog_authors (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    name character varying(255) NOT NULL,
    slug character varying(255) NOT NULL UNIQUE,
    email character varying(255) UNIQUE,
    bio text,
    medical_credentials text,
    specialties text[],
    profile_image_url text,
    social_links jsonb,
    is_medical_reviewer boolean DEFAULT false,
    is_active boolean DEFAULT true,
    user_id uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- 2. Create blog_categories table  
CREATE TABLE IF NOT EXISTS public.blog_categories (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    name character varying(100) NOT NULL UNIQUE,
    slug character varying(100) NOT NULL UNIQUE,
    description text,
    color character varying(7) DEFAULT '#3B82F6',
    icon character varying(50),
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- 3. Create blog_posts table
CREATE TABLE IF NOT EXISTS public.blog_posts (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    title character varying(500) NOT NULL,
    slug character varying(500) NOT NULL UNIQUE,
    excerpt text,
    content text NOT NULL,
    featured_image_url text,
    featured_image_alt text,
    category_id uuid REFERENCES public.blog_categories(id),
    author_id uuid REFERENCES public.blog_authors(id),
    medical_reviewer_id uuid REFERENCES public.blog_authors(id),
    status character varying(20) DEFAULT 'draft',
    published_at timestamp with time zone,
    meta_title character varying(60),
    meta_description character varying(160),
    reading_time_minutes integer,
    view_count integer DEFAULT 0,
    is_featured boolean DEFAULT false,
    is_trending boolean DEFAULT false,
    related_doctor_ids integer[],
    related_team_ids integer[],
    structured_data jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);`}</pre>
          </div>
          
          <p>After running the SQL, click "Initialize Database" above to add the initial data.</p>
        </CardContent>
      </Card>
    </div>
  )
} 