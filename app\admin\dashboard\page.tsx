"use client"

import { useState, useEffect } from "react"
import { 
  <PERSON>,
  UserCog,
  Star,
  FileText,
  BarChart,
  <PERSON><PERSON><PERSON>,
  AlertCircle
} from "lucide-react"

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { supabase } from "@/lib/supabase-client"

export default function AdminDashboard() {
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    userCount: 0,
    doctorCount: 0,
    reviewCount: 0
  })
  const [error, setError] = useState<string | null>(null)
  
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true)
        setError(null)
        
        // Debug log to verify we're using the correct client
        console.log("Fetching dashboard stats with Supabase client")
        
        // Simple count of users table rows
        const { count: userCount, error: userError } = await supabase
          .from('users')
          .select('*', { count: 'exact', head: true })
        
        if (userError) {
          console.error("Error fetching users:", userError)
          throw userError
        }
        
        // Simple count of doctors table rows
        const { count: doctorCount, error: doctorError } = await supabase
          .from('doctors')
          .select('*', { count: 'exact', head: true })
        
        if (doctorError) {
          console.error("Error fetching doctors:", doctorError)
          throw doctorError
        }
        
        // Simple count of reviews table rows
        const { count: reviewCount, error: reviewError } = await supabase
          .from('reviews')
          .select('*', { count: 'exact', head: true })
        
        if (reviewError) {
          console.error("Error fetching reviews:", reviewError)
          throw reviewError
        }
        
        console.log("Database counts:", { userCount, doctorCount, reviewCount })
        
        setStats({
          userCount: userCount || 0,
          doctorCount: doctorCount || 0,
          reviewCount: reviewCount || 0
        })
      } catch (error) {
        console.error("Error fetching dashboard stats:", error)
        setError("Failed to fetch statistics. Check browser console for details.")
      } finally {
        setLoading(false)
      }
    }
    
    fetchStats()
  }, [])
  
  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Dashboard Overview</h1>
      </div>
      
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}
      
      {/* Key Metrics */}
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Patients</CardTitle>
            <div className="h-8 w-8 rounded-full bg-primary/10 p-1.5 text-primary">
              <Users className="h-full w-full" />
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-2xl font-bold flex items-center">
                <div className="animate-pulse h-8 w-20 bg-green-200 rounded"></div>
              </div>
            ) : (
              <>
                <div className="text-2xl font-bold">{stats.userCount}</div>
                <p className="text-xs text-muted-green">Registered patients</p>
              </>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Doctors</CardTitle>
            <div className="h-8 w-8 rounded-full bg-primary/10 p-1.5 text-primary">
              <UserCog className="h-full w-full" />
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-2xl font-bold flex items-center">
                <div className="animate-pulse h-8 w-20 bg-green-200 rounded"></div>
              </div>
            ) : (
              <>
                <div className="text-2xl font-bold">{stats.doctorCount}</div>
                <p className="text-xs text-muted-green">Registered medical professionals</p>
              </>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Reviews</CardTitle>
            <div className="h-8 w-8 rounded-full bg-primary/10 p-1.5 text-primary">
              <Star className="h-full w-full" />
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-2xl font-bold flex items-center">
                <div className="animate-pulse h-8 w-20 bg-green-200 rounded"></div>
              </div>
            ) : (
              <>
                <div className="text-2xl font-bold">{stats.reviewCount}</div>
                <p className="text-xs text-muted-green">Patient reviews submitted</p>
              </>
            )}
          </CardContent>
        </Card>
      </div>
      
      {/* Admin Actions */}
      <div className="grid gap-4 grid-cols-1 md:grid-cols-3">
        <Button variant="outline" className="h-32 flex flex-col gap-2" onClick={() => window.location.href = '/admin/users'}>
          <Users className="h-8 w-8" />
          <span>Manage Patients</span>
        </Button>
        
        <Button variant="outline" className="h-32 flex flex-col gap-2" onClick={() => window.location.href = '/admin/doctors'}>
          <UserCog className="h-8 w-8" />
          <span>Manage Doctors</span>
        </Button>
        
        <Button variant="outline" className="h-32 flex flex-col gap-2" onClick={() => window.location.href = '/admin/reviews'}>
          <FileText className="h-8 w-8" />
          <span>Manage Reviews</span>
        </Button>
        
        <Button variant="outline" className="h-32 flex flex-col gap-2" onClick={() => window.location.href = '/admin/ads'}>
          <BarChart className="h-8 w-8" />
          <span>Manage Ads</span>
        </Button>
        
        <Button variant="outline" className="h-32 flex flex-col gap-2" onClick={() => window.location.href = '/admin/settings'}>
          <Settings className="h-8 w-8" />
          <span>Site Settings</span>
        </Button>
      </div>
    </div>
  )
} 