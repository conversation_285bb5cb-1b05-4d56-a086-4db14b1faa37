"use client"

import { useState, useEffect } from "react"
import { 
  Search, 
  Plus, 
  Edit, 
  UserCog,
  EyeIcon,
  Star,
  MapPin,
  MoreHorizontal,
  Building,
  GraduationCap,
  AlertCircle,
  X,
  Eye,
  Pencil,
  Trophy,
  Trash2
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { supabase } from "@/lib/supabase-client"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

type Doctor = {
  doctor_id: number
  fullname?: string
  facility?: string
  medical_title?: string
  specialty?: string
  subspecialty?: string
  educational_background?: string
  board_certifications?: string
  experience?: number
  publications?: string
  awards_recognitions?: string
  phone_number?: number
  email?: string
  languages_spoken?: string
  professional_affiliations?: string
  procedures_performed?: string
  treatment_services_expertise?: string
  hospital_id?: number
  image_path?: string
  wins?: number
  losses?: number
  form?: string
  rating?: number
  review_count?: number
  country_id?: number
  specialty_id?: number
  last_updated?: string
  auth_id?: string
  draws?: number
}

type Country = {
  country_id: number
  country_name: string
}

type Hospital = {
  hospital_id: number
  hospital_name: string
  country_id?: number
  city?: string
}

type Specialty = {
  specialty_id: number
  specialty_name: string
  description?: string
}

export default function DoctorsPage() {
  const [doctors, setDoctors] = useState<Doctor[]>([])
  const [filteredDoctors, setFilteredDoctors] = useState<Doctor[]>([])
  const [countries, setCountries] = useState<Country[]>([])
  const [hospitals, setHospitals] = useState<Hospital[]>([])
  const [specialties, setSpecialties] = useState<Specialty[]>([])
  
  const [searchQuery, setSearchQuery] = useState("")
  const [countryFilter, setCountryFilter] = useState("all")
  const [hospitalFilter, setHospitalFilter] = useState("all")
  const [specialtyFilter, setSpecialtyFilter] = useState("all")
  const [sortBy, setSortBy] = useState("rating-desc")
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [viewMode, setViewMode] = useState<"table" | "grid">("table")
  
  const itemsPerPage = viewMode === "table" ? 10 : 12
  
  // Add state for view/edit doctor profile dialogs
  const [showViewDialog, setShowViewDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [selectedDoctor, setSelectedDoctor] = useState<Doctor | null>(null)
  const [formValues, setFormValues] = useState<Partial<Doctor>>({})
  
  // Add state for new doctor dialog
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [newDoctorData, setNewDoctorData] = useState<Partial<Doctor>>({
    fullname: '',
    email: '',
    medical_title: '',
    facility: '',
    specialty: '',
    subspecialty: '',
    specialty_id: undefined,
    country_id: undefined,
    hospital_id: undefined,
    experience: undefined,
    languages_spoken: '',
    educational_background: '',
    board_certifications: '',
    phone_number: undefined,
    publications: '',
    awards_recognitions: '',
    professional_affiliations: '',
    procedures_performed: '',
    treatment_services_expertise: ''
  })
  
  // Add missing state for delete dialog
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        console.log("Fetching doctors data with Supabase client")
        
        // Fetch doctors
        const { data: doctorsData, error: doctorsError } = await supabase
          .from('doctors')
          .select('*')
          .order('rating', { ascending: false })
        
        if (doctorsError) {
          console.error("Error fetching doctors:", doctorsError)
          throw doctorsError
        }
        
        console.log(`Fetched ${doctorsData ? doctorsData.length : 0} doctors`)
        
        // Fetch countries
        const { data: countriesData, error: countriesError } = await supabase
          .from('countries')
          .select('*')
          .order('country_name', { ascending: true })
        
        if (countriesError) {
          console.error("Error fetching countries:", countriesError)
          // Continue with other fetches even if this fails
        }
        
        console.log(`Fetched ${countriesData ? countriesData.length : 0} countries`)
        
        // Fetch hospitals
        const { data: hospitalsData, error: hospitalsError } = await supabase
          .from('hospitals')
          .select('*')
          .order('hospital_name', { ascending: true })
        
        if (hospitalsError) {
          console.error("Error fetching hospitals:", hospitalsError)
          // Continue with other fetches even if this fails
        }
        
        console.log(`Fetched ${hospitalsData ? hospitalsData.length : 0} hospitals`)
        
        // Fetch specialties
        const { data: specialtiesData, error: specialtiesError } = await supabase
          .from('specialties')
          .select('*')
          .order('specialty_name', { ascending: true })
        
        if (specialtiesError) {
          console.error("Error fetching specialties:", specialtiesError)
          // Continue with other fetches even if this fails
        }
        
        console.log(`Fetched ${specialtiesData ? specialtiesData.length : 0} specialties`)
        
        // Update state with fetched data
        setDoctors(doctorsData || [])
        setFilteredDoctors(doctorsData || [])
        setCountries(countriesData || [])
        setHospitals(hospitalsData || [])
        setSpecialties(specialtiesData || [])
      } catch (error) {
        console.error("Error loading data:", error)
        setError("Failed to fetch doctor data. Check browser console for details.")
      } finally {
        setLoading(false)
      }
    }
    
    fetchData()
  }, [])
  
  useEffect(() => {
    let result = [...doctors]
    
    console.log("Filtering doctors:", {
      total: doctors.length,
      searchQuery,
      countryFilter,
      hospitalFilter,
      specialtyFilter,
      sortBy
    })
    
    // Apply search query filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(doctor => 
        doctor.fullname?.toLowerCase().includes(query) ||
        doctor.email?.toLowerCase().includes(query) ||
        doctor.specialty?.toLowerCase().includes(query) ||
        doctor.subspecialty?.toLowerCase().includes(query) ||
        doctor.facility?.toLowerCase().includes(query) ||
        doctor.medical_title?.toLowerCase().includes(query)
      )
    }
    
    // Apply country filter
    if (countryFilter !== "all") {
      const countryId = parseInt(countryFilter)
      console.log(`Filtering by country_id ${countryId}`)
      result = result.filter(doctor => doctor.country_id === countryId)
    }
    
    // Apply hospital filter
    if (hospitalFilter !== "all") {
      const hospitalId = parseInt(hospitalFilter)
      console.log(`Filtering by hospital_id ${hospitalId}`)
      result = result.filter(doctor => doctor.hospital_id === hospitalId)
    }
    
    // Apply specialty filter with improved logging and more flexible matching
    if (specialtyFilter !== "all") {
      const specId = parseInt(specialtyFilter)
      console.log(`Filtering by specialty_id ${specId}, before filter: ${result.length} doctors`)
      
      // Deep check of the specialty_id values for debugging
      const specialtyValues = result.map(d => ({ 
        id: d.doctor_id, 
        name: d.fullname, 
        spec_id: d.specialty_id,
        spec_name: d.specialty
      }))
      
      // Log the specialty values to help debug
      console.log(`Doctors with specialty_id matching ${specId}:`, 
        specialtyValues.filter(d => d.spec_id === specId)
      )
      
      // Find the specialty name for the selected filter
      const selectedSpecialtyName = specialties.find(s => s.specialty_id === specId)?.specialty_name || ""
      console.log(`Selected specialty name: ${selectedSpecialtyName}`)
      
      // Use more flexible filtering - match by either ID or name
      result = result.filter(doctor => 
        doctor.specialty_id === specId || 
        (doctor.specialty && selectedSpecialtyName && 
         doctor.specialty.toLowerCase().includes(selectedSpecialtyName.toLowerCase()))
      )
      
      console.log(`After specialty filter: ${result.length} doctors`)
    }
    
    // Apply sorting
    result = sortDoctors(result, sortBy)
    
    setFilteredDoctors(result)
    setCurrentPage(1) // Reset to first page when filters change
  }, [searchQuery, countryFilter, hospitalFilter, specialtyFilter, sortBy, doctors])
  
  const sortDoctors = (doctorsList: Doctor[], sortOption: string) => {
    const doctorsCopy = [...doctorsList]
    
    switch (sortOption) {
      case "name-asc":
        return doctorsCopy.sort((a, b) => {
          return (a.fullname || "").localeCompare(b.fullname || "")
        })
      case "name-desc":
        return doctorsCopy.sort((a, b) => {
          return (b.fullname || "").localeCompare(a.fullname || "")
        })
      case "rating-high":
        return doctorsCopy.sort((a, b) => {
          const aRating = a.rating || 0
          const bRating = b.rating || 0
          return bRating - aRating
        })
      case "rating-low":
        return doctorsCopy.sort((a, b) => {
          const aRating = a.rating || 0
          const bRating = b.rating || 0
          return aRating - bRating
        })
      case "experience-high":
        return doctorsCopy.sort((a, b) => {
          const aExp = a.experience || 0
          const bExp = b.experience || 0
          return bExp - aExp
        })
      case "experience-low":
        return doctorsCopy.sort((a, b) => {
          const aExp = a.experience || 0
          const bExp = b.experience || 0
          return aExp - bExp
        })
      default:
        return doctorsCopy
    }
  }
  
  // Get current page data
  const indexOfLastItem = currentPage * itemsPerPage
  const indexOfFirstItem = indexOfLastItem - itemsPerPage
  const currentDoctors = filteredDoctors.slice(indexOfFirstItem, indexOfLastItem)
  const totalPages = Math.ceil(filteredDoctors.length / itemsPerPage)
  
  // Get country name by ID
  const getCountryName = (countryId?: number) => {
    if (!countryId) return "Unknown";
    const country = countries.find(c => c.country_id === countryId);
    return country ? country.country_name : "Unknown";
  }
  
  // Get hospital name by ID
  const getHospitalName = (hospitalId?: number) => {
    if (!hospitalId) return "Unknown";
    const hospital = hospitals.find(h => h.hospital_id === hospitalId);
    return hospital ? hospital.hospital_name : "Unknown";
  }
  
  // Get specialty name by ID
  const getSpecialtyName = (specialtyId?: number) => {
    if (!specialtyId) return "Unknown";
    const specialty = specialties.find(s => s.specialty_id === specialtyId);
    return specialty ? specialty.specialty_name : "Unknown";
  }
  
  // Add function to handle adding a new doctor
  const handleAddDoctor = async () => {
    try {
      console.log("Adding new doctor with data:", newDoctorData)
      
      // Validate required fields
      if (!newDoctorData.fullname) {
        throw new Error("Doctor name is required")
      }
      
      // Insert the new doctor
      const { data, error } = await supabase
        .from('doctors')
        .insert([newDoctorData])
        .select()
      
      if (error) {
        console.error("Error adding doctor:", error)
        throw new Error(`Failed to add doctor: ${error.message}`)
      }
      
      console.log("Doctor added successfully:", data)
      
      // Update local state to include the new doctor
      if (data && data.length > 0) {
        setDoctors([...doctors, data[0]])
      }
      
      // Close dialog and reset form
      setShowAddDialog(false)
      setNewDoctorData({
        fullname: '',
        email: '',
        medical_title: '',
        facility: '',
        specialty: '',
        subspecialty: '',
        specialty_id: undefined,
        country_id: undefined,
        hospital_id: undefined,
        experience: undefined,
        languages_spoken: '',
        educational_background: '',
        board_certifications: '',
        phone_number: undefined,
        publications: '',
        awards_recognitions: '',
        professional_affiliations: '',
        procedures_performed: '',
        treatment_services_expertise: ''
      })
      
      // Clear any previous errors
      setError(null)
    } catch (error: any) {
      console.error("Error adding doctor:", error)
      setError(`Failed to add doctor: ${error.message || "Unknown error"}`)
    }
  }
  
  // Handle deleting a doctor
  const handleDeleteDoctor = async (doctorId: number) => {
    if (!confirm("Are you sure you want to delete this doctor? This action cannot be undone.")) {
      return;
    }
    
    try {
      console.log(`Deleting doctor with ID ${doctorId}`)
      
      // First check if the doctor exists
      const { data: existingDoctor, error: checkError } = await supabase
        .from('doctors')
        .select('doctor_id')
        .eq('doctor_id', doctorId)
        .single()
      
      if (checkError) {
        console.error("Error finding doctor:", checkError)
        throw new Error(`Doctor with ID ${doctorId} not found: ${checkError.message}`)
      }
      
      if (!existingDoctor) {
        throw new Error(`Doctor with ID ${doctorId} not found`)
      }
      
      // Proceed with deletion
      const { error } = await supabase
        .from('doctors')
        .delete()
        .eq('doctor_id', doctorId)
      
      if (error) {
        console.error("Error deleting doctor:", error)
        throw new Error(`Failed to delete doctor: ${error.message}`)
      }
      
      console.log(`Doctor ${doctorId} deleted successfully`)
      
      // Update local state to remove the doctor
      setDoctors(doctors.filter(doctor => doctor.doctor_id !== doctorId))
      setFilteredDoctors(filteredDoctors.filter(doctor => doctor.doctor_id !== doctorId))
      
      // Clear any previous errors
      setError(null)
    } catch (error: any) {
      console.error("Error deleting doctor:", error)
      setError(`Failed to delete doctor: ${error.message || "Unknown error"}`)
    }
  }
  
  // Add function to handle editing a doctor
  const handleEditDoctor = async () => {
    if (!selectedDoctor) return
    
    try {
      console.log(`Updating doctor with ID ${selectedDoctor.doctor_id}`)
      
      // First check if the doctor exists
      const { data: existingDoctor, error: checkError } = await supabase
        .from('doctors')
        .select('doctor_id')
        .eq('doctor_id', selectedDoctor.doctor_id)
        .single()
      
      if (checkError) {
        console.error("Error finding doctor:", checkError)
        throw new Error(`Doctor with ID ${selectedDoctor.doctor_id} not found: ${checkError.message}`)
      }
      
      if (!existingDoctor) {
        throw new Error(`Doctor with ID ${selectedDoctor.doctor_id} not found`)
      }
      
      // Prepare update data from form values
      const updateData = { ...formValues }
      
      console.log("Updating doctor with data:", updateData)
      
      // Perform the update
      const { error } = await supabase
        .from('doctors')
        .update(updateData)
        .eq('doctor_id', selectedDoctor.doctor_id)
      
      if (error) {
        console.error("Error updating doctor:", error)
        throw new Error(`Failed to update doctor: ${error.message}`)
      }
      
      console.log(`Doctor ${selectedDoctor.doctor_id} updated successfully`)
      
      // Update the doctor in the state
      setDoctors(doctors.map(d => 
        d.doctor_id === selectedDoctor.doctor_id ? { ...d, ...updateData } : d
      ))
      
      // Close dialog and reset form
      setShowEditDialog(false)
      setSelectedDoctor(null)
      setFormValues({})
      
      // Clear any previous errors
      setError(null)
    } catch (error: any) {
      console.error("Error updating doctor:", error)
      setError(`Failed to update doctor: ${error.message || "Unknown error"}`)
    }
  }
  
  // Improve the grid view of doctors
  const renderDoctorCard = (doctor: Doctor) => {
    const rating = doctor.rating ? doctor.rating.toFixed(1) : "N/A";
    const record = `${doctor.wins || 0}W ${doctor.losses || 0}L ${doctor.draws || 0}D`;
    
    return (
      <div key={doctor.doctor_id} className="bg-card rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
        <div className="p-5 flex flex-col h-full">
          <div className="flex items-start gap-4 mb-4">
            <div className="w-20 h-20 flex-shrink-0 rounded-full overflow-hidden bg-primary/10 flex items-center justify-center">
              {doctor.image_path ? (
                <img 
                  src={doctor.image_path} 
                  alt={doctor.fullname} 
                  className="w-full h-full object-cover"
                />
              ) : (
                <UserCog className="h-8 w-8 text-primary" />
              )}
            </div>
            
            <div className="flex-1">
              <h3 className="font-bold text-lg line-clamp-2">{doctor.fullname}</h3>
              <p className="text-sm text-muted-green line-clamp-1">{doctor.medical_title}</p>
              
              <div className="mt-2 flex items-center gap-2">
                <Badge variant="outline" className="bg-primary/10 text-primary hover:bg-primary/20 border-0">
                  {getSpecialtyName(doctor.specialty_id) || doctor.specialty || "Unknown"}
                </Badge>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-2 mb-4 text-sm">
            <div className="flex items-center gap-1.5">
              <Building className="h-4 w-4 text-muted-green" />
              <span className="line-clamp-1">{getHospitalName(doctor.hospital_id) || doctor.facility || "Unknown"}</span>
            </div>
            
            <div className="flex items-center gap-1.5">
              <MapPin className="h-4 w-4 text-muted-green" />
              <span className="line-clamp-1">{getCountryName(doctor.country_id) || "Unknown"}</span>
            </div>
            
            <div className="flex items-center gap-1.5">
              <Star className={`h-4 w-4 ${doctor.rating ? "fill-amber-400 text-amber-400" : "text-muted-green"}`} />
              <span>{rating}</span>
              <span className="text-xs text-muted-green">({doctor.review_count || 0})</span>
            </div>
            
            <div className="flex items-center gap-1.5">
              <Trophy className="h-4 w-4 text-muted-green" />
              <span>{record}</span>
            </div>
          </div>
          
          <div className="mt-auto pt-2 flex flex-col gap-2">
            <Button 
              variant="default" 
              size="sm" 
              className="w-full justify-center"
              onClick={() => {
                setSelectedDoctor(doctor)
                setShowViewDialog(true)
              }}
            >
              <Eye className="mr-2 h-4 w-4" />
              View Profile
            </Button>
            
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full justify-center"
              onClick={() => {
                setSelectedDoctor(doctor)
                setFormValues({
                  fullname: doctor.fullname || '',
                  email: doctor.email || '',
                  medical_title: doctor.medical_title || '',
                  facility: doctor.facility || '',
                  specialty: doctor.specialty || '',
                  subspecialty: doctor.subspecialty || '',
                  specialty_id: doctor.specialty_id,
                  country_id: doctor.country_id,
                  hospital_id: doctor.hospital_id,
                  experience: doctor.experience,
                  languages_spoken: doctor.languages_spoken || '',
                  educational_background: doctor.educational_background || '',
                  board_certifications: doctor.board_certifications || '',
                  phone_number: doctor.phone_number,
                  publications: doctor.publications || '',
                  awards_recognitions: doctor.awards_recognitions || '',
                  professional_affiliations: doctor.professional_affiliations || '',
                  procedures_performed: doctor.procedures_performed || '',
                  treatment_services_expertise: doctor.treatment_services_expertise || ''
                })
                setShowEditDialog(true)
              }}
            >
              <Pencil className="mr-2 h-4 w-4" />
              Edit Details
            </Button>
          </div>
        </div>
      </div>
    )
  }
  
  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin h-12 w-12 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Doctors Management</h1>
        <div className="flex items-center gap-4">
          {/* View toggle buttons */}
          <div className="flex items-center">
            <Button
              variant={viewMode === "table" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("table")}
              className="rounded-r-none"
            >
              Table
            </Button>
            <Button
              variant={viewMode === "grid" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("grid")}
              className="rounded-l-none"
            >
              Grid
            </Button>
          </div>
          <Button onClick={() => setShowAddDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Doctor
          </Button>
        </div>
      </div>
      
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}
      
      {/* Filters & Search */}
      <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-green" />
            <Input
              placeholder="Search doctors by name, specialty, title..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <div className="flex flex-wrap gap-2">
          <Select value={countryFilter} onValueChange={setCountryFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Country" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Countries</SelectItem>
              {countries.map((country) => (
                <SelectItem key={country.country_id} value={country.country_id.toString()}>
                  {country.country_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={hospitalFilter} onValueChange={setHospitalFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Hospital" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Hospitals</SelectItem>
              {hospitals.map((hospital) => (
                <SelectItem key={hospital.hospital_id} value={hospital.hospital_id.toString()}>
                  {hospital.hospital_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={specialtyFilter} onValueChange={setSpecialtyFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Specialty" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Specialties</SelectItem>
              {specialties.map((specialty) => (
                <SelectItem key={specialty.specialty_id} value={specialty.specialty_id.toString()}>
                  {specialty.specialty_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name-asc">Name (A-Z)</SelectItem>
              <SelectItem value="name-desc">Name (Z-A)</SelectItem>
              <SelectItem value="rating-high">Highest Rating</SelectItem>
              <SelectItem value="rating-low">Lowest Rating</SelectItem>
              <SelectItem value="experience-high">Most Experience</SelectItem>
              <SelectItem value="experience-low">Least Experience</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Doctors List */}
      {viewMode === "table" ? (
        <>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Doctor</TableHead>
                <TableHead>Specialty</TableHead>
                <TableHead>Hospital</TableHead>
                <TableHead>Country</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Record</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentDoctors.length > 0 ? (
                currentDoctors.map((doctor) => (
                  <TableRow key={doctor.doctor_id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="w-9 h-9 rounded-full overflow-hidden bg-primary/10 flex items-center justify-center">
                          {doctor.image_path ? (
                            <img 
                              src={doctor.image_path} 
                              alt={doctor.fullname} 
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <UserCog className="h-4 w-4 text-primary" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{doctor.fullname}</p>
                          <p className="text-xs text-muted-green">{doctor.medical_title}</p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className="bg-primary/10 text-primary hover:bg-primary/20 border-0">
                        {getSpecialtyName(doctor.specialty_id) || doctor.specialty || "Unknown Specialty"}
                      </Badge>
                      {doctor.subspecialty && (
                        <div className="text-xs text-muted-green mt-1">{doctor.subspecialty}</div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1.5">
                        <Building className="h-4 w-4 text-muted-green" />
                        <span>{getHospitalName(doctor.hospital_id) || doctor.facility || "Unknown Facility"}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1.5">
                        <MapPin className="h-4 w-4 text-muted-green" />
                        <span>{getCountryName(doctor.country_id)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1.5">
                        <Star className={`h-4 w-4 ${doctor.rating ? "fill-amber-400 text-amber-400" : "text-muted-green"}`} />
                        <span>{doctor.rating ? doctor.rating.toFixed(1) : "N/A"}</span>
                        <span className="text-xs text-muted-green">({doctor.review_count || 0})</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1.5">
                        <Trophy className="h-4 w-4 text-muted-green" />
                        <span className="text-green-600 font-medium">{doctor.wins || 0}W</span>
                        <span className="text-red-600 font-medium">{doctor.losses || 0}L</span>
                        <span className="text-muted-green font-medium">{doctor.draws || 0}D</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => {
                            setSelectedDoctor(doctor)
                            setShowViewDialog(true)
                          }}>
                            <Eye className="mr-2 h-4 w-4" />
                            <span>View Profile</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => {
                            setSelectedDoctor(doctor)
                            setFormValues({
                              fullname: doctor.fullname || '',
                              email: doctor.email || '',
                              medical_title: doctor.medical_title || '',
                              facility: doctor.facility || '',
                              specialty: doctor.specialty || '',
                              subspecialty: doctor.subspecialty || '',
                              specialty_id: doctor.specialty_id,
                              country_id: doctor.country_id,
                              hospital_id: doctor.hospital_id,
                              experience: doctor.experience,
                              languages_spoken: doctor.languages_spoken || '',
                              educational_background: doctor.educational_background || '',
                              board_certifications: doctor.board_certifications || '',
                              phone_number: doctor.phone_number,
                              publications: doctor.publications || '',
                              awards_recognitions: doctor.awards_recognitions || '',
                              professional_affiliations: doctor.professional_affiliations || '',
                              procedures_performed: doctor.procedures_performed || '',
                              treatment_services_expertise: doctor.treatment_services_expertise || ''
                            })
                            setShowEditDialog(true)
                          }}>
                            <Pencil className="mr-2 h-4 w-4" />
                            <span>Edit Details</span>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => {
                              setSelectedDoctor(doctor)
                              setShowDeleteDialog(true)
                            }}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            <span>Delete</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="h-32 text-center">
                    No doctors found matching the criteria
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {currentDoctors.length > 0 ? (
            currentDoctors.map((doctor) => (
              <div key={doctor.doctor_id}>
                {renderDoctorCard(doctor)}
              </div>
            ))
          ) : (
            <div className="col-span-full h-32 flex items-center justify-center text-muted-green">
              No doctors found matching the criteria
            </div>
          )}
        </div>
      )}
      
      {/* Pagination */}
      {filteredDoctors.length > 0 && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious 
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  setCurrentPage(prev => Math.max(prev - 1, 1));
                }}
                size="default"
                aria-disabled={currentPage === 1}
                tabIndex={currentPage === 1 ? -1 : 0}
                className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
              />
            </PaginationItem>
            
            {[...Array(totalPages)].map((_, i) => {
              const page = i + 1;
              if (
                page === 1 || 
                page === totalPages || 
                (page >= currentPage - 1 && page <= currentPage + 1)
              ) {
                return (
                  <PaginationItem key={page}>
                    <PaginationLink
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        setCurrentPage(page);
                      }}
                      size="default"
                      isActive={page === currentPage}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                );
              }
              
              if (page === 2 && currentPage > 3) {
                return <PaginationItem key="ellipsis-start">...</PaginationItem>;
              }
              
              if (page === totalPages - 1 && currentPage < totalPages - 2) {
                return <PaginationItem key="ellipsis-end">...</PaginationItem>;
              }
              
              return null;
            })}
            
            <PaginationItem>
              <PaginationNext 
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  setCurrentPage(prev => Math.min(prev + 1, totalPages));
                }}
                size="default"
                aria-disabled={currentPage === totalPages}
                tabIndex={currentPage === totalPages ? -1 : 0}
                className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
      
      {/* Add the View Doctor Dialog */}
      <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl">Doctor Profile</DialogTitle>
          </DialogHeader>
          
          {selectedDoctor && (
            <div className="space-y-6">
              <div className="flex flex-col md:flex-row gap-6">
                <div className="w-32 h-32 relative rounded-lg overflow-hidden bg-green-100 flex-shrink-0">
                  {selectedDoctor.image_path ? (
                    <img 
                      src={selectedDoctor.image_path} 
                      alt={selectedDoctor.fullname} 
                      className="object-cover w-full h-full"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-primary/10">
                      <UserCog className="h-12 w-12 text-primary" />
                    </div>
                  )}
                </div>
                
                <div className="flex-1">
                  <h2 className="text-2xl font-bold">{selectedDoctor.fullname}</h2>
                  <p className="text-muted-green">{selectedDoctor.medical_title}</p>
                  
                  <div className="mt-2 flex flex-wrap gap-2 items-center">
                    <Badge className="bg-primary/10 text-primary hover:bg-primary/20 border-0">
                      {getSpecialtyName(selectedDoctor.specialty_id) || selectedDoctor.specialty || "Unknown Specialty"}
                    </Badge>
                    
                    {selectedDoctor.subspecialty && (
                      <Badge variant="outline">
                        {selectedDoctor.subspecialty}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-2">
                    <div className="flex items-center gap-2 text-sm">
                      <Building className="h-4 w-4 text-muted-green" />
                      <span>{getHospitalName(selectedDoctor.hospital_id) || selectedDoctor.facility || "Unknown Facility"}</span>
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm">
                      <MapPin className="h-4 w-4 text-muted-green" />
                      <span>{getCountryName(selectedDoctor.country_id)}</span>
                    </div>
                    
                    {selectedDoctor.experience !== undefined && (
                      <div className="flex items-center gap-2 text-sm">
                        <GraduationCap className="h-4 w-4 text-muted-green" />
                        <span>{selectedDoctor.experience} years experience</span>
                      </div>
                    )}
                    
                    {selectedDoctor.languages_spoken && (
                      <div className="flex items-center gap-2 text-sm">
                        <span className="font-medium">Languages:</span>
                        <span>{selectedDoctor.languages_spoken}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="mt-4 flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <Star className="h-5 w-5 fill-amber-400 text-amber-400" />
                      <span className="font-bold">{selectedDoctor.rating ? selectedDoctor.rating.toFixed(1) : "N/A"}</span>
                      <span className="text-sm text-muted-green">({selectedDoctor.review_count || 0} reviews)</span>
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm">
                      <span className="font-medium">Record:</span>
                      <span className="text-green-600 font-medium">{selectedDoctor.wins || 0}W</span>
                      <span className="text-red-600 font-medium">{selectedDoctor.losses || 0}L</span>
                      <span className="text-muted-green font-medium">{selectedDoctor.draws || 0}D</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4 pt-4 border-t">
                {selectedDoctor.educational_background && (
                  <div>
                    <h3 className="font-medium text-lg">Educational Background</h3>
                    <p className="mt-1 text-sm whitespace-pre-line">{selectedDoctor.educational_background}</p>
                  </div>
                )}
                
                {selectedDoctor.board_certifications && (
                  <div>
                    <h3 className="font-medium text-lg">Board Certifications</h3>
                    <p className="mt-1 text-sm whitespace-pre-line">{selectedDoctor.board_certifications}</p>
                  </div>
                )}
                
                <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium">Contact Information</h3>
                    <div className="mt-2 space-y-2 text-sm">
                      {selectedDoctor.email && (
                        <div className="flex items-center gap-2">
                          <span className="font-medium">Email:</span>
                          <span>{selectedDoctor.email}</span>
                        </div>
                      )}
                      
                      {selectedDoctor.phone_number && (
                        <div className="flex items-center gap-2">
                          <span className="font-medium">Phone:</span>
                          <span>{selectedDoctor.phone_number}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-medium">Last Updated</h3>
                    <p className="mt-2 text-sm">{selectedDoctor.last_updated ? new Date(selectedDoctor.last_updated).toLocaleDateString() : "Unknown"}</p>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end gap-3 pt-4 border-t">
                <Button variant="outline" onClick={() => setShowViewDialog(false)}>
                  Close
                </Button>
                <Button 
                  onClick={() => {
                    setFormValues({
                      fullname: selectedDoctor.fullname || '',
                      email: selectedDoctor.email || '',
                      medical_title: selectedDoctor.medical_title || '',
                      facility: selectedDoctor.facility || '',
                      specialty: selectedDoctor.specialty || '',
                      subspecialty: selectedDoctor.subspecialty || '',
                      specialty_id: selectedDoctor.specialty_id,
                      country_id: selectedDoctor.country_id,
                      hospital_id: selectedDoctor.hospital_id,
                      experience: selectedDoctor.experience,
                      languages_spoken: selectedDoctor.languages_spoken || '',
                      educational_background: selectedDoctor.educational_background || '',
                      board_certifications: selectedDoctor.board_certifications || '',
                      phone_number: selectedDoctor.phone_number,
                      publications: selectedDoctor.publications || '',
                      awards_recognitions: selectedDoctor.awards_recognitions || '',
                      professional_affiliations: selectedDoctor.professional_affiliations || '',
                      procedures_performed: selectedDoctor.procedures_performed || '',
                      treatment_services_expertise: selectedDoctor.treatment_services_expertise || ''
                    })
                    setShowViewDialog(false)
                    setShowEditDialog(true)
                  }}
                >
                  Edit Profile
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
      
      {/* Add the Edit Doctor Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Doctor Profile</DialogTitle>
            <p className="text-sm text-muted-green">
              Update doctor information in the database
            </p>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="fullname" className="text-right text-sm font-medium">
                Full Name
              </label>
              <Input
                id="fullname"
                className="col-span-3"
                value={formValues.fullname || ''}
                onChange={(e) => setFormValues({...formValues, fullname: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="email" className="text-right text-sm font-medium">
                Email
              </label>
              <Input
                id="email"
                type="email"
                className="col-span-3"
                value={formValues.email || ''}
                onChange={(e) => setFormValues({...formValues, email: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="medicalTitle" className="text-right text-sm font-medium">
                Medical Title
              </label>
              <Input
                id="medicalTitle"
                className="col-span-3"
                value={formValues.medical_title || ''}
                onChange={(e) => setFormValues({...formValues, medical_title: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="specialty" className="text-right text-sm font-medium">
                Specialty
              </label>
              <Select 
                value={formValues.specialty_id?.toString() || ''} 
                onValueChange={(value) => setFormValues({
                  ...formValues, 
                  specialty_id: parseInt(value),
                  specialty: specialties.find(s => s.specialty_id === parseInt(value))?.specialty_name || ''
                })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select specialty" />
                </SelectTrigger>
                <SelectContent>
                  {specialties.map((specialty) => (
                    <SelectItem key={specialty.specialty_id} value={specialty.specialty_id.toString()}>
                      {specialty.specialty_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="subspecialty" className="text-right text-sm font-medium">
                Subspecialty
              </label>
              <Input
                id="subspecialty"
                className="col-span-3"
                value={formValues.subspecialty || ''}
                onChange={(e) => setFormValues({...formValues, subspecialty: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="facility" className="text-right text-sm font-medium">
                Facility
              </label>
              <Input
                id="facility"
                className="col-span-3"
                value={formValues.facility || ''}
                onChange={(e) => setFormValues({...formValues, facility: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="hospital" className="text-right text-sm font-medium">
                Hospital
              </label>
              <Select 
                value={formValues.hospital_id?.toString() || ''} 
                onValueChange={(value) => setFormValues({...formValues, hospital_id: parseInt(value)})}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select hospital" />
                </SelectTrigger>
                <SelectContent>
                  {hospitals.map((hospital) => (
                    <SelectItem key={hospital.hospital_id} value={hospital.hospital_id.toString()}>
                      {hospital.hospital_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="country" className="text-right text-sm font-medium">
                Country
              </label>
              <Select 
                value={formValues.country_id?.toString() || ''} 
                onValueChange={(value) => setFormValues({...formValues, country_id: parseInt(value)})}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select country" />
                </SelectTrigger>
                <SelectContent>
                  {countries.map((country) => (
                    <SelectItem key={country.country_id} value={country.country_id.toString()}>
                      {country.country_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="experience" className="text-right text-sm font-medium">
                Experience (years)
              </label>
              <Input
                id="experience"
                type="number"
                className="col-span-3"
                value={formValues.experience || ''}
                onChange={(e) => setFormValues({...formValues, experience: parseInt(e.target.value) || undefined})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="phone" className="text-right text-sm font-medium">
                Phone Number
              </label>
              <Input
                id="phone"
                className="col-span-3"
                value={formValues.phone_number || ''}
                onChange={(e) => setFormValues({...formValues, phone_number: parseInt(e.target.value) || undefined})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="languages" className="text-right text-sm font-medium">
                Languages Spoken
              </label>
              <Input
                id="languages"
                className="col-span-3"
                value={formValues.languages_spoken || ''}
                onChange={(e) => setFormValues({...formValues, languages_spoken: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-start gap-4">
              <label htmlFor="education" className="text-right text-sm font-medium pt-2">
                Education
              </label>
              <textarea
                id="education"
                className="col-span-3 min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-green focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={formValues.educational_background || ''}
                onChange={(e) => setFormValues({...formValues, educational_background: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-start gap-4">
              <label htmlFor="certifications" className="text-right text-sm font-medium pt-2">
                Board Certifications
              </label>
              <textarea
                id="certifications"
                className="col-span-3 min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-green focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={formValues.board_certifications || ''}
                onChange={(e) => setFormValues({...formValues, board_certifications: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-start gap-4">
              <label htmlFor="publications" className="text-right text-sm font-medium pt-2">
                Publications
              </label>
              <textarea
                id="publications"
                className="col-span-3 min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-green focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={formValues.publications || ''}
                onChange={(e) => setFormValues({...formValues, publications: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-start gap-4">
              <label htmlFor="awards" className="text-right text-sm font-medium pt-2">
                Awards & Recognitions
              </label>
              <textarea
                id="awards"
                className="col-span-3 min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-green focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={formValues.awards_recognitions || ''}
                onChange={(e) => setFormValues({...formValues, awards_recognitions: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-start gap-4">
              <label htmlFor="affiliations" className="text-right text-sm font-medium pt-2">
                Professional Affiliations
              </label>
              <textarea
                id="affiliations"
                className="col-span-3 min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-green focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={formValues.professional_affiliations || ''}
                onChange={(e) => setFormValues({...formValues, professional_affiliations: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-start gap-4">
              <label htmlFor="procedures" className="text-right text-sm font-medium pt-2">
                Procedures Performed
              </label>
              <textarea
                id="procedures"
                className="col-span-3 min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-green focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={formValues.procedures_performed || ''}
                onChange={(e) => setFormValues({...formValues, procedures_performed: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-start gap-4">
              <label htmlFor="expertise" className="text-right text-sm font-medium pt-2">
                Treatment Services & Expertise
              </label>
              <textarea
                id="expertise"
                className="col-span-3 min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-green focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={formValues.treatment_services_expertise || ''}
                onChange={(e) => setFormValues({...formValues, treatment_services_expertise: e.target.value})}
              />
            </div>
          </div>
          
          <div className="flex justify-end gap-3 mt-4">
            <Button variant="outline" onClick={() => {
              setShowEditDialog(false)
              setSelectedDoctor(null)
              setFormValues({})
            }}>
              Cancel
            </Button>
            <Button onClick={handleEditDoctor}>
              Save Changes
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      
      {/* Add the Add Doctor Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Doctor</DialogTitle>
            <p className="text-sm text-muted-green">
              Create a new doctor profile in the system
            </p>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newFullName" className="text-right text-sm font-medium">
                Full Name*
              </label>
              <Input
                id="newFullName"
                className="col-span-3"
                value={newDoctorData.fullname || ''}
                onChange={(e) => setNewDoctorData({...newDoctorData, fullname: e.target.value})}
                required
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newEmail" className="text-right text-sm font-medium">
                Email
              </label>
              <Input
                id="newEmail"
                type="email"
                className="col-span-3"
                value={newDoctorData.email || ''}
                onChange={(e) => setNewDoctorData({...newDoctorData, email: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newMedicalTitle" className="text-right text-sm font-medium">
                Medical Title
              </label>
              <Input
                id="newMedicalTitle"
                className="col-span-3"
                value={newDoctorData.medical_title || ''}
                onChange={(e) => setNewDoctorData({...newDoctorData, medical_title: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newSpecialty" className="text-right text-sm font-medium">
                Specialty
              </label>
              <Select 
                value={newDoctorData.specialty_id?.toString() || ''} 
                onValueChange={(value) => setNewDoctorData({
                  ...newDoctorData, 
                  specialty_id: parseInt(value),
                  specialty: specialties.find(s => s.specialty_id === parseInt(value))?.specialty_name || ''
                })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select specialty" />
                </SelectTrigger>
                <SelectContent>
                  {specialties.map((specialty) => (
                    <SelectItem key={specialty.specialty_id} value={specialty.specialty_id.toString()}>
                      {specialty.specialty_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newSubspecialty" className="text-right text-sm font-medium">
                Subspecialty
              </label>
              <Input
                id="newSubspecialty"
                className="col-span-3"
                value={newDoctorData.subspecialty || ''}
                onChange={(e) => setNewDoctorData({...newDoctorData, subspecialty: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newFacility" className="text-right text-sm font-medium">
                Facility
              </label>
              <Input
                id="newFacility"
                className="col-span-3"
                value={newDoctorData.facility || ''}
                onChange={(e) => setNewDoctorData({...newDoctorData, facility: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newHospital" className="text-right text-sm font-medium">
                Hospital
              </label>
              <Select 
                value={newDoctorData.hospital_id?.toString() || ''} 
                onValueChange={(value) => setNewDoctorData({...newDoctorData, hospital_id: parseInt(value)})}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select hospital" />
                </SelectTrigger>
                <SelectContent>
                  {hospitals.map((hospital) => (
                    <SelectItem key={hospital.hospital_id} value={hospital.hospital_id.toString()}>
                      {hospital.hospital_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newCountry" className="text-right text-sm font-medium">
                Country
              </label>
              <Select 
                value={newDoctorData.country_id?.toString() || ''} 
                onValueChange={(value) => setNewDoctorData({...newDoctorData, country_id: parseInt(value)})}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select country" />
                </SelectTrigger>
                <SelectContent>
                  {countries.map((country) => (
                    <SelectItem key={country.country_id} value={country.country_id.toString()}>
                      {country.country_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newExperience" className="text-right text-sm font-medium">
                Experience (years)
              </label>
              <Input
                id="newExperience"
                type="number"
                className="col-span-3"
                value={newDoctorData.experience || ''}
                onChange={(e) => setNewDoctorData({...newDoctorData, experience: parseInt(e.target.value) || undefined})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newPhone" className="text-right text-sm font-medium">
                Phone Number
              </label>
              <Input
                id="newPhone"
                className="col-span-3"
                value={newDoctorData.phone_number || ''}
                onChange={(e) => setNewDoctorData({...newDoctorData, phone_number: parseInt(e.target.value) || undefined})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newLanguages" className="text-right text-sm font-medium">
                Languages Spoken
              </label>
              <Input
                id="newLanguages"
                className="col-span-3"
                value={newDoctorData.languages_spoken || ''}
                onChange={(e) => setNewDoctorData({...newDoctorData, languages_spoken: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-start gap-4">
              <label htmlFor="newEducation" className="text-right text-sm font-medium pt-2">
                Education
              </label>
              <textarea
                id="newEducation"
                className="col-span-3 min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-green focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={newDoctorData.educational_background || ''}
                onChange={(e) => setNewDoctorData({...newDoctorData, educational_background: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-start gap-4">
              <label htmlFor="newCertifications" className="text-right text-sm font-medium pt-2">
                Board Certifications
              </label>
              <textarea
                id="newCertifications"
                className="col-span-3 min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-green focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={newDoctorData.board_certifications || ''}
                onChange={(e) => setNewDoctorData({...newDoctorData, board_certifications: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-start gap-4">
              <label htmlFor="newPublications" className="text-right text-sm font-medium pt-2">
                Publications
              </label>
              <textarea
                id="newPublications"
                className="col-span-3 min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-green focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={newDoctorData.publications || ''}
                onChange={(e) => setNewDoctorData({...newDoctorData, publications: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-start gap-4">
              <label htmlFor="newAwards" className="text-right text-sm font-medium pt-2">
                Awards & Recognitions
              </label>
              <textarea
                id="newAwards"
                className="col-span-3 min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-green focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={newDoctorData.awards_recognitions || ''}
                onChange={(e) => setNewDoctorData({...newDoctorData, awards_recognitions: e.target.value})}
              />
            </div>
          </div>
          
          <div className="flex justify-end gap-3 mt-4">
            <Button variant="outline" onClick={() => {
              setShowAddDialog(false)
              setNewDoctorData({
                fullname: '',
                email: '',
                medical_title: '',
                facility: '',
                specialty: '',
                subspecialty: '',
                specialty_id: undefined,
                country_id: undefined,
                hospital_id: undefined,
                experience: undefined,
                languages_spoken: '',
                educational_background: '',
                board_certifications: '',
                phone_number: undefined,
                publications: '',
                awards_recognitions: '',
                professional_affiliations: '',
                procedures_performed: '',
                treatment_services_expertise: ''
              })
            }}>
              Cancel
            </Button>
            <Button onClick={handleAddDoctor}>
              Add Doctor
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      
      {/* Add Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-2">
            <p>Are you sure you want to delete this doctor?</p>
            <p className="text-sm text-muted-green">This action cannot be undone.</p>
          </div>
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button 
              variant="outline" 
              onClick={() => {
                if (selectedDoctor) {
                  handleDeleteDoctor(selectedDoctor.doctor_id)
                  setShowDeleteDialog(false)
                }
              }}
              className="bg-red-100 text-red-600 hover:bg-red-200 border-red-200"
            >
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
} 