"use client"

import { useState, useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import Link from "next/link"
import {
  Menu,
  X,
  Home,
  Users,
  UserCog,
  FileText,
  BarChart,
  Settings,
  LogOut,
  Bell,
  BookOpen,
  Shield,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { AdminAuthCheck } from "./middleware"
import { cn } from "@/lib/utils"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip"

interface NavItemProps {
  href: string
  icon: React.ReactNode
  title: string
  isActive: boolean
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [menuOpen, setMenuOpen] = useState(true)
  const router = useRouter()
  const pathname = usePathname()
  
  // Handle sign out
  const handleSignOut = () => {
    sessionStorage.removeItem("admin_authenticated")
    router.push("/admin/login")
  }
  
  // Use a smaller sidebar on mobile
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1024) {
        setMenuOpen(false)
      } else {
        setMenuOpen(true)
      }
    }
    
    // Set initial state
    handleResize()
    
    // Add event listener
    window.addEventListener("resize", handleResize)
    
    // Clean up
    return () => window.removeEventListener("resize", handleResize)
  }, [])
  
  // NavItem component
  const NavItem = ({ href, icon, title, isActive }: NavItemProps) => (
    <Link 
      href={href}
      className={`flex items-center gap-3 px-3 py-2 rounded-md transition-colors ${
        isActive 
          ? "bg-primary text-primary-foreground" 
          : "hover:bg-muted-green"
      }`}
    >
      {icon}
      {menuOpen && <span>{title}</span>}
    </Link>
  )
  
  return (
    <AdminAuthCheck>
      <TooltipProvider>
        <div className="flex min-h-screen">
          {/* Sidebar */}
          <aside className={`bg-card fixed inset-y-0 z-10 transition-all duration-300 ${
            menuOpen ? "w-64" : "w-16"
          } lg:static`}>
            <div className="flex h-14 items-center px-4 border-b gap-2">
              <Button
                variant="outline"
                size="sm"
                className="mr-2 h-8 w-8 p-0 lg:hidden"
                onClick={() => setMenuOpen(!menuOpen)}
              >
                <span className="sr-only">Toggle Menu</span>
                <Menu className="h-4 w-4" />
              </Button>
              <span className="text-lg font-semibold tracking-tight flex-1">
                Admin Dashboard
              </span>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={handleSignOut}
                  >
                    <span className="sr-only">Sign out</span>
                    <LogOut className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Sign out</p>
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="sm" className="ml-auto h-8 w-8 p-0">
                    <span className="sr-only">Notifications</span>
                    <Bell className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Notifications</p>
                </TooltipContent>
              </Tooltip>
            </div>
            
            <ScrollArea className="h-[calc(100vh-4rem)] px-3">
              <div className="space-y-4 py-4">
                <div className="space-y-1">
                  <NavItem 
                    href="/admin/dashboard" 
                    icon={<Home className="h-4 w-4" />} 
                    title="Dashboard" 
                    isActive={pathname === "/admin/dashboard"} 
                  />
                  <NavItem 
                    href="/admin/analytics" 
                    icon={<BarChart className="h-4 w-4" />} 
                    title="Analytics" 
                    isActive={pathname === "/admin/analytics"} 
                  />
                  <NavItem 
                    href="/admin/users" 
                    icon={<Users className="h-4 w-4" />} 
                    title="Users" 
                    isActive={pathname === "/admin/users"} 
                  />
                  <NavItem 
                    href="/admin/doctors" 
                    icon={<UserCog className="h-4 w-4" />} 
                    title="Doctors" 
                    isActive={pathname === "/admin/doctors"} 
                  />
                  <NavItem
                    href="/admin/reviews"
                    icon={<FileText className="h-4 w-4" />}
                    title="Reviews"
                    isActive={pathname === "/admin/reviews"}
                  />
                  <NavItem
                    href="/admin/moderation"
                    icon={<Shield className="h-4 w-4" />}
                    title="Moderation"
                    isActive={pathname === "/admin/moderation"}
                  />
                  <NavItem
                    href="/admin/blog"
                    icon={<BookOpen className="h-4 w-4" />}
                    title="Blog"
                    isActive={pathname?.startsWith("/admin/blog") || false}
                  />
                  <NavItem 
                    href="/admin/ads" 
                    icon={<BarChart className="h-4 w-4" />} 
                    title="Advertising" 
                    isActive={pathname === "/admin/ads"} 
                  />
                  <NavItem 
                    href="/admin/registration-approval" 
                    icon={<UserCog className="h-4 w-4" />} 
                    title="Pending Registrations" 
                    isActive={pathname === "/admin/registration-approval"} 
                  />
                  <NavItem 
                    href="/admin/settings" 
                    icon={<Settings className="h-4 w-4" />} 
                    title="Settings" 
                    isActive={pathname === "/admin/settings"} 
                  />
                </div>
              </div>
            </ScrollArea>
          </aside>
          
          {/* Main content */}
          <main className={`flex-1 min-h-screen transition-all duration-300 ${
            menuOpen ? "lg:ml-64" : "lg:ml-16"
          }`}>
            {/* Header */}
            <header className="h-16 border-b flex items-center justify-between px-6">
              <div>
                <h1 className="text-lg font-medium">Doctor's League Admin</h1>
              </div>
              
              <div className="flex items-center space-x-4">
                <Button variant="outline" size="sm" className="p-2 h-9 w-9">
                  <Bell className="h-4 w-4" />
                </Button>
              </div>
            </header>
            
            {/* Page content */}
            <div className="p-6">
              {children}
            </div>
          </main>
        </div>
      </TooltipProvider>
    </AdminAuthCheck>
  )
} 