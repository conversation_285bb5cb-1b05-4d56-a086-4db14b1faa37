"use client"

import { useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"

export function AdminAuthCheck({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const pathname = usePathname()
  
  useEffect(() => {
    // <PERSON>p auth check for the login page itself
    if (pathname === "/admin/login") {
      return
    }
    
    // Check if user is authenticated
    const isAuthenticated = sessionStorage.getItem("admin_authenticated") === "true"
    
    if (!isAuthenticated) {
      router.push("/admin/login")
    }
  }, [pathname, router])
  
  // If we're on the login page, or authenticated, render children
  return children
} 