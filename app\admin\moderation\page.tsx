"use client"

import { useState, useEffect, useTransition } from "react"
import { useRout<PERSON> } from "next/navigation"
import {
  Eye,
  Check,
  X,
  AlertTriangle,
  FileText,
  Flag,
  Loader2,
  Calendar,
  User,
  MessageSquare,
  Download,
  ZoomIn,
  ZoomOut,
  RotateCcw
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Tabs,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  getPendingVerifications,
  decideVerificationRequest,
  getOpenReview<PERSON>lags,
  dismissReviewFlag,
  removeFlaggedReview
} from "@/actions/admin-actions"

interface VerificationRequest {
  id: string
  review_id: string
  proof_image_url: string
  created_at: string
  reviews: {
    review_id: string
    additional_comments: string
    rating: number
    review_date: string
    verification_status: string
    doctor_id: number
    user_id: number
    doctors: {
      doctor_id: number
      fullname: string
    }
  }
}

interface ReviewFlag {
  id: string
  review_id: string
  reporter_user_id: number | null
  flag_reason: string
  flag_description: string
  status: string
  created_at: string
  reviews: {
    review_id: string
    additional_comments: string
    rating: number
    review_date: string
    verification_status: string
    doctor_id: number
    user_id: number
  }
}

export default function ModerationPage() {
  const router = useRouter()
  const [pendingVerifications, setPendingVerifications] = useState<VerificationRequest[]>([])
  const [reviewFlags, setReviewFlags] = useState<ReviewFlag[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Dialog states
  const [showProofDialog, setShowProofDialog] = useState(false)
  const [selectedProofUrl, setSelectedProofUrl] = useState<string>("")
  const [imageZoom, setImageZoom] = useState(1)

  // Transition states for server actions
  const [isPending, startTransition] = useTransition()
  const [actioningItem, setActioningItem] = useState<string | null>(null)

  // Admin notes for flag actions
  const [adminNotes, setAdminNotes] = useState("")
  const [showNotesDialog, setShowNotesDialog] = useState(false)
  const [pendingFlagAction, setPendingFlagAction] = useState<{
    type: 'dismiss' | 'remove'
    flagId: string
    reviewId?: string
  } | null>(null)

  // Load initial data
  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Check admin authentication using the same method as admin middleware
      const isAuthenticated = sessionStorage.getItem("admin_authenticated") === "true"
      if (!isAuthenticated) {
        setError("Admin authentication required. Please log in as admin.")
        return
      }

      // Fetch actual data from server actions
      console.log("Fetching pending verifications and review flags...")

      const [verificationsResult, flagsResult] = await Promise.all([
        getPendingVerifications(),
        getOpenReviewFlags()
      ])

      console.log("Verifications result:", verificationsResult)
      console.log("Flags result:", flagsResult)

      if (verificationsResult.error) {
        console.error("Error fetching verifications:", verificationsResult.error)
        setError(verificationsResult.error)
        return
      }

      if (flagsResult.error) {
        console.error("Error fetching flags:", flagsResult.error)
        setError(flagsResult.error)
        return
      }

      setPendingVerifications(verificationsResult.data || [])
      setReviewFlags(flagsResult.data || [])

      console.log(`Loaded ${verificationsResult.data?.length || 0} pending verifications`)
      console.log(`Loaded ${flagsResult.data?.length || 0} review flags`)

    } catch (err: any) {
      console.error("Error loading moderation data:", err)
      setError(err.message || "Failed to load moderation data")
    } finally {
      setLoading(false)
    }
  }

  const handleVerificationDecision = (reviewId: string, decision: 'approved' | 'rejected') => {
    setActioningItem(reviewId)

    startTransition(async () => {
      try {
        const result = await decideVerificationRequest(reviewId, decision)

        if (!result.success) {
          throw new Error(result.error || "Failed to process verification decision")
        }

        // Refresh the page to update the data
        router.refresh()

        // Remove the item from local state
        setPendingVerifications(prev =>
          prev.filter(item => item.reviews.review_id !== reviewId)
        )

      } catch (err: any) {
        console.error("Error processing verification decision:", err)
        setError(err.message || "Failed to process verification decision")
      } finally {
        setActioningItem(null)
      }
    })
  }

  const handleViewProof = (proofUrl: string) => {
    setSelectedProofUrl(proofUrl)
    setImageZoom(1) // Reset zoom when opening new image
    setShowProofDialog(true)
  }

  const handleZoomIn = () => {
    setImageZoom(prev => Math.min(prev + 0.25, 3))
  }

  const handleZoomOut = () => {
    setImageZoom(prev => Math.max(prev - 0.25, 0.5))
  }

  const handleResetZoom = () => {
    setImageZoom(1)
  }

  const handleDownloadProof = (proofUrl: string) => {
    const link = document.createElement('a')
    link.href = proofUrl
    link.download = `verification-proof-${Date.now()}.jpg`
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleFlagAction = (type: 'dismiss' | 'remove', flagId: string, reviewId?: string) => {
    setPendingFlagAction({ type, flagId, reviewId })
    setAdminNotes("")
    setShowNotesDialog(true)
  }

  const executeFlagAction = () => {
    if (!pendingFlagAction) return

    setActioningItem(pendingFlagAction.flagId)
    setShowNotesDialog(false)

    startTransition(async () => {
      try {
        let result

        if (pendingFlagAction.type === 'dismiss') {
          result = await dismissReviewFlag(pendingFlagAction.flagId, adminNotes)
        } else {
          result = await removeFlaggedReview(
            pendingFlagAction.flagId,
            pendingFlagAction.reviewId!,
            adminNotes
          )
        }

        if (!result.success) {
          throw new Error(result.error || "Failed to process flag action")
        }

        // Refresh the page to update the data
        router.refresh()

        // Remove the item from local state
        setReviewFlags(prev =>
          prev.filter(item => item.id !== pendingFlagAction.flagId)
        )

      } catch (err: any) {
        console.error("Error processing flag action:", err)
        setError(err.message || "Failed to process flag action")
      } finally {
        setActioningItem(null)
        setPendingFlagAction(null)
      }
    })
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'No Date'

    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) {
        return 'Invalid Date'
      }

      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch (error) {
      console.error('Error formatting date:', error)
      return 'Invalid Date'
    }
  }

  const formatFlagReason = (reason: string) => {
    return reason.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading moderation data...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Content Moderation</h1>
          <p className="text-muted-foreground">
            Review verification requests and manage flagged content
          </p>
        </div>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertTriangle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="verification" className="space-y-4">
        <TabsList>
          <TabsTrigger value="verification" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Verification Queue ({pendingVerifications.length})
          </TabsTrigger>
          <TabsTrigger value="flags" className="flex items-center gap-2">
            <Flag className="h-4 w-4" />
            Flagged Reviews ({reviewFlags.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="verification" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pending Verification Requests</CardTitle>
              <CardDescription>
                Review appointment receipts and approve or reject verification requests
              </CardDescription>
            </CardHeader>
            <CardContent>
              {pendingVerifications.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No pending verifications</h3>
                  <p className="text-muted-foreground">All verification requests have been processed.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table className="min-w-[900px]">
                  <TableHeader>
                    <TableRow>
                      <TableHead className="min-w-[120px] text-green-100 font-semibold">Reviewer</TableHead>
                      <TableHead className="min-w-[400px] text-green-100 font-semibold">Review Details & Ratings</TableHead>
                      <TableHead className="min-w-[140px] text-green-100 font-semibold">Date Submitted</TableHead>
                      <TableHead className="min-w-[200px] text-green-100 font-semibold">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {pendingVerifications.map((verification) => (
                      <TableRow key={verification.proof_id}>
                        <TableCell className="min-w-[120px]">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-green-600" />
                            <div className="flex flex-col">
                              <span className="font-medium light:text-green-800 dark:text-white">User #{verification.reviews.user_id}</span>
                              <span className="text-xs light:text-green-600 dark:text-green-200">Reviewer</span>
                              <div className="mt-1 pt-1 border-t border-green-400/30">
                                <span className="text-xs light:text-green-700 dark:text-green-300">Reviewing:</span>
                                <span className="text-xs font-medium light:text-green-800 dark:text-white block">{verification.reviews.doctors.fullname}</span>
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="min-w-[400px] max-w-[500px]">
                          <div className="space-y-4">
                            <div className="grid grid-cols-1 gap-3">
                              <div className="grid grid-cols-2 gap-2">
                                <div className="flex items-center justify-between p-2 bg-white/10 rounded border border-green-400/30">
                                  <span className="text-xs font-medium light:text-green-800 dark:text-white">Clinical:</span>
                                  <Badge variant="outline" className="text-xs ml-2 bg-green-100 text-green-800 border-green-300">
                                    {verification.reviews.clinical_competence}/5
                                  </Badge>
                                </div>
                                <div className="flex items-center justify-between p-2 bg-white/10 rounded border border-green-400/30">
                                  <span className="text-xs font-medium light:text-green-800 dark:text-white">Communication:</span>
                                  <Badge variant="outline" className="text-xs ml-2 bg-green-100 text-green-800 border-green-300">
                                    {verification.reviews.communication_stats}/5
                                  </Badge>
                                </div>
                              </div>
                              <div className="grid grid-cols-2 gap-2">
                                <div className="flex items-center justify-between p-2 bg-white/10 rounded border border-green-400/30">
                                  <span className="text-xs font-medium light:text-green-800 dark:text-white">Empathy:</span>
                                  <Badge variant="outline" className="text-xs ml-2 bg-green-100 text-green-800 border-green-300">
                                    {verification.reviews.empathy_compassion}/5
                                  </Badge>
                                </div>
                                <div className="flex items-center justify-between p-2 bg-white/10 rounded border border-green-400/30">
                                  <span className="text-xs font-medium light:text-green-800 dark:text-white">Time Mgmt:</span>
                                  <Badge variant="outline" className="text-xs ml-2 bg-green-100 text-green-800 border-green-300">
                                    {verification.reviews.time_management}/5
                                  </Badge>
                                </div>
                              </div>
                              <div className="grid grid-cols-2 gap-2">
                                <div className="flex items-center justify-between p-2 bg-white/10 rounded border border-green-400/30">
                                  <span className="text-xs font-medium light:text-green-800 dark:text-white">Follow-up:</span>
                                  <Badge variant="outline" className="text-xs ml-2 bg-green-100 text-green-800 border-green-300">
                                    {verification.reviews.follow_up_care}/5
                                  </Badge>
                                </div>
                                <div className="flex items-center justify-between p-2 bg-white/10 rounded border border-green-400/30">
                                  <span className="text-xs font-medium light:text-green-800 dark:text-white">Overall:</span>
                                  <Badge variant="outline" className="text-xs ml-2 bg-green-100 text-green-800 border-green-300">
                                    {verification.reviews.overall_satisfaction}/5
                                  </Badge>
                                </div>
                              </div>
                              <div className="flex items-center justify-between p-2 bg-yellow-500/20 rounded border border-yellow-400/50">
                                <span className="text-xs font-medium text-yellow-100">Recommendation:</span>
                                <Badge variant="outline" className="text-xs ml-2 bg-yellow-100 text-yellow-800 border-yellow-300">
                                  {verification.reviews.Recommendation}/5
                                </Badge>
                              </div>
                            </div>
                            <div className="border-t border-green-400/30 pt-3">
                              <p className="text-xs font-medium mb-2 light:text-green-700 dark:text-green-200">Comments:</p>
                              <div className="bg-white/10 p-3 rounded border border-green-400/30 text-xs light:text-green-800 dark:text-white">
                                {verification.reviews.additional_comments || "No comment provided"}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="min-w-[140px]">
                          <div className="flex flex-col gap-1">
                            <div className="flex items-center gap-2 text-sm">
                              <Calendar className="h-4 w-4 text-green-400" />
                              <span className="font-medium light:text-green-800 dark:text-white">
                                {formatDate(verification.reviews.review_date)}
                              </span>
                            </div>
                            <span className="text-xs light:text-green-600 dark:text-green-200 ml-6">
                              Submitted
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="min-w-[200px]">
                          <div className="flex flex-col gap-2">
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleViewProof(verification.proof_image_url)}
                                className="flex-1"
                              >
                                <Eye className="h-4 w-4 mr-1" />
                                View
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDownloadProof(verification.proof_image_url)}
                                className="flex-1"
                              >
                                <Download className="h-4 w-4 mr-1" />
                                Download
                              </Button>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                className="bg-green-600 hover:bg-green-700 flex-1"
                                onClick={() => handleVerificationDecision(verification.reviews.review_id, 'approved')}
                                disabled={isPending && actioningItem === verification.reviews.review_id}
                              >
                                {isPending && actioningItem === verification.reviews.review_id ? (
                                  <Loader2 className="h-4 w-4 animate-spin mr-1" />
                                ) : (
                                  <Check className="h-4 w-4 mr-1" />
                                )}
                                Approve
                              </Button>
                              <Button
                                variant="destructive"
                                size="sm"
                                className="flex-1"
                                onClick={() => handleVerificationDecision(verification.reviews.review_id, 'rejected')}
                                disabled={isPending && actioningItem === verification.reviews.review_id}
                              >
                                {isPending && actioningItem === verification.reviews.review_id ? (
                                  <Loader2 className="h-4 w-4 animate-spin mr-1" />
                                ) : (
                                  <X className="h-4 w-4 mr-1" />
                                )}
                                Reject
                              </Button>
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="flags" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Flagged Reviews</CardTitle>
              <CardDescription>
                Review reported content and take appropriate moderation actions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {reviewFlags.length === 0 ? (
                <div className="text-center py-8">
                  <Flag className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No flagged reviews</h3>
                  <p className="text-muted-foreground">All reported content has been reviewed.</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Review Content</TableHead>
                      <TableHead>Reason for Flag</TableHead>
                      <TableHead>Flagged By</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {reviewFlags.map((flag) => (
                      <TableRow key={flag.id}>
                        <TableCell className="max-w-md">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">
                                {flag.reviews.rating}/5 stars
                              </Badge>
                              <Badge variant="secondary" className="text-xs">
                                Review #{flag.reviews.review_id}
                              </Badge>
                            </div>
                            <p className="text-sm line-clamp-3">
                              {flag.reviews.additional_comments || "No comment provided"}
                            </p>
                            {flag.flag_description && (
                              <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                                <p className="text-xs text-red-800">
                                  <strong>Reporter's note:</strong> {flag.flag_description}
                                </p>
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="destructive" className="bg-red-100 text-red-800">
                            {formatFlagReason(flag.flag_reason)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2 text-sm">
                            <User className="h-4 w-4 text-muted-foreground" />
                            {flag.reporter_user_id ? (
                              <span>User #{flag.reporter_user_id}</span>
                            ) : (
                              <span className="text-muted-foreground">Anonymous</span>
                            )}
                          </div>
                          <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
                            <Calendar className="h-3 w-3" />
                            {formatDate(flag.created_at)}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleFlagAction('dismiss', flag.id)}
                              disabled={isPending && actioningItem === flag.id}
                            >
                              {isPending && actioningItem === flag.id ? (
                                <Loader2 className="h-4 w-4 animate-spin mr-1" />
                              ) : (
                                <X className="h-4 w-4 mr-1" />
                              )}
                              Dismiss Flag
                            </Button>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => handleFlagAction('remove', flag.id, flag.reviews.review_id)}
                              disabled={isPending && actioningItem === flag.id}
                            >
                              {isPending && actioningItem === flag.id ? (
                                <Loader2 className="h-4 w-4 animate-spin mr-1" />
                              ) : (
                                <AlertTriangle className="h-4 w-4 mr-1" />
                              )}
                              Remove Review
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Proof Image Dialog */}
      <Dialog open={showProofDialog} onOpenChange={setShowProofDialog}>
        <DialogContent className="max-w-5xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Appointment Receipt</DialogTitle>
            <DialogDescription>
              Review the uploaded appointment receipt for verification
            </DialogDescription>
          </DialogHeader>

          {/* Zoom Controls */}
          <div className="flex items-center justify-center gap-2 mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handleZoomOut}
              disabled={imageZoom <= 0.5}
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            <span className="text-sm font-medium min-w-[60px] text-center">
              {Math.round(imageZoom * 100)}%
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleZoomIn}
              disabled={imageZoom >= 3}
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleResetZoom}
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>

          {/* Image Container */}
          <div className="max-h-[60vh] overflow-auto border rounded-lg bg-muted/20">
            <div className="flex justify-center p-4">
              {selectedProofUrl ? (
                <img
                  src={selectedProofUrl}
                  alt="Appointment Receipt"
                  className="rounded-lg border bg-white"
                  style={{
                    transform: `scale(${imageZoom})`,
                    transformOrigin: 'center',
                    transition: 'transform 0.2s ease-in-out',
                    maxWidth: 'none',
                    height: 'auto'
                  }}
                />
              ) : (
                <div className="flex items-center justify-center h-64 bg-muted rounded-lg">
                  <p className="text-muted-foreground">No image available</p>
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Admin Notes Dialog */}
      <Dialog open={showNotesDialog} onOpenChange={setShowNotesDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {pendingFlagAction?.type === 'dismiss' ? 'Dismiss Flag' : 'Remove Review'}
            </DialogTitle>
            <DialogDescription>
              {pendingFlagAction?.type === 'dismiss'
                ? 'Add optional notes about why this flag is being dismissed.'
                : 'Add optional notes about why this review is being removed.'
              }
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="admin-notes">Admin Notes (Optional)</Label>
              <Textarea
                id="admin-notes"
                placeholder="Enter your notes here..."
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                className="mt-1"
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowNotesDialog(false)}
              >
                Cancel
              </Button>
              <Button
                variant={pendingFlagAction?.type === 'dismiss' ? 'default' : 'destructive'}
                onClick={executeFlagAction}
              >
                {pendingFlagAction?.type === 'dismiss' ? 'Dismiss Flag' : 'Remove Review'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
