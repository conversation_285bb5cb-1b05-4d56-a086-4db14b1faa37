// File: app/admin/registration-approval/page.tsx - VERIFIED FINAL VERSION

import { createClient } from '@supabase/supabase-js';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Doctor<PERSON>omparison<PERSON><PERSON> from '@/components/admin/DoctorComparisonCard';
import React from 'react';

type DoctorProfile = { id: number; doctor_id: number; fullname: string; hospital: string; [key: string]: any; };
type RegistrationData = { pendingRegistration: DoctorProfile; existingDoctor: DoctorProfile | null; };

export default async function RegistrationApprovalPage() {
  const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
  const { data: pendingRegistrations, error: pendingError } = await supabase.from('doctors_registration').select('*').eq('status', 'pending');

  if (pendingError) {
    return <div className="p-6 text-red-500">Error loading registrations: {pendingError.message}</div>;
  }
  if (!pendingRegistrations || pendingRegistrations.length === 0) {
    return (
      <div className="p-6"><h1 className="text-2xl font-bold mb-6">Pending Doctor Registrations</h1><Card><CardHeader><CardTitle>No Pending Registrations</CardTitle></CardHeader><CardContent><p>There are no registrations awaiting approval.</p></CardContent></Card></div>
    );
  }
  
  const dataPairs: RegistrationData[] = await Promise.all(
    pendingRegistrations.map(async (pendingReg) => {
      const { data: existingDoctor } = await supabase.from('doctors').select('*').eq('fullname', pendingReg.fullname).eq('hospital', pendingReg.hospital).maybeSingle();
      return { pendingRegistration: { ...pendingReg, id: pendingReg.doctor_id }, existingDoctor: existingDoctor || null };
    })
  );

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Pending Doctor Registrations</h1>
      <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3">
        {dataPairs.map((pair) => (
          <DoctorComparisonCard key={pair.pendingRegistration.id} pendingDoctor={pair.pendingRegistration} existingDoctor={pair.existingDoctor} />
        ))}
      </div>
    </div>
  );
}