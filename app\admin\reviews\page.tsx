"use client"

import { useState, useEffect } from "react"
import { 
  Search, 
  UserPlus, 
  Check, 
  X, 
  Edit, 
  Trash2,
  Filter,
  MoreHorizontal,
  Star,
  AlertCircle,
  FileText,
  EyeIcon,
  UserCircle
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  Dialog<PERSON><PERSON>le,
} from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { supabase } from "@/lib/supabase-client"

type Review = {
  review_id: number
  user_id?: number
  doctor_id?: number
  clinical_competence?: number
  communication_stats?: number
  empathy_compassion?: number
  time_management?: number
  follow_up_care?: number
  overall_satisfaction?: number
  additional_comments?: string
  rating?: number
  review_date?: string
  is_approved?: boolean
  username?: string
  doctor_fullname?: string
  specialty?: string
  title?: string
  content?: string
  wait_time?: string
  bedside_manner?: string
  availability?: string
  would_recommend?: boolean
  created_at?: string
}

type Doctor = {
  doctor_id: number
  fullname: string
}

type User = {
  user_id: number
  username?: string
  first_name?: string
  last_name?: string
  email?: string
}

export default function ReviewsPage() {
  const [reviews, setReviews] = useState<Review[]>([])
  const [filteredReviews, setFilteredReviews] = useState<Review[]>([])
  const [doctors, setDoctors] = useState<Doctor[]>([])
  const [users, setUsers] = useState<User[]>([])
  
  const [searchQuery, setSearchQuery] = useState("")
  const [doctorFilter, setDoctorFilter] = useState<string>("all")
  const [approvalFilter, setApprovalFilter] = useState<string>("all")
  const [ratingFilter, setRatingFilter] = useState<string>("all")
  const [sortBy, setSortBy] = useState<string>("date-desc")
  
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedReview, setSelectedReview] = useState<Review | null>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showViewDialog, setShowViewDialog] = useState(false)
  
  const itemsPerPage = 10
  
  // Fetch reviews, doctors and users data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        console.log("Fetching reviews data with Supabase client")
        
        // Fetch reviews with doctor names and user names
        const { data: reviewsData, error: reviewsError } = await supabase
          .from('reviews')
          .select('*')
          .order('review_date', { ascending: false })
        
        if (reviewsError) {
          console.error("Error fetching reviews:", reviewsError)
          throw reviewsError
        }
        
        console.log(`Fetched ${reviewsData ? reviewsData.length : 0} reviews`)
        
        // Fetch doctors for filtering options
        const { data: doctorsData, error: doctorsError } = await supabase
          .from('doctors')
          .select('doctor_id, fullname')
          .order('fullname', { ascending: true })
        
        if (doctorsError) {
          console.error("Error fetching doctors:", doctorsError)
          // Continue even if there's an error
        }
        
        console.log(`Fetched ${doctorsData ? doctorsData.length : 0} doctors`)
        
        // Fetch users for displaying user names
        const { data: usersData, error: usersError } = await supabase
          .from('users')
          .select('user_id, username, first_name, last_name, email')
        
        if (usersError) {
          console.error("Error fetching users:", usersError)
          // Continue even if there's an error
        }
        
        console.log(`Fetched ${usersData ? usersData.length : 0} users`)
        
        // Enrich reviews with doctor and user names
        const enrichedReviews = reviewsData?.map(review => {
          const doctor = doctorsData?.find(d => d.doctor_id === review.doctor_id);
          const user = usersData?.find(u => u.user_id.toString() === review.user_id.toString());
          
          return {
            ...review,
            doctor_name: doctor?.fullname || `Doctor ID: ${review.doctor_id}`,
            user_name: user?.username || 
                      (user?.first_name && user?.last_name ? `${user.first_name} ${user.last_name}` : 
                      user?.email || `User ID: ${review.user_id}`),
            is_approved: review.is_approved !== undefined ? review.is_approved : true, // Default to approved
          }
        }) || [];
        
        setReviews(enrichedReviews)
        setFilteredReviews(enrichedReviews)
        setDoctors(doctorsData || [])
        setUsers(usersData || [])
      } catch (error) {
        console.error("Error loading reviews data:", error)
        setError("Failed to fetch review data. Check browser console for details.")
      } finally {
        setLoading(false)
      }
    }
    
    fetchData()
  }, [])
  
  // Apply filters
  useEffect(() => {
    let result = [...reviews]
    
    // Apply search query filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(review => 
        review.content?.toLowerCase().includes(query) ||
        review.doctor_fullname?.toLowerCase().includes(query) ||
        review.username?.toLowerCase().includes(query) ||
        review.additional_comments?.toLowerCase().includes(query)
      )
    }
    
    // Apply doctor filter
    if (doctorFilter !== "all") {
      result = result.filter(review => review.doctor_id === parseInt(doctorFilter))
    }
    
    // Apply approval filter
    if (approvalFilter !== "all") {
      const isApproved = approvalFilter === "approved"
      result = result.filter(review => review.is_approved === isApproved)
    }
    
    // Apply rating filter
    if (ratingFilter !== "all") {
      const rating = parseInt(ratingFilter)
      result = result.filter(review => review.rating === rating)
    }
    
    // Apply sorting
    switch (sortBy) {
      case "date-desc":
        result.sort((a, b) => {
          const dateA = a.review_date ? new Date(a.review_date).getTime() : 0
          const dateB = b.review_date ? new Date(b.review_date).getTime() : 0
          return dateB - dateA
        })
        break
      case "date-asc":
        result.sort((a, b) => {
          const dateA = a.review_date ? new Date(a.review_date).getTime() : 0
          const dateB = b.review_date ? new Date(b.review_date).getTime() : 0
          return dateA - dateB
        })
        break
      case "rating-desc":
        result.sort((a, b) => (b.rating || 0) - (a.rating || 0))
        break
      case "rating-asc":
        result.sort((a, b) => (a.rating || 0) - (b.rating || 0))
        break
      default:
        // Default to newest first
        result.sort((a, b) => {
          const dateA = a.review_date ? new Date(a.review_date).getTime() : 0
          const dateB = b.review_date ? new Date(b.review_date).getTime() : 0
          return dateB - dateA
        })
    }
    
    setFilteredReviews(result)
    setCurrentPage(1) // Reset to first page when filters change
  }, [searchQuery, doctorFilter, approvalFilter, ratingFilter, sortBy, reviews])
  
  // Get current page data
  const indexOfLastItem = currentPage * itemsPerPage
  const indexOfFirstItem = indexOfLastItem - itemsPerPage
  const currentReviews = filteredReviews.slice(indexOfFirstItem, indexOfLastItem)
  const totalPages = Math.ceil(filteredReviews.length / itemsPerPage)
  
  // Toggle review approval
  const handleToggleApproval = async (review: Review) => {
    try {
      const newApprovalStatus = !review.is_approved
      
      console.log(`Toggling review ${review.review_id} approval to ${newApprovalStatus ? 'approved' : 'rejected'}`)
      
      // First check if the review exists
      const { data: existingReview, error: checkError } = await supabase
        .from('reviews')
        .select('review_id, is_approved')
        .eq('review_id', review.review_id)
        .single()
      
      if (checkError) {
        console.error("Error finding review:", checkError)
        throw new Error(`Review with ID ${review.review_id} not found: ${checkError.message}`)
      }
      
      if (!existingReview) {
        throw new Error(`Review with ID ${review.review_id} not found`)
      }
      
      // Check if is_approved field exists in the database schema
      // If it doesn't exist, we need to alter our approach
      // First, attempt a simple update
      const { error } = await supabase
        .from('reviews')
        .update({ is_approved: newApprovalStatus })
        .eq('review_id', review.review_id)
      
      if (error) {
        console.error("Error updating review approval status:", error)
        
        // If error might be due to column not existing, try an alternate approach
        // Update with multiple columns to make sure at least one valid column is included
        if (error.message && (error.message.includes("column") || error.message.includes("field"))) {
          const updateData: any = {
            is_approved: newApprovalStatus
          }
          
          // Add one more column we know exists
          if (review.review_date) {
            updateData.review_date = review.review_date
          } else if (review.rating) {
            updateData.rating = review.rating
          }
          
          console.log("Trying alternative update with data:", updateData)
          
          const { error: altError } = await supabase
            .from('reviews')
            .update(updateData)
            .eq('review_id', review.review_id)
            
          if (altError) {
            console.error("Alternative update also failed:", altError)
            throw new Error(`Failed to update review approval status: ${altError.message}`)
          }
        } else {
          throw new Error(`Failed to update review approval status: ${error.message}`)
        }
      }
      
      console.log(`Review ${review.review_id} approval status updated successfully`)
      
      // Update the review in the state
      setReviews(reviews.map(r => 
        r.review_id === review.review_id ? { ...r, is_approved: newApprovalStatus } : r
      ))
      
      // Close view dialog if open
      if (showViewDialog && selectedReview && selectedReview.review_id === review.review_id) {
        setSelectedReview({...selectedReview, is_approved: newApprovalStatus})
      }
      
      // Clear any previous errors
      setError(null)
    } catch (error: any) {
      console.error("Error updating review approval status:", error)
      setError(`Failed to update review approval status: ${error.message || "Unknown error"}`)
    }
  }
  
  // Delete review
  const handleDeleteReview = async () => {
    if (!selectedReview) return
    
    try {
      console.log(`Deleting review with ID ${selectedReview.review_id}`)
      
      // First check if the review exists
      const { data: existingReview, error: checkError } = await supabase
        .from('reviews')
        .select('review_id')
        .eq('review_id', selectedReview.review_id)
        .single()
      
      if (checkError) {
        console.error("Error finding review:", checkError)
        throw new Error(`Review with ID ${selectedReview.review_id} not found: ${checkError.message}`)
      }
      
      if (!existingReview) {
        throw new Error(`Review with ID ${selectedReview.review_id} not found`)
      }
      
      // Proceed with deletion
      const { error } = await supabase
        .from('reviews')
        .delete()
        .eq('review_id', selectedReview.review_id)
      
      if (error) {
        console.error("Error deleting review:", error)
        throw new Error(`Failed to delete review: ${error.message}`)
      }
      
      console.log(`Review ${selectedReview.review_id} deleted successfully`)
      
      // Update the state by removing the deleted review
      setReviews(reviews.filter(review => review.review_id !== selectedReview.review_id))
      setShowDeleteDialog(false)
      setSelectedReview(null)
      
      // Clear any previous errors
      setError(null)
    } catch (error: any) {
      console.error("Error deleting review:", error)
      setError(`Failed to delete review: ${error.message || "Unknown error"}`)
    }
  }
  
  const formatReviewDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    } catch (e) {
      return dateString
    }
  }
  
  const renderStarRating = (rating: number) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map(star => (
          <Star 
            key={star} 
            className={`h-4 w-4 ${star <= rating ? 'text-amber-400 fill-amber-400' : 'text-muted-green'}`}
          />
        ))}
      </div>
    )
  }
  
  // Fetch full review details when a review is selected
  const handleViewReview = async (reviewId: number) => {
    try {
      console.log(`Fetching complete details for review ID: ${reviewId}`)
      
      // Get the review with all joined details
      const { data, error } = await supabase
        .from('reviews')
        .select(`
          *,
          users:user_id (username),
          doctors:doctor_id (fullname, specialty)
        `)
        .eq('review_id', reviewId)
        .single()
      
      if (error) {
        console.error("Error fetching review details:", error)
        throw new Error(`Failed to fetch review details: ${error.message}`)
      }
      
      if (!data) {
        throw new Error(`Review with ID ${reviewId} not found`)
      }
      
      console.log("Retrieved review details:", data)
      
      // Format the review data
      const formattedReview: Review = {
        ...data,
        username: data.users?.username || "Anonymous User",
        doctor_fullname: data.doctors?.fullname || "Unknown Doctor",
        specialty: data.doctors?.specialty || "Specialty not specified",
        // Convert numeric ratings to descriptive text for display
        wait_time: formatRatingToText(data.time_management),
        bedside_manner: formatRatingToText(data.empathy_compassion),
        availability: formatRatingToText(data.follow_up_care),
        content: data.additional_comments || "No review content provided",
        would_recommend: data.overall_satisfaction >= 3, // Assuming ratings of 3 or higher indicate recommendation
        created_at: data.review_date,
        title: `Review for ${data.doctors?.fullname || "Unknown Doctor"}`
      }
      
      setSelectedReview(formattedReview)
      setShowViewDialog(true)
    } catch (error: any) {
      console.error("Error viewing review:", error)
      setError(`Failed to view review: ${error.message || "Unknown error"}`)
    }
  }
  
  // Helper function to convert numeric ratings to text descriptions
  const formatRatingToText = (rating?: number): string => {
    if (rating === undefined) return "Not specified"
    
    switch(rating) {
      case 1: return "Poor"
      case 2: return "Fair"
      case 3: return "Average"
      case 4: return "Good"
      case 5: return "Excellent"
      default: return "Not specified"
    }
  }
  
  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin h-12 w-12 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Reviews Management</h1>
      </div>
      
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}
      
      {/* Filters & Search */}
      <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-green" />
            <Input
              placeholder="Search reviews by text, doctor, or patient..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <div className="flex flex-wrap gap-2">
          <Select value={doctorFilter} onValueChange={setDoctorFilter}>
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder="Filter by Doctor" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Doctors</SelectItem>
              {doctors.map((doctor) => (
                <SelectItem key={doctor.doctor_id} value={doctor.doctor_id.toString()}>
                  {doctor.fullname}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={approvalFilter} onValueChange={setApprovalFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Approval Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={ratingFilter} onValueChange={setRatingFilter}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Rating" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Ratings</SelectItem>
              <SelectItem value="5">5 Stars</SelectItem>
              <SelectItem value="4">4 Stars</SelectItem>
              <SelectItem value="3">3 Stars</SelectItem>
              <SelectItem value="2">2 Stars</SelectItem>
              <SelectItem value="1">1 Star</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Sort By" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="date-desc">Newest First</SelectItem>
              <SelectItem value="date-asc">Oldest First</SelectItem>
              <SelectItem value="rating-desc">Highest Rating</SelectItem>
              <SelectItem value="rating-asc">Lowest Rating</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Reviews Table */}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Doctor</TableHead>
            <TableHead>Patient</TableHead>
            <TableHead>Rating</TableHead>
            <TableHead>Review</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {currentReviews.length > 0 ? (
            currentReviews.map((review) => (
              <TableRow key={review.review_id}>
                <TableCell>
                  <div className="font-medium">{review.doctor_name}</div>
                </TableCell>
                <TableCell>
                  <div className="font-medium">{review.user_name}</div>
                </TableCell>
                <TableCell>
                  {renderStarRating(review.rating)}
                </TableCell>
                <TableCell>
                  <div className="max-w-xs truncate">
                    {review.review_text || "No text provided"}
                  </div>
                </TableCell>
                <TableCell>
                  {formatReviewDate(review.review_date)}
                </TableCell>
                <TableCell>
                  <Badge variant={review.is_approved ? 'outline' : 'secondary'} className={
                    review.is_approved
                      ? 'bg-green-100 text-green-800 hover:bg-green-100'
                      : 'bg-red-100 text-red-800 hover:bg-red-100'
                  }>
                    {review.is_approved ? 'Approved' : 'Rejected'}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewReview(review.review_id)}>
                        <EyeIcon className="mr-2 h-4 w-4" />
                        <span>View Full Review</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleToggleApproval(review)}>
                        {review.is_approved ? (
                          <>
                            <X className="mr-2 h-4 w-4" />
                            <span>Reject Review</span>
                          </>
                        ) : (
                          <>
                            <Check className="mr-2 h-4 w-4" />
                            <span>Approve Review</span>
                          </>
                        )}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => {
                        setSelectedReview(review)
                        setShowDeleteDialog(true)
                      }}>
                        <Trash2 className="mr-2 h-4 w-4" />
                        <span>Delete Review</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={7} className="h-32 text-center">
                No reviews found matching the criteria
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      
      {/* Pagination */}
      {filteredReviews.length > 0 && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious 
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  setCurrentPage(prev => Math.max(prev - 1, 1));
                }}
                aria-disabled={currentPage === 1}
                tabIndex={currentPage === 1 ? -1 : 0}
                className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
              />
            </PaginationItem>
            
            {[...Array(totalPages)].map((_, i) => {
              const page = i + 1;
              // Show only current page, first, last, and adjacent pages
              if (
                page === 1 || 
                page === totalPages || 
                (page >= currentPage - 1 && page <= currentPage + 1)
              ) {
                return (
                  <PaginationItem key={page}>
                    <PaginationLink
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        setCurrentPage(page);
                      }}
                      isActive={page === currentPage}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                );
              }
              
              // Show ellipsis for gaps in pagination
              if (page === 2 && currentPage > 3) {
                return <PaginationItem key="ellipsis-start">...</PaginationItem>;
              }
              
              if (page === totalPages - 1 && currentPage < totalPages - 2) {
                return <PaginationItem key="ellipsis-end">...</PaginationItem>;
              }
              
              return null;
            })}
            
            <PaginationItem>
              <PaginationNext 
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  setCurrentPage(prev => Math.min(prev + 1, totalPages));
                }}
                aria-disabled={currentPage === totalPages}
                tabIndex={currentPage === totalPages ? -1 : 0}
                className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Review Deletion</DialogTitle>
          </DialogHeader>
          <p className="text-sm text-muted-green">
            Are you sure you want to delete this review? This action cannot be undone.
          </p>
          {selectedReview && (
            <div className="bg-green-50 p-3 rounded-md mt-2">
              <p className="font-medium">{selectedReview.doctor_name}</p>
              <div className="flex items-center gap-2 my-1">
                {renderStarRating(selectedReview.rating)}
                <span className="text-sm text-muted-green">
                  by {selectedReview.user_name} on {formatReviewDate(selectedReview.review_date)}
                </span>
              </div>
              <p className="text-sm">{selectedReview.review_text}</p>
            </div>
          )}
          <div className="flex justify-end gap-3 mt-4">
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button 
              variant="outline" 
              className="bg-red-600 hover:bg-red-700 text-foreground" 
              onClick={handleDeleteReview}
            >
              Delete Review
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      
      {/* View Review Dialog */}
      <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl">Full Review Details</DialogTitle>
          </DialogHeader>
          
          {selectedReview && (
            <div className="space-y-6">
              <div className="flex flex-col md:flex-row justify-between gap-4">
                <div>
                  <h3 className="font-medium text-lg">
                    {selectedReview.doctor_fullname || "Unknown Doctor"}
                  </h3>
                  <p className="text-sm text-muted-green">
                    {selectedReview.specialty || "Specialty not specified"}
                  </p>
                </div>
                
                <div className="flex flex-col items-end">
                  <div className="flex items-center gap-1">
                    <Star className={`h-5 w-5 ${selectedReview.rating && selectedReview.rating >= 1 ? "fill-amber-400 text-amber-400" : "text-muted-green"}`} />
                    <Star className={`h-5 w-5 ${selectedReview.rating && selectedReview.rating >= 2 ? "fill-amber-400 text-amber-400" : "text-muted-green"}`} />
                    <Star className={`h-5 w-5 ${selectedReview.rating && selectedReview.rating >= 3 ? "fill-amber-400 text-amber-400" : "text-muted-green"}`} />
                    <Star className={`h-5 w-5 ${selectedReview.rating && selectedReview.rating >= 4 ? "fill-amber-400 text-amber-400" : "text-muted-green"}`} />
                    <Star className={`h-5 w-5 ${selectedReview.rating && selectedReview.rating >= 5 ? "fill-amber-400 text-amber-400" : "text-muted-green"}`} />
                    <span className="ml-2 font-bold">{selectedReview.rating || 0}</span>
                  </div>
                  <p className="text-xs text-muted-green">
                    Submitted on {selectedReview.created_at ? new Date(selectedReview.created_at).toLocaleDateString() : "Unknown date"}
                  </p>
                </div>
              </div>
              
              <div className="border rounded-lg p-4 bg-muted-green/20">
                <div className="flex items-center gap-2 mb-3">
                  <UserCircle className="h-6 w-6 text-muted-green" />
                  <div>
                    <p className="font-medium">{selectedReview.username || "Anonymous User"}</p>
                    <p className="text-xs text-muted-green">User ID: {selectedReview.user_id || "Unknown"}</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  {selectedReview.title && (
                    <div>
                      <h4 className="font-medium text-sm">Title:</h4>
                      <p className="text-base">{selectedReview.title}</p>
                    </div>
                  )}
                  
                  <div>
                    <h4 className="font-medium text-sm">Review:</h4>
                    <p className="text-base whitespace-pre-line">{selectedReview.content || "No review content provided"}</p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
                    <div>
                      <h4 className="font-medium text-sm">Clinical Competence:</h4>
                      <div className="flex items-center">
                        <p>{formatRatingToText(selectedReview.clinical_competence)}</p>
                        <span className="ml-2 text-xs text-muted-green">
                          {selectedReview.clinical_competence ? `(${selectedReview.clinical_competence}/5)` : ""}
                        </span>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-sm">Communication:</h4>
                      <div className="flex items-center">
                        <p>{formatRatingToText(selectedReview.communication_stats)}</p>
                        <span className="ml-2 text-xs text-muted-green">
                          {selectedReview.communication_stats ? `(${selectedReview.communication_stats}/5)` : ""}
                        </span>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-sm">Empathy & Compassion:</h4>
                      <div className="flex items-center">
                        <p>{formatRatingToText(selectedReview.empathy_compassion)}</p>
                        <span className="ml-2 text-xs text-muted-green">
                          {selectedReview.empathy_compassion ? `(${selectedReview.empathy_compassion}/5)` : ""}
                        </span>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-sm">Time Management:</h4>
                      <div className="flex items-center">
                        <p>{formatRatingToText(selectedReview.time_management)}</p>
                        <span className="ml-2 text-xs text-muted-green">
                          {selectedReview.time_management ? `(${selectedReview.time_management}/5)` : ""}
                        </span>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-sm">Follow-up Care:</h4>
                      <div className="flex items-center">
                        <p>{formatRatingToText(selectedReview.follow_up_care)}</p>
                        <span className="ml-2 text-xs text-muted-green">
                          {selectedReview.follow_up_care ? `(${selectedReview.follow_up_care}/5)` : ""}
                        </span>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-sm">Overall Satisfaction:</h4>
                      <div className="flex items-center">
                        <p>{formatRatingToText(selectedReview.overall_satisfaction)}</p>
                        <span className="ml-2 text-xs text-muted-green">
                          {selectedReview.overall_satisfaction ? `(${selectedReview.overall_satisfaction}/5)` : ""}
                        </span>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-sm">Recommendation:</h4>
                      <p>{selectedReview.would_recommend ? "Yes" : "No"}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-between items-center pt-4 border-t">
                <Badge variant={selectedReview.is_approved ? "default" : "outline"}>
                  {selectedReview.is_approved ? "Approved" : "Pending Approval"}
                </Badge>
                
                <div className="flex gap-3">
                  <Button variant="outline" onClick={() => setShowViewDialog(false)}>
                    Close
                  </Button>
                  <Button 
                    variant={selectedReview.is_approved ? "outline" : "default"}
                    className={selectedReview.is_approved ? "text-red-600 border-red-200 hover:bg-red-50" : ""}
                    onClick={() => {
                      handleToggleApproval(selectedReview)
                      setShowViewDialog(false)
                    }}
                  >
                    {selectedReview.is_approved ? "Reject Review" : "Approve Review"}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
} 