"use client"

import { useState } from "react"
import { 
  Save,
  RefreshCw,
  Mail,
  Globe,
  Shield,
  FileText,
  Image,
  AlertCircle
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { supabase } from "@/lib/supabase-client"

export default function SettingsPage() {
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [saveSuccess, setSaveSuccess] = useState<boolean>(false)
  
  // Site settings form
  const [siteSettings, setSiteSettings] = useState({
    siteName: "Doctor's League",
    siteDescription: "Find and connect with the best doctors in your area",
    contactEmail: "<EMAIL>",
    enableReviews: true,
    enableDoctorRegistration: true,
    reviewApprovalRequired: true,
    doctorVerificationRequired: true,
    maxReviewsPerPatient: 1
  })
  
  const handleSiteSettingsChange = (field: string, value: any) => {
    setSiteSettings({
      ...siteSettings,
      [field]: value
    })
    
    // Clear success message when form is modified
    if (saveSuccess) {
      setSaveSuccess(false)
    }
  }
  
  const saveSiteSettings = async () => {
    try {
      setIsSaving(true)
      setError(null)
      setSaveSuccess(false)
      
      console.log("Saving site settings with Supabase client")
      
      // Check if settings table exists
      const { count, error: countError } = await supabase
        .from('site_settings')
        .select('*', { count: 'exact', head: true })
      
      if (countError) {
        console.error("Error checking settings table:", countError)
        throw countError
      }
      
      let settingsError = null
      
      // If settings exist, update them
      if (count && count > 0) {
        console.log("Updating existing site settings")
        
        const { error } = await supabase
          .from('site_settings')
          .update({
            settings: siteSettings,
            updated_at: new Date().toISOString()
          })
          .eq('id', 1) // Assuming the primary settings have ID 1
        
        settingsError = error
      } else {
        // Otherwise create new settings
        console.log("Creating new site settings record")
        
        const { error } = await supabase
          .from('site_settings')
          .insert({
            settings: siteSettings,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
        
        settingsError = error
      }
      
      if (settingsError) {
        console.error("Error saving settings:", settingsError)
        throw settingsError
      }
      
      console.log("Settings saved successfully:", siteSettings)
      setSaveSuccess(true)
    } catch (error) {
      console.error("Error saving settings:", error)
      setError("There was a problem saving your settings. Please try again.")
    } finally {
      setIsSaving(false)
    }
  }
  
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Admin Settings</h1>
        <p className="text-muted-green mt-2">
          Configure your application settings and preferences
        </p>
      </div>
      
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}
      
      {saveSuccess && (
        <Alert className="bg-green-50 border-green-200">
          <AlertCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-600">
            Settings saved successfully!
          </AlertDescription>
        </Alert>
      )}
      
      <Tabs defaultValue="general">
        <TabsList className="grid w-full grid-cols-4 mb-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="reviews">Reviews</TabsTrigger>
          <TabsTrigger value="accounts">Accounts</TabsTrigger>
          <TabsTrigger value="email">Email</TabsTrigger>
        </TabsList>
        
        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Globe className="mr-2 h-5 w-5" />
                Site Information
              </CardTitle>
              <CardDescription>
                Basic information about your website that appears across the platform
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="siteName">Site Name</Label>
                  <Input
                    id="siteName"
                    value={siteSettings.siteName}
                    onChange={(e) => handleSiteSettingsChange('siteName', e.target.value)}
                  />
                  <p className="text-sm text-muted-green">
                    This is the name used throughout your site and in the browser title
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contactEmail">Contact Email</Label>
                  <Input
                    id="contactEmail"
                    type="email"
                    value={siteSettings.contactEmail}
                    onChange={(e) => handleSiteSettingsChange('contactEmail', e.target.value)}
                  />
                  <p className="text-sm text-muted-green">
                    Primary contact email displayed on the site
                  </p>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="siteDescription">Site Description</Label>
                <Textarea
                  id="siteDescription"
                  rows={3}
                  value={siteSettings.siteDescription}
                  onChange={(e) => handleSiteSettingsChange('siteDescription', e.target.value)}
                />
                <p className="text-sm text-muted-green">
                  This appears in search engines and when sharing your site
                </p>
              </div>
            </CardContent>
            <CardFooter className="border-t px-6 py-4">
              <Button onClick={saveSiteSettings} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="reviews" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                Review Settings
              </CardTitle>
              <CardDescription>
                Configure how reviews are handled on your platform
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Enable Reviews</Label>
                  <p className="text-sm text-muted-green">
                    Allow patients to leave reviews for doctors
                  </p>
                </div>
                <Switch
                  checked={siteSettings.enableReviews}
                  onCheckedChange={(checked) => handleSiteSettingsChange('enableReviews', checked)}
                />
              </div>
              
              <Separator />
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Review Approval Required</Label>
                  <p className="text-sm text-muted-green">
                    Reviews must be approved by an admin before being published
                  </p>
                </div>
                <Switch
                  checked={siteSettings.reviewApprovalRequired}
                  onCheckedChange={(checked) => handleSiteSettingsChange('reviewApprovalRequired', checked)}
                />
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <Label htmlFor="maxReviewsPerPatient">Max Reviews Per Patient</Label>
                <Input
                  id="maxReviewsPerPatient"
                  type="number"
                  min="1"
                  max="5"
                  value={siteSettings.maxReviewsPerPatient}
                  onChange={(e) => handleSiteSettingsChange('maxReviewsPerPatient', parseInt(e.target.value))}
                  className="w-20"
                />
                <p className="text-sm text-muted-green">
                  Maximum number of reviews a patient can leave for one doctor
                </p>
              </div>
            </CardContent>
            <CardFooter className="border-t px-6 py-4">
              <Button onClick={saveSiteSettings} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="accounts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="mr-2 h-5 w-5" />
                Account Settings
              </CardTitle>
              <CardDescription>
                Configure user account and registration settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Enable Doctor Registration</Label>
                  <p className="text-sm text-muted-green">
                    Allow doctors to register and create profiles on your platform
                  </p>
                </div>
                <Switch
                  checked={siteSettings.enableDoctorRegistration}
                  onCheckedChange={(checked) => handleSiteSettingsChange('enableDoctorRegistration', checked)}
                />
              </div>
              
              <Separator />
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Doctor Verification Required</Label>
                  <p className="text-sm text-muted-green">
                    Doctor profiles must be verified by an admin before being published
                  </p>
                </div>
                <Switch
                  checked={siteSettings.doctorVerificationRequired}
                  onCheckedChange={(checked) => handleSiteSettingsChange('doctorVerificationRequired', checked)}
                />
              </div>
            </CardContent>
            <CardFooter className="border-t px-6 py-4">
              <Button onClick={saveSiteSettings} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="email" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Mail className="mr-2 h-5 w-5" />
                Email Settings
              </CardTitle>
              <CardDescription>
                Configure email notifications and templates
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="flex items-center justify-center h-40 border-2 border-dashed rounded-md">
                <p className="text-muted-green">
                  Email configuration will be available in a future update
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 