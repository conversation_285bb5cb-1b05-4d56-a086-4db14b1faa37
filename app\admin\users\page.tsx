"use client"

import { useState, useEffect } from "react"
import { 
  Search, 
  UserPlus, 
  Check, 
  X, 
  Edit, 
  Trash2,
  MoreHorizontal,
  AlertCircle,
  Plus
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert"
import { supabase } from "@/lib/supabase-client"

type User = {
  user_id: number
  auth_id?: string
  email?: string
  username?: string
  first_name?: string
  last_name?: string
  gender?: string
  city?: string
  country?: string
  age?: number
  "Medical Condition"?: string
  "Registration date"?: string
  State?: string
  Phone_Number?: number
  is_active?: boolean
  created_at?: string
  last_login?: string
  user_type?: string
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([])
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [formValues, setFormValues] = useState<Partial<User>>({})
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [newUserData, setNewUserData] = useState<Partial<User>>({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    gender: '',
    age: undefined,
    city: '',
    country: '',
    Phone_Number: undefined,
    State: '',
    "Medical Condition": ''
  })
  
  const itemsPerPage = 10
  
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true)
        setError(null)
        
        console.log("Fetching users with Supabase client")
        
        // Basic query to get all users from the users table
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .order('user_id', { ascending: false })
        
        if (error) {
          console.error("Error fetching users:", error)
          throw error
        }
        
        console.log("Fetched users:", data ? data.length : 0)
        
        // Add is_active field if not present
        const usersWithStatus = data?.map(user => ({
          ...user,
          is_active: user.is_active !== undefined ? user.is_active : true, // Default to active
          created_at: user.created_at || user["Registration date"] || new Date().toISOString()
        })) || []
        
        setUsers(usersWithStatus)
        setFilteredUsers(usersWithStatus)
      } catch (error) {
        console.error("Error processing users:", error)
        setError("Failed to fetch users. Check browser console for details.")
      } finally {
        setLoading(false)
      }
    }
    
    fetchUsers()
  }, [])
  
  useEffect(() => {
    let result = [...users]
    
    // Apply search query filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(user => 
        user.username?.toLowerCase().includes(query) ||
        user.email?.toLowerCase().includes(query) ||
        user.first_name?.toLowerCase().includes(query) ||
        user.last_name?.toLowerCase().includes(query) ||
        user.city?.toLowerCase().includes(query) ||
        user.country?.toLowerCase().includes(query) ||
        (user["Medical Condition"] && user["Medical Condition"].toLowerCase().includes(query))
      )
    }
    
    // Apply status filter
    if (statusFilter !== "all") {
      const isActive = statusFilter === "active"
      result = result.filter(user => user.is_active === isActive)
    }
    
    setFilteredUsers(result)
    setCurrentPage(1) // Reset to first page when filters change
  }, [searchQuery, statusFilter, users])
  
  // Get current page data
  const indexOfLastItem = currentPage * itemsPerPage
  const indexOfFirstItem = indexOfLastItem - itemsPerPage
  const currentUsers = filteredUsers.slice(indexOfFirstItem, indexOfLastItem)
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage)
  
  const handleDeleteUser = async () => {
    if (!selectedUser) return
    
    try {
      console.log(`Deleting user with ID ${selectedUser.user_id}`)
      
      // Check if the user exists first
      const { data: existingUser, error: checkError } = await supabase
        .from('users')
        .select('user_id')
        .eq('user_id', selectedUser.user_id)
        .single()
      
      if (checkError) {
        console.error("Error finding user:", checkError)
        throw new Error(`User with ID ${selectedUser.user_id} not found: ${checkError.message}`)
      }
      
      if (!existingUser) {
        throw new Error(`User with ID ${selectedUser.user_id} not found`)
      }
      
      // Delete from users table
      const { error: userError } = await supabase
        .from('users')
        .delete()
        .eq('user_id', selectedUser.user_id)
      
      if (userError) {
        console.error("Error deleting user:", userError)
        throw new Error(`Failed to delete user: ${userError.message}`)
      }
      
      console.log(`User ${selectedUser.user_id} deleted successfully`)
      
      // Update the UI by removing the deleted user
      setUsers(users.filter(user => user.user_id !== selectedUser.user_id))
      setShowDeleteDialog(false)
      setSelectedUser(null)
      
      // Clear any previous errors
      setError(null)
    } catch (error: any) {
      console.error("Error deleting user:", error)
      setError(`Failed to delete user: ${error.message || "Unknown error"}`)
    }
  }
  
  const handleToggleUserStatus = async (user: User) => {
    try {
      const newStatus = !(user.is_active ?? true)
      
      console.log(`Toggling user ${user.user_id} status to ${newStatus ? 'active' : 'inactive'}`)
      
      // Check if the user exists first
      const { data: existingUser, error: checkError } = await supabase
        .from('users')
        .select('user_id, is_active')
        .eq('user_id', user.user_id)
        .single()
      
      if (checkError) {
        console.error("Error finding user:", checkError)
        throw new Error(`User with ID ${user.user_id} not found: ${checkError.message}`)
      }
      
      if (!existingUser) {
        throw new Error(`User with ID ${user.user_id} not found`)
      }
      
      // Prepare update data - only include columns that definitely exist
      const updateData: any = {}
      
      // Add a column that definitely exists in the schema plus is_active
      updateData.is_active = newStatus
      
      // If the user has an email, include it in the update to ensure at least one valid column
      if (user.email) {
        updateData.email = user.email
      }
      
      console.log("Update data:", updateData)
      
      // Perform the update
      const { error } = await supabase
        .from('users')
        .update(updateData)
        .eq('user_id', user.user_id)
      
      if (error) {
        console.error("Error updating user status:", error)
        throw new Error(`Failed to update user status: ${error.message}`)
      }
      
      console.log(`User ${user.user_id} status updated successfully`)
      
      // Update the user in the state
      setUsers(users.map(u => 
        u.user_id === user.user_id ? { ...u, is_active: newStatus } : u
      ))
      
      // Clear any previous errors
      setError(null)
    } catch (error: any) {
      console.error("Error updating user status:", error)
      setError(`Failed to update user status: ${error.message || "Unknown error"}`)
    }
  }
  
  const handleAddUser = async () => {
    try {
      console.log("Adding new user with data:", newUserData)
      
      // Validate required fields
      if (!newUserData.username || !newUserData.email) {
        throw new Error("Username and email are required")
      }
      
      // Generate a random password for new users (they can reset it later)
      const randomPassword = Math.random().toString(36).slice(-8)
      
      // Prepare data for insertion
      const userData = {
        ...newUserData,
        password: randomPassword, // In a real app, this should be hashed
        user_type: 'patient',
        "Registration date": new Date().toISOString()
      }
      
      // Insert the new user
      const { data, error } = await supabase
        .from('users')
        .insert([userData])
        .select()
      
      if (error) {
        console.error("Error adding user:", error)
        throw new Error(`Failed to add user: ${error.message}`)
      }
      
      console.log("User added successfully:", data)
      
      // Update local state to include the new user
      if (data && data.length > 0) {
        setUsers([...users, data[0]])
      }
      
      // Close dialog and reset form
      setShowAddDialog(false)
      setNewUserData({
        username: '',
        email: '',
        first_name: '',
        last_name: '',
        gender: '',
        age: undefined,
        city: '',
        country: '',
        Phone_Number: undefined,
        State: '',
        "Medical Condition": ''
      })
      
      // Clear any previous errors
      setError(null)
    } catch (error: any) {
      console.error("Error adding user:", error)
      setError(`Failed to add user: ${error.message || "Unknown error"}`)
    }
  }
  
  const handleEditUser = async () => {
    if (!editingUser) return
    
    try {
      console.log(`Updating user with ID ${editingUser.user_id}`)
      
      // Check if the user exists first
      const { data: existingUser, error: checkError } = await supabase
        .from('users')
        .select('user_id')
        .eq('user_id', editingUser.user_id)
        .single()
      
      if (checkError) {
        console.error("Error finding user:", checkError)
        throw new Error(`User with ID ${editingUser.user_id} not found: ${checkError.message}`)
      }
      
      if (!existingUser) {
        throw new Error(`User with ID ${editingUser.user_id} not found`)
      }
      
      // Prepare update data with form values
      const updateData = { ...formValues }
      
      console.log("Updating user with data:", updateData)
      
      // Perform the update
      const { error } = await supabase
        .from('users')
        .update(updateData)
        .eq('user_id', editingUser.user_id)
      
      if (error) {
        console.error("Error updating user:", error)
        throw new Error(`Failed to update user: ${error.message}`)
      }
      
      console.log(`User ${editingUser.user_id} updated successfully`)
      
      // Update the user in the state
      setUsers(users.map(u => 
        u.user_id === editingUser.user_id ? { ...u, ...updateData } : u
      ))
      
      // Close dialog and clear form
      setShowEditDialog(false)
      setEditingUser(null)
      setFormValues({})
      
      // Clear any previous errors
      setError(null)
    } catch (error: any) {
      console.error("Error updating user:", error)
      setError(`Failed to update user: ${error.message || "Unknown error"}`)
    }
  }
  
  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin h-12 w-12 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Patients Management</h1>
        <Button onClick={() => setShowAddDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Patient
        </Button>
      </div>
      
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}
      
      {/* Filters & Search */}
      <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-green" />
            <Input
              placeholder="Search patients by name, email, location..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <div className="flex space-x-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Users Table */}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Patient</TableHead>
            <TableHead>Demographics</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Registered</TableHead>
            <TableHead>Medical Condition</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {currentUsers.length > 0 ? (
            currentUsers.map((user) => (
              <TableRow key={user.user_id}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>
                        {user.username?.[0] || user.first_name?.[0] || 'P'}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium leading-none">
                        {user.first_name && user.last_name
                          ? `${user.first_name} ${user.last_name}`
                          : user.username || 'Unknown'}
                      </p>
                      <p className="text-sm text-muted-green">{user.email || 'No email'}</p>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    <p>{user.gender ? `${user.gender}, ${user.age || '?'}` : 'Not specified'}</p>
                    <p className="text-muted-green">{[user.city, user.country].filter(Boolean).join(', ') || 'Location unknown'}</p>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={user.is_active ? 'secondary' : 'outline'} className={
                    user.is_active 
                      ? 'bg-green-100 text-green-800 hover:bg-green-100' 
                      : 'bg-red-100 text-red-800 hover:bg-red-100'
                  }>
                    {user.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                </TableCell>
                <TableCell>
                  {user["Registration date"] 
                    ? new Date(user["Registration date"]).toLocaleDateString() 
                    : user.created_at 
                      ? new Date(user.created_at).toLocaleDateString()
                      : 'Unknown'}
                </TableCell>
                <TableCell>
                  {user["Medical Condition"] || 'None recorded'}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleToggleUserStatus(user)}>
                        {user.is_active ? (
                          <>
                            <X className="mr-2 h-4 w-4" />
                            <span>Deactivate</span>
                          </>
                        ) : (
                          <>
                            <Check className="mr-2 h-4 w-4" />
                            <span>Activate</span>
                          </>
                        )}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => {
                        setEditingUser(user)
                        setFormValues({
                          username: user.username || '',
                          email: user.email || '',
                          first_name: user.first_name || '',
                          last_name: user.last_name || '',
                          gender: user.gender || '',
                          age: user.age,
                          city: user.city || '',
                          country: user.country || '',
                          "Medical Condition": user["Medical Condition"] || '',
                          State: user.State || '',
                          Phone_Number: user.Phone_Number
                        })
                        setShowEditDialog(true)
                      }}>
                        <Edit className="mr-2 h-4 w-4" />
                        <span>Edit</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => {
                        setSelectedUser(user)
                        setShowDeleteDialog(true)
                      }}>
                        <Trash2 className="mr-2 h-4 w-4" />
                        <span>Delete</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={6} className="h-32 text-center">
                No patients found matching the criteria
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      
      {/* Pagination */}
      {filteredUsers.length > 0 && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious 
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  setCurrentPage(prev => Math.max(prev - 1, 1));
                }}
                size="default"
                aria-disabled={currentPage === 1}
                tabIndex={currentPage === 1 ? -1 : 0}
                className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
              />
            </PaginationItem>
            
            {[...Array(totalPages)].map((_, i) => {
              const page = i + 1;
              // Show only current page, first, last, and adjacent pages
              if (
                page === 1 || 
                page === totalPages || 
                (page >= currentPage - 1 && page <= currentPage + 1)
              ) {
                return (
                  <PaginationItem key={page}>
                    <PaginationLink
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        setCurrentPage(page);
                      }}
                      size="default"
                      isActive={page === currentPage}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                );
              }
              
              // Show ellipsis for gaps in pagination
              if (page === 2 && currentPage > 3) {
                return <PaginationItem key="ellipsis-start">...</PaginationItem>;
              }
              
              if (page === totalPages - 1 && currentPage < totalPages - 2) {
                return <PaginationItem key="ellipsis-end">...</PaginationItem>;
              }
              
              return null;
            })}
            
            <PaginationItem>
              <PaginationNext 
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  setCurrentPage(prev => Math.min(prev + 1, totalPages));
                }}
                size="default"
                aria-disabled={currentPage === totalPages}
                tabIndex={currentPage === totalPages ? -1 : 0}
                className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Patient Deletion</DialogTitle>
            <p className="text-sm text-muted-green">
              Are you sure you want to delete the patient "{selectedUser?.username || selectedUser?.email || `ID: ${selectedUser?.user_id}`}"? 
              This action cannot be undone and will permanently remove the patient and all associated data.
            </p>
          </DialogHeader>
          <div className="flex justify-end gap-3 mt-4">
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button variant="outline" className="bg-red-600 hover:bg-red-700 text-foreground" onClick={handleDeleteUser}>
              Delete Patient
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      
      {/* Edit User Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="username" className="text-right text-sm font-medium">
                Username
              </label>
              <Input
                id="username"
                className="col-span-3"
                value={formValues.username || ''}
                onChange={(e) => setFormValues({...formValues, username: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="email" className="text-right text-sm font-medium">
                Email
              </label>
              <Input
                id="email"
                type="email"
                className="col-span-3"
                value={formValues.email || ''}
                onChange={(e) => setFormValues({...formValues, email: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="firstName" className="text-right text-sm font-medium">
                First Name
              </label>
              <Input
                id="firstName"
                className="col-span-3"
                value={formValues.first_name || ''}
                onChange={(e) => setFormValues({...formValues, first_name: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="lastName" className="text-right text-sm font-medium">
                Last Name
              </label>
              <Input
                id="lastName"
                className="col-span-3"
                value={formValues.last_name || ''}
                onChange={(e) => setFormValues({...formValues, last_name: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="gender" className="text-right text-sm font-medium">
                Gender
              </label>
              <Select 
                value={formValues.gender || ''} 
                onValueChange={(value) => setFormValues({...formValues, gender: value})}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="age" className="text-right text-sm font-medium">
                Age
              </label>
              <Input
                id="age"
                type="number"
                className="col-span-3"
                value={formValues.age || ''}
                onChange={(e) => setFormValues({...formValues, age: e.target.value ? parseInt(e.target.value) : undefined})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="city" className="text-right text-sm font-medium">
                City
              </label>
              <Input
                id="city"
                className="col-span-3"
                value={formValues.city || ''}
                onChange={(e) => setFormValues({...formValues, city: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="state" className="text-right text-sm font-medium">
                State
              </label>
              <Input
                id="state"
                className="col-span-3"
                value={formValues.State || ''}
                onChange={(e) => setFormValues({...formValues, State: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="country" className="text-right text-sm font-medium">
                Country
              </label>
              <Input
                id="country"
                className="col-span-3"
                value={formValues.country || ''}
                onChange={(e) => setFormValues({...formValues, country: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="phone" className="text-right text-sm font-medium">
                Phone Number
              </label>
              <Input
                id="phone"
                type="number"
                className="col-span-3"
                value={formValues.Phone_Number || ''}
                onChange={(e) => setFormValues({...formValues, Phone_Number: e.target.value ? parseInt(e.target.value) : undefined})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-start gap-4">
              <label htmlFor="medicalCondition" className="text-right text-sm font-medium pt-2">
                Medical Condition
              </label>
              <textarea
                id="medicalCondition"
                className="col-span-3 min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-green focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                value={formValues["Medical Condition"] || ''}
                onChange={(e) => setFormValues({...formValues, "Medical Condition": e.target.value})}
              />
            </div>
          </div>
          <div className="flex justify-end gap-3 mt-4">
            <Button variant="outline" onClick={() => {
              setShowEditDialog(false)
              setFormValues({})
            }}>
              Cancel
            </Button>
            <Button onClick={handleEditUser}>
              Save Changes
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      
      {/* Add New Patient Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Patient</DialogTitle>
            <p className="text-sm text-muted-green">
              Create a new patient account in the system
            </p>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newUsername" className="text-right text-sm font-medium">
                Username*
              </label>
              <Input
                id="newUsername"
                className="col-span-3"
                value={newUserData.username || ''}
                onChange={(e) => setNewUserData({...newUserData, username: e.target.value})}
                required
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newEmail" className="text-right text-sm font-medium">
                Email*
              </label>
              <Input
                id="newEmail"
                type="email"
                className="col-span-3"
                value={newUserData.email || ''}
                onChange={(e) => setNewUserData({...newUserData, email: e.target.value})}
                required
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newFirstName" className="text-right text-sm font-medium">
                First Name
              </label>
              <Input
                id="newFirstName"
                className="col-span-3"
                value={newUserData.first_name || ''}
                onChange={(e) => setNewUserData({...newUserData, first_name: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newLastName" className="text-right text-sm font-medium">
                Last Name
              </label>
              <Input
                id="newLastName"
                className="col-span-3"
                value={newUserData.last_name || ''}
                onChange={(e) => setNewUserData({...newUserData, last_name: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newGender" className="text-right text-sm font-medium">
                Gender
              </label>
              <Select 
                value={newUserData.gender || ''} 
                onValueChange={(value) => setNewUserData({...newUserData, gender: value})}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newAge" className="text-right text-sm font-medium">
                Age
              </label>
              <Input
                id="newAge"
                type="number"
                className="col-span-3"
                value={newUserData.age || ''}
                onChange={(e) => setNewUserData({...newUserData, age: e.target.value ? parseInt(e.target.value) : undefined})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newCity" className="text-right text-sm font-medium">
                City
              </label>
              <Input
                id="newCity"
                className="col-span-3"
                value={newUserData.city || ''}
                onChange={(e) => setNewUserData({...newUserData, city: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newState" className="text-right text-sm font-medium">
                State
              </label>
              <Input
                id="newState"
                className="col-span-3"
                value={newUserData.State || ''}
                onChange={(e) => setNewUserData({...newUserData, State: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newCountry" className="text-right text-sm font-medium">
                Country
              </label>
              <Input
                id="newCountry"
                className="col-span-3"
                value={newUserData.country || ''}
                onChange={(e) => setNewUserData({...newUserData, country: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newPhone" className="text-right text-sm font-medium">
                Phone Number
              </label>
              <Input
                id="newPhone"
                type="number"
                className="col-span-3"
                value={newUserData.Phone_Number || ''}
                onChange={(e) => setNewUserData({...newUserData, Phone_Number: e.target.value ? parseInt(e.target.value) : undefined})}
              />
            </div>
            
            <div className="grid grid-cols-4 items-start gap-4">
              <label htmlFor="newMedicalCondition" className="text-right text-sm font-medium pt-2">
                Medical Condition
              </label>
              <textarea
                id="newMedicalCondition"
                className="col-span-3 min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-green focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                value={newUserData["Medical Condition"] || ''}
                onChange={(e) => setNewUserData({...newUserData, "Medical Condition": e.target.value})}
              />
            </div>
          </div>
          
          <div className="flex justify-end gap-3 mt-4">
            <Button variant="outline" onClick={() => {
              setShowAddDialog(false)
              setNewUserData({
                username: '',
                email: '',
                first_name: '',
                last_name: '',
                gender: '',
                age: undefined,
                city: '',
                country: '',
                Phone_Number: undefined,
                State: '',
                "Medical Condition": ''
              })
            }}>
              Cancel
            </Button>
            <Button onClick={handleAddUser}>
              Add Patient
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
} 