import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'

export const dynamic = 'force-dynamic'

// Add CORS headers for better compatibility
function corsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Content-Type': 'application/json'
  }
}

export async function OPTIONS() {
  return new NextResponse(null, { headers: corsHeaders() })
}

export async function POST(request: NextRequest) {
  // Set headers for proper response format
  const headers = {
    ...corsHeaders(),
    'Content-Type': 'application/json'
  }

  try {
    // Log the full request for debugging
    console.log('[API] Received password reset request')
    
    let requestData
    try {
      requestData = await request.json()
      console.log('[API] Request data:', requestData)
    } catch (parseError) {
      console.error('[API] Failed to parse request JSON:', parseError)
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400, headers }
      )
    }

    // For token validation
    const { token, password } = requestData

    // For direct email reset (this is kept for backward compatibility)
    const { email, redirectTo } = requestData

    // Get authentication environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://uapbzzscckhtptliynyj.supabase.co'
    const supabaseServiceRole = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q'

    // Create admin client
    console.log('[API] Creating Supabase admin client')
    const supabaseAdmin = createClient(
      supabaseUrl,
      supabaseServiceRole,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Check if we have a token (new custom flow) or email (legacy Supabase Auth flow)
    if (token && password) {
      // Handle custom password reset flow with token
      console.log('[API] Processing password reset using custom token')
      
      // Validate password
      if (password.length < 6) {
        return NextResponse.json(
          { error: 'Password must be at least 6 characters long' },
          { status: 400, headers }
        )
      }

      // Find the token in the database
      const { data: tokenData, error: tokenError } = await supabaseAdmin
        .from('password_reset_tokens')
        .select('*')
        .eq('token', token)
        .eq('used', false)
        .gt('expires_at', new Date().toISOString())
        .single()

      if (tokenError || !tokenData) {
        console.error('[API] Invalid or expired token:', tokenError)
        return NextResponse.json(
          { error: 'Invalid or expired reset token' },
          { status: 400, headers }
        )
      }

      // Hash the new password
      const salt = await bcrypt.genSalt(10)
      const hashedPassword = await bcrypt.hash(password, salt)

      // Update the user's password in auth_credentials
      const { error: updateError } = await supabaseAdmin
        .from('auth_credentials')
        .update({ hashed_password: hashedPassword })
        .eq('email', tokenData.email)

      if (updateError) {
        console.error('[API] Failed to update password:', updateError)
        return NextResponse.json(
          { error: 'Failed to update password' },
          { status: 500, headers }
        )
      }

      // Mark the token as used
      await supabaseAdmin
        .from('password_reset_tokens')
        .update({ used: true })
        .eq('id', tokenData.id)

      console.log('[API] Password reset successfully using token')
      return NextResponse.json(
        { success: true, message: 'Password has been reset successfully' },
        { headers }
      )
    } 
    else if (email) {
      // Legacy support for Supabase Auth flow
      console.log('[API] Using legacy Supabase Auth for password reset')
      console.log(`[API] Processing reset for: ${email}, redirect to: ${redirectTo}`)

      try {
        // Try using Supabase Auth (for backward compatibility)
        const { data, error } = await supabaseAdmin.auth.resetPasswordForEmail(email, {
          redirectTo: redirectTo || `${request.nextUrl.origin}/reset-success`,
        })

        if (error) {
          console.error('[API] Supabase auth error:', error)
          
          // Fall back to our custom reset flow
          return NextResponse.json(
            { 
              error: 'Password reset using Supabase Auth failed. Please use the custom reset flow.',
              redirect: '/forgot-password'
            },
            { status: 307, headers }
          )
        }

        console.log('[API] Password reset email sent successfully via Supabase Auth')
        return NextResponse.json({ success: true, message: 'Reset email sent' }, { headers })
      } catch (supabaseError) {
        console.error('[API] Error calling Supabase:', supabaseError)
        return NextResponse.json(
          { error: 'Failed to communicate with authentication service' },
          { status: 500, headers }
        )
      }
    }
    else {
      return NextResponse.json(
        { error: 'Invalid request: missing token/password or email' },
        { status: 400, headers }
      )
    }
  } catch (error: any) {
    console.error('[API] Unhandled error:', error)
    return NextResponse.json(
      { error: 'An unexpected server error occurred' },
      { status: 500, headers }
    )
  }
} 