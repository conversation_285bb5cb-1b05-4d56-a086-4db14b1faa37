import { NextResponse } from "next/server"
import { supabase } from "@/lib/supabase-client"

export async function GET() {
  try {
    // Test connection to Supabase
    const startTime = Date.now()
    const { data, error } = await supabase.from("countries").select("count", { count: "exact", head: true })
    const endTime = Date.now()

    if (error) {
      console.error("Supabase connection test failed:", error)
      return NextResponse.json({
        status: "error",
        message: error.message,
        responseTime: endTime - startTime,
      })
    }

    return NextResponse.json({
      status: "success",
      message: "Connected to Supabase successfully",
      responseTime: endTime - startTime,
    })
  } catch (error: any) {
    console.error("Supabase connection test error:", error)
    return NextResponse.json({
      status: "error",
      message: error.message || "Unknown error",
      responseTime: -1,
    })
  }
}

