import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase-server';
import { getAllDoctors } from '@/lib/hybrid-data-service';

export async function GET(request: NextRequest) {
  try {
    console.log("DEBUG API: Starting doctor debug check...");
    
    const supabase = createServerClient();
    
    // Test 1: Get first 3 doctors with all fields
    const { data: allDoctors, error: allError } = await supabase
      .from('doctors')
      .select('*')
      .limit(3);
    
    if (allError) {
      console.error("DEBUG API: Error fetching doctors:", allError);
      return NextResponse.json({ error: allError.message }, { status: 500 });
    }
    
    // Test 2: Check specific columns
    const { data: specificDoctors, error: specificError } = await supabase
      .from('doctors')
      .select('doctor_id, fullname, rating, community_rating, verified_rating, review_count')
      .limit(3);
    
    if (specificError) {
      console.error("DEBUG API: Error fetching specific columns:", specificError);
      return NextResponse.json({ error: specificError.message }, { status: 500 });
    }
    
    // Test 3: Check for doctors with non-null community_rating
    const { data: ratedDoctors, error: ratedError } = await supabase
      .from('doctors')
      .select('doctor_id, fullname, community_rating')
      .not('community_rating', 'is', null)
      .gt('community_rating', 0)
      .order('community_rating', { ascending: false })
      .limit(5);
    
    if (ratedError) {
      console.error("DEBUG API: Error fetching rated doctors:", ratedError);
    }

    // Test 4: Test getAllDoctors function from hybrid-data-service
    console.log("DEBUG API: Testing getAllDoctors function...");
    const allDoctorsFromService = await getAllDoctors();

    const result = {
      timestamp: new Date().toISOString(),
      allDoctors: {
        count: allDoctors?.length || 0,
        sample: allDoctors?.[0] ? {
          doctor_id: allDoctors[0].doctor_id,
          fullname: allDoctors[0].fullname,
          rating: allDoctors[0].rating,
          community_rating: allDoctors[0].community_rating,
          verified_rating: allDoctors[0].verified_rating,
          review_count: allDoctors[0].review_count,
          available_fields: Object.keys(allDoctors[0])
        } : null
      },
      specificDoctors: {
        count: specificDoctors?.length || 0,
        data: specificDoctors
      },
      ratedDoctors: {
        count: ratedDoctors?.length || 0,
        data: ratedDoctors,
        error: ratedError?.message || null
      },
      getAllDoctorsService: {
        count: allDoctorsFromService?.length || 0,
        sample: allDoctorsFromService?.[0] ? {
          doctor_id: allDoctorsFromService[0].doctor_id,
          fullname: allDoctorsFromService[0].fullname,
          rating: allDoctorsFromService[0].rating,
          community_rating: allDoctorsFromService[0].community_rating,
          verified_rating: allDoctorsFromService[0].verified_rating,
          review_count: allDoctorsFromService[0].review_count
        } : null,
        topRated: allDoctorsFromService?.slice(0, 5).map(d => ({
          fullname: d.fullname,
          community_rating: d.community_rating
        })) || []
      }
    };
    
    console.log("DEBUG API: Result:", JSON.stringify(result, null, 2));
    
    return NextResponse.json(result);
    
  } catch (error) {
    console.error("DEBUG API: Exception:", error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
