import { NextResponse } from 'next/server'

// Types for our fixtures data
export interface Team {
  id: string
  name: string
  score: number | null
  logo?: string
}

export interface Fixture {
  id: string
  title: string
  date: string
  location: string
  teams: Team[]
  type: string
  specialty: string
  status: 'upcoming' | 'completed' | 'live'
  league_id?: string
}

// Mock data - would be replaced with actual Supabase queries
const MOCK_FIXTURES: Fixture[] = [
  {
    id: '1',
    title: 'Cardiology Championship Finals',
    date: '2025-03-15T14:00:00',
    location: 'Mayo Clinic, Rochester',
    teams: [
      { id: '1', name: 'Heart Specialists Team', score: null },
      { id: '2', name: 'Cardiac Excellence Group', score: null }
    ],
    type: 'Championship',
    specialty: 'Cardiology',
    status: 'upcoming'
  },
  {
    id: '2',
    title: 'Neurology Division Semifinals',
    date: '2025-03-10T15:30:00',
    location: 'Johns Hopkins Hospital, Baltimore',
    teams: [
      { id: '3', name: 'Brain Trust Alliance', score: null },
      { id: '4', name: 'Neuro Innovators', score: null }
    ],
    type: 'Semifinal',
    specialty: 'Neurology',
    status: 'upcoming'
  },
  {
    id: '3',
    title: 'Orthopedics Quarterly Match',
    date: '2025-02-28T13:00:00',
    location: 'Hospital for Special Surgery, New York',
    teams: [
      { id: '5', name: 'Joint Specialists', score: 85 },
      { id: '6', name: 'Bone & Tissue Experts', score: 92 }
    ],
    type: 'Regular',
    specialty: 'Orthopedics',
    status: 'completed'
  },
  {
    id: '4',
    title: 'Pediatrics Annual Conference',
    date: '2025-04-05T10:00:00',
    location: 'Children\'s Hospital of Philadelphia',
    teams: [
      { id: '7', name: 'Child Wellness Team', score: null },
      { id: '8', name: 'Pediatric Pioneers', score: null }
    ],
    type: 'Conference',
    specialty: 'Pediatrics',
    status: 'upcoming'
  },
  {
    id: '5',
    title: 'Surgery Masters Challenge',
    date: '2025-02-20T09:00:00',
    location: 'Cleveland Clinic, Ohio',
    teams: [
      { id: '9', name: 'Precision Surgeons', score: 88 },
      { id: '10', name: 'Surgical Innovators', score: 91 }
    ],
    type: 'Challenge',
    specialty: 'Surgery',
    status: 'completed'
  },
  {
    id: '6',
    title: 'Dermatology Symposium',
    date: '2025-03-25T11:00:00',
    location: 'Massachusetts General Hospital, Boston',
    teams: [
      { id: '11', name: 'Skin Health Experts', score: null },
      { id: '12', name: 'Dermatological Research Team', score: null }
    ],
    type: 'Symposium',
    specialty: 'Dermatology',
    status: 'upcoming'
  },
  {
    id: '7',
    title: 'Internal Medicine League Match',
    date: '2025-02-15T16:00:00',
    location: 'UCLA Medical Center, Los Angeles',
    teams: [
      { id: '13', name: 'Diagnostic Specialists', score: 79 },
      { id: '14', name: 'Comprehensive Care Team', score: 82 }
    ],
    type: 'League',
    specialty: 'Internal Medicine',
    status: 'completed'
  },
  {
    id: '8',
    title: 'Ophthalmology Vision Cup',
    date: '2025-03-18T14:30:00',
    location: 'Wills Eye Hospital, Philadelphia',
    teams: [
      { id: '15', name: 'Vision Restoration Group', score: null },
      { id: '16', name: 'Eye Care Specialists', score: null }
    ],
    type: 'Cup',
    specialty: 'Ophthalmology',
    status: 'upcoming'
  },
  {
    id: '9',
    title: 'Cardiology Live Case Competition',
    date: '2025-03-01T10:00:00',
    location: 'Texas Heart Institute, Houston',
    teams: [
      { id: '17', name: 'Interventional Cardiology Team', score: 45 },
      { id: '18', name: 'Cardiac Imaging Specialists', score: 42 }
    ],
    type: 'Competition',
    specialty: 'Cardiology',
    status: 'live'
  },
  {
    id: '10',
    title: 'Neurosurgery Precision Challenge',
    date: '2025-03-05T09:30:00',
    location: 'UCSF Medical Center, San Francisco',
    teams: [
      { id: '19', name: 'Neurosurgical Precision Team', score: 94 },
      { id: '20', name: 'Brain Surgery Innovators', score: 89 }
    ],
    type: 'Challenge',
    specialty: 'Neurosurgery',
    status: 'completed'
  }
];

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    
    // Get all specialty parameters (can be multiple)
    const specialties = searchParams.getAll('specialty');
    
    // Get all type parameters (can be multiple)
    const types = searchParams.getAll('type');
    
    // Filter fixtures based on query parameters
    let filteredFixtures = [...MOCK_FIXTURES];
    
    if (status) {
      filteredFixtures = filteredFixtures.filter(fixture => fixture.status === status);
    }
    
    // Filter by specialties if provided (any match)
    if (specialties.length > 0) {
      filteredFixtures = filteredFixtures.filter(fixture => 
        specialties.some(specialty => 
          fixture.specialty.toLowerCase().includes(specialty.toLowerCase())
        )
      );
    }
    
    // Filter by types if provided (any match)
    if (types.length > 0) {
      filteredFixtures = filteredFixtures.filter(fixture => 
        types.some(type => 
          fixture.type.toLowerCase().includes(type.toLowerCase())
        )
      );
    }
    
    // Sort fixtures by date (most recent first for completed, soonest first for upcoming)
    filteredFixtures.sort((a, b) => {
      const dateA = new Date(a.date).getTime();
      const dateB = new Date(b.date).getTime();
      
      if (status === 'completed') {
        return dateB - dateA; // Most recent first for completed
      } else {
        return dateA - dateB; // Soonest first for upcoming
      }
    });
    
    return NextResponse.json({ 
      fixtures: filteredFixtures,
      count: filteredFixtures.length,
      status: 'success' 
    });
  } catch (error) {
    console.error('Error fetching fixtures:', error);
    return NextResponse.json({ 
      fixtures: [],
      count: 0,
      status: 'error',
      message: 'Failed to fetch fixtures' 
    }, { status: 500 });
  }
} 