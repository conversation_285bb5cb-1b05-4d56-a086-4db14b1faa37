import { NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { generateStructuredPrompt } from '@/lib/blog-post-structures';

// Check for the API key and log its status immediately
const apiKey = process.env.GEMINI_API_KEY;
if (!apiKey) {
  console.error("🔴 FATAL: GEMINI_API_KEY environment variable is not set. Please create a .env.local file and add the key. The AI service will not work.");
} else {
  console.log("🟢 INFO: Gemini API key loaded successfully.");
}

// Initialize the Gemini AI model
const genAI = new GoogleGenerativeAI(apiKey || '');
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-lite" });

export async function POST(request: Request) {
  // 1. Re-check for API Key at request time
  if (!apiKey) {
    console.error("❌ ERROR: API call received, but service is not configured (missing GEMINI_API_KEY).");
    return NextResponse.json({ 
      error: 'AI service is not configured. The GEMINI_API_KEY is missing from your environment variables. Please add it to a .env.local file and restart the server.' 
    }, { status: 500 });
  }

  try {
    // 2. Parse request body
    const body = await request.json();
    const { categorySlug, specialtyName, authorName, customInstructions } = body;

    // Validate input
    if (!categorySlug || !specialtyName || !authorName) {
      return NextResponse.json({ error: 'Missing required fields: categorySlug, specialtyName, or authorName.' }, { status: 400 });
    }

    // 3. Generate the structured prompt and convert it to text
    const promptString = generateStructuredPrompt(categorySlug, specialtyName, authorName, customInstructions);
    const promptData = JSON.parse(promptString);
    
    // Convert the JSON structure to a clear text prompt for Gemini
    const textPrompt = `${promptData.system_prompt}

ARTICLE REQUEST:
- Medical Specialty: ${promptData.user_prompt.specialty}
- Category: ${promptData.user_prompt.category}
- Author: ${promptData.user_prompt.author}
- Custom Instructions: ${promptData.user_prompt.custom_instructions}

REQUIRED OUTPUT FORMAT:
Please respond with a JSON object containing these exact fields:
${JSON.stringify(promptData.user_prompt.required_output_format, null, 2)}

ARTICLE STRUCTURE GUIDELINES:
${JSON.stringify(promptData.user_prompt.article_structure_guidelines, null, 2)}

Please generate the complete article following these guidelines and return it as valid JSON.`;

    console.log("🚀 Sending text prompt to Gemini (length:", textPrompt.length, "characters)");

    // 4. Call the Gemini API
    const result = await model.generateContent(textPrompt);
    const response = result.response;
    const aiResponseText = response.text();
    
    // Log the raw response for debugging
    console.log("✅ Received raw response from Gemini:", aiResponseText);
    
    // 5. Clean and parse the AI's JSON response
    // The model might return the JSON wrapped in ```json ... ```, so we clean it.
    const cleanedJsonString = aiResponseText.replace(/^```json\s*|```$/g, '').trim();
    
    let parsedContent;
    try {
      parsedContent = JSON.parse(cleanedJsonString);
    } catch (e) {
      console.error("❌ ERROR: Failed to parse JSON response from AI:", e);
      console.error("Raw response was:", cleanedJsonString);
      return NextResponse.json({ 
        error: 'Failed to parse AI response. The response was not valid JSON.', 
        rawResponse: cleanedJsonString 
      }, { status: 500 });
    }

    // 6. Return the structured content
    return NextResponse.json(parsedContent);

  } catch (error: any) {
    console.error('❌ FATAL: An error occurred in the AI generation route:', error);
    
    // Provide a more specific error message if it's an API key issue
    if (error.message && error.message.includes('API key')) {
      return NextResponse.json({ 
        error: 'The provided Gemini API key is invalid or lacks permissions for the requested model.' 
      }, { status: 401 }); // 401 Unauthorized is more appropriate
    }
    
    return NextResponse.json({ 
      error: 'An unexpected error occurred on the server during AI generation.', 
      details: error.message 
    }, { status: 500 });
  }
} 