import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role key for admin privileges
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.NEXT_PUBLIC_service_role || ''
);

export async function POST(request: Request) {
  try {
    const body = await request.json();
    
    // Extract review data from request body with correct field names
    // Make sure these match your actual database schema
    const {
      doctor_id,
      clinical_competence,
      communication_stats,
      empathy_compassion,
      time_management,
      follow_up_care,
      recommendation_rating, // This column doesn't exist in your schema
      additional_comments,
      rating
    } = body;
    
    // Create review object with only fields that exist in your database
    const reviewData = {
      doctor_id,
      clinical_competence,
      communication_stats,
      empathy_compassion,
      time_management,
      follow_up_care,
      // Remove or rename recommendation_rating to match your schema
      // recommendation: recommendation_rating, // If you have a column named 'recommendation'
      additional_comments,
      rating
    };
    
    // Validate required fields
    if (!doctor_id) {
      return NextResponse.json({ error: "Doctor ID is required" }, { status: 400 });
    }
    
    console.log(`Submitting review for doctor ${doctor_id} with rating ${rating}`);
    
    // Insert review into database with corrected field names
    const { data, error } = await supabase
      .from('reviews')
      .insert([reviewData]);
      
    if (error) {
      console.error("Error inserting review:", error);
      return NextResponse.json({ error: `Database error: ${error.message}` }, { status: 500 });
    }
    
    // Update doctor's average rating
    await updateDoctorRating(doctor_id);
    
    return NextResponse.json({ success: true, data });
  } catch (error) {
    console.error("Unexpected error during review submission:", error);
    return NextResponse.json({ error: "Failed to process review submission" }, { status: 500 });
  }
}

// Helper function to update doctor's average rating
async function updateDoctorRating(doctorId: string) {
  try {
    // Get all reviews for this doctor
    const { data: reviews, error: fetchError } = await supabase
      .from('reviews')
      .select('rating')
      .eq('doctor_id', doctorId);
    
    if (fetchError) {
      console.error("Error fetching reviews:", fetchError);
      return;
    }
    
    // Calculate average rating
    if (reviews && reviews.length > 0) {
      const totalRating = reviews.reduce((sum, review) => sum + (review.rating || 0), 0);
      const averageRating = totalRating / reviews.length;
      
      console.log(`Updating doctor ${doctorId} rating to ${averageRating.toFixed(1)} based on ${reviews.length} reviews`);
      
      // Update doctor's rating
      const { error: updateError } = await supabase
        .from('doctors')
        .update({ 
          rating: averageRating,
          review_count: reviews.length
        })
        .eq('doctor_id', doctorId);
      
      if (updateError) {
        console.error("Error updating doctor rating:", updateError);
      }
    }
  } catch (error) {
    console.error("Error in updateDoctorRating:", error);
  }
}

