import { type NextRequest, NextResponse } from "next/server"
import { query } from "@/lib/db"

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const name = searchParams.get("name")
  const country = searchParams.get("country")
  const page = searchParams.get("page") || "1"
  const pageSize = 10
  const offset = (Number.parseInt(page) - 1) * pageSize

  let sql = "SELECT * FROM hospitals WHERE 1=1"
  const params: any[] = []

  if (name) {
    sql += " AND name LIKE ?"
    params.push(`%${name}%`)
  }

  if (country) {
    sql += " AND country = ?"
    params.push(country)
  }

  sql += " LIMIT ? OFFSET ?"
  params.push(pageSize, offset)

  const hospitals = await query(sql, params)

  return NextResponse.json(hospitals)
}

