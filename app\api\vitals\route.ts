import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export const dynamic = 'force-dynamic'

// Add CORS headers for better compatibility
function corsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Content-Type': 'application/json'
  }
}

export async function OPTIONS() {
  return new NextResponse(null, { headers: corsHeaders() })
}

export async function POST(request: NextRequest) {
  // Set headers for proper response format
  const headers = {
    ...corsHeaders(),
    'Content-Type': 'application/json'
  }

  try {
    // Parse the web vitals metrics from the request
    const webVitalsData = await request.json()
    
    // Log the data in development for debugging
    if (process.env.NODE_ENV === 'development') {
      console.log('[Web Vitals API]', webVitalsData)
    } else {
      // Create a Supabase client using service role key for server-side operations
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
      const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
      
      console.log(`[Web Vitals API] Supabase URL available: ${!!supabaseUrl}`)
      console.log(`[Web Vitals API] Service Role Key available: ${!!supabaseServiceKey}`)
      
      if (supabaseUrl && supabaseServiceKey) {
        try {
          const supabase = createClient(supabaseUrl, supabaseServiceKey, {
            auth: {
              persistSession: false,
              autoRefreshToken: false
            }
          })
          
          // Check if the 'web_vitals' table exists, and if not, create it
          const { error: tableCheckError } = await supabase
            .from('web_vitals')
            .select('id', { count: 'exact', head: true })
            .limit(1)
          
          // If the table doesn't exist, we'll handle the error silently
          // The actual table creation would need to be done via migrations
          
          if (!tableCheckError) {
            console.log('[Web Vitals API] Storing metric in database')
            
            const { error } = await supabase
              .from('web_vitals')
              .insert({
                metric_name: webVitalsData.name,
                metric_value: webVitalsData.value,
                metric_rating: webVitalsData.rating,
                page_url: webVitalsData.url,
                user_agent: request.headers.get('user-agent') || '',
                timestamp: new Date().toISOString(),
              })
            
            if (error) {
              console.error('[Web Vitals API] Error storing web vitals:', error)
            } else {
              console.log('[Web Vitals API] Successfully stored web vital metric')
            }
          } else {
            console.log('[Web Vitals API] web_vitals table might not exist:', tableCheckError.message)
          }
        } catch (supabaseError) {
          console.error('[Web Vitals API] Supabase client error:', supabaseError)
        }
      }
      
      // Still log the metric for monitoring
      console.log(
        `[Web Vitals] ${webVitalsData.name}: ${webVitalsData.value.toFixed(2)} (${webVitalsData.rating}) - ${webVitalsData.url || 'unknown'}`
      )
    }

    // Return a successful response
    return NextResponse.json({ success: true }, { headers })
  } catch (error: any) {
    console.error('[Web Vitals API] Error:', error)
    
    // Don't fail the app if analytics fails
    return NextResponse.json(
      { error: 'Failed to process web vitals data' },
      { status: 500, headers }
    )
  }
} 