"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { User, Stethoscope } from "lucide-react"
import { MedicalSportsFrame } from "@/components/medical-sports-frame"
import { MedicalSportsButton } from "@/components/medical-sports-button"
import { But<PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"

export default function LoginChoicePage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleChooseRole = (role: "doctor" | "patient") => {
    setIsLoading(true)
    if (role === "doctor") {
      router.push("/doctor/login")
    } else {
      router.push("/patient/login")
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 to-indigo-900 flex flex-col items-center justify-center px-4">
      <MedicalSportsFrame className="max-w-md w-full p-6" hideDecorations={true}>
        <div className="p-6 space-y-6">
          <motion.h2
            className="text-2xl font-bold text-center text-foreground"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            Choose Your Login
          </motion.h2>
          
          <p className="text-foreground/80 text-center mb-6">
            Your email has been verified. Please select your role to log in.
          </p>

          <div className="grid grid-cols-1 gap-4">
            <motion.button
              onClick={() => handleChooseRole("patient")}
              className="flex flex-col items-center gap-4 p-6 rounded-lg border-2 border-blue-500/30 hover:border-blue-500 bg-background/50 hover:bg-blue-500/10 transition-all duration-200"
              whileHover={{ y: -5 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              disabled={isLoading}
            >
              <User className="h-12 w-12 text-blue-500" />
              <div className="space-y-1 text-center">
                <h3 className="font-semibold text-foreground">Patient Login</h3>
                <p className="text-sm text-foreground">Access your patient account</p>
              </div>
            </motion.button>

            <motion.button
              onClick={() => handleChooseRole("doctor")}
              className="flex flex-col items-center gap-4 p-6 rounded-lg border-2 border-green-500/30 hover:border-green-500 bg-background/50 hover:bg-green-500/10 transition-all duration-200"
              whileHover={{ y: -5 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
              disabled={isLoading}
            >
              <Stethoscope className="h-12 w-12 text-green-500" />
              <div className="space-y-1 text-center">
                <h3 className="font-semibold text-foreground">Doctor Login</h3>
                <p className="text-sm text-foreground">Access your medical professional account</p>
              </div>
            </motion.button>
          </div>

          <div className="text-center mt-6">
            <Button
              onClick={() => router.push("/")}
              variant="ghost"
              className="text-foreground/60 hover:text-foreground hover:bg-white/10"
            >
              Back to Home
            </Button>
          </div>
        </div>
      </MedicalSportsFrame>
    </div>
  )
} 