"use client"

import { useEffect, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { MedicalSportsFrame } from "@/components/medical-sports-frame"
import { MedicalSportsButton } from "@/components/medical-sports-button"

export default function VerificationSuccessPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const supabase = createClientComponentClient()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [userRole, setUserRole] = useState<"patient" | "doctor" | null>(null)
  
  // Get parameters from URL
  const emailParam = searchParams?.get('email') || null
  const typeParam = searchParams?.get('type') as "doctor" | "patient" | null

  useEffect(() => {
    async function checkAuthStatus() {
      try {
        const { data: { user }, error: userError } = await supabase.auth.getUser()
        
        // If we get an auth session missing error, don't treat it as an error
        // This is expected if the user was verified but not logged in
        if (userError && !userError.message.includes("Auth session missing")) {
          console.error("Error getting user:", userError)
          setError("Unable to verify your account status. Please try logging in.")
          setIsLoading(false)
          return
        }
        
        // If we do have a user, try to get their role
        if (user) {
          // Check if the user has a role in metadata
          const role = user.user_metadata?.role as "patient" | "doctor" | undefined
          if (role) {
            setUserRole(role)
          }
        } else if (typeParam) {
          // Use the type from URL if we can't get a user
          setUserRole(typeParam)
        }
        
        // Success state
        setIsLoading(false)
      } catch (err) {
        console.error("Unexpected error checking auth status:", err)
        setError("An unexpected error occurred.")
        setIsLoading(false)
      }
    }
    
    checkAuthStatus()
  }, [supabase.auth, typeParam])

  const handleLogin = () => {
    // Redirect to the appropriate login page based on role if we know it
    if (userRole === "doctor") {
      router.push("/doctor/login")
    } else if (userRole === "patient") {
      router.push("/patient/login") 
    } else {
      // Default to patient login if we don't know the role
      router.push("/patient/login")
    }
  }

  return (
    <>
      {/* Theme-specific background styling - Light theme gets light green, Dark theme keeps blue */}
      <style jsx>{`
        /* Light theme: Light green background to match homepage */
        html:not(.dark) .verification-page-bg {
          background: linear-gradient(135deg, hsl(120, 25%, 95%) 0%, hsl(120, 20%, 97%) 100%) !important;
        }

        /* Dark theme: Keep original blue background */
        html.dark .verification-page-bg {
          background: linear-gradient(135deg, hsl(220, 90%, 15%) 0%, hsl(220, 85%, 10%) 100%) !important;
        }
      `}</style>

      <div className="min-h-screen verification-page-bg bg-gradient-to-br from-blue-900 to-indigo-900 flex flex-col items-center justify-center px-4">
      <MedicalSportsFrame 
        variant={userRole === "doctor" ? "doctor" : "patient"}
        className="max-w-md w-full p-6"
      >
        <div className="p-6">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <p className="mt-4 text-foreground">Verifying your account...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-foreground mb-2">Verification Issue</h2>
              <p className="text-foreground/80 mb-6">{error}</p>
              <MedicalSportsButton 
                onClick={handleLogin} 
                variant={userRole === "doctor" ? "doctor" : "patient"}
                className="w-full"
              >
                Go to Login
              </MedicalSportsButton>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="h-10 w-10 text-green-500" />
              </div>
              <h2 className="text-2xl font-bold text-foreground mb-4">Email Verified Successfully!</h2>
              <p className="text-foreground/80 mb-2">
                Thank you for verifying your email address.
                {emailParam ? ` (${emailParam})` : ''}
              </p>
              <p className="text-foreground/80 mb-8">Your account is now activated and ready to use.</p>
              <MedicalSportsButton 
                onClick={handleLogin} 
                variant={userRole === "doctor" ? "doctor" : "patient"}
                className="w-full"
              >
                Proceed to Login
              </MedicalSportsButton>
            </div>
          )}
        </div>
      </MedicalSportsFrame>
      
      <div className="mt-8 text-center">
        <h1 className="text-2xl font-bold text-foreground mb-2">DOCTORS LEAGUES</h1>
        <p className="text-foreground/80">connecting healthcare professionals through sports</p>
      </div>
    </div>
    </>
  )
} 