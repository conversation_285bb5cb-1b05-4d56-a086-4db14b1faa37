"use client"

import { useEffect, useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { ECGDivider } from "@/components/ecg-divider"
import { ChooseRoleDialog } from "@/components/registration/choose-role-dialog"
import { Icons } from "@/components/icons"
import { useRouter } from "next/navigation"

export default function BecomeAMemberPage() {
  const [showRegistration, setShowRegistration] = useState(false)
  const router = useRouter()

  // Automatically open the registration dialog when the page loads
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowRegistration(true)
    }, 500)

    return () => clearTimeout(timer)
  }, [])

  // If dialog is closed, redirect back to home
  const handleDialogChange = (open: boolean) => {
    setShowRegistration(open)
    if (!open) {
      router.push("/")
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-background/95 to-primary/5 text-foreground flex items-center justify-center">
      <div className="container mx-auto px-4 py-16 text-center">
        <div className="animate-pulse">
          <Icons.logo className="mx-auto h-12 w-12 text-primary" />
          <h1 className="text-3xl font-bold mt-4 mb-2">
            Opening Registration...
          </h1>
          <p className="text-foreground/70 max-w-md mx-auto">
            Please wait while we prepare your registration options
          </p>
        </div>
        
        <ECGDivider className="my-10" />
        
        <Card className="bg-background/40 backdrop-blur-md border-primary/20 max-w-md mx-auto">
          <CardContent className="p-6">
            <div className="flex items-center justify-center space-x-4">
              <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
              <p className="text-foreground">Loading registration options...</p>
            </div>
          </CardContent>
        </Card>
        
        <ChooseRoleDialog
          open={showRegistration}
          onOpenChange={handleDialogChange}
        />
      </div>
    </div>
  )
} 