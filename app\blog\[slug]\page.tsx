import { Metada<PERSON> } from 'next'
import { notFound } from 'next/navigation'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Calendar, Clock, User, Eye, Share2, BookOpen, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { getBlogPostBySlug } from '@/lib/blog-service'
import { convertMarkdownToHtml } from '@/lib/blog-post-structures'

interface BlogPostPageProps {
  params: {
    slug: string
  }
}

const getBlogPost = async (slug: string) => {
  try {
    console.log('BlogPostPage: Fetching post by slug:', slug)
    const post = await getBlogPostBySlug(slug)
    console.log('BlogPostPage: Retrieved post:', post ? post.title : 'Not found')
    return post
  } catch (error) {
    console.error('BlogPostPage: Error fetching blog post:', error)
    return null
  }
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const { slug } = await params
  const post = await getBlogPost(slug)
  
  if (!post) {
    return {
      title: 'Post Not Found | Doctor\'s Leagues Blog'
    }
  }

  const tagNames = post.tags?.map(tag => tag.name) || []
  const authorName = post.author?.name || 'Unknown Author'

  return {
    title: `${post.title} | Doctor's Leagues Blog`,
    description: post.excerpt,
    keywords: tagNames,
    authors: [{ name: authorName }],
    openGraph: {
      title: post.title,
      description: post.excerpt,
      type: 'article',
      publishedTime: post.published_at,
      modifiedTime: post.updated_at,
      authors: [authorName],
      url: `https://doctorsleagues.com/blog/${post.slug}`,
      images: post.featured_image_url ? [
        {
          url: post.featured_image_url,
          alt: post.featured_image_alt || post.title
        }
      ] : undefined,
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.excerpt,
      images: post.featured_image_url ? [post.featured_image_url] : undefined,
    },
  }
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params
  const post = await getBlogPost(slug)
  
  if (!post) {
    notFound()
  }

  // Handle content that's already HTML or needs markdown conversion
  const htmlContent = post.content.trim().startsWith('<') 
    ? post.content // Content is already HTML
    : convertMarkdownToHtml(post.content) // Content is markdown and needs conversion

  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-background to-primary/20">
      {/* Back Navigation */}
      <div className="container mx-auto px-4 pt-8 relative z-10">
        <Link 
          href="/blog"
          className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Blog
        </Link>
      </div>

      {/* Article Header */}
      <header className="container mx-auto px-4 py-8 relative z-10">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            {post.category && (
              <Badge 
                style={{ backgroundColor: post.category.color }}
                className="text-foreground border-0 mb-4"
              >
                {post.category.name}
              </Badge>
            )}
            <h1 className="text-4xl md:text-5xl font-bold text-foreground leading-tight mb-4">
              {post.title}
            </h1>
            <p className="text-xl text-foreground/80 leading-relaxed">
              {post.excerpt}
            </p>
          </div>

          {/* Article Meta */}
          <div className="flex flex-wrap items-center gap-6 text-sm text-foreground/70 mb-8">
            {post.author && (
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>By {post.author.name}{post.author.medical_credentials ? `, ${post.author.medical_credentials}` : ''}</span>
              </div>
            )}
            {post.published_at && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>{new Date(post.published_at).toLocaleDateString('en-US', { 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}</span>
              </div>
            )}
            {post.reading_time_minutes && (
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span>{post.reading_time_minutes} min read</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              <span>{post.view_count.toLocaleString()} views</span>
            </div>
          </div>

          {/* Featured Image */}
          {post.featured_image_url ? (
            <div className="aspect-video rounded-lg overflow-hidden mb-8">
              <Image
                src={post.featured_image_url}
                alt={post.featured_image_alt || post.title}
                width={1200}
                height={675}
                className="w-full h-full object-cover"
                priority
              />
            </div>
          ) : (
            <div className="aspect-video bg-gradient-to-br from-primary/20 to-primary/30 rounded-lg flex items-center justify-center mb-8 border border-primary/20">
              <div className="text-center">
                <div className="text-6xl mb-4 text-primary/40">🏥</div>
                <p className="text-foreground/60 text-sm">Featured image will be displayed here</p>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Article Content */}
      <main className="container mx-auto px-4 pb-16 relative z-10">
        <div className="max-w-4xl mx-auto">
          <div className="grid lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <article className="lg:col-span-3">
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-8 border border-white/10 mb-8">
                <div 
                  className="blog-content max-w-none text-foreground"
                  dangerouslySetInnerHTML={{ __html: htmlContent }}
                />
              </div>

              {/* Medical Review Notice */}
              {post.medical_reviewer && (
                <Card className="mt-8 bg-primary/10 border-primary/30 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-primary/20 rounded-full">
                        <BookOpen className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-foreground mb-2">Medical Review</h3>
                        <p className="text-foreground/80 text-sm">
                          This article has been medically reviewed by {post.medical_reviewer.name}
                          {post.medical_reviewer.medical_credentials ? `, ${post.medical_reviewer.medical_credentials}` : ''}, 
                          to ensure accuracy and adherence to current medical standards.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Tags */}
              {post.tags && post.tags.length > 0 && (
                <div className="mt-8">
                  <h3 className="text-lg font-semibold text-foreground mb-4">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {post.tags.map((tag) => (
                      <Badge key={tag.id} variant="secondary" className="text-sm bg-white/10 text-foreground hover:bg-white/20">
                        {tag.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </article>

            {/* Sidebar */}
            <aside className="lg:col-span-1">
              {/* Author Bio */}
              {post.author && (
                <Card className="mb-6 bg-white/5 border-white/10 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <h3 className="font-semibold text-foreground mb-4">About the Author</h3>
                    <div className="space-y-4">
                      <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-primary/40 rounded-full flex items-center justify-center border border-primary/30">
                        <User className="h-8 w-8 text-primary" />
                      </div>
                      <div>
                        <h4 className="font-medium text-foreground text-lg">{post.author.name}</h4>
                        {post.author.medical_credentials && (
                          <p className="text-sm text-primary/80 font-medium">{post.author.medical_credentials}</p>
                        )}
                      </div>
                      {post.author.bio && (
                        <p className="text-sm text-foreground/80 leading-relaxed">{post.author.bio}</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Share */}
              <Card className="mb-6 bg-white/5 border-white/10 backdrop-blur-sm">
                <CardContent className="p-6">
                  <h3 className="font-semibold text-foreground mb-4">Share Article</h3>
                  <Button variant="outline" className="w-full border-primary/30 text-foreground hover:bg-primary/10 hover:border-primary/50 transition-colors">
                    <Share2 className="h-4 w-4 mr-2" />
                    Share Article
                  </Button>
                </CardContent>
              </Card>
            </aside>
          </div>
        </div>
      </main>
    </div>
  )
} 