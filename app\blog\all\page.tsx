import { Metada<PERSON> } from 'next'
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Calendar, Clock, User, ArrowRight, Search, Filter } from "lucide-react"
import Link from "next/link"
import { getBlogPosts } from '@/lib/blog-service'

export const metadata: Metadata = {
  title: 'All Articles | Medical Insights Blog',
  description: 'Browse all medical insights articles, analysis, and expert commentary from leading healthcare professionals.',
  keywords: ['medical articles', 'healthcare insights', 'medical analysis', 'doctor rankings', 'medical research'],
}

export default async function AllArticlesPage() {
  // Get all published blog posts from the database
  const allPosts = await getBlogPosts({ 
    status: 'published',
    limit: 50 // Reasonable limit for the all articles page
  })

  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-background to-primary/20">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-20">
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            All Medical Insights
          </h1>
          <p className="text-xl text-foreground/80 max-w-3xl mx-auto">
            Comprehensive collection of medical insights, rankings analysis, and expert commentary 
            from leading healthcare professionals
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-16 max-w-6xl mx-auto">
          <Card className="bg-white/10 border-white/20">
            <CardContent className="p-10">
              <div className="flex flex-col md:flex-row gap-4 items-center">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground/60 h-5 w-5" />
                  <Input
                    placeholder="Search articles by title, author, or topic..."
                    className="pl-12 bg-white/10 border-white/30 text-foreground placeholder:text-foreground/50 h-12"
                  />
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" className="border-white/30 text-foreground hover:bg-white/10">
                    <Filter className="h-4 w-4 mr-2" />
                    All Categories
                  </Button>
                  <Button variant="outline" className="border-white/30 text-foreground hover:bg-white/10">
                    Latest
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Articles Grid */}
        {allPosts.length === 0 ? (
          <div className="text-center py-16">
            <div className="text-6xl opacity-40 text-foreground mb-4">📝</div>
            <h3 className="text-xl font-semibold text-foreground mb-2">No Articles Yet</h3>
            <p className="text-foreground/70">
              Published articles will appear here once they are available.
            </p>
          </div>
        ) : (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {allPosts.map((post) => (
              <Card key={post.id} className="h-full hover:shadow-lg transition-shadow group bg-white/10 border-white/20">
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between gap-4">
                    {post.category && (
                      <Badge 
                        style={{ backgroundColor: post.category.color }}
                        className="text-foreground border-0 text-xs"
                      >
                        {post.category.name}
                      </Badge>
                    )}
                    {post.is_featured && (
                      <Badge className="bg-orange-500 text-foreground border-0 text-xs">
                        Featured
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <div className="space-y-4">
                    <h3 className="text-xl font-bold text-foreground line-clamp-2 leading-tight group-hover:text-primary transition-colors">
                      {post.title}
                    </h3>
                    
                    <p className="text-foreground/80 text-sm line-clamp-3">
                      {post.excerpt || post.content?.substring(0, 150) + '...'}
                    </p>
                    
                    <div className="flex items-center gap-4 text-xs text-foreground/70">
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span>{post.author?.name || 'Anonymous'}</span>
                      </div>
                      {post.published_at && (
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span>{new Date(post.published_at).toLocaleDateString()}</span>
                        </div>
                      )}
                      {post.reading_time_minutes && (
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>{post.reading_time_minutes} min</span>
                        </div>
                      )}
                    </div>

                    <div className="text-xs text-foreground/60">
                      {post.view_count || 0} views
                    </div>
                    
                    <div className="pt-2">
                      <Link 
                        href={`/blog/${post.slug}`}
                        className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium text-sm transition-colors group"
                      >
                        Read Article
                        <ArrowRight className="h-3 w-3 group-hover:translate-x-1 transition-transform" />
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Pagination Placeholder */}
        <div className="flex justify-center items-center gap-4 mb-8">
          <Button variant="outline" className="border-white/30 text-foreground hover:bg-white/10" disabled>
            Previous
          </Button>
          <span className="text-foreground/70 text-sm">Showing {allPosts.length} articles</span>
          <Button variant="outline" className="border-white/30 text-foreground hover:bg-white/10" disabled>
            Next
          </Button>
        </div>

        {/* Back to Blog Home */}
        <div className="text-center mt-12">
          <Link 
            href="/blog"
            className="inline-flex items-center gap-2 text-foreground/70 hover:text-foreground transition-colors"
          >
            ← Back to Blog Home
          </Link>
        </div>
      </div>
    </div>
  )
} 