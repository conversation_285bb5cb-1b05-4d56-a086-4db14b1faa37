import { Metadata } from 'next'
import { B<PERSON><PERSON><PERSON> } from '@/components/blog/blog-hero'
import { FeaturedPosts } from '@/components/blog/featured-posts'
import { CategoryGrid } from '@/components/blog/category-grid'
import { RecentPosts } from '@/components/blog/recent-posts'
import { BlogNewsletter } from '@/components/blog/blog-newsletter'
import { Suspense } from 'react'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

export const metadata: Metadata = {
  title: 'Medical Insights Blog | Doctor\'s Leagues',
  description: 'Authoritative medical insights, doctor rankings analysis, and healthcare industry trends from the leading medical professional platform.',
  keywords: ['medical blog', 'doctor rankings', 'healthcare insights', 'medical analysis', 'healthcare trends'],
  openGraph: {
    title: 'Medical Insights Blog | Doctor\'s Leagues',
    description: 'Authoritative medical insights, doctor rankings analysis, and healthcare industry trends.',
    type: 'website',
    url: 'https://doctorsleagues.com/blog',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Medical Insights Blog | Doctor\'s Leagues',
    description: 'Authoritative medical insights, doctor rankings analysis, and healthcare industry trends.',
  },
}

export default async function BlogPage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-background to-primary/20">
      {/* Blog Hero Section */}
      <BlogHero />
      
              <div className="container mx-auto px-4 py-12 space-y-16 relative z-10">
        {/* Featured Posts */}
        <section>
          <Suspense fallback={<LoadingSpinner />}>
            <FeaturedPosts />
          </Suspense>
        </section>

        {/* Category Grid */}
        <section>
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Explore Our Content Categories
            </h2>
            <p className="text-lg text-foreground/80 max-w-2xl mx-auto">
              Discover authoritative medical insights across our specialized content areas
            </p>
          </div>
          <Suspense fallback={<LoadingSpinner />}>
            <CategoryGrid />
          </Suspense>
        </section>

        {/* Recent Posts */}
        <section>
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Latest Medical Insights
            </h2>
            <p className="text-lg text-foreground/80 max-w-2xl mx-auto">
              Stay updated with the latest trends, analysis, and insights from the medical field
            </p>
          </div>
          <Suspense fallback={<LoadingSpinner />}>
            <RecentPosts />
          </Suspense>
        </section>

        {/* Newsletter Signup */}
        <section>
          <BlogNewsletter />
        </section>
      </div>
    </div>
  )
} 