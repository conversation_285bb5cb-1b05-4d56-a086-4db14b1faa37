/* Additional styles for buttons - more selective to prevent layout issues */
/* Only apply increased font size to action buttons, not all buttons */
button.action-button {
  font-size: 1rem !important;
}

/* Target action button spans */
button.action-button span {
  font-size: 1rem !important;
}

/* Target specific action buttons */
[data-join-match-button="true"] {
  font-size: 1rem !important;
}

/* Target specific action buttons spans */
[data-join-match-button="true"] span {
  font-size: 1rem !important;
}

/* Target navigation buttons but not logo/brand buttons */
button.nav-button:not(.brand-button) {
  font-size: 1rem !important;
}

/* Ensure buttons maintain proper padding and alignment */
button.action-button, button.nav-button {
  padding: 0.5rem 1rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Ensure button content is properly aligned */
button.action-button span, button.nav-button span {
  display: inline-block !important;
  vertical-align: middle !important;
}

/* Target mobile menu buttons specifically */
.md\:hidden button span.nav-menu-item {
  font-size: 1rem !important;
}

/* Ensure mobile action buttons have correct font size */
[data-join-match-button="true"] span.nav-menu-item {
  font-size: 1rem !important;
}

/* Target small buttons in hero section */
button.action-button[size="sm"] span.nav-menu-item {
  font-size: 0.875rem !important;
}

/* Target the mobile navigation footer buttons specifically */
.mobile-navigation-footer button span,
.p-4.border-t button span {
  font-size: 1rem !important;
}
