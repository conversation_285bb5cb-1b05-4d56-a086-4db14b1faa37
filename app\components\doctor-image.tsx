"use client"

import Image from "next/image"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { User } from "lucide-react"

interface DoctorImageProps {
  imagePath?: string | null
  name: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  fallbackClassName?: string
}

/**
 * A component for displaying doctor profile images with fallback
 */
export function DoctorImage({ 
  imagePath, 
  name, 
  size = 'md', 
  className = '',
  fallbackClassName = ''
}: DoctorImageProps) {
  // Define sizes for different options
  const sizeClasses = {
    sm: 'h-10 w-10',
    md: 'h-16 w-16',
    lg: 'h-24 w-24',
    xl: 'h-32 w-32'
  }
  
  const initials = name
    .split(' ')
    .map(part => part[0])
    .slice(0, 2)
    .join('')
    .toUpperCase()

  // Use Avatar component for consistent styling
  return (
    <Avatar className={`${sizeClasses[size]} ${className} bg-white border-2 border-primary/30 shadow-sm overflow-hidden`}>
      {imagePath ? (
        <AvatarImage 
          src={imagePath} 
          alt={`Dr. ${name}`}
          className="object-cover"
          onError={(e) => {
            // If image fails to load, fallback will show
            e.currentTarget.src = '' 
          }}
        />
      ) : null}
      <AvatarFallback className={`bg-primary/10 text-primary ${fallbackClassName}`}>
        {initials || <User className="h-6 w-6" />}
      </AvatarFallback>
    </Avatar>
  )
} 