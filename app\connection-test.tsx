"use client"

import { useState, useEffect } from "react"
import { createClient } from "@supabase/supabase-js"
import { supabase as importedSupabase } from "@/lib/supabase-client"
import { getFeaturedDoctors, getTopDoctors } from "@/lib/hybrid-data-service"

export default function ConnectionTest() {
  const [results, setResults] = useState<any>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function runTests() {
      setLoading(true)
      const testResults: any = {}

      // Test 1: Direct initialization with hardcoded credentials
      try {
        const directClient = createClient(
          "https://uapbzzscckhtptliynyj.supabase.co",
          "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q"
        )
        const { data, error } = await directClient.from("countries").select("*").limit(1)
        testResults.directClient = {
          success: !error,
          data: data ? data.length : 0,
          error: error ? error.message : null
        }
      } catch (e: any) {
        testResults.directClient = {
          success: false,
          error: e.message
        }
      }

      // Test 2: Imported supabase client
      try {
        const { data, error } = await importedSupabase.from("countries").select("*").limit(1)
        testResults.importedClient = {
          success: !error,
          data: data ? data.length : 0,
          error: error ? error.message : null
        }
      } catch (e: any) {
        testResults.importedClient = {
          success: false,
          error: e.message
        }
      }

      // Test 3: Environment variables availability
      testResults.environmentVars = {
        NEXT_PUBLIC_SUPABASE_URL: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
        NEXT_PUBLIC_SUPABASE_ANON_KEY: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        NEXT_PUBLIC_service_role: !!process.env.NEXT_PUBLIC_service_role
      }

      // Test 4: Home page function calls
      try {
        const featuredDoctors = await getFeaturedDoctors()
        testResults.featuredDoctors = {
          success: true,
          count: featuredDoctors.length,
          sample: featuredDoctors.length > 0 ? featuredDoctors[0].fullname : null
        }
      } catch (e: any) {
        testResults.featuredDoctors = {
          success: false,
          error: e.message
        }
      }

      try {
        const topDoctors = await getTopDoctors()
        testResults.topDoctors = {
          success: true,
          count: topDoctors.length,
          sample: topDoctors.length > 0 ? topDoctors[0].fullname : null
        }
      } catch (e: any) {
        testResults.topDoctors = {
          success: false,
          error: e.message
        }
      }

      // Test 5: Direct standings page query
      try {
        const { data, error } = await importedSupabase
          .from('specialties')
          .select('specialty_id, specialty_name')
          .limit(1)
        
        testResults.specialties = {
          success: !error,
          data: data ? data.length : 0,
          error: error ? error.message : null
        }

        if (data && data.length > 0) {
          const specialtyId = data[0].specialty_id
          const { data: doctorsData, error: doctorsError } = await importedSupabase
            .from('doctors')
            .select('*')
            .eq('specialty_id', specialtyId)
            .limit(1)
          
          testResults.doctorsBySpecialty = {
            success: !doctorsError,
            data: doctorsData ? doctorsData.length : 0,
            error: doctorsError ? doctorsError.message : null
          }
        }
      } catch (e: any) {
        testResults.specialties = {
          success: false,
          error: e.message
        }
      }

      setResults(testResults)
      setLoading(false)
    }

    runTests()
  }, [])

  return (
    <div className="p-8 max-w-4xl mx-auto bg-background text-foreground">
      <h1 className="text-2xl font-bold mb-6">Database Connection Test</h1>
      
      {loading ? (
        <div className="animate-pulse">Testing connections...</div>
      ) : (
        <div className="space-y-8">
          <div className="border border-primary/30 rounded-lg p-4">
            <h2 className="text-xl font-semibold mb-2">1. Direct Supabase Client</h2>
            <div className="flex items-center gap-2 mb-2">
              <div className={`w-4 h-4 rounded-full ${results.directClient?.success ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span>{results.directClient?.success ? 'Connected' : 'Failed'}</span>
            </div>
            {results.directClient?.error && (
              <div className="text-red-400 text-sm mt-2">
                Error: {results.directClient.error}
              </div>
            )}
            {results.directClient?.data !== undefined && (
              <div className="text-green-400 text-sm">
                Retrieved {results.directClient.data} records
              </div>
            )}
          </div>

          <div className="border border-primary/30 rounded-lg p-4">
            <h2 className="text-xl font-semibold mb-2">2. Imported Supabase Client</h2>
            <div className="flex items-center gap-2 mb-2">
              <div className={`w-4 h-4 rounded-full ${results.importedClient?.success ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span>{results.importedClient?.success ? 'Connected' : 'Failed'}</span>
            </div>
            {results.importedClient?.error && (
              <div className="text-red-400 text-sm mt-2">
                Error: {results.importedClient.error}
              </div>
            )}
            {results.importedClient?.data !== undefined && (
              <div className="text-green-400 text-sm">
                Retrieved {results.importedClient.data} records
              </div>
            )}
          </div>

          <div className="border border-primary/30 rounded-lg p-4">
            <h2 className="text-xl font-semibold mb-2">3. Environment Variables</h2>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className={`w-4 h-4 rounded-full ${results.environmentVars?.NEXT_PUBLIC_SUPABASE_URL ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span>NEXT_PUBLIC_SUPABASE_URL: {results.environmentVars?.NEXT_PUBLIC_SUPABASE_URL ? 'Available' : 'Not available'}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className={`w-4 h-4 rounded-full ${results.environmentVars?.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span>NEXT_PUBLIC_SUPABASE_ANON_KEY: {results.environmentVars?.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Available' : 'Not available'}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className={`w-4 h-4 rounded-full ${results.environmentVars?.NEXT_PUBLIC_service_role ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span>NEXT_PUBLIC_service_role: {results.environmentVars?.NEXT_PUBLIC_service_role ? 'Available' : 'Not available'}</span>
              </div>
            </div>
          </div>

          <div className="border border-primary/30 rounded-lg p-4">
            <h2 className="text-xl font-semibold mb-2">4. Home Page Functions</h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium">getFeaturedDoctors()</h3>
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-4 h-4 rounded-full ${results.featuredDoctors?.success ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span>{results.featuredDoctors?.success ? 'Success' : 'Failed'}</span>
                </div>
                {results.featuredDoctors?.error && (
                  <div className="text-red-400 text-sm">
                    Error: {results.featuredDoctors.error}
                  </div>
                )}
                {results.featuredDoctors?.count !== undefined && (
                  <div className="text-green-400 text-sm">
                    Retrieved {results.featuredDoctors.count} doctors
                    {results.featuredDoctors.sample && ` (e.g., ${results.featuredDoctors.sample})`}
                  </div>
                )}
              </div>

              <div>
                <h3 className="font-medium">getTopDoctors()</h3>
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-4 h-4 rounded-full ${results.topDoctors?.success ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span>{results.topDoctors?.success ? 'Success' : 'Failed'}</span>
                </div>
                {results.topDoctors?.error && (
                  <div className="text-red-400 text-sm">
                    Error: {results.topDoctors.error}
                  </div>
                )}
                {results.topDoctors?.count !== undefined && (
                  <div className="text-green-400 text-sm">
                    Retrieved {results.topDoctors.count} doctors
                    {results.topDoctors.sample && ` (e.g., ${results.topDoctors.sample})`}
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="border border-primary/30 rounded-lg p-4">
            <h2 className="text-xl font-semibold mb-2">5. Standings Page Queries</h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium">Specialties Query</h3>
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-4 h-4 rounded-full ${results.specialties?.success ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span>{results.specialties?.success ? 'Success' : 'Failed'}</span>
                </div>
                {results.specialties?.error && (
                  <div className="text-red-400 text-sm">
                    Error: {results.specialties.error}
                  </div>
                )}
                {results.specialties?.data !== undefined && (
                  <div className="text-green-400 text-sm">
                    Retrieved {results.specialties.data} specialties
                  </div>
                )}
              </div>

              <div>
                <h3 className="font-medium">Doctors by Specialty Query</h3>
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-4 h-4 rounded-full ${results.doctorsBySpecialty?.success ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span>{results.doctorsBySpecialty?.success ? 'Success' : 'Failed'}</span>
                </div>
                {results.doctorsBySpecialty?.error && (
                  <div className="text-red-400 text-sm">
                    Error: {results.doctorsBySpecialty.error}
                  </div>
                )}
                {results.doctorsBySpecialty?.data !== undefined && (
                  <div className="text-green-400 text-sm">
                    Retrieved {results.doctorsBySpecialty.data} doctors for specialty
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-background/90 rounded-lg">
            <h3 className="font-semibold">Conclusion:</h3>
            <p className="mt-2">
              {results.directClient?.success && results.importedClient?.success && results.featuredDoctors?.success && results.specialties?.success
                ? "All connections are working properly. If you're still experiencing issues, there might be a problem with how specific pages use the client."
                : "Connection issues detected. The direct client with hardcoded credentials is "
                  + (results.directClient?.success ? "working" : "not working")
                  + ", but the imported client is "
                  + (results.importedClient?.success ? "working" : "not working")
                  + ". Home page functions are "
                  + (results.featuredDoctors?.success && results.topDoctors?.success ? "working" : "not working")
                  + "."}
            </p>
          </div>
        </div>
      )}
    </div>
  )
} 