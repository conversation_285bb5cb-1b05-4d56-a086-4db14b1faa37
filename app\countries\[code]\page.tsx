import { Metadata } from "next"
import { notFound } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { getCountryById, getSpecialties, getTopDoctors, getCountries } from "@/lib/hybrid-data-service"
import { MapPin, ChevronRight, Trophy, Star, Phone, Globe, Mail, Users, Award } from "lucide-react"
import { generateMedicalOrganizationSchema } from "@/components/seo"
import { SchemaMarkup } from "@/components/seo/schema-generator"
import { CountryMetadata } from "@/components/seo/country-metadata"
import { isLocalFilePath, getImageUrl } from "@/lib/utils"

// Pre-generate all country pages at build time
export async function generateStaticParams() {
  const countries = await getCountries()
  
  return countries.map((country) => ({
    code: country.country_id.toString(),
  }))
}

// Generate metadata for the page
export async function generateMetadata({ 
  params 
}: { 
  params: { code: string } 
}): Promise<Metadata> {
  const country = await getCountryById(params.code)
  
  if (!country) {
    return {
      title: "Country Not Found",
      description: "The country page you are looking for could not be found."
    }
  }
  
  return {
    title: `Top Doctors in ${country.country_name} | Healthcare Rankings`,
    description: `Find the best healthcare professionals in ${country.country_name}. Compare doctor rankings, specialties, and patient reviews for leading medical experts.`,
    keywords: `doctors in ${country.country_name}, healthcare, medical professional, top doctors, ${country.country_name} healthcare, doctor rankings, medical specialists`,
    alternates: {
      canonical: `https://doctorsleagues.com/countries/${params.code}`,
      languages: {
        'x-default': `https://doctorsleagues.com/countries/${params.code}`,
        'en': `https://doctorsleagues.com/countries/${params.code}?lang=en`,
        'ar': country.country_name === 'Saudi Arabia' || country.country_name === 'UAE' || country.country_name === 'Qatar' 
          ? `https://doctorsleagues.com/countries/${params.code}?lang=ar` 
          : undefined
      }
    }
  }
}

export default async function CountryPage({ 
  params,
  searchParams
}: { 
  params: { code: string },
  searchParams?: { lang?: string }
}) {
  const country = await getCountryById(params.code)
  
  if (!country) {
    notFound()
  }
  
  // Determine language from URL parameter or default based on country
  const language = searchParams?.lang || 
    (country.country_name === 'Saudi Arabia' || country.country_name === 'UAE' || country.country_name === 'Qatar' 
      ? 'ar' : 'en')
  
  // Fetch specialties and top doctors for this country
  const [specialties, topDoctors] = await Promise.all([
    getSpecialties(),
    getTopDoctors(10) // Limit to top 10 doctors
  ])
  
  // Filter top doctors for this country if available
  const countryDoctors = topDoctors
    .filter(doctor => doctor.country_id === country.country_id)
    .slice(0, 6) // Show max 6 doctors
  
  // Generate organization schema for the country healthcare system
  const organizationSchema = generateMedicalOrganizationSchema(
    `Doctor's Leagues ${country.country_name}`,
    `https://doctorsleagues.com/countries/${params.code}`,
    "https://doctorsleagues.com/logo.png",
    `Leading healthcare professional rankings and comparisons in ${country.country_name}.`,
    "+**********",
    {
      streetAddress: `Medical District`,
      addressLocality: `${country.country_name} Capital`,
      addressCountry: country.country_name
    }
  )
  
  return (
    <>
      {/* Country-specific metadata with proper hreflang */}
      <CountryMetadata
        variants={[
          { country: params.code, language: 'en', url: `/countries/${params.code}?lang=en` },
          ...(language === 'ar' ? [{ country: params.code, language: 'ar', url: `/countries/${params.code}?lang=ar` }] : []),
          { country: 'x-default', language: 'en', url: `/countries/${params.code}` }
        ]}
        currentCountry={params.code}
        currentLanguage={language}
      />
      
      {/* Schema markup */}
      <SchemaMarkup schema={organizationSchema} />
      
      <main className={`container mx-auto px-4 py-12 ${language === 'ar' ? 'rtl' : 'ltr'}`}>
        {/* Hero Section */}
        <div className="max-w-6xl mx-auto mb-16">
          <Card className="border-primary/20 bg-background/90 overflow-hidden">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent z-0"></div>
              <CardContent className="p-8 relative z-10">
                <div className="flex flex-col md:flex-row items-center gap-8">
                  <div className="md:w-1/2">
                    <Badge className="mb-4 bg-primary text-foreground">{country.country_name}</Badge>
                    <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-4">
                      Top Medical Professionals in {country.country_name}
                    </h1>
                    <p className="text-foreground/80 text-lg mb-6">
                      Find and compare the best doctors across all specialties ranked by expertise, 
                      patient outcomes, and professional achievements.
                    </p>
                    <div className="flex flex-wrap gap-3">
                      <Button asChild>
                        <Link href={`/divisions/${country.country_id}`}>
                          <Trophy className="mr-2 h-4 w-4" />
                          View All Rankings
                        </Link>
                      </Button>
                      <Button variant="outline" className="border-primary/30 text-primary">
                        <Link href={`/search?country=${country.country_id}`}>
                          <MapPin className="mr-2 h-4 w-4" />
                          Find Doctors
                        </Link>
                      </Button>
                    </div>
                  </div>
                  <div className="md:w-1/2 flex justify-center">
                    {country.flag_url ? (
                      <div className="relative h-40 w-64 overflow-hidden rounded-lg border-2 border-white/10 shadow-lg">
                        <Image
                          src={country.flag_url}
                          alt={`${country.country_name} Flag`}
                          fill
                          className="object-cover"
                        />
                      </div>
                    ) : (
                      <div className="h-40 w-64 bg-gradient-to-br from-primary/30 to-primary/10 rounded-lg flex items-center justify-center">
                        <Globe className="h-20 w-20 text-primary/60" />
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </div>
          </Card>
        </div>
        
        {/* Featured Specialties Section */}
        <section className="mb-16">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-foreground flex items-center">
              <Award className="h-5 w-5 text-primary mr-2" />
              Medical Specialties
            </h2>
            <Button variant="link" asChild>
              <Link href="/specialties" className="text-primary">
                View All
                <ChevronRight className="ml-1 h-4 w-4" />
              </Link>
            </Button>
          </div>
          <Separator className="bg-primary/20 mb-6" />
          
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {specialties.slice(0, 6).map((specialty) => (
              <Card key={specialty.specialty_id} className="bg-background/40 border-primary/20 hover:border-primary/40 transition-all">
                <CardHeader className="pb-2">
                  <CardTitle className="text-foreground">
                    {specialty.specialty_name}
                  </CardTitle>
                  <CardDescription>
                    {/* Display specialist count if available in data */}
                    Medical expertise & rankings
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button asChild size="sm" className="w-full">
                    <Link href={`/divisions/${country.country_id}/${specialty.specialty_id}`}>
                      <Trophy className="mr-2 h-4 w-4" />
                      View Rankings
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
        
        {/* Featured Doctors Section */}
        {countryDoctors.length > 0 && (
          <section className="mb-16">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-foreground flex items-center">
                <Star className="h-5 w-5 text-yellow-500 mr-2" />
                Top Doctors in {country.country_name}
              </h2>
              <Button variant="link" asChild>
                <Link href={`/search?country=${country.country_id}`} className="text-primary">
                  View All
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Link>
              </Button>
            </div>
            <Separator className="bg-primary/20 mb-6" />
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {countryDoctors.map((doctor) => (
                <Card key={doctor.doctor_id} className="bg-background/40 border-primary/20 overflow-hidden hover:border-primary/40 transition-all">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-b from-transparent to-background/70 z-0"></div>
                    <div className="p-4 flex items-start gap-4">
                      <div className="h-20 w-20 rounded-full overflow-hidden bg-green-200 border-2 border-primary/30 flex-shrink-0 relative z-10">
                        {doctor.image_path && !isLocalFilePath(doctor.image_path) ? (
                          <Image
                            src={doctor.image_path}
                            alt={doctor.fullname}
                            fill
                            className="object-cover"
                          />
                        ) : (
                          <Users className="h-full w-full p-4 text-muted-green" />
                        )}
                      </div>
                      <div className="flex-1 relative z-10">
                        <h3 className="text-lg font-bold text-foreground mb-1">{doctor.fullname}</h3>
                        <p className="text-primary mb-1">{doctor.specialty}</p>
                        <div className="flex items-center text-yellow-500 mb-2">
                          {[...Array(5)].map((_, index) => (
                            <Star
                              key={index}
                              className={`h-4 w-4 ${
                                index < Math.floor(doctor.rating || 0) ? "fill-yellow-500" : ""
                              }`}
                            />
                          ))}
                          <span className="ml-1 text-foreground text-sm">
                            {doctor.rating?.toFixed(1) || "0.0"}
                          </span>
                        </div>
                        <Button asChild size="sm" variant="outline" className="w-full border-primary/30 text-primary">
                          <Link href={`/doctors/${doctor.doctor_id}`}>
                            View Profile
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </section>
        )}
        
        {/* Healthcare Resources Section */}
        <section className="mb-16">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-foreground flex items-center">
              <Globe className="h-5 w-5 text-primary mr-2" />
              Healthcare in {country.country_name}
            </h2>
          </div>
          <Separator className="bg-primary/20 mb-6" />
          
          <Card className="bg-background/40 border-primary/20">
            <CardContent className="p-6">
              <div className="prose prose-invert max-w-none">
                <p>
                  Doctor's Leagues provides comprehensive rankings and comparisons of healthcare 
                  professionals in {country.country_name}. Our platform helps patients find the 
                  best specialists based on expertise, patient outcomes, and professional achievements.
                </p>
                <p>
                  Healthcare in {country.country_name} offers a range of medical services across various 
                  specialties. Whether you're looking for primary care or specialized treatment, our 
                  rankings can help you make informed decisions about your healthcare providers.
                </p>
                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start gap-3">
                    <Trophy className="h-5 w-5 text-primary mt-1" />
                    <div>
                      <h3 className="text-lg font-medium text-foreground">Ranked Professionals</h3>
                      <p className="text-foreground/70">
                        Find top specialists across all medical fields based on verified performance metrics.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Star className="h-5 w-5 text-primary mt-1" />
                    <div>
                      <h3 className="text-lg font-medium text-foreground">Patient Reviews</h3>
                      <p className="text-foreground/70">
                        Read authentic patient experiences and satisfaction scores from verified visits.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <MapPin className="h-5 w-5 text-primary mt-1" />
                    <div>
                      <h3 className="text-lg font-medium text-foreground">Location Services</h3>
                      <p className="text-foreground/70">
                        Find healthcare professionals near you with detailed location information.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Phone className="h-5 w-5 text-primary mt-1" />
                    <div>
                      <h3 className="text-lg font-medium text-foreground">Direct Contact</h3>
                      <p className="text-foreground/70">
                        Connect with healthcare providers directly through our platform.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>
        
        {/* Contact Section */}
        <section>
          <Card className="bg-gradient-to-r from-primary/20 to-background border-primary/20">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row items-center gap-6">
                <div className="md:w-2/3">
                  <h2 className="text-2xl font-bold text-foreground mb-4">
                    Looking for Healthcare Support in {country.country_name}?
                  </h2>
                  <p className="text-foreground/80 mb-6">
                    Contact our local team for assistance finding the right specialists and healthcare 
                    services in your area. We're here to help you navigate the healthcare system and 
                    connect with top-rated professionals.
                  </p>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-foreground/80">
                    <div className="flex items-center gap-2">
                      <Mail className="h-5 w-5 text-primary" />
                      <span><EMAIL></span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-5 w-5 text-primary" />
                      <span>+**********</span>
                    </div>
                  </div>
                </div>
                <div className="md:w-1/3">
                  <Button size="lg" className="w-full" asChild>
                    <Link href="/contact">Get in Touch</Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>
      </main>
    </>
  )
} 