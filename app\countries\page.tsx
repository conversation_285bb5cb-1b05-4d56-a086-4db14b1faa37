import { Metadata } from "next"
import Image from "next/image"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { getCountries } from "@/lib/hybrid-data-service"
import { MapPin, ChevronRight, Globe } from "lucide-react"
import { SchemaMarkup } from "@/components/seo/schema-generator"
import { generateMedicalWebPageSchema } from "@/components/seo"

export const revalidate = 86400; // Revalidate this page once per day (in seconds)

export const metadata: Metadata = {
  title: "Doctor's Leagues | Countries",
  description: "Find top-ranked medical professionals across multiple countries. Compare healthcare providers and access country-specific doctor rankings.",
  keywords: "international doctors, global healthcare rankings, doctor ratings by country, medical professionals",
  alternates: {
    canonical: "https://doctorsleagues.com/countries"
  }
}

export default async function CountriesPage() {
  // Fetch all countries
  const countries = await getCountries()
  
  // Group countries by region/continent for better organization
  const regions = {
    "Middle East": ["Saudi Arabia", "UAE", "Qatar", "Bahrain", "Kuwait", "Oman"],
    "Europe": ["UK", "Germany", "France", "Spain", "Italy"],
    "North America": ["USA", "Canada"],
    "Asia": ["Singapore", "Malaysia", "Japan", "South Korea", "India"]
  }
  
  // Organize countries by region
  const countriesByRegion = Object.entries(regions).reduce(
    (acc, [region, regionCountries]) => {
      const matchingCountries = countries.filter(country => 
        regionCountries.includes(country.country_name)
      )
      
      if (matchingCountries.length > 0) {
        acc[region] = matchingCountries
      }
      
      return acc
    }, 
    {} as Record<string, typeof countries>
  )
  
  // Add "Other" category for countries not in any specific region
  const otherCountries = countries.filter(country => 
    !Object.values(regions).flat().includes(country.country_name)
  )
  
  if (otherCountries.length > 0) {
    countriesByRegion["Other Regions"] = otherCountries
  }
  
  // Create schema for the page
  const pageSchema = generateMedicalWebPageSchema(
    "Countries with Doctor Rankings",
    "Find and compare top medical professionals across multiple countries. Our international rankings help you discover the best healthcare specialists worldwide.",
    "https://doctorsleagues.com/countries"
  )
  
  return (
    <>
      {/* Schema markup */}
      <SchemaMarkup schema={pageSchema} />
      
      <main className="container mx-auto px-4 py-12">
        <div className="max-w-6xl mx-auto mb-12 text-center">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-4">
            Global Medical Excellence
          </h1>
          <p className="text-foreground/80 text-lg max-w-3xl mx-auto">
            Discover top-ranked healthcare professionals across countries and regions.
            Our global database helps you find the best specialists wherever you are.
          </p>
        </div>
        
        {/* Countries by Region */}
        {Object.entries(countriesByRegion).map(([region, regionCountries]) => (
          <section key={region} className="mb-16">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-foreground flex items-center">
                <Globe className="h-5 w-5 text-primary mr-2" />
                {region}
              </h2>
            </div>
            <Separator className="bg-primary/20 mb-6" />
            
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {regionCountries.map((country) => (
                <Card 
                  key={country.country_id} 
                  className="bg-background/40 border-primary/20 hover:border-primary/40 transition-all"
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-foreground text-lg">
                        {country.country_name}
                      </CardTitle>
                      {country.flag_url && (
                        <div className="relative h-6 w-10 overflow-hidden rounded-sm border border-white/10">
                          <Image
                            src={country.flag_url}
                            alt={`${country.country_name} Flag`}
                            fill
                            className="object-cover"
                          />
                        </div>
                      )}
                    </div>
                    <CardDescription>
                      Healthcare rankings & professionals
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button asChild size="sm" className="w-full">
                      <Link href={`/countries/${country.country_id}`}>
                        <MapPin className="mr-2 h-4 w-4" />
                        View Country
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        ))}
        
        {/* Global Healthcare Information */}
        <section className="mt-16">
          <Card className="bg-gradient-to-r from-primary/20 to-background border-primary/20">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row items-center gap-6">
                <div className="md:w-2/3">
                  <h2 className="text-2xl font-bold text-foreground mb-4">
                    Looking for International Healthcare?
                  </h2>
                  <p className="text-foreground/80 mb-6">
                    Doctor's Leagues provides comprehensive rankings and comparisons of healthcare 
                    professionals across multiple countries. Our platform helps patients find the 
                    best specialists based on expertise, patient outcomes, and professional achievements
                    no matter where they are located.
                  </p>
                </div>
                <div className="md:w-1/3">
                  <Button size="lg" className="w-full" asChild>
                    <Link href="/about">Learn More</Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>
      </main>
    </>
  )
} 