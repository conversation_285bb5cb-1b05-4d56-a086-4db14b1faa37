/* Custom styles for navigation menu - more selective to prevent layout issues */
/* Apply reduced font size to navigation menu items, not the logo/brand */
.nav-menu-item {
  font-size: 0.95rem !important; /* Reduced from 1rem */
  line-height: 1.5 !important;
}

.nav-menu-icon {
  width: 0.95rem !important; /* Reduced from 1rem */
  height: 0.95rem !important;
}

/* Ensure the styles are applied to specific navigation elements only */
nav a:not(.brand-link), nav button:not(.brand-button), .dropdown-menu-item {
  font-size: 0.95rem !important; /* Reduced from 1.44rem */
}

/* Force the styles with higher specificity but exclude brand elements */
header nav a:not(.brand-link) span, header nav button:not(.brand-button) span {
  font-size: 0.95rem !important; /* Reduced from 1.44rem */
}

/* Target navigation menu items directly but exclude brand elements */
header nav a:not(.brand-link), header nav button:not(.brand-button) {
  font-size: 0.95rem !important; /* Reduced from 1.44rem */
}

/* Target specific elements but be more selective */
.text-base:not(.brand-text) {
  font-size: 0.95rem !important; /* Reduced from 1.44rem */
}

/* Target the action buttons in the header */
header .action-buttons button span {
  font-size: 0.95rem !important; /* Reduced from 1.44rem */
}

/* Target the specific action buttons */
[data-join-match-button="true"] span {
  font-size: 0.95rem !important; /* Reduced from 1.44rem */
}

/* Explicitly protect brand elements from font size changes */
.brand-link, .brand-text {
  font-size: initial !important;
}

/* Ensure the brand link has proper spacing */
.brand-link {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

/* Ensure the logo maintains its size */
.brand-logo-container {
  flex-shrink: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 3rem !important;
  min-height: 3rem !important;
}

.brand-link img {
  height: 3rem !important;
  width: 3rem !important;
  min-width: 3rem !important;
  object-fit: contain !important;
}

/* Ensure the brand text container has proper spacing */
.brand-text-container {
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  min-width: 150px !important;
}

/* Ensure the brand text is properly sized */
.brand-link .brand-text {
  font-size: 1.5rem !important;
  line-height: 1.75rem !important;
  white-space: nowrap !important;
}

/* Ensure the brand subtitle is properly sized */
.brand-link .text-xs {
  font-size: 0.75rem !important;
  line-height: 1rem !important;
  white-space: nowrap !important;
}

/* Ensure proper spacing in the header */
header .container {
  max-width: 100% !important;
  margin: 0 auto !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

/* Ensure the header height is maintained */
header .flex.h-20 {
  height: 5rem !important; /* 80px */
  min-height: 5rem !important;
  justify-content: space-between !important;
}

/* Ensure proper spacing between navigation items */
header nav {
  gap: 0.25rem !important;
}

/* Reduce padding on navigation items to fit more in the viewport */
header nav button, header nav a {
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
}

/* Ensure the mobile menu button is properly sized */
header button[class*="md:hidden"] {
  font-size: initial !important;
}

/* Target mobile menu action buttons */
header .md\:hidden button.action-button span.nav-menu-item {
  font-size: 0.95rem !important; /* Reduced from 1.44rem */
}

/* Ensure mobile menu Join Match button has correct font size */
header .md\:hidden [data-join-match-button="true"] span.nav-menu-item {
  font-size: 0.95rem !important; /* Reduced from 1.44rem */
}
