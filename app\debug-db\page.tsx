"use client"

import { useState, useEffect, useRef } from "react"
import { createClient } from "@supabase/supabase-js"
import { supabase, createBrowserClient } from "@/lib/supabase-client"
import { Button } from "@/components/ui/button"
import { getFeaturedDoctors, getTopDoctors } from "@/lib/hybrid-data-service"

export default function DebugDatabasePage() {
  const [logs, setLogs] = useState<string[]>([])
  const [testResults, setTestResults] = useState<any>({})
  const [isLoading, setIsLoading] = useState(false)
  const [selectedTest, setSelectedTest] = useState<string>("all")
  const logRef = useRef<HTMLDivElement>(null)

  const addLog = (message: string) => {
    const timestamp = new Date().toISOString().slice(11, 23)
    setLogs(prev => [...prev, `[${timestamp}] ${message}`])
    // Auto-scroll to bottom
    setTimeout(() => {
      if (logRef.current) {
        logRef.current.scrollTop = logRef.current.scrollHeight
      }
    }, 100)
  }

  useEffect(() => {
    addLog("Debug page initialized")
    addLog(`Running in ${typeof window !== 'undefined' ? 'browser' : 'server'} environment`)
  }, [])

  const runAllTests = async () => {
    setIsLoading(true)
    setTestResults({})
    addLog("Running all tests...")
    
    // Track time for performance
    const startTime = Date.now()
    
    // Test 1: Direct supabase client
    try {
      addLog("Testing direct supabase client...")
      const { data, error } = await supabase.from('countries').select('*').limit(1)
      
      if (error) {
        addLog(`❌ Direct client error: ${error.message}`)
        setTestResults(prev => ({ ...prev, directClient: { success: false, error: error.message } }))
      } else {
        addLog(`✅ Direct client success: ${data?.length || 0} records`)
        setTestResults(prev => ({ ...prev, directClient: { success: true, data } }))
      }
    } catch (error: any) {
      addLog(`❌ Direct client exception: ${error.message}`)
      setTestResults(prev => ({ ...prev, directClient: { success: false, error: error.message } }))
    }
    
    // Test 2: Create browser client
    try {
      addLog("Testing createBrowserClient()...")
      const client = createBrowserClient()
      const { data, error } = await client.from('countries').select('*').limit(1)
      
      if (error) {
        addLog(`❌ Browser client error: ${error.message}`)
        setTestResults(prev => ({ ...prev, browserClient: { success: false, error: error.message } }))
      } else {
        addLog(`✅ Browser client success: ${data?.length || 0} records`)
        setTestResults(prev => ({ ...prev, browserClient: { success: true, data } }))
      }
    } catch (error: any) {
      addLog(`❌ Browser client exception: ${error.message}`)
      setTestResults(prev => ({ ...prev, browserClient: { success: false, error: error.message } }))
    }
    
    // Test 3: Direct createClient with hardcoded values
    try {
      addLog("Testing direct createClient with hardcoded values...")
      const directClient = createClient(
        "https://uapbzzscckhtptliynyj.supabase.co",
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q"
      )
      const { data, error } = await directClient.from('countries').select('*').limit(1)
      
      if (error) {
        addLog(`❌ Direct createClient error: ${error.message}`)
        setTestResults(prev => ({ ...prev, directCreateClient: { success: false, error: error.message } }))
      } else {
        addLog(`✅ Direct createClient success: ${data?.length || 0} records`)
        setTestResults(prev => ({ ...prev, directCreateClient: { success: true, data } }))
      }
    } catch (error: any) {
      addLog(`❌ Direct createClient exception: ${error.message}`)
      setTestResults(prev => ({ ...prev, directCreateClient: { success: false, error: error.message } }))
    }
    
    // Test 4: getFeaturedDoctors function
    try {
      addLog("Testing getFeaturedDoctors function...")
      const doctors = await getFeaturedDoctors(2)
      
      const isFallback = doctors.length > 0 && doctors[0].doctor_id === "error"
      
      if (isFallback) {
        addLog(`⚠️ getFeaturedDoctors returned fallback data`)
        setTestResults(prev => ({ 
          ...prev, 
          featuredDoctors: { 
            success: false, 
            isFallback: true,
            data: doctors 
          } 
        }))
      } else {
        addLog(`✅ getFeaturedDoctors success: ${doctors.length} records`)
        setTestResults(prev => ({ 
          ...prev, 
          featuredDoctors: { 
            success: true, 
            data: doctors 
          } 
        }))
      }
    } catch (error: any) {
      addLog(`❌ getFeaturedDoctors exception: ${error.message}`)
      setTestResults(prev => ({ ...prev, featuredDoctors: { success: false, error: error.message } }))
    }
    
    // Calculate total time
    const totalTime = Date.now() - startTime
    addLog(`All tests completed in ${totalTime}ms`)
    setIsLoading(false)
  }

  const runHybridServicesTest = async () => {
    setIsLoading(true)
    addLog("Testing hybrid-data-service...")
    
    try {
      const doctors = await getFeaturedDoctors(3)
      addLog(`getFeaturedDoctors returned ${doctors.length} doctors`)
      addLog(`First doctor: ${doctors[0]?.fullname || 'Unknown'}`)
      addLog(`Is fallback data: ${doctors[0]?.doctor_id === "error" ? 'Yes' : 'No'}`)
      
      const topDoctors = await getTopDoctors(3)
      addLog(`getTopDoctors returned ${topDoctors.length} doctors`)
      addLog(`First doctor: ${topDoctors[0]?.fullname || 'Unknown'}`)
      addLog(`Is fallback data: ${topDoctors[0]?.doctor_id === "error" ? 'Yes' : 'No'}`)
      
      setTestResults(prev => ({
        ...prev,
        hybridServices: {
          success: true,
          featuredDoctors: {
            count: doctors.length,
            isFallback: doctors[0]?.doctor_id === "error",
            sample: doctors[0]?.fullname
          },
          topDoctors: {
            count: topDoctors.length,
            isFallback: topDoctors[0]?.doctor_id === "error",
            sample: topDoctors[0]?.fullname
          }
        }
      }))
    } catch (error: any) {
      addLog(`❌ Hybrid services test failed: ${error.message}`)
      setTestResults(prev => ({
        ...prev,
        hybridServices: {
          success: false,
          error: error.message
        }
      }))
    }
    
    setIsLoading(false)
  }

  const clearLogs = () => {
    setLogs([])
    addLog("Logs cleared")
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">Database Connection Debugger</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <div className="bg-background/80 rounded-lg p-6 border border-primary/30 mb-6">
            <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
            <div className="space-y-4">
              <div className="flex flex-wrap gap-3">
                <Button 
                  variant="default" 
                  onClick={runAllTests}
                  disabled={isLoading}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Run All Tests
                </Button>
                <Button 
                  variant="default" 
                  onClick={runHybridServicesTest} 
                  disabled={isLoading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Test Hybrid Services
                </Button>
                <Button 
                  variant="outline" 
                  onClick={clearLogs}
                  disabled={isLoading}
                  className="text-foreground"
                >
                  Clear Logs
                </Button>
              </div>
              {isLoading && (
                <div className="animate-pulse text-center py-2">
                  Running tests...
                </div>
              )}
            </div>
          </div>
          
          <div className="bg-background/80 rounded-lg p-6 border border-primary/30">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Debug Logs</h2>
              <span className="text-xs text-foreground/60">{logs.length} entries</span>
            </div>
            <div 
              ref={logRef}
              className="bg-background p-4 rounded h-96 overflow-y-auto font-mono text-xs text-foreground/80"
            >
              {logs.map((log, index) => (
                <div key={index} className="pb-1">
                  {log}
                </div>
              ))}
              {logs.length === 0 && (
                <div className="text-center text-foreground/40 py-8">No logs yet</div>
              )}
            </div>
          </div>
        </div>
        
        <div className="lg:col-span-1">
          <div className="bg-background/80 rounded-lg p-6 border border-primary/30 sticky top-4">
            <h2 className="text-xl font-semibold mb-4">Test Results</h2>
            
            {Object.keys(testResults).length === 0 ? (
              <div className="text-center text-foreground/40 py-8">
                Run tests to see results
              </div>
            ) : (
              <div className="space-y-6">
                {testResults.directClient && (
                  <div className="border border-white/10 rounded p-4">
                    <h3 className="font-bold mb-2 flex items-center gap-2">
                      <span className={`w-3 h-3 rounded-full ${testResults.directClient.success ? 'bg-green-500' : 'bg-red-500'}`}></span>
                      Direct Client
                    </h3>
                    {testResults.directClient.success ? (
                      <div className="text-green-400 text-sm">Connected successfully</div>
                    ) : (
                      <div className="text-red-400 text-sm">{testResults.directClient.error}</div>
                    )}
                  </div>
                )}
                
                {testResults.browserClient && (
                  <div className="border border-white/10 rounded p-4">
                    <h3 className="font-bold mb-2 flex items-center gap-2">
                      <span className={`w-3 h-3 rounded-full ${testResults.browserClient.success ? 'bg-green-500' : 'bg-red-500'}`}></span>
                      Browser Client
                    </h3>
                    {testResults.browserClient.success ? (
                      <div className="text-green-400 text-sm">Connected successfully</div>
                    ) : (
                      <div className="text-red-400 text-sm">{testResults.browserClient.error}</div>
                    )}
                  </div>
                )}
                
                {testResults.directCreateClient && (
                  <div className="border border-white/10 rounded p-4">
                    <h3 className="font-bold mb-2 flex items-center gap-2">
                      <span className={`w-3 h-3 rounded-full ${testResults.directCreateClient.success ? 'bg-green-500' : 'bg-red-500'}`}></span>
                      Direct createClient
                    </h3>
                    {testResults.directCreateClient.success ? (
                      <div className="text-green-400 text-sm">Connected successfully</div>
                    ) : (
                      <div className="text-red-400 text-sm">{testResults.directCreateClient.error}</div>
                    )}
                  </div>
                )}
                
                {testResults.featuredDoctors && (
                  <div className="border border-white/10 rounded p-4">
                    <h3 className="font-bold mb-2 flex items-center gap-2">
                      <span className={`w-3 h-3 rounded-full ${testResults.featuredDoctors.success ? 'bg-green-500' : 'bg-red-500'}`}></span>
                      Featured Doctors
                    </h3>
                    {testResults.featuredDoctors.success ? (
                      <div className="text-green-400 text-sm">Retrieved {testResults.featuredDoctors.data?.length || 0} doctors</div>
                    ) : testResults.featuredDoctors.isFallback ? (
                      <div className="text-yellow-400 text-sm">Using fallback data</div>
                    ) : (
                      <div className="text-red-400 text-sm">{testResults.featuredDoctors.error}</div>
                    )}
                  </div>
                )}
                
                {testResults.hybridServices && (
                  <div className="border border-white/10 rounded p-4">
                    <h3 className="font-bold mb-2 flex items-center gap-2">
                      <span className={`w-3 h-3 rounded-full ${testResults.hybridServices.success ? 'bg-green-500' : 'bg-red-500'}`}></span>
                      Hybrid Services
                    </h3>
                    {testResults.hybridServices.success ? (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <span className={`w-2 h-2 rounded-full ${!testResults.hybridServices.featuredDoctors.isFallback ? 'bg-green-500' : 'bg-yellow-500'}`}></span>
                          <span className="text-sm">
                            Featured Doctors: {testResults.hybridServices.featuredDoctors.count} 
                            {testResults.hybridServices.featuredDoctors.isFallback ? ' (Fallback)' : ''}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className={`w-2 h-2 rounded-full ${!testResults.hybridServices.topDoctors.isFallback ? 'bg-green-500' : 'bg-yellow-500'}`}></span>
                          <span className="text-sm">
                            Top Doctors: {testResults.hybridServices.topDoctors.count}
                            {testResults.hybridServices.topDoctors.isFallback ? ' (Fallback)' : ''}
                          </span>
                        </div>
                      </div>
                    ) : (
                      <div className="text-red-400 text-sm">{testResults.hybridServices.error}</div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
} 