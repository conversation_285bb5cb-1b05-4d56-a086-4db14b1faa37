"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { 
  Alert, 
  AlertTitle, 
  AlertDescription 
} from "@/components/ui/alert"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { useToast } from "@/components/ui/use-toast"
import { updateDoctorProfileImage, uploadDoctorProfileImage } from "@/actions/doctor-registration-actions"
import { getSupabaseProfileImageUrl } from "@/app/lib/utils"
import { imageLogger } from "@/app/lib/image-logger"
import { createBrowserClient } from "@/lib/supabase"

/**
 * Component to diagnose and fix profile image issues
 */
export default function FixProfileImages() {
  const { toast } = useToast()
  const [doctorId, setDoctorId] = useState("")
  const [doctorInfo, setDoctorInfo] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [diagnosisResults, setDiagnosisResults] = useState<string[]>([])
  const [bucketInfo, setBucketInfo] = useState<any>(null)
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [uploading, setUploading] = useState(false)
  const [processingStatus, setProcessingStatus] = useState("")
  const [progress, setProgress] = useState(0)
  const [errorLogs, setErrorLogs] = useState<any[]>([])

  // Load image error logs on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const logs = JSON.parse(localStorage.getItem('image-error-logs') || '[]')
        setErrorLogs(logs)
      } catch (e) {
        console.error("Error loading error logs:", e)
      }
    }
  }, [])

  // Function to fetch doctor info and diagnose image issues
  const diagnoseDoctorImage = async () => {
    if (!doctorId) {
      toast({
        title: "Error",
        description: "Please enter a doctor ID",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    setDiagnosisResults([])
    setProgress(10)
    try {
      addDiagnosisStep("Starting diagnosis for doctor ID: " + doctorId)
      const supabase = await createBrowserClient()
      
      // Check if doctor exists
      const { data: doctorData, error: doctorError } = await supabase
        .from('doctors')
        .select('*')
        .eq('doctor_id', doctorId)
        .single()
      
      setProgress(30)
      
      if (doctorError) {
        addDiagnosisStep(`Error fetching doctor: ${doctorError.message}`)
        toast({
          title: "Error",
          description: "Could not find doctor with that ID",
          variant: "destructive"
        })
        setLoading(false)
        return
      }
      
      setDoctorInfo(doctorData)
      addDiagnosisStep(`Found doctor: ${doctorData.fullname || 'Unknown'}`)
      
      // Check image path
      if (!doctorData.profile_image) {
        addDiagnosisStep("No profile image path found in database")
      } else {
        addDiagnosisStep(`Found image path: ${doctorData.profile_image}`)
        
        // Check if image path is a local file path (common issue)
        if (doctorData.profile_image.startsWith('C:') || 
            doctorData.profile_image.startsWith('/') ||
            doctorData.profile_image.includes('\\')) {
          addDiagnosisStep(`⚠️ WARNING: Image path appears to be a local file path: ${doctorData.profile_image}`)
        }
        
        // Check if the image path has the correct format
        if (!doctorData.profile_image.startsWith('profile-images/') && 
            !doctorData.profile_image.startsWith('http')) {
          addDiagnosisStep(`⚠️ WARNING: Image path is missing 'profile-images/' prefix`)
        }
      }
      
      setProgress(50)
      
      // Check bucket existence
      const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets()
      
      if (bucketsError) {
        addDiagnosisStep(`Error checking storage buckets: ${bucketsError.message}`)
      } else {
        const profileBucket = buckets.find(b => b.name === 'profile-images')
        setBucketInfo(profileBucket)
        
        if (!profileBucket) {
          addDiagnosisStep(`⚠️ CRITICAL: 'profile-images' bucket does not exist in Supabase storage`)
        } else {
          addDiagnosisStep(`'profile-images' bucket exists`)
          
          // Check bucket public setting
          if (!profileBucket.public) {
            addDiagnosisStep(`⚠️ WARNING: 'profile-images' bucket is not public`)
          } else {
            addDiagnosisStep(`'profile-images' bucket is properly set to public`)
          }
          
          setProgress(70)
          
          // Check storage policies
          const { data: policies, error: policiesError } = await supabase.rpc('get_policies')
          
          if (policiesError) {
            addDiagnosisStep(`Error checking storage policies: ${policiesError.message}`)
          } else {
            const storagePolicies = policies.filter((p: any) => p.table === 'objects' && p.schema === 'storage')
            const hasPublicSelect = storagePolicies.some((p: any) => 
              p.definition.includes('bucket_id = \'profile-images\'') && 
              p.definition.includes('operation = \'SELECT\''))
              
            if (!hasPublicSelect) {
              addDiagnosisStep(`⚠️ WARNING: No public SELECT policy found for 'profile-images' bucket`)
            } else {
              addDiagnosisStep(`Public SELECT policy exists for 'profile-images' bucket`)
            }
          }
        }
      }
      
      setProgress(90)
      
      // Test the image URL if one exists
      if (doctorData.profile_image) {
        try {
          const imageUrl = getSupabaseProfileImageUrl(doctorData.profile_image)
          addDiagnosisStep(`Generated image URL: ${imageUrl}`)
          
          // Try to fetch the image to check if it exists
          const response = await fetch(imageUrl, { method: 'HEAD' })
          
          if (response.ok) {
            addDiagnosisStep(`✅ Image is accessible at URL`)
          } else {
            addDiagnosisStep(`⚠️ WARNING: Image is NOT accessible (HTTP ${response.status})`)
          }
        } catch (error) {
          addDiagnosisStep(`Error testing image URL: ${error instanceof Error ? error.message : String(error)}`)
        }
      }
      
      setProgress(100)
      addDiagnosisStep(`Diagnosis complete.`)
      
    } catch (error) {
      addDiagnosisStep(`Error during diagnosis: ${error instanceof Error ? error.message : String(error)}`)
      toast({
        title: "Error",
        description: "An error occurred during diagnosis",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }
  
  // Helper to add diagnosis steps
  const addDiagnosisStep = (step: string) => {
    setDiagnosisResults(prev => [...prev, step])
  }
  
  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setImageFile(file)
    }
  }
  
  // Function to upload a new image and update the doctor's profile
  const uploadNewImage = async () => {
    if (!doctorInfo || !imageFile) {
      toast({
        title: "Error",
        description: "Doctor information or image file is missing",
        variant: "destructive"
      })
      return
    }
    
    setUploading(true)
    setProcessingStatus("Uploading image...")
    setProgress(10)
    
    try {
      // Upload the image
      const { profileImageUrl, error } = await uploadDoctorProfileImage(
        doctorInfo.doctor_id.toString(), 
        imageFile
      )
      
      setProgress(50)
      setProcessingStatus("Updating doctor profile...")
      
      if (error) {
        throw new Error(`Failed to upload image: ${error.message || JSON.stringify(error)}`)
      }
      
      // Update doctor record
      if (!profileImageUrl) {
        throw new Error("No profile image URL returned after upload")
      }
      
      setProgress(100)
      setProcessingStatus("Done!")
      
      toast({
        title: "Success",
        description: "Image uploaded and profile updated successfully",
      })
      
      // Refresh doctor info
      await diagnoseDoctorImage()
      
    } catch (error) {
      console.error("Error uploading image:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive"
      })
    } finally {
      setUploading(false)
      setImageFile(null)
      // Reset the file input
      const fileInput = document.getElementById('profile-image-upload') as HTMLInputElement
      if (fileInput) {
        fileInput.value = ''
      }
    }
  }
  
  // Function to clear error logs
  const clearErrorLogs = () => {
    imageLogger.clearErrorLogs()
    setErrorLogs([])
    toast({
      title: "Success",
      description: "Image error logs cleared"
    })
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Profile Image Diagnostics & Repair</h1>
      
      <Tabs defaultValue="diagnosis">
        <TabsList className="mb-6">
          <TabsTrigger value="diagnosis">Diagnose & Fix</TabsTrigger>
          <TabsTrigger value="logs">Error Logs</TabsTrigger>
          <TabsTrigger value="utilities">Utilities</TabsTrigger>
        </TabsList>
        
        <TabsContent value="diagnosis">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Doctor lookup and diagnosis */}
            <Card>
              <CardHeader>
                <CardTitle>Doctor Profile Image Diagnosis</CardTitle>
                <CardDescription>Diagnose image issues for a specific doctor</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-end gap-4 mb-4">
                  <div className="flex-1">
                    <label className="text-sm font-medium mb-1 block">Doctor ID</label>
                    <Input 
                      placeholder="Enter doctor ID" 
                      value={doctorId}
                      onChange={(e) => setDoctorId(e.target.value)}
                    />
                  </div>
                  <Button onClick={diagnoseDoctorImage} disabled={loading}>
                    {loading ? "Diagnosing..." : "Diagnose"}
                  </Button>
                </div>
                
                {loading && (
                  <Progress value={progress} className="mb-4" />
                )}
                
                {diagnosisResults.length > 0 && (
                  <div className="mt-4">
                    <h3 className="text-lg font-semibold mb-2">Diagnosis Results</h3>
                    <div className="bg-background/10 p-4 rounded-md font-mono text-sm overflow-auto max-h-80">
                      {diagnosisResults.map((result, i) => (
                        <div key={i} className="mb-1">
                          {result}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          
            {/* Doctor info and fix options */}
            <Card>
              <CardHeader>
                <CardTitle>Doctor Information</CardTitle>
                <CardDescription>View and fix profile image</CardDescription>
              </CardHeader>
              <CardContent>
                {doctorInfo ? (
                  <div className="space-y-4">
                    <div className="flex items-center gap-4">
                      <Avatar className="h-16 w-16 border-2">
                        <AvatarImage 
                          src={doctorInfo.profile_image ? getSupabaseProfileImageUrl(doctorInfo.profile_image) : undefined} 
                          alt={doctorInfo.fullname || 'Doctor'}
                        />
                        <AvatarFallback>
                          {doctorInfo.fullname ? doctorInfo.fullname.split(' ').map((part: string) => part[0]).join('').toUpperCase() : 'DR'}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold">{doctorInfo.fullname || 'Unknown'}</h3>
                        <p className="text-sm text-muted-green">ID: {doctorInfo.doctor_id}</p>
                        {doctorInfo.profile_image && (
                          <p className="text-xs mt-1 text-muted-green truncate max-w-[300px]">
                            Image: {doctorInfo.profile_image}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    <div className="border-t pt-4">
                      <h4 className="font-medium mb-2">Upload New Image</h4>
                      <div className="flex items-end gap-4">
                        <div className="flex-1">
                          <Input 
                            id="profile-image-upload"
                            type="file" 
                            accept="image/*"
                            onChange={handleFileChange}
                          />
                        </div>
                        <Button 
                          onClick={uploadNewImage} 
                          disabled={uploading || !imageFile}
                        >
                          {uploading ? "Uploading..." : "Upload"}
                        </Button>
                      </div>
                      
                      {uploading && (
                        <div className="mt-4">
                          <Progress value={progress} className="mb-2" />
                          <p className="text-center text-sm">{processingStatus}</p>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-green">
                    No doctor information available. Run a diagnosis first.
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="logs">
          <Card>
            <CardHeader>
              <CardTitle>Image Error Logs</CardTitle>
              <CardDescription>View errors that occurred during image loading</CardDescription>
            </CardHeader>
            <CardContent>
              {errorLogs.length > 0 ? (
                <div>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Timestamp</TableHead>
                        <TableHead>Component</TableHead>
                        <TableHead>Message</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {errorLogs.map((log, i) => (
                        <TableRow key={i}>
                          <TableCell className="whitespace-nowrap">{new Date(log.timestamp).toLocaleString()}</TableCell>
                          <TableCell>{log.component}</TableCell>
                          <TableCell>{log.message}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  <div className="mt-4 flex justify-end">
                    <Button variant="destructive" onClick={clearErrorLogs}>
                      Clear Error Logs
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-green">
                  No image error logs found.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="utilities">
          <Card>
            <CardHeader>
              <CardTitle>Storage Bucket Fix</CardTitle>
              <CardDescription>
                Run this to create the required storage bucket and policies if they don't exist
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Textarea 
                className="font-mono"
                value={`-- Run this in Supabase SQL Editor
                
-- 1. Make sure the profile-images bucket exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM storage.buckets WHERE name = 'profile-images'
    ) THEN
        INSERT INTO storage.buckets (id, name, public)
        VALUES ('profile-images', 'profile-images', true);
    END IF;
END $$;

-- 2. Ensure the bucket is public
UPDATE storage.buckets
SET public = true
WHERE name = 'profile-images';

-- 3. Set up proper storage policies for the profile-images bucket
-- First, delete any existing policies to avoid conflicts
DELETE FROM storage.policies 
WHERE bucket_id = 'profile-images';

-- Add policy for public read access (everyone can view images)
INSERT INTO storage.policies (bucket_id, name, definition, owner)
VALUES (
    'profile-images',
    'Public Read Access',
    '{"statement":"SELECT", "allow":true, "using":"true", "resource":"*"}'::jsonb,
    'postgres'
);

-- Add policy for authenticated uploads (only authenticated users can upload)
INSERT INTO storage.policies (bucket_id, name, definition, owner)
VALUES (
    'profile-images',
    'Authenticated Upload',
    '{"statement":"INSERT", "allow":true, "using":"auth.role() = ''authenticated''", "resource":"*"}'::jsonb,
    'postgres'
);

-- Add policy for authenticated updates
INSERT INTO storage.policies (bucket_id, name, definition, owner)
VALUES (
    'profile-images',
    'Authenticated Update',
    '{"statement":"UPDATE", "allow":true, "using":"auth.role() = ''authenticated''", "resource":"*"}'::jsonb,
    'postgres'
);`}
                readOnly
                rows={20}
              />
              <p className="mt-4 text-sm text-muted-green">
                Copy this SQL and run it in your Supabase SQL Editor to fix storage bucket issues.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 