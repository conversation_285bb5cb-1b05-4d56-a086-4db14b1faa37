"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { getSupabaseProfileImageUrl } from "@/app/lib/utils"
import Image from "next/image"
import { Check, X } from "lucide-react"

export default function ImageUrlTester() {
  const [imageUrl, setImageUrl] = useState("")
  const [imageStatus, setImageStatus] = useState<"idle" | "loading" | "success" | "error">("idle")
  const [statusMessage, setStatusMessage] = useState("")
  const [processedUrl, setProcessedUrl] = useState("")
  const [headers, setHeaders] = useState<Record<string, string>>({})

  // Test if an image URL is accessible
  const testImage = async () => {
    if (!imageUrl.trim()) return
    
    setImageStatus("loading")
    setStatusMessage("Testing image URL...")
    setHeaders({})
    
    try {
      // Process URL through the utility function
      const url = getSupabaseProfileImageUrl(imageUrl)
      setProcessedUrl(url)
      
      // Check if the URL is accessible
      const response = await fetch(url, { method: 'HEAD' })
      
      // Store headers for display
      const headerObj: Record<string, string> = {}
      response.headers.forEach((value, key) => {
        headerObj[key] = value
      })
      setHeaders(headerObj)
      
      if (response.ok) {
        setImageStatus("success")
        setStatusMessage(`Image is accessible (HTTP ${response.status})`)
      } else {
        setImageStatus("error")
        setStatusMessage(`Image is NOT accessible (HTTP ${response.status})`)
      }
    } catch (error) {
      setImageStatus("error")
      setStatusMessage(`Error accessing URL: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Image URL Tester</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Test Image URL</CardTitle>
            <CardDescription>Check if an image URL or storage path is accessible</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium block mb-1">
                  Image Path or URL
                </label>
                <div className="flex gap-2">
                  <Input
                    placeholder="e.g., profile-images/doctor-123.jpg or full URL"
                    value={imageUrl}
                    onChange={(e) => setImageUrl(e.target.value)}
                  />
                  <Button onClick={testImage} disabled={imageStatus === "loading"}>
                    Test
                  </Button>
                </div>
                <p className="text-xs text-muted-green mt-1">
                  Enter a Supabase storage path or a full URL
                </p>
              </div>
              
              {imageStatus !== "idle" && (
                <div className="mt-4">
                  <div className="flex items-center gap-2">
                    {imageStatus === "loading" && (
                      <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
                    )}
                    {imageStatus === "success" && (
                      <Check className="text-green-500 h-5 w-5" />
                    )}
                    {imageStatus === "error" && (
                      <X className="text-red-500 h-5 w-5" />
                    )}
                    <span className={`${imageStatus === "success" ? "text-green-500" : imageStatus === "error" ? "text-red-500" : ""}`}>
                      {statusMessage}
                    </span>
                  </div>
                </div>
              )}
              
              {processedUrl && (
                <div className="mt-4">
                  <h3 className="text-sm font-medium mb-1">Processed URL</h3>
                  <div className="bg-muted-green p-2 rounded text-xs font-mono break-all">
                    {processedUrl}
                  </div>
                </div>
              )}
              
              {Object.keys(headers).length > 0 && (
                <div className="mt-4">
                  <h3 className="text-sm font-medium mb-1">Response Headers</h3>
                  <div className="bg-muted-green p-2 rounded text-xs font-mono">
                    {Object.entries(headers).map(([key, value]) => (
                      <div key={key} className="mb-1">
                        <span className="text-primary">{key}</span>: {value}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Image Preview</CardTitle>
            <CardDescription>See if the image loads correctly</CardDescription>
          </CardHeader>
          <CardContent>
            {processedUrl ? (
              <div className="flex flex-col items-center space-y-4">
                <div className="border-2 border-muted rounded-md overflow-hidden relative w-60 h-60 bg-green-100">
                  <img
                    src={processedUrl}
                    alt="Image preview"
                    className="object-contain w-full h-full"
                    onError={() => {
                      setImageStatus("error")
                      setStatusMessage("Image failed to load")
                    }}
                    onLoad={() => {
                      if (imageStatus !== "success") {
                        setImageStatus("success")
                        setStatusMessage("Image loaded successfully")
                      }
                    }}
                  />
                </div>
                <div className="text-sm text-center">
                  {imageStatus === "success" ? (
                    <span className="text-green-500">Image loaded successfully</span>
                  ) : imageStatus === "error" ? (
                    <span className="text-red-500">Image failed to load</span>
                  ) : (
                    <span>Loading image...</span>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-60 text-muted-green">
                Enter an image URL to preview
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Common Issues & Solutions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium">CORS Errors</h3>
              <p className="text-sm text-muted-green">
                If you're seeing CORS errors in the console, make sure the bucket is properly configured with CORS policies. 
                In Supabase, go to Storage → Buckets → profile-images → Settings → CORS configuration.
              </p>
            </div>
            
            <div>
              <h3 className="font-medium">404 Not Found</h3>
              <p className="text-sm text-muted-green">
                If the image returns a 404, check that the path is correct and the file exists in the bucket.
                Also verify the 'profile-images' bucket exists and has the right policies.
              </p>
            </div>
            
            <div>
              <h3 className="font-medium">Local File Paths</h3>
              <p className="text-sm text-muted-green">
                If you see paths starting with 'C:\' or local file paths in your database, they need to be updated to Supabase storage paths.
                Use the "Fix Profile Images" tool to upload a new image that will be properly stored in Supabase.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 