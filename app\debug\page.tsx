import { ArrowRight, FileImage, Database, Settings, TestTube, Images } from "lucide-react"
import Link from "next/link"

export default function DebugPage() {
  return (
    <div className="container mx-auto py-12">
      <h1 className="text-4xl font-bold mb-6">Diagnostics & Debug Tools</h1>
      <p className="text-lg text-muted-green mb-12">
        Utilities for diagnosing and fixing issues with your Doctor's League application
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Image Diagnostics */}
        <Link href="/debug/fix-profile-images" className="group">
          <div className="p-6 border rounded-lg bg-card transition-all 
            hover:shadow-md hover:border-primary/50 group-hover:bg-muted-green/50">
            <div className="flex items-center mb-4">
              <div className="rounded-full p-2 bg-primary/10 text-primary mr-4">
                <Images className="w-6 h-6" />
              </div>
              <h2 className="text-xl font-semibold">Profile Image Fixer</h2>
            </div>
            <p className="text-muted-green mb-6">
              Diagnose and fix doctor profile image issues. Upload new images and check storage settings.
            </p>
            <div className="flex items-center text-primary transition-all 
              group-hover:translate-x-1">
              <span>Fix profile images</span>
              <ArrowRight className="ml-2 w-4 h-4" />
            </div>
          </div>
        </Link>
        
        {/* Image URL Tester */}
        <Link href="/debug/image-url-tester" className="group">
          <div className="p-6 border rounded-lg bg-card transition-all 
            hover:shadow-md hover:border-primary/50 group-hover:bg-muted-green/50">
            <div className="flex items-center mb-4">
              <div className="rounded-full p-2 bg-primary/10 text-primary mr-4">
                <FileImage className="w-6 h-6" />
              </div>
              <h2 className="text-xl font-semibold">Image URL Tester</h2>
            </div>
            <p className="text-muted-green mb-6">
              Test image URLs and storage paths to see if they're accessible. Diagnose CORS and other image loading issues.
            </p>
            <div className="flex items-center text-primary transition-all 
              group-hover:translate-x-1">
              <span>Test image URLs</span>
              <ArrowRight className="ml-2 w-4 h-4" />
            </div>
          </div>
        </Link>
        
        {/* Supabase Bucket Helper */}
        <Link href="/debug/supabase/bucket-checker" className="group">
          <div className="p-6 border rounded-lg bg-card transition-all 
            hover:shadow-md hover:border-primary/50 group-hover:bg-muted-green/50">
            <div className="flex items-center mb-4">
              <div className="rounded-full p-2 bg-primary/10 text-primary mr-4">
                <Database className="w-6 h-6" />
              </div>
              <h2 className="text-xl font-semibold">Supabase Helpers</h2>
            </div>
            <p className="text-muted-green mb-6">
              Helper functions and SQL for Supabase. Create functions needed for diagnostic tools to work properly.
            </p>
            <div className="flex items-center text-primary transition-all 
              group-hover:translate-x-1">
              <span>Setup helper functions</span>
              <ArrowRight className="ml-2 w-4 h-4" />
            </div>
          </div>
        </Link>
      </div>
      
      <div className="mt-12 p-6 border rounded-lg bg-muted-green/30">
        <h2 className="text-xl font-semibold mb-4">Understanding Image Issues</h2>
        <p className="mb-4">
          The profile image loading issues are typically caused by one of these problems:
        </p>
        <ul className="list-disc list-inside space-y-2 text-muted-green">
          <li>
            <strong>Missing profile-images bucket:</strong> The Supabase storage bucket doesn't exist or has incorrect permissions
          </li>
          <li>
            <strong>Incorrect image paths in the database:</strong> Local file paths (starting with C:\ or /) instead of Supabase paths
          </li>
          <li>
            <strong>CORS configuration issues:</strong> Improper CORS settings in Supabase preventing image loading
          </li>
          <li>
            <strong>Missing database entry:</strong> No profile_image value stored in the doctor's record
          </li>
        </ul>
        <p className="mt-4">
          Use the tools above to diagnose exactly which issue is affecting your system, then follow the recommended fixes.
        </p>
      </div>
    </div>
  )
} 