"use client"

import { Textarea } from "@/components/ui/textarea"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { useState } from "react"
import { Check, Copy } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

export default function BucketChecker() {
  const { toast } = useToast()
  const [copied, setCopied] = useState(false)
  
  const policiesFunction = `-- Create a function to read policies for diagnostics
CREATE OR REPLACE FUNCTION public.get_policies()
RETURNS TABLE (
  schema text, 
  table text, 
  definition text, 
  operation text,
  policy_name text
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.schemaname::text,
    p.tablename::text,
    pg_policies.definition::text,
    pg_policies.cmd::text as operation,
    pg_policies.policyname::text
  FROM pg_policies
  JOIN pg_class c ON c.relname = pg_policies.tablename
  JOIN pg_namespace n ON n.oid = c.relnamespace AND n.nspname = pg_policies.schemaname
  JOIN pg_catalog.pg_tables p ON p.schemaname = pg_policies.schemaname AND p.tablename = pg_policies.tablename
  ORDER BY p.schemaname, p.tablename, pg_policies.policyname;
END;
$$;

-- Grant execute permissions to the function
GRANT EXECUTE ON FUNCTION public.get_policies() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_policies() TO anon;
GRANT EXECUTE ON FUNCTION public.get_policies() TO service_role;`

  const copyToClipboard = async () => {
    await navigator.clipboard.writeText(policiesFunction)
    setCopied(true)
    toast({
      title: "Copied to clipboard",
      description: "SQL has been copied to your clipboard"
    })
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Supabase Helper Functions</h1>
      
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Policy Lookup Function</CardTitle>
          <CardDescription>
            Add this function to your Supabase database to enable the image diagnostics tool to check storage policies.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Textarea
              className="font-mono text-sm"
              rows={20}
              value={policiesFunction}
              readOnly
            />
            <Button 
              className="absolute top-3 right-3" 
              size="sm" 
              onClick={copyToClipboard}
              variant="outline"
            >
              {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              {copied ? "Copied" : "Copy"}
            </Button>
          </div>
          <p className="mt-4 text-sm text-muted-green">
            Execute this SQL in your Supabase SQL Editor to create a function that can retrieve all policies.
            This function is required for the image diagnostics tool to check storage bucket permissions.
          </p>
        </CardContent>
      </Card>
    </div>
  )
} 