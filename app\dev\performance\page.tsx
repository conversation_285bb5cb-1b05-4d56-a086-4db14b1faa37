"use client"

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { detectFeatures, getAllPerformanceMetrics, getPerformanceGuidance } from '@/lib/performance'

export default function PerformancePage() {
  const [metrics, setMetrics] = useState<any>(null)
  const [features, setFeatures] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Collect performance metrics after a slight delay to ensure page is loaded
    const timer = setTimeout(() => {
      try {
        const performanceMetrics = getAllPerformanceMetrics()
        const browserFeatures = detectFeatures()
        
        setMetrics(performanceMetrics)
        setFeatures(browserFeatures)
      } catch (error) {
        console.error('Error collecting metrics:', error)
      } finally {
        setIsLoading(false)
      }
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // Safely access nested properties with defaults to prevent runtime errors
  const getSafeValue = (obj: any, path: string, defaultValue: any = null) => {
    try {
      return path.split('.').reduce((o, key) => (o && o[key] !== undefined) ? o[key] : defaultValue, obj);
    } catch (err) {
      return defaultValue;
    }
  }

  // Format a time value in ms
  const formatTime = (timeMs: number) => {
    if (!timeMs && timeMs !== 0) return 'N/A';
    if (timeMs < 1000) return `${Math.round(timeMs)}ms`
    return `${(timeMs / 1000).toFixed(2)}s`
  }

  // Format a size value in bytes
  const formatSize = (bytes: number) => {
    if (!bytes && bytes !== 0) return 'N/A';
    if (bytes < 1024) return `${bytes} B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} KB`
    return `${(bytes / (1024 * 1024)).toFixed(2)} MB`
  }

  // Get color based on performance rating
  const getRatingColor = (value: number, metricName: string): string => {
    try {
      if (typeof value !== 'number') return 'bg-background/60';
      
      switch (metricName) {
        case 'LCP':
          return value < 2500 ? 'bg-green-500' : value < 4000 ? 'bg-yellow-500' : 'bg-red-500'
        case 'FID':
          return value < 100 ? 'bg-green-500' : value < 300 ? 'bg-yellow-500' : 'bg-red-500'
        case 'CLS':
          return value < 0.1 ? 'bg-green-500' : value < 0.25 ? 'bg-yellow-500' : 'bg-red-500'
        case 'TTFB':
          return value < 800 ? 'bg-green-500' : value < 1800 ? 'bg-yellow-500' : 'bg-red-500'
        default:
          return 'bg-blue-500'
      }
    } catch (error) {
      console.error('Error in getRatingColor:', error);
      return 'bg-background/60';
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8 text-foreground">Core Web Vitals Dashboard</h1>
      
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : metrics ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Timing Metrics */}
          <Card>
            <CardHeader>
              <CardTitle className="text-green-900">Navigation Timing</CardTitle>
              <CardDescription className="text-muted-green">Key timing metrics for page load</CardDescription>
            </CardHeader>
            <CardContent>
              {metrics.navigation ? (
                <div className="space-y-4">
                  <div className="flex flex-col">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-muted-green">TTFB</span>
                      <span className="text-sm text-muted-green">{formatTime(getSafeValue(metrics, 'derived.ttfb', 0))}</span>
                    </div>
                    <div className="w-full bg-green-200 rounded-full h-2.5">
                      <div 
                        className={`h-2.5 rounded-full ${getRatingColor(getSafeValue(metrics, 'derived.ttfb', 0), 'TTFB')}`} 
                        style={{ width: `${Math.min(100, (getSafeValue(metrics, 'derived.ttfb', 0) / 2000) * 100)}%` }}
                      ></div>
                    </div>
                    <span className="text-xs mt-1 text-muted-green">
                      {(getPerformanceGuidance('TTFB').split('.')[0] || '') + '.'}
                    </span>
                  </div>
                  
                  <div className="flex flex-col">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-muted-green">DOM Interactive</span>
                      <span className="text-sm text-muted-green">{formatTime(getSafeValue(metrics, 'navigation.domInteractive', 0))}</span>
                    </div>
                    <div className="w-full bg-green-200 rounded-full h-2.5">
                      <div 
                        className="bg-blue-500 h-2.5 rounded-full" 
                        style={{ width: `${Math.min(100, (getSafeValue(metrics, 'navigation.domInteractive', 0) / 5000) * 100)}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div className="flex flex-col">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-muted-green">DOM Complete</span>
                      <span className="text-sm text-muted-green">{formatTime(getSafeValue(metrics, 'navigation.domComplete', 0))}</span>
                    </div>
                    <div className="w-full bg-green-200 rounded-full h-2.5">
                      <div 
                        className="bg-blue-500 h-2.5 rounded-full" 
                        style={{ width: `${Math.min(100, (getSafeValue(metrics, 'navigation.domComplete', 0) / 5000) * 100)}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div className="flex flex-col">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-muted-green">Load Complete</span>
                      <span className="text-sm text-muted-green">{formatTime(getSafeValue(metrics, 'navigation.loadEventEnd', 0))}</span>
                    </div>
                    <div className="w-full bg-green-200 rounded-full h-2.5">
                      <div 
                        className="bg-blue-500 h-2.5 rounded-full" 
                        style={{ width: `${Math.min(100, (getSafeValue(metrics, 'navigation.loadEventEnd', 0) / 5000) * 100)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-muted-green">Navigation timing data not available</p>
              )}
            </CardContent>
          </Card>
          
          {/* Resource Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-green-900">Resource Statistics</CardTitle>
              <CardDescription className="text-muted-green">Network and asset information</CardDescription>
            </CardHeader>
            <CardContent>
              {metrics.resources ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-green-100 p-4 rounded-lg">
                      <p className="text-sm text-muted-green">Resources</p>
                      <p className="text-2xl font-bold text-green-900">{getSafeValue(metrics, 'resources.totalResources', 0)}</p>
                    </div>
                    <div className="bg-green-100 p-4 rounded-lg">
                      <p className="text-sm text-muted-green">Total Size</p>
                      <p className="text-2xl font-bold text-green-900">{formatSize(getSafeValue(metrics, 'resources.totalResourceSize', 0))}</p>
                    </div>
                  </div>
                  
                  {getSafeValue(metrics, 'resources.resourcesByType') && (
                    <div>
                      <h3 className="text-sm font-medium mb-2 text-muted-green">Resource Types</h3>
                      <div className="flex flex-wrap gap-2">
                        {Object.entries(getSafeValue(metrics, 'resources.resourcesByType', {})).map(([type, count]) => (
                          <Badge key={type} variant="outline" className="text-muted-green">
                            {type}: {count as number}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {getSafeValue(metrics, 'resources.slowestResource') && (
                    <div>
                      <h3 className="text-sm font-medium mb-2 text-muted-green">Slowest Resource</h3>
                      <p className="text-sm truncate text-muted-green">{getSafeValue(metrics, 'resources.slowestResource.name', 'Unknown')}</p>
                      <p className="text-xs text-muted-green">
                        {formatTime(getSafeValue(metrics, 'resources.slowestResource.duration', 0))}
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-muted-green">Resource data not available</p>
              )}
            </CardContent>
          </Card>
          
          {/* Browser Features */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle className="text-green-900">Browser Capabilities</CardTitle>
              <CardDescription className="text-muted-green">Features supported by the current browser</CardDescription>
            </CardHeader>
            <CardContent>
              {features ? (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-green-100 p-4 rounded-lg">
                    <p className="text-sm text-muted-green">WebP Support</p>
                    <div className="flex items-center mt-2">
                      <div className={`w-3 h-3 rounded-full mr-2 ${getSafeValue(features, 'webp', false) ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <p className="text-sm font-medium text-muted-green">{getSafeValue(features, 'webp', false) ? 'Supported' : 'Not Supported'}</p>
                    </div>
                  </div>
                  
                  <div className="bg-green-100 p-4 rounded-lg">
                    <p className="text-sm text-muted-green">AVIF Support</p>
                    <div className="flex items-center mt-2">
                      <div className={`w-3 h-3 rounded-full mr-2 ${getSafeValue(features, 'avif', false) ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <p className="text-sm font-medium text-muted-green">{getSafeValue(features, 'avif', false) ? 'Supported' : 'Not Supported'}</p>
                    </div>
                  </div>
                  
                  <div className="bg-green-100 p-4 rounded-lg">
                    <p className="text-sm text-muted-green">Intersection Observer</p>
                    <div className="flex items-center mt-2">
                      <div className={`w-3 h-3 rounded-full mr-2 ${getSafeValue(features, 'intersectionObserver', false) ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <p className="text-sm font-medium text-muted-green">{getSafeValue(features, 'intersectionObserver', false) ? 'Supported' : 'Not Supported'}</p>
                    </div>
                  </div>
                  
                  <div className="bg-green-100 p-4 rounded-lg">
                    <p className="text-sm text-muted-green">Service Worker</p>
                    <div className="flex items-center mt-2">
                      <div className={`w-3 h-3 rounded-full mr-2 ${getSafeValue(features, 'serviceWorker', false) ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <p className="text-sm font-medium text-muted-green">{getSafeValue(features, 'serviceWorker', false) ? 'Supported' : 'Not Supported'}</p>
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-muted-green">Feature detection data not available</p>
              )}
            </CardContent>
          </Card>
          
          {/* Core Web Vitals Explainer */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle className="text-green-900">Improving Core Web Vitals</CardTitle>
              <CardDescription className="text-muted-green">Recommendations to enhance performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 border border-yellow-200 bg-yellow-50 rounded-lg">
                  <h3 className="text-sm font-medium flex items-center text-green-900">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    Largest Contentful Paint (LCP)
                  </h3>
                  <p className="text-sm mt-1 text-muted-green">
                    {(() => {
                      try {
                        return getPerformanceGuidance('LCP');
                      } catch (e) {
                        return 'Optimize your largest content element to load quickly.';
                      }
                    })()}
                  </p>
                </div>
                
                <div className="p-4 border border-yellow-200 bg-yellow-50 rounded-lg">
                  <h3 className="text-sm font-medium flex items-center text-green-900">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    First Input Delay (FID)
                  </h3>
                  <p className="text-sm mt-1 text-muted-green">
                    {(() => {
                      try {
                        return getPerformanceGuidance('FID');
                      } catch (e) {
                        return 'Improve responsiveness to user interactions.';
                      }
                    })()}
                  </p>
                </div>
                
                <div className="p-4 border border-yellow-200 bg-yellow-50 rounded-lg">
                  <h3 className="text-sm font-medium flex items-center text-green-900">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    Cumulative Layout Shift (CLS)
                  </h3>
                  <p className="text-sm mt-1 text-muted-green">
                    {(() => {
                      try {
                        return getPerformanceGuidance('CLS');
                      } catch (e) {
                        return 'Minimize unexpected layout shifts during page load.';
                      }
                    })()}
                  </p>
                </div>
                
                <div className="p-4 border border-blue-200 bg-blue-50 rounded-lg">
                  <h3 className="text-sm font-medium flex items-center text-green-900">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    New: Interaction to Next Paint (INP)
                  </h3>
                  <p className="text-sm mt-1 text-muted-green">
                    {(() => {
                      try {
                        return getPerformanceGuidance('INP');
                      } catch (e) {
                        return 'Optimize event handlers to respond quickly to user interactions.';
                      }
                    })()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : (
        <p className="text-foreground">No performance metrics available</p>
      )}
    </div>
  )
} 