"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Info, AlertTriangle, Check, RefreshCw, Link as LinkIcon } from "lucide-react"
import { SEOHealthCheck } from "@/components/seo"
import { useRouter, useSearchParams } from 'next/navigation'

export default function SEOToolsPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  // Get URL parameters
  const urlParam = searchParams?.get('url') || null
  const titleParam = searchParams?.get('title') || null
  
  const [isDev, setIsDev] = useState(false)
  const [url, setUrl] = useState('https://doctorsleagues.com/about')
  const [pageTitle, setPageTitle] = useState("SEO Tools")
  
  // Set initial values from URL parameters if available
  useEffect(() => {
    if (urlParam) {
      const fullUrl = `https://doctorsleagues.com${urlParam}`
      setUrl(fullUrl)
    }
    if (titleParam) {
      setPageTitle(titleParam)
    }
  }, [urlParam, titleParam])
  
  // Placeholder schemas for demonstration
  const demoSchemas = [
    {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      'name': "Doctor's Leagues",
      'url': 'https://doctorsleagues.com',
      'logo': 'https://doctorsleagues.com/logo.png'
    },
    {
      '@context': 'https://schema.org',
      '@type': 'WebPage',
      'name': pageTitle,
      'description': 'Development tools for SEO testing and validation',
      'url': url
    }
  ]

  useEffect(() => {
    // Check if we're in development mode
    setIsDev(process.env.NODE_ENV === 'development')
    
    // Redirect in production
    if (process.env.NODE_ENV === 'production') {
      router.push('/')
    }
  }, [router])

  if (!isDev) {
    return (
      <div className="container mx-auto px-4 py-32 flex justify-center">
        <Alert variant="destructive" className="max-w-lg">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Development Only</AlertTitle>
          <AlertDescription>
            This page is only accessible in development mode. You will be redirected to the homepage shortly.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">SEO Development Tools</h1>
        <p className="text-muted-green mt-2">
          Tools to help test and validate SEO elements on the Doctor's Leagues website.
        </p>
        
        {urlParam && (
          <Alert className="mt-4 bg-primary/5 border-primary/20">
            <LinkIcon className="h-4 w-4 text-primary" />
            <AlertTitle>Analyzing Page</AlertTitle>
            <AlertDescription className="text-primary">
              {url.replace('https://doctorsleagues.com', '')}
            </AlertDescription>
          </Alert>
        )}
      </div>
      
      <div className="mb-8">
        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Development Only</AlertTitle>
          <AlertDescription>
            These tools are only available in development mode and will not be accessible in production.
          </AlertDescription>
        </Alert>
      </div>
      
      <Tabs defaultValue="health-check">
        <TabsList className="mb-6">
          <TabsTrigger value="health-check">SEO Health Check</TabsTrigger>
          <TabsTrigger value="schema-tester">Schema Tester</TabsTrigger>
          <TabsTrigger value="meta-tags">Meta Tags</TabsTrigger>
        </TabsList>
        
        <TabsContent value="health-check">
          <Card>
            <CardHeader>
              <CardTitle>SEO Health Check</CardTitle>
              <CardDescription>
                Analyze and validate the SEO elements of a page. Enter the URL to check.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-6">
                <div className="flex items-end gap-4">
                  <div className="flex-1">
                    <Label htmlFor="page-url">Page URL</Label>
                    <Input 
                      id="page-url" 
                      value={url} 
                      onChange={(e) => setUrl(e.target.value)}
                      placeholder="https://doctorsleagues.com/about" 
                    />
                  </div>
                  <div className="flex-1">
                    <Label htmlFor="page-title">Page Title</Label>
                    <Input 
                      id="page-title" 
                      value={pageTitle} 
                      onChange={(e) => setPageTitle(e.target.value)}
                      placeholder="Page Title" 
                    />
                  </div>
                  <Button type="button" className="flex items-center gap-2">
                    <RefreshCw className="h-4 w-4" />
                    Check
                  </Button>
                </div>
              </div>
              
              <Separator className="my-6" />
              
              <div>
                <p className="text-sm text-muted-green mb-4">SEO Health Check Tool:</p>
                <SEOHealthCheck 
                  schemas={demoSchemas}
                  pageUrl={url}
                  pageTitle={pageTitle}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="schema-tester">
          <Card>
            <CardHeader>
              <CardTitle>Schema Markup Tester</CardTitle>
              <CardDescription>
                Test and validate JSON-LD schema markup for different page types.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-12 flex items-center justify-center">
                <div className="text-center">
                  <p className="text-lg font-medium mb-2">Schema Testing Tools</p>
                  <p className="text-muted-green mb-6">Coming soon in a future update</p>
                  <div className="flex gap-4 justify-center">
                    <Button variant="outline" onClick={() => window.open('https://validator.schema.org/', '_blank')}>
                      Google Schema Validator
                    </Button>
                    <Button variant="outline" onClick={() => window.open('https://search.google.com/test/rich-results', '_blank')}>
                      Rich Results Test
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="meta-tags">
          <Card>
            <CardHeader>
              <CardTitle>Meta Tags Preview</CardTitle>
              <CardDescription>
                Preview how your page will appear in search results and social media.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-12 flex items-center justify-center">
                <div className="text-center">
                  <p className="text-lg font-medium mb-2">Meta Tag Preview Tools</p>
                  <p className="text-muted-green mb-6">Coming soon in a future update</p>
                  <div className="flex gap-4 justify-center">
                    <Button variant="outline" onClick={() => window.open('https://developers.facebook.com/tools/debug/', '_blank')}>
                      Facebook Debugger
                    </Button>
                    <Button variant="outline" onClick={() => window.open('https://cards-dev.twitter.com/validator', '_blank')}>
                      Twitter Card Validator
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 