"use client"

import { useState, useEffect } from "react"
import { supabase, createBrowserClient } from "@/lib/supabase-client"

export default function DirectClientTestPage() {
  const [results, setResults] = useState<any>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function testDirectClient() {
      try {
        // Test 1: Direct import of supabase client
        console.log("Testing direct supabase import...")
        const { data: countriesData, error: countriesError } = await supabase
          .from('countries')
          .select('*')
          .limit(3)

        // Test 2: Browser client function
        console.log("Testing createBrowserClient...")
        const browserClient = createBrowserClient()
        const { data: specialtiesData, error: specialtiesError } = await browserClient
          .from('specialties')
          .select('*')
          .limit(3)

        // Log for debugging
        console.log("Countries fetch result:", { data: countriesData, error: countriesError })
        console.log("Specialties fetch result:", { data: specialtiesData, error: specialtiesError })

        setResults({
          directImport: {
            success: !countriesError,
            count: countriesData?.length || 0,
            error: countriesError ? countriesError.message : null,
            firstItem: countriesData?.[0]
          },
          browserClient: {
            success: !specialtiesError,
            count: specialtiesData?.length || 0,
            error: specialtiesError ? specialtiesError.message : null,
            firstItem: specialtiesData?.[0]
          }
        })
      } catch (error: any) {
        console.error("Test failed with exception:", error)
        setResults({
          error: error.message
        })
      } finally {
        setLoading(false)
      }
    }

    testDirectClient()
  }, [])

  return (
    <div className="container mx-auto py-12 px-4">
      <h1 className="text-3xl font-bold mb-6">Direct Supabase Client Test</h1>
      
      {loading ? (
        <div className="animate-pulse text-center">Testing direct Supabase client...</div>
      ) : (
        <div className="max-w-2xl mx-auto bg-background/80 rounded-lg p-6 border border-primary/30">
          <h2 className="text-xl font-semibold mb-4">Test Results:</h2>
          
          {results.error ? (
            <div className="bg-red-900/30 p-4 rounded border border-red-500">
              <h3 className="font-bold text-red-400">Test Failed with Exception</h3>
              <p className="text-foreground/80 mt-2">{results.error}</p>
            </div>
          ) : (
            <>
              <div className="mb-6">
                <h3 className="font-bold text-lg mb-2">1. Direct Supabase Import</h3>
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-4 h-4 rounded-full ${results.directImport?.success ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span>{results.directImport?.success ? 'Success' : 'Failed'}</span>
                </div>
                
                {results.directImport?.error && (
                  <div className="mt-2 text-red-400 text-sm">
                    Error: {results.directImport.error}
                  </div>
                )}
                
                {results.directImport?.success && (
                  <div className="mt-2">
                    <p className="text-green-400">Retrieved {results.directImport.count} countries</p>
                    {results.directImport.firstItem && (
                      <div className="mt-2 p-3 bg-background/50 rounded">
                        <p className="font-semibold">Sample data:</p>
                        <pre className="text-xs mt-1 overflow-auto">
                          {JSON.stringify(results.directImport.firstItem, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                )}
              </div>
              
              <div>
                <h3 className="font-bold text-lg mb-2">2. Created Browser Client</h3>
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-4 h-4 rounded-full ${results.browserClient?.success ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span>{results.browserClient?.success ? 'Success' : 'Failed'}</span>
                </div>
                
                {results.browserClient?.error && (
                  <div className="mt-2 text-red-400 text-sm">
                    Error: {results.browserClient.error}
                  </div>
                )}
                
                {results.browserClient?.success && (
                  <div className="mt-2">
                    <p className="text-green-400">Retrieved {results.browserClient.count} specialties</p>
                    {results.browserClient.firstItem && (
                      <div className="mt-2 p-3 bg-background/50 rounded">
                        <p className="font-semibold">Sample data:</p>
                        <pre className="text-xs mt-1 overflow-auto">
                          {JSON.stringify(results.browserClient.firstItem, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </>
          )}
          
          <div className="mt-8 p-4 bg-background/90 rounded">
            <h3 className="font-semibold">Conclusion:</h3>
            <p className="mt-2">
              {results.directImport?.success && results.browserClient?.success 
                ? "Both the direct import and the browser client are working correctly. If pages aren't showing data, the issue may be elsewhere."
                : results.directImport?.success 
                  ? "The direct supabase import is working, but the browser client is not."
                  : results.browserClient?.success
                    ? "The browser client is working, but direct supabase import is not."
                    : "Both methods are failing. Database connection is not established."}
            </p>
          </div>
        </div>
      )}
    </div>
  )
} 