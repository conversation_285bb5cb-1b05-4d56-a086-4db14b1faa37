"use client"

import React, { useState, use<PERSON><PERSON>back, useMemo } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { ChevronDown, ChevronUp, Trophy, Star, ArrowUpRight } from "lucide-react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { getImageUrl } from "@/lib/utils"

// Doctor interface
export interface Doctor {
  doctor_id: string | number
  fullname: string
  profile_image?: string
  specialty?: string
  specialty_name?: string
  community_rating?: number
  wins?: number
  losses?: number
  draws?: number
  country?: string
  rank?: number
  hospitals?: {
    hospital_name?: string
  }
  facility?: string
  review_count?: number
  form?: string
}

// Sorting options
type SortField = "community_rating" | "wins" | "losses" | "draws" | "name"
type SortDirection = "asc" | "desc"

export function DoctorsTable({ doctors }: { doctors: Doctor[] }) {
  const router = useRouter()
  const [sortField, setSortField] = useState<SortField>("community_rating")
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc")

  // Handle sorting changes
  const handleSort = useCallback((field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("desc") // Default to descending when changing fields
    }
  }, [sortField, sortDirection])

  // Helper for displaying sort indicators
  const getSortIndicator = useCallback((field: SortField) => {
    if (sortField !== field) return null
    return sortDirection === "asc" ? (
      <ChevronUp className="ml-1 h-4 w-4" />
    ) : (
      <ChevronDown className="ml-1 h-4 w-4" />
    )
  }, [sortField, sortDirection])

  // Helper to calculate tiebreakers based on wins, losses, draws, community_rating
  const calculateRank = useCallback((doc: Doctor): number => {
    const winPoints = (doc.wins || 0) * 3
    const drawPoints = (doc.draws || 0) * 1
    // Normalized community_rating score (0-10 range)
    const ratingScore = (doc.community_rating || 0) * 0.5
    return winPoints + drawPoints - (doc.losses || 0) + ratingScore
  }, [])

  // Prepare sorted doctors with proper fallback for empty array
  const sortedDoctors = useMemo(() => {
    if (!doctors || doctors.length === 0) {
      return [];
    }
    
    let result = [...doctors];
    
    if (sortField) {
      result.sort((a, b) => {
        if (sortField === "name") {
          return sortDirection === "asc"
            ? (a.fullname || "").localeCompare(b.fullname || "")
            : (b.fullname || "").localeCompare(a.fullname || "");
        }
        
        const aValue = a[sortField as keyof typeof a] || 0;
        const bValue = b[sortField as keyof typeof b] || 0;
        
        return sortDirection === "asc" ? (aValue as number) - (bValue as number) : (bValue as number) - (aValue as number);
      });
    }
    
    return result;
  }, [doctors, sortField, sortDirection]);

  // Handle row click to navigate to doctor's page
  const handleRowClick = useCallback((doctorId: string | number) => {
    router.push(`/doctors/${doctorId}`)
  }, [router])

  // Helper to render form indicators (W, L, D)
  const renderFormIndicator = useCallback((type: 'win' | 'loss' | 'draw', count: number) => {
    if (count <= 0) return null
    
    const getColor = () => {
      switch (type) {
        case 'win': return 'bg-green-500 text-foreground'
        case 'loss': return 'bg-red-500 text-foreground'
        case 'draw': return 'bg-yellow-500 text-foreground'
        default: return 'bg-background/60 text-foreground'
      }
    }
    
    const getLabel = () => {
      switch (type) {
        case 'win': return 'W'
        case 'loss': return 'L'
        case 'draw': return 'D'
        default: return ''
      }
    }
    
    return (
      <Badge className={`${getColor()} mr-1`}>
        {getLabel()} {count}
      </Badge>
    )
  }, [])

  if (doctors.length === 0) {
    return (
      <Card className="w-full mb-8" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
        <CardContent className="p-6 flex flex-col items-center justify-center space-y-4">
          <div className="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center">
            <Trophy className="h-8 w-8 text-green-600" />
          </div>
          <p className="text-lg font-medium text-center">No doctors found for this specialty</p>
          <p className="text-muted-green text-center max-w-md">
            There are currently no doctors registered in this specialty. Check back later or explore other specialties.
          </p>
          <Button 
            onClick={() => router.back()}
            className="mt-4 bg-green-600 hover:bg-green-700"
          >
            Go Back
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full overflow-hidden" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
      <div className="w-full">
        <Table className="w-full">
          <TableHeader className="bg-gradient-to-r from-background to-background/90 sticky top-0">
            <TableRow>
              <TableHead className="text-foreground font-semibold w-[8%]">Rank</TableHead>
              <TableHead className="text-foreground font-semibold w-[35%]">Doctor</TableHead>
              <TableHead
                className="text-foreground font-semibold cursor-pointer w-[15%]"
                onClick={() => handleSort("community_rating")}
              >
                <div className="flex items-center">
                  Rating
                  {getSortIndicator("community_rating")}
                </div>
              </TableHead>
              <TableHead 
                className="text-foreground font-semibold cursor-pointer text-center w-[8%]" 
                onClick={() => handleSort("wins")}
              >
                <div className="flex items-center justify-center">
                  Wins
                  {getSortIndicator("wins")}
                </div>
              </TableHead>
              <TableHead 
                className="text-foreground font-semibold cursor-pointer text-center w-[8%]" 
                onClick={() => handleSort("losses")}
              >
                <div className="flex items-center justify-center">
                  Losses
                  {getSortIndicator("losses")}
                </div>
              </TableHead>
              <TableHead 
                className="text-foreground font-semibold cursor-pointer text-center w-[8%]" 
                onClick={() => handleSort("draws")}
              >
                <div className="flex items-center justify-center">
                  Draws
                  {getSortIndicator("draws")}
                </div>
              </TableHead>
              <TableHead className="text-foreground font-semibold hidden md:table-cell">Form</TableHead>
              <TableHead className="text-foreground font-semibold w-[12%] text-center">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {doctors.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center text-foreground/70 py-8">
                  No doctors have joined this division yet.
                </TableCell>
              </TableRow>
            ) : (
              sortedDoctors.map((doctor, index) => (
                <TableRow 
                  key={doctor.doctor_id}
                  className="border-b border-green-500/10 bg-gradient-to-r from-background/90 to-background/80 hover:bg-background/60 cursor-pointer transition-colors"
                  onClick={() => handleRowClick(doctor.doctor_id)}
                >
                  <TableCell className="font-medium text-foreground">
                    <Badge className={`
                      ${index < 3 ? 'bg-gradient-to-r' : 'bg-background/40 border border-white/20 text-foreground/80'} 
                      ${index === 0 ? 'from-yellow-500 to-amber-400' : ''} 
                      ${index === 1 ? 'from-gray-300 to-gray-400' : ''} 
                      ${index === 2 ? 'from-amber-700 to-amber-600' : ''}
                    `}>
                      {index + 1}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar>
                        <AvatarImage src={getImageUrl(doctor.profile_image)} alt={doctor.fullname} />
                        <AvatarFallback>
                          {doctor.fullname?.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-semibold text-foreground">{doctor.fullname}</div>
                        <div className="text-sm text-foreground/70">{doctor.specialty_name || doctor.specialty}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center">
                              {Array.from({ length: 5 }).map((_, i) => (
                                <Star 
                                  key={i}
                                  className={`h-3 w-3 md:h-4 md:w-4 ${i < Math.floor(doctor.community_rating || 0) ? 'text-yellow-400 fill-yellow-400' : 'text-muted-green'}`}
                                />
                              ))}
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Rating: {(doctor.community_rating || 0).toFixed(1)}/5</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <span className="text-green-400 font-semibold">{doctor.wins || 0}</span>
                  </TableCell>
                  <TableCell className="text-center">
                    <span className="text-red-400 font-semibold">{doctor.losses || 0}</span>
                  </TableCell>
                  <TableCell className="text-center">
                    <span className="text-yellow-400 font-semibold">{doctor.draws || 0}</span>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    <div className="flex">
                      {renderFormIndicator('win', doctor.wins || 0)}
                      {renderFormIndicator('loss', doctor.losses || 0)}
                      {renderFormIndicator('draw', doctor.draws || 0)}
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <Button variant="outline" size="sm" className="group">
                      View <ArrowUpRight className="ml-1 h-4 w-4 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </Card>
  )
}

