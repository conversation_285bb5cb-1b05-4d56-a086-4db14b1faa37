"use client"

import React, { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { CalendarDays, Users, Stethoscope, ArrowUpRight } from "lucide-react"

function SafeImage({ src, alt, ...props }: React.ComponentProps<typeof Image>) {
  const [imgSrc, setImgSrc] = useState(src)
  
  return (
    <Image 
      {...props}
      src={imgSrc}
      alt={alt}
      onError={() => setImgSrc("/placeholder.svg")}
    />
  )
}

export function DivisionsBannerAd() {
  return (
    <Card className="w-full mb-6 overflow-hidden bg-gradient-to-r from-primary/30 to-background/30 border border-primary/20">
      <CardContent className="p-0">
        <div className="flex flex-col md:flex-row items-center">
          <div className="relative w-full md:w-1/3 h-48 md:h-full">
            <SafeImage 
              src="/events/medical-conference.jpg" 
              alt="Medical Conference" 
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-transparent to-background/80 md:from-transparent md:to-background/80" />
          </div>
          
          <div className="p-6 flex-1">
            <Badge variant="outline" className="mb-2 bg-primary/20 text-foreground border-primary/30">
              Featured Event
            </Badge>
            <h3 className="text-xl md:text-2xl font-bold text-foreground mb-2">
              International Medical Sports Conference 2023
            </h3>
            <p className="text-foreground/80 mb-4 text-sm md:text-base">
              Join leading medical professionals from around the world at this premier event discussing the latest advances in sports medicine.
            </p>
            
            <div className="flex flex-wrap gap-4 mb-4">
              <div className="flex items-center gap-2 text-foreground/70">
                <CalendarDays className="h-4 w-4 text-primary" />
                <span>June 15-18, 2023</span>
              </div>
              <div className="flex items-center gap-2 text-foreground/70">
                <Users className="h-4 w-4 text-primary" />
                <span>500+ Attendees</span>
              </div>
              <div className="flex items-center gap-2 text-foreground/70">
                <Stethoscope className="h-4 w-4 text-primary" />
                <span>CME Credits Available</span>
              </div>
            </div>
            
            <Button asChild className="group">
              <a href="https://example.com/conference" target="_blank" rel="noopener noreferrer">
                Register Now
                <ArrowUpRight className="ml-2 h-4 w-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform" />
              </a>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function DivisionsSidebarAd() {
  return (
    <div className="space-y-4">
      <Card className="overflow-hidden border border-primary/20 bg-background/40">
        <div className="relative h-40">
          <SafeImage 
            src="/events/medical-equipment.jpg" 
            alt="Medical Equipment" 
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-background to-transparent" />
          <Badge className="absolute top-2 right-2 bg-primary text-foreground">
            Sponsored
          </Badge>
        </div>
        
        <CardContent className="p-4">
          <h4 className="text-lg font-semibold text-foreground mb-2">
            Advanced Medical Tools
          </h4>
          <p className="text-foreground/70 text-sm mb-4">
            Discover the latest innovations in medical technology. Special discount for league members.
          </p>
          <Button variant="outline" size="sm" className="w-full bg-primary/10 border-primary/20 hover:bg-primary/20">
            Learn More
          </Button>
        </CardContent>
      </Card>
      
      <Card className="overflow-hidden border border-primary/20 bg-background/40">
        <CardContent className="p-4">
          <h4 className="text-lg font-semibold text-foreground mb-2 flex items-center gap-2">
            <Stethoscope className="h-5 w-5 text-primary" />
            Join Elite Medical Network
          </h4>
          <p className="text-foreground/70 text-sm mb-4">
            Connect with top medical professionals worldwide. Exclusive opportunities for collaboration and research.
          </p>
          <Button variant="default" size="sm" className="w-full">
            Sign Up Now
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

export function DivisionsSideLeftAd() {
  return (
    <div className="hidden xl:block fixed left-4 top-1/4 w-48 z-10">
      <Card className="overflow-hidden border border-primary/20 bg-background/40">
        <div className="relative h-72">
          <SafeImage 
            src="/events/medical-vertical.jpg" 
            alt="Medical Research" 
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-background via-background/50 to-transparent" />
        </div>
        
        <CardContent className="p-4">
          <Badge className="mb-2 bg-primary text-foreground">
            Research Opportunity
          </Badge>
          <h4 className="text-base font-semibold text-foreground mb-2">
            Medical Research Grants
          </h4>
          <p className="text-foreground/70 text-xs mb-4">
            Apply for research funding in sports medicine and related fields.
          </p>
          <Button variant="outline" size="sm" className="w-full bg-primary/10 border-primary/20 hover:bg-primary/20 text-xs">
            Apply Now
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
