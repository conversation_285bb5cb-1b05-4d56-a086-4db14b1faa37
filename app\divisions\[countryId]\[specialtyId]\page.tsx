import React from "react"
import Link from "next/link"
import Image from "next/image"
import { notFound } from "next/navigation"
import { B<PERSON><PERSON>rumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { ChevronRightIcon, GlobeIcon, StarIcon, Trophy, FlameIcon, Flag } from "lucide-react"
import { getCountryById, getCountries } from "@/lib/hybrid-data-service"
import { getSpecialtyById } from "@/lib/hybrid-data-service"
import { getDoctorsByCountryAndSpecialty } from "@/lib/hybrid-data-service"
import { DoctorsTable } from "./doctors-table"
import { PageAds } from "@/components/ads/page-ads"
import { getSpecialtyIconPath } from "@/lib/getSpecialtyIconPath"
import { getSpecialtyDoctorsCount, getActiveDoctorsCount, getTotalMatchesCount, getSpecialtyRankingScore } from "@/lib/medleague/statistics"
import { Metadata } from "next"
import { SchemaMarkup, generateBreadcrumbSchema, LeagueCountryMetadata } from "@/components/seo"

interface PageProps {
  params: {
    countryId: string
    specialtyId: string
  }
}

export async function generateMetadata({ 
  params 
}: PageProps): Promise<Metadata> {
  const { countryId, specialtyId } = params
  
  // Fetch country and specialty data
  const [country, specialty] = await Promise.all([
    getCountryById(countryId),
    getSpecialtyById(specialtyId)
  ])
  
  if (!country || !specialty) {
    return {
      title: "Division Not Found",
      description: "The league or division you are looking for could not be found."
    }
  }
  
  return {
    title: `${specialty.specialty_name} Doctors League - ${country.country_name} | Rankings & Statistics`,
    description: `View the latest rankings, statistics, and standings for ${specialty.specialty_name} doctors in ${country.country_name}. Compare medical professionals and find the top-rated specialists.`,
    keywords: `${specialty.specialty_name}, doctors league, ${country.country_name}, medical rankings, doctor statistics, top doctors, healthcare rankings, medical leaderboard`,
  }
}

export default async function DivisionPage({ params }: PageProps) {
  const { countryId, specialtyId } = params
  
  console.log(`Rendering division page for countryId: ${countryId}, specialtyId: ${specialtyId}`)
  
  if (!countryId || !specialtyId) {
    console.error("Missing required parameters: countryId or specialtyId")
    return notFound()
  }

  try {
    // Fetch country, specialty and doctors data in parallel, along with statistics
    const [country, specialty, doctors, allCountries] = await Promise.all([
      getCountryById(countryId),
      getSpecialtyById(specialtyId),
      getDoctorsByCountryAndSpecialty(countryId, specialtyId),
      getCountries()
    ])

    if (!country || !specialty) {
      console.error(`Country or specialty not found. Country: ${!!country}, Specialty: ${!!specialty}`)
      return notFound()
    }

    console.log(`Found ${doctors?.length || 0} doctors for this division`)
    
    // Get all available countries for hreflang tags
    const countryIds: string[] = allCountries.map(country => String(country.country_id))
    
    // Create league slug from specialty name and country
    const leagueSlug = `${specialty.specialty_name.toLowerCase().replace(/\s+/g, '-')}-${country.country_name.toLowerCase().replace(/\s+/g, '-')}`
    
    // Breadcrumb data for schema
    const breadcrumbItems = [
      { position: 1, name: "Home", url: "/" },
      { position: 2, name: "Divisions", url: "/divisions" },
      { position: 3, name: country.country_name, url: `/divisions/${countryId}` },
      { position: 4, name: specialty.specialty_name, url: `/divisions/${countryId}/${specialtyId}` }
    ]
    
    // Generate breadcrumb schema
    const breadcrumbSchema = generateBreadcrumbSchema(breadcrumbItems)

    // Now try to get the statistics - handle each separately so one failure doesn't affect others
    let totalDoctors = 0;
    let activeDoctors = 0;
    let totalMatches = 0;
    let rankingScore = 0;

    try {
      totalDoctors = await getSpecialtyDoctorsCount(countryId, specialtyId);
    } catch (error) {
      console.error("Error fetching doctor count:", error);
      totalDoctors = doctors?.length || 0; // Fallback to length of doctors array
    }

    try {
      activeDoctors = await getActiveDoctorsCount(countryId, specialtyId);
    } catch (error) {
      console.error("Error fetching active doctor count:", error);
      // Fallback to approximate count based on doctors with activity
      activeDoctors = doctors?.filter((doc: any) => 
        (doc.wins || 0) + (doc.losses || 0) + (doc.draws || 0) > 0
      ).length || 0;
    }

    console.log(`Active Doctors Count: ${activeDoctors}`);

    try {
      totalMatches = await getTotalMatchesCount(countryId, specialtyId);
    } catch (error) {
      console.error("Error fetching total matches:", error);
      // Fallback to sum of all matches
      totalMatches = doctors?.reduce((sum: number, doc: any) => 
        sum + (doc.wins || 0) + (doc.losses || 0) + (doc.draws || 0)
      , 0) || 0;
    }

    try {
      // Only fetch ranking if we have doctors
      if (totalDoctors > 0) {
        rankingScore = await getSpecialtyRankingScore(countryId, specialtyId);
      } else {
        rankingScore = 0; // No ranking if there are no doctors
      }
    } catch (error) {
      console.error("Error fetching ranking score:", error);
      // Fallback to a score based on specialty ID only if there are doctors
      if (totalDoctors > 0) {
        const specialtyIdNum = parseInt(specialtyId, 10) || 0;
        rankingScore = ((specialtyIdNum * 17) % 30) + 70; // Score between 70-100
      } else {
        rankingScore = 0;
      }
    }

    console.log(`Statistics - Total Doctors: ${totalDoctors}, Active: ${activeDoctors}, Matches: ${totalMatches}, Ranking: ${rankingScore}%`)

    const specialtyIconSrc = getSpecialtyIconPath(specialty.specialty_name)

    return (
      <>
        {/* SEO Schema Markup */}
        <SchemaMarkup schema={breadcrumbSchema} />
        <LeagueCountryMetadata
          leagueSlug={leagueSlug}
          countries={countryIds}
          currentCountry={countryId}
        />
        
        <div className="flex-1 space-y-6 lg:space-y-8">
          
          {/* Page Header */}
          <div 
            className="p-6 rounded-lg shadow-md bg-cover bg-center" 
            style={{ backgroundImage: "url('/placeholder/team-banner.jpg')", backgroundColor: 'hsl(142, 76%, 36%)', border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem' }}
          >
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-white rounded-full">
                  <Image 
                    src={specialtyIconSrc} 
                    alt={`${specialty.specialty_name} icon`}
                    width={32}
                    height={32}
                    className="h-8 w-8"
                  />
                </div>
                <div>
                  <h1 className="text-3xl font-bold tracking-tight text-white">{specialty.specialty_name}</h1>
                  <p className="text-lg text-white/80">
                    A league for top-rated {specialty.specialty_name} specialists in {country.country_name}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Breadcrumb Navigation */}
          <Breadcrumb className="mb-6">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Home</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/divisions">Divisions</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href={`/divisions/${country.country_id}`}>{country.country_name}</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>{specialty.specialty_name}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card className="bg-primary/90 text-primary-foreground">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Doctors</CardTitle>
                <Trophy className="h-5 w-5 text-primary-foreground/80" />
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{totalDoctors}</div>
                <p className="text-xs text-primary-foreground/80">Registered in this division</p>
              </CardContent>
            </Card>
            <Card className="bg-primary/90 text-primary-foreground">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Doctors</CardTitle>
                <FlameIcon className="h-5 w-5 text-primary-foreground/80" />
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{activeDoctors}</div>
                <p className="text-xs text-primary-foreground/80">Currently competing</p>
              </CardContent>
            </Card>
            <Card className="bg-primary/90 text-primary-foreground">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Matches</CardTitle>
                <Flag className="h-5 w-5 text-primary-foreground/80" />
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{totalMatches}</div>
                <p className="text-xs text-primary-foreground/80">Played in this division</p>
              </CardContent>
            </Card>
          </div>

        {/* Banner Ad */}
          <div className="my-6">
            <PageAds pageName="divisions" positions={['banner']} />
          </div>

          {/* Doctors Table - Give it full width */}
          <div className="w-full mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-foreground flex items-center">
                <Trophy className="h-5 w-5 text-yellow-500 mr-2" />
                League Rankings
              </h2>
            </div>
            <Separator className="bg-green-500/20 mb-4" />
            <DoctorsTable doctors={doctors || []} />
          </div>
        </div>
      </>
    )
  } catch (error) {
    console.error("Error fetching division data:", error)
    return (
      <div className="container mx-auto px-4 py-12">
        <Card className="bg-background/50 border border-red-500/40">
          <CardContent className="p-6">
            <div className="text-center">
              <h2 className="text-xl font-bold text-foreground mb-4">Error Loading Division</h2>
              <p className="text-foreground/70 mb-6">
                We encountered an error while loading this division. Please try again later.
              </p>
              <Button asChild>
                <Link href="/divisions">
                  <ChevronRightIcon className="h-4 w-4 mr-2" />
                  Return to Divisions
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }
}
