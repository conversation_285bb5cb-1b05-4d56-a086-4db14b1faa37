// Import the updated functions and types
import { getCountryById, getSpecialtiesByCountryId, getSpecialties, CountryWithDoctorCount, Specialty } from "@/lib/hybrid-data-service" // Added getSpecialties
import { SpecialtyCard } from "./specialty-card"
// Import only the icons we're actually using
import { Trophy, Activity, Info, Users, Shield } from "lucide-react"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
// Import statistics functions needed for server-side fetching
import { getSpecialtyDoctorsCount, getSpecialtyRankingScore } from "@/lib/medleague/statistics"
import { CountryFlagComponent } from "../country-flag-component"
import { Badge } from "@/components/ui/badge"
// Remove unused imports
// import { FALLBACK_COUNTRIES } from "@/lib/fallback-data"
// import { createClient } from "@supabase/supabase-js"

// Set route segment config for dynamic page with ISR-like caching
export const revalidate = 3600; // Revalidate this page once per hour (in seconds)

interface CountryDivisionsPageProps {
  params: {
    countryId: string
  }
}

export default async function CountryDivisionsPage({ params }: CountryDivisionsPageProps) {
  // In Next.js 13+, params are already resolved, so no need to await them
  // Just use them directly, but let's destructure for clarity
  const { countryId } = params;

  console.log(`Rendering CountryDivisionsPage for countryId: ${countryId}`);

  // Early validation
  if (!countryId) {
    console.error("Missing countryId parameter");
    return (
      <div className="container mx-auto py-12 text-center">
        <h1 className="text-3xl font-bold">Country Not Found</h1>
        <p className="mt-4">Invalid or missing country ID.</p>
        <Link href="/">
          <Button className="mt-6">Go to Home</Button>
        </Link>
      </div>
    );
  }

  console.log(`Fetching country with ID: ${countryId}`);

  // REMOVE direct client creation

  try {
    // Fetch country (with count) and ALL specialties in parallel
    const [countryResult, countrySpecialties, allSpecialties] = await Promise.all([
      getCountryById(countryId), // Now returns CountryWithDoctorCount | null
      getSpecialtiesByCountryId(countryId), // Fetch country-specific specialties
      getSpecialties() // Fetch ALL specialties to ensure we have the full set
    ]);

    // Add detailed logging for troubleshooting
    console.log(`DEBUGGING SPECIALTIES COUNT: Retrieved ${countrySpecialties.length} country-specific specialties and ${allSpecialties.length} total specialties for country: ${countryId}`);
    
    // Create a combined set of specialties to ensure we show all 36
    // First, create a map of specialty IDs that are already in countrySpecialties
    const existingSpecialtyIds = new Set(countrySpecialties.map((s: Specialty) => String(s.specialty_id)));
    
    // Then, add any specialties from allSpecialties that aren't already included
    const missingSpecialties = allSpecialties.filter((s: Specialty) => !existingSpecialtyIds.has(String(s.specialty_id)));
    
    // Combine the arrays, putting country-specific specialties first
    const specialties = [...countrySpecialties, ...missingSpecialties];
    
    console.log(`Combined specialties total: ${specialties.length} (${countrySpecialties.length} country-specific + ${missingSpecialties.length} missing)`);
    
    if (specialties.length > 0) {
      console.log(`Sample specialty IDs: ${specialties.slice(0, 3).map(s => s.specialty_id).join(', ')}...`);
    }

    // Check if countryResult is null or doesn't have expected properties
    // Also check the type guard for doctor_count
    if (!countryResult || !countryResult.country_name || typeof countryResult.doctor_count === 'undefined') {
      console.error(`No country found for ID: ${countryId} using hybrid service.`);
      return (
        <div className="container mx-auto py-12 text-center">
          <h1 className="text-3xl font-bold">Country Not Found</h1>
          <p className="mt-4">The country you're looking for doesn't exist in our database.</p>
          <Link href="/">
            <Button className="mt-6">Go to Home</Button>
          </Link>
        </div>
      );
    }

    // Assign the fetched country data (now includes doctor_count)
    const country = countryResult; // Type is CountryWithDoctorCount
    const countryDoctorsCount = country.doctor_count; // Get count directly
    console.log(`Successfully retrieved country: ${country.country_name} with ${countryDoctorsCount} doctors.`);

    // Specialties are fetched specifically for this country
    console.log(`Retrieved ${specialties.length} total specialties (${countrySpecialties.length} country-specific + ${missingSpecialties.length} additional) for country: ${country.country_name}`);

    // Fetch stats for each specialty in parallel
    const specialtiesWithStats = await Promise.all(
      specialties.map(async (specialty) => {
        try {
          const [doctorCount, rankingScore] = await Promise.all([
            getSpecialtyDoctorsCount(countryId, String(specialty.specialty_id)),
            getSpecialtyRankingScore(countryId, String(specialty.specialty_id))
          ]);
          return { ...specialty, doctorCount, rankingScore };
        } catch (error) {
          console.error(`Error fetching stats for specialty ${specialty.specialty_name}:`, error);
          // Return the specialty with default stats if there's an error
          return { ...specialty, doctorCount: 0, rankingScore: 0 };
        }
      })
    );

    console.log(`Fetched stats for ${specialtiesWithStats.length} total specialties.`);

    // Rest of component rendering
    return (
      <div className="relative bg-gradient-to-b from-background via-background to-primary/20 -mt-20 pt-20">
        <div className="container mx-auto py-8 px-4 sm:px-6 lg:max-w-7xl">
          {/* Breadcrumb Navigation */}
          <nav className="flex items-center gap-1 text-sm text-muted-green mb-6">
            <Link href="/" className="hover:text-primary transition-colors">
              Home
            </Link>
            <span>/</span>
            <Link href="/divisions" className="hover:text-primary transition-colors">
              Divisions
            </Link>
            <span>/</span>
            <span className="text-foreground font-medium">{country.country_name}</span>
          </nav>

          {/* Hero Header */}
          <div className="rounded-lg overflow-hidden mb-8 relative">
            <div className="absolute inset-0 bg-gradient-to-r from-primary/30 to-transparent z-0" />
            <div className="relative z-10 p-6 md:p-8 flex flex-col md:flex-row gap-6 md:items-center backdrop-blur-sm bg-background/30 border border-primary/20">
              <div className="flex-shrink-0">
                <div className="w-20 h-14 md:w-24 md:h-16 relative overflow-hidden rounded-md border-2 border-primary/30 shadow-md">
                  <CountryFlagComponent
                    countryName={country.country_name}
                    width="100%"
                    height="100%"
                  />
                </div>
              </div>

              <div className="flex-1">
                <h1 className="text-2xl md:text-3xl font-bold hero-title mb-2">
                  {country.country_name} Medical Divisions
                </h1>
                <p className="hero-text max-w-3xl">
                  Browse all medical specialties available in {country.country_name}.
                  Each division represents a medical specialty with its top-ranked doctors competing in the league.
                </p>
              </div>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
            <Card className="bg-background/40 border-primary/20" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
              <CardContent className="flex items-center gap-4 p-4">
                <div className="rounded-full bg-primary/10 p-3">
                  <Trophy className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-green">Specialties</p>
                  <div className="flex items-center gap-2">
                    <p className="text-xl font-bold text-foreground">{specialties.length}</p>
                    <Badge variant="outline" className="text-xs bg-transparent">
                      {countrySpecialties.length} Active
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-background/40 border-primary/20" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
              <CardContent className="flex items-center gap-4 p-4">
                <div className="rounded-full bg-primary/10 p-3">
                  <Users className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-green">Participating Doctors</p>
                  <p className="text-xl font-bold text-foreground">{countryDoctorsCount || 0}</p>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-background/40 border-primary/20" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
              <CardContent className="flex items-center gap-4 p-4">
                <div className="rounded-full bg-primary/10 p-3">
                  <Shield className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-green">Rating System</p>
                  <p className="text-xl font-bold text-foreground">Performance</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Medical Specialties Content */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-foreground flex items-center gap-2">
                {country.country_name} Medical Specialties
              </h2>
              {/* Removed 'View All' button since all specialties are shown */}
            </div>

            {specialties.length === 0 ? (
              <Card className="bg-background/40 border-primary/20">
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <div className="bg-primary/10 p-4 rounded-full mb-4">
                    <Info className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold text-foreground mb-2">No Specialties Found</h3>
                  <p className="text-muted-green text-center max-w-md">
                    There are currently no medical specialties registered for {country.country_name}.
                    Check back soon or explore other countries.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {specialtiesWithStats
                  .slice() // Create a copy to avoid mutating the original array
                  .sort((a, b) => a.specialty_name.localeCompare(b.specialty_name)) // Sort alphabetically
                  .map((specialty) => (
                  <SpecialtyCard
                    key={String(specialty.specialty_id)}
                    specialty={{
                      specialty_id: String(specialty.specialty_id),
                      specialty_name: specialty.specialty_name,
                      description: specialty.description
                    }}
                    countryId={String(country.country_id)}
                    countryName={country.country_name}
                    countryFlagUrl={country.flag_url || `https://flagcdn.com/w320/${country.country_name === 'UAE' ? 'ae' : country.country_name.substring(0, 2).toLowerCase()}.png`}
                    // Pass fetched stats as props
                    doctorCount={specialty.doctorCount}
                    rankingScore={specialty.rankingScore}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Footer CTA */}
          <div className="mt-12">
            <Card className="hero-card border-primary/20 overflow-hidden">
              <CardContent className="p-6 sm:p-8">
                <div className="flex flex-col sm:flex-row gap-6 items-center">
                  <div className="flex-1">
                    <h3 className="text-xl sm:text-2xl font-bold hero-text mb-2">
                      Join the {country.country_name} Medical League
                    </h3>
                    <p className="text-muted-green mb-4">
                      Are you a medical professional in {country.country_name}? Join our platform
                      to connect with peers, track your performance, and enhance your career.
                    </p>
                    <div className="flex flex-wrap gap-3">
                      <Button asChild>
                        <Link href="/doctor/register">Register as Doctor</Link>
                      </Button>
                      <Button variant="outline" className="bg-primary/10 border-primary/20 hover:bg-primary/20" asChild>
                        <Link href="/help">Learn More</Link>
                      </Button>
                    </div>
                  </div>
                  <div className="hidden sm:block">
                    <div className="relative w-32 h-32 lg:w-40 lg:h-40">
                      <div className="absolute inset-0 rounded-full bg-primary/20 animate-pulse" />
                      <div className="absolute inset-4 rounded-full bg-primary/30 animate-pulse [animation-delay:200ms]" />
                      <div className="absolute inset-8 rounded-full bg-primary/40 animate-pulse [animation-delay:400ms]" />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Activity className="h-12 w-12 text-foreground" />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  } catch (error: any) {
    console.error(`Error in CountryDivisionsPage:`, error);

    // Return an error UI
    return (
      <div className="container mx-auto py-12 text-center">
        <Card className="max-w-lg mx-auto bg-background/50 border-red-500/30">
          <CardContent className="pt-6">
            <h1 className="text-3xl font-bold text-red-500 mb-4">Error Loading Country</h1>
            <p className="text-foreground/80 mb-2">There was a problem loading the country information.</p>
            <p className="text-sm text-red-300 mb-6">Error: {error.message}</p>
            <Button
              variant="default"
              className="bg-primary hover:bg-primary/90"
              asChild
            >
              <Link href="/">Return Home</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }
}

// Removed unused SpecialtyCardSkeleton function
