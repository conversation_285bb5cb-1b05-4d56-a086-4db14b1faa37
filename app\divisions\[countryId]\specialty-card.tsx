"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { SpecialtyIcon } from "@/components/specialty-icon" // Keep if needed elsewhere, remove if not
import { ArrowUpRight, Users, Activity, Star, Globe2 } from "lucide-react"
import { getSpecialtyIconPath } from "@/lib/getSpecialtyIconPath"
// Remove unused imports for client-side fetching
// Removed Avatar imports as we are replacing it
// import { getSpecialtyDoctorsCount, getSpecialtyRankingScore } from "@/lib/medleague/statistics"

// Specialty props - Add doctorCount and rankingScore
interface SpecialtyCardProps {
  specialty: {
    specialty_id: string,
    specialty_name: string
    description?: string
  },
  countryId: string,
  countryName?: string,
  countryFlagUrl?: string,
  doctorCount: number | null, // Added prop
  rankingScore: number | null // Added prop
}

export function SpecialtyCard({
  specialty,
  countryId,
  countryName,
  countryFlagUrl,
  doctorCount, // Destructure new prop
  rankingScore // Destructure new prop
}: SpecialtyCardProps) {
  // Remove client-side state and useEffect hooks
  // const [doctorCount, setDoctorCount] = useState<number | null>(null)
  // const [rankingScore, setRankingScore] = useState<number | null>(null)
  // const [loading, setLoading] = useState(true)
  // useEffect(() => { ... }, []); // Remove logging useEffect
  // useEffect(() => { ... }, [countryId, specialty.specialty_id]); // Remove data fetching useEffect

  const specialtyIconSrc = getSpecialtyIconPath(specialty.specialty_name);
  const specialtyPath = `/divisions/${countryId}/${specialty.specialty_id}`;

  // Determine activity level based on doctor count
  const isActive = doctorCount && doctorCount > 0;
  const activityLevel = isActive ? 100 : 5; // 100% if active, 5% if available to join

  return (
    <Card className={`overflow-hidden border transition-all duration-300 hover:shadow-md group ${
      isActive 
        ? 'border-primary/20 bg-gradient-to-b from-background/60 to-background/90 hover:border-primary/40 hover:shadow-primary/10' 
        : 'border-yellow-500/20 bg-gradient-to-b from-background/60 to-background/90 hover:border-yellow-500/40 hover:shadow-yellow-500/10'
    }`} style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
      <CardHeader className="p-4 pb-0 relative">
        <div className="absolute top-3 right-3">
          {doctorCount && doctorCount > 0 ? (
            <Badge variant="outline" className="bg-primary/10 text-foreground">
              Medical
            </Badge>
          ) : (
            <Badge variant="outline" className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">
              Available
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-3">
          {/* Replace Avatar with div and next/image */}
          <div className={`flex items-center justify-center h-12 w-12 rounded-md border ${doctorCount && doctorCount > 0 ? 'border-primary/30' : 'border-yellow-500/30'} bg-white dark:bg-white p-1 overflow-hidden shadow-sm`}>
            <div className="bg-white rounded">
              <Image
                src={specialtyIconSrc}
                alt={`${specialty.specialty_name} icon`}
                width={40} // Adjust size as needed
                height={40} // Adjust size as needed
                className="object-contain" // Ensure icon fits well
              />
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">{specialty.specialty_name}</h3>
            {specialty.description && (
              <p className="text-sm text-foreground/70 line-clamp-1">
                {specialty.description}
              </p>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-4">
        <div className="grid grid-cols-2 gap-2 mb-2">
          <div className={`rounded-md border p-2 flex flex-col items-center ${doctorCount && doctorCount > 0 ? 'bg-primary/5 border-primary/10' : 'bg-yellow-500/5 border-yellow-500/10'}`}>
            <Users className={`h-5 w-5 mb-1 ${doctorCount && doctorCount > 0 ? 'text-primary' : 'text-yellow-500'}`} />
            <span className="text-sm text-foreground/70">Doctors</span>
            <span className="text-lg font-bold text-foreground">
              {doctorCount ?? 0}
            </span>
          </div>

          <div className={`rounded-md border p-2 flex flex-col items-center ${doctorCount && doctorCount > 0 ? 'bg-primary/5 border-primary/10' : 'bg-yellow-500/5 border-yellow-500/10'}`}>
            <Star className={`h-5 w-5 mb-1 ${doctorCount && doctorCount > 0 ? 'text-primary' : 'text-yellow-500'}`} />
            <span className="text-sm text-foreground/70">Ranking</span>
            <span className="text-lg font-bold text-foreground">
              {(rankingScore && rankingScore > 0) ? rankingScore : 'N/A'}
            </span>
          </div>
        </div>

        {/* Activity Level */}
        <div className="mt-4">
          <div className="flex justify-between text-xs mb-1">
            <span className="text-foreground/70">Activity Level</span>
            <span className={`${isActive ? 'text-foreground' : 'text-yellow-400'}`}>
              {isActive ? 'Active' : 'Open to Join'}
            </span>
          </div>
          <div className="h-2 w-full bg-background/50 rounded-full overflow-hidden">
            <div
              className={`h-full rounded-full transition-all duration-500 ${isActive 
                ? 'bg-gradient-to-r from-green-500 to-primary' 
                : 'bg-gradient-to-r from-yellow-600 to-yellow-400'}`}
              style={{ width: `${activityLevel}%` }}
            />
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-1">
        <Button
          className={`w-full transition-colors ${doctorCount && doctorCount > 0 
            ? 'group-hover:bg-primary/90' 
            : 'bg-yellow-600 hover:bg-yellow-700 group-hover:bg-yellow-700'}`}
          asChild
        >
          <Link href={specialtyPath}>
            <span className="flex items-center justify-center">
              {doctorCount && doctorCount > 0 ? 'View Division' : 'Join Division'}
              <ArrowUpRight className="ml-2 h-4 w-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform" />
            </span>
          </Link>
        </Button>
      </CardFooter>
    </Card>
  )
}
