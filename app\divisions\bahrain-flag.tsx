"use client"

import React from 'react'

interface BahrainFlagProps {
  className?: string
  width?: number
  height?: number
}

export function BahrainFlag({ className = "", width = 32, height = 24 }: BahrainFlagProps) {
  return (
    <div 
      className={`relative overflow-hidden ${className}`}
      style={{ width: `${width}px`, height: `${height}px` }}
    >
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        viewBox="0 0 1500 900"
        width="100%"
        height="100%"
      >
        <rect width="1500" height="900" fill="#ce1126"/>
        <path d="M0,0 h500 l150,75 l-150,75 l150,75 l-150,75 l150,75 l-150,75 l150,75 l-150,75 l150,75 l-150,75 l150,75 l-150,75 h-500 z" fill="white"/>
      </svg>
    </div>
  )
}
