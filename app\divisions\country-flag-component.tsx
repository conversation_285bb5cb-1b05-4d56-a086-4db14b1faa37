"use client"

import React from 'react'
import ReactCountryFlag from 'react-country-flag'

interface CountryFlagComponentProps {
  countryName: string
  className?: string
  width?: string
  height?: string
  style?: React.CSSProperties
}

// Map of country names to ISO 3166-1 alpha-2 codes
const countryCodeMap: Record<string, string> = {
  'Bahrain': 'BH',
  'Saudi Arabia': 'SA',
  'United Arab Emirates': 'AE',
  'UAE': 'AE',
  'Kuwait': 'KW',
  'Oman': 'OM',
  'Qatar': 'QA',
  'United States': 'US',
  'USA': 'US',
  'United Kingdom': 'GB',
  'UK': 'GB'
}

export function CountryFlagComponent({ 
  countryName, 
  className = "", 
  width = "100%", 
  height = "100%",
  style = {}
}: CountryFlagComponentProps) {
  // Get the country code from the map, or use the first two letters of the country name
  const countryCode = countryCodeMap[countryName] || countryName.substring(0, 2).toUpperCase()
  
  return (
    <div className={`relative overflow-hidden ${className}`} style={{ width, height, ...style }}>
      <ReactCountryFlag
        countryCode={countryCode}
        svg
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover'
        }}
      />
    </div>
  )
}
