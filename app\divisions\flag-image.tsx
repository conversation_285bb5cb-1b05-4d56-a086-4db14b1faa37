"use client"

import { useState } from 'react'
import Image from 'next/image'

interface FlagImageProps {
  countryName: string
  flagUrl?: string
  width?: number
  height?: number
  className?: string
  fill?: boolean
}

export function FlagImage({ countryName, flagUrl, width, height, className, fill = false }: FlagImageProps) {
  const [error, setError] = useState(false)

  // For Bahrain, we know we have a local SVG file
  let imageUrl = ''

  if (countryName === 'Bahrain') {
    // Use the local SVG file for Bahrain
    imageUrl = '/flags/bahrain.svg'
  } else if (countryName === 'Saudi Arabia') {
    // Use the local SVG file for Saudi Arabia
    imageUrl = '/flags/saudi.svg'
  } else {
    // For other countries, use the country code to generate a URL
    const countryCodeMap: Record<string, string> = {
      'United Arab Emirates': 'ae',
      'UAE': 'ae',
      'Kuwait': 'kw',
      'Oman': 'om',
      'Qatar': 'qa',
      'United States': 'us',
      'USA': 'us',
      'United Kingdom': 'gb',
      'UK': 'gb'
    }

    // Use the mapped code if available, otherwise use first two letters
    const code = countryCodeMap[countryName] || countryName.substring(0, 2).toLowerCase()
    imageUrl = `https://flagcdn.com/w320/${code}.png`
  }

  // Use the provided flagUrl if it exists and we haven't encountered an error
  if (flagUrl && !error) {
    imageUrl = flagUrl
  }

  if (fill) {
    return (
      <Image
        src={imageUrl}
        alt={countryName}
        fill
        className={className || "object-cover"}
        onError={() => setError(true)}
      />
    )
  }

  return (
    <Image
      src={imageUrl}
      alt={countryName}
      width={width || 32}
      height={height || 24}
      className={className || "object-cover"}
      onError={() => setError(true)}
    />
  )
}
