"use client"

import { useEffect, useState } from 'react'
import Image from 'next/image'
import { Stethoscope } from 'lucide-react'

export function DivisionTitleWithFlag({ countryName }: { countryName: string }) {
  const [flagUrl, setFlagUrl] = useState<string | null>(null)
  const [flagError, setFlagError] = useState(false)
  
  useEffect(() => {
    // First try the local flag
    const localFlagUrl = `/flags/${countryName.toLowerCase() === 'uae' ? 'ae' : countryName.toLowerCase()}.svg`
    
    // Check if the local flag exists
    fetch(localFlagUrl)
      .then(response => {
        if (response.ok) {
          setFlagUrl(localFlagUrl)
        } else {
          // If local flag doesn't exist, use flagcdn
          const code = countryName === 'UAE' ? 'ae' : 
                      countryName === 'USA' ? 'us' :
                      countryName.substring(0, 2).toLowerCase()
          setFlagUrl(`https://flagcdn.com/w320/${code}.png`)
        }
      })
      .catch(() => {
        // If fetch fails, use flagcdn
        const code = countryName === 'UAE' ? 'ae' : 
                    countryName === 'USA' ? 'us' :
                    countryName.substring(0, 2).toLowerCase()
        setFlagUrl(`https://flagcdn.com/w320/${code}.png`)
      })
  }, [countryName])

  return (
    <h2 className="text-xl font-bold text-foreground flex items-center gap-2">
      {flagUrl && !flagError ? (
        <div className="w-8 h-6 relative overflow-hidden rounded-sm border border-white/20 shadow-sm">
          <Image
            src={flagUrl}
            alt={countryName}
            width={32}
            height={24}
            className="object-cover"
            onError={() => setFlagError(true)}
          />
        </div>
      ) : (
        <Stethoscope className="h-5 w-5 text-primary" />
      )}
      {countryName} Medical Specialties
    </h2>
  )
}
