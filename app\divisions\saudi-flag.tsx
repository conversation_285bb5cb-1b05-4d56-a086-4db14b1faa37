"use client"

import React from 'react'

interface SaudiFlagProps {
  className?: string
  width?: number
  height?: number
}

export function SaudiFlag({ className = "", width = 32, height = 24 }: SaudiFlagProps) {
  return (
    <div 
      className={`relative overflow-hidden ${className}`}
      style={{ width: `${width}px`, height: `${height}px` }}
    >
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        viewBox="0 0 1500 900"
        width="100%"
        height="100%"
      >
        {/* Green background */}
        <rect width="1500" height="900" fill="#006c35"/>
        
        {/* Shahada text */}
        <g transform="translate(750, 450) scale(4.5)">
          <path d="M-40,-15 C-40,-15 -36,-4 -18,-4 C-10,-4 -7,-7 0,-7 C7,-7 10,-4 18,-4 C36,-4 40,-15 40,-15 L40,0 C40,0 36,11 18,11 C10,11 7,8 0,8 C-7,8 -10,11 -18,11 C-36,11 -40,0 -40,0 Z" fill="white"/>
        </g>
        
        {/* Sword */}
        <g transform="translate(750, 550) scale(4)">
          <path d="M-40,0 L40,0 M-30,-5 L30,-5 M-35,5 L35,5" stroke="white" strokeWidth="2" fill="none"/>
          <path d="M-40,0 L-50,10 L-45,0 L-50,-10 Z" fill="white"/>
        </g>
      </svg>
    </div>
  )
}
