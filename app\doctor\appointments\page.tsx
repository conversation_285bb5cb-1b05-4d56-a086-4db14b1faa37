"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/context/AuthContext"
import { createClient } from "@supabase/supabase-js"
import { useRouter } from "next/navigation"
import { 
  ArrowLeft, 
  Calendar, 
  Clock, 
  User, 
  Phone, 
  Mail, 
  CheckCircle,
  XCircle,
  AlertCircle,
  Calendar as CalendarIcon,
  Stethoscope,
  Loader2
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { format } from "date-fns"
import { toast } from "sonner"

// This will be used if no appointment table exists yet
type MockAppointment = {
  id: string;
  patientName: string;
  datetime: string;
  reason: string;
  status: "confirmed" | "pending" | "canceled";
  notes?: string;
  patientEmail?: string;
  patientPhone?: string;
}

export default function DoctorAppointmentsPage() {
  const { isAuthenticated, user: authUser, isLoading: authIsLoading } = useAuth()
  const [dataLoading, setDataLoading] = useState(true)
  const [doctorProfile, setDoctorProfile] = useState<any>(null)
  const [upcomingAppointments, setUpcomingAppointments] = useState<any[]>([])
  const [pastAppointments, setPastAppointments] = useState<any[]>([])
  const router = useRouter()

  const createServiceRoleClient = () => {
    const supabaseUrl = "https://uapbzzscckhtptliynyj.supabase.co"
    const supabaseServiceKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q"
    return createClient(supabaseUrl, supabaseServiceKey)
  }

  useEffect(() => {
    if (authIsLoading) return
    
    if (!isAuthenticated || !authUser) {
      router.push("/")
      return
    }
    
    if (authUser.userType !== 'doctor') {
      router.push("/")
      return
    }
    
    const fetchDoctorAppointments = async () => {
      setDataLoading(true)
      try {
        const serviceClient = createServiceRoleClient()
        
        let doctorData
        
        // Try to find doctor by all possible methods
        if (authUser.userId) {
          const { data, error } = await serviceClient
            .from('doctors')
            .select('*')
            .eq('auth_id', authUser.userId)
            .single()
            
          if (!error && data) {
            doctorData = data
          } else {
            const { data: doctorIdData, error: doctorIdError } = await serviceClient
              .from('doctors')
              .select('*')
              .eq('doctor_id', authUser.userId)
              .single()
              
            if (!doctorIdError && doctorIdData) {
              doctorData = doctorIdData
            }
          }
        }
        
        if (!doctorData && authUser.email) {
          const { data, error } = await serviceClient
            .from('doctors')
            .select('*')
            .eq('email', authUser.email)
            .single()
            
          if (!error && data) {
            doctorData = data
          }
        }
        
        if (doctorData) {
          setDoctorProfile(doctorData)
          
          // Try to fetch real appointments from database
          try {
            // First check if appointments table exists by attempting to query it
            const { error: tableCheckError } = await serviceClient
              .from('appointments')
              .select('count')
              .limit(1)
              .single()
            
            if (!tableCheckError) {
              // Table exists, fetch upcoming appointments
              const now = new Date().toISOString()
              const { data: upcomingData, error: upcomingError } = await serviceClient
                .from('appointments')
                .select(`
                  *,
                  patient:users(*)
                `)
                .eq('doctor_id', doctorData.doctor_id)
                .gte('datetime', now)
                .order('datetime', { ascending: true })
                
              if (!upcomingError && upcomingData) {
                setUpcomingAppointments(upcomingData)
              }
              
              // Fetch past appointments
              const { data: pastData, error: pastError } = await serviceClient
                .from('appointments')
                .select(`
                  *,
                  patient:users(*)
                `)
                .eq('doctor_id', doctorData.doctor_id)
                .lt('datetime', now)
                .order('datetime', { ascending: false })
                .limit(20)
                
              if (!pastError && pastData) {
                setPastAppointments(pastData)
              }
            } else {
              console.log("Appointments table doesn't exist or is inaccessible, using mock data")
              // Use mock data if table doesn't exist
              generateMockAppointments(doctorData.doctor_id)
            }
          } catch (err) {
            console.error("Error fetching appointments:", err)
            // Fallback to mock data
            generateMockAppointments(doctorData.doctor_id)
          }
        } else {
          // No doctor profile found, create basic mock data
          generateMockAppointments(0)
        }
      } catch (err) {
        console.error("Error fetching doctor data:", err)
        // Use basic mock data
        generateMockAppointments(0)
      } finally {
        setDataLoading(false)
      }
    }
    
    const generateMockAppointments = (doctorId: number) => {
      // Mock upcoming appointments
      const upcoming: MockAppointment[] = []
      const today = new Date()
      
      for (let i = 1; i <= 5; i++) {
        const futureDate = new Date(today)
        futureDate.setDate(today.getDate() + i)
        futureDate.setHours(9 + Math.floor(Math.random() * 8), Math.floor(Math.random() * 4) * 15, 0)
        
        upcoming.push({
          id: `appt-${doctorId}-${i}`,
          patientName: `Patient ${i}`,
          datetime: futureDate.toISOString(),
          reason: ["Check-up", "Follow-up", "Consultation", "Emergency", "Procedure"][Math.floor(Math.random() * 5)],
          status: Math.random() > 0.2 ? "confirmed" : "pending",
          patientEmail: `patient${i}@example.com`,
          patientPhone: `******-${100 + i}-${1000 + i}`
        })
      }
      
      // Mock past appointments
      const past: MockAppointment[] = []
      
      for (let i = 1; i <= 8; i++) {
        const pastDate = new Date(today)
        pastDate.setDate(today.getDate() - i)
        pastDate.setHours(9 + Math.floor(Math.random() * 8), Math.floor(Math.random() * 4) * 15, 0)
        
        past.push({
          id: `past-${doctorId}-${i}`,
          patientName: `Previous Patient ${i}`,
          datetime: pastDate.toISOString(),
          reason: ["Check-up", "Follow-up", "Consultation", "Emergency", "Procedure"][Math.floor(Math.random() * 5)],
          status: Math.random() > 0.1 ? "confirmed" : "canceled",
          patientEmail: `previous${i}@example.com`,
          patientPhone: `******-${200 + i}-${2000 + i}`,
          notes: Math.random() > 0.5 ? "Patient responded well to treatment" : undefined
        })
      }
      
      setUpcomingAppointments(upcoming)
      setPastAppointments(past)
    }
    
    fetchDoctorAppointments()
  }, [authIsLoading, isAuthenticated, authUser, router])
  
  const handleConfirmAppointment = (id: string) => {
    // In a real app, this would update the database
    toast.success("Appointment confirmed", {
      description: "You have confirmed this appointment"
    })
    
    // Update local state
    setUpcomingAppointments(prev => 
      prev.map(apt => 
        apt.id === id ? { ...apt, status: "confirmed" } : apt
      )
    )
  }
  
  const handleCancelAppointment = (id: string) => {
    // In a real app, this would update the database
    toast.info("Appointment canceled", {
      description: "The appointment has been canceled"
    })
    
    // Update local state
    setUpcomingAppointments(prev => 
      prev.map(apt => 
        apt.id === id ? { ...apt, status: "canceled" } : apt
      )
    )
  }
  
  if (authIsLoading || dataLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-background to-background">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
          <p className="mt-4 text-lg text-foreground/70">Loading appointments...</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-background pb-12">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary/20 to-primary/10 border-b border-primary/20">
        <div className="container mx-auto py-6 px-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button 
                variant="ghost" 
                className="p-2 text-foreground" 
                onClick={() => router.push("/doctor/dashboard")}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <h1 className="text-2xl font-bold text-foreground">
                Appointments
              </h1>
            </div>
          </div>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-8">
        <Tabs defaultValue="upcoming" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-8 bg-background/90/50">
            <TabsTrigger value="upcoming" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              Upcoming
            </TabsTrigger>
            <TabsTrigger value="past" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              Past
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="upcoming" className="space-y-4">
            {upcomingAppointments.length === 0 ? (
              <Card className="bg-gradient-to-b from-background/90 to-background border-border">
                <CardContent className="pt-6">
                  <div className="text-center py-8">
                    <Calendar className="h-12 w-12 mx-auto mb-4 text-primary/70" />
                    <h3 className="text-xl font-medium text-foreground">No Upcoming Appointments</h3>
                    <p className="text-foreground/60 mt-2">
                      You don't have any upcoming appointments scheduled.
                    </p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              upcomingAppointments.map((appointment, index) => (
                <Card key={appointment.id || index} className="bg-gradient-to-b from-background/90 to-background border-border">
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row justify-between gap-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                            <User className="h-6 w-6 text-primary" />
                          </div>
                          <div>
                            <h3 className="text-lg font-medium text-foreground">
                              {appointment.patientName || 
                                (appointment.patient ? 
                                  `${appointment.patient.first_name || ''} ${appointment.patient.last_name || ''}` : 
                                  'Unknown Patient')}
                            </h3>
                            <p className="text-foreground/60 text-sm">
                              {appointment.reason || "Appointment"}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex flex-wrap gap-4 mt-4">
                          <div className="flex items-center gap-2 text-foreground/80">
                            <CalendarIcon className="h-4 w-4 text-primary/70" />
                            <span>{format(new Date(appointment.datetime), 'MMM d, yyyy')}</span>
                          </div>
                          
                          <div className="flex items-center gap-2 text-foreground/80">
                            <Clock className="h-4 w-4 text-primary/70" />
                            <span>{format(new Date(appointment.datetime), 'h:mm a')}</span>
                          </div>
                          
                          <div className="flex items-center gap-2 text-foreground/80">
                            <Stethoscope className="h-4 w-4 text-primary/70" />
                            <span>{appointment.duration || "30 min"}</span>
                          </div>
                        </div>
                        
                        {appointment.patientEmail && (
                          <div className="flex items-center gap-2 text-foreground/80 mt-2">
                            <Mail className="h-4 w-4 text-primary/70" />
                            <span>{appointment.patientEmail}</span>
                          </div>
                        )}
                        
                        {appointment.patientPhone && (
                          <div className="flex items-center gap-2 text-foreground/80 mt-2">
                            <Phone className="h-4 w-4 text-primary/70" />
                            <span>{appointment.patientPhone}</span>
                          </div>
                        )}
                      </div>
                      
                      <div className="flex flex-col md:items-end gap-4">
                        <Badge 
                          className={`
                            ${appointment.status === 'confirmed' ? 'bg-green-500/20 text-green-400 hover:bg-green-500/30' : 
                              appointment.status === 'canceled' ? 'bg-red-500/20 text-red-400 hover:bg-red-500/30' : 
                              'bg-amber-500/20 text-amber-400 hover:bg-amber-500/30'}
                            border-0
                          `}
                        >
                          {appointment.status === 'confirmed' ? 'Confirmed' : 
                            appointment.status === 'canceled' ? 'Canceled' : 'Pending'}
                        </Badge>
                        
                        <div className="flex gap-2">
                          {appointment.status !== 'confirmed' && (
                            <Button 
                              onClick={() => handleConfirmAppointment(appointment.id)}
                              size="sm" 
                              className="bg-green-600 hover:bg-green-700 text-foreground"
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Confirm
                            </Button>
                          )}
                          
                          {appointment.status !== 'canceled' && (
                            <Button 
                              onClick={() => handleCancelAppointment(appointment.id)}
                              size="sm" 
                              className="bg-background/80 hover:bg-background/90 text-foreground"
                            >
                              <XCircle className="h-4 w-4 mr-1" />
                              Cancel
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </TabsContent>
          
          <TabsContent value="past" className="space-y-4">
            {pastAppointments.length === 0 ? (
              <Card className="bg-gradient-to-b from-background/90 to-background border-border">
                <CardContent className="pt-6">
                  <div className="text-center py-8">
                    <Calendar className="h-12 w-12 mx-auto mb-4 text-primary/70" />
                    <h3 className="text-xl font-medium text-foreground">No Past Appointments</h3>
                    <p className="text-foreground/60 mt-2">
                      You don't have any past appointments on record.
                    </p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              pastAppointments.map((appointment, index) => (
                <Card key={appointment.id || index} className="bg-gradient-to-b from-background/90 to-background border-border">
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row justify-between gap-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center opacity-70">
                            <User className="h-6 w-6 text-primary" />
                          </div>
                          <div>
                            <h3 className="text-lg font-medium text-foreground/90">
                              {appointment.patientName || 
                                (appointment.patient ? 
                                  `${appointment.patient.first_name || ''} ${appointment.patient.last_name || ''}` : 
                                  'Unknown Patient')}
                            </h3>
                            <p className="text-foreground/60 text-sm">
                              {appointment.reason || "Appointment"}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex flex-wrap gap-4 mt-4">
                          <div className="flex items-center gap-2 text-foreground/70">
                            <CalendarIcon className="h-4 w-4 text-primary/60" />
                            <span>{format(new Date(appointment.datetime), 'MMM d, yyyy')}</span>
                          </div>
                          
                          <div className="flex items-center gap-2 text-foreground/70">
                            <Clock className="h-4 w-4 text-primary/60" />
                            <span>{format(new Date(appointment.datetime), 'h:mm a')}</span>
                          </div>
                        </div>
                        
                        {appointment.notes && (
                          <div className="mt-3 p-3 rounded bg-background/80/30 text-foreground/80 text-sm">
                            <p>{appointment.notes}</p>
                          </div>
                        )}
                      </div>
                      
                      <div>
                        <Badge 
                          className={`
                            ${appointment.status === 'confirmed' ? 'bg-green-500/20 text-green-400' : 
                              appointment.status === 'canceled' ? 'bg-red-500/20 text-red-400' : 
                              'bg-amber-500/20 text-amber-400'}
                            border-0
                          `}
                        >
                          {appointment.status === 'confirmed' ? 'Completed' : 
                            appointment.status === 'canceled' ? 'Canceled' : 'Missed'}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
} 