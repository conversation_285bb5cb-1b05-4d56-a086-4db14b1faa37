"use client"

import { useEffect } from "react"
import { useAuth } from "@/context/AuthContext"
import { useRouter } from "next/navigation"
import { Loader2 } from "lucide-react"

export default function DoctorDashboardPlaceholder() {
  const { isAuthenticated, user: authUser, isLoading: authIsLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // This effect runs when auth state changes
    if (!authIsLoading) {
      if (isAuthenticated && authUser && authUser.userType === 'doctor') {
        console.log("Redirecting authenticated doctor to dashboard")
        router.push('/doctor/dashboard')
      } else if (!isAuthenticated || !authUser) {
        console.log("Not authenticated, redirecting to login")
        router.push('/doctor/login')
      } else {
        console.log("Not a doctor account, redirecting to home")
        router.push('/')
      }
    }
  }, [authIsLoading, isAuthenticated, authUser, router])

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-green-900 to-green-800">
      <div className="text-center space-y-4">
        <Loader2 className="h-12 w-12 animate-spin text-foreground mx-auto" />
        <p className="text-lg text-foreground/70">Loading doctor dashboard...</p>
      </div>
    </div>
  )
} 