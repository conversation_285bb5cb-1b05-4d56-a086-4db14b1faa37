"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/context/AuthContext"
import { createClient } from "@supabase/supabase-js"
import {
  Star,
  Trophy,
  User,
  Calendar,
  Search,
  MessageCircle,
  Activity,
  Award,
  Bell,
  Stethoscope,
  ClipboardList,
  LogOut,
  BookOpen,
  Home,
  Edit,
  UserCog,
  BarChart3,
  UsersRound,
  FileStack,
  Medal,
  MapPin,
  Building,
  GraduationCap,
  HeartPulse,
  UserCheck,
  PieChart,
  ChevronRight,
  Clock,
  ClipboardEdit,
  ThumbsUp,
  UserPlus,
  ArrowUpRight,
  Sparkles,
  Flame,
  Loader2
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { toast } from "sonner"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { format } from "date-fns"
import { getSupabaseProfileImageUrl } from "@/app/lib/utils"

export default function DoctorDashboard() {
  const { isAuthenticated, user: authUser, isLoading: authIsLoading, logout } = useAuth()
  const [dataLoading, setDataLoading] = useState(true)
  const [doctorProfile, setDoctorProfile] = useState<any>(null)
  const [patientStats, setPatientStats] = useState<any>({
    total: 0,
    newThisMonth: 0,
    returningRate: 0
  })
  const [recentReviews, setRecentReviews] = useState<any[]>([])
  const [upcomingAppointments, setUpcomingAppointments] = useState<any[]>([])
  const [performanceStats, setPerformanceStats] = useState<any>({
    wins: 0,
    losses: 0,
    draws: 0,
    rating: 0,
    reviewCount: 0,
    rank: null
  })
  const [randomTip, setRandomTip] = useState("")
  const [leagueStatus, setLeagueStatus] = useState<any>(null)
  const router = useRouter()

  const createServiceRoleClient = () => {
    const supabaseUrl = "https://uapbzzscckhtptliynyj.supabase.co"
    const supabaseServiceKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q"
    return createClient(supabaseUrl, supabaseServiceKey)
  }
  
  const medicalTips = [
    "Maintain a healthy work-life balance to prevent burnout in your medical practice.",
    "Stay updated with the latest medical research and evidence-based guidelines in your specialty.",
    "Build strong relationships with your patients by practicing empathetic listening and clear communication.",
    "Collaborate with other healthcare professionals to provide comprehensive care for complex cases.",
    "Implement a consistent follow-up system to monitor patient progress and adherence to treatment plans.",
    "Invest time in continuing medical education to expand your knowledge and skills.",
    "Practice self-care and stress management techniques to maintain your own wellbeing.",
    "Use technology effectively to improve patient care coordination and medical record keeping.",
    "Consider participating in medical research or clinical trials in your specialty.",
    "Document patient encounters thoroughly to ensure continuity of care and reduce medical errors.",
    "Engage with professional medical associations to network and access valuable resources.",
    "Develop cultural competence to better serve patients from diverse backgrounds.",
    "Implement preventive care strategies in your practice to help patients stay healthy.",
    "Consider telemedicine options to improve access to care for patients with mobility limitations.",
    "Stay informed about healthcare policy changes that may affect your practice.",
    "Regularly review and update your clinical protocols to align with current best practices.",
    "Build a support network of colleagues for consultation on challenging cases.",
    "Maintain clear boundaries in doctor-patient relationships to ensure professional care.",
    "Develop skills in shared decision-making to involve patients in their healthcare choices.",
    "Regularly audit your clinical outcomes to identify areas for improvement in patient care."
  ];

  useEffect(() => {
    console.log("Doctor Dashboard component mounted");
    
    if (authIsLoading) {
      console.log("DoctorDashboard: Waiting for auth state to load...");
      return;
    }
    
    if (!isAuthenticated || !authUser) {
      console.log("No active session, redirecting to home");
      router.push("/");
      return;
    }
    
    if (authUser.userType !== 'doctor') {
      console.log("Not a doctor account, redirecting to home");
      router.push("/");
      return;
    }
    
    console.log("DoctorDashboard: Authenticated as doctor:", authUser);
    
    const fetchDoctorData = async () => {
      setDataLoading(true);
      try {
        const serviceClient = createServiceRoleClient();
        
        // Store last login date
        localStorage.setItem('lastLoginDate', new Date().toDateString());
        
        console.log("Fetching doctor profile with user ID:", authUser.userId);
        
        // Approach 1: Try fetching basic doctor data first without relationships
        let doctorData;
        let hospitalData;
        let countryData;
        
        if (authUser.userId) {
          console.log("Trying to fetch doctor by user ID/auth_id:", authUser.userId);
          
          try {
            // First try by auth_id with no relationships
            const { data: authIdData, error: authIdError } = await serviceClient
              .from('doctors')
              .select('*')
              .eq('auth_id', authUser.userId)
              .single();
              
            if (!authIdError && authIdData) {
              console.log("Found doctor by auth_id");
              doctorData = authIdData;
            } else {
              console.log("Could not find doctor by auth_id, trying doctor_id", authIdError?.message);
              
              try {
                // Then try by doctor_id with no relationships
                const { data: doctorIdData, error: doctorIdError } = await serviceClient
                  .from('doctors')
                  .select('*')
                  .eq('doctor_id', authUser.userId)
                  .single();
                  
                if (!doctorIdError && doctorIdData) {
                  console.log("Found doctor by doctor_id");
                  doctorData = doctorIdData;
                } else {
                  console.log("Could not find doctor by doctor_id either", doctorIdError?.message);
                }
              } catch (doctorIdErr) {
                console.error("Exception when fetching by doctor_id:", doctorIdErr);
              }
            }
          } catch (authIdErr) {
            console.error("Exception when fetching by auth_id:", authIdErr);
          }
        }
        
        // Approach 2: Try using email if direct ID lookup fails
        if (!doctorData && authUser.email) {
          console.log("Trying to fetch doctor by email:", authUser.email);
          
          try {
            const { data: emailData, error: emailError } = await serviceClient
              .from('doctors')
              .select('*')
              .eq('email', authUser.email)
              .single();
              
            if (!emailError && emailData) {
              console.log("Found doctor by email");
              doctorData = emailData;
            } else {
              console.log("Could not find doctor by email either", emailError?.message);
            }
          } catch (emailErr) {
            console.error("Exception when fetching by email:", emailErr);
          }
        }
        
        // Approach 3: Last resort - hardcoded test account for demo
        if (!doctorData) {
          console.log("Using hardcoded test doctor as fallback");
          
          try {
            const { data: testDoctorData, error: testDoctorError } = await serviceClient
              .from('doctors')
              .select('*')
              .eq('doctor_id', 4097)  // Test doctor ID from your scripts
              .single();
              
            if (!testDoctorError && testDoctorData) {
              console.log("Found test doctor as fallback");
              doctorData = testDoctorData;
            } else {
              console.error("Even test doctor lookup failed:", testDoctorError?.message);
            }
          } catch (testDoctorErr) {
            console.error("Exception when fetching test doctor:", testDoctorErr);
          }
        }
        
        // Fetch hospital data if we have hospital_id
        if (doctorData && doctorData.hospital_id) {
          try {
            console.log("Fetching hospital data for hospital_id:", doctorData.hospital_id);
            const { data: hospital, error: hospitalError } = await serviceClient
              .from('hospitals')
              .select('*')
              .eq('hospital_id', doctorData.hospital_id)
              .single();
              
            if (!hospitalError && hospital) {
              hospitalData = hospital;
              console.log("Found hospital data:", hospital);
            } else {
              console.log("Could not find hospital data:", hospitalError?.message);
            }
          } catch (hospitalErr) {
            console.error("Exception when fetching hospital:", hospitalErr);
          }
        }
        
        // Fetch country data if we have country_id
        if (doctorData && doctorData.country_id) {
          try {
            console.log("Fetching country data for country_id:", doctorData.country_id);
            const { data: country, error: countryError } = await serviceClient
              .from('countries')
              .select('country_id, country_name')
              .eq('country_id', doctorData.country_id)
              .single();
              
            if (!countryError && country) {
              countryData = country;
              console.log("Found country data:", country);
            } else {
              console.log("Could not find country data:", countryError?.message);
              // Attempt a fallback query with all countries to find a match
              const { data: allCountries, error: allCountriesError } = await serviceClient
                .from('countries')
                .select('country_id, country_name');
                
              if (!allCountriesError && allCountries && allCountries.length > 0) {
                console.log("Fetched all countries as fallback, found:", allCountries.length);
                // Just use the first country as a fallback if needed
                countryData = {
                  country_id: doctorData.country_id,
                  country_name: `Country ${doctorData.country_id}`
                };
              }
            }
          } catch (countryErr) {
            console.error("Exception when fetching country:", countryErr);
          }
        }
        
        // Combine the data into a single doctor object
        if (doctorData) {
          // Add the related data manually
          if (hospitalData) {
            doctorData.hospitals = hospitalData;
          }
          
          if (countryData) {
            doctorData.countries = countryData;
          } else if (doctorData.country_id) {
            // Create a basic country object if we have country_id but no data
            doctorData.countries = {
              country_id: doctorData.country_id,
              country_name: `Country ${doctorData.country_id}`
            };
          }
          
          setDoctorProfile(doctorData);
          
          // Set performance stats from real data
          setPerformanceStats({
            wins: doctorData.wins || 0,
            losses: doctorData.losses || 0,
            draws: doctorData.draws || 0,
            rating: doctorData.rating || 0,
            reviewCount: doctorData.review_count || 0,
            rank: null // Will be calculated later
          });
          
          try {
            // Fetch real recent reviews from the database
            const { data: reviewsData, error: reviewsError } = await serviceClient
              .from('reviews')
              .select(`
                *,
                patients:users(*)
              `)
              .eq('doctor_id', doctorData.doctor_id)
              .order('review_date', { ascending: false })
              .limit(5);
              
            if (!reviewsError) {
              setRecentReviews(reviewsData || []);
              console.log("Retrieved reviews:", reviewsData);
            } else {
              console.error("Error fetching reviews:", reviewsError);
              setRecentReviews([]);
            }
            
            // Fetch real patient statistics from reviews and stats
            try {
              // Get total patients count from reviews (unique users)
              const { count: totalPatientsCount, error: patientCountError } = await serviceClient
                .from('reviews')
                .select('user_id', { count: 'exact' })
                .eq('doctor_id', doctorData.doctor_id);
                
              const totalPatients = totalPatientsCount || 0;
                
              // Get new patients this month
              const firstDayOfMonth = new Date();
              firstDayOfMonth.setDate(1);
              firstDayOfMonth.setHours(0, 0, 0, 0);
              
              const { count: newPatientsCount, error: newPatientsError } = await serviceClient
                .from('reviews')
                .select('user_id', { count: 'exact' })
                .eq('doctor_id', doctorData.doctor_id)
                .gte('review_date', firstDayOfMonth.toISOString());
                
              const newPatients = newPatientsCount || 0;
                
              // Calculate returning rate (if we have enough data)
              let returningRate = 0;
              if (totalPatients > 0) {
                const { data: returningData, error: returningError } = await serviceClient
                  .from('reviews')
                  .select('user_id, count')
                  .eq('doctor_id', doctorData.doctor_id)
                  .order('count', { ascending: false });
                  
                // Filter out users with multiple reviews
                if (!returningError && returningData) {
                  const usersWithMultipleReviews = returningData.filter(item => item.count > 1).length;
                  returningRate = Math.round((usersWithMultipleReviews / totalPatients) * 100);
                }
              }
              
              setPatientStats({
                total: totalPatients,
                newThisMonth: newPatients,
                returningRate: returningRate
              });
              
            } catch (statsErr) {
              console.error("Error calculating patient stats:", statsErr);
              setPatientStats({
                total: 0,
                newThisMonth: 0,
                returningRate: 0
              });
            }
            
            // Fetch upcoming appointments if available
            // Note: This assumes you have an appointments table
            try {
              const { data: appointmentsData, error: appointmentsError } = await serviceClient
                .from('appointments')
                .select(`
                  *,
                  patients:users(*)
                `)
                .eq('doctor_id', doctorData.doctor_id)
                .gte('datetime', new Date().toISOString())
                .order('datetime', { ascending: true })
                .limit(5);
                
              if (!appointmentsError && appointmentsData) {
                console.log("Retrieved appointments:", appointmentsData);
                setUpcomingAppointments(appointmentsData);
              } else {
                console.log("No appointments found or table doesn't exist:", appointmentsError?.message);
                
                // Fallback to empty appointments array
                setUpcomingAppointments([]);
              }
            } catch (appErr) {
              console.error("Error fetching appointments:", appErr);
              setUpcomingAppointments([]);
            }
            
            // Get league status from doctor stats
            const { data: leagueData, error: leagueError } = await serviceClient
              .from('doctors')
              .select('rating, wins, losses, draws, review_count')
              .order('rating', { ascending: false })
              .limit(20);
              
            if (!leagueError && leagueData) {
              // Find position of current doctor in the league
              const position = leagueData.findIndex(d => d.rating === doctorData.rating) + 1;
              
              // Calculate total points (wins*3 + draws*1)
              const points = (doctorData.wins || 0) * 3 + (doctorData.draws || 0);
              
              // Determine division based on rating
              let division = "Bronze";
              if (doctorData.rating >= 4.5) division = "Platinum";
              else if (doctorData.rating >= 4.0) division = "Gold";
              else if (doctorData.rating >= 3.5) division = "Silver";
              
              setLeagueStatus({
                division: division,
                position: position > 0 ? position : leagueData.length + 1,
                points: points,
                trend: "up" // This would need additional historical data to calculate accurately
              });
            } else {
              console.log("Error fetching league data:", leagueError?.message);
              setLeagueStatus({
                division: "Bronze",
                position: 0,
                points: 0,
                trend: "neutral"
              });
            }
            
            // Set a random tip
            const randomIndex = Math.floor(Math.random() * medicalTips.length);
            setRandomTip(medicalTips[randomIndex]);
            
          } catch (err) {
            console.error("Error processing doctor data:", err);
          }
        } else {
          console.error("No doctor profile found for this user");
          
          // Create a fallback doctor profile with the information we have
          const fallbackDoctor = {
            doctor_id: authUser.userId || "unknown",
            fullname: authUser.email ? `Dr. ${authUser.email.split('@')[0]}` : "Doctor",
            email: authUser.email || "<EMAIL>",
            specialty: "General Medicine",
            medical_title: "Medical Doctor",
            experience: 0,
            rating: 4.0,
            review_count: 0,
            wins: 0,
            losses: 0,
            draws: 0,
            facility: "Not specified",
            countries: {
              country_id: 0,
              country_name: "Not specified"
            },
            hospitals: {
              hospital_id: 0,
              hospital_name: "Not specified",
              city: "Not specified",
              address: "Not specified"
            },
            auth_id: authUser.userId
          };
          
          setDoctorProfile(fallbackDoctor);
          setPerformanceStats({
            wins: 0,
            losses: 0,
            draws: 0,
            rating: 4.0,
            reviewCount: 0,
            rank: null
          });
          
          setPatientStats({
            total: 0,
            newThisMonth: 0,
            returningRate: 0
          });
          
          setUpcomingAppointments([]);
          setRecentReviews([]);
          
          setLeagueStatus({
            division: "Bronze",
            position: 0,
            points: 0,
            trend: "neutral"
          });
          
          const randomIndex = Math.floor(Math.random() * medicalTips.length);
          setRandomTip(medicalTips[randomIndex]);
          
          toast.warning("Using temporary profile. Please complete your profile details.", {
            duration: 5000,
            action: {
              label: "Edit Profile",
              onClick: () => router.push("/doctor/profile/edit")
            }
          });
        }
      } catch (err) {
        console.error("Error in fetchDoctorData:", err);
        toast.error("Could not load dashboard data");
      } finally {
        setDataLoading(false);
      }
    };
    
    fetchDoctorData();
    
    return () => {
      console.log("Doctor Dashboard component unmounted");
    };
  }, [authIsLoading, isAuthenticated, authUser, router]);

  const handleSignOut = async () => {
    try {
      console.log("Signing out via AuthContext logout");
      logout();
    } catch (error) {
      console.error("Error during sign out:", error);
      toast.error("Sign out failed. Please try again.");
    }
  };
  
  if (authIsLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-background to-background">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
          <p className="mt-4 text-lg text-foreground/70">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !authUser || authUser.userType !== 'doctor') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-background to-background">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
          <p className="text-lg text-foreground/70">Redirecting to login...</p>
        </div>
      </div>
    );
  }
  
  if (dataLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-background p-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold text-foreground">Doctor Dashboard</h1>
          </div>
          
          <div className="grid gap-8 grid-cols-1 md:grid-cols-3">
            {/* Loading skeletons */}
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <Card key={i} className="bg-background/90/50 border-border">
                <CardHeader className="pb-2">
                  <div className="w-1/2 h-6 bg-background/80/50 rounded animate-pulse"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="w-full h-4 bg-background/80/50 rounded animate-pulse"></div>
                    <div className="w-3/4 h-4 bg-background/80/50 rounded animate-pulse"></div>
                    <div className="w-1/2 h-4 bg-background/80/50 rounded animate-pulse"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-background pb-12">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary/20 to-primary/10 border-b border-primary/20">
        <div className="container mx-auto py-8 px-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-foreground">
                Welcome, {doctorProfile?.fullname || authUser.email}
              </h1>
              <p className="text-foreground/70 mt-1">
                Your professional dashboard is ready
              </p>
            </div>
            <div className="flex gap-3">
              <Button 
                variant="outline" 
                className="bg-white/5 border-white/10 text-foreground hover:bg-white/10 hover:text-foreground"
                onClick={() => router.push("/doctor/profile/edit")}
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit Profile
              </Button>
              <Button 
                variant="outline" 
                className="bg-white/5 border-white/10 text-foreground hover:bg-white/10 hover:text-foreground"
                onClick={handleSignOut}
              >
                <LogOut className="mr-2 h-4 w-4" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Doctor Profile Summary */}
          <Card className="bg-gradient-to-b from-background/90 to-background border-border md:col-span-3">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-6 items-center md:items-start">
                <div className="relative">
                  <div className="w-28 h-28 rounded-xl overflow-hidden bg-primary/10 flex items-center justify-center">
                    {doctorProfile?.profile_image || doctorProfile?.image_path ? (
                      <img 
                        src={getSupabaseProfileImageUrl(doctorProfile.profile_image || doctorProfile.image_path)} 
                        alt={doctorProfile.fullname || "Doctor"} 
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // Fallback if image fails to load
                          e.currentTarget.src = '/placeholder-avatar.png';
                        }}
                      />
                    ) : (
                      <UserCog className="h-12 w-12 text-primary" />
                    )}
                  </div>
                  
                  <div className="absolute -bottom-3 -right-3 bg-primary text-foreground p-1.5 rounded-full">
                    <Trophy className="h-5 w-5" />
                  </div>
                </div>
                
                <div className="text-center md:text-left flex-1">
                  <h2 className="text-xl font-bold text-foreground">
                    {doctorProfile?.fullname || "Doctor"}
                  </h2>
                  <p className="text-foreground/70">{doctorProfile?.medical_title || "Medical Doctor"}</p>
                  
                  <div className="flex flex-wrap justify-center md:justify-start gap-2 mt-2">
                    <Badge 
                      className="bg-primary/20 text-primary hover:bg-primary/30 border-0"
                    >
                      {doctorProfile?.specialty || "Specialty"}
                    </Badge>
                    
                    {doctorProfile?.subspecialty && (
                      <Badge variant="outline" className="border-border text-muted-green">
                        {doctorProfile.subspecialty}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex flex-wrap justify-center md:justify-start gap-4 mt-4 text-sm text-foreground/70">
                    <div className="flex items-center gap-1">
                      <Building className="h-4 w-4 text-primary/70" />
                      <span>
                        {doctorProfile?.facility || 
                         (doctorProfile?.hospitals && typeof doctorProfile.hospitals === 'object' ? 
                          doctorProfile.hospitals.hospital_name : 
                          "Hospital")}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <GraduationCap className="h-4 w-4 text-primary/70" />
                      <span>{doctorProfile?.experience || "0"} years experience</span>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4 text-primary/70" />
                      <span className="font-medium">
                        {doctorProfile?.countries && typeof doctorProfile.countries === 'object' ? 
                          doctorProfile.countries.country_name : 
                          (doctorProfile?.country_name || "Country")}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-amber-400" />
                      <span className="font-medium text-foreground">
                        {doctorProfile?.rating?.toFixed(1) || "0.0"}
                      </span>
                      <span className="text-foreground/50">
                        ({doctorProfile?.review_count || 0} reviews)
                      </span>
                    </div>
                  </div>
                  
                  {/* Add Educational Background */}
                  {doctorProfile?.educational_background && (
                    <div className="mt-4 p-3 rounded-lg bg-primary/10 border border-primary/20">
                      <div className="flex items-start gap-2">
                        <GraduationCap className="h-5 w-5 text-primary/70 mt-0.5" />
                        <div>
                          <h4 className="text-sm font-medium text-foreground">Educational Background</h4>
                          <p className="text-sm text-foreground/70">{doctorProfile.educational_background}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="w-full md:w-auto flex flex-col gap-2">
                  <div className="flex flex-col items-center p-4 rounded-lg bg-background/90/50 border border-border">
                    <h3 className="font-medium text-foreground text-lg">Performance</h3>
                    <div className="mt-2 grid grid-cols-3 gap-4 text-center">
                      <div>
                        <p className="text-green-400 font-bold text-2xl">{performanceStats.wins}</p>
                        <p className="text-xs text-foreground/50">Wins</p>
                      </div>
                      <div>
                        <p className="text-red-400 font-bold text-2xl">{performanceStats.losses}</p>
                        <p className="text-xs text-foreground/50">Losses</p>
                      </div>
                      <div>
                        <p className="text-blue-400 font-bold text-2xl">{performanceStats.draws}</p>
                        <p className="text-xs text-foreground/50">Draws</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Main Dashboard Cards - First Row */}
          <Card className="bg-gradient-to-b from-background/90 to-background border-border">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg text-foreground">Patients Overview</CardTitle>
                <UsersRound className="h-5 w-5 text-primary" />
              </div>
              <CardDescription className="text-foreground/60">Patient statistics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm text-foreground/80">Total Patients</span>
                    <span className="text-sm font-medium text-foreground">{patientStats.total}</span>
                  </div>
                  <Progress value={patientStats.total > 200 ? 100 : (patientStats.total / 200) * 100} className="h-2 bg-background/80 [&>div]:bg-primary" />
                </div>
                
                <div className="grid grid-cols-2 gap-4 pt-2">
                  <div className="bg-primary/10 rounded-lg p-3">
                    <span className="text-xs text-foreground/60 block">New This Month</span>
                    <div className="flex items-end gap-1 mt-1">
                      <span className="text-xl font-bold text-foreground">{patientStats.newThisMonth}</span>
                      <UserPlus className="h-4 w-4 text-green-400 mb-1" />
                    </div>
                  </div>
                  
                  <div className="bg-primary/10 rounded-lg p-3">
                    <span className="text-xs text-foreground/60 block">Returning Rate</span>
                    <div className="flex items-end gap-1 mt-1">
                      <span className="text-xl font-bold text-foreground">{patientStats.returningRate}%</span>
                      <ThumbsUp className="h-4 w-4 text-blue-400 mb-1" />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-b from-background/90 to-background border-border">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg text-foreground">League Stats</CardTitle>
                <Trophy className="h-5 w-5 text-amber-400" />
              </div>
              <CardDescription className="text-foreground/60">Your current standings</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-gradient-to-r from-amber-900/20 to-amber-700/10 rounded-lg p-4 border border-amber-800/20">
                  <div className="flex items-center justify-between">
                    <span className="text-foreground/80">Division</span>
                    <Badge className="bg-amber-500/20 text-amber-300 hover:bg-amber-500/30 border-0">
                      {leagueStatus?.division || "Silver"}
                    </Badge>
                  </div>
                  
                  <div className="mt-3 grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-xs text-foreground/60 block">Position</span>
                      <div className="flex items-center gap-1 mt-1">
                        <span className="text-xl font-bold text-foreground">{leagueStatus?.position || "N/A"}</span>
                        <span className="text-xs text-foreground/60">of 10</span>
                      </div>
                    </div>
                    
                    <div>
                      <span className="text-xs text-foreground/60 block">Points</span>
                      <div className="flex items-center gap-1 mt-1">
                        <span className="text-xl font-bold text-foreground">{leagueStatus?.points || "0"}</span>
                        <span className="text-xs text-foreground/60">pts</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <Button 
                  variant="outline" 
                  className="w-full border-border text-foreground hover:bg-background/80"
                  onClick={() => router.push(`/divisions/${doctorProfile?.country_id || 1}`)}
                >
                  View League Table
                  <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-b from-background/90 to-background border-border">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg text-foreground">Quick Stats</CardTitle>
                <Activity className="h-5 w-5 text-primary" />
              </div>
              <CardDescription className="text-foreground/60">Performance metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-green-900/20 p-3 rounded-lg border border-green-700/20">
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-foreground/60">Win Rate</span>
                      <Badge variant="outline" className="text-green-400 border-green-800/50">
                        {performanceStats.wins + performanceStats.losses + performanceStats.draws > 0 
                          ? Math.round((performanceStats.wins / (performanceStats.wins + performanceStats.losses + performanceStats.draws)) * 100) 
                          : 0}%
                      </Badge>
                    </div>
                    <Progress 
                      value={performanceStats.wins + performanceStats.losses + performanceStats.draws > 0 
                        ? (performanceStats.wins / (performanceStats.wins + performanceStats.losses + performanceStats.draws)) * 100 
                        : 0} 
                      className="h-1.5 mt-2 bg-background/80 [&>div]:bg-green-500" 
                    />
                  </div>
                  
                  <div className="bg-blue-900/20 p-3 rounded-lg border border-blue-700/20">
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-foreground/60">Rating</span>
                      <Badge variant="outline" className="text-amber-400 border-amber-800/50">
                        {(performanceStats.rating || 0).toFixed(1)}
                      </Badge>
                    </div>
                    <Progress 
                      value={(performanceStats.rating || 0) * 20} 
                      className="h-1.5 mt-2 bg-background/80 [&>div]:bg-amber-500" 
                    />
                  </div>
                </div>
                
                <div className="border-t border-border pt-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-foreground/70">Last Updated</span>
                    <span className="text-foreground">
                      {doctorProfile?.last_updated 
                        ? format(new Date(doctorProfile.last_updated), 'MMM d, yyyy')
                        : 'Never'}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Second Row */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-8">
          {/* Appointments Card */}
          <Card className="bg-gradient-to-b from-background/90 to-background border-border md:col-span-2">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg text-foreground">Upcoming Appointments</CardTitle>
                <Calendar className="h-5 w-5 text-primary" />
              </div>
              <CardDescription className="text-foreground/60">Your schedule for the next days</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-6 text-center border border-dashed border-border rounded-lg bg-background/90/30">
                <Calendar className="h-10 w-10 text-muted-green mx-auto mb-2" />
                <p className="text-foreground font-medium mb-2">Appointment System Coming Soon</p>
                <p className="text-foreground/70 text-sm max-w-md mx-auto">We're currently developing our appointment management system. Soon you'll be able to view and manage all your patient appointments right here.</p>
              </div>
            </CardContent>
          </Card>
          
          {/* Reviews Card */}
          <Card className="bg-gradient-to-b from-background/90 to-background border-border">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg text-foreground">Recent Reviews</CardTitle>
                <MessageCircle className="h-5 w-5 text-primary" />
              </div>
              <CardDescription className="text-foreground/60">
                What patients are saying
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[300px] pr-4">
                {recentReviews.length > 0 ? (
                  <div className="space-y-4">
                    {recentReviews.map((review) => (
                      <div key={review.review_id} className="p-3 rounded-lg border border-border bg-background/90/50">
                        <div className="flex justify-between items-start">
                          <div className="flex gap-2 items-center">
                            <Avatar className="h-8 w-8">
                              <AvatarFallback className="bg-primary/20 text-primary">
                                {review.patients?.first_name?.[0] || 'P'}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="text-sm font-medium text-foreground">
                                {review.patients?.first_name || 'Anonymous'} {review.patients?.last_name || ''}
                              </p>
                              <p className="text-xs text-foreground/60">
                                {review.review_date ? format(new Date(review.review_date), 'MMM d, yyyy') : 'Unknown date'}
                              </p>
                            </div>
                          </div>
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <Star 
                                key={i} 
                                className={`h-4 w-4 ${i < review.rating ? 'fill-amber-400 text-amber-400' : 'text-muted-green'}`} 
                              />
                            ))}
                          </div>
                        </div>
                        
                        {review.additional_comments && (
                          <p className="mt-2 text-sm text-foreground/80">{review.additional_comments}</p>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="p-6 text-center border border-dashed border-border rounded-lg bg-background/90/30">
                    <MessageCircle className="h-10 w-10 text-muted-green mx-auto mb-2" />
                    <p className="text-foreground/70">No reviews yet</p>
                  </div>
                )}
              </ScrollArea>

              <div className="mt-4">
                <Button 
                  variant="outline" 
                  className="w-full border-border text-foreground hover:bg-background/80"
                  onClick={() => router.push("/doctor/reviews")}
                >
                  View All Reviews
                  <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Third Row */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-8">
          {/* Professional Tip Card */}
          <Card className="bg-gradient-to-b from-background/90 to-background border-border md:col-span-3">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row items-center gap-6 p-4 bg-gradient-to-r from-primary/20 to-primary/5 rounded-lg border border-primary/30">
                <div className="shrink-0">
                  <div className="h-16 w-16 rounded-full bg-primary/20 text-primary flex items-center justify-center">
                    <Sparkles className="h-8 w-8" />
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium text-foreground mb-2">Professional Tip</h3>
                  <p className="text-foreground/80 italic">{randomTip}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Bottom Actions */}
        <div className="mt-8 flex flex-col md:flex-row gap-4 justify-center">
          <Button 
            className="bg-primary/90 hover:bg-primary text-foreground gap-2"
            onClick={() => router.push("/doctor/profile/edit")}
          >
            <Edit className="h-4 w-4" />
            Edit Profile
          </Button>
          
          <Button 
            variant="outline" 
            className="text-foreground border-white/20 hover:bg-white/5 gap-2"
            onClick={() => router.push(`/divisions/${doctorProfile?.country_id || 1}`)}
          >
            <Trophy className="h-4 w-4 text-amber-400" />
            View Leagues
          </Button>

          <Button 
            variant="outline" 
            className="text-foreground border-white/20 hover:bg-white/5 gap-2"
            onClick={() => router.push(`/doctor/resources/${doctorProfile?.specialty_id || 1}`)}
          >
            <BookOpen className="h-4 w-4 text-primary" />
            Medical Resources
          </Button>
        </div>
        
        {/* Testing tools - only visible in development */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-8 border-t border-border pt-8">
            <h3 className="text-foreground text-center mb-4">Developer Testing Tools</h3>
            <div className="flex justify-center gap-4">
              <Button 
                variant="destructive"
                size="sm"
                onClick={async () => {
                  try {
                    const serviceClient = createServiceRoleClient();
                    
                    // First try to find/create a test doctor
                    const TEST_EMAIL = "<EMAIL>";
                    
                    // Check if doctor exists
                    const { data: existingDoctor } = await serviceClient
                      .from('doctors')
                      .select('*')
                      .eq('email', TEST_EMAIL)
                      .single();
                      
                    let doctorId = existingDoctor?.doctor_id;
                    
                    if (!existingDoctor) {
                      // Create a new test doctor
                      const { data: newDoctor, error } = await serviceClient
                        .from('doctors')
                        .insert([
                          {
                            fullname: "Dr. Test Account",
                            email: TEST_EMAIL,
                            specialty: "General Medicine",
                            medical_title: "Medical Doctor",
                            experience: 5,
                            facility: "Test Hospital",
                            rating: 4.5,
                            review_count: 10,
                            wins: 5,
                            losses: 2,
                            draws: 1
                          }
                        ])
                        .select()
                        .single();
                        
                      if (error) {
                        console.error("Error creating test doctor:", error);
                        toast.error("Could not create test doctor");
                        return;
                      }
                      
                      doctorId = newDoctor.doctor_id;
                    }
                    
                    // Force immediate reload of page
                    toast.success("Test doctor loaded, reloading page...");
                    setTimeout(() => {
                      window.location.reload();
                    }, 1500);
                  } catch (error) {
                    console.error("Error in direct doctor login:", error);
                    toast.error("Test login failed");
                  }
                }}
              >
                Direct Test Login
              </Button>
              
              <Button 
                variant="secondary"
                size="sm"
                onClick={() => {
                  // Clear any stored data and reload
                  localStorage.removeItem('lastLoginDate');
                  window.location.reload();
                }}
              >
                Refresh Dashboard
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 