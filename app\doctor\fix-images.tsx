"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { useAuth } from "@/lib/auth/auth-context"
import { uploadDoctorProfileImage } from "@/actions/doctor-registration-actions"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { getSupabaseProfileImageUrl } from "@/app/lib/utils"
import { Upload, Check, AlertCircle, User } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

/**
 * A utility component to fix profile images for doctors by allowing them
 * to upload new images that will be properly saved in the database
 */
export default function FixProfileImage() {
  const { isAuthenticated, user, isLoading } = useAuth()
  const { toast } = useToast()
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploading, setUploading] = useState(false)
  const [profileImageUrl, setProfileImageUrl] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [imagePreview, setImagePreview] = useState<string | null>(null)

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setError(null)
    setSuccess(false)
    
    const file = e.target.files?.[0]
    if (!file) return
    
    // Validate file size
    if (file.size > 5 * 1024 * 1024) {
      setError("Image size must be less than 5MB")
      return
    }
    
    // Validate file type
    if (!file.type.startsWith("image/")) {
      setError("Only image files are allowed")
      return
    }
    
    setSelectedFile(file)
    
    // Create preview
    const reader = new FileReader()
    reader.onload = (event) => {
      setImagePreview(event.target?.result as string || null)
    }
    reader.readAsDataURL(file)
  }
  
  // Handle image upload
  const handleUpload = async () => {
    if (!selectedFile || !user?.userId) {
      setError("Please select an image file")
      return
    }
    
    setUploading(true)
    setError(null)
    
    try {
      // Upload the image to Supabase Storage
      const result = await uploadDoctorProfileImage(user.userId, selectedFile)
      
      if (result.error) {
        console.error("Error uploading image:", result.error)
        setError(`Failed to upload image: ${result.error.message || "Unknown error"}`)
        return
      }
      
      if (!result.profileImageUrl) {
        setError("Upload succeeded but no image URL was returned")
        return
      }
      
      // Success! Update the UI
      setProfileImageUrl(result.profileImageUrl)
      setSuccess(true)
      
      toast({
        title: "Profile image updated!",
        description: "Your profile image has been successfully updated.",
        variant: "default",
      })
      
      // Reset the form
      setSelectedFile(null)
      
    } catch (err) {
      console.error("Error during image upload:", err)
      setError(`An unexpected error occurred: ${err instanceof Error ? err.message : "Unknown error"}`)
    } finally {
      setUploading(false)
    }
  }
  
  if (isLoading) {
    return <div className="p-8 text-center">Loading...</div>
  }
  
  if (!isAuthenticated) {
    return (
      <div className="p-8 text-center">
        <p className="mb-4">You need to be logged in to update your profile image.</p>
        <Button onClick={() => window.location.href = '/login'}>Sign In</Button>
      </div>
    )
  }
  
  return (
    <div className="container max-w-2xl mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle>Update Your Profile Image</CardTitle>
          <CardDescription>
            Upload a new profile image that will be properly linked to your doctor account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Current profile section */}
            <div className="flex flex-col sm:flex-row gap-6 items-center p-4 bg-green-50 rounded-lg">
              <div className="text-center sm:text-left">
                <h3 className="text-sm font-medium mb-1">Current Profile</h3>
                <p className="text-sm text-muted-green">{user?.email}</p>
              </div>
              <div className="flex-1"></div>
              <Avatar className="h-20 w-20 border border-green-300">
                <AvatarImage src={profileImageUrl} alt={user?.email || "Doctor"} />
                <AvatarFallback>
                  <User className="h-8 w-8 text-muted-green" />
                </AvatarFallback>
              </Avatar>
            </div>
            
            {/* Upload section */}
            <div className="space-y-4 p-4 border rounded-md">
              <div className="flex flex-col sm:flex-row gap-4 items-center">
                <div className="flex-1">
                  <h3 className="text-sm font-medium mb-1">Upload New Image</h3>
                  <p className="text-xs text-muted-green">
                    Choose a profile image (max 5MB). This will update your profile across the entire platform.
                  </p>
                </div>
                
                <div className="flex items-center gap-3">
                  <Button 
                    variant="outline" 
                    onClick={() => document.getElementById('image-input')?.click()}
                    disabled={uploading}
                    className="relative"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Choose File
                    <input
                      id="image-input"
                      type="file"
                      accept="image/*"
                      onChange={handleFileChange}
                      className="absolute inset-0 opacity-0 cursor-pointer"
                    />
                  </Button>
                  
                  <Button 
                    onClick={handleUpload} 
                    disabled={!selectedFile || uploading}
                  >
                    {uploading ? "Uploading..." : "Upload"}
                  </Button>
                </div>
              </div>
              
              {/* Preview */}
              {imagePreview && (
                <div className="flex flex-col items-center sm:flex-row gap-4 mt-4">
                  <div className="w-32 h-32 relative rounded-md overflow-hidden border border-green-300">
                    <img
                      src={imagePreview}
                      alt="Preview"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <p className="text-sm">{selectedFile?.name}</p>
                    <p className="text-xs text-muted-green">{selectedFile ? formatFileSize(selectedFile.size) : ''}</p>
                  </div>
                </div>
              )}
              
              {/* Status messages */}
              {error && (
                <div className="flex items-start gap-2 text-red-600 bg-red-50 p-3 rounded-md">
                  <AlertCircle className="h-5 w-5 shrink-0 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Error</p>
                    <p className="text-sm">{error}</p>
                  </div>
                </div>
              )}
              
              {success && (
                <div className="flex items-start gap-2 text-green-600 bg-green-50 p-3 rounded-md">
                  <Check className="h-5 w-5 shrink-0 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Success</p>
                    <p className="text-sm">Your profile image has been updated successfully!</p>
                  </div>
                </div>
              )}
            </div>
            
            {/* Troubleshooting section */}
            <div className="mt-6 text-sm text-muted-green space-y-2">
              <h3 className="font-medium text-muted-green">Troubleshooting</h3>
              <p>
                If your profile image is not showing up in other areas of the application after updating:
              </p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Try refreshing the page</li>
                <li>Make sure you are using the same account across all pages</li>
                <li>Clear your browser cache and try again</li>
                <li>Contact support if the issue persists</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes < 1024) return bytes + ' bytes';
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
  return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
} 