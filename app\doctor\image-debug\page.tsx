"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { uploadDoctorProfileImage } from "@/actions/doctor-registration-actions"
import { getSupabaseProfileImageUrl } from "@/app/lib/utils"
import { useToast } from "@/components/ui/mock-toast"

export default function ImageDebugPage() {
  const { toast } = useToast()
  const [file, setFile] = useState<File | null>(null)
  const [originalSize, setOriginalSize] = useState<string>("")
  const [uploadedImagePath, setUploadedImagePath] = useState<string>("")
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string>("")
  const [loading, setLoading] = useState(false)
  const [logs, setLogs] = useState<string[]>([])
  
  const addLog = (message: string) => {
    setLogs(prev => [...prev, `[${new Date().toISOString()}] ${message}`])
  }
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (!selectedFile) return
    
    setFile(selectedFile)
    const size = selectedFile.size / (1024 * 1024)
    setOriginalSize(`${size.toFixed(2)} MB (${selectedFile.type})`)
    addLog(`Selected file: ${selectedFile.name}, Size: ${size.toFixed(2)} MB, Type: ${selectedFile.type}`)
  }
  
  const handleUpload = async () => {
    if (!file) {
      toast({
        title: "No file selected",
        description: "Please select a file first",
        variant: "destructive"
      })
      return
    }
    
    setLoading(true)
    addLog("Starting upload...")
    
    try {
      // Use a test user ID for demonstration
      const userId = "test-" + Date.now()
      addLog(`Using test user ID: ${userId}`)
      
      const result = await uploadDoctorProfileImage(userId, file)
      
      if (result.error) {
        addLog(`Upload error: ${JSON.stringify(result.error)}`)
        toast({
          title: "Upload failed",
          description: result.error.message || "Unknown error",
          variant: "destructive"
        })
      } else if (result.profileImageUrl) {
        addLog(`Upload successful, URL: ${result.profileImageUrl}`)
        
        // Extract path from URL
        const path = result.profileImageUrl.split('/public/')[1]
        addLog(`Extracted path: ${path}`)
        
        setUploadedImagePath(path)
        setUploadedImageUrl(result.profileImageUrl)
        
        toast({
          title: "Upload successful",
          description: "Image was uploaded and should now display below",
        })
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error"
      addLog(`Exception: ${errorMessage}`)
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }
  
  return (
    <div className="container mx-auto py-8 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Image Upload Diagnostics</CardTitle>
          <CardDescription>
            Test image upload and optimization for doctor profile images
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium mb-2">Upload Test Image</h3>
              <div className="space-y-4">
                <div>
                  <input 
                    type="file" 
                    accept="image/*" 
                    onChange={handleFileChange}
                    className="block w-full text-sm text-muted-green file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                  {originalSize && (
                    <p className="mt-1 text-sm text-muted-green">
                      Original size: {originalSize}
                    </p>
                  )}
                </div>
                
                <Button 
                  onClick={handleUpload}
                  disabled={!file || loading}
                  className="w-full"
                >
                  {loading ? "Uploading..." : "Upload & Test"}
                </Button>
              </div>
            </div>
            
            <div>
              <h3 className="font-medium mb-2">Result</h3>
              {uploadedImageUrl ? (
                <div className="space-y-3">
                  <div className="border rounded-lg overflow-hidden w-32 h-32 flex items-center justify-center bg-green-50">
                    <img 
                      src={uploadedImageUrl} 
                      alt="Uploaded image" 
                      className="max-w-full max-h-full object-contain"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Path stored in DB:</p>
                    <p className="text-xs font-mono bg-green-50 p-2 rounded border whitespace-nowrap overflow-x-auto">
                      {uploadedImagePath}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Full URL:</p>
                    <p className="text-xs font-mono bg-green-50 p-2 rounded border whitespace-nowrap overflow-x-auto">
                      {uploadedImageUrl}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="border rounded-lg w-32 h-32 flex items-center justify-center bg-green-50 text-muted-green">
                  No image uploaded
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Debug Logs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-background text-foreground font-mono text-sm p-4 rounded h-60 overflow-y-auto">
            {logs.length > 0 ? (
              logs.map((log, i) => (
                <div key={i}>{log}</div>
              ))
            ) : (
              <div className="text-muted-green">No logs yet</div>
            )}
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Path Testing</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            The following demonstrates how different path formats are handled by the <code>getSupabaseProfileImageUrl</code> utility:
          </p>
          
          <div className="space-y-4">
            {/* Test with actual uploaded path if available */}
            {uploadedImagePath && (
              <div>
                <p className="font-medium">Your uploaded image path:</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  <div className="bg-green-50 p-3 rounded border">
                    <p className="text-xs font-mono break-all">{uploadedImagePath}</p>
                  </div>
                  <div className="bg-green-50 p-3 rounded border">
                    <p className="text-xs font-mono break-all">{getSupabaseProfileImageUrl(uploadedImagePath)}</p>
                  </div>
                </div>
              </div>
            )}
            
            <div>
              <p className="font-medium">Test cases:</p>
              <table className="w-full mt-2 text-sm">
                <thead>
                  <tr className="bg-green-100">
                    <th className="text-left p-2 border">Path Format</th>
                    <th className="text-left p-2 border">Result URL</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="p-2 border font-mono">doctor-profiles/123.jpg</td>
                    <td className="p-2 border font-mono break-all">{getSupabaseProfileImageUrl("doctor-profiles/123.jpg")}</td>
                  </tr>
                  <tr>
                    <td className="p-2 border font-mono">profile-images/doctor-profiles/123.jpg</td>
                    <td className="p-2 border font-mono break-all">{getSupabaseProfileImageUrl("profile-images/doctor-profiles/123.jpg")}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 