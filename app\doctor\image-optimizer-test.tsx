"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, CardH<PERSON>er, CardTitle, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Info, Upload } from "lucide-react"

export default function ImageOptimizerTest() {
  const [originalImage, setOriginalImage] = useState<File | null>(null)
  const [optimizedImage, setOptimizedImage] = useState<File | null>(null)
  const [originalPreview, setOriginalPreview] = useState<string | null>(null)
  const [optimizedPreview, setOptimizedPreview] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [stats, setStats] = useState<{
    originalSize: string;
    optimizedSize: string;
    reduction: number;
    width: number;
    height: number;
    format: string;
  } | null>(null)
  
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return
    
    setOriginalImage(file)
    setOptimizedImage(null)
    setOptimizedPreview(null)
    setStats(null)
    
    // Show preview of original
    const reader = new FileReader()
    reader.onload = (e) => {
      setOriginalPreview(e.target?.result as string || null)
    }
    reader.readAsDataURL(file)
  }
  
  const convertToWebP = async () => {
    if (!originalImage) return
    
    setIsProcessing(true)
    
    try {
      // Load the image
      const img = document.createElement('img')
      const objectUrl = URL.createObjectURL(originalImage)
      
      await new Promise((resolve, reject) => {
        img.onload = resolve
        img.onerror = reject
        img.src = objectUrl
      })
      
      // Calculate new dimensions
      const MAX_WIDTH = 800
      const MAX_HEIGHT = 800
      
      let width = img.width
      let height = img.height
      
      if (width > height) {
        if (width > MAX_WIDTH) {
          height = Math.round(height * (MAX_WIDTH / width))
          width = MAX_WIDTH
        }
      } else {
        if (height > MAX_HEIGHT) {
          width = Math.round(width * (MAX_HEIGHT / height))
          height = MAX_HEIGHT
        }
      }
      
      // Create canvas and draw resized image
      const canvas = document.createElement('canvas')
      canvas.width = width
      canvas.height = height
      const ctx = canvas.getContext('2d')
      if (!ctx) throw new Error("Couldn't get canvas context")
      
      ctx.drawImage(img, 0, 0, width, height)
      
      // Clean up the object URL
      URL.revokeObjectURL(objectUrl)
      
      // Convert to WebP
      const QUALITY = 0.8
      const webpDataUrl = canvas.toDataURL('image/webp', QUALITY)
      
      // Convert data URL back to blob/file
      const byteString = atob(webpDataUrl.split(',')[1])
      const mimeString = webpDataUrl.split(',')[0].split(':')[1].split(';')[0]
      const ab = new ArrayBuffer(byteString.length)
      const ia = new Uint8Array(ab)
      
      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i)
      }
      
      // Create optimized file
      const optimizedBlob = new Blob([ab], { type: mimeString })
      const optimizedName = originalImage.name.split('.')[0] + '.webp'
      const optimizedFile = new File([optimizedBlob], optimizedName, { 
        type: mimeString,
        lastModified: originalImage.lastModified 
      })
      
      setOptimizedImage(optimizedFile)
      setOptimizedPreview(webpDataUrl)
      
      // Calculate stats
      const originalSizeMB = originalImage.size / (1024 * 1024)
      const optimizedSizeMB = optimizedFile.size / (1024 * 1024)
      const reduction = Math.round((1 - optimizedFile.size / originalImage.size) * 100)
      
      setStats({
        originalSize: originalSizeMB.toFixed(2) + ' MB',
        optimizedSize: optimizedSizeMB.toFixed(2) + ' MB',
        reduction,
        width,
        height,
        format: 'WebP'
      })
      
    } catch (error) {
      console.error('Error optimizing image:', error)
    } finally {
      setIsProcessing(false)
    }
  }
  
  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            WebP Image Optimizer Test
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-6 flex flex-col gap-4">
            <div>
              <label className="block mb-2 text-sm font-medium">Select an image to optimize</label>
              <input 
                type="file" 
                accept="image/*" 
                onChange={handleFileSelect} 
                className="block w-full text-sm text-green-900 border border-green-400 rounded-lg cursor-pointer bg-green-50 focus:outline-none p-2.5"
              />
            </div>
            
            {originalImage && (
              <Button 
                onClick={convertToWebP}
                disabled={isProcessing}
                className="w-full md:w-auto"
              >
                {isProcessing ? "Optimizing..." : "Convert to WebP & Optimize"}
              </Button>
            )}
          </div>
          
          {isProcessing && (
            <div className="mb-6">
              <p className="text-sm mb-2">Processing image...</p>
              <Progress value={50} className="h-2" />
            </div>
          )}
          
          {stats && (
            <div className="mb-6 bg-blue-50 border border-blue-200 rounded-md p-4">
              <h3 className="font-semibold text-blue-800 flex items-center gap-2 mb-3">
                <Info className="h-4 w-4" />
                Optimization Results
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm font-medium text-blue-700">Size Reduction</p>
                  <p className="text-2xl font-bold text-blue-800">{stats.reduction}%</p>
                  <p className="text-xs text-blue-600">
                    From {stats.originalSize} to {stats.optimizedSize}
                  </p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-blue-700">Dimensions</p>
                  <p className="text-2xl font-bold text-blue-800">{stats.width}x{stats.height}</p>
                  <p className="text-xs text-blue-600">
                    Resized to optimal dimensions
                  </p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-blue-700">Format</p>
                  <p className="text-2xl font-bold text-blue-800">{stats.format}</p>
                  <p className="text-xs text-blue-600">
                    Modern compressed format
                  </p>
                </div>
              </div>
            </div>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium mb-3">Original Image</h3>
              {originalPreview ? (
                <div className="border rounded-md overflow-hidden bg-green-100 aspect-square flex items-center justify-center">
                  <img 
                    src={originalPreview} 
                    alt="Original" 
                    className="max-w-full max-h-full object-contain"
                  />
                </div>
              ) : (
                <div className="border rounded-md aspect-square flex items-center justify-center bg-green-100 text-muted-green">
                  No image selected
                </div>
              )}
              {originalImage && (
                <p className="text-sm text-muted-green mt-2">
                  {originalImage.name} ({(originalImage.size / (1024 * 1024)).toFixed(2)} MB)
                </p>
              )}
            </div>
            
            <div>
              <h3 className="font-medium mb-3">Optimized WebP Image</h3>
              {optimizedPreview ? (
                <div className="border rounded-md overflow-hidden bg-green-100 aspect-square flex items-center justify-center">
                  <img 
                    src={optimizedPreview} 
                    alt="Optimized" 
                    className="max-w-full max-h-full object-contain"
                  />
                </div>
              ) : (
                <div className="border rounded-md aspect-square flex items-center justify-center bg-green-100 text-muted-green">
                  {originalPreview ? "Click 'Convert to WebP & Optimize'" : "No image selected"}
                </div>
              )}
              {optimizedImage && (
                <p className="text-sm text-muted-green mt-2">
                  {optimizedImage.name} ({(optimizedImage.size / (1024 * 1024)).toFixed(2)} MB)
                </p>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex-col items-start">
          <div className="bg-green-50 border border-green-200 rounded-md p-4 w-full">
            <h3 className="font-semibold text-green-800 mb-2">About WebP Optimization</h3>
            <p className="text-sm text-green-700 mb-2">
              WebP is a modern image format developed by Google that provides superior compression compared to JPG and PNG while maintaining good quality.
            </p>
            <ul className="list-disc pl-5 text-sm text-green-700 space-y-1">
              <li>Smaller file sizes (typically 25-50% smaller than JPG)</li>
              <li>Supports both lossy and lossless compression</li>
              <li>Supports transparency like PNG but with smaller file sizes</li>
              <li>Widely supported in modern browsers</li>
            </ul>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
} 