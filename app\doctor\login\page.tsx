"use client"

import { useState } from "react"
import { LoginDialog } from "@/components/login/login-dialog"
import { useSearchParams, useRouter } from "next/navigation"
import { ChooseRoleDialog } from "@/components/registration/choose-role-dialog"

export default function DoctorLoginPage() {
  const [open, setOpen] = useState(true)
  const [showSignUp, setShowSignUp] = useState(false)
  const searchParams = useSearchParams()
  const redirectTo = searchParams ? searchParams.get('redirectTo') : null
  const router = useRouter()

  // Handle login dialog closing - redirect to appropriate page
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen)
    if (!isOpen && !showSignUp) {
      // Only redirect if we're not showing the sign-up form
      // Use redirectTo if available, otherwise default to dashboard
      router.push(redirectTo || '/doctor/dashboard-placeholder')
    }
  }

  // Handle sign-up click
  const handleSignUpClick = () => {
    console.log("Doctor login: Sign up clicked");
    setOpen(false);
    // Directly navigate to the doctor registration page
    router.push('/doctor/register');
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-green-50 to-white">
      {/* Login Dialog */}
      <LoginDialog 
        open={open} 
        onOpenChange={handleOpenChange} 
        userType="doctor"
        onSignUpClick={handleSignUpClick}
        redirectUrl={redirectTo || "/doctor/dashboard"}
      />
      
      {/* Registration Dialog - shown when sign up clicked */}
      {showSignUp && (
        <ChooseRoleDialog
          open={showSignUp}
          onOpenChange={(isOpen) => {
            console.log("Registration dialog change:", isOpen);
            setShowSignUp(isOpen);
            if (!isOpen) {
              // If sign-up closed without completing, show login again
              setOpen(true);
            }
          }}
        />
      )}
    </div>
  )
}

