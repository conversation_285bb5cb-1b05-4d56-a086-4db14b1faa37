"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { getSupabaseProfileImageUrl } from "@/app/lib/utils"

export default function ProfileImageTest() {
  const [testResults, setTestResults] = useState<Array<{path: string, url: string}>>([])
  
  useEffect(() => {
    // Test various path formats
    const testPaths = [
      // Path with bucket name and subfolders (new format - should work correctly)
      'profile-images/doctor-profiles/12345-1234567890.jpg',
      
      // Path with just the filename (should add bucket name automatically)
      'doctor-12345.jpg',
      
      // Old inconsistent format (should still work)
      'profile-images/doctor-12345.jpg',
      
      // Path starting with slash (should remove the slash)
      '/profile-images/doctor-12345.jpg',
      
      // Already a full URL (should pass through unchanged)
      'https://uapbzzscckhtptliynyj.supabase.co/storage/v1/object/public/profile-images/doctor-profiles/12345-1234567890.jpg'
    ]
    
    // Generate URLs for each test path
    const results = testPaths.map(path => ({
      path,
      url: getSupabaseProfileImageUrl(path)
    }))
    
    setTestResults(results)
  }, [])
  
  return (
    <div className="container mx-auto py-8">
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Profile Image URL Generation Test</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="mb-4">This page tests that the image URL generation works correctly with the updated path format.</p>
          
          <div className="border rounded-md overflow-hidden">
            <table className="w-full">
              <thead className="bg-green-100">
                <tr>
                  <th className="px-4 py-2 text-left">Path in Database</th>
                  <th className="px-4 py-2 text-left">Generated URL</th>
                  <th className="px-4 py-2 text-left">Image Preview</th>
                </tr>
              </thead>
              <tbody>
                {testResults.map((result, index) => (
                  <tr key={index} className="border-t">
                    <td className="px-4 py-2 font-mono text-sm break-all">
                      {result.path}
                    </td>
                    <td className="px-4 py-2 font-mono text-sm break-all">
                      {result.url}
                    </td>
                    <td className="px-4 py-2">
                      <div className="relative w-12 h-12 bg-green-200 rounded-md overflow-hidden">
                        {/* Use a placeholder since these aren't real images */}
                        <div className="absolute inset-0 flex items-center justify-center text-muted-green text-xs">
                          IMG
                        </div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-md">
            <h3 className="font-medium text-green-800 mb-2">Summary of Changes Made</h3>
            <ol className="list-decimal pl-5 space-y-1 text-green-700">
              <li>Fixed the <code className="bg-white px-1 rounded">uploadDoctorProfileImage</code> function to store the full, correct path</li>
              <li>Removed path manipulation in <code className="bg-white px-1 rounded">updateDoctorProfileImage</code> to preserve the exact storage path</li>
              <li>Confirmed <code className="bg-white px-1 rounded">getSupabaseProfileImageUrl</code> correctly handles all path formats</li>
            </ol>
            <p className="mt-4 text-green-700">
              These changes ensure that new doctor registrations will correctly display profile images.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 