"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { getSupabaseProfileImageUrl } from "@/app/lib/utils"

export default function ProfileImageTestPage() {
  // Test various path formats
  const testPaths = [
    // Path with bucket name and subfolders
    'profile-images/doctor-profiles/12345-1234567890.jpg',
    
    // Path with just doctor-profiles subfolder (correct format from uploadDoctorProfileImage)
    'doctor-profiles/12345-1234567890.jpg',
    
    // Path with just the filename
    'doctor-12345.jpg',
    
    // Path starting with slash
    '/profile-images/doctor-12345.jpg',
    
    // Already a full URL
    'https://uapbzzscckhtptliynyj.supabase.co/storage/v1/object/public/profile-images/doctor-profiles/12345-1234567890.jpg'
  ]
  
  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle>Profile Image Path Test</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="mb-4">This page tests how different image path formats are processed by getSupabaseProfileImageUrl.</p>
          
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-green-100">
                <th className="border p-2 text-left">Original Path</th>
                <th className="border p-2 text-left">Generated URL</th>
                <th className="border p-2 text-left">Image</th>
              </tr>
            </thead>
            <tbody>
              {testPaths.map((path, index) => {
                const url = getSupabaseProfileImageUrl(path)
                return (
                  <tr key={index} className="border-b">
                    <td className="border p-2 font-mono text-sm">{path}</td>
                    <td className="border p-2 font-mono text-sm break-all">{url}</td>
                    <td className="border p-2">
                      <div className="h-12 w-12 overflow-hidden rounded-full bg-green-100">
                        <img 
                          src={url} 
                          alt={`Test ${index + 1}`}
                          className="h-full w-full object-cover"
                          onError={(e) => {
                            // Replace broken images with placeholder
                            e.currentTarget.src = '/placeholder-avatar.png';
                          }}
                        />
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </CardContent>
      </Card>
    </div>
  )
} 