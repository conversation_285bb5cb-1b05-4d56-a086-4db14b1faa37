"use client"

import { useState } from "react"
import { useRouter } from "next/navigation" // Import useRouter
import { NewDoctorRegistration } from "@/components/registration/new-doctor-registration"
import { MedicalSportsModal } from "@/components/medical-sports-modal" // Import MedicalSportsModal
import { motion } from "framer-motion"
import { Stethoscope } from "lucide-react"

export default function DoctorRegisterPage() {
  const [open, setOpen] = useState(true)
  const router = useRouter() // Initialize useRouter

  const handleCloseMessage = () => {
    router.push("/") // Navigate to home page
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background via-background to-primary/20">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute -top-20 -right-20 w-64 h-64 rounded-full bg-gradient-to-br from-primary/20 to-primary/5"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute top-1/3 -left-20 w-80 h-80 rounded-full bg-gradient-to-tr from-primary/20 to-blue-500/10"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{
            duration: 10,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
            delay: 1,
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-40 h-40 rounded-full bg-gradient-to-bl from-primary/15 to-green-500/10"
          animate={{
            scale: [1, 1.4, 1],
            opacity: [0.1, 0.3, 0.1],
          }}
          transition={{
            duration: 12,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
            delay: 2,
          }}
        />
      </div>

      <NewDoctorRegistration open={open} onOpenChange={setOpen} />

      {!open && (
        <MedicalSportsModal
          isOpen={!open}
          onClose={handleCloseMessage}
          title="Registration Closed"
          icon="referee" // Using referee icon for stethoscope as per image
        >
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center p-4"
          >
            <div className="mb-6">
              <motion.div
                className="mx-auto bg-gradient-to-b from-background to-background/90 p-4 rounded-full border border-primary/30 shadow-[0_0_15px_rgba(0,200,255,0.15)] w-fit"
                whileHover={{
                  scale: 1.05,
                  boxShadow: "0 0 20px rgba(0,200,255,0.3)",
                  borderColor: "rgba(0,200,255,0.5)"
                }}
                transition={{ duration: 0.3 }}
              >
                <motion.div
                  animate={{ y: [0, -3, 0] }}
                  transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
                >
                  <Stethoscope className="h-10 w-10 text-primary drop-shadow-[0_0_5px_rgba(0,200,255,0.5)]" />
                </motion.div>
              </motion.div>
            </div>
            
            <p className="text-foreground/90 mb-6">You've closed the registration form. Would you like to register again?</p>
            
            <motion.button
              onClick={() => setOpen(true)}
              className="px-6 py-3 bg-primary hover:bg-primary/90 text-foreground rounded-full font-medium shadow-lg shadow-primary/25 transition-all duration-300"
              whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
              whileTap={{ scale: 0.98 }}
            >
              Open Registration
            </motion.button>
          </motion.div>
        </MedicalSportsModal>
      )}
    </div>
  )
}
