"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/context/AuthContext"
import { createClient } from "@supabase/supabase-js"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { 
  Star, 
  Search, 
  Filter, 
  ArrowLeft, 
  LayoutDashboard, 
  Download, 
  FileText, 
  Video, 
  BookOpen, 
  ExternalLink,
  CheckCircle2,
  Tag,
  Loader2
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { toast } from "sonner"

interface Resource {
  id: string;
  title: string;
  description: string;
  url: string;
  type: 'article' | 'guide' | 'video' | 'ebook';
  specialtyId: string;
  specialtyName: string;
  tags: string[];
  date: string;
  publisher: string;
  isPremium: boolean;
}

export function DoctorResourcesClient({ specialtyId }: { specialtyId: string }) {
  const { isAuthenticated, user: authUser, isLoading: authIsLoading } = useAuth()
  const [specialty, setSpecialty] = useState<any>(null)
  const [resources, setResources] = useState<Resource[]>([])
  const [filteredResources, setFilteredResources] = useState<Resource[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [resourceTypeFilter, setResourceTypeFilter] = useState("all")
  const [premiumFilter, setPremiumFilter] = useState("all")
  const router = useRouter()
  
  // Create Service Role Client
  const createServiceRoleClient = () => {
    const supabaseUrl = "https://uapbzzscckhtptliynyj.supabase.co"
    const supabaseServiceKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q"
    return createClient(supabaseUrl, supabaseServiceKey)
  }
  
  // Sample specialty mapping
  const specialtyMap: Record<string, string> = {
    "1": "Cardiology",
    "2": "Neurology",
    "3": "Dermatology",
    "4": "Orthopedics",
    "5": "Pediatrics",
    "6": "Gynecology",
    "7": "Urology",
    "8": "Ophthalmology",
    "9": "Endocrinology",
    "10": "Oncology"
  }
  
  // Sample resources data
  const sampleResources: Resource[] = [
    {
      id: "1",
      title: "Latest Advances in Cardiovascular Medicine",
      description: "A comprehensive review of recent developments in cardiovascular treatments and diagnosis methods.",
      url: "https://www.journals.com/cardiology/advances",
      type: "article",
      specialtyId: "1",
      specialtyName: "Cardiology",
      tags: ["Research", "Treatment", "Innovation"],
      date: "2025-03-15",
      publisher: "International Journal of Cardiology",
      isPremium: false
    },
    {
      id: "2",
      title: "Diagnostic Imaging Techniques for Heart Conditions",
      description: "This guide covers modern imaging techniques for accurate cardiac diagnostics.",
      url: "https://www.medicalresource.com/cardiology/imaging",
      type: "guide",
      specialtyId: "1",
      specialtyName: "Cardiology",
      tags: ["Diagnostics", "Imaging", "Technology"],
      date: "2025-01-20",
      publisher: "Medical Resource Center",
      isPremium: true
    },
    {
      id: "3",
      title: "Early Signs of Neurological Disorders",
      description: "Learn to identify early indicators of common neurological conditions for better patient outcomes.",
      url: "https://www.neurojournal.org/early-signs",
      type: "article",
      specialtyId: "2",
      specialtyName: "Neurology",
      tags: ["Diagnosis", "Prevention", "Patient Care"],
      date: "2025-02-08",
      publisher: "Neurology Today",
      isPremium: false
    },
    {
      id: "4",
      title: "Dermatologic Manifestations of Systemic Diseases",
      description: "Understanding skin signs that may indicate underlying systemic conditions.",
      url: "https://www.dermatologycentral.com/systemic",
      type: "ebook",
      specialtyId: "3",
      specialtyName: "Dermatology",
      tags: ["Systemic Disease", "Diagnosis", "Clinical Signs"],
      date: "2024-11-30",
      publisher: "Dermatology Central",
      isPremium: true
    },
    {
      id: "5",
      title: "Minimally Invasive Techniques in Orthopedic Surgery",
      description: "Video demonstrations of cutting-edge minimally invasive orthopedic procedures.",
      url: "https://www.orthosurgery.edu/videos/minimally-invasive",
      type: "video",
      specialtyId: "4",
      specialtyName: "Orthopedics",
      tags: ["Surgery", "Innovation", "Techniques"],
      date: "2025-04-02",
      publisher: "Orthopedic Surgery Academy",
      isPremium: false
    },
    {
      id: "6",
      title: "Advanced Interventional Cardiology Procedures",
      description: "Step-by-step video guide on the latest interventional techniques in cardiology.",
      url: "https://www.cardio-edu.com/interventional/videos",
      type: "video",
      specialtyId: "1",
      specialtyName: "Cardiology",
      tags: ["Interventional", "Procedures", "Techniques"],
      date: "2025-02-28",
      publisher: "Cardiology Education Network",
      isPremium: true
    },
    {
      id: "7",
      title: "Heart Failure Management Guidelines 2025",
      description: "The latest evidence-based guidelines for managing heart failure patients.",
      url: "https://www.cardiology-association.org/guidelines/heart-failure",
      type: "guide",
      specialtyId: "1",
      specialtyName: "Cardiology",
      tags: ["Guidelines", "Management", "Clinical Practice"],
      date: "2025-01-15",
      publisher: "American Association of Cardiology",
      isPremium: false
    },
    {
      id: "8",
      title: "Cardiovascular Pharmacology Handbook",
      description: "A comprehensive reference for cardiovascular medications, mechanisms, and clinical applications.",
      url: "https://www.medical-library.com/cardiology/pharmacology",
      type: "ebook",
      specialtyId: "1",
      specialtyName: "Cardiology",
      tags: ["Pharmacology", "Medications", "Reference"],
      date: "2024-12-10",
      publisher: "Medical Library Press",
      isPremium: true
    }
  ]
  
  // Replace resource fetch and display with a "Coming Soon" message
  useEffect(() => {
    if (authIsLoading) return;
    
    if (!isAuthenticated || !authUser) {
      router.push("/doctor/login");
      return;
    }
    
    const fetchSpecialtyOnly = async () => {
      setIsLoading(true);
      try {
        const serviceClient = createServiceRoleClient();
        
        // Fetch specialty from database
        try {
          const { data: specialtyData, error: specialtyError } = await serviceClient
            .from('specialties')
            .select('*')
            .eq('specialty_id', specialtyId)
            .single();
          
          if (specialtyError) {
            console.error("Error fetching specialty:", specialtyError);
            // Fallback to map
            setSpecialty({ 
              specialty_id: specialtyId,
              specialty_name: specialtyMap[specialtyId] || `Specialty ${specialtyId}`
            });
          } else if (specialtyData) {
            setSpecialty(specialtyData);
          } else {
            setSpecialty({ 
              specialty_id: specialtyId,
              specialty_name: specialtyMap[specialtyId] || `Specialty ${specialtyId}`
            });
          }
        } catch (error) {
          console.error("Exception fetching specialty:", error);
          setSpecialty({ 
            specialty_id: specialtyId,
            specialty_name: specialtyMap[specialtyId] || `Specialty ${specialtyId}`
          });
        }
      } catch (error) {
        console.error("Error fetching specialty information:", error);
        toast.error("Failed to load specialty information");
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchSpecialtyOnly();
  }, [authIsLoading, isAuthenticated, authUser, specialtyId, router]);
  
  if (authIsLoading || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-background to-background">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
          <p className="text-lg text-foreground/70 mt-4">
            {authIsLoading ? "Loading..." : "Redirecting to login..."}
          </p>
        </div>
      </div>
    );
  }
  
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-background p-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold text-foreground">Medical Resources</h1>
          </div>
          
          <div className="flex justify-center">
            <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-background pb-16">
      {/* Header with navigation */}
      <div className="bg-gradient-to-r from-primary/20 to-primary/10 border-b border-primary/20">
        <div className="container mx-auto py-8 px-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-2">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-foreground hover:bg-white/10"
                  onClick={() => router.push("/doctor/dashboard")}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Dashboard
                </Button>
              </div>
              <h1 className="text-2xl md:text-3xl font-bold text-foreground mt-2">
                {specialty?.specialty_name || "Medical"} Resources
              </h1>
              <p className="text-foreground/70 mt-1">
                Evidence-based materials for your specialty
              </p>
            </div>
            <Button
              className="bg-primary/90 hover:bg-primary text-foreground hidden md:flex"
              onClick={() => router.push("/doctor/dashboard")}
            >
              <LayoutDashboard className="h-4 w-4 mr-2" />
              Dashboard
            </Button>
          </div>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-16">
        {/* Coming Soon Message */}
        <Card className="bg-background/40 border-primary/20 mx-auto max-w-3xl">
          <CardContent className="flex flex-col items-center justify-center py-16">
            <div className="h-24 w-24 rounded-full bg-primary/20 text-primary flex items-center justify-center mb-6">
              <BookOpen className="h-12 w-12" />
            </div>
            <h2 className="text-2xl font-bold text-foreground mb-4">Medical Resources Coming Soon</h2>
            <p className="text-foreground/70 text-center max-w-xl mb-6">
              We're currently compiling high-quality, evidence-based resources for {specialty?.specialty_name || "your specialty"}. 
              Soon you'll have access to articles, videos, guidelines, and more to support your medical practice.
            </p>
            <Button 
              className="bg-primary/90 hover:bg-primary text-foreground"
              onClick={() => router.push("/doctor/dashboard")}
            >
              <LayoutDashboard className="h-4 w-4 mr-2" />
              Return to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 