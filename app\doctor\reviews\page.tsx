"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/context/AuthContext"
import { createClient } from "@supabase/supabase-js"
import { useRouter } from "next/navigation"
import { 
  Star, 
  User, 
  CalendarDays, 
  MessageSquare, 
  ThumbsUp, 
  ThumbsDown,
  ArrowLeft, 
  Search,
  Filter,
  SlidersHorizontal,
  ArrowDownUp,
  Loader2
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { format } from "date-fns"

export default function DoctorReviewsPage() {
  const { isAuthenticated, user: authUser, isLoading: authIsLoading } = useAuth()
  const [dataLoading, setDataLoading] = useState(true)
  const [doctorProfile, setDoctorProfile] = useState<any>(null)
  const [reviews, setReviews] = useState<any[]>([])
  const [filteredReviews, setFilteredReviews] = useState<any[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [ratingFilter, setRatingFilter] = useState<string>("all")
  const [sortOption, setSortOption] = useState<string>("newest")
  const router = useRouter()

  const createServiceRoleClient = () => {
    const supabaseUrl = "https://uapbzzscckhtptliynyj.supabase.co"
    const supabaseServiceKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q"
    return createClient(supabaseUrl, supabaseServiceKey)
  }

  useEffect(() => {
    if (authIsLoading) return
    
    if (!isAuthenticated || !authUser) {
      router.push("/")
      return
    }
    
    if (authUser.userType !== 'doctor') {
      router.push("/")
      return
    }
    
    const fetchDoctorReviews = async () => {
      setDataLoading(true)
      try {
        const serviceClient = createServiceRoleClient()
        
        let doctorData
        
        // Try to find doctor by all possible methods
        if (authUser.userId) {
          const { data, error } = await serviceClient
            .from('doctors')
            .select('*')
            .eq('auth_id', authUser.userId)
            .single()
            
          if (!error && data) {
            doctorData = data
          } else {
            const { data: doctorIdData, error: doctorIdError } = await serviceClient
              .from('doctors')
              .select('*')
              .eq('doctor_id', authUser.userId)
              .single()
              
            if (!doctorIdError && doctorIdData) {
              doctorData = doctorIdData
            }
          }
        }
        
        if (!doctorData && authUser.email) {
          const { data, error } = await serviceClient
            .from('doctors')
            .select('*')
            .eq('email', authUser.email)
            .single()
            
          if (!error && data) {
            doctorData = data
          }
        }
        
        if (doctorData) {
          setDoctorProfile(doctorData)
          
          try {
            // Fetch all reviews for this doctor
            const { data: reviewsData, error: reviewsError } = await serviceClient
              .from('reviews')
              .select(`
                *,
                users:user_id(*)
              `)
              .eq('doctor_id', doctorData.doctor_id)
              .order('review_date', { ascending: false })
              
            if (!reviewsError && reviewsData) {
              console.log("Retrieved reviews:", reviewsData)
              setReviews(reviewsData)
              setFilteredReviews(reviewsData)
            } else {
              console.error("Error fetching reviews:", reviewsError)
              // Create mock data as fallback
              generateMockReviews(doctorData.doctor_id)
            }
          } catch (err) {
            console.error("Error fetching reviews:", err)
            // Fallback to mock reviews
            generateMockReviews(doctorData.doctor_id)
          }
        } else {
          // No doctor profile found, use mock data
          generateMockReviews(0)
        }
      } catch (err) {
        console.error("Error fetching doctor data:", err)
        // Use mock data
        generateMockReviews(0)
      } finally {
        setDataLoading(false)
      }
    }
    
    const generateMockReviews = (doctorId: number) => {
      const mockReviews = []
      const today = new Date()
      
      // Some sample review comments
      const reviewComments = [
        "Great doctor, very attentive and knowledgeable.",
        "I appreciated the time taken to explain my condition thoroughly.",
        "The doctor was professional but seemed rushed during my visit.",
        "Excellent bedside manner and very thorough in the examination.",
        "Wait time was too long, but the care received was good.",
        "Very satisfied with my visit and the treatment plan.",
        "The doctor seemed distracted during our appointment.",
        "Highly recommend this doctor for their expertise and compassion.",
        "The staff was friendly but the doctor was not very helpful.",
        "I felt my concerns were taken seriously and addressed appropriately."
      ]
      
      // Generate random reviews
      for (let i = 1; i <= 12; i++) {
        const reviewDate = new Date(today)
        reviewDate.setDate(today.getDate() - Math.floor(Math.random() * 180)) // Random date within last 6 months
        
        const rating = Math.floor(Math.random() * 5) + 1 // Random rating 1-5
        
        mockReviews.push({
          review_id: `review-${doctorId}-${i}`,
          user_id: `user-${i}`,
          doctor_id: doctorId,
          rating: rating,
          overall_satisfaction: rating,
          clinical_competence: Math.floor(Math.random() * 5) + 1,
          communication_stats: Math.floor(Math.random() * 5) + 1,
          empathy_compassion: Math.floor(Math.random() * 5) + 1,
          time_management: Math.floor(Math.random() * 5) + 1,
          follow_up_care: Math.floor(Math.random() * 5) + 1,
          additional_comments: reviewComments[Math.floor(Math.random() * reviewComments.length)],
          review_date: reviewDate.toISOString().split('T')[0],
          sentiment_score: (rating / 5) * 100,
          Recommendation: rating >= 4 ? 1 : 0,
          users: {
            user_id: `user-${i}`,
            username: `patient${i}`,
            first_name: `Patient`,
            last_name: `${i}`,
            email: `patient${i}@example.com`
          }
        })
      }
      
      setReviews(mockReviews)
      setFilteredReviews(mockReviews)
    }
    
    fetchDoctorReviews()
  }, [authIsLoading, isAuthenticated, authUser, router])
  
  // Apply filters and sorting
  useEffect(() => {
    let filtered = [...reviews]
    
    // Apply rating filter
    if (ratingFilter !== "all") {
      const ratingValue = parseInt(ratingFilter)
      filtered = filtered.filter(review => 
        Math.round(review.rating) === ratingValue
      )
    }
    
    // Apply search query
    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(review => 
        (review.additional_comments && review.additional_comments.toLowerCase().includes(query)) ||
        (review.users && review.users.username && review.users.username.toLowerCase().includes(query)) ||
        (review.users && review.users.first_name && review.users.first_name.toLowerCase().includes(query)) ||
        (review.users && review.users.last_name && review.users.last_name.toLowerCase().includes(query))
      )
    }
    
    // Apply sorting
    if (sortOption === "newest") {
      filtered.sort((a, b) => new Date(b.review_date).getTime() - new Date(a.review_date).getTime())
    } else if (sortOption === "oldest") {
      filtered.sort((a, b) => new Date(a.review_date).getTime() - new Date(b.review_date).getTime())
    } else if (sortOption === "highest") {
      filtered.sort((a, b) => b.rating - a.rating)
    } else if (sortOption === "lowest") {
      filtered.sort((a, b) => a.rating - b.rating)
    }
    
    setFilteredReviews(filtered)
  }, [reviews, searchQuery, ratingFilter, sortOption])
  
  const renderStars = (rating: number) => {
    const stars = []
    const roundedRating = Math.round(rating)
    
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Star 
          key={i} 
          className={`h-4 w-4 ${i <= roundedRating ? 'text-amber-400 fill-amber-400' : 'text-muted-green'}`} 
        />
      )
    }
    
    return <div className="flex">{stars}</div>
  }
  
  const renderRatingStats = (review: any) => {
    // Create an array of all ratings from the review
    const ratings = [
      { name: "Clinical Competence", value: review.clinical_competence },
      { name: "Communication", value: review.communication_stats },
      { name: "Empathy & Compassion", value: review.empathy_compassion },
      { name: "Time Management", value: review.time_management },
      { name: "Follow-up Care", value: review.follow_up_care }
    ]
    
    return (
      <div className="grid grid-cols-2 gap-x-6 gap-y-2 mt-3">
        {ratings.map(rating => (
          rating.value ? (
            <div key={rating.name} className="flex justify-between items-center text-sm">
              <span className="text-foreground/70">{rating.name}:</span>
              <div className="flex items-center">
                <span className="text-foreground mr-1">{rating.value}/5</span>
                <Star className={`h-3 w-3 ${rating.value >= 4 ? 'text-amber-400 fill-amber-400' : rating.value >= 3 ? 'text-amber-400' : 'text-muted-green'}`} />
              </div>
            </div>
          ) : null
        ))}
      </div>
    )
  }
  
  if (authIsLoading || dataLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-background to-background">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
          <p className="mt-4 text-lg text-foreground/70">Loading reviews...</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-background pb-12">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary/20 to-primary/10 border-b border-primary/20">
        <div className="container mx-auto py-6 px-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button 
                variant="ghost" 
                className="p-2 text-foreground" 
                onClick={() => router.push("/doctor/dashboard")}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <h1 className="text-2xl font-bold text-foreground">
                Patient Reviews
              </h1>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge 
                className="bg-primary/20 text-primary hover:bg-primary/30 border-0 py-1 px-3"
              >
                <span className="mr-1 font-bold">{doctorProfile?.review_count || reviews.length}</span> Reviews
              </Badge>
              
              <Badge 
                className="bg-amber-500/20 text-amber-300 hover:bg-amber-500/30 border-0 py-1 px-3"
              >
                <span className="mr-1 font-bold">{doctorProfile?.rating?.toFixed(1) || (reviews.reduce((sum, r) => sum + r.rating, 0) / (reviews.length || 1)).toFixed(1)}</span>
                <Star className="h-3 w-3 ml-1 fill-amber-400" />
              </Badge>
            </div>
          </div>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-8">
        {/* Filters and Search */}
        <div className="mb-8 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-foreground/50" />
            <Input 
              placeholder="Search reviews..." 
              className="pl-10 bg-background/90/50 border-border text-foreground placeholder:text-foreground/50 focus-visible:ring-primary"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <div className="flex items-center gap-3">
            <SlidersHorizontal className="h-4 w-4 text-foreground/70" />
            <Select value={ratingFilter} onValueChange={setRatingFilter}>
              <SelectTrigger 
                className="bg-background/90/50 border-border text-foreground focus:ring-primary"
              >
                <SelectValue placeholder="Filter by rating" />
              </SelectTrigger>
              <SelectContent className="bg-background/90 border-border text-foreground">
                <SelectItem value="all">All Ratings</SelectItem>
                <SelectItem value="5">5 Stars</SelectItem>
                <SelectItem value="4">4 Stars</SelectItem>
                <SelectItem value="3">3 Stars</SelectItem>
                <SelectItem value="2">2 Stars</SelectItem>
                <SelectItem value="1">1 Star</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center gap-3">
            <ArrowDownUp className="h-4 w-4 text-foreground/70" />
            <Select value={sortOption} onValueChange={setSortOption}>
              <SelectTrigger 
                className="bg-background/90/50 border-border text-foreground focus:ring-primary"
              >
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent className="bg-background/90 border-border text-foreground">
                <SelectItem value="newest">Newest First</SelectItem>
                <SelectItem value="oldest">Oldest First</SelectItem>
                <SelectItem value="highest">Highest Rating</SelectItem>
                <SelectItem value="lowest">Lowest Rating</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        {/* Reviews List */}
        <div className="space-y-6">
          {filteredReviews.length === 0 ? (
            <Card className="bg-gradient-to-b from-background/90 to-background border-border">
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 text-primary/70" />
                  <h3 className="text-xl font-medium text-foreground">No Reviews Found</h3>
                  <p className="text-foreground/60 mt-2">
                    {searchQuery || ratingFilter !== "all" ? 
                      "Try adjusting your search or filters" : 
                      "You don't have any patient reviews yet"
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            filteredReviews.map((review) => (
              <Card 
                key={review.review_id} 
                className="bg-gradient-to-b from-background/90 to-background border-border"
              >
                <CardContent className="p-6">
                  <div className="flex flex-col md:flex-row justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                          <User className="h-5 w-5 text-primary" />
                        </div>
                        <div>
                          <div className="flex flex-wrap gap-2 items-center">
                            <h3 className="text-lg font-medium text-foreground">
                              {review.users ? 
                                `${review.users.first_name || ''} ${review.users.last_name || ''}` : 
                                `Anonymous Patient`
                              }
                            </h3>
                            <div className="flex items-center gap-1">
                              {renderStars(review.rating)}
                              <span className="text-foreground/70 text-sm ml-1">({review.rating.toFixed(1)})</span>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2 text-sm text-foreground/60 mt-1">
                            <CalendarDays className="h-3.5 w-3.5" />
                            <span>{review.review_date ? format(new Date(review.review_date), 'MMMM d, yyyy') : 'Unknown Date'}</span>
                          </div>
                        </div>
                      </div>
                      
                      {review.additional_comments && (
                        <div className="mt-4 text-foreground/90">
                          <p>"{review.additional_comments}"</p>
                        </div>
                      )}
                      
                      {renderRatingStats(review)}
                    </div>
                    
                    <div className="md:text-right mt-4 md:mt-0">
                      <Badge 
                        className={`
                          ${review.Recommendation === 1 ? 
                            'bg-green-500/20 text-green-400 hover:bg-green-500/30' : 
                            'bg-red-500/20 text-red-400 hover:bg-red-500/30'}
                          border-0
                        `}
                      >
                        {review.Recommendation === 1 ? (
                          <div className="flex items-center gap-1">
                            <ThumbsUp className="h-3.5 w-3.5" />
                            <span>Recommends</span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-1">
                            <ThumbsDown className="h-3.5 w-3.5" />
                            <span>Doesn't Recommend</span>
                          </div>
                        )}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </div>
  )
} 