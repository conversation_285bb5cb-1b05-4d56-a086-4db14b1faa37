"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/context/AuthContext"
import { createClient } from "@supabase/supabase-js"
import { useRouter } from "next/navigation"
import { 
  Bar<PERSON>hart3, 
  PieChart, 
  Activity, 
  Trophy, 
  Star, 
  Calendar,
  ArrowLeft, 
  Loader2 
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

export default function DoctorStatsPage() {
  const { isAuthenticated, user: authUser, isLoading: authIsLoading } = useAuth()
  const [dataLoading, setDataLoading] = useState(true)
  const [doctorProfile, setDoctorProfile] = useState<any>(null)
  const [performanceStats, setPerformanceStats] = useState<any>({
    wins: 0,
    losses: 0,
    draws: 0,
    rating: 0,
    reviewCount: 0
  })
  const [ratingDistribution, setRatingDistribution] = useState<any>({
    oneStar: 0,
    twoStar: 0,
    threeStar: 0,
    fourStar: 0,
    fiveStar: 0
  })
  const router = useRouter()

  const createServiceRoleClient = () => {
    const supabaseUrl = "https://uapbzzscckhtptliynyj.supabase.co"
    const supabaseServiceKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q"
    return createClient(supabaseUrl, supabaseServiceKey)
  }

  useEffect(() => {
    if (authIsLoading) return
    
    if (!isAuthenticated || !authUser) {
      router.push("/")
      return
    }
    
    if (authUser.userType !== 'doctor') {
      router.push("/")
      return
    }
    
    const fetchDoctorStats = async () => {
      setDataLoading(true)
      try {
        const serviceClient = createServiceRoleClient()
        
        let doctorData
        
        // Try to find doctor data by all possible methods
        if (authUser.userId) {
          const { data, error } = await serviceClient
            .from('doctors')
            .select('*')
            .eq('auth_id', authUser.userId)
            .single()
            
          if (!error && data) {
            doctorData = data
          } else {
            const { data: doctorIdData, error: doctorIdError } = await serviceClient
              .from('doctors')
              .select('*')
              .eq('doctor_id', authUser.userId)
              .single()
              
            if (!doctorIdError && doctorIdData) {
              doctorData = doctorIdData
            }
          }
        }
        
        if (!doctorData && authUser.email) {
          const { data, error } = await serviceClient
            .from('doctors')
            .select('*')
            .eq('email', authUser.email)
            .single()
            
          if (!error && data) {
            doctorData = data
          }
        }
        
        if (doctorData) {
          setDoctorProfile(doctorData)
          
          // Set performance stats
          setPerformanceStats({
            wins: doctorData.wins || 0,
            losses: doctorData.losses || 0,
            draws: doctorData.draws || 0,
            rating: doctorData.rating || 0,
            reviewCount: doctorData.review_count || 0,
          })
          
          // Get rating distribution
          try {
            const { data: reviewsData, error: reviewsError } = await serviceClient
              .from('reviews')
              .select('rating')
              .eq('doctor_id', doctorData.doctor_id)
              
            if (!reviewsError && reviewsData) {
              const distribution = {
                oneStar: 0,
                twoStar: 0,
                threeStar: 0,
                fourStar: 0,
                fiveStar: 0
              }
              
              reviewsData.forEach(review => {
                const rating = Math.round(review.rating || 0)
                switch(rating) {
                  case 1: distribution.oneStar++; break
                  case 2: distribution.twoStar++; break
                  case 3: distribution.threeStar++; break
                  case 4: distribution.fourStar++; break
                  case 5: distribution.fiveStar++; break
                }
              })
              
              setRatingDistribution(distribution)
            }
          } catch (err) {
            console.error("Error fetching reviews for rating distribution:", err)
          }
        }
      } catch (err) {
        console.error("Error fetching doctor stats:", err)
      } finally {
        setDataLoading(false)
      }
    }
    
    fetchDoctorStats()
  }, [authIsLoading, isAuthenticated, authUser, router])
  
  if (authIsLoading || dataLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-background to-background">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
          <p className="mt-4 text-lg text-foreground/70">Loading statistics...</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-background pb-12">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary/20 to-primary/10 border-b border-primary/20">
        <div className="container mx-auto py-6 px-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button 
                variant="ghost" 
                className="p-2 text-foreground" 
                onClick={() => router.push("/doctor/dashboard")}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <h1 className="text-2xl font-bold text-foreground">
                Detailed Statistics
              </h1>
            </div>
          </div>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Performance Summary */}
          <Card className="bg-gradient-to-b from-background/90 to-background border-border md:col-span-3">
            <CardHeader>
              <CardTitle className="text-xl text-foreground">Performance Overview</CardTitle>
              <CardDescription className="text-foreground/60">
                Your statistics and competitive standing
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 md:gap-8">
                <div className="bg-green-900/20 p-4 rounded-lg border border-green-700/20 flex flex-col items-center">
                  <span className="text-foreground/70 text-sm">Wins</span>
                  <span className="text-3xl font-bold text-green-500 mt-1">{performanceStats.wins}</span>
                  <Badge className="mt-2 bg-green-500/20 text-green-300 hover:bg-green-500/30 border-0">
                    Success Rate: {performanceStats.wins + performanceStats.losses + performanceStats.draws > 0 
                      ? Math.round((performanceStats.wins / (performanceStats.wins + performanceStats.losses + performanceStats.draws)) * 100) 
                      : 0}%
                  </Badge>
                </div>
                
                <div className="bg-red-900/20 p-4 rounded-lg border border-red-700/20 flex flex-col items-center">
                  <span className="text-foreground/70 text-sm">Losses</span>
                  <span className="text-3xl font-bold text-red-500 mt-1">{performanceStats.losses}</span>
                  <Badge className="mt-2 bg-red-500/20 text-red-300 hover:bg-red-500/30 border-0">
                    Loss Rate: {performanceStats.wins + performanceStats.losses + performanceStats.draws > 0 
                      ? Math.round((performanceStats.losses / (performanceStats.wins + performanceStats.losses + performanceStats.draws)) * 100) 
                      : 0}%
                  </Badge>
                </div>
                
                <div className="bg-blue-900/20 p-4 rounded-lg border border-blue-700/20 flex flex-col items-center">
                  <span className="text-foreground/70 text-sm">Draws</span>
                  <span className="text-3xl font-bold text-blue-500 mt-1">{performanceStats.draws}</span>
                  <Badge className="mt-2 bg-blue-500/20 text-blue-300 hover:bg-blue-500/30 border-0">
                    Draw Rate: {performanceStats.wins + performanceStats.losses + performanceStats.draws > 0 
                      ? Math.round((performanceStats.draws / (performanceStats.wins + performanceStats.losses + performanceStats.draws)) * 100) 
                      : 0}%
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Rating Stats */}
          <Card className="bg-gradient-to-b from-background/90 to-background border-border md:col-span-2 row-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg text-foreground">Rating Distribution</CardTitle>
                <Star className="h-5 w-5 text-amber-400" />
              </div>
              <CardDescription className="text-foreground/60">
                Breakdown of your patient ratings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm flex items-center gap-1 text-foreground/80">
                      <span>5 Star</span>
                      <Star className="h-3 w-3 text-amber-400 fill-amber-400" />
                    </span>
                    <span className="text-sm font-medium text-foreground">{ratingDistribution.fiveStar}</span>
                  </div>
                  <Progress 
                    value={performanceStats.reviewCount > 0 ? (ratingDistribution.fiveStar / performanceStats.reviewCount) * 100 : 0} 
                    className="h-2 bg-background/80 [&>div]:bg-amber-400" 
                  />
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm flex items-center gap-1 text-foreground/80">
                      <span>4 Star</span>
                      <Star className="h-3 w-3 text-amber-400 fill-amber-400" />
                    </span>
                    <span className="text-sm font-medium text-foreground">{ratingDistribution.fourStar}</span>
                  </div>
                  <Progress 
                    value={performanceStats.reviewCount > 0 ? (ratingDistribution.fourStar / performanceStats.reviewCount) * 100 : 0} 
                    className="h-2 bg-background/80 [&>div]:bg-amber-400/80" 
                  />
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm flex items-center gap-1 text-foreground/80">
                      <span>3 Star</span>
                      <Star className="h-3 w-3 text-amber-400 fill-amber-400" />
                    </span>
                    <span className="text-sm font-medium text-foreground">{ratingDistribution.threeStar}</span>
                  </div>
                  <Progress 
                    value={performanceStats.reviewCount > 0 ? (ratingDistribution.threeStar / performanceStats.reviewCount) * 100 : 0} 
                    className="h-2 bg-background/80 [&>div]:bg-amber-400/60" 
                  />
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm flex items-center gap-1 text-foreground/80">
                      <span>2 Star</span>
                      <Star className="h-3 w-3 text-amber-400 fill-amber-400" />
                    </span>
                    <span className="text-sm font-medium text-foreground">{ratingDistribution.twoStar}</span>
                  </div>
                  <Progress 
                    value={performanceStats.reviewCount > 0 ? (ratingDistribution.twoStar / performanceStats.reviewCount) * 100 : 0} 
                    className="h-2 bg-background/80 [&>div]:bg-amber-400/40" 
                  />
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm flex items-center gap-1 text-foreground/80">
                      <span>1 Star</span>
                      <Star className="h-3 w-3 text-amber-400 fill-amber-400" />
                    </span>
                    <span className="text-sm font-medium text-foreground">{ratingDistribution.oneStar}</span>
                  </div>
                  <Progress 
                    value={performanceStats.reviewCount > 0 ? (ratingDistribution.oneStar / performanceStats.reviewCount) * 100 : 0} 
                    className="h-2 bg-background/80 [&>div]:bg-amber-400/20" 
                  />
                </div>
                
                <div className="border-t border-border pt-4 mt-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-foreground/80">Average Rating</span>
                    <div className="flex items-center gap-1">
                      <span className="text-lg font-bold text-foreground">{performanceStats.rating?.toFixed(1) || 0}</span>
                      <Star className="h-4 w-4 text-amber-400 fill-amber-400" />
                    </div>
                  </div>
                  <div className="flex justify-between mt-2">
                    <span className="text-sm text-foreground/80">Total Reviews</span>
                    <span className="text-lg font-bold text-foreground">{performanceStats.reviewCount}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* League Position */}
          <Card className="bg-gradient-to-b from-background/90 to-background border-border row-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg text-foreground">League Standing</CardTitle>
                <Trophy className="h-5 w-5 text-amber-400" />
              </div>
              <CardDescription className="text-foreground/60">
                Your position in the league
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-primary/10 p-4 rounded-lg border border-primary/20">
                  <div className="flex flex-col items-center text-center gap-2">
                    <div className="text-3xl font-bold text-primary">
                      {performanceStats.wins * 3 + performanceStats.draws * 1}
                    </div>
                    <div className="text-sm text-foreground/60">Total Points</div>
                    <div className="mt-2 text-xs text-foreground/40">
                      (Wins: 3 points, Draws: 1 point)
                    </div>
                  </div>
                </div>
                
                <Button 
                  variant="outline" 
                  className="w-full border-border text-foreground hover:bg-background/80"
                  onClick={() => router.push("/leagues")}
                >
                  View League Details
                </Button>
              </div>
            </CardContent>
          </Card>
          
          {/* More Stats Cards can be added here as needed */}
        </div>
      </div>
    </div>
  )
} 