"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { 
  Star, 
  MapPin, 
  Phone, 
  Mail, 
  Globe, 
  Award, 
  BookOpen, 
  Clock, 
  Trophy, 
  Medal, 
  ShieldCheck,
  GraduationCap,
  Stethoscope,
  HeartPulse,
  Users,
  ThumbsUp,
  Flame,
  ArrowUpRight,
  Activity
} from "lucide-react"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { ChooseRoleLoginDialog } from "@/components/login/choose-role-login-dialog"
import { useState, useEffect } from "react"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { Doctor<PERSON>rofileAds } from "./page-ads"
import { getImageUrl } from "@/lib/utils"
import { useAuth } from "@/context/AuthContext"

interface Doctor {
  doctor_id: string
  fullname: string
  hospital: string
  medical_title: string
  specialty: string
  subspecialty?: string
  educational_background: string
  board_certifications?: string
  experience: number
  publications?: string
  awards_recognitions?: string
  phone_number: string
  email: string
  languages_spoken: string
  professional_affiliations?: string
  procedures_performed?: string
  treatment_services_expertise?: string
  hospital_id?: string
  country_id?: string
  profile_image?: string
  wins?: number
  losses?: number
  draws?: number
  form?: string
  rating?: number
  review_count?: number
  last_updated?: string
  personal_biography?: string
  work_history?: any
  timings?: any
  verified_rating?: number | null
  community_rating?: number | null
  ranking_score?: number
  hospitals?: {
    hospital_id: string
    hospital_name: string
    city: string
    address: string
  }
  countries?: {
    country_id: string
    country_name: string
  }
}

interface DoctorProfileProps {
  doctor: Doctor
}

const DataNotAvailable = () => (
  <div className="text-center py-4 text-foreground/60">
    Data is not available yet. Check soon for further data.
  </div>
);


// Helper function to render form indicators (similar to sports results)
const renderFormIndicator = (type: 'win' | 'loss' | 'draw', count: number) => {
  const maxToShow = 5;
  const indicators = [];
  
  for (let i = 0; i < Math.min(count, maxToShow); i++) {
    if (type === 'win') {
      indicators.push(
        <div key={`win-${i}`} className="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center text-foreground text-xs font-bold">
          W
        </div>
      );
    } else if (type === 'loss') {
      indicators.push(
        <div key={`loss-${i}`} className="w-6 h-6 rounded-full bg-red-500 flex items-center justify-center text-foreground text-xs font-bold">
          L
        </div>
      );
    } else {
      indicators.push(
        <div key={`draw-${i}`} className="w-6 h-6 rounded-full bg-yellow-500 flex items-center justify-center text-foreground text-xs font-bold">
          D
        </div>
      );
    }
  }
  
  return indicators;
};

export function DoctorProfile({ doctor }: DoctorProfileProps) {
  const router = useRouter()
  const [showLoginDialog, setShowLoginDialog] = useState(false)
  const [hasSidebarAds, setHasSidebarAds] = useState(false)
  const supabase = createClientComponentClient()

  // Use the same authentication context as the rating form
  const { isAuthenticated, user: authUser, isLoading: authIsLoading } = useAuth()



  // Debug authentication state on every render
  useEffect(() => {
    console.log("🔍 === DOCTOR PROFILE AUTH STATE DEBUG ===")
    console.log("🏥 Doctor ID:", doctor.doctor_id)
    console.log("🔐 isAuthenticated:", isAuthenticated)
    console.log("⏳ authIsLoading:", authIsLoading)
    console.log("👤 authUser:", authUser)
    console.log("💾 localStorage jwtToken:", typeof window !== 'undefined' && localStorage.getItem('jwtToken') ? 'exists' : 'missing')
    console.log("💾 localStorage userData:", typeof window !== 'undefined' && localStorage.getItem('userData') ? 'exists' : 'missing')
    console.log("🔍 === END DEBUG ===")
  }, [isAuthenticated, authIsLoading, authUser, doctor.doctor_id])

  // Calculate win rate and career stats
  const totalMatches = (doctor.wins || 0) + (doctor.losses || 0) + (doctor.draws || 0);
  const winRate = totalMatches > 0 ? Math.round(((doctor.wins || 0) / totalMatches) * 100) : 0;
  


  const handleViewReviews = () => {
    router.push(`/doctors/${doctor.doctor_id}/reviews`)
  }

  const handleRateDoctor = () => {
    console.log("🎯 === RATE DOCTOR BUTTON CLICKED ===")
    console.log("🏥 Doctor ID:", doctor.doctor_id)
    console.log("🔐 isAuthenticated:", isAuthenticated)
    console.log("⏳ authIsLoading:", authIsLoading)
    console.log("👤 authUser:", authUser ? `${authUser.userType} - ${authUser.email}` : "null")

    // If auth is still loading, wait a moment
    if (authIsLoading) {
      console.log("Auth is still loading, please wait...")
      return
    }

    // Primary check: Use auth context
    if (isAuthenticated && authUser) {
      console.log("User is authenticated via auth context, redirecting to rating page")
      console.log("Redirecting to:", `/doctors/${doctor.doctor_id}/rate`)
      router.push(`/doctors/${doctor.doctor_id}/rate`)
      return
    }

    // Fallback check: Check localStorage directly (in case auth context is not synced)
    try {
      const token = localStorage.getItem('jwtToken')
      const userData = localStorage.getItem('userData')

      if (token && userData) {
        const parsedUserData = JSON.parse(userData)
        console.log("User is authenticated via localStorage fallback, redirecting to rating page")
        console.log("User data from localStorage:", parsedUserData.userType, parsedUserData.email)
        router.push(`/doctors/${doctor.doctor_id}/rate`)
        return
      }
    } catch (error) {
      console.error("Error checking localStorage:", error)
    }

    // User is not logged in, show login dialog
    console.log("User is not authenticated, showing login dialog")
    setShowLoginDialog(true)
  }

  const handleLoginSuccess = (role: "patient" | "doctor") => {
    // Only patients can rate doctors, redirect to rating page after successful login
    if (role === "patient") {
      router.push(`/doctors/${doctor.doctor_id}/rate`)
    }
  }

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Banner Ad */}
      <div className="mb-8">
        <DoctorProfileAds variant="banner" onAdsLoaded={() => {}} />
      </div>
      
      <div className={`grid grid-cols-1 ${hasSidebarAds ? 'lg:grid-cols-3 gap-8' : 'lg:grid-cols-1 max-w-5xl mx-auto'}`}>
        {/* Main content column */}
        <div className="lg:col-span-2 space-y-6">
          {/* Hero Card */}
          <Card className="overflow-hidden form-friendly shadow-lg">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent z-0"></div>
              <CardContent className="p-6 relative z-10">
                <div className="flex flex-col md:flex-row gap-6">
                  <div className="relative shrink-0 mx-auto md:mx-0">
                    <div className="w-40 h-40 rounded-lg overflow-hidden bg-white border-2 border-primary/30 shadow-[0_0_15px_rgba(0,128,0,0.2)] relative z-10">
                      <Image
                        src={getImageUrl(doctor.profile_image)}
                        alt={doctor.fullname}
                        fill
                        className="object-contain"
                        onError={(e) => {
                          const target = e.currentTarget as HTMLImageElement;
                          console.error(`Failed to load image for doctor ${doctor.doctor_id}: ${target.src}`);
                          // Set to a local fallback
                          target.src = "/placeholder.svg?height=128&width=128";
                          // Prevent infinite error loops
                          target.onerror = null;
                        }}
                      />
                    </div>
                    
                    {/* Doctor ranking badge */}
                    {(doctor.wins || 0) > 0 && (
                      <div className="absolute -bottom-3 -right-3 bg-gradient-to-r from-yellow-500 to-amber-500 rounded-full w-12 h-12 flex items-center justify-center shadow-lg z-20 border-2 border-white">
                        <Trophy className="h-6 w-6 text-foreground" />
                      </div>
                    )}
                  </div>

                  <div className="flex-1">
                    <div className="flex flex-wrap items-center gap-2 mb-2">
                      <Badge className="bg-primary text-foreground">{doctor.medical_title}</Badge>
                      <Badge variant="outline" className="bg-primary/10 text-foreground border-primary/30">
                        {doctor.specialty}
                      </Badge>
                      {doctor.subspecialty && (
                        <Badge variant="outline" className="bg-background/50 text-foreground border-white/20">
                          {doctor.subspecialty}
                        </Badge>
                      )}
                    </div>
                    
                    <h1 className="text-2xl md:text-3xl font-bold text-foreground mb-1">{doctor.fullname}</h1>
                    
                    {/* Dual Rating System */}
                    <div className="flex flex-col gap-3 mb-4">
                      {/* Verified Rating */}
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center gap-3 p-4 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800/30 rounded-lg">
                              <ShieldCheck className="h-6 w-6 text-green-500" />
                              <div className="flex flex-col">
                                <span className="text-2xl font-bold text-green-800 dark:text-green-200">
                                  {doctor.verified_rating?.toFixed(1) || "No Verified Rating Yet"}
                                </span>
                                <span className="text-sm font-medium text-green-700 dark:text-green-300">
                                  Verified Rating
                                </span>
                              </div>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent className="max-w-xs">
                            <p>This rating is calculated exclusively from reviews submitted by patients whose visits have been confirmed by our team.</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      {/* Community Pulse */}
                      <div className="flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-700/30 rounded-lg">
                        <div className="flex items-center justify-center w-8 h-8 bg-gray-100 dark:bg-gray-800/30 rounded-full">
                          <Users className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="text-lg font-semibold text-gray-700 dark:text-gray-300">
                              {doctor.community_rating?.toFixed(1) || "0.0"}
                            </span>
                            <div className="flex">
                              {Array.from({ length: 5 }).map((_, i) => (
                                <Star
                                  key={i}
                                  className={`h-3 w-3 ${i < Math.floor(doctor.community_rating || 0) ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
                                />
                              ))}
                            </div>
                          </div>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            Community Pulse (All Reviews)
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-y-2 gap-x-4 text-foreground/80">
                      <div className="flex items-start gap-2">
                        <MapPin className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                        <div>
                          <p className="text-foreground">{doctor.hospitals?.hospital_name || doctor.hospital}</p>
                          <p className="text-sm text-foreground/60">
                            {[doctor.hospitals?.address, doctor.hospitals?.city].filter(Boolean).join(", ")}
                          </p>
                        </div>
                      </div>

                      {doctor.countries?.country_name && (
                        <div className="flex items-center gap-2">
                          <Globe className="h-5 w-5 text-primary" />
                          <p>{doctor.countries.country_name}</p>
                        </div>
                      )}

                      {doctor.phone_number && (
                        <div className="flex items-center gap-2">
                          <Phone className="h-5 w-5 text-primary" />
                          <p>{doctor.phone_number}</p>
                        </div>
                      )}

                      {doctor.email && (
                        <div className="flex items-center gap-2">
                          <Mail className="h-5 w-5 text-primary" />
                          <p>{doctor.email}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Career Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
                  <Card className="bg-background/50 border border-green-500/30 overflow-hidden">
                    <CardContent className="py-8 px-6 flex flex-col items-center justify-center" style={{ minHeight: '175px' }}>
                      <Trophy className="h-8 w-8 text-green-500 mb-2" />
                      <p className="text-xl font-bold text-foreground">{doctor.wins || 0}</p>
                      <p className="text-sm text-foreground/70 mt-1">Wins</p>
                    </CardContent>
                  </Card>
                  
                  <Card className="bg-background/50 border border-red-500/30 overflow-hidden">
                    <CardContent className="py-8 px-6 flex flex-col items-center justify-center" style={{ minHeight: '175px' }}>
                      <ThumbsUp className="h-8 w-8 text-red-500 mb-2 transform rotate-180" />
                      <p className="text-xl font-bold text-foreground">{doctor.losses || 0}</p>
                      <p className="text-sm text-foreground/70 mt-1">Losses</p>
                    </CardContent>
                  </Card>
                  
                  <Card className="bg-background/50 border border-yellow-500/30 overflow-hidden">
                    <CardContent className="py-8 px-6 flex flex-col items-center justify-center" style={{ minHeight: '175px' }}>
                      <Activity className="h-8 w-8 text-yellow-500 mb-2" />
                      <p className="text-xl font-bold text-foreground">{doctor.draws || 0}</p>
                      <p className="text-sm text-foreground/70 mt-1">Draws</p>
                    </CardContent>
                  </Card>
                  
                  <Card className="bg-background/50 border border-blue-500/30 overflow-hidden">
                    <CardContent className="py-8 px-6 flex flex-col items-center justify-center" style={{ minHeight: '175px' }}>
                      <Flame className="h-8 w-8 text-blue-500 mb-2" />
                      <p className="text-xl font-bold text-foreground">{winRate}%</p>
                      <p className="text-sm text-foreground/70 mt-1">Win Rate</p>
                    </CardContent>
                  </Card>
                </div>
                
                {/* Recent Form */}
                {totalMatches > 0 && (
                  <div className="mt-6">
                    <h3 className="text-sm font-medium text-foreground/70 mb-2">Recent Form</h3>
                    <div className="flex gap-1">
                      {renderFormIndicator('win', doctor.wins || 0)}
                      {renderFormIndicator('loss', doctor.losses || 0)}
                      {renderFormIndicator('draw', doctor.draws || 0)}
                    </div>
                  </div>
                )}
                
                {/* Action Buttons */}
                <div className="mt-6 flex flex-col sm:flex-row gap-4">
                  <Button
                    onClick={handleViewReviews}
                    className="flex-1 bg-gradient-to-r from-primary to-primary/80 hover:from-primary hover:to-primary/90 text-foreground shadow-md hover:shadow-[0_0_15px_rgba(0,128,0,0.3)] transition-all duration-300 border border-primary/20 rounded-md group transform hover:scale-[1.02]"
                  >
                    <BookOpen className="mr-2 h-5 w-5" />
                    <span>View All Reviews</span>
                  </Button>
                  <Button
                    onClick={handleRateDoctor}
                    className="flex-1 bg-gradient-to-r from-yellow-500 to-amber-500 hover:from-yellow-500/90 hover:to-amber-500/90 text-foreground shadow-md hover:shadow-[0_0_15px_rgba(255,200,0,0.3)] transition-all duration-300 border border-yellow-500/20 rounded-md group transform hover:scale-[1.02]"
                  >
                    <Star className="mr-2 h-5 w-5" />
                    <span>Rate This Doctor</span>
                  </Button>
                </div>
              </CardContent>
            </div>
          </Card>

          {/* Expert Skills Section */}
          <Card className="border border-primary/20 bg-gradient-to-b from-background/70 to-background/90 shadow-md overflow-hidden">
            <CardHeader className="pb-2">
              <CardTitle className="text-xl text-foreground flex items-center gap-2">
                <Stethoscope className="h-5 w-5 text-primary" />
                Professional Experience
              </CardTitle>
              <CardDescription className="text-foreground/70">
                {doctor.experience} years of specialized medical practice
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-foreground font-medium mb-2 flex items-center gap-2">
                    <GraduationCap className="h-4 w-4 text-primary" />
                    Education & Training
                  </h3>
                  <p className="text-foreground/70 text-sm">{doctor.educational_background}</p>
                  
                  {doctor.board_certifications && (
                    <div className="mt-4">
                      <h4 className="text-foreground font-medium mb-2 flex items-center gap-2">
                        <ShieldCheck className="h-4 w-4 text-primary" />
                        Board Certifications
                      </h4>
                      <p className="text-foreground/70 text-sm">{doctor.board_certifications}</p>
                    </div>
                  )}
                </div>
                
                <div>
                  {doctor.professional_affiliations && (
                    <div>
                      <h3 className="text-foreground font-medium mb-2 flex items-center gap-2">
                        <Users className="h-4 w-4 text-primary" />
                        Professional Affiliations
                      </h3>
                      <p className="text-foreground/70 text-sm">{doctor.professional_affiliations}</p>
                    </div>
                  )}
                  
                  {doctor.languages_spoken && (
                    <div className="mt-4">
                      <h4 className="text-foreground font-medium mb-2 flex items-center gap-2">
                        <Globe className="h-4 w-4 text-primary" />
                        Languages Spoken
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {doctor.languages_spoken.split(",").map((lang, index) => (
                          <Badge key={index} variant="outline" className="bg-background/50 text-foreground border-primary/30">
                            {lang.trim()}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tabs Section */}
          <Tabs defaultValue="biography" className="w-full">
            <TabsList className="h-auto flex flex-wrap justify-start">
              <TabsTrigger value="biography">Personal Biography</TabsTrigger>
              <TabsTrigger value="credentials">Credentials</TabsTrigger>
              <TabsTrigger value="expertise">Expertise</TabsTrigger>
              <TabsTrigger value="work_history">Work History</TabsTrigger>
              <TabsTrigger value="timings">Timings</TabsTrigger>
              <TabsTrigger value="publications">Publications</TabsTrigger>
              <TabsTrigger value="awards">Awards</TabsTrigger>
              <TabsTrigger value="affiliations">Affiliations</TabsTrigger>
              <TabsTrigger value="languages">Languages</TabsTrigger>
            </TabsList>

            <TabsContent value="biography">
              <Card>
                <CardHeader>
                  <CardTitle>Personal Biography</CardTitle>
                </CardHeader>
                <CardContent>
                  {doctor.personal_biography ? (
                    <p className="text-foreground/80 whitespace-pre-line">{doctor.personal_biography}</p>
                  ) : (
                    <DataNotAvailable />
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="credentials">
              <Card>
                <CardHeader>
                  <CardTitle>Credentials</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">Education</h3>
                    <p className="text-foreground/80">{doctor.educational_background || "Information not available"}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">Board Certifications</h3>
                    <p className="text-foreground/80">{doctor.board_certifications || "Information not available"}</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="expertise">
              <Card>
                <CardHeader>
                  <CardTitle>Areas of Expertise</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">Procedures Performed</h3>
                    <p className="text-foreground/80">{doctor.procedures_performed || "Information not available"}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">Treatment & Services Expertise</h3>
                    <p className="text-foreground/80">{doctor.treatment_services_expertise || "Information not available"}</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="work_history">
              <Card>
                <CardHeader>
                  <CardTitle>Work History</CardTitle>
                </CardHeader>
                <CardContent>
                  {doctor.work_history ? (
                    <div className="text-foreground/80 whitespace-pre-line">{doctor.work_history}</div>
                  ) : (
                    <DataNotAvailable />
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="publications">
              <Card>
                <CardHeader>
                  <CardTitle>Publications</CardTitle>
                </CardHeader>
                <CardContent>
                  {doctor.publications ? (
                    <div className="text-foreground/80 whitespace-pre-line">{doctor.publications}</div>
                  ) : (
                    <DataNotAvailable />
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="awards">
              <Card>
                <CardHeader>
                  <CardTitle>Awards & Recognitions</CardTitle>
                </CardHeader>
                <CardContent>
                  {doctor.awards_recognitions ? (
                    <div className="text-foreground/80 whitespace-pre-line">{doctor.awards_recognitions}</div>
                  ) : (
                    <DataNotAvailable />
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="affiliations">
              <Card>
                <CardHeader>
                  <CardTitle>Professional Affiliations</CardTitle>
                </CardHeader>
                <CardContent>
                  {doctor.professional_affiliations ? (
                    <div className="text-foreground/80 whitespace-pre-line">{doctor.professional_affiliations}</div>
                  ) : (
                    <DataNotAvailable />
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="languages">
              <Card>
                <CardHeader>
                  <CardTitle>Spoken Languages</CardTitle>
                </CardHeader>
                <CardContent>
                  {doctor.languages_spoken ? (
                    <div className="text-foreground/80">{doctor.languages_spoken}</div>
                  ) : (
                    <DataNotAvailable />
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="timings">
              <Card>
                <CardHeader>
                  <CardTitle>Timings</CardTitle>
                </CardHeader>
                <CardContent>
                  {doctor.timings ? (
                    <div className="text-foreground/80 whitespace-pre-line">{doctor.timings}</div>
                  ) : (
                    <DataNotAvailable />
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="sticky top-24 space-y-6">
            {/* Sidebar Ad */}
            <DoctorProfileAds
              variant="sidebar"
              onAdsLoaded={(hasAds) => setHasSidebarAds(hasAds)}
            />
            
            {/* Experience & Reputation Card */}
            <Card className="border border-primary/20 bg-gradient-to-b from-background/70 to-background/90 shadow-md overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg text-foreground">Experience & Reputation</CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-6">
                {/* Years Experience */}
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm text-foreground">Years Experience</span>
                    <span className="text-sm font-medium text-foreground">{doctor.experience}</span>
                  </div>
                  <Progress value={Math.min(doctor.experience / 30 * 100, 100)} className="h-2 bg-primary/20" />
                </div>
                
                {/* Verified Rating */}
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm text-foreground">Verified Rating</span>
                    <span className="text-sm font-medium text-foreground">{doctor.verified_rating?.toFixed(1) || "N/A"}/5.0</span>
                  </div>
                  <Progress value={((doctor.verified_rating || 0) / 5) * 100} className="h-2 bg-green-500/20" />
                </div>

                {/* Community Rating */}
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm text-foreground">Community Pulse</span>
                    <span className="text-sm font-medium text-foreground">{doctor.community_rating?.toFixed(1) || "0.0"}/5.0</span>
                  </div>
                  <Progress value={((doctor.community_rating || 0) / 5) * 100} className="h-2 bg-primary/20" />
                </div>
                
                {/* Win Rate */}
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm text-foreground">Win Rate</span>
                    <span className="text-sm font-medium text-foreground">{winRate}%</span>
                  </div>
                  <Progress value={winRate} className="h-2 bg-primary/20" />
                </div>
                
                {/* Activity Level */}
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm text-foreground">Activity Level</span>
                    <span className="text-sm font-medium text-foreground">
                      {totalMatches > 10 ? 'Very Active' : totalMatches > 5 ? 'Active' : totalMatches > 0 ? 'Moderate' : 'New'}
                    </span>
                  </div>
                  <Progress 
                    value={totalMatches > 15 ? 100 : totalMatches > 10 ? 75 : totalMatches > 5 ? 50 : totalMatches > 0 ? 25 : 10} 
                    className="h-2 bg-primary/20" 
                  />
                </div>
              </CardContent>
            </Card>
            
            {/* CTA Card */}
            <Card className="border border-yellow-500/30 bg-gradient-to-b from-background/70 to-background/90 shadow-md overflow-hidden">
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="h-10 w-10 rounded-full bg-yellow-500/20 flex items-center justify-center">
                    <Trophy className="h-5 w-5 text-yellow-500" />
                  </div>
                  <div>
                    <h3 className="text-foreground font-medium">Rate This Doctor</h3>
                    <p className="text-foreground/60 text-sm">Help others with your experience</p>
                  </div>
                </div>
                
                <Button 
                  onClick={handleRateDoctor}
                  className="w-full bg-gradient-to-r from-yellow-500 to-amber-500 hover:from-yellow-500/90 hover:to-amber-500/90 text-foreground"
                >
                  Rate & Review
                  <ArrowUpRight className="ml-2 h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Login dialog for non-logged-in users */}
      <ChooseRoleLoginDialog
        open={showLoginDialog}
        onOpenChange={setShowLoginDialog}
        onLoginSuccess={handleLoginSuccess}
        redirectUrl={`/doctors/${doctor.doctor_id}/rate`}
      />
    </div>
  )
}

