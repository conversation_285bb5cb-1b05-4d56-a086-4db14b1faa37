"use client"

import { PageAds } from "@/components/ads/page-ads"
import { useEffect, useState } from "react"
import { getAdFetchFunction } from "@/components/ads/ad-helpers"

interface DoctorProfileAdsProps {
  variant: 'banner' | 'sidebar' | 'in-content'
  onAdsLoaded?: (hasAds: boolean) => void
}

export function DoctorProfileAds({ variant, onAdsLoaded }: DoctorProfileAdsProps) {
  const [hasAds, setHasAds] = useState(false)

  // Map variant to position
  const getPosition = () => {
    switch (variant) {
      case 'banner':
        return 'banner'
      case 'sidebar':
        return 'sidebar'
      case 'in-content':
        return 'in-content'
      default:
        return 'banner'
    }
  }

  // Check if ads are available for this position
  useEffect(() => {
    const position = getPosition()
    const fetchFunction = getAdFetchFunction('doctor-profile', position)

    if (fetchFunction) {
      const checkAds = async () => {
        try {
          const result = await fetchFunction()

          // Handle different response formats safely
          let data
          if (result && typeof result === 'object') {
            if ('data' in result) {
              data = result.data
            } else if (Array.isArray(result)) {
              data = result
            } else {
              data = result
            }
          } else {
            data = result || []
          }

          const hasAdsAvailable = !!data && data.length > 0
          setHasAds(hasAdsAvailable)

          if (onAdsLoaded) {
            onAdsLoaded(hasAdsAvailable)
          }
        } catch (error) {
          console.error(`Error checking ads for doctor-profile ${position}:`, error)
          setHasAds(false)
          if (onAdsLoaded) {
            onAdsLoaded(false)
          }
        }
      }

      checkAds()
    } else {
      setHasAds(false)
      if (onAdsLoaded) {
        onAdsLoaded(false)
      }
    }
  }, [variant, onAdsLoaded])

  return (
    <PageAds
      pageName="doctor-profile"
      positions={[getPosition()]}
      showTestAds={false}
    />
  )
}
