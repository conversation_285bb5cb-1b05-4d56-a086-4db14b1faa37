import { getDoctorById, getCountries } from "@/lib/hybrid-data-service"
import { DoctorPro<PERSON>le } from "./doctor-profile"
import { notFound } from "next/navigation"
import { Metadata } from "next"
import { Doctor<PERSON>oc<PERSON><PERSON>etaTag<PERSON>, DoctorCountryMetadata } from "@/components/seo"
import { SchemaMarkup, generateDoctorSchema } from "@/components/seo"
import ScrollToTop from "@/components/scroll-to-top"

interface Doctor<PERSON><PERSON><PERSON><PERSON><PERSON> extends Omit<ReturnType<typeof getDoctorById> extends Promise<infer T> ? T : never, 'hospitals'> {
  hospitals?: {
    hospital_id: string;
    hospital_name: string;
    city: string;
    address: string;
  };
}

export async function generateMetadata({
  params
}: {
  params: Promise<{ id: string }>
}): Promise<Metadata> {
  const { id } = await params
  const doctor = await getDoctorById(id)
  
  if (!doctor) {
    return {
      title: "Doctor Not Found",
      description: "The doctor profile you are looking for could not be found."
    }
  }
  
  // The doctor from API may have hospitals join
  const doctor<PERSON><PERSON><PERSON>oi<PERSON> = doctor as unknown as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;
  
  return {
    title: `${doctor.fullname} - ${doctor.medical_title} | ${doctor.specialty} Specialist`,
    description: `${doctor.fullname} is a ${doctor.specialty} specialist with ${doctor.experience} years of experience at ${doctor.facility || doctorWithJoins.hospitals?.hospital_name || ''}. View profile, credentials, ratings and reviews.`,
    keywords: `${doctor.fullname}, ${doctor.specialty}, doctor, ${doctor.medical_title}, medical expert, healthcare, doctor ratings`,
  }
}

export default async function DoctorProfilePage({
  params,
  searchParams,
}: {
  params: Promise<{ id: string }>
  searchParams?: Promise<{ country?: string }>
}) {
  const { id } = await params
  const doctor = await getDoctorById(id)
  
  if (!doctor) {
    notFound()
  }
  
  // The doctor from API may have hospitals join
  const doctorWithJoins = doctor as unknown as DoctorWithJoins;
  
  // Get current country from search params or doctor's country
  const resolvedSearchParams = searchParams ? await searchParams : {}
  const currentCountry = resolvedSearchParams?.country || (doctor.country_id ? String(doctor.country_id) : "us")
  
  // Get all available countries for hreflang tags
  const countries = await getCountries()
  const countryIds = countries.map(country => String(country.country_id))
  
  // Generate schema for the doctor
  const doctorSchema = generateDoctorSchema({
    id: String(doctor.doctor_id),
    name: doctor.fullname,
    image: doctor.image_path || "/placeholder.svg",
    description: `Dr. ${doctor.fullname} is a ${doctor.medical_title} specializing in ${doctor.specialty} with ${doctor.experience} years of experience.`,
    specialty: doctor.specialty,
    hospital: doctorWithJoins.hospitals?.hospital_name || doctor.facility,
    education: doctor.educational_background ? [doctor.educational_background] : [],
    awards: doctor.awards_recognitions ? [doctor.awards_recognitions] : [],
    rating: doctor.rating || 0,
    reviewCount: doctor.review_count || 0
  })

  // Prepare full canonical URL for the doctor profile
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://doctorsleagues.com'
  const canonicalUrl = `${baseUrl}/doctors/${id}`

  return (
    <>
      {/* Country-specific metadata with hreflang tags */}
      <DoctorCountryMetadata
        doctorId={id}
        countries={countryIds}
        currentCountry={currentCountry}
      />
      
      {/* Social sharing metadata */}
      <DoctorSocialMetaTags
        doctorName={doctor.fullname}
        specialty={doctor.specialty}
        description={`${doctor.fullname} is a ${doctor.medical_title} specializing in ${doctor.specialty} with ${doctor.experience} years of experience.`}
        canonicalUrl={canonicalUrl}
        imageUrl={doctor.image_path}
        hospitalName={doctorWithJoins.hospitals?.hospital_name || doctor.facility}
      />
      
      {/* Schema markup */}
      <SchemaMarkup schema={doctorSchema} />
      
      {/* Component to ensure scrolling to top */}
      <ScrollToTop />
      
      <DoctorProfile doctor={doctor as any} />
    </>
  )
}

