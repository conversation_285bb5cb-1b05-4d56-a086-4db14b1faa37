"use client"

import { useState } from "react"
import { <PERSON>, User, <PERSON> } from "lucide-react"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChooseRoleLoginDialog } from "@/components/login/choose-role-login-dialog"
import { useRouter } from "next/navigation"

interface AuthenticationRequiredProps {
  doctorId: string
  doctorName: string
}

export default function AuthenticationRequired({ doctorId, doctorName }: AuthenticationRequiredProps) {
  const [showLoginDialog, setShowLoginDialog] = useState(false)
  const router = useRouter()

  const handleSignIn = () => {
    // Navigate to patient login page instead of showing a dialog
    router.push("/patient/login?redirectTo=" + encodeURIComponent(`/doctors/${doctorId}/rate`))
  }

  return (
    <div className="container mx-auto py-12">
      <style jsx>{`
        /* Light theme authentication card styling */
        html:not(.dark) .auth-card {
          background: linear-gradient(to bottom, rgba(240, 253, 244, 0.95), rgba(220, 252, 231, 0.95)) !important;
          border: 2px solid rgba(34, 197, 94, 0.3) !important;
          box-shadow: 0 0 15px rgba(34, 197, 94, 0.15) !important;
        }

        html:not(.dark) .auth-card .text-foreground {
          color: hsl(142, 76%, 25%) !important;
        }

        html:not(.dark) .auth-card .text-foreground\/70 {
          color: hsl(142, 76%, 35%) !important;
        }
      `}</style>
      <div className="max-w-xl mx-auto">
        <Card className="auth-card bg-gradient-to-b from-background/90 to-background border-2 border-yellow-500/30 shadow-[0_0_15px_rgba(255,204,0,0.15)] dark:bg-gradient-to-b dark:from-background/90 dark:to-background dark:border-yellow-500/30 dark:shadow-[0_0_15px_rgba(255,204,0,0.15)] light:bg-gradient-to-b light:from-green-50 light:to-green-100 light:border-green-500/30 light:shadow-[0_0_15px_rgba(34,197,94,0.15)]">
          <CardHeader className="text-center pb-2">
            <div className="flex justify-center mb-4">
              <div className="icon-bg w-16 h-16 bg-yellow-500/10 rounded-full flex items-center justify-center">
                <Trophy className="w-8 h-8 text-yellow-500 dark:text-yellow-500 light:text-green-600" />
              </div>
            </div>
            <CardTitle className="text-2xl font-bold text-foreground">Authentication Required</CardTitle>
            <CardDescription className="text-foreground/70">
              You must be signed in as a patient to rate doctors.
            </CardDescription>
          </CardHeader>
          
          <CardContent className="px-8 py-6 space-y-6">
            <div className="content-bg p-4 rounded-lg bg-gradient-to-r from-yellow-500/10 to-transparent border border-yellow-500/20">
              <h3 className="text-lg font-semibold text-foreground mb-2">Why Rate {doctorName}?</h3>
              <ul className="space-y-2 text-foreground/80">
                <li className="flex items-start gap-2">
                  <span className="icon-circle bg-yellow-500/20 p-1 rounded-full flex-shrink-0 mt-0.5">
                    <User className="h-4 w-4 text-yellow-500 dark:text-yellow-500 light:text-green-600" />
                  </span>
                  <span>Help other patients make informed decisions</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="icon-circle bg-yellow-500/20 p-1 rounded-full flex-shrink-0 mt-0.5">
                    <Trophy className="h-4 w-4 text-yellow-500 dark:text-yellow-500 light:text-green-600" />
                  </span>
                  <span>Recognize excellence in healthcare</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="icon-circle bg-yellow-500/20 p-1 rounded-full flex-shrink-0 mt-0.5">
                    <Lock className="h-4 w-4 text-yellow-500 dark:text-yellow-500 light:text-green-600" />
                  </span>
                  <span>Contribute to improving healthcare standards</span>
                </li>
              </ul>
            </div>
          </CardContent>
          
          <CardFooter className="flex justify-center pb-8 px-8">
            <Button 
              className="w-full bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 text-foreground font-medium py-6 text-lg rounded-md transition-all duration-300 transform hover:translate-y-[-2px] shadow-md hover:shadow-lg hover:shadow-green-500/30 group overflow-hidden relative"
              onClick={handleSignIn}
            >
              <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-green-400/0 via-green-400/30 to-green-400/0 transform -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></span>
              <span className="relative flex items-center justify-center gap-2">
                Sign In as Patient
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 transform group-hover:translate-x-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </span>
            </Button>
          </CardFooter>
        </Card>
      </div>
      
      {/* Login Dialog - No longer needed since we're navigating directly */}
      {/*<ChooseRoleLoginDialog 
        open={showLoginDialog} 
        onOpenChange={setShowLoginDialog}
        ratingContext="rateDoctor"
        redirectUrl={`/doctors/${doctorId}/rate`}
      />*/}
    </div>
  )
} 