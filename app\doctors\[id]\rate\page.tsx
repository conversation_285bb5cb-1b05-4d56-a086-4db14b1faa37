import { getDoctorById } from "@/lib/hybrid-data-service"
import RatingForm from "./rating-form"
import { notFound } from "next/navigation"
import { Star, Award, Users } from "lucide-react"
import Link from "next/link"

export default async function RateDoctorPage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  // Get the doctor data
  const { id } = await params
  const doctor = await getDoctorById(id)

  if (!doctor) {
    notFound()
  }

  // The RatingForm component will handle auth checks client-side
  return (
    <div className="container mx-auto py-8">
      <div className="text-center mb-12">
        <div className="inline-block p-2 bg-primary/10 rounded-xl mb-4">
          <Star className="w-8 h-8 text-yellow-500" />
        </div>
        <h1 className="text-4xl font-bold mb-3">Rate {doctor.fullname}</h1>
        <p className="text-lg text-muted-green max-w-2xl mx-auto">
          Share your experience with {doctor.fullname} and help others make informed decisions.
          Your honest feedback helps improve healthcare quality.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 mb-8">
        <div className="lg:col-span-8 lg:col-start-3">
          <div className="flex justify-center gap-12 mb-12">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 rounded-full flex items-center justify-center bg-primary/10 mb-2">
                <Star className="w-6 h-6 text-primary" />
              </div>
              <p className="font-medium">Rate Honestly</p>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 rounded-full flex items-center justify-center bg-green-500/10 mb-2">
                <Award className="w-6 h-6 text-green-500" />
              </div>
              <p className="font-medium">Recognize Excellence</p>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 rounded-full flex items-center justify-center bg-blue-500/10 mb-2">
                <Users className="w-6 h-6 text-blue-500" />
              </div>
              <p className="font-medium">Help Others</p>
            </div>
          </div>

          <RatingForm doctor={doctor} />
        </div>
      </div>
    </div>
  )
}

