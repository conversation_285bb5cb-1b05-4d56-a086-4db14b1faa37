"use client"

import { useEffect, useState } from "react"
import { createClient } from "@supabase/supabase-js"
import { toast } from "sonner"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { ReloadIcon, StarIcon } from "@radix-ui/react-icons"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Sparkles, Trophy, Star, StarHalf, Info, AlertCircle, Upload, FileImage, CheckCircle } from "lucide-react"
import { ChooseRoleLoginDialog } from "@/components/login/choose-role-login-dialog"
import { useRouter } from "next/navigation"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { submitReview } from "@/actions/review-actions"
import { useAuth } from "@/context/AuthContext"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface Doctor {
  doctor_id: string | number
  fullname: string
  specialty: string
  hospital_id?: string | number
  hospital_name?: string
  facility?: string
}

interface RatingFormProps {
  doctor: Doctor
}

export default function RatingForm({ doctor }: RatingFormProps) {
  // State for individual rating categories
  const [clinicalCompetence, setClinicalCompetence] = useState(0)
  const [communication, setCommunication] = useState(0)
  const [empathyCompassion, setEmpathyCompassion] = useState(0)
  const [timeManagement, setTimeManagement] = useState(0)
  const [followUpCare, setFollowUpCare] = useState(0)
  const [overallSatisfaction, setOverallSatisfaction] = useState(0)
  const [comments, setComments] = useState("")
  const [recommendationRating, setRecommendationRating] = useState(0)
  
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [hasExistingReview, setHasExistingReview] = useState(false)
  const [isUser, setIsUser] = useState<boolean | null>(null)
  const [showLoginDialog, setShowLoginDialog] = useState(false)
  const [userVerified, setUserVerified] = useState(false)
  const [userData, setUserData] = useState<any>(null)
  const [existingReviewData, setExistingReviewData] = useState<any>(null)
  const [authTimeout, setAuthTimeout] = useState(false)

  // Verification upload states
  const [verificationFile, setVerificationFile] = useState<File | null>(null)
  const [verificationError, setVerificationError] = useState<string | null>(null)
  const [mounted, setMounted] = useState(false)
  const router = useRouter()
  const { isAuthenticated, user: authUser, isLoading: authIsLoading } = useAuth()

  // Prevent hydration mismatch by only rendering after mount
  useEffect(() => {
    setMounted(true)
  }, [])

  // Create service role client for database operations
  const createServiceRoleClient = () => {
    // Hardcoded values for client-side use since process.env is not fully accessible in client components
    const supabaseUrl = "https://uapbzzscckhtptliynyj.supabase.co"
    
    // Direct hardcoded service role key - in production this should be handled through a server API endpoint
    const supabaseServiceKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q"
    
    return createClient(supabaseUrl, supabaseServiceKey)
  }

  // Calculate average rating
  const calculateAverageRating = () => {
    const ratings = [
      clinicalCompetence,
      communication,
      empathyCompassion,
      timeManagement,
      followUpCare,
      overallSatisfaction
    ].filter(rating => rating > 0)
    
    if (ratings.length === 0) return 0
    return ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length
  }

  // Add timeout for authentication check
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (isUser === null && !authIsLoading) {
        console.log("Authentication check timed out, assuming not authenticated")
        setAuthTimeout(true)
        setIsUser(false)
      }
    }, 10000) // 10 second timeout

    return () => clearTimeout(timeout)
  }, [isUser, authIsLoading])

  // Check if user is logged in and verify they are a patient
  useEffect(() => {
    async function checkAndVerifyUser() {
      // Reset state on each check
      setUserVerified(false)
      setUserData(null)
      setHasExistingReview(false)

      console.log("Starting user verification process with custom auth...")
      console.log("Auth state:", { isAuthenticated, authIsLoading, authUser: !!authUser })

      // Check if component is still loading auth state
      if (authIsLoading) {
        console.log("Auth is still loading, waiting...")
        return;
      }

      // Step 1: Check if user is logged in using our custom auth system
      // FIXED: More robust authentication check that handles loading states properly
      if (!isAuthenticated && !authIsLoading) {
        // Only show login dialog if we're sure the user is not authenticated and not still loading
        console.log("No active session found with custom auth - user not logged in")
        setIsUser(false)
        setShowLoginDialog(true)
        return
      }

      // If we're still loading or user data is not available yet, wait
      if (!authUser && authIsLoading) {
        console.log("Auth user data not available yet, waiting...")
        return
      }

      // If authentication is true but no user data, there might be an issue
      if (isAuthenticated && !authUser) {
        console.log("Authentication state inconsistency detected, waiting for user data...")
        return
      }

      console.log("Custom auth session found with ID:", authUser.userId)
      
      // We'll keep track of the login date but won't force re-authentication
      try {
        const now = new Date();
        const today = now.toDateString();
        
        // Just update the last login date without forcing re-authentication
        if (!localStorage.getItem('lastLoginDate')) {
          console.log("Setting initial login date");
          localStorage.setItem('lastLoginDate', today);
        }
      } catch (error) {
        console.error("Error checking session date:", error);
        // Continue with session if there's an error checking the date
      }
      
      setIsUser(true)
      
      // Step 2: Create service client to verify user in database
      const serviceClient = createServiceRoleClient()
      
      try {
        // Check if user is a patient by verifying userType
        if (authUser.userType !== 'patient') {
          console.log("User is not a patient:", authUser.userType)
          toast.error("Only patients can leave ratings.")
          return
        }
        
        // Get user_id from auth context or re-verify from database if needed
        const userId = authUser.userId;
        
        if (!userId) {
          console.error("Missing userId in auth user data")
          toast.error("Your account profile is not complete. Please update your profile before rating.")
          return
        }
        
        console.log("Retrieved user data from custom auth:", authUser)
        
        // User is verified as a patient
        setUserVerified(true)
        setUserData({
          user_id: userId,
          first_name: authUser.first_name,
          last_name: authUser.last_name,
        });
        console.log("User verified as patient with user_id:", userId)
        
        // Check if the user has already submitted a review for this doctor
        try {
          const doctorId = doctor.doctor_id.toString()
          console.log("Checking for existing review for doctor_id:", doctorId)
          
          const { data: existingReviews, error: reviewError } = await serviceClient
            .from("reviews")
            .select("*")
            .eq("user_id", userId)
            .eq("doctor_id", doctorId)
            .limit(1)
          
          if (reviewError) {
            console.error("Error checking for existing reviews:", reviewError)
            // Continue anyway, just assume no review exists
          } else if (existingReviews && existingReviews.length > 0) {
            console.log("User has already submitted a review for this doctor:", existingReviews[0])
            setExistingReviewData(existingReviews[0])
            setHasExistingReview(true)
            setIsSubmitted(true)
          } else {
            console.log("No existing review found, user can submit a new review")
          }
        } catch (reviewCheckError) {
          console.error("Exception while checking for existing review:", reviewCheckError)
          // Continue anyway, assuming no review exists
        }
      } catch (error) {
        console.error("Error during user verification:", error)
        toast.error("We encountered an issue verifying your account. Please try again later.")
      }
    }
    
    checkAndVerifyUser()
  }, [doctor.doctor_id, isAuthenticated, authUser, authIsLoading])

  // Additional safety check: if user becomes authenticated after initial load, re-verify
  useEffect(() => {
    if (isAuthenticated && authUser && !userVerified && !authIsLoading) {
      console.log("User authentication state changed, re-verifying...")
      // Don't immediately show login dialog, let the main effect handle verification
      setShowLoginDialog(false)
    }
  }, [isAuthenticated, authUser, userVerified, authIsLoading])

  const handleLoginSuccess = (role: "patient" | "doctor") => {
    // Only allow patients to rate doctors
    if (role === "patient") {
      // Refresh the page to show the rating form after successful login
      router.refresh()
    } else {
      toast.error("Only patients can rate doctors")
    }
  }

  // Handle verification file selection
  const handleVerificationFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setVerificationError('Please select an image file (JPG, PNG, WebP)')
        setVerificationFile(null)
        return
      }

      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        setVerificationError('File size must be less than 5MB')
        setVerificationFile(null)
        return
      }

      setVerificationFile(file)
      setVerificationError(null)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log("=== STARTING REVIEW SUBMISSION PROCESS ===")

    // Prevent multiple submissions
    if (isSubmitting) {
      console.log("Submission already in progress, ignoring duplicate submit")
      return
    }

    // Enforce authentication
    if (!isUser) {
      console.log("Review submission failed: User not logged in")
      toast.error("You must be logged in to submit a rating")
      setShowLoginDialog(true)
      return
    }

    // Enforce patient-only rating
    if (!userVerified || !userData) {
      console.log("Review submission failed: User not verified as a patient", { userVerified, userData })
      toast.error("Only verified patients can rate doctors")
      return
    }
    
    // Check if user has already submitted a review
    if (hasExistingReview) {
      console.log("Review submission failed: User has already submitted a review")
      toast.error("You have already submitted a review for this doctor")
      setIsSubmitted(true)
      return
    }

    // Log user data for debugging
    console.log("Verified user data:", userData)

    // Validate required fields
    const requiredRatings = [
      { value: clinicalCompetence, name: "Clinical Competence" },
      { value: communication, name: "Communication" },
      { value: empathyCompassion, name: "Empathy & Compassion" },
      { value: timeManagement, name: "Time Management" },
      { value: followUpCare, name: "Follow-up Care" },
      { value: overallSatisfaction, name: "Overall Satisfaction" }
    ]

    const missingRatings = requiredRatings.filter(rating => rating.value === 0)
    if (missingRatings.length > 0) {
      console.log("Review submission failed: Missing ratings", missingRatings)
      toast.error(`Please provide ratings for: ${missingRatings.map(r => r.name).join(", ")}`)
      return
    }

    if (recommendationRating === 0) {
      console.log("Review submission failed: Missing recommendation rating")
      toast.error("Please provide a recommendation rating")
      return
    }

    setIsSubmitting(true)
    console.log("Starting review submission with validation passed")

    try {
      // Show processing toast
      const loadingToast = toast.loading("Submitting your review...")
      
      // Create FormData object to use with server action
      const formData = new FormData()
      
      // Extract doctor ID directly from the doctor prop rather than parsing from URL
      const doctorId = doctor.doctor_id.toString()
      
      console.log("Using doctor_id from props:", doctorId)
      
      // Add user_id from the userData that was already verified during component initialization
      if (!userData || !userData.user_id) {
        toast.error("User information not available. Please try logging in again.")
        toast.dismiss(loadingToast)
        setIsSubmitting(false)
        return
      }

      console.log("Adding verified user_id to form data:", userData.user_id)
      formData.append("user_id", userData.user_id.toString())
      
      // Add all form fields to FormData
      formData.append("doctor_id", doctorId)
      formData.append("clinical_competence", clinicalCompetence.toString())
      formData.append("communication_stats", communication.toString())
      formData.append("empathy_compassion", empathyCompassion.toString())
      formData.append("time_management", timeManagement.toString())
      formData.append("follow_up_care", followUpCare.toString())
      formData.append("overall_satisfaction", overallSatisfaction.toString())
      formData.append("additional_comments", comments)
      formData.append("recommendation_rating", recommendationRating.toString())

      // Add verification file if provided
      if (verificationFile) {
        formData.append("verification_file", verificationFile)
        console.log("Adding verification file to submission:", verificationFile.name)
      }

      console.log("Submitting review with doctor_id:", doctorId, "and user_id:", userData.user_id)
      
      try {
        const result = await submitReview(formData)
        
        // Dismiss loading toast
        toast.dismiss(loadingToast)
        
        if (!result.success) {
          console.error("Server action error:", result.error)
          
          // Show error toast with specific message from server
          toast.error(result.error || "Failed to submit review")
          setIsSubmitting(false)
          return
        }
        
        console.log("=== REVIEW SUBMISSION COMPLETED SUCCESSFULLY ===")
        toast.success("Thank you! Your rating has been submitted successfully")
        
        // Reset form
        resetForm()
        
        // Navigate to the thank you page
        console.log("Redirecting to thank you page...")
        setTimeout(() => {
          router.push(`/doctors/${doctorId}/rate/thank-you`)
        }, 800) // Longer delay to ensure toast is visible and form reset is complete
      } catch (error: any) {
        // Dismiss loading toast
        toast.dismiss(loadingToast)
        
        console.error("=== REVIEW SUBMISSION FAILED ===")
        console.error("Error during review submission:", error)
        
        // Show specific error message
        const errorMessage = error?.message || "Unknown error occurred"
        toast.error(`Failed to submit review: ${errorMessage}`)
        setIsSubmitting(false)
      }
    } catch (error: any) {
      console.error("=== REVIEW SUBMISSION FAILED - FORM PREPARATION ERROR ===")
      console.error("Error preparing review submission:", error)
      toast.error(`An unexpected error occurred: ${error?.message || "Unknown error"}`)
      setIsSubmitting(false)
    }
  }

  const resetForm = () => {
    console.log("Resetting form fields")
    setClinicalCompetence(0)
    setCommunication(0)
    setEmpathyCompassion(0)
    setTimeManagement(0)
    setFollowUpCare(0)
    setOverallSatisfaction(0)
    setComments("")
    setRecommendationRating(0)
    setVerificationFile(null)
    setVerificationError(null)
  }

  // If we're still checking authentication (but not if timeout occurred) or not mounted yet
  if (!mounted || ((isUser === null || authIsLoading) && !authTimeout)) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="flex items-center space-x-2">
          <ReloadIcon className="animate-spin h-6 w-6 text-primary" />
          <span className="text-foreground">
            {authIsLoading ? "Loading authentication..." : "Verifying account..."}
          </span>
        </div>
      </div>
    )
  }

  // If user is not logged in or not a patient
  if (!isUser || !userVerified) {
    return (
      <>
      <style jsx global>{`
        /* Ultra-specific light theme styling for rating authentication card - Override global styles */
        html:not(.dark) .rating-auth-card.rating-auth-card.rating-auth-card {
          background: linear-gradient(to bottom, rgba(240, 253, 244, 0.95), rgba(220, 252, 231, 0.95)) !important;
          border: 2px solid rgba(34, 197, 94, 0.3) !important;
          box-shadow: 0 20px 60px rgba(34, 197, 94, 0.15) !important;
        }

        html:not(.dark) .rating-auth-card .text-foreground {
          color: hsl(142, 76%, 25%) !important;
        }

        html:not(.dark) .rating-auth-card .text-foreground\/80 {
          color: hsl(142, 76%, 30%) !important;
        }

        html:not(.dark) .rating-auth-card .text-yellow-500 {
          color: hsl(142, 76%, 36%) !important;
        }

        html:not(.dark) .rating-auth-card .text-primary {
          color: hsl(142, 76%, 36%) !important;
        }

        html:not(.dark) .rating-auth-card .bg-gradient-to-br {
          background: linear-gradient(to bottom right, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05)) !important;
        }

        html:not(.dark) .rating-auth-card .border-primary\/20 {
          border-color: rgba(34, 197, 94, 0.2) !important;
        }
      `}</style>
      <Card className="rating-auth-card bg-gradient-to-b from-slate-900 to-slate-950 border border-primary/30 shadow-xl overflow-hidden">
        <div className="absolute top-0 right-0 w-32 h-32 bg-primary/10 rounded-full -mt-16 -mr-16 backdrop-blur-xl"></div>
        <div className="absolute bottom-0 left-0 w-16 h-16 bg-primary/10 rounded-full -mb-8 -ml-8 backdrop-blur-xl"></div>
        
        <CardHeader className="pb-3 relative z-10">
          <div className="flex items-center gap-2 mb-2">
            <Trophy className="h-5 w-5 text-yellow-500" />
            <CardTitle className="text-foreground text-xl">Authentication Required</CardTitle>
          </div>
          <CardDescription className="text-foreground/80">
              {!isUser ? "You must be signed in as a patient to rate doctors." : 
                "Only patients can rate doctors. Please sign in with a patient account."}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="relative z-10 space-y-4">
          <div className="bg-gradient-to-br from-primary/20 to-primary/5 p-4 rounded-lg border border-primary/20">
            <h4 className="font-medium text-foreground mb-2 flex items-center gap-1.5">
              <Sparkles className="h-4 w-4 text-yellow-500" />
              Why Rate Doctors?
            </h4>
            <ul className="space-y-2 text-sm text-foreground/80">
              <li className="flex items-start gap-1.5">
                <span className="text-primary mt-0.5">•</span>
                <span>Help other patients make informed decisions</span>
              </li>
              <li className="flex items-start gap-1.5">
                <span className="text-primary mt-0.5">•</span>
                <span>Recognize excellence in healthcare</span>
              </li>
              <li className="flex items-start gap-1.5">
                <span className="text-primary mt-0.5">•</span>
                <span>Contribute to improving healthcare standards</span>
              </li>
            </ul>
          </div>
          
          <Button
            className="w-full bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/70"
            onClick={() => router.push(`/patient/login?redirectTo=${encodeURIComponent(window.location.href)}`)}
          >
            Sign In as Patient
          </Button>
          </CardContent>
        </Card>
      </>
    )
  }

  // If the user has already submitted
  if (isSubmitted && hasExistingReview) {
    // Format the review date
    const reviewDate = existingReviewData?.review_date ? 
      new Date(existingReviewData.review_date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }) : 'Recently';

    return (
      <Card className="border-primary/20 bg-gradient-to-b from-slate-900 to-slate-950">
        <CardHeader className="pb-3">
          <CardTitle className="text-foreground flex items-center gap-2">
            <StarIcon className="h-5 w-5 text-yellow-500" />
            You've Already Reviewed This Doctor
          </CardTitle>
          <CardDescription className="text-foreground/80">
            You submitted a review for {doctor.fullname} on {reviewDate}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-6">
            {/* Summary of existing review */}
            <div className="bg-primary/10 rounded-lg p-4 border border-primary/20">
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-foreground font-medium">Your Review Summary</h3>
                <div className="flex items-center gap-1">
                  <span className="text-foreground/90 text-sm">Overall Rating:</span>
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <StarIcon 
                        key={i} 
                        className={`h-4 w-4 ${i < Math.round(existingReviewData?.rating || 0) ? 'text-yellow-500 fill-yellow-500' : 'text-foreground/30'}`} 
                      />
                    ))}
                  </div>
                </div>
              </div>
              
              <div className="grid gap-2 text-sm">
                <div className="grid grid-cols-2">
                  <span className="text-foreground/60">Clinical Competence:</span>
                  <span className="text-foreground">{existingReviewData?.clinical_competence}/5</span>
                </div>
                <div className="grid grid-cols-2">
                  <span className="text-foreground/60">Communication:</span>
                  <span className="text-foreground">{existingReviewData?.communication_stats}/5</span>
                </div>
                <div className="grid grid-cols-2">
                  <span className="text-foreground/60">Empathy & Compassion:</span>
                  <span className="text-foreground">{existingReviewData?.empathy_compassion}/5</span>
                </div>
                <div className="grid grid-cols-2">
                  <span className="text-foreground/60">Time Management:</span>
                  <span className="text-foreground">{existingReviewData?.time_management}/5</span>
                </div>
                <div className="grid grid-cols-2">
                  <span className="text-foreground/60">Follow-up Care:</span>
                  <span className="text-foreground">{existingReviewData?.follow_up_care}/5</span>
                </div>
                <div className="grid grid-cols-2">
                  <span className="text-foreground/60">Overall Satisfaction:</span>
                  <span className="text-foreground">{existingReviewData?.overall_satisfaction}/5</span>
                </div>
              </div>
              
              {existingReviewData?.additional_comments && (
                <div className="mt-4 pt-4 border-t border-white/10">
                  <h4 className="text-foreground text-sm mb-2">Your Comments:</h4>
                  <p className="text-foreground/80 text-sm italic">"{existingReviewData.additional_comments}"</p>
                </div>
              )}
            </div>
            
            <div className="pt-2 text-center">
              <p className="text-foreground/80 text-sm mb-4">
                Thank you for sharing your experience. Your feedback helps other patients make informed decisions.
              </p>
              <div className="flex gap-3 justify-center">
                <Button 
                  className="bg-background/90 hover:bg-background/80 text-foreground"
                  onClick={() => router.push(`/doctors/${doctor.doctor_id}`)}
                >
                  Back to Doctor Profile
                </Button>
                <Button 
                  className="bg-gradient-to-r from-primary to-primary/90"
                  onClick={() => router.push(`/standings`)}
                >
                  Find Another Doctor
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // If the user has just submitted (but doesn't have existing data yet)
  if (isSubmitted) {
    return (
      <Card className="border-primary/20 bg-gradient-to-b from-slate-900 to-slate-950">
        <CardHeader className="pb-3">
          <CardTitle className="text-foreground flex items-center gap-2">
            <StarIcon className="h-5 w-5 text-yellow-500" />
            Thank You For Your Rating
          </CardTitle>
          <CardDescription className="text-foreground/80">
            Your feedback helps improve healthcare quality
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center p-6">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
              <StarIcon className="h-8 w-8 text-yellow-500" />
            </div>
            <p className="text-foreground text-center mb-6">
              Your rating has been submitted successfully and will help others make informed decisions.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // If user is a verified patient, show the rating form
  return (
    <>


      <Card className="bg-card border-border text-card-foreground shadow-xl">
      <CardHeader className="pb-3">
        <CardTitle className="text-card-foreground flex items-center gap-2">
          <Star className="h-5 w-5 text-yellow-500" />
          Rate {doctor.fullname}
        </CardTitle>
        <CardDescription className="text-muted-foreground">
          {doctor.specialty} {doctor.hospital_name ? `at ${doctor.hospital_name}` : ""}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Clinical Competence */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-card-foreground flex items-center gap-2">
              <span className="w-7 h-7 bg-green-500/20 rounded-full flex items-center justify-center">
                <span className="text-green-500 text-sm font-bold">1</span>
              </span>
              Clinical Competence
            </h3>
            <p className="text-sm text-muted-foreground">
              How would you rate the doctor's knowledge and skills in diagnosing and treating your condition?
            </p>
            <div className="flex items-center gap-2 pt-2">
              {[1, 2, 3, 4, 5].map((value) => (
                <button
                  key={value}
                  type="button"
                  onClick={() => setClinicalCompetence(value)}
                  className={`rating-button w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200 border ${
                    clinicalCompetence >= value
                      ? "selected bg-primary text-primary-foreground border-primary"
                      : "bg-background text-foreground border-border hover:bg-accent hover:text-accent-foreground dark:bg-white/10 dark:text-white dark:border-white/30 dark:hover:bg-white/20 dark:hover:border-white/50"
                  }`}
                >
                  {value}
                </button>
              ))}
              <span className="ml-3 text-sm text-muted-foreground">
                {clinicalCompetence > 0 ? `${clinicalCompetence} out of 5` : "Select a rating"}
              </span>
            </div>
          </div>

          {/* Communication */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-card-foreground flex items-center gap-2">
              <span className="w-7 h-7 bg-blue-500/20 rounded-full flex items-center justify-center">
                <span className="text-blue-500 text-sm font-bold">2</span>
              </span>
              Communication
            </h3>
            <p className="text-sm text-muted-foreground">
              Did the doctor listen carefully to your concerns and questions? Did the doctor explain your diagnosis and treatment options clearly?
            </p>
            <div className="flex items-center gap-2 pt-2">
              {[1, 2, 3, 4, 5].map((value) => (
                <button
                  key={value}
                  type="button"
                  onClick={() => setCommunication(value)}
                  className={`rating-button w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200 border ${
                    communication >= value
                      ? "selected bg-blue-500 text-white border-blue-500"
                      : "bg-background text-foreground border-border hover:bg-accent hover:text-accent-foreground dark:bg-white/10 dark:text-white dark:border-white/30 dark:hover:bg-white/20 dark:hover:border-white/50"
                  }`}
                >
                  {value}
                </button>
              ))}
              <span className="ml-3 text-sm text-muted-foreground">
                {communication > 0 ? `${communication} out of 5` : "Select a rating"}
              </span>
            </div>
          </div>

          {/* Empathy & Compassion */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-card-foreground flex items-center gap-2">
              <span className="w-7 h-7 bg-purple-500/20 rounded-full flex items-center justify-center">
                <span className="text-purple-500 text-sm font-bold">3</span>
              </span>
              Empathy & Compassion
            </h3>
            <p className="text-sm text-muted-foreground">
              How would you rate the doctor's empathy and compassion towards your situation?
            </p>
            <div className="flex items-center gap-2 pt-2">
              {[1, 2, 3, 4, 5].map((value) => (
                <button
                  key={value}
                  type="button"
                  onClick={() => setEmpathyCompassion(value)}
                  className={`rating-button w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200 border ${
                    empathyCompassion >= value
                      ? "selected bg-purple-500 text-white border-purple-500"
                      : "bg-background text-foreground border-border hover:bg-accent hover:text-accent-foreground dark:bg-white/10 dark:text-white dark:border-white/30 dark:hover:bg-white/20 dark:hover:border-white/50"
                  }`}
                >
                  {value}
                </button>
              ))}
              <span className="ml-3 text-sm text-muted-foreground">
                {empathyCompassion > 0 ? `${empathyCompassion} out of 5` : "Select a rating"}
              </span>
            </div>
          </div>

          {/* Time Management */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-card-foreground flex items-center gap-2">
              <span className="w-7 h-7 bg-orange-500/20 rounded-full flex items-center justify-center">
                <span className="text-orange-500 text-sm font-bold">4</span>
              </span>
              Time Management
            </h3>
            <p className="text-sm text-muted-foreground">
              Did the doctor spend adequate time with you during the appointment?
            </p>
            <div className="flex items-center gap-2 pt-2">
              {[1, 2, 3, 4, 5].map((value) => (
                <button
                  key={value}
                  type="button"
                  onClick={() => setTimeManagement(value)}
                  className={`rating-button w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200 border ${
                    timeManagement >= value
                      ? "selected bg-orange-500 text-white border-orange-500"
                      : "bg-background text-foreground border-border hover:bg-accent hover:text-accent-foreground dark:bg-white/10 dark:text-white dark:border-white/30 dark:hover:bg-white/20 dark:hover:border-white/50"
                  }`}
                >
                  {value}
                </button>
              ))}
              <span className="ml-3 text-sm text-muted-foreground">
                {timeManagement > 0 ? `${timeManagement} out of 5` : "Select a rating"}
              </span>
            </div>
          </div>

          {/* Follow-up Care */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-card-foreground flex items-center gap-2">
              <span className="w-7 h-7 bg-pink-500/20 rounded-full flex items-center justify-center">
                <span className="text-pink-500 text-sm font-bold">5</span>
              </span>
              Follow-up Care
            </h3>
            <p className="text-sm text-muted-foreground">
              Were you satisfied with the follow-up care and instructions provided by the doctor?
            </p>
            <div className="flex items-center gap-2 pt-2">
              {[1, 2, 3, 4, 5].map((value) => (
                <button
                  key={value}
                  type="button"
                  onClick={() => setFollowUpCare(value)}
                  className={`rating-button w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200 border ${
                    followUpCare >= value
                      ? "selected bg-pink-500 text-white border-pink-500"
                      : "bg-background text-foreground border-border hover:bg-accent hover:text-accent-foreground dark:bg-white/10 dark:text-white dark:border-white/30 dark:hover:bg-white/20 dark:hover:border-white/50"
                  }`}
                >
                  {value}
                </button>
              ))}
              <span className="ml-3 text-sm text-muted-foreground">
                {followUpCare > 0 ? `${followUpCare} out of 5` : "Select a rating"}
              </span>
            </div>
          </div>

          {/* Overall Satisfaction */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-card-foreground flex items-center gap-2">
              <span className="w-7 h-7 bg-primary/20 rounded-full flex items-center justify-center">
                <span className="text-primary text-sm font-bold">6</span>
              </span>
              Overall Satisfaction
            </h3>
            <p className="text-sm text-muted-foreground">
              Overall, how satisfied were you with the care provided by the doctor?
            </p>
            <div className="flex items-center gap-2 pt-2">
              {[1, 2, 3, 4, 5].map((value) => (
                <button
                  key={value}
                  type="button"
                  onClick={() => setOverallSatisfaction(value)}
                  className={`rating-button w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200 border ${
                    overallSatisfaction >= value
                      ? "selected bg-primary text-primary-foreground border-primary"
                      : "bg-background text-foreground border-border hover:bg-accent hover:text-accent-foreground dark:bg-white/10 dark:text-white dark:border-white/30 dark:hover:bg-white/20 dark:hover:border-white/50"
                  }`}
                >
                  {value}
                </button>
              ))}
              <span className="ml-3 text-sm text-muted-foreground">
                {overallSatisfaction > 0 ? `${overallSatisfaction} out of 5` : "Select a rating"}
              </span>
            </div>
          </div>

          {/* Additional Comments */}
          <div className="space-y-3 pt-2">
            <h3 className="text-lg font-semibold text-card-foreground flex items-center gap-2">
              <span className="w-7 h-7 bg-yellow-500/20 rounded-full flex items-center justify-center">
                <span className="text-yellow-500 text-sm font-bold">7</span>
              </span>
              Additional Comments
            </h3>
            <p className="text-sm text-muted-foreground">
              Do you have any additional comments or suggestions for improvement?
            </p>
            <Textarea
              placeholder="Share your experience with this doctor..."
              value={comments}
              onChange={(e) => setComments(e.target.value)}
              className="bg-background border-border text-foreground placeholder:text-muted-foreground min-h-[100px]"
            />
          </div>

          {/* Recommendation Rating */}
          <div className="space-y-3 pt-2">
            <h3 className="text-lg font-semibold text-card-foreground flex items-center gap-2">
              <span className="w-7 h-7 bg-teal-500/20 rounded-full flex items-center justify-center">
                <span className="text-teal-500 text-sm font-bold">8</span>
              </span>
              Recommendation Rating
            </h3>
            <p className="text-sm text-muted-foreground">
              On a scale of 1-5, how likely are you to recommend this doctor to others? (1 being very unlikely, 5 being very likely)
            </p>
            <div className="flex items-center gap-2 pt-2">
              {[1, 2, 3, 4, 5].map((value) => (
                <button
                  key={value}
                  type="button"
                  onClick={() => setRecommendationRating(value)}
                  className={`rating-button w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200 border ${
                    recommendationRating >= value
                      ? "selected bg-teal-500 text-white border-teal-500"
                      : "bg-background text-foreground border-border hover:bg-accent hover:text-accent-foreground dark:bg-white/10 dark:text-white dark:border-white/30 dark:hover:bg-white/20 dark:hover:border-white/50"
                  }`}
                >
                  {value}
                </button>
              ))}
              <span className="ml-3 text-sm text-muted-foreground">
                {recommendationRating > 0 ? `${recommendationRating} out of 5` : "Select a rating"}
              </span>
            </div>
          </div>

          {/* Verification Upload Section - Available in both light and dark themes */}
          <div className="space-y-3 pt-2">
            <h3 className="text-lg font-semibold text-card-foreground flex items-center gap-2">
              <span className="w-7 h-7 bg-blue-500/20 rounded-full flex items-center justify-center">
                <FileImage className="w-4 h-4 text-blue-500" />
              </span>
              Verify Your Appointment (Optional)
            </h3>
            <p className="text-sm text-muted-foreground">
              Upload a photo of your appointment receipt or confirmation to verify your visit. This helps ensure review authenticity.
            </p>

            <div className="space-y-3">
              <div className="relative">
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handleVerificationFileChange}
                  className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-green-100 file:text-green-800 hover:file:bg-green-200 dark:file:bg-green-900 dark:file:text-green-100"
                  disabled={isSubmitting}
                />
                {verificationFile && (
                  <div className="mt-2 flex items-center gap-2 text-sm text-green-700 dark:text-green-300">
                    <CheckCircle className="h-4 w-4" />
                    <span>{verificationFile.name}</span>
                    <span className="text-xs">({(verificationFile.size / 1024 / 1024).toFixed(1)}MB)</span>
                  </div>
                )}
              </div>

              <p className="text-xs text-muted-foreground">
                Accepted formats: JPG, PNG, WebP. Maximum size: 5MB. All sensitive information will be permanently deleted after verification.
              </p>

              {verificationError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{verificationError}</AlertDescription>
                </Alert>
              )}
            </div>
          </div>

          {/* Submit button section */}
          <div className="pt-4">
            <div className="p-4 bg-primary/10 rounded-lg border border-primary/20 mb-4 flex items-start gap-3">
              <Info className="h-5 w-5 text-primary shrink-0 mt-0.5" />
              <div className="text-sm text-muted-foreground">
                <p className="font-medium text-card-foreground mb-1">Your feedback matters</p>
                <p>Your ratings help other patients make informed decisions and helps doctors improve their services.</p>
              </div>
          </div>

          <Button
            type="submit"
              className="w-full bg-primary text-primary-foreground hover:bg-primary/90 py-6 text-lg font-semibold"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                  <ReloadIcon className="mr-2 h-5 w-5 animate-spin" />
                Submitting...
              </>
            ) : (
              <>Submit Rating</>
            )}
          </Button>
          </div>
        </form>
      </CardContent>
    </Card>
    </>
  )
}

