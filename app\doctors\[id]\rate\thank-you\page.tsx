"use client"

import React, { useEffect } from "react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Star, Trophy, CheckCircle, ArrowLeft } from "lucide-react"
import { useRouter, useParams } from "next/navigation"
import Link from "next/link"

export default function ThankYouPage() {
  const router = useRouter()
  // Use the useParams hook instead of React.use
  const params = useParams()
  const doctorId = params?.id as string

  // Confetti effect - trigger on page load
  useEffect(() => {
    const showConfetti = async () => {
      // Dynamically import the confetti library to reduce bundle size
      const confetti = (await import('canvas-confetti')).default
      
      // Launch confetti
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 },
        colors: ['#FFD700', '#FFA500', '#87CEEB']
      })
    }

    showConfetti().catch(console.error)
  }, [])

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="max-w-md w-full bg-card border-border text-card-foreground shadow-xl overflow-hidden">
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-primary/10 rounded-full -mt-16 -mr-16 backdrop-blur-xl"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-yellow-500/10 rounded-full -mb-12 -ml-12 backdrop-blur-xl"></div>
          
          <CardHeader className="pb-4 relative z-10 text-center">
            <div className="mx-auto w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
            
            <CardTitle className="text-card-foreground text-3xl mb-2">Thank You!</CardTitle>
            <CardDescription className="text-muted-foreground text-lg">
              Your review has been successfully submitted
            </CardDescription>
          </CardHeader>
          
          <CardContent className="relative z-10 space-y-6 px-6 pb-8">
            <div className="bg-gradient-to-br from-primary/20 to-primary/5 p-4 rounded-lg border border-primary/20 text-center">
              <p className="text-card-foreground text-lg mb-4">
                Your feedback is valuable and helps improve healthcare quality.
              </p>
              
              <div className="flex justify-center space-x-4 mb-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star 
                    key={star}
                    className="h-7 w-7 text-yellow-500 fill-yellow-500"
                  />
                ))}
              </div>
              
              <p className="text-muted-foreground text-sm">
                Other patients will now benefit from your honest assessment
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 pt-2">
              <Button
                variant="outline"
                className="bg-background border-border text-foreground hover:bg-accent hover:text-accent-foreground flex-1"
                onClick={() => router.push(`/doctors/${doctorId}`)}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Doctor Profile
              </Button>
              
              <Button
                className="bg-primary text-primary-foreground hover:bg-primary/90 flex-1"
                onClick={() => router.push('/standings')}
              >
                <Trophy className="mr-2 h-4 w-4" />
                View Doctor Rankings
              </Button>
            </div>
        </CardContent>
      </Card>
    </div>
  )
} 