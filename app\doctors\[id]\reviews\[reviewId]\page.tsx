import { getDoctorById, getReviewById } from "@/lib/hybrid-data-service"
import { notFound } from "next/navigation"
import { Star, ArrowLeft, Calendar, User } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import Link from "next/link"
import { format } from "date-fns"
import { ErrorBoundary } from "next/dist/client/components/error-boundary"

// Add a fallback component to show when review data isn't available
function ReviewNotFound() {
  return (
    <div className="container mx-auto py-8">
      <div className="max-w-3xl mx-auto">
        <Card className="border-primary/20 bg-gradient-to-b from-slate-900 to-slate-950 shadow-xl overflow-hidden">
          <CardHeader className="pb-3">
            <CardTitle className="text-foreground text-xl">Review Not Found</CardTitle>
            <CardDescription className="text-foreground/80">
              We couldn't find the review you're looking for
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <p className="text-foreground/80">The review may have been removed or the link might be incorrect.</p>
            <Link href="/standings">
              <Button className="bg-primary hover:bg-primary/90">
                Browse Top Doctors
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default async function ReviewDetailPage({
  params,
}: {
  params: { id: string, reviewId: string }
}) {
  console.log(`[ReviewDetailPage] Starting page render for doctor ID: ${params.id}, review ID: ${params.reviewId}`)
  
  try {
    // Fetch doctor and review data with proper error handling
    console.log(`[ReviewDetailPage] Fetching doctor (${params.id}) and review (${params.reviewId}) data`)
    
    let doctor = null;
    let review = null;
    let isDynamicReview = false;
    
    try {
      doctor = await getDoctorById(params.id);
      console.log(`[ReviewDetailPage] Doctor data:`, doctor ? 'Found' : 'Not found');
    } catch (doctorError) {
      console.error(`[ReviewDetailPage] Error fetching doctor:`, doctorError);
    }
    
    try {
      review = await getReviewById(params.reviewId);
      console.log(`[ReviewDetailPage] Review data:`, review ? 'Found' : 'Not found', 
        review ? `for ID ${review.review_id}` : '');
      
      // Check if this is a dynamically generated review (they use user_id: "999")
      if (review && review.user_id === "999") {
        isDynamicReview = true;
        console.log(`[ReviewDetailPage] This is a dynamically generated review`);
      }
    } catch (reviewError) {
      console.error(`[ReviewDetailPage] Error fetching review:`, reviewError);
    }

    if (!doctor) {
      console.error(`[ReviewDetailPage] Doctor with ID ${params.id} not found`);
      return <ReviewNotFound />;
    }

    if (!review) {
      console.error(`[ReviewDetailPage] Review with ID ${params.reviewId} not found`);
      return <ReviewNotFound />;
    }
    
    console.log(`[ReviewDetailPage] Successfully retrieved data for review ID ${params.reviewId}:`, 
      { 
        review_id: review.review_id,
        doctor_id: review.doctor_id,
        rating: review.rating,
        date: review.review_date
      }
    );

    // Format date with fallback
    const reviewDate = review.review_date ? 
      format(new Date(review.review_date), 'MMMM d, yyyy') : 'Recently'

    // Get numerical values with fallbacks
    const clinicalCompetence = Number(review.clinical_competence) || 0
    const communicationStats = Number(review.communication_stats) || 0
    const empathyCompassion = Number(review.empathy_compassion) || 0
    const timeManagement = Number(review.time_management) || 0
    const followUpCare = Number(review.follow_up_care) || 0
    const overallSatisfaction = Number(review.overall_satisfaction) || 0
    // Get recommendation rating from either column name
    const recommendationRating = Number(review.Recommendation || review.recommendation_rating) || 0
    const overallRating = Number(review.rating) || 0

    return (
      <div className="container mx-auto py-8">
        <div className="mb-6">
          <Link href={`/doctors/${doctor.doctor_id}`}>
            <Button variant="outline" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to {doctor.fullname}
            </Button>
          </Link>
        </div>

        <h1 className="text-3xl font-bold mb-2">Review for {doctor.fullname}</h1>
        <p className="text-muted-green mb-8">
          {doctor.specialty} • Review submitted on {reviewDate}
        </p>

        {isDynamicReview && (
          <div className="mb-4 p-3 bg-amber-500/20 border border-amber-500/30 rounded-md">
            <p className="text-amber-500 text-sm font-medium">
              <span className="inline-block mr-2">⚠️</span>
              This is a preview review generated for demonstration purposes. The actual review may not be available in the database.
            </p>
          </div>
        )}

        <div className="max-w-3xl mx-auto">
          <Card className="review-card-light dark:border-primary/20 dark:bg-gradient-to-b dark:from-slate-900 dark:to-slate-950 shadow-xl overflow-hidden">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-green-900 dark:text-foreground text-xl flex items-center gap-2">
                  <Star className="h-5 w-5 text-yellow-500" />
                  Review Summary
                </CardTitle>
                <div className="flex items-center gap-2">
                  <span className="text-green-800 dark:text-foreground/80">Overall Rating:</span>
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star 
                        key={i} 
                        className={`h-5 w-5 ${i < Math.round(overallRating) ? 'text-yellow-500 fill-yellow-500' : 'text-gray-400 dark:text-foreground/30'}`} 
                      />
                    ))}
                  </div>
                </div>
              </div>
              <CardDescription className="text-green-800 dark:text-foreground/80 flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>Submitted on {reviewDate}</span>
                {isDynamicReview && (
                  <span className="inline-flex items-center px-2 py-1 ml-2 rounded-full text-xs font-medium bg-amber-500/30 text-amber-400">
                    Preview
                  </span>
                )}
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="review-section-light dark:bg-primary/10 p-4 rounded-lg dark:border-primary/20">
                    <h3 className="text-lg font-medium text-green-900 dark:text-foreground mb-3">Rating Details</h3>
                    
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-green-800 dark:text-foreground/80">Clinical Competence</span>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star 
                              key={i} 
                              className={`h-4 w-4 ${i < clinicalCompetence ? 'text-yellow-500 fill-yellow-500' : 'text-gray-400 dark:text-foreground/30'}`} 
                            />
                          ))}
                        </div>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-green-800 dark:text-foreground/80">Communication</span>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star 
                              key={i} 
                              className={`h-4 w-4 ${i < communicationStats ? 'text-yellow-500 fill-yellow-500' : 'text-gray-400 dark:text-foreground/30'}`} 
                            />
                          ))}
                        </div>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-green-800 dark:text-foreground/80">Empathy & Compassion</span>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star 
                              key={i} 
                              className={`h-4 w-4 ${i < empathyCompassion ? 'text-yellow-500 fill-yellow-500' : 'text-gray-400 dark:text-foreground/30'}`} 
                            />
                          ))}
                        </div>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-green-800 dark:text-foreground/80">Time Management</span>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star 
                              key={i} 
                              className={`h-4 w-4 ${i < timeManagement ? 'text-yellow-500 fill-yellow-500' : 'text-gray-400 dark:text-foreground/30'}`} 
                            />
                          ))}
                        </div>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-green-800 dark:text-foreground/80">Follow-up Care</span>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star 
                              key={i} 
                              className={`h-4 w-4 ${i < followUpCare ? 'text-yellow-500 fill-yellow-500' : 'text-gray-400 dark:text-foreground/30'}`} 
                            />
                          ))}
                        </div>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-green-800 dark:text-foreground/80">Overall Satisfaction</span>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star 
                              key={i} 
                              className={`h-4 w-4 ${i < overallSatisfaction ? 'text-yellow-500 fill-yellow-500' : 'text-gray-400 dark:text-foreground/30'}`} 
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  {review.additional_comments && (
                    <div className="review-section-light dark:bg-primary/10 p-4 rounded-lg dark:border-primary/20">
                      <h3 className="text-lg font-medium text-green-900 dark:text-foreground mb-3">Additional Comments</h3>
                      <p className="text-green-800 dark:text-foreground/80 italic">"{review.additional_comments}"</p>
                    </div>
                  )}
                  
                  {recommendationRating > 0 && (
                    <div className="review-section-light dark:bg-primary/10 p-4 rounded-lg dark:border-primary/20">
                      <h3 className="text-lg font-medium text-green-900 dark:text-foreground mb-3">Recommendation</h3>
                      <div className="flex justify-between items-center">
                        <span className="text-green-800 dark:text-foreground/80">Likelihood to Recommend</span>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star 
                              key={i} 
                              className={`h-4 w-4 ${i < recommendationRating ? 'text-yellow-500 fill-yellow-500' : 'text-gray-400 dark:text-foreground/30'}`} 
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div className="review-section-light dark:bg-primary/10 p-4 rounded-lg dark:border-primary/20">
                    <h3 className="text-lg font-medium text-green-900 dark:text-foreground mb-3">About the Doctor</h3>
                    <div className="space-y-2">
                      <p><strong className="text-green-800 dark:text-foreground/80">Specialty:</strong> {doctor.specialty}</p>
                      <p><strong className="text-green-800 dark:text-foreground/80">Subspecialty:</strong> {doctor.subspecialty || "Not Applicable"}</p>
                    </div>
                    <Link href={`/doctors/${doctor.doctor_id}`} className="mt-4 inline-block">
                      <Button className="bg-primary hover:bg-primary/90">View Doctor Profile</Button>
                    </Link>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  } catch (error) {
    console.error("Error fetching review details:", error)
    return <ReviewNotFound />;
  }
} 