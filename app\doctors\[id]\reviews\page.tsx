import { getDoctorById, getDoctorReviews } from "@/lib/hybrid-data-service"
import { ReviewsList } from "./reviews-list"
import { notFound } from "next/navigation"

export default async function DoctorReviewsPage({
  params,
}: {
  params: { id: string }
}) {
  const [doctor, reviews] = await Promise.all([getDoctorById(params.id), getDoctorReviews(params.id)])

  if (!doctor) {
    notFound()
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-2">Reviews for {doctor.fullname}</h1>
      <p className="text-muted-green mb-8">
        {reviews.length} {reviews.length === 1 ? "review" : "reviews"} for {doctor.specialty} services
      </p>

      <ReviewsList doctor={doctor} reviews={reviews} />
    </div>
  )
}

