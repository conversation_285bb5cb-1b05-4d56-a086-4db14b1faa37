"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Star, ThumbsUp, User, Upload } from "lucide-react"
import { useRouter } from "next/navigation"
import { formatDistanceToNow } from "date-fns"
import { VerifiedVisitBadge } from "@/components/ui/verified-visit-badge"
import { VerificationRequestModal } from "@/components/verification-request-modal"
import { useAuth } from "@/context/AuthContext"

interface Doctor {
  doctor_id: string
  fullname: string
  specialty: string
}

interface Review {
  review_id: string
  user_id: string
  doctor_id: string
  clinical_competence: number
  communication_stats: number
  empathy_compassion: number
  time_management: number
  follow_up_care: number
  overall_satisfaction: number
  additional_comments?: string
  recommendation_rating?: number
  Recommendation: number
  rating: number
  review_date: string
  verification_status?: 'unverified' | 'pending_verification' | 'verified' | 'rejected'
  user?: {
    username: string
  }
}

interface ReviewsListProps {
  doctor: Doctor
  reviews: Review[]
}

export function ReviewsList({ doctor, reviews }: ReviewsListProps) {
  const router = useRouter()
  const { user } = useAuth()
  const [sortBy, setSortBy] = useState("recent")
  const [showVerifiedOnly, setShowVerifiedOnly] = useState(false)

  const handleAddReview = () => {
    router.push(`/doctors/${doctor.doctor_id}/rate`)
  }

  // Filter reviews based on verification status
  const displayedReviews = showVerifiedOnly
    ? reviews.filter(r => r.verification_status === 'verified')
    : reviews

  // Sort the displayed reviews
  const sortedReviews = [...displayedReviews].sort((a, b) => {
    if (sortBy === "recent") {
      return new Date(b.review_date).getTime() - new Date(a.review_date).getTime()
    } else if (sortBy === "highest") {
      return b.rating - a.rating
    } else if (sortBy === "lowest") {
      return a.rating - b.rating
    }
    return 0
  })

  // Calculate average ratings
  const calculateAverage = (values: number[]) => {
    if (values.length === 0) return 0
    const sum = values.reduce((acc, value) => acc + value, 0)
    return sum / values.length
  }

  const averageRatings = {
    clinical: calculateAverage(sortedReviews.map((r) => r.clinical_competence)),
    communication: calculateAverage(sortedReviews.map((r) => r.communication_stats)),
    empathy: calculateAverage(sortedReviews.map((r) => r.empathy_compassion)),
    time: calculateAverage(sortedReviews.map((r) => r.time_management)),
    followUp: calculateAverage(sortedReviews.map((r) => r.follow_up_care)),
    overall: calculateAverage(sortedReviews.map((r) => r.rating)),
    recommendation: calculateAverage(sortedReviews.map((r) => r.Recommendation)),
  }

  // Render stars for a rating
  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${star <= rating ? "fill-yellow-400 text-yellow-400" : "text-muted-green"}`}
          />
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {/* Left column - Summary */}
      <div>
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-medium mb-4">Rating Summary</h3>

            <div className="flex items-center gap-2 mb-6">
              <div className="text-4xl font-bold">{averageRatings.overall.toFixed(1)}</div>
              <div>
                <div className="flex">{renderStars(Math.round(averageRatings.overall))}</div>
                <p className="text-sm text-muted-green">
                  Based on {reviews.length} {reviews.length === 1 ? "review" : "reviews"}
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm">Clinical Competence</span>
                  <span className="text-sm font-medium">{averageRatings.clinical.toFixed(1)}</span>
                </div>
                <div className="w-full bg-muted-green rounded-full h-1.5">
                  <div
                    className="bg-primary h-1.5 rounded-full"
                    style={{ width: `${(averageRatings.clinical / 5) * 100}%` }}
                  ></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm">Communication</span>
                  <span className="text-sm font-medium">{averageRatings.communication.toFixed(1)}</span>
                </div>
                <div className="w-full bg-muted-green rounded-full h-1.5">
                  <div
                    className="bg-primary h-1.5 rounded-full"
                    style={{ width: `${(averageRatings.communication / 5) * 100}%` }}
                  ></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm">Empathy & Compassion</span>
                  <span className="text-sm font-medium">{averageRatings.empathy.toFixed(1)}</span>
                </div>
                <div className="w-full bg-muted-green rounded-full h-1.5">
                  <div
                    className="bg-primary h-1.5 rounded-full"
                    style={{ width: `${(averageRatings.empathy / 5) * 100}%` }}
                  ></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm">Time Management</span>
                  <span className="text-sm font-medium">{averageRatings.time.toFixed(1)}</span>
                </div>
                <div className="w-full bg-muted-green rounded-full h-1.5">
                  <div
                    className="bg-primary h-1.5 rounded-full"
                    style={{ width: `${(averageRatings.time / 5) * 100}%` }}
                  ></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm">Follow-up Care</span>
                  <span className="text-sm font-medium">{averageRatings.followUp.toFixed(1)}</span>
                </div>
                <div className="w-full bg-muted-green rounded-full h-1.5">
                  <div
                    className="bg-primary h-1.5 rounded-full"
                    style={{ width: `${(averageRatings.followUp / 5) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>

            <div className="mt-8">
              <h4 className="font-medium mb-2">Would Recommend</h4>
              <div className="flex items-center gap-2">
                <ThumbsUp className="h-5 w-5 text-primary" />
                <p>
                  <span className="font-medium">{Math.round((averageRatings.recommendation / 5) * 100)}%</span> of
                  patients
                </p>
              </div>
            </div>

            <Button onClick={handleAddReview} className="w-full mt-8 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 text-foreground font-medium shadow-md hover:shadow-lg hover:shadow-green-500/30 transition-all duration-300">
              Write a Review
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Right column - Reviews list */}
      <div className="lg:col-span-2">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <h3 className="text-lg font-medium">
            {showVerifiedOnly ? "Verified Reviews" : "All Reviews"}
            <span className="text-sm text-muted-green ml-2">
              ({sortedReviews.length} {sortedReviews.length === 1 ? "review" : "reviews"})
            </span>
          </h3>

          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            {/* Verified Reviews Filter */}
            <div className="flex items-center space-x-2">
              <Switch
                id="verified-filter"
                checked={showVerifiedOnly}
                onCheckedChange={setShowVerifiedOnly}
              />
              <Label htmlFor="verified-filter" className="text-sm font-medium">
                Show Verified Reviews Only
              </Label>
            </div>

            {/* Sort Dropdown */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="recent">Most Recent</SelectItem>
                <SelectItem value="highest">Highest Rated</SelectItem>
                <SelectItem value="lowest">Lowest Rated</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {sortedReviews.length > 0 ? (
          <div className="space-y-6">
            {sortedReviews.map((review) => (
              <Card key={review.review_id} className="border-2 border-green-500/40 shadow-[0_0_10px_rgba(0,128,0,0.1)] hover:shadow-[0_0_15px_rgba(0,255,0,0.2)] hover:border-green-400/60 transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-4">
                        <User className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <p className="font-medium">{review.user?.username || "Anonymous"}</p>
                          {review.verification_status === 'verified' && (
                            <VerifiedVisitBadge size="sm" />
                          )}
                        </div>
                        <p className="text-sm text-muted-green">
                          {formatDistanceToNow(new Date(review.review_date), { addSuffix: true })}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="flex mr-2">{renderStars(review.rating)}</div>
                      <span className="font-medium">{review.rating.toFixed(1)}</span>
                    </div>
                  </div>

                  {review.additional_comments && (
                    <p className="mt-4 text-foreground">{review.additional_comments}</p>
                  )}

                  <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm text-muted-green">Clinical</p>
                      <div className="flex items-center">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="ml-1">{review.clinical_competence}</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-muted-green">Communication</p>
                      <div className="flex items-center">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="ml-1">{review.communication_stats}</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-muted-green">Empathy</p>
                      <div className="flex items-center">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="ml-1">{review.empathy_compassion}</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-muted-green">Time</p>
                      <div className="flex items-center">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="ml-1">{review.time_management}</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-muted-green">Follow-up</p>
                      <div className="flex items-center">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="ml-1">{review.follow_up_care}</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-muted-green">Recommend</p>
                      <div className="flex items-center">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="ml-1">{review.Recommendation || review.recommendation_rating}</span>
                      </div>
                    </div>
                  </div>

                  {/* Verification Request Button - Only show for user's own unverified reviews */}
                  {user &&
                   user.userId === review.user_id &&
                   review.verification_status === 'unverified' && (
                    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-foreground">Verify Your Visit</p>
                          <p className="text-xs text-muted-green">Upload your appointment receipt to verify this review</p>
                        </div>
                        <VerificationRequestModal
                          reviewId={review.review_id}
                          trigger={
                            <Button variant="outline" size="sm" className="gap-2">
                              <Upload className="h-4 w-4" />
                              Verify My Visit
                            </Button>
                          }
                        />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="p-6 text-center">
              <p className="text-muted-green mb-4">No reviews yet for this doctor.</p>
              <Button onClick={handleAddReview}>Be the First to Review</Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

