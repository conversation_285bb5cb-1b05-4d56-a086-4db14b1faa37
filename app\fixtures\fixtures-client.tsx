"use client"

import { <PERSON>, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { 
  Calendar, 
  Trophy, 
  Users, 
  Medal, 
  Clock, 
  MapPin, 
  Star, 
  Filter, 
  ChevronRight, 
  Activity,
  Stethoscope,
  Microscope,
  Heart,
  Brain,
  X
} from "lucide-react"
import { motion } from "framer-motion"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { useState } from "react"
import { format } from "date-fns"

interface Event {
  id: number;
  title: string;
  description: string;
  date: string;
  time?: string;
  location?: string;
  hosts?: string;
  country_id?: number;
}

interface Country {
  country_id: number;
  country_name: string;
}

interface FixturesClientProps {
  events: Event[];
  countries: Country[] | null;
}

// Championship events data for fallback
const championshipEvents = [
  {
    id: 1,
    title: "Annual Cardiology Championship",
    date: "2025-05-15",
    location: "Cleveland Clinic, Ohio",
    description: "The premier event for cardiac specialists, featuring team competitions in diagnostic accuracy, treatment planning, and patient outcomes."
  },
  {
    id: 2,
    title: "Surgical Excellence Tournament",
    date: "2025-06-10",
    location: "Johns Hopkins Hospital, Maryland",
    description: "Elite surgical teams compete in precision, innovation, and patient recovery metrics across multiple surgical specialties."
  },
  {
    id: 3,
    title: "International Medical Olympics",
    date: "2025-08-05",
    location: "Mayo Clinic, Minnesota",
    description: "The most prestigious global competition bringing together medical teams from around the world to compete across all specialties."
  }
];

// Specialty options for filter
const specialtyOptions = [
  { id: "cardiology", label: "Cardiology", icon: Heart },
  { id: "neurology", label: "Neurology", icon: Brain },
  { id: "neurosurgery", label: "Neurosurgery", icon: Brain },
  { id: "surgery", label: "Surgery", icon: Microscope },
  { id: "internal-medicine", label: "Internal Medicine", icon: Stethoscope },
  { id: "pediatrics", label: "Pediatrics", icon: Stethoscope },
  { id: "orthopedics", label: "Orthopedics", icon: Activity },
  { id: "dermatology", label: "Dermatology", icon: Activity },
  { id: "ophthalmology", label: "Ophthalmology", icon: Activity },
];

export default function FixturesClient({ events = championshipEvents, countries = [] }: FixturesClientProps) {
  return (
    <Tabs defaultValue="all" className="mb-8">
      <TabsList className="bg-background/50 border border-primary/20">
        <TabsTrigger value="all">All Events</TabsTrigger>
        {countries?.map(country => (
          <TabsTrigger key={country.country_id} value={country.country_id.toString()}>
            {country.country_name}
          </TabsTrigger>
        ))}
      </TabsList>

      <TabsContent value="all" className="mt-6">
        <div className="grid gap-6">
          {events.map((event) => (
            <EventCard key={event.id} event={event} />
          ))}
        </div>
      </TabsContent>

      {countries?.map(country => (
        <TabsContent key={country.country_id} value={country.country_id.toString()} className="mt-6">
          <div className="grid gap-6">
            {events
              .filter(event => event.country_id === country.country_id)
              .map((event) => (
                <EventCard key={event.id} event={event} />
              ))}
          </div>
        </TabsContent>
      ))}
    </Tabs>
  );
}

function EventCard({ event }: { event: Event }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="bg-gradient-to-b from-background/80 to-background/60 border border-primary/20 overflow-hidden">
        <div className="flex flex-col md:flex-row">
          <div className="w-full md:w-1/4 bg-primary/10 p-6 flex flex-col justify-center items-center">
            <div className="text-center">
              <p className="text-3xl font-bold text-foreground">
                {format(new Date(event.date), "dd")}
              </p>
              <p className="text-lg text-foreground/80">
                {format(new Date(event.date), "MMM yyyy")}
              </p>
            </div>
          </div>
          
          <CardContent className="flex-1 p-6">
            <h3 className="text-xl font-bold text-foreground mb-2">{event.title}</h3>
            <p className="text-foreground/70 mb-4">{event.description}</p>
            
            <div className="flex flex-wrap gap-4 text-sm text-foreground/80">
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4 text-primary" />
                <span>{event.time || "TBD"}</span>
              </div>
              <div className="flex items-center gap-1">
                <MapPin className="h-4 w-4 text-primary" />
                <span>{event.location || "TBD"}</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4 text-primary" />
                <span>{event.hosts || "Various Medical Specialists"}</span>
              </div>
            </div>
            
            <div className="mt-4 flex justify-end">
              <Button variant="outline" className="border-primary text-primary hover:bg-primary/20">
                Register Interest
              </Button>
            </div>
          </CardContent>
        </div>
      </Card>
    </motion.div>
  );
} 