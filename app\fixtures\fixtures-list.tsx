"use client"

import { useEffect, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { 
  Calendar, 
  Clock, 
  MapPin, 
  ChevronRight, 
  Activity,
  Stethoscope,
  Microscope,
  Heart,
  Brain
} from "lucide-react"
import { motion } from "framer-motion"

// Types for our fixtures data
interface Team {
  id: string
  name: string
  score: number | null
  logo?: string
}

interface Fixture {
  id: string
  title: string
  date: string
  location: string
  teams: Team[]
  type: string
  specialty: string
  status: 'upcoming' | 'completed' | 'live'
  league_id?: string
}

// Map specialties to icons
const specialtyIconMap: Record<string, any> = {
  "Cardiology": Heart,
  "Neurology": Brain,
  "Neurosurgery": Brain,
  "Surgery": Microscope,
  "Internal Medicine": Stethoscope,
  "Pediatrics": Stethoscope
}

function getSpecialtyIcon(specialty: string) {
  return specialtyIconMap[specialty] || Activity
}

function formatDate(dateString: string) {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  }).format(date)
}

function formatTime(dateString: string) {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true
  }).format(date)
}

function FixtureCard({ fixture, isResult = false }: { fixture: Fixture, isResult?: boolean }) {
  const Icon = getSpecialtyIcon(fixture.specialty)
  
  return (
    <motion.div
      whileHover={{ y: -5 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="bg-gradient-to-b from-background/80 to-background/60 border border-primary/20 shadow-lg overflow-hidden">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div className="flex items-center gap-2">
              <div className="p-2 rounded-full bg-primary/20">
                <Icon className="h-5 w-5 text-primary" />
              </div>
              <CardTitle className="text-lg text-foreground">{fixture.title}</CardTitle>
            </div>
            <div className="px-2 py-1 rounded-full bg-primary/10 text-xs font-medium text-primary border border-primary/20">
              {fixture.specialty}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-foreground/70 text-sm">
              <Calendar className="h-4 w-4 text-primary/70" />
              <span>{formatDate(fixture.date)}</span>
              <Clock className="h-4 w-4 text-primary/70 ml-2" />
              <span>{formatTime(fixture.date)}</span>
            </div>
            
            <div className="flex items-center gap-2 text-foreground/70 text-sm">
              <MapPin className="h-4 w-4 text-primary/70" />
              <span>{fixture.location}</span>
            </div>
            
            <div className="mt-4 space-y-3">
              {fixture.teams.map((team, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium">
                      {team.name.charAt(0)}
                    </div>
                    <span className="text-foreground">{team.name}</span>
                  </div>
                  {isResult ? (
                    <div className="flex items-center">
                      <div className={`px-3 py-1 rounded-full ${index === 0 && team.score !== null && fixture.teams[1].score !== null && team.score > fixture.teams[1].score ? 'bg-green-500/20 text-green-400' : index === 1 && team.score !== null && fixture.teams[0].score !== null && team.score > fixture.teams[0].score ? 'bg-green-500/20 text-green-400' : 'bg-card text-foreground/70'}`}>
                        {team.score}
                      </div>
                    </div>
                  ) : (
                    <div className="w-8 h-8 flex items-center justify-center">
                      <div className="w-2 h-2 rounded-full bg-primary/50"></div>
                    </div>
                  )}
                </div>
              ))}
            </div>
            
            <div className="pt-2">
              <Button variant="outline" className="w-full border-primary/30 text-primary hover:bg-primary/10 hover:text-foreground">
                {isResult ? "View Match Details" : "Match Preview"}
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

interface FixturesListProps {
  status: string;
  isResult?: boolean;
  specialties?: string[];
  types?: string[];
}

export default function FixturesList({ status, isResult = false, specialties, types }: FixturesListProps) {
  const [fixtures, setFixtures] = useState<Fixture[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchFixtures() {
      try {
        setLoading(true)
        
        // Build query parameters
        const params = new URLSearchParams();
        params.append('status', status);
        
        // Add specialty filters if provided
        if (specialties && specialties.length > 0) {
          specialties.forEach(specialty => {
            params.append('specialty', specialty);
          });
        }
        
        // Add type filters if provided
        if (types && types.length > 0) {
          types.forEach(type => {
            params.append('type', type);
          });
        }
        
        const response = await fetch(`/api/fixtures?${params.toString()}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch fixtures')
        }
        
        const data = await response.json()
        setFixtures(data.fixtures || [])
      } catch (err) {
        console.error('Error fetching fixtures:', err)
        setError('Failed to load fixtures. Please try again later.')
      } finally {
        setLoading(false)
      }
    }
    
    fetchFixtures()
  }, [status, specialties, types])

  if (error) {
    return (
      <div className="text-center py-10">
        <p className="text-red-400">{error}</p>
        <Button variant="outline" className="mt-4 border-primary/30 text-primary hover:bg-primary/10" onClick={() => window.location.reload()}>
          Try Again
        </Button>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="bg-gradient-to-b from-background/80 to-background/60 border border-primary/20 shadow-lg overflow-hidden animate-pulse">
            <CardHeader className="h-20"></CardHeader>
            <CardContent className="space-y-4">
              <div className="h-4 bg-primary/10 rounded"></div>
              <div className="h-4 bg-primary/10 rounded w-3/4"></div>
              <div className="h-20 bg-primary/5 rounded"></div>
              <div className="h-10 bg-primary/10 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (fixtures.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-foreground/70">No fixtures found for this category.</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {fixtures.map((fixture) => (
        <FixtureCard key={fixture.id} fixture={fixture} isResult={isResult} />
      ))}
    </div>
  )
} 