"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { ECGDivider } from "@/components/ecg-divider"
import { Calendar, Filter, Info, Trophy, ChevronRight } from "lucide-react"
import { Suspense, useState, useEffect } from "react"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import FixturesSection from "./fixtures-section"
import dynamic from 'next/dynamic'

// Dynamically import ChooseRoleDialog exactly like the header does
const ChooseRoleDialog = dynamic(() => import('@/components/registration/choose-role-dialog').then(mod => mod.ChooseRoleDialog), { ssr: false })

export default function FreshFixturesPage() {
  const [events, setEvents] = useState<any[]>([])
  const [countries, setCountries] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [showRegistration, setShowRegistration] = useState(false)
  const supabase = createClientComponentClient()

  // Fetch data from database tables
  useEffect(() => {
    async function fetchData() {
      try {
        const { data: eventsData, error: eventsError } = await supabase
          .from('medical_events')
          .select('*')
          .order('date', { ascending: true })

        const { data: countriesData, error: countriesError } = await supabase
          .from('countries')
          .select('*')

        if (eventsData) setEvents(eventsData)
        if (countriesData) setCountries(countriesData)
      } catch (error) {
        console.error('Error fetching data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background via-background/95 to-primary/5 text-foreground">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-4 dark:!text-white" style={{ color: 'hsl(142, 76%, 25%)' }}>
              Medical Events Calendar
            </h1>
            <p className="text-foreground/70 max-w-3xl mx-auto">
              Loading events...
            </p>
          </div>
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    )
  }

  // If no events data exists, show appropriate message
  if (!events || events.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background via-background/95 to-primary/5 text-foreground">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-4 dark:!text-white" style={{ color: 'hsl(142, 76%, 25%)' }}>
              Medical Events Calendar
            </h1>
            <p className="text-foreground/70 max-w-3xl mx-auto">
              Stay updated with the latest conferences, competitions, and healthcare events in the medical community.
            </p>
          </div>
          
          <ECGDivider className="my-10" />
          
          <Card className="max-w-3xl mx-auto" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
            <CardHeader>
              <CardTitle className="text-foreground flex items-center gap-2">
                <Info className="h-5 w-5 text-primary" />
                No Upcoming Events
              </CardTitle>
              <CardDescription className="text-foreground/70">
                Check back later for updates on medical events and competitions
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0 pb-6 text-center">
              <div className="p-8 text-foreground/80">
                <p className="mb-4">There are currently no scheduled medical events in our database.</p>
                <p>Please check back soon as we continuously update our calendar with the latest conferences, competitions, and healthcare gatherings.</p>
              </div>
              <Button 
                className="mt-4 bg-primary hover:bg-primary/90"
                onClick={() => setShowRegistration(true)}
              >
                Subscribe to Notifications
                <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            </CardContent>
          </Card>
          
          <ChooseRoleDialog open={showRegistration} onOpenChange={setShowRegistration} />
        </div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-background/95 to-primary/5 text-foreground">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 dark:!text-white" style={{ color: 'hsl(142, 76%, 25%)' }}>
            Medical Events Calendar
          </h1>
          <p className="text-foreground/70 max-w-3xl mx-auto">
            Stay updated with the latest conferences, competitions, and healthcare events in the medical community.
          </p>
        </div>
        
        {/* Feature cards - centered layout with consistent sizing */}
        <div className="flex flex-wrap justify-center gap-6 mb-12 max-w-6xl mx-auto">
          <Card className="flex-1 min-w-[250px] max-w-[300px]" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
            <CardHeader className="pb-2">
              <CardTitle className="text-foreground flex items-center gap-2">
                <Trophy className="h-5 w-5 text-primary" />
                Championships
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-foreground/70">Prestigious medical competitions showcasing excellence in healthcare.</p>
            </CardContent>
          </Card>

          <Card className="flex-1 min-w-[250px] max-w-[300px]" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
            <CardHeader className="pb-2">
              <CardTitle className="text-foreground flex items-center gap-2">
                <Calendar className="h-5 w-5 text-yellow-500" />
                Conferences
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-foreground/70">Knowledge-sharing events featuring renowned speakers and cutting-edge research.</p>
            </CardContent>
          </Card>

          <Card className="flex-1 min-w-[250px] max-w-[300px]" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
            <CardHeader className="pb-2">
              <CardTitle className="text-foreground flex items-center gap-2">
                <Filter className="h-5 w-5 text-primary" />
                Customizable
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-foreground/70">Filter events by country, specialty, and date to find exactly what you're looking for.</p>
            </CardContent>
          </Card>
        </div>
        
        <ECGDivider className="my-10" />
        
        <Suspense fallback={
          <div className="space-y-8 max-w-6xl mx-auto">
            <div className="bg-gradient-to-r from-primary/10 to-transparent p-4 rounded-lg mb-6">
              <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
                <div>
                  <Skeleton className="h-8 w-64 bg-primary/10 mb-2" />
                  <Skeleton className="h-4 w-96 bg-primary/5" />
                </div>
                <div className="flex gap-2">
                  <Skeleton className="h-8 w-24 bg-primary/10 rounded-full" />
                  <Skeleton className="h-8 w-32 bg-primary/10 rounded-full" />
                </div>
              </div>
            </div>
            
            <div className="space-y-6">
              {[1, 2, 3].map(i => (
                <Card key={i} className="bg-background/40 backdrop-blur-md border-primary/20 overflow-hidden">
                  <div className="flex flex-col md:flex-row animate-pulse">
                    <div className="w-full md:w-1/4 bg-primary/10 p-6 flex flex-col justify-center items-center">
                      <Skeleton className="h-16 w-16 rounded-full bg-primary/10 mb-2" />
                      <Skeleton className="h-4 w-20 bg-primary/5" />
                    </div>
                    <CardContent className="flex-1 p-6">
                      <Skeleton className="h-6 w-3/4 bg-primary/10 mb-4" />
                      <Skeleton className="h-4 w-full bg-primary/5 mb-2" />
                      <Skeleton className="h-4 w-2/3 bg-primary/5 mb-6" />
                      <div className="flex gap-4">
                        <Skeleton className="h-4 w-24 bg-primary/10 rounded-full" />
                        <Skeleton className="h-4 w-32 bg-primary/10 rounded-full" />
                      </div>
                    </CardContent>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        }>
          <div className="max-w-6xl mx-auto">
            <FixturesSection events={events} countries={countries} />
          </div>
        </Suspense>
        
        <ChooseRoleDialog open={showRegistration} onOpenChange={setShowRegistration} />
      </div>
    </div>
  )
}
