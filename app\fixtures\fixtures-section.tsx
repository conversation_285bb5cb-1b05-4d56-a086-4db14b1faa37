"use client";

import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardDescription, 
  CardFooter 
} from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card"
import { 
  Clock, 
  MapPin, 
  Users,
  Heart,
  Brain,
  Calendar,
  Trophy,
  Tag,
  User,
  ChevronRight,
  Share2
} from "lucide-react"
import { motion } from "framer-motion"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { format, isPast, isToday, parseISO } from "date-fns"
import Link from 'next/link'

interface Event {
  id: number;
  title: string;
  description: string;
  date: string;
  time?: string;
  location?: string;
  hosts?: string;
  country_id?: number;
  category?: string;
  attendees?: number;
}

interface Country {
  country_id: number;
  country_name: string;
}

interface FixturesSectionProps {
  events: Event[];
  countries: Country[] | null;
}

// Championship events data for fallback
const championshipEvents = [
  {
    id: 1,
    title: "Annual Cardiology Championship",
    date: "2025-05-15",
    location: "Cleveland Clinic, Ohio",
    description: "The premier event for cardiac specialists, featuring team competitions in diagnostic accuracy, treatment planning, and patient outcomes.",
    category: "Championship",
    attendees: 250
  },
  {
    id: 2,
    title: "Surgical Excellence Tournament",
    date: "2025-06-10",
    location: "Johns Hopkins Hospital, Maryland",
    description: "Elite surgical teams compete in precision, innovation, and patient recovery metrics across multiple surgical specialties.",
    category: "Tournament",
    attendees: 180
  },
  {
    id: 3,
    title: "International Medical Olympics",
    date: "2025-08-05",
    location: "Mayo Clinic, Minnesota",
    description: "The most prestigious global competition bringing together medical teams from around the world to compete across all specialties.",
    category: "Olympics",
    attendees: 500
  }
];

// Get a random image for an event
const getEventImage = (eventId: number) => {
  const images = [
    "/event-images/medical-event-1.jpg",
    "/event-images/medical-event-2.jpg",
    "/event-images/medical-event-3.jpg",
    "/event-images/medical-event-4.jpg"
  ];
  return images[eventId % images.length] || images[0];
};

// Get icon by category
const getCategoryIcon = (category: string) => {
  switch (category?.toLowerCase()) {
    case 'championship':
      return <Trophy className="w-4 h-4" />;
    case 'tournament':
      return <Users className="w-4 h-4" />;
    case 'olympics':
      return <Trophy className="w-4 h-4" />;
    default:
      return <Calendar className="w-4 h-4" />;
  }
};

export default function FixturesSection({ events = championshipEvents, countries = [] }: FixturesSectionProps) {
  const categories = [...new Set(events.map(e => e.category || "Event"))];
  
  return (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-primary/10 to-transparent p-4 rounded-lg mb-6">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold text-foreground flex items-center gap-2">
              <Calendar className="h-6 w-6 text-primary" />
              Upcoming Medical Events
            </h2>
            <p className="text-foreground/70">Join renowned specialists in prestigious medical competitions</p>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {categories.map(category => (
              <Badge key={category} variant="outline" className="bg-background/50 text-foreground border-primary/30">
                {getCategoryIcon(category)}
                <span className="ml-1">{category}</span>
              </Badge>
            ))}
          </div>
        </div>
      </div>
      
      <Tabs defaultValue="all" className="mb-8">
        <TabsList className="bg-background/50 border border-primary/20 h-auto p-1 flex flex-wrap">
          <TabsTrigger value="all" className="data-[state=active]:bg-primary/30 rounded-md">All Events</TabsTrigger>
          {countries?.map(country => (
            <TabsTrigger 
              key={country.country_id} 
              value={country.country_id.toString()}
              className="data-[state=active]:bg-primary/30 rounded-md"
            >
              {country.country_name}
            </TabsTrigger>
          ))}
        </TabsList>
        
        <TabsContent value="all" className="mt-6">
          <div className="grid gap-6">
            {events.map((event) => (
              <EventCard key={event.id} event={event} />
            ))}
          </div>
        </TabsContent>
        
        {countries?.map(country => (
          <TabsContent key={country.country_id} value={country.country_id.toString()} className="mt-6">
            <div className="grid gap-6">
              {events
                .filter(event => event.country_id === country.country_id)
                .map((event) => (
                  <EventCard key={event.id} event={event} />
                ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}

function EventCard({ event }: { event: Event }) {
  const eventDate = parseISO(event.date);
  const isPastEvent = isPast(eventDate) && !isToday(eventDate);
  const badgeVariant = isPastEvent ? "outline" : "default";
  const badgeClass = isPastEvent 
    ? "bg-background/90/50 text-muted-green border-border"
    : "bg-primary/20 text-primary border-primary/30";
    
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="bg-gradient-to-b from-background/80 to-background/60 border border-primary/20 overflow-hidden hover:border-primary/40 transition-colors duration-300 group">
        <div className="flex flex-col md:flex-row">
          <div className="w-full md:w-1/4 bg-primary/10 p-6 flex flex-col justify-center items-center relative overflow-hidden">
            <div className="absolute inset-0 opacity-20 group-hover:opacity-30 transition-opacity duration-500">
              <div className="absolute inset-0 bg-gradient-to-t from-background via-transparent to-transparent z-10"></div>
              <img 
                src={getEventImage(event.id)} 
                alt={event.title}
                className="object-cover w-full h-full"
              />
            </div>
            
            <div className="text-center relative z-10">
              <Badge variant={badgeVariant} className={`mb-2 ${badgeClass}`}>
                {isPastEvent ? "Past Event" : "Upcoming"}
              </Badge>
              <p className="text-4xl font-bold text-foreground">
                {format(parseISO(event.date), "dd")}
              </p>
              <p className="text-lg text-foreground/80">
                {format(parseISO(event.date), "MMM yyyy")}
              </p>
              {event.category && (
                <div className="mt-2 inline-flex items-center rounded-full bg-background/40 px-2.5 py-1 text-xs text-foreground/90">
                  {getCategoryIcon(event.category)}
                  <span className="ml-1">{event.category}</span>
                </div>
              )}
            </div>
          </div>
          
          <CardContent className="flex-1 p-6">
            <CardTitle className="text-xl font-bold text-foreground mb-2 group-hover:text-primary transition-colors">
              {event.title}
            </CardTitle>
            <CardDescription className="text-foreground/70 mb-4 line-clamp-2">
              {event.description}
            </CardDescription>
            
            <Separator className="my-4 bg-card" />
            
            <div className="flex flex-wrap gap-4 text-sm text-foreground/80">
              <HoverCard>
                <HoverCardTrigger asChild>
                  <div className="flex items-center gap-1 cursor-help">
                    <Clock className="h-4 w-4 text-primary" />
                    <span>{event.time || "TBD"}</span>
                  </div>
                </HoverCardTrigger>
                <HoverCardContent className="bg-background/90 border-primary/30 text-foreground">
                  <div className="text-sm">
                    <div className="font-medium">Event Time</div>
                    <div className="text-foreground/70">
                      {event.time || "Time to be determined"} on {format(parseISO(event.date), "EEEE, MMMM do, yyyy")}
                    </div>
                  </div>
                </HoverCardContent>
              </HoverCard>
              
              <HoverCard>
                <HoverCardTrigger asChild>
                  <div className="flex items-center gap-1 cursor-help">
                    <MapPin className="h-4 w-4 text-primary" />
                    <span>{event.location || "TBD"}</span>
                  </div>
                </HoverCardTrigger>
                <HoverCardContent className="bg-background/90 border-primary/30 text-foreground">
                  <div className="text-sm">
                    <div className="font-medium">Event Location</div>
                    <div className="text-foreground/70">{event.location || "Location to be announced"}</div>
                  </div>
                </HoverCardContent>
              </HoverCard>
              
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4 text-primary" />
                <span>{event.hosts || "Various Medical Specialists"}</span>
              </div>
              
              {event.attendees && (
                <div className="flex items-center gap-1">
                  <User className="h-4 w-4 text-primary" />
                  <span>{event.attendees} Registered</span>
                </div>
              )}
            </div>
            
            <div className="mt-6 flex justify-between items-center">
              <Badge variant="outline" className="bg-background/30 text-foreground/80 border-border">
                {format(parseISO(event.date), "EEEE")}
              </Badge>
              
              <div className="flex gap-2">
                <Button size="sm" variant="outline" className="border-primary/30">
                  <Share2 className="h-4 w-4 mr-1" /> Share
                </Button>
                <Link href="/become-a-member">
                  <Button variant="default" className="bg-primary hover:bg-primary/80">
                    Register Interest
                    <ChevronRight className="ml-1 h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </div>
      </Card>
    </motion.div>
  );
} 