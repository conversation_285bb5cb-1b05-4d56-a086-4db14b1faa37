"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { AlertTriangle, CheckCircle, ArrowLeft, Mail } from "lucide-react"
import Link from "next/link"

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [resetEmailSent, setResetEmailSent] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email) {
      setError("Please enter your email address")
      return
    }
    
    setLoading(true)
    setError(null)
    
    try {
      console.log(`Requesting password reset for ${email}`);
      
      // Use our custom forgot-password endpoint
      const response = await fetch('/api/auth/custom/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        setError(result.error || "Failed to send reset email")
      } else {
        // Success
        console.log("Password reset request complete");
        setResetEmailSent(true);
      }
    } catch (err: any) {
      console.error("Password reset error:", err);
      setError(err.message || "An unexpected error occurred")
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-background p-4">
      <div className="w-full max-w-md bg-background/60 backdrop-blur-md p-8 rounded-xl border border-primary/20 shadow-xl">
        <div className="mb-6 text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">Forgot Password</h1>
          <p className="text-muted-green">Enter your email to receive a password reset link</p>
        </div>
        
        {resetEmailSent ? (
          <div className="space-y-4">
            <div className="p-4 rounded-md bg-green-500/20 border border-green-500/50 flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-green-500 shrink-0 mt-0.5" />
              <div>
                <p className="text-foreground font-medium">Reset Link Sent!</p>
                <p className="text-sm text-foreground/80 mt-1">
                  Please check your email and follow the instructions to reset your password.
                </p>
              </div>
            </div>
            
            <div className="flex justify-center mt-4">
              <Link href="/">
                <Button className="bg-primary hover:bg-primary/90 text-foreground">
                  Back to Home
                </Button>
              </Link>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <div className="p-3 rounded-md bg-red-500/20 border border-red-500/50 flex items-start gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500 shrink-0 mt-0.5" />
                <p className="text-sm text-foreground">{error}</p>
              </div>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="email" className="text-foreground">
                Email
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="bg-card border-border text-card-foreground placeholder:text-foreground/50"
              />
            </div>
            
            <Button
              type="submit"
              className="w-full bg-primary hover:bg-primary/90 text-foreground"
              disabled={loading}
            >
              {loading ? (
                <>
                  <div className="animate-spin mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                  Sending...
                </>
              ) : (
                <>
                  <Mail className="mr-2 h-4 w-4" /> Send Reset Link
                </>
              )}
            </Button>
            
            <div className="text-center mt-4">
              <Link 
                href="/"
                className="inline-flex items-center text-sm text-primary hover:text-primary/80"
              >
                <ArrowLeft className="w-4 h-4 mr-1" />
                Back to Home
              </Link>
            </div>
          </form>
        )}
      </div>
    </div>
  )
} 