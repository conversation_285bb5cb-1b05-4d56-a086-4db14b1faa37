@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ensure smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Add padding to anchors to account for fixed header */
:target {
  scroll-margin-top: 100px;
}

:root {
  --background: 142 30% 95%;
  --foreground: 142 60% 20%;

  --card: 0 0% 100%;
  --card-foreground: 142 60% 20%;

  --popover: 0 0% 100%;
  --popover-foreground: 142 60% 20%;

  --primary: 123 41% 49%;
  --primary-foreground: 0 0% 100%;

  --secondary: 60 100% 50%;
  --secondary-foreground: 142 50% 25%;

  --muted: 0 0% 96%;
  --muted-foreground: 142 40% 35%;

  --accent: 0 100% 50%;
  --accent-foreground: 142 60% 20%;

  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 100%;

  --border: 0 0% 90%;
  --input: 0 0% 90%;
  --ring: 123 41% 49%;

  --radius: 0.5rem;

  /* Semantic colors for light theme */
  --success: 120 100% 50%;
  --success-foreground: 0 0% 100%;
  --warning: 45 100% 50%;
  --warning-foreground: 142 60% 20%;
  --error: 0 100% 50%;
  --error-foreground: 0 0% 100%;
  --info: 200 100% 50%;
  --info-foreground: 0 0% 100%;
}

.dark {
  --background: 142 70% 15%;  /* Dark green background for dark theme */
  --foreground: 0 0% 100%;

  --card: 0 0% 13%;
  --card-foreground: 0 0% 100%;

  --popover: 0 0% 13%;
  --popover-foreground: 0 0% 100%;

  --primary: 123 41% 49%;
  --primary-foreground: 0 0% 100%;

  --secondary: 60 100% 50%;
  --secondary-foreground: 0 0% 13%;

  --muted: 0 0% 25%;
  --muted-foreground: 0 0% 80%;

  --accent: 0 100% 50%;
  --accent-foreground: 0 0% 100%;

  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 100%;

  --border: 0 0% 20%;
  --input: 0 0% 20%;
  --ring: 123 41% 49%;

  /* Semantic colors for dark theme */
  --success: 120 100% 40%;
  --success-foreground: 0 0% 100%;
  --warning: 45 100% 60%;
  --warning-foreground: 0 0% 15%;
  --error: 0 100% 60%;
  --error-foreground: 0 0% 100%;
  --info: 200 100% 60%;
  --info-foreground: 0 0% 100%;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif,
      "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-bold;
  }
}

/* Custom blog content styling */
.blog-content {
  line-height: 1.7;
  font-size: 1.1rem;
}

.blog-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: hsl(var(--foreground));
  margin-top: 2rem;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.blog-content h2 {
  font-size: 2rem;
  font-weight: 700;
  color: hsl(var(--foreground));
  margin-top: 2rem;
  margin-bottom: 1.5rem;
  line-height: 1.3;
}

.blog-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: hsl(var(--foreground));
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.blog-content h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: hsl(var(--foreground));
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.blog-content p {
  color: hsl(var(--muted-foreground));
  margin-bottom: 1.5rem;
  line-height: 1.7;
}

.blog-content ul {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
  list-style-type: disc;
}

.blog-content ol {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
  list-style-type: decimal;
}

.blog-content li {
  color: hsl(var(--muted-foreground));
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.blog-content strong {
  color: hsl(var(--foreground));
  font-weight: 600;
}

.blog-content em {
  color: hsl(var(--muted-foreground));
  font-style: italic;
}

.blog-content a {
  color: hsl(var(--primary));
  text-decoration: none;
  transition: color 0.2s;
}

.blog-content a:hover {
  color: hsl(var(--primary) / 0.8);
}

.blog-content hr {
  border: none;
  border-top: 1px solid hsl(var(--border));
  margin: 2rem 0;
}

.blog-content blockquote {
  border-left: 4px solid hsl(var(--primary));
  background-color: hsl(var(--muted));
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  border-radius: 0 8px 8px 0;
  color: hsl(var(--muted-foreground));
}

.blog-content code {
  background-color: hsl(var(--muted));
  color: hsl(var(--primary));
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.blog-content pre {
  background-color: hsl(var(--card));
  color: hsl(var(--card-foreground));
  padding: 1rem;
  border-radius: 8px;
  overflow-x: auto;
  margin: 1.5rem 0;
  border: 1px solid hsl(var(--border));
}

.blog-content img {
  border-radius: 8px;
  border: 1px solid hsl(var(--border));
  max-width: 100%;
  height: auto;
  margin: 1.5rem 0;
  display: block;
  box-shadow: 0 4px 6px -1px hsl(var(--background) / 0.1), 0 2px 4px -1px hsl(var(--background) / 0.06);
}

/* Custom styled call-to-action boxes - Fix all light background issues */
.blog-content div[style*="background-color: #f4f4f4"],
.blog-content div[style*="background-color:#f4f4f4"],
.blog-content div[style*="background-color: #f9f9f9"],
.blog-content div[style*="background-color:#f9f9f9"],
.blog-content div[style*="background-color: #e8f4f8"],
.blog-content div[style*="background-color:#e8f4f8"],
.blog-content div[style*="background-color: #f2f8ff"],
.blog-content div[style*="background-color:#f2f8ff"] {
  background-color: hsl(var(--primary) / 0.15) !important;
  border-left: 4px solid hsl(var(--primary)) !important;
  border-radius: 8px;
  padding: 1.5rem !important;
  margin: 2rem 0 !important;
  border: 1px solid hsl(var(--primary) / 0.3) !important;
}

.blog-content div[style*="background-color: #f4f4f4"] h4,
.blog-content div[style*="background-color:#f4f4f4"] h4,
.blog-content div[style*="background-color: #f9f9f9"] h4,
.blog-content div[style*="background-color:#f9f9f9"] h4,
.blog-content div[style*="background-color: #e8f4f8"] h4,
.blog-content div[style*="background-color:#e8f4f8"] h4,
.blog-content div[style*="background-color: #f2f8ff"] h4,
.blog-content div[style*="background-color:#f2f8ff"] h4 {
  color: hsl(var(--foreground)) !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  margin-bottom: 1rem !important;
  margin-top: 0 !important;
}

.blog-content div[style*="background-color: #f4f4f4"] p,
.blog-content div[style*="background-color:#f4f4f4"] p,
.blog-content div[style*="background-color: #f9f9f9"] p,
.blog-content div[style*="background-color:#f9f9f9"] p,
.blog-content div[style*="background-color: #e8f4f8"] p,
.blog-content div[style*="background-color:#e8f4f8"] p,
.blog-content div[style*="background-color: #f2f8ff"] p,
.blog-content div[style*="background-color:#f2f8ff"] p {
  color: hsl(var(--foreground)) !important;
  margin-bottom: 0 !important;
}

.blog-content div[style*="background-color: #f4f4f4"] a,
.blog-content div[style*="background-color:#f4f4f4"] a,
.blog-content div[style*="background-color: #f9f9f9"] a,
.blog-content div[style*="background-color:#f9f9f9"] a,
.blog-content div[style*="background-color: #e8f4f8"] a,
.blog-content div[style*="background-color:#e8f4f8"] a,
.blog-content div[style*="background-color: #f2f8ff"] a,
.blog-content div[style*="background-color:#f2f8ff"] a {
  color: hsl(var(--primary)) !important;
  font-weight: 600 !important;
}

/* Call-to-action highlight box styling */
.blog-content .cta-highlight {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(16, 185, 129, 0.05) 100%);
  border: 2px solid #10B981;
  border-radius: 12px;
  padding: 1.5rem 2rem;
  margin: 2.5rem 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.1);
}

.blog-content .cta-highlight::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #10B981, #059669);
}

.blog-content .cta-highlight p {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  color: white !important;
  text-align: center;
  line-height: 1.6;
}

.blog-content .cta-highlight a {
  color: #10B981 !important;
  font-weight: 700 !important;
  text-decoration: none !important;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(16, 185, 129, 0.1));
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  border: 1px solid rgba(16, 185, 129, 0.3);
  transition: all 0.3s ease;
  display: inline-block;
  margin: 0 0.25rem;
}

.blog-content .cta-highlight a:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(16, 185, 129, 0.2));
  border-color: #10B981;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

/* Add custom font face for Obelix Pro */
@font-face {
  font-family: 'Obelix Pro';
  src: url('/fonts/ObelixProB-cyr.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

/* Custom classes for hero text with Obelix Pro font */
.hero-title-custom {
  font-family: 'Obelix Pro', 'Inter Variable', 'Inter', system-ui, -apple-system, sans-serif !important;
}

.about-overlay-text-custom {
  font-family: 'Obelix Pro', 'Inter Variable', 'Inter', system-ui, -apple-system, sans-serif !important;
}

/* Dark theme gradient background */
.dark .dark-theme-gradient {
  background: linear-gradient(
    to bottom,
    hsl(0, 0%, 0%) 0%,
    hsl(120, 25%, 8%) 25%,
    hsl(120, 30%, 12%) 50%,
    hsl(120, 35%, 16%) 75%,
    hsl(120, 40%, 20%) 100%
  ) !important;
}

/* Dark theme footer styling */
.dark .footer-dark-theme {
  background: hsl(120, 40%, 4%) !important;
  border-color: hsl(120, 30%, 8%) !important;
}

/* Dark theme hero section background matching header */
.dark .hero-header-match {
  background: linear-gradient(to right,
    hsl(var(--background) / 0.9),
    hsl(var(--background) / 0.95),
    hsl(var(--background) / 0.9)
  ) !important;
}

/* Dark theme popup background adjustments */
.dark .popup-dark-blue {
  background: rgba(30, 58, 138, 0.95) !important;
}

.dark .popup-patient-blue {
  background: rgba(30, 58, 138, 0.95) !important;
}

.dark .popup-doctor-green {
  background: rgba(5, 46, 22, 0.95) !important;
}

/* Head-to-head title styling for both themes */
.head-to-head-title {
  color: hsl(142, 76%, 25%);
  background: rgba(255, 255, 255, 0.95);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  text-shadow: none;
}

/* Dark theme head-to-head title styling */
.dark .head-to-head-title {
  color: #ffffff !important;
  background: transparent !important;
  padding: 0 !important;
  border-radius: 0 !important;
  text-shadow: none !important;
}

/* Dark theme help center title styling */
.dark h1[style*="color: hsl(142, 76%, 25%)"] {
  color: #ffffff !important;
}

/* Dark theme navigation menu text styling */
.dark .nav-menu-item {
  color: #ffffff !important;
}

/* Dark theme card backgrounds and frames */
.dark .card,
.dark [data-card],
.dark .bg-card {
  background: hsl(142, 70%, 15%) !important;
  border: 2px solid hsl(142, 50%, 50%) !important;
}

/* Dark theme statistics boxes */
.dark .stats-card,
.dark .statistics-card,
.dark [data-stats-card] {
  background: hsl(142, 70%, 15%) !important;
  border: 2px solid hsl(142, 50%, 50%) !important;
}

/* Dark theme ensure text visibility */
.dark .text-gray-600,
.dark .text-gray-700,
.dark .text-muted,
.dark .text-muted-foreground {
  color: #ffffff !important;
}

/* Dark theme links on dark green background */
.dark a[style*="color: hsl(142"],
.dark .text-primary {
  color: #ffffff !important;
}

/* Dark theme green icons to white */
.dark .text-green-500,
.dark .text-green-600,
.dark .text-primary {
  color: #ffffff !important;
}

/* Dark theme specific page styles */

/* Help page title */
.dark h1[style*="hsl(142, 76%, 25%)"] {
  color: #ffffff !important;
}

/* Teams page title */
.dark h1[class*="dark:!text-white"] {
  color: #ffffff !important;
}

/* About page frames - dark green background with light green borders */
.dark .card-content-frame,
.dark .about-card,
.dark .mission-card,
.dark .ranking-card {
  background: hsl(142, 70%, 15%) !important;
  border: 2px solid hsl(142, 50%, 50%) !important;
}

/* Standings page - card backgrounds and text visibility */
.dark [class*="bg-background/40"],
.dark [class*="bg-background/60"],
.dark [class*="bg-background/80"] {
  background: hsl(142, 70%, 15%) !important;
  border: 2px solid hsl(142, 50%, 50%) !important;
}

/* Standings page - ensure link visibility */
.dark a:not(.nav-menu-item) {
  color: #ffffff !important;
}

.dark .text-primary:not(.nav-menu-item) {
  color: #ffffff !important;
}

/* Teams page statistics boxes */
.dark .bg-gradient-to-b {
  background: hsl(142, 70%, 15%) !important;
  border: 2px solid hsl(142, 50%, 50%) !important;
}

/* Teams page icons - change yellow and green to white */
.dark .text-yellow-500 {
  color: #ffffff !important;
}

/* Fixtures page rectangular boxes */
.dark .card,
.dark .bg-gradient-to-r {
  background: hsl(142, 70%, 15%) !important;
  border: 2px solid hsl(142, 50%, 50%) !important;
}

/* General text visibility on dark green backgrounds */
.dark [class*="text-foreground/70"],
.dark [class*="text-foreground/80"],
.dark [class*="text-foreground/60"] {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* Navigation and footer dark green background */
.dark header,
.dark footer,
.dark nav {
  background: hsl(142, 70%, 15%) !important;
}

/* Specific footer dark theme styling */
.dark .footer-dark-theme {
  background: hsl(142, 70%, 15%) !important;
  border-color: hsl(142, 50%, 50%) !important;
}

/* Patient forms and popups - BLUE theme (not green) */
.dark .patient-registration-dialog,
.dark .patient-form,
.dark .patient-popup,
.dark [data-patient-form="true"] {
  background: linear-gradient(135deg, hsl(220, 80%, 25%) 0%, hsl(220, 70%, 20%) 100%) !important;
  border: 2px solid hsl(220, 60%, 50%) !important;
}

/* Patient registration specific styling */
.dark .patient-registration-dialog .medical-sports-frame {
  background: linear-gradient(135deg, hsl(220, 80%, 25%) 0%, hsl(220, 70%, 20%) 100%) !important;
  border: 2px solid hsl(220, 60%, 50%) !important;
}

/* Patient form buttons */
.dark .patient-registration-dialog .medical-sports-button,
.dark [data-patient-form="true"] button {
  background: linear-gradient(135deg, hsl(220, 70%, 45%) 0%, hsl(220, 60%, 40%) 100%) !important;
  border: 1px solid hsl(220, 60%, 50%) !important;
  color: #ffffff !important;
}

/* Patient form inputs */
.dark .patient-registration-dialog .medical-sports-input,
.dark [data-patient-form="true"] input,
.dark [data-patient-form="true"] select {
  background: hsl(220, 40%, 15%) !important;
  border: 1px solid hsl(220, 60%, 40%) !important;
  color: #ffffff !important;
}

/* Patient gender selection buttons */
.dark .patient-registration-dialog [class*="bg-blue-600"] {
  background: hsl(220, 70%, 45%) !important;
  border: 2px solid hsl(220, 60%, 60%) !important;
}

/* Remove unwanted lines/borders near navigation items (especially Standings) */
.dark .nav-menu-item::after,
.dark .nav-menu-item::before,
.dark nav a::after,
.dark nav a::before,
.dark nav button::after,
.dark nav button::before {
  display: none !important;
}

/* Remove any unwanted green lines or borders in navigation */
.dark nav *[class*="border"],
.dark nav *[class*="underline"] {
  border: none !important;
  text-decoration: none !important;
}

/* Dark theme comprehensive fixes - phase 2 */

/* Remove ANY navigation lines, borders, or pseudo-elements that could cause green lines */
.dark nav *::before,
.dark nav *::after,
.dark .nav-menu-item::before,
.dark .nav-menu-item::after,
.dark [href="/standings"]::before,
.dark [href="/standings"]::after,
.dark button[class*="nav"]::before,
.dark button[class*="nav"]::after {
  display: none !important;
  content: none !important;
  border: none !important;
  background: none !important;
}

/* Comprehensive navigation line removal */
.dark nav a,
.dark nav button,
.dark .nav-menu-item,
.dark [href="/standings"],
.dark [class*="navigation"] {
  border-bottom: none !important;
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
  text-decoration: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Remove any gradient or background lines */
.dark nav .absolute,
.dark nav [class*="gradient"],
.dark nav [class*="bg-gradient"] {
  display: none !important;
}

/* PATIENT FORMS COMPREHENSIVE BLUE THEME - NUCLEAR OPTION */
/* Target all patient-related dialogs, forms, and components */

/* Patient registration dialog main container */
.dark [class*="patient"] [class*="dialog"],
.dark [data-patient-form="true"],
.dark [data-patient-dialog="true"],
.dark .patient-registration-dialog,
.dark [class*="PatientRegistration"] {
  background: hsl(220, 80%, 25%) !important;
  border: 2px solid hsl(220, 60%, 50%) !important;
  box-shadow: 0 20px 60px rgba(59, 130, 246, 0.3) !important;
}

/* Patient form frames and containers */
.dark [class*="patient"] [class*="frame"],
.dark [data-patient="true"] [class*="frame"],
.dark [variant="patient"] {
  background: linear-gradient(135deg, hsl(220, 80%, 25%) 0%, hsl(220, 75%, 20%) 100%) !important;
  border: 2px solid hsl(220, 60%, 50%) !important;
}

/* Patient form inputs */
.dark [class*="patient"] input,
.dark [data-patient-form="true"] input,
.dark [data-patient="true"] input {
  background: hsl(220, 70%, 15%) !important;
  border: 1px solid hsl(220, 50%, 40%) !important;
  color: #ffffff !important;
}

.dark [class*="patient"] input:focus,
.dark [data-patient-form="true"] input:focus,
.dark [data-patient="true"] input:focus {
  border-color: hsl(220, 60%, 60%) !important;
  box-shadow: 0 0 0 2px hsl(220, 60%, 50%, 0.3) !important;
}

/* Patient form buttons */
.dark [class*="patient"] button,
.dark [data-patient-form="true"] button,
.dark [data-patient="true"] button {
  background: linear-gradient(135deg, hsl(220, 70%, 45%) 0%, hsl(220, 75%, 40%) 100%) !important;
  border: 1px solid hsl(220, 60%, 50%) !important;
  color: #ffffff !important;
}

.dark [class*="patient"] button:hover,
.dark [data-patient-form="true"] button:hover,
.dark [data-patient="true"] button:hover {
  background: linear-gradient(135deg, hsl(220, 75%, 50%) 0%, hsl(220, 80%, 45%) 100%) !important;
  box-shadow: 0 5px 15px rgba(59, 130, 246, 0.4) !important;
}

/* Patient registration titles and text */
.dark [class*="patient"] h1,
.dark [class*="patient"] h2,
.dark [class*="patient"] h3,
.dark [data-patient-form="true"] h1,
.dark [data-patient-form="true"] h2,
.dark [data-patient-form="true"] h3 {
  color: hsl(220, 70%, 85%) !important;
}

/* Patient form labels */
.dark [class*="patient"] label,
.dark [data-patient-form="true"] label,
.dark [data-patient="true"] label {
  color: hsl(220, 60%, 75%) !important;
}

/* Choose role dialog - patient option */
.dark [class*="choose-role"] button[class*="blue"],
.dark [class*="patient"][class*="button"],
.dark button[onclick*="patient"],
.dark [aria-label*="patient"] {
  background: linear-gradient(135deg, hsl(220, 70%, 35%) 0%, hsl(220, 75%, 30%) 100%) !important;
  border: 2px solid hsl(220, 60%, 50%) !important;
  color: #ffffff !important;
}

.dark [class*="choose-role"] button[class*="blue"]:hover,
.dark [class*="patient"][class*="button"]:hover,
.dark button[onclick*="patient"]:hover {
  background: linear-gradient(135deg, hsl(220, 75%, 40%) 0%, hsl(220, 80%, 35%) 100%) !important;
  border-color: hsl(220, 65%, 60%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
}

/* Patient login forms */
.dark [data-user-type="patient"],
.dark [userType="patient"],
.dark .patient-login-form {
  background: hsl(220, 80%, 25%) !important;
  border: 2px solid hsl(220, 60%, 50%) !important;
}

/* Specific targeting for patient registration dialog content */
.dark [class*="PatientRegistrationDialog"] [class*="DialogContent"],
.dark [class*="patient-registration"] [class*="content"] {
  background: linear-gradient(145deg, hsl(220, 85%, 25%) 0%, hsl(220, 80%, 20%) 100%) !important;
  border: 3px solid hsl(220, 65%, 55%) !important;
  box-shadow:
    0 25px 80px rgba(59, 130, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

/* Remove any green tinting from patient elements */
.dark [class*="patient"] *[class*="green"],
.dark [data-patient="true"] *[class*="green"],
.dark [class*="patient"] *[style*="142"],
.dark [data-patient="true"] *[style*="142"] {
  background: hsl(220, 70%, 30%) !important;
  border-color: hsl(220, 60%, 50%) !important;
  color: hsl(220, 70%, 85%) !important;
}

/* Target specific patient registration signup text */
.dark [class*="patient"] *:contains("REFEREE SIGN-UP"),
.dark h2:contains("REFEREE SIGN-UP") {
  color: hsl(220, 70%, 85%) !important;
}

/* Additional navigation line removal - ultra specific */
.dark header nav *,
.dark .header-client *,
.dark [class*="HeaderClient"] * {
  border-image: none !important;
  border-image-source: none !important;
}

/* Remove any standing-specific styling that might cause lines */
.dark [href="/standings"],
.dark a[href="/standings"],
.dark *[href="/standings"] {
  position: relative !important;
}

.dark [href="/standings"] span,
.dark a[href="/standings"] span,
.dark *[href="/standings"] span {
  border-bottom: none !important;
  text-decoration: none !important;
  box-shadow: none !important;
}

/* ========================================
   PATIENT BLUE THEME - COMPREHENSIVE FIX
   ======================================== */

/* CHOOSE ROLE DIALOG - Patient Button Pure Blue */
.dark [data-patient="true"],
.dark button[data-patient="true"],
.dark [class*="choose-role"] [data-patient="true"] {
  background: linear-gradient(135deg, hsl(220, 70%, 35%) 0%, hsl(220, 75%, 30%) 100%) !important;
  border: 2px solid hsl(220, 60%, 50%) !important;
  color: #ffffff !important;
}

.dark [data-patient="true"]:hover,
.dark button[data-patient="true"]:hover,
.dark [class*="choose-role"] [data-patient="true"]:hover {
  background: linear-gradient(135deg, hsl(220, 75%, 40%) 0%, hsl(220, 80%, 35%) 100%) !important;
  border-color: hsl(220, 65%, 60%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
}

/* MEDICAL SPORTS FRAME - Patient Variant Override */
.dark [variant="patient"] {
  background: linear-gradient(135deg, hsl(220, 80%, 25%) 0%, hsl(220, 75%, 20%) 100%) !important;
  border: 2px solid hsl(220, 60%, 50%) !important;
  box-shadow: 0 20px 60px rgba(59, 130, 246, 0.3) !important;
}

/* PATIENT REGISTRATION MODAL - Complete Blue Override */
.dark [data-patient-dialog="true"] {
  background: transparent !important;
}

.dark [data-patient-dialog="true"] [class*="MedicalSportsFrame"],
.dark [data-patient-dialog="true"] [class*="medical-sports-frame"] {
  background: linear-gradient(135deg, hsl(220, 80%, 25%) 0%, hsl(220, 75%, 20%) 100%) !important;
  border: 2px solid hsl(220, 60%, 50%) !important;
  box-shadow: 0 20px 60px rgba(59, 130, 246, 0.3) !important;
}

/* PATIENT REGISTRATION CONTENT - Blue Theme */
.dark [data-patient-dialog="true"] .bg-gradient-to-b {
  background: linear-gradient(to bottom, hsl(220, 80%, 25%), hsl(220, 75%, 20%)) !important;
}

.dark [data-patient-dialog="true"] .border-slate-700 {
  border-color: hsl(220, 60%, 50%) !important;
}

.dark [data-patient-dialog="true"] .from-slate-900 {
  background: hsl(220, 80%, 25%) !important;
}

.dark [data-patient-dialog="true"] .to-slate-800 {
  background: hsl(220, 75%, 20%) !important;
}

/* PATIENT FORM PROGRESS INDICATORS - Blue Theme */
.dark [data-patient-dialog="true"] .bg-blue-600\/20 {
  background: hsl(220, 60%, 40%) !important;
}

.dark [data-patient-dialog="true"] .text-blue-600 {
  color: hsl(220, 70%, 85%) !important;
}

.dark [data-patient-dialog="true"] .bg-white {
  background: hsl(220, 70%, 85%) !important;
  color: hsl(220, 80%, 25%) !important;
}

/* PATIENT FORM INPUTS - Blue Focus States */
.dark [data-patient-dialog="true"] input:focus {
  border-color: hsl(220, 60%, 60%) !important;
  box-shadow: 0 0 0 2px hsl(220, 60%, 50%, 0.3) !important;
}

.dark [data-patient-dialog="true"] select:focus {
  border-color: hsl(220, 60%, 60%) !important;
  box-shadow: 0 0 0 2px hsl(220, 60%, 50%, 0.3) !important;
}

/* PATIENT GENDER SELECTION - Blue Buttons */
.dark [data-patient-dialog="true"] .bg-blue-600 {
  background: hsl(220, 70%, 45%) !important;
  border: 2px solid hsl(220, 60%, 60%) !important;
}

/* PATIENT SUCCESS SCREEN - Blue Theme */
.dark [data-patient-dialog="true"] .bg-blue-600\/10 {
  background: hsl(220, 70%, 25%) !important;
}

.dark [data-patient-dialog="true"] .bg-blue-600\/20 {
  background: hsl(220, 60%, 30%) !important;
}

.dark [data-patient-dialog="true"] .text-blue-500 {
  color: hsl(220, 70%, 65%) !important;
}

/* REMOVE ANY GREEN INTERFERENCE - Nuclear Option */
.dark [data-patient="true"] *[style*="green"],
.dark [data-patient-dialog="true"] *[style*="green"],
.dark [data-patient-form="true"] *[style*="green"],
.dark [data-patient="true"] *[style*="142"],
.dark [data-patient-dialog="true"] *[style*="142"],
.dark [data-patient-form="true"] *[style*="142"],
.dark [data-patient="true"] *[class*="green"],
.dark [data-patient-dialog="true"] *[class*="green"],
.dark [data-patient-form="true"] *[class*="green"] {
  background: hsl(220, 70%, 30%) !important;
  border-color: hsl(220, 60%, 50%) !important;
  color: hsl(220, 70%, 85%) !important;
}

/* PATIENT LOGIN FORMS - Blue Theme */
.dark [data-user-type="patient"] input,
.dark [userType="patient"] input,
.dark .patient-login-form input {
  background: hsl(220, 70%, 15%) !important;
  border: 1px solid hsl(220, 50%, 40%) !important;
  color: #ffffff !important;
}

.dark [data-user-type="patient"] input:focus,
.dark [userType="patient"] input:focus,
.dark .patient-login-form input:focus {
  border-color: hsl(220, 60%, 60%) !important;
  box-shadow: 0 0 0 2px hsl(220, 60%, 50%, 0.3) !important;
}

.dark [data-user-type="patient"] button,
.dark [userType="patient"] button,
.dark .patient-login-form button {
  background: linear-gradient(135deg, hsl(220, 70%, 45%) 0%, hsl(220, 75%, 40%) 100%) !important;
  border: 1px solid hsl(220, 60%, 50%) !important;
  color: #ffffff !important;
}

/* PATIENT SIGN UP DIALOG - Blue Theme */
.dark .patient-signup-dialog,
.dark [class*="PatientSignUp"],
.dark [data-patient-signup="true"] {
  background: hsl(220, 80%, 25%) !important;
  border: 2px solid hsl(220, 60%, 50%) !important;
  box-shadow: 0 20px 60px rgba(59, 130, 246, 0.3) !important;
}

/* ==========================================
   PATIENT PURE BLUE THEME - COMPREHENSIVE OVERRIDE
   FOR DARK THEME ONLY - USER SPECIFIC FIXES
   ========================================== */

/* 1. JOIN AS PATIENT MODAL (PremiumMedicalModal with type="patient") */
.dark [data-type="patient"],
.dark .premium-medical-modal[data-type="patient"],
.dark .premium-medical-modal .bg-gradient-to-br.from-blue-900,
.dark [class*="blue-900"] {
  background: linear-gradient(135deg, hsl(220, 85%, 25%) 0%, hsl(220, 80%, 15%) 100%) !important;
  border: 2px solid hsl(220, 70%, 55%) !important;
  color: #ffffff !important;
}

/* Premium Medical Modal - Patient specific styling */
.dark .premium-medical-modal[data-type="patient"] .bg-card,
.dark [class*="patient"] .bg-card,
.dark [data-patient-modal="true"] {
  background: hsl(220, 85%, 20%) !important;
  border: 2px solid hsl(220, 70%, 55%) !important;
  color: #ffffff !important;
}

/* Patient modal content areas */
.dark [data-type="patient"] .bg-background,
.dark [data-patient-modal="true"] .bg-background {
  background: hsl(220, 80%, 18%) !important;
}

/* Patient modal text elements */
.dark [data-type="patient"] h1,
.dark [data-type="patient"] h2,
.dark [data-type="patient"] h3,
.dark [data-type="patient"] p,
.dark [data-patient-modal="true"] h1,
.dark [data-patient-modal="true"] h2,
.dark [data-patient-modal="true"] h3,
.dark [data-patient-modal="true"] p {
  color: #ffffff !important;
}

/* Patient modal buttons */
.dark [data-type="patient"] button,
.dark [data-patient-modal="true"] button {
  background: linear-gradient(135deg, hsl(220, 75%, 45%) 0%, hsl(220, 80%, 40%) 100%) !important;
  border: 1px solid hsl(220, 70%, 60%) !important;
  color: #ffffff !important;
}

.dark [data-type="patient"] button:hover,
.dark [data-patient-modal="true"] button:hover {
  background: linear-gradient(135deg, hsl(220, 80%, 50%) 0%, hsl(220, 85%, 45%) 100%) !important;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.6) !important;
}

/* 2. CHOOSE ROLE DIALOG - PATIENT OPTION COMPLETE BLUE */
.dark [class*="choose-role"] [data-patient="true"],
.dark .choose-role-dialog [data-patient="true"],
.dark button[data-patient="true"] {
  background: linear-gradient(135deg, hsl(220, 80%, 35%) 0%, hsl(220, 75%, 25%) 100%) !important;
  border: 2px solid hsl(220, 70%, 55%) !important;
  color: #ffffff !important;
}

.dark [class*="choose-role"] [data-patient="true"]:hover,
.dark button[data-patient="true"]:hover {
  background: linear-gradient(135deg, hsl(220, 85%, 40%) 0%, hsl(220, 80%, 30%) 100%) !important;
  border-color: hsl(220, 75%, 65%) !important;
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.6) !important;
  transform: translateY(-3px) !important;
}

/* Patient role text styling */
.dark [data-patient="true"] h3,
.dark [data-patient="true"] p {
  color: #ffffff !important;
}

/* 3. PATIENT REGISTRATION DIALOG COMPLETE BLUE OVERRIDE */
.dark .patient-registration-dialog,
.dark [class*="PatientRegistration"],
.dark [data-patient-form="true"] {
  background: linear-gradient(145deg, hsl(220, 90%, 20%) 0%, hsl(220, 85%, 15%) 100%) !important;
  border: 3px solid hsl(220, 75%, 60%) !important;
  box-shadow: 0 30px 80px rgba(59, 130, 246, 0.5) !important;
  color: #ffffff !important;
}

/* Patient registration form frame */
.dark .patient-registration-dialog .medical-sports-frame,
.dark [data-patient-form="true"] .medical-sports-frame {
  background: linear-gradient(135deg, hsl(220, 85%, 22%) 0%, hsl(220, 80%, 17%) 100%) !important;
  border: 2px solid hsl(220, 70%, 55%) !important;
}

/* Patient registration inputs */
.dark .patient-registration-dialog input,
.dark .patient-registration-dialog select,
.dark [data-patient-form="true"] input,
.dark [data-patient-form="true"] select {
  background: hsl(220, 75%, 12%) !important;
  border: 2px solid hsl(220, 60%, 45%) !important;
  color: #ffffff !important;
}

.dark .patient-registration-dialog input:focus,
.dark .patient-registration-dialog select:focus,
.dark [data-patient-form="true"] input:focus,
.dark [data-patient-form="true"] select:focus {
  border-color: hsl(220, 70%, 65%) !important;
  box-shadow: 0 0 0 3px hsl(220, 70%, 50%, 0.3) !important;
  background: hsl(220, 80%, 15%) !important;
}

/* Patient registration labels */
.dark .patient-registration-dialog label,
.dark [data-patient-form="true"] label {
  color: hsl(220, 70%, 85%) !important;
}

/* Patient registration buttons */
.dark .patient-registration-dialog button,
.dark [data-patient-form="true"] button {
  background: linear-gradient(135deg, hsl(220, 75%, 40%) 0%, hsl(220, 80%, 35%) 100%) !important;
  border: 2px solid hsl(220, 70%, 55%) !important;
  color: #ffffff !important;
}

.dark .patient-registration-dialog button:hover,
.dark [data-patient-form="true"] button:hover {
  background: linear-gradient(135deg, hsl(220, 80%, 45%) 0%, hsl(220, 85%, 40%) 100%) !important;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.6) !important;
}

/* 4. PATIENT LOGIN/SIGN-IN FORMS COMPLETE BLUE */
.dark [userType="patient"],
.dark [data-user-type="patient"],
.dark .login-dialog[data-user-type="patient"],
.dark [class*="patient-login"] {
  background: linear-gradient(145deg, hsl(220, 85%, 20%) 0%, hsl(220, 80%, 15%) 100%) !important;
  border: 3px solid hsl(220, 70%, 55%) !important;
  color: #ffffff !important;
}

/* Patient login modal content */
.dark [userType="patient"] .bg-white,
.dark [data-user-type="patient"] .bg-white {
  background: hsl(220, 85%, 20%) !important;
}

/* Patient login form inputs */
.dark [userType="patient"] input,
.dark [data-user-type="patient"] input {
  background: hsl(220, 70%, 12%) !important;
  border: 2px solid hsl(220, 60%, 45%) !important;
  color: #ffffff !important;
}

.dark [userType="patient"] input:focus,
.dark [data-user-type="patient"] input:focus {
  border-color: hsl(220, 70%, 60%) !important;
  box-shadow: 0 0 0 3px hsl(220, 70%, 50%, 0.3) !important;
  background: hsl(220, 75%, 15%) !important;
}

/* Patient login form labels */
.dark [userType="patient"] label,
.dark [data-user-type="patient"] label {
  color: hsl(220, 70%, 85%) !important;
}

/* Patient login buttons */
.dark [userType="patient"] button,
.dark [data-user-type="patient"] button {
  background: linear-gradient(135deg, hsl(220, 75%, 40%) 0%, hsl(220, 80%, 35%) 100%) !important;
  border: 2px solid hsl(220, 70%, 55%) !important;
  color: #ffffff !important;
}

.dark [userType="patient"] button:hover,
.dark [data-user-type="patient"] button:hover {
  background: linear-gradient(135deg, hsl(220, 80%, 45%) 0%, hsl(220, 85%, 40%) 100%) !important;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.6) !important;
}

/* 5. CHOOSE ROLE LOGIN DIALOG - PATIENT OPTION */
.dark .choose-role-login-dialog [data-patient="true"],
.dark [class*="ChooseRoleLogin"] [data-patient="true"] {
  background: linear-gradient(135deg, hsl(220, 80%, 30%) 0%, hsl(220, 75%, 20%) 100%) !important;
  border: 2px solid hsl(220, 70%, 55%) !important;
  color: #ffffff !important;
}

/* 6. REMOVE ANY RESIDUAL GREEN STYLING FROM PATIENT COMPONENTS */
.dark [data-patient="true"] *[class*="green"],
.dark [data-patient-form="true"] *[class*="green"],
.dark [userType="patient"] *[class*="green"],
.dark [data-user-type="patient"] *[class*="green"],
.dark .patient-registration-dialog *[class*="green"],
.dark [class*="patient"] *[class*="primary"] {
  background: linear-gradient(135deg, hsl(220, 75%, 35%) 0%, hsl(220, 80%, 30%) 100%) !important;
  border-color: hsl(220, 70%, 55%) !important;
  color: #ffffff !important;
}

/* 7. COMPREHENSIVE TEXT COLOR OVERRIDE FOR PATIENT COMPONENTS */
.dark [data-patient="true"] *,
.dark [data-patient-form="true"] *,
.dark [userType="patient"] *,
.dark [data-user-type="patient"] *,
.dark .patient-registration-dialog *,
.dark [class*="patient"] * {
  color: #ffffff !important;
}

/* Specific text elements that need white color */
.dark [data-patient="true"] span,
.dark [data-patient="true"] div,
.dark [data-patient-form="true"] span,
.dark [data-patient-form="true"] div,
.dark [userType="patient"] span,
.dark [userType="patient"] div {
  color: #ffffff !important;
}

/* 8. DIALOG CONTENT BACKGROUNDS - ENSURE PURE BLUE */
.dark [data-patient="true"] .bg-background,
.dark [data-patient-form="true"] .bg-background,
.dark [userType="patient"] .bg-background,
.dark .patient-registration-dialog .bg-background {
  background: hsl(220, 80%, 18%) !important;
}

/* Dialog overlays and backdrops */
.dark [data-patient="true"] .bg-card,
.dark [data-patient-form="true"] .bg-card,
.dark [userType="patient"] .bg-card {
  background: hsl(220, 85%, 20%) !important;
}

/* 9. LINK COLORS IN PATIENT COMPONENTS */
.dark [data-patient="true"] a,
.dark [data-patient-form="true"] a,
.dark [userType="patient"] a {
  color: hsl(220, 80%, 85%) !important;
}

.dark [data-patient="true"] a:hover,
.dark [data-patient-form="true"] a:hover,
.dark [userType="patient"] a:hover {
  color: #ffffff !important;
}

/* 10. ENSURE ICONS ARE WHITE IN PATIENT COMPONENTS */
.dark [data-patient="true"] svg,
.dark [data-patient-form="true"] svg,
.dark [userType="patient"] svg {
  color: #ffffff !important;
  fill: #ffffff !important;
}

/* Medical icons specifically */
.dark [data-patient="true"] .lucide,
.dark [data-patient-form="true"] .lucide,
.dark [userType="patient"] .lucide {
  color: #ffffff !important;
  stroke: #ffffff !important;
}

/* ==========================================
   FINAL COMPREHENSIVE PATIENT BLUE OVERRIDE
   DARK THEME ONLY - CATCH ALL RULES
   ========================================== */

/* Catch all for any patient-related components with blue styling */
.dark *[data-patient="true"],
.dark *[data-patient-form="true"],
.dark *[data-patient-dialog="true"],
.dark *[data-patient-modal="true"],
.dark *[userType="patient"],
.dark *[data-user-type="patient"] {
  background: linear-gradient(135deg, hsl(220, 80%, 25%) 0%, hsl(220, 75%, 20%) 100%) !important;
  border: 2px solid hsl(220, 70%, 55%) !important;
  color: #ffffff !important;
}

/* Override any green coloring in patient components */
.dark [data-patient="true"] *[style*="green"],
.dark [data-patient-form="true"] *[style*="green"],
.dark [data-patient-dialog="true"] *[style*="green"],
.dark [userType="patient"] *[style*="green"],
.dark [data-user-type="patient"] *[style*="green"] {
  background: hsl(220, 80%, 30%) !important;
  border-color: hsl(220, 70%, 55%) !important;
  color: #ffffff !important;
}

/* Override any HSL green values in patient components */
.dark [data-patient="true"] *[style*="hsl(142"],
.dark [data-patient-form="true"] *[style*="hsl(142"],
.dark [data-patient-dialog="true"] *[style*="hsl(142"],
.dark [userType="patient"] *[style*="hsl(142"],
.dark [data-user-type="patient"] *[style*="hsl(142"] {
  background: hsl(220, 80%, 30%) !important;
  border-color: hsl(220, 70%, 55%) !important;
  color: #ffffff !important;
}

/* Force white text on all patient components */
.dark [data-patient="true"] *,
.dark [data-patient-form="true"] *,
.dark [data-patient-dialog="true"] *,
.dark [data-patient-modal="true"] *,
.dark [userType="patient"] *,
.dark [data-user-type="patient"] * {
  color: #ffffff !important;
}

/* Ensure button backgrounds are pure blue in patient components */
.dark [data-patient="true"] button,
.dark [data-patient-form="true"] button,
.dark [data-patient-dialog="true"] button,
.dark [data-patient-modal="true"] button,
.dark [userType="patient"] button,
.dark [data-user-type="patient"] button {
  background: linear-gradient(135deg, hsl(220, 75%, 40%) 0%, hsl(220, 80%, 35%) 100%) !important;
  border: 2px solid hsl(220, 70%, 55%) !important;
  color: #ffffff !important;
}

/* Ensure input backgrounds are dark blue in patient components */
.dark [data-patient="true"] input,
.dark [data-patient="true"] select,
.dark [data-patient="true"] textarea,
.dark [data-patient-form="true"] input,
.dark [data-patient-form="true"] select,
.dark [data-patient-form="true"] textarea,
.dark [data-patient-dialog="true"] input,
.dark [data-patient-dialog="true"] select,
.dark [data-patient-dialog="true"] textarea,
.dark [userType="patient"] input,
.dark [userType="patient"] select,
.dark [userType="patient"] textarea {
  background: hsl(220, 70%, 12%) !important;
  border: 2px solid hsl(220, 60%, 45%) !important;
  color: #ffffff !important;
}

/* Focus states for patient form elements */
.dark [data-patient="true"] input:focus,
.dark [data-patient="true"] select:focus,
.dark [data-patient="true"] textarea:focus,
.dark [data-patient-form="true"] input:focus,
.dark [data-patient-form="true"] select:focus,
.dark [data-patient-form="true"] textarea:focus,
.dark [data-patient-dialog="true"] input:focus,
.dark [data-patient-dialog="true"] select:focus,
.dark [data-patient-dialog="true"] textarea:focus,
.dark [userType="patient"] input:focus,
.dark [userType="patient"] select:focus,
.dark [userType="patient"] textarea:focus {
  border-color: hsl(220, 70%, 65%) !important;
  box-shadow: 0 0 0 3px hsl(220, 70%, 50%, 0.3) !important;
  background: hsl(220, 75%, 15%) !important;
  outline: none !important;
}

/* Custom class for View Full Standings button border in dark theme */
.dark .view-full-standings-button {
  border-color: #90EE90 !important; /* Light green */
}

/* Override any Tailwind classes that might interfere */
.dark [data-patient="true"].bg-green-600,
.dark [data-patient="true"].bg-green-500,
.dark [data-patient="true"].bg-primary,
.dark [data-patient-form="true"].bg-green-600,
.dark [data-patient-form="true"].bg-green-500,
.dark [data-patient-form="true"].bg-primary,
.dark [userType="patient"].bg-green-600,
.dark [userType="patient"].bg-green-500,
.dark [userType="patient"].bg-primary {
  background: linear-gradient(135deg, hsl(220, 75%, 35%) 0%, hsl(220, 80%, 30%) 100%) !important;
}

/* Override any border classes that might interfere */
.dark [data-patient="true"].border-green-600,
.dark [data-patient="true"].border-green-500,
.dark [data-patient="true"].border-primary,
.dark [data-patient-form="true"].border-green-600,
.dark [data-patient-form="true"].border-green-500,
.dark [data-patient-form="true"].border-primary,
.dark [userType="patient"].border-green-600,
.dark [userType="patient"].border-green-500,
.dark [userType="patient"].border-primary {
  border-color: hsl(220, 70%, 55%) !important;
}

/* Override any text classes that might interfere */
.dark [data-patient="true"].text-green-600,
.dark [data-patient="true"].text-green-500,
.dark [data-patient="true"].text-primary,
.dark [data-patient-form="true"].text-green-600,
.dark [data-patient-form="true"].text-green-500,
.dark [data-patient-form="true"].text-primary,
.dark [userType="patient"].text-green-600,
.dark [userType="patient"].text-green-500,
.dark [userType="patient"].text-primary {
  color: #ffffff !important;
}

/* ==========================================
   ULTIMATE NUCLEAR OPTION FOR PATIENT BLUE
   TARGET ALL DYNAMIC TAILWIND CLASSES
   ========================================== */

/* Override ALL possible Tailwind background classes with green in patient components */
.dark [data-patient="true"] .from-green-100,
.dark [data-patient="true"] .from-green-200,
.dark [data-patient="true"] .from-green-300,
.dark [data-patient="true"] .from-green-400,
.dark [data-patient="true"] .from-green-500,
.dark [data-patient="true"] .from-green-600,
.dark [data-patient="true"] .from-green-700,
.dark [data-patient="true"] .from-green-800,
.dark [data-patient="true"] .from-green-900,
.dark [data-patient-form="true"] .from-green-100,
.dark [data-patient-form="true"] .from-green-200,
.dark [data-patient-form="true"] .from-green-300,
.dark [data-patient-form="true"] .from-green-400,
.dark [data-patient-form="true"] .from-green-500,
.dark [data-patient-form="true"] .from-green-600,
.dark [data-patient-form="true"] .from-green-700,
.dark [data-patient-form="true"] .from-green-800,
.dark [data-patient-form="true"] .from-green-900,
.dark [data-patient-dialog="true"] .from-green-100,
.dark [data-patient-dialog="true"] .from-green-200,
.dark [data-patient-dialog="true"] .from-green-300,
.dark [data-patient-dialog="true"] .from-green-400,
.dark [data-patient-dialog="true"] .from-green-500,
.dark [data-patient-dialog="true"] .from-green-600,
.dark [data-patient-dialog="true"] .from-green-700,
.dark [data-patient-dialog="true"] .from-green-800,
.dark [data-patient-dialog="true"] .from-green-900,
.dark [userType="patient"] .from-green-100,
.dark [userType="patient"] .from-green-200,
.dark [userType="patient"] .from-green-300,
.dark [userType="patient"] .from-green-400,
.dark [userType="patient"] .from-green-500,
.dark [userType="patient"] .from-green-600,
.dark [userType="patient"] .from-green-700,
.dark [userType="patient"] .from-green-800,
.dark [userType="patient"] .from-green-900 {
  background: linear-gradient(135deg, hsl(220, 80%, 30%) 0%, hsl(220, 75%, 25%) 100%) !important;
}

/* Override text colors with green in patient components */
.dark [data-patient="true"] .text-green-100,
.dark [data-patient="true"] .text-green-200,
.dark [data-patient="true"] .text-green-300,
.dark [data-patient="true"] .text-green-400,
.dark [data-patient="true"] .text-green-500,
.dark [data-patient="true"] .text-green-600,
.dark [data-patient="true"] .text-green-700,
.dark [data-patient="true"] .text-green-800,
.dark [data-patient="true"] .text-green-900,
.dark [data-patient-form="true"] .text-green-100,
.dark [data-patient-form="true"] .text-green-200,
.dark [data-patient-form="true"] .text-green-300,
.dark [data-patient-form="true"] .text-green-400,
.dark [data-patient-form="true"] .text-green-500,
.dark [data-patient-form="true"] .text-green-600,
.dark [data-patient-form="true"] .text-green-700,
.dark [data-patient-form="true"] .text-green-800,
.dark [data-patient-form="true"] .text-green-900,
.dark [data-patient-dialog="true"] .text-green-100,
.dark [data-patient-dialog="true"] .text-green-200,
.dark [data-patient-dialog="true"] .text-green-300,
.dark [data-patient-dialog="true"] .text-green-400,
.dark [data-patient-dialog="true"] .text-green-500,
.dark [data-patient-dialog="true"] .text-green-600,
.dark [data-patient-dialog="true"] .text-green-700,
.dark [data-patient-dialog="true"] .text-green-800,
.dark [data-patient-dialog="true"] .text-green-900,
.dark [userType="patient"] .text-green-100,
.dark [userType="patient"] .text-green-200,
.dark [userType="patient"] .text-green-300,
.dark [userType="patient"] .text-green-400,
.dark [userType="patient"] .text-green-500,
.dark [userType="patient"] .text-green-600,
.dark [userType="patient"] .text-green-700,
.dark [userType="patient"] .text-green-800,
.dark [userType="patient"] .text-green-900 {
  color: #ffffff !important;
}

/* MOST AGGRESSIVE: Override SVG colors that might be green */
.dark [data-patient="true"] .text-green-400,
.dark [data-patient="true"] .text-green-300,
.dark [data-patient="true"] svg.text-green-400,
.dark [data-patient="true"] svg.text-green-300,
.dark [data-patient-form="true"] .text-green-400,
.dark [data-patient-form="true"] .text-green-300,
.dark [data-patient-form="true"] svg.text-green-400,
.dark [data-patient-form="true"] svg.text-green-300,
.dark [data-patient-dialog="true"] .text-green-400,
.dark [data-patient-dialog="true"] .text-green-300,
.dark [data-patient-dialog="true"] svg.text-green-400,
.dark [data-patient-dialog="true"] svg.text-green-300,
.dark [userType="patient"] .text-green-400,
.dark [userType="patient"] .text-green-300,
.dark [userType="patient"] svg.text-green-400,
.dark [userType="patient"] svg.text-green-300 {
  color: #ffffff !important;
}

/* Override border colors with green in patient components */
.dark [data-patient="true"] .border-green-100,
.dark [data-patient="true"] .border-green-200,
.dark [data-patient="true"] .border-green-300,
.dark [data-patient="true"] .border-green-400,
.dark [data-patient="true"] .border-green-500,
.dark [data-patient="true"] .border-green-600,
.dark [data-patient="true"] .border-green-700,
.dark [data-patient="true"] .border-green-800,
.dark [data-patient="true"] .border-green-900,
.dark [data-patient-form="true"] .border-green-100,
.dark [data-patient-form="true"] .border-green-200,
.dark [data-patient-form="true"] .border-green-300,
.dark [data-patient-form="true"] .border-green-400,
.dark [data-patient-form="true"] .border-green-500,
.dark [data-patient-form="true"] .border-green-600,
.dark [data-patient-form="true"] .border-green-700,
.dark [data-patient-form="true"] .border-green-800,
.dark [data-patient-form="true"] .border-green-900,
.dark [data-patient-dialog="true"] .border-green-100,
.dark [data-patient-dialog="true"] .border-green-200,
.dark [data-patient-dialog="true"] .border-green-300,
.dark [data-patient-dialog="true"] .border-green-400,
.dark [data-patient-dialog="true"] .border-green-500,
.dark [data-patient-dialog="true"] .border-green-600,
.dark [data-patient-dialog="true"] .border-green-700,
.dark [data-patient-dialog="true"] .border-green-800,
.dark [data-patient-dialog="true"] .border-green-900,
.dark [userType="patient"] .border-green-100,
.dark [userType="patient"] .border-green-200,
.dark [userType="patient"] .border-green-300,
.dark [userType="patient"] .border-green-400,
.dark [userType="patient"] .border-green-500,
.dark [userType="patient"] .border-green-600,
.dark [userType="patient"] .border-green-700,
.dark [userType="patient"] .border-green-800,
.dark [userType="patient"] .border-green-900 {
  border-color: hsl(220, 70%, 55%) !important;
}

/* ABSOLUTE NUCLEAR OPTION: Force all elements to use blue color scheme */
.dark [data-patient-modal="true"] * {
  color: #ffffff !important;
  background-color: transparent !important;
}

.dark [data-patient-modal="true"] {
  background: linear-gradient(135deg, hsl(220, 90%, 15%) 0%, hsl(220, 85%, 10%) 100%) !important;
}

.dark [data-patient-modal="true"] > div {
  background: linear-gradient(145deg, hsl(220, 85%, 20%) 0%, hsl(220, 80%, 15%) 100%) !important;
  border: 3px solid hsl(220, 75%, 60%) !important;
  box-shadow: 0 30px 80px rgba(59, 130, 246, 0.5) !important;
}

/* Force header to be blue in patient modals */
.dark [data-patient-modal="true"] div[class*="relative h-32"],
.dark [data-patient-modal="true"] div[class*="h-32"],
.dark [data-patient-modal="true"] [class*="bg-gradient-to-r"] {
  background: linear-gradient(135deg, hsl(220, 85%, 30%) 0%, hsl(220, 80%, 25%) 100%) !important;
}

/* Force buttons to be blue in patient modals */
.dark [data-patient-modal="true"] button {
  background: linear-gradient(135deg, hsl(220, 75%, 45%) 0%, hsl(220, 80%, 40%) 100%) !important;
  border: 2px solid hsl(220, 70%, 60%) !important;
  color: #ffffff !important;
}

.dark [data-patient-modal="true"] button:hover {
  background: linear-gradient(135deg, hsl(220, 80%, 50%) 0%, hsl(220, 85%, 45%) 100%) !important;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.6) !important;
}

/* Specific fix for the registration complete icon alignment */
.dark [data-patient-modal="true"] div[class*="bg-blue-600/20"] {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Light theme: Fix specific button text visibility issues */
/* Only target the specific buttons mentioned by the user */

/* Light theme: Hero section buttons - Join as Patient, Join as Doctor */
html:not(.dark) .hero-button-primary,
html:not(.dark) .hero-button-secondary {
  color: #ffffff !important;
}

/* Light theme: Buttons with green backgrounds need white text */
html:not(.dark) button.bg-primary,
html:not(.dark) a.bg-primary,
html:not(.dark) button[class*="bg-gradient"],
html:not(.dark) a[class*="bg-gradient"] {
  color: #ffffff !important;
}

/* Light theme: Ensure button content is white */
html:not(.dark) button.bg-primary *,
html:not(.dark) a.bg-primary *,
html:not(.dark) button[class*="bg-gradient"] *,
html:not(.dark) a[class*="bg-gradient"] * {
  color: #ffffff !important;
}

/* ==========================================
   FINAL ULTIMATE MEDICAL SPORTS FRAME OVERRIDE
   TARGET ALL PATIENT FRAME ELEMENTS
   ========================================== */

/* FORCE MEDICAL SPORTS FRAME TO BE BLUE FOR PATIENT VARIANT */
.dark [data-variant="patient"],
.dark [data-variant="patient"] *,
.dark div[data-patient-form="true"],
.dark div[data-patient-form="true"] * {
  color: #ffffff !important;
}

/* Override the main frame background */
.dark [data-variant="patient"],
.dark div[data-patient-form="true"] {
  background: linear-gradient(145deg, hsl(220, 85%, 20%) 0%, hsl(220, 80%, 15%) 100%) !important;
  border: 3px solid hsl(220, 75%, 60%) !important;
  box-shadow: 0 30px 80px rgba(59, 130, 246, 0.5) !important;
}

/* Override inner frame elements */
.dark [data-variant="patient"] > div,
.dark [data-variant="patient"] .relative,
.dark div[data-patient-form="true"] > div,
.dark div[data-patient-form="true"] .relative {
  background: linear-gradient(145deg, hsl(220, 85%, 20%) 0%, hsl(220, 80%, 15%) 100%) !important;
  border-color: hsl(220, 75%, 60%) !important;
}

/* Force all div elements inside patient frame to be blue */
.dark [data-variant="patient"] div,
.dark div[data-patient-form="true"] div {
  background-color: transparent !important;
  color: #ffffff !important;
}

/* Override any remaining green backgrounds in patient frames */
.dark [data-variant="patient"] div[style*="green"],
.dark [data-variant="patient"] div[style*="hsl(142"],
.dark div[data-patient-form="true"] div[style*="green"],
.dark div[data-patient-form="true"] div[style*="hsl(142"] {
  background: hsl(220, 80%, 25%) !important;
  border-color: hsl(220, 70%, 55%) !important;
}

/* ULTIMATE OVERRIDE: Target any element with background gradients */
.dark [data-variant="patient"] *[style*="linear-gradient"],
.dark div[data-patient-form="true"] *[style*="linear-gradient"] {
  background: linear-gradient(145deg, hsl(220, 85%, 20%) 0%, hsl(220, 80%, 15%) 100%) !important;
}

/* Override form inputs inside medical sports frame */
.dark [data-variant="patient"] input,
.dark [data-variant="patient"] select,
.dark [data-variant="patient"] textarea,
.dark div[data-patient-form="true"] input,
.dark div[data-patient-form="true"] select,
.dark div[data-patient-form="true"] textarea {
  background: hsl(220, 70%, 12%) !important;
  border: 2px solid hsl(220, 60%, 45%) !important;
  color: #ffffff !important;
}

/* Override form labels inside medical sports frame */
.dark [data-variant="patient"] label,
.dark div[data-patient-form="true"] label {
  color: hsl(220, 70%, 85%) !important;
}

/* Override buttons inside medical sports frame */
.dark [data-variant="patient"] button,
.dark div[data-patient-form="true"] button {
  background: linear-gradient(135deg, hsl(220, 75%, 40%) 0%, hsl(220, 80%, 35%) 100%) !important;
  border: 2px solid hsl(220, 70%, 55%) !important;
  color: #ffffff !important;
}

.dark [data-variant="patient"] button:hover,
.dark div[data-patient-form="true"] button:hover {
  background: linear-gradient(135deg, hsl(220, 80%, 45%) 0%, hsl(220, 85%, 40%) 100%) !important;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.6) !important;
}

/* NUCLEAR OPTION: Override ANY element that might have green styling */
.dark [data-variant="patient"] *[class*="green"],
.dark [data-variant="patient"] *[class*="primary"],
.dark div[data-patient-form="true"] *[class*="green"],
.dark div[data-patient-form="true"] *[class*="primary"] {
  background: hsl(220, 80%, 25%) !important;
  border-color: hsl(220, 70%, 55%) !important;
  color: #ffffff !important;
}

/* Override any CSS custom properties that might be setting green colors */
.dark [data-variant="patient"],
.dark [data-variant="patient"] *,
.dark div[data-patient-form="true"],
.dark div[data-patient-form="true"] * {
  --primary: 220 80% 50%;
  --primary-foreground: 0 0% 100%;
  --background: 220 80% 18%;
  --foreground: 0 0% 100%;
  --card: 220 85% 20%;
  --card-foreground: 0 0% 100%;
  --muted: 220 60% 25%;
  --muted-foreground: 0 0% 85%;
  --accent: 220 70% 30%;
  --accent-foreground: 0 0% 100%;
  --border: 220 70% 55%;
}

/* Responsive Design Media Queries */

/* Mobile-first optimizations (up to 767px) */
@media (max-width: 767px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Optimize touch targets for mobile */
  button, .btn, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve text readability on mobile */
  body {
    font-size: 16px;
    line-height: 1.6;
  }

  /* Optimize hero sections for mobile */
  .hero-text {
    line-height: 1.2;
  }
}





/* Tablet and Laptop Navigation Styles */
@media (min-width: 768px) and (max-width: 1365px) {
  /* Ensure hamburger menu is visible on tablets and laptops */
  .tablet-laptop-menu {
    display: flex !important;
  }

  /* Hide desktop navigation on tablets and laptops */
  .desktop-nav {
    display: none !important;
  }
}

/* Desktop Navigation Styles */
@media (min-width: 1366px) {
  /* Show desktop navigation */
  .desktop-nav {
    display: flex !important;
  }

  /* Hide hamburger menu on desktop */
  .tablet-laptop-menu {
    display: none !important;
  }
}