"use client"

import { CommandItem } from "@/components/ui/command"
import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Trophy, Plus, X, Star, Building, Globe2, User, Stethoscope, Medal, Activity, Heart, Award, Zap, Search } from "lucide-react"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { createBrowserClient } from "@/lib/supabase-client"
import { motion, AnimatePresence } from "framer-motion"
import { ECGDivider } from "@/components/ecg-divider"
import { FALLBACK_COUNTRIES, FALLBACK_HOSPITALS, FALLBACK_DOCTORS } from "@/lib/fallback-data"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton"
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card"
import Image from "next/image" // Import next/image
import { PageAds } from "@/components/ads/page-ads"
import { format } from 'date-fns'

interface Doctor {
  doctor_id: string;
  fullname: string;
  specialty?: string;
  hospital_name?: string;
  profile_image?: string;
  wins?: number;
  draws?: number;
  losses?: number;
  points?: number;
  rating?: number;
  review_count?: number;
}

interface Hospital {
  hospital_id: string;
  hospital_name: string;
  location?: string;
}

interface Country {
  country_id: string;
  country_name: string;
  flag_url?: string;
}

interface Column {
  id: string;
  country?: Country;
  hospital?: Hospital;
  doctor?: Doctor;
}

interface LoadingState {
  countries: boolean;
  hospitals: Record<string, boolean>;
  doctors: Record<string, boolean>;
}

export default function HeadToHeadPage() {
  const [open, setOpen] = useState<Record<string, boolean>>({})
  const [columns, setColumns] = useState<Column[]>([{ id: "1" }, { id: "2" }])
  const [countries, setCountries] = useState<Country[]>([])
  const [filteredCountries, setFilteredCountries] = useState<Country[]>([])
  const [hospitals, setHospitals] = useState<Record<string, Hospital[]>>({})
  const [filteredHospitals, setFilteredHospitals] = useState<Record<string, Hospital[]>>({})
  const [doctors, setDoctors] = useState<Record<string, Doctor[]>>({})
  const [filteredDoctors, setFilteredDoctors] = useState<Record<string, Doctor[]>>({})
  const [searchTerms, setSearchTerms] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState<LoadingState>({
    countries: true,
    hospitals: {},
    doctors: {}
  })

  // Logs for debugging
  const [logs, setLogs] = useState<string[]>([])
  const addLog = (message: string) => {
    const timestamp = format(new Date(), 'HH:mm:ss')
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 49)])
    console.log(`[${timestamp}] ${message}`)
  }

  // Add state for ad placements
  const [bannerAd, setBannerAd] = useState<any>(null)
  const [sidebarAd, setSidebarAd] = useState<any>(null)
  const [bottomAd, setBottomAd] = useState<any>(null)

  // Initialize with default columns
  useEffect(() => {
    addLog("Component mounted");
    fetchCountries();
    fetchPageAds();
    
    // Initialize with two empty columns
    if (columns.length === 0) {
      addColumn();
      addColumn();
    }
  }, []);

  // Add column function
  const addColumn = () => {
    if (columns.length >= 4) {
      addLog("Maximum number of columns reached (4)");
      return;
    }
    
    const newColumn: Column = {
      id: Math.random().toString(36).substring(2, 9),
    };
    
    setColumns(prev => [...prev, newColumn]);
    addLog(`Added new column #${columns.length + 1}`);
  };

  // Update column data
  const updateColumn = (id: string, data: Partial<Column>) => {
    setColumns(prev => 
      prev.map(col => (col.id === id ? { ...col, ...data } : col))
    );
    addLog(`Updated column ${id} with new data`);
  };
  
  // Remove column
  const removeColumn = (id: string) => {
    setColumns(prev => prev.filter(col => col.id !== id));
    addLog(`Removed column ${id}`);
  };

  // Clear search term
  const clearSearch = (key: string) => {
    setSearchTerms(prev => ({ ...prev, [key]: '' }));
  };

  // Fetch ads for the page
  const fetchPageAds = async () => {
    try {
      addLog("Fetching page ads");
      const supabase = createBrowserClient()
      
      // Banner ad
      const { data: bannerData, error: bannerError } = await supabase
        .from('ads')
        .select('*')
        .eq('position', 'banner')
        .eq('page', 'head-to-head')
        .eq('is_active', true)
        .order('priority', { ascending: false })
        .limit(1);
        
      if (bannerError) {
        addLog(`Error fetching banner ad: ${bannerError.message}`);
      } else if (bannerData && bannerData.length > 0) {
        setBannerAd(bannerData[0]);
        addLog(`Loaded banner ad: ${bannerData[0].ad_id}`);
      }
      
      // Sidebar ad
      const { data: sidebarData, error: sidebarError } = await supabase
        .from('ads')
        .select('*')
        .eq('position', 'sidebar')
        .eq('page', 'head-to-head')
        .eq('is_active', true)
        .order('priority', { ascending: false })
        .limit(1);
        
      if (sidebarError) {
        addLog(`Error fetching sidebar ad: ${sidebarError.message}`);
      } else if (sidebarData && sidebarData.length > 0) {
        setSidebarAd(sidebarData[0]);
        addLog(`Loaded sidebar ad: ${sidebarData[0].ad_id}`);
      }
      
      // Bottom ad
      const { data: bottomData, error: bottomError } = await supabase
        .from('ads')
        .select('*')
        .eq('position', 'bottom')
        .eq('page', 'head-to-head')
        .eq('is_active', true)
        .order('priority', { ascending: false })
        .limit(1);
        
      if (bottomError) {
        addLog(`Error fetching bottom ad: ${bottomError.message}`);
      } else if (bottomData && bottomData.length > 0) {
        setBottomAd(bottomData[0]);
        addLog(`Loaded bottom ad: ${bottomData[0].ad_id}`);
      }
    } catch (error) {
      addLog(`Error in fetchPageAds: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  // Fetch list of countries
  const fetchCountries = async () => {
    try {
      setLoading(prev => ({ ...prev, countries: true }));
      addLog("Fetching countries");
      
      const supabase = createBrowserClient()
      const { data, error } = await supabase
        .from('countries')
        .select('*')
        .order('country_name', { ascending: true });
      
      if (error) {
        addLog(`Error fetching countries: ${error.message}`);
        return;
      }
      
      const formattedCountries = data.map(country => ({
        country_id: country.country_id.toString(),
        country_name: country.country_name || 'Unknown Country',
        flag_url: country.flag_url
      }));
      
      setCountries(formattedCountries);
      setFilteredCountries(formattedCountries);
      addLog(`Loaded ${formattedCountries.length} countries`);
    } catch (error) {
      addLog(`Error in fetchCountries: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(prev => ({ ...prev, countries: false }));
    }
  };

  // Handle country search
  const handleCountrySearch = (searchTerm: string) => {
    if (!searchTerm) {
      setFilteredCountries(countries);
      return;
    }
    
    const filtered = countries.filter(country => 
      country.country_name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    setFilteredCountries(filtered);
    addLog(`Filtered countries to ${filtered.length} results for search: "${searchTerm}"`);
  };

  // Fetch hospitals for a country
  const fetchHospitals = async (countryId: string) => {
    try {
      setLoading(prev => ({ ...prev, hospitals: { ...prev.hospitals, [countryId]: true } }));
      addLog(`Fetching hospitals for country ID: ${countryId}`);
      
      const supabase = createBrowserClient();
      const { data, error } = await supabase
        .from('hospitals')
        .select('*')
        .eq('country_id', countryId)
        .order('hospital_name', { ascending: true });
      
      if (error) {
        addLog(`Error fetching hospitals: ${error.message}`);
        return;
      }
      
      const formattedHospitals: Hospital[] = data.map(hospital => ({
        hospital_id: hospital.hospital_id.toString(),
        hospital_name: hospital.hospital_name || 'Unknown Hospital',
        location: hospital.location
      }));
      
      setHospitals(prev => ({ ...prev, [countryId]: formattedHospitals }));
      setFilteredHospitals(prev => ({ ...prev, [countryId]: formattedHospitals }));
      addLog(`Loaded ${formattedHospitals.length} hospitals for country ID: ${countryId}`);
    } catch (error) {
      addLog(`Error in fetchHospitals: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(prev => ({ ...prev, hospitals: { ...prev.hospitals, [countryId]: false } }));
    }
  };

  // Handle hospital search
  const handleHospitalSearch = (searchTerm: string, columnId: string, countryId: string) => {
    if (!searchTerm) {
      setFilteredHospitals(prev => ({ ...prev, [countryId]: hospitals[countryId] || [] }));
      return;
    }
    
    const filtered = (hospitals[countryId] || []).filter(hospital => 
      hospital.hospital_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (hospital.location && hospital.location.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    
    setFilteredHospitals(prev => ({ ...prev, [countryId]: filtered }));
    addLog(`Filtered hospitals to ${filtered.length} results for search: "${searchTerm}"`);
  };

  // Fetch doctors for a hospital
  const fetchDoctors = async (hospitalId: string) => {
    try {
      setLoading(prev => ({ ...prev, doctors: { ...prev.doctors, [hospitalId]: true } }));
      addLog(`Fetching doctors for hospital ID: ${hospitalId}`);
      
      const supabase = createBrowserClient()
      const { data, error } = await supabase
        .from('doctors')
        .select('*')
        .eq('hospital_id', hospitalId)
        .order('rating', { ascending: false });
      
      if (error) {
        addLog(`Error fetching doctors: ${error.message}`);
        return;
      }
      
      const formattedDoctors = data.map(doctor => ({
        doctor_id: doctor.doctor_id.toString(),
        fullname: doctor.fullname || 'Unknown Doctor',
        specialty: doctor.specialty,
        profile_image: doctor.profile_image,
        wins: doctor.wins || 0,
        draws: doctor.draws || 0,
        losses: doctor.losses || 0,
        rating: doctor.rating || 0,
        review_count: doctor.review_count || 0
      }));
      
      setDoctors(prev => ({ ...prev, [hospitalId]: formattedDoctors }));
      setFilteredDoctors(prev => ({ ...prev, [hospitalId]: formattedDoctors }));
      addLog(`Loaded ${formattedDoctors.length} doctors for hospital ID: ${hospitalId}`);
    } catch (error) {
      addLog(`Error in fetchDoctors: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(prev => ({ ...prev, doctors: { ...prev.doctors, [hospitalId]: false } }));
    }
  };

  // Handle doctor search
  const handleDoctorSearch = (searchTerm: string, columnId: string, hospitalId: string) => {
    if (!searchTerm) {
      setFilteredDoctors(prev => ({ ...prev, [hospitalId]: doctors[hospitalId] || [] }));
      return;
    }
    
    const filtered = (doctors[hospitalId] || []).filter(doctor => 
      doctor.fullname.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (doctor.specialty && doctor.specialty.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    
    setFilteredDoctors(prev => ({ ...prev, [hospitalId]: filtered }));
    addLog(`Filtered doctors to ${filtered.length} results for search: "${searchTerm}"`);
  };

  // Calculate win percentage for display
  const calculateWinPercentage = (doctor: Doctor) => {
    const wins = doctor.wins || 0;
    const total = (doctor.wins || 0) + (doctor.losses || 0) + (doctor.draws || 0);
    return total > 0 ? Math.round((wins / total) * 100) : 0;
  }

  // Light theme styles for head-to-head page
  const lightThemeStyles = `
    /* Light Theme: Head-to-head page specific styles */
    html:not(.dark) .head-to-head-title {
      color: hsl(140, 50%, 20%) !important; /* Dark green for light theme */
      background: transparent !important; /* Remove white background */
    }
  `

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: lightThemeStyles }} />
      <div className="relative min-h-screen bg-gradient-to-b from-background via-background to-background">
        {/* Background pattern */}
        <div className="absolute inset-0 bg-[url('/patterns/medical-crosses.svg')] bg-repeat opacity-5"></div>
      
      <div className="container mx-auto px-4 py-12 relative z-10">
        {/* Banner Ad - top of page */}
        <div className="my-6 w-full flex justify-center">
          <PageAds
            pageName="head-to-head"
            positions={['banner']}
            showTestAds={false}
          />
        </div>

        {/* Header */}
        <div className="flex flex-col items-center text-center mb-12">
          <div className="space-y-2">
            <div className="flex items-center gap-3 justify-center">
              <div className="relative">
                <div className="absolute -inset-2 rounded-full bg-green-500/20 animate-pulse"></div>
                <Activity className="h-8 w-8 text-green-500" />
              </div>
              <h1 className="text-3xl font-bold head-to-head-title">
                Dr Head to Head
              </h1>
            </div>
            <p className="text-foreground/70 max-w-2xl">
              Compare doctors side by side to analyze their ratings, win rates, and performance statistics.
            </p>
          </div>
        </div>
        
        {/* Main comparison area */}
        <div className="flex flex-wrap gap-8 justify-center">
          <AnimatePresence mode="popLayout">
            {columns.map((column) => (
              <motion.div
                key={column.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9, transition: { duration: 0.2 } }}
                transition={{ duration: 0.3 }}
                className="w-[calc(25%-2rem)] min-w-[300px]"
                layout
              >
                <Card className="h-full shadow-xl overflow-hidden bg-background/40 backdrop-blur-sm border border-green-500/30 hover:shadow-green-900/10 hover:border-green-500/50 transition-all">
                  <CardHeader className="bg-gradient-to-b from-green-500/20 to-transparent backdrop-blur-md border-b border-green-500/20 flex flex-row justify-between items-center">
                    <div>
                      <CardTitle className="text-foreground">Doctor {columns.indexOf(column) + 1}</CardTitle>
                      <CardDescription className="text-foreground/70">
                        Compare statistics
                      </CardDescription>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => removeColumn(column.id)}
                      className="h-8 w-8 rounded-full text-foreground/70 hover:text-foreground hover:bg-red-500/20"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </CardHeader>
                  <CardContent className="p-6 space-y-4">
                    {/* Country Selection */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium flex items-center gap-2 text-foreground/90">
                        <Globe2 className="w-4 h-4 text-green-500" />
                        Select Country
                      </label>
                      <Popover
                        open={open[`country-${column.id}`]}
                        onOpenChange={(isOpen) => setOpen((prev) => ({ ...prev, [`country-${column.id}`]: isOpen }))}
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal bg-background/50 border-green-500/50 hover:bg-green-500/10 text-foreground"
                          >
                            {column.country?.country_name || "Select country..."}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="p-0 bg-background/90 border border-green-500/50 shadow-[0_0_15px_rgba(34,197,94,0.15)]" align="start" side="bottom">
                          <Command className="bg-transparent text-foreground" filter={(value, search) => {
                            // Custom filter function for better matching
                            if (value.toLowerCase().includes(search.toLowerCase())) return 1
                            return 0
                          }}>
                            <div className="relative">
                              <CommandInput
                                placeholder="Search countries..."
                                className="focus:ring-1 focus:ring-green-500/50 pr-8 text-green-500"
                                value={searchTerms[`country-${column.id}`] || ''}
                                onValueChange={(value) => {
                                  setSearchTerms((prev) => ({ ...prev, [`country-${column.id}`]: value }));
                                  handleCountrySearch(value);
                                }}
                              />
                              {searchTerms[`country-${column.id}`] && (
                                <button
                                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-green-500/60 hover:text-green-500"
                                  onClick={() => clearSearch(`country-${column.id}`)}
                                >
                                  <X className="h-4 w-4" />
                                </button>
                              )}
                            </div>
                            <CommandList className="max-h-[300px]">
                              <CommandEmpty className="text-foreground/60 p-6 flex flex-col items-center justify-center">
                                <div className="mb-3 text-green-500 bg-green-500/10 p-3 rounded-full">
                                  <Building className="w-5 h-5" />
                                </div>
                                <p className="font-medium text-foreground/80">No country found</p>
                                <p className="text-sm text-foreground/40 mt-1">Try a different search term</p>
                              </CommandEmpty>
                              <CommandGroup>
                                {loading.countries === true ? (
                                  <div className="flex flex-col gap-2 py-4 px-1">
                                    <div className="flex items-center gap-2">
                                      <Skeleton className="h-4 w-4 rounded-full" />
                                      <Skeleton className="h-4 w-[80%]" />
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <Skeleton className="h-4 w-4 rounded-full" />
                                      <Skeleton className="h-4 w-[65%]" />
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <Skeleton className="h-4 w-4 rounded-full" />
                                      <Skeleton className="h-4 w-[70%]" />
                                    </div>
                                  </div>
                                ) : (
                                  <ScrollArea className="h-[50vh] max-h-[300px] pr-3">
                                    {filteredCountries.map((country) => (
                                      <CommandItem
                                        key={country.country_id}
                                        value={country.country_name?.toLowerCase() || ""}
                                        onSelect={() => {
                                          updateColumn(column.id, {
                                            country,
                                            hospital: undefined,
                                            doctor: undefined,
                                          })
                                          setOpen((prev) => ({ ...prev, [`country-${column.id}`]: false }))
                                          fetchHospitals(country.country_id.toString())
                                        }}
                                        className="cursor-pointer hover:bg-green-500/20 group flex items-center gap-2 py-2"
                                      >
                                        <div className="flex items-center gap-2 w-full">
                                          <div className="flex-shrink-0">
                                            <Globe2 className="h-4 w-4 text-green-500/80 group-hover:text-green-500 transition-colors" />
                                          </div>
                                          <span className="flex-grow group-hover:text-green-300 transition-colors">{country.country_name}</span>
                                          {country.flag_url && (
                                            <div className="w-5 h-3 overflow-hidden rounded-sm border border-border/50">
                                              <Image src={country.flag_url} alt={country.country_name} width={20} height={12} className="w-full h-full object-cover" />
                                            </div>
                                          )}
                                        </div>
                                      </CommandItem>
                                    ))}
                                  </ScrollArea>
                                )}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                    </div>

                    {/* Hospital Selection */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium flex items-center gap-2 text-foreground/90">
                        <Building className="w-4 h-4 text-green-500" />
                        Select Hospital
                      </label>
                      <Popover
                        open={open[`hospital-${column.id}`]}
                        onOpenChange={(isOpen) => setOpen((prev) => ({ ...prev, [`hospital-${column.id}`]: isOpen }))}
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal bg-background/50 border-green-500/50 hover:bg-green-500/10 text-foreground",
                              !column.country && "opacity-50 cursor-not-allowed hover:bg-transparent"
                            )}
                            disabled={!column.country}
                          >
                            {column.hospital?.hospital_name || "Select hospital..."}
                            {!column.country && (
                              <span className="ml-auto text-xs text-foreground/40">
                                Select a country first
                              </span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="p-0 bg-background/90 border border-green-500/50 shadow-[0_0_15px_rgba(34,197,94,0.15)]" align="start" side="bottom">
                          <Command className="bg-transparent text-foreground" filter={(value, search) => {
                            // Custom filter function for better matching
                            if (value.toLowerCase().includes(search.toLowerCase())) return 1
                            return 0
                          }}>
                            <div className="relative">
                              <CommandInput
                                placeholder="Search hospitals..."
                                className="focus:ring-1 focus:ring-green-500/50 pr-8 text-green-500"
                                value={searchTerms[`hospital-${column.id}`] || ''}
                                onValueChange={(value) => {
                                  setSearchTerms((prev) => ({ ...prev, [`hospital-${column.id}`]: value }));
                                  if (column.country && column.country.country_id) {
                                    handleHospitalSearch(value, column.id, column.country.country_id.toString());
                                  }
                                }}
                              />
                              {searchTerms[`hospital-${column.id}`] && (
                                <button
                                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-green-500/60 hover:text-green-500"
                                  onClick={() => clearSearch(`hospital-${column.id}`)}
                                >
                                  <X className="h-4 w-4" />
                                </button>
                              )}
                            </div>
                            <CommandList className="max-h-[300px]">
                              <CommandEmpty className="text-foreground/60 p-6 flex flex-col items-center justify-center">
                                <div className="mb-3 text-green-500 bg-green-500/10 p-3 rounded-full">
                                  <Building className="w-5 h-5" />
                                </div>
                                <p className="font-medium text-foreground/80">No hospital found</p>
                                <p className="text-sm text-foreground/40 mt-1">Try a different search term</p>
                              </CommandEmpty>
                              <CommandGroup>
                                {column.country && loading.hospitals[column.country.country_id] === true ? (
                                  <div className="flex flex-col gap-2 py-4 px-1">
                                    <div className="flex items-center gap-2">
                                      <Skeleton className="h-4 w-4 rounded-full" />
                                      <Skeleton className="h-4 w-[80%]" />
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <Skeleton className="h-4 w-4 rounded-full" />
                                      <Skeleton className="h-4 w-[65%]" />
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <Skeleton className="h-4 w-4 rounded-full" />
                                      <Skeleton className="h-4 w-[70%]" />
                                    </div>
                                  </div>
                                ) : (
                                  <ScrollArea className="h-[50vh] max-h-[300px] pr-3">
                                    {column.country && column.country.country_id &&
                                      filteredHospitals[column.country.country_id]?.map((hospital) => (
                                        <CommandItem
                                          key={hospital.hospital_id}
                                          value={`${hospital.hospital_name?.toLowerCase() || ""} ${hospital.location?.toLowerCase() || ""}`}
                                          onSelect={() => {
                                            updateColumn(column.id, {
                                              hospital,
                                              doctor: undefined,
                                            })
                                            setOpen((prev) => ({ ...prev, [`hospital-${column.id}`]: false }))
                                            fetchDoctors(hospital.hospital_id.toString())
                                          }}
                                          className="cursor-pointer hover:bg-green-500/20 group flex items-center gap-2 py-2"
                                        >
                                          <div className="flex items-center gap-2 w-full">
                                            <div className="flex-shrink-0">
                                              <Building className="h-4 w-4 text-green-500/80 group-hover:text-green-500 transition-colors" />
                                            </div>
                                            <div className="flex-grow">
                                              <span className="group-hover:text-green-300 transition-colors">{hospital.hospital_name}</span>
                                              {hospital.location && (
                                                <p className="text-xs text-foreground/40 group-hover:text-foreground/60 transition-colors">
                                                  {hospital.location}
                                                </p>
                                              )}
                                            </div>
                                          </div>
                                        </CommandItem>
                                      ))
                                    }
                                  </ScrollArea>
                                )}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                    </div>

                    {/* Doctor Selection */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium flex items-center gap-2 text-foreground/90">
                        <User className="w-4 h-4 text-green-500" />
                        Select Doctor
                      </label>
                      <Popover
                        open={open[`doctor-${column.id}`]}
                        onOpenChange={(isOpen) => setOpen((prev) => ({ ...prev, [`doctor-${column.id}`]: isOpen }))}
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal bg-background/50 border-green-500/50 hover:bg-green-500/10 text-foreground",
                              !column.hospital && "opacity-50 cursor-not-allowed hover:bg-transparent"
                            )}
                            disabled={!column.hospital}
                          >
                            {column.doctor?.fullname || "Select doctor..."}
                            {!column.hospital && (
                              <span className="ml-auto text-xs text-foreground/40">
                                Select a hospital first
                              </span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="p-0 bg-background/90 border border-green-500/50 shadow-[0_0_15px_rgba(34,197,94,0.15)]" align="start" side="bottom">
                          <Command className="bg-transparent text-foreground" filter={(value, search) => {
                            // Custom filter function for better matching
                            if (value.toLowerCase().includes(search.toLowerCase())) return 1
                            return 0
                          }}>
                            <div className="relative">
                              <CommandInput
                                placeholder="Search doctors..."
                                className="focus:ring-1 focus:ring-green-500/50 pr-8 text-green-500"
                                value={searchTerms[`doctor-${column.id}`] || ''}
                                onValueChange={(value) => {
                                  setSearchTerms((prev) => ({ ...prev, [`doctor-${column.id}`]: value }));
                                  if (column.hospital && column.hospital.hospital_id) {
                                    handleDoctorSearch(value, column.id, column.hospital.hospital_id.toString());
                                  }
                                }}
                              />
                              {searchTerms[`doctor-${column.id}`] && (
                                <button
                                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-green-500/60 hover:text-green-500"
                                  onClick={() => clearSearch(`doctor-${column.id}`)}
                                >
                                  <X className="h-4 w-4" />
                                </button>
                              )}
                            </div>
                            <CommandList className="max-h-[300px]">
                              <CommandEmpty className="text-foreground/60 p-6 flex flex-col items-center justify-center">
                                <div className="mb-3 text-green-500 bg-green-500/10 p-3 rounded-full">
                                  <User className="w-5 h-5" />
                                </div>
                                <p className="font-medium text-foreground/80">No doctor found</p>
                                <p className="text-sm text-foreground/40 mt-1">Try a different search term</p>
                              </CommandEmpty>
                              <CommandGroup>
                                {column.hospital && column.hospital.hospital_id && loading.doctors[column.hospital.hospital_id] === true ? (
                                  <div className="flex flex-col gap-2 py-4 px-1">
                                    <div className="flex items-center gap-2">
                                      <Skeleton className="h-4 w-4 rounded-full" />
                                      <Skeleton className="h-4 w-[80%]" />
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <Skeleton className="h-4 w-4 rounded-full" />
                                      <Skeleton className="h-4 w-[65%]" />
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <Skeleton className="h-4 w-4 rounded-full" />
                                      <Skeleton className="h-4 w-[70%]" />
                                    </div>
                                  </div>
                                ) : (
                                  <ScrollArea className="h-[50vh] max-h-[300px] pr-3">
                                    {column.hospital && column.hospital.hospital_id &&
                                      filteredDoctors[column.hospital.hospital_id]?.map((doctor) => (
                                        <CommandItem
                                          key={doctor.doctor_id}
                                          value={`${doctor.fullname?.toLowerCase() || ""} ${doctor.specialty?.toLowerCase() || ""}`}
                                          onSelect={() => {
                                            updateColumn(column.id, {
                                              doctor: {
                                                ...doctor,
                                                hospital_name: column.hospital?.hospital_name
                                              }
                                            });
                                            setOpen((prev) => ({ ...prev, [`doctor-${column.id}`]: false }));
                                          }}
                                          className="cursor-pointer hover:bg-green-500/20 group flex items-center gap-2 py-2"
                                        >
                                          <div className="flex items-center gap-2 w-full">
                                            <div className="flex-shrink-0">
                                              {doctor.profile_image ? (
                                                <Avatar className="h-6 w-6">
                                                  <AvatarImage src={doctor.profile_image} alt={doctor.fullname} />
                                                  <AvatarFallback className="bg-green-500/20 text-green-500">
                                                    {doctor.fullname.charAt(0)}
                                                  </AvatarFallback>
                                                </Avatar>
                                              ) : (
                                                <div className="h-6 w-6 rounded-full bg-green-500/20 flex items-center justify-center">
                                                  <span className="text-[10px] font-medium text-green-500">{doctor.fullname.charAt(0)}</span>
                                                </div>
                                              )}
                                            </div>
                                            <div className="flex-grow">
                                              <span className="group-hover:text-green-300 transition-colors">{doctor.fullname}</span>
                                              {doctor.specialty && (
                                                <div className="flex items-center text-xs text-foreground/40 group-hover:text-foreground/60 transition-colors">
                                                  <Stethoscope className="w-3 h-3 mr-1 text-green-500/70" />
                                                  {doctor.specialty}
                                                </div>
                                              )}
                                            </div>
                                            <div className="flex items-center">
                                              <Star className="h-3 w-3 text-yellow-500 mr-1" />
                                              <span className="text-xs font-medium text-foreground/60">{doctor.rating?.toFixed(1) || "—"}</span>
                                            </div>
                                          </div>
                                        </CommandItem>
                                      ))
                                    }
                                  </ScrollArea>
                                )}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                    </div>

                    {/* Doctor Info */}
                    {column.doctor && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                        className="mt-4 p-4 bg-gradient-to-b from-green-500/10 to-green-500/5 rounded-lg space-y-3 border border-green-500/30"
                      >
                        <div className="flex items-center gap-3">
                          <Avatar className="h-14 w-14 border-2 border-green-500/50 ring-2 ring-green-500/20 ring-offset-1 ring-offset-black">
                            {column.doctor.profile_image ? (
                              <AvatarImage src={column.doctor.profile_image} alt={column.doctor.fullname} />
                            ) : (
                              <AvatarFallback className="bg-green-500/20 text-green-500 text-lg font-bold">
                                {column.doctor.fullname.charAt(0)}
                              </AvatarFallback>
                            )}
                          </Avatar>
                          <div>
                            <HoverCard>
                              <HoverCardTrigger asChild>
                                <h3 className="font-bold text-foreground hover:text-green-300 cursor-pointer transition-colors">
                                  {column.doctor.fullname}
                                </h3>
                              </HoverCardTrigger>
                              <HoverCardContent className="w-72 bg-background/90 border border-green-500/30 text-foreground">
                                <div className="flex justify-between">
                                  <Avatar className="h-12 w-12">
                                    {column.doctor.profile_image ? (
                                      <AvatarImage src={column.doctor.profile_image} alt={column.doctor.fullname} />
                                    ) : (
                                      <AvatarFallback className="bg-green-500/20 text-green-500">
                                        {column.doctor.fullname.charAt(0)}
                                      </AvatarFallback>
                                    )}
                                  </Avatar>
                                  <div className="flex flex-col items-end">
                                    <Badge variant="outline" className="bg-green-500/10 border-green-500/40 text-green-400">
                                      {calculateWinPercentage(column.doctor)}% Win Rate
                                    </Badge>
                                  </div>
                                </div>
                                <div className="space-y-1 mt-3">
                                  <h4 className="text-sm font-semibold text-foreground">{column.doctor.fullname}</h4>
                                  <p className="text-xs text-foreground/70">{column.doctor.specialty}</p>
                                  {column.doctor.hospital_name && (
                                    <p className="text-xs text-foreground/70 flex items-center gap-1">
                                      <Building className="h-3 w-3 text-green-500" />
                                      {column.doctor.hospital_name}
                                    </p>
                                  )}
                                </div>
                                <Separator className="my-2 bg-green-500/20" />
                                <div className="grid grid-cols-2 gap-2 text-xs text-foreground/70">
                                  <div className="flex items-center gap-1">
                                    <Trophy className="h-3 w-3 text-yellow-500" />
                                    <span>Wins: {column.doctor.wins || 0}</span>
                                  </div>
                                  <div className="flex items-center gap-1 justify-end">
                                    <Star className="h-3 w-3 text-yellow-500" />
                                    <span>Rating: {column.doctor.rating?.toFixed(1) || "0.0"}</span>
                                  </div>
                                </div>
                              </HoverCardContent>
                            </HoverCard>
                            <p className="text-sm text-foreground/70 flex items-center gap-1">
                              <Stethoscope className="w-3 h-3 text-green-500" />
                              {column.doctor.specialty}
                            </p>
                          </div>
                        </div>

                        <Separator className="my-2 bg-green-500/20" />

                        <div className="grid grid-cols-2 gap-2 mt-2">
                          <div className="bg-background/40 p-2 rounded flex flex-col items-center group hover:bg-green-500/10 transition-colors">
                            <div className="flex items-center gap-1 mb-1">
                              <Star className="w-4 h-4 text-yellow-500 fill-yellow-500" />
                              <span className="font-bold text-foreground">{column.doctor.rating?.toFixed(1) || "0.0"}</span>
                            </div>
                            <span className="text-xs text-foreground/60">Rating</span>
                          </div>

                          <div className="bg-background/40 p-2 rounded flex flex-col items-center group hover:bg-green-500/10 transition-colors">
                            <div className="flex items-center gap-1 mb-1">
                              <Award className="w-4 h-4 text-green-500" />
                              <span className="font-bold text-foreground">{calculateWinPercentage(column.doctor)}%</span>
                            </div>
                            <span className="text-xs text-foreground/60">Win Rate</span>
                          </div>

                          <div className="bg-background/40 p-2 rounded flex flex-col items-center group hover:bg-green-500/10 transition-colors">
                            <div className="flex items-center gap-1 mb-1">
                              <Zap className="w-4 h-4 text-yellow-500" />
                              <span className="font-bold text-foreground">{column.doctor.wins || 0}</span>
                            </div>
                            <span className="text-xs text-foreground/60">Wins</span>
                          </div>

                          <div className="bg-background/40 p-2 rounded flex flex-col items-center group hover:bg-green-500/10 transition-colors">
                            <div className="flex items-center gap-1 mb-1">
                              <Activity className="w-4 h-4 text-green-500" />
                              <span className="font-bold text-foreground">{column.doctor.review_count || 0}</span>
                            </div>
                            <span className="text-xs text-foreground/60">Reviews</span>
                          </div>
                        </div>

                        <Button
                          variant="default"
                          className="w-full mt-2 bg-gradient-to-r from-green-600/90 to-green-600/70 hover:from-green-500 hover:to-green-600/80 text-foreground border border-green-500/30 shadow-md shadow-green-900/20 hover:shadow-green-900/30 transition-all"
                          asChild
                        >
                          <Link href={`/doctors/${column.doctor.doctor_id}`}>View Full Profile</Link>
                        </Button>
                      </motion.div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            ))}

            {/* Add Doctor Button - Placed inline with other cards */}
            {columns.length < 4 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.2 }}
                className="w-[calc(25%-2rem)] min-w-[300px]"
                layout
              >
                <Card className="h-full shadow-xl overflow-hidden bg-background/40 border-2 border-dashed border-green-500/30 hover:border-green-500/60 transition-all">
                  <CardHeader className="bg-gradient-to-b from-green-500/20 to-transparent backdrop-blur-md border-b border-green-500/20">
                    <CardTitle className="text-foreground text-center">Add Doctor</CardTitle>
                    <CardDescription className="text-foreground/70 text-center">
                      Compare more doctors
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-6 flex items-center justify-center">
                    <Button
                      onClick={addColumn}
                      variant="ghost"
                      className="w-full h-full p-0 relative group bg-transparent text-foreground flex flex-col items-center justify-center transition-all hover:bg-green-500/5"
                    >
                      <div className="flex flex-col items-center justify-center gap-4 py-16">
                        <div className="relative w-16 h-16 flex items-center justify-center">
                          <div className="absolute inset-0 rounded-full bg-green-500/10 group-hover:bg-green-500/20 transition-all"></div>
                          <motion.div
                            className="relative"
                            initial={{ rotate: 0 }}
                            animate={{ rotate: 360 }}
                            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                          >
                            <motion.div
                              className="absolute -inset-8 opacity-20 bg-gradient-to-r from-green-400/0 via-green-400/70 to-green-400/0"
                              animate={{
                                rotate: [0, 180, 360],
                                scale: [0.8, 1, 0.8]
                              }}
                              transition={{
                                duration: 3,
                                repeat: Infinity,
                                repeatType: "reverse"
                              }}
                            />
                            <Plus className="w-8 h-8 text-green-500" />
                          </motion.div>
                        </div>
                      </div>
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Empty state when no columns */}
        {columns.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex flex-col items-center justify-center py-16 px-4 space-y-8"
          >
            <div className="relative">
              <motion.div
                className="absolute -inset-4 rounded-full bg-green-500/10 opacity-75"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.7, 0.2, 0.7]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              />
              <Stethoscope className="w-16 h-16 text-green-500" />
            </div>
            <div className="text-center space-y-3 max-w-md">
              <h3 className="text-2xl font-bold text-foreground">No Doctors Selected</h3>
              <p className="text-foreground/70">
                Start by adding doctors to compare their performance, ratings, and specialties side by side.
              </p>
              <Button
                onClick={addColumn}
                className="mt-4 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-foreground border border-green-500/30 px-6"
              >
                <Plus className="mr-2 h-4 w-4" /> Add Your First Doctor
              </Button>
            </div>
          </motion.div>
        )}

        {/* Bottom Ad - after comparison section */}
        <div className="mt-16 mb-8 w-full flex justify-center">
          <PageAds
            pageName="head-to-head"
            positions={['bottom']}
            showTestAds={false}
          />
        </div>
      </div>
      </div>
    </>
  )
}
