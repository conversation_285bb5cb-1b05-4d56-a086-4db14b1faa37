"use client"

import { FAQSection } from "@/components/faq-section"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ECGDivider } from "@/components/ecg-divider"
import { HelpCircle, Lightbulb, BookOpen, HeartPulse, ArrowLeft, ChevronRight, Trophy, Award } from "lucide-react"
import Link from "next/link"

export default function HelpPage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-background/95 to-primary/5 text-foreground">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4" style={{ color: 'hsl(142, 76%, 25%)' }}>
            Help Center
          </h1>
          <p className="text-foreground/70 max-w-3xl mx-auto">
            Find answers to your questions about Doctors League and learn how to make the most of our medical league platform.
          </p>
          <Link href="/" className="inline-block mt-4">
            <Button variant="outline" className="border-primary/30 text-foreground">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Return to Homepage
            </Button>
          </Link>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16 max-w-6xl mx-auto">
          <Link href="#general">
            <Card className="h-full group" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
              <CardContent className="p-6">
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center mb-4 group-hover:bg-primary/30 transition-colors">
                    <Lightbulb className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold text-foreground mb-2 group-hover:text-primary transition-colors">Getting Started</h3>
                  <p className="text-foreground/70 mb-3">
                    New to Doctors League? Learn how to join as a Spectator, Referee, or Player and discover top medical professionals.
                  </p>
                  <div className="flex items-center text-primary text-sm font-medium">
                    Learn more <ChevronRight className="h-4 w-4 ml-1" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
          
          <Link href="#features">
            <Card className="h-full group" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
              <CardContent className="p-6">
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center mb-4 group-hover:bg-primary/30 transition-colors">
                    <Trophy className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold text-foreground mb-2 group-hover:text-primary transition-colors">League Features</h3>
                  <p className="text-foreground/70 mb-3">
                    Discover how to navigate Standings, compare Players (doctors) head-to-head, and find medical events in the Fixtures section.
                  </p>
                  <div className="flex items-center text-primary text-sm font-medium">
                    Explore features <ChevronRight className="h-4 w-4 ml-1" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
          
          <Link href="#doctors">
            <Card className="h-full group" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
              <CardContent className="p-6">
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center mb-4 group-hover:bg-primary/30 transition-colors">
                    <Award className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold text-foreground mb-2 group-hover:text-primary transition-colors">For Players & Referees</h3>
                  <p className="text-foreground/70 mb-3">
                    Information for doctors (Players) about joining the league and for patients (Referees) on how to rate and evaluate healthcare professionals.
                  </p>
                  <div className="flex items-center text-primary text-sm font-medium">
                    Medical roles <ChevronRight className="h-4 w-4 ml-1" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        </div>
        
        <ECGDivider className="my-10" />
        
        {/* FAQ Section */}
        <div id="faqs">
          <FAQSection />
        </div>
        
        <div className="text-center mt-16 max-w-2xl mx-auto">
          <h2 className="text-2xl font-bold text-foreground mb-4">Still have questions?</h2>
          <p className="text-foreground/70 mb-6">
            Our league support team is ready to help you with any questions or issues you may have.
          </p>
          <Link href="/contact">
            <Button className="bg-primary hover:bg-primary/90">
              <HelpCircle className="mr-2 h-5 w-5" />
              Contact Support
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
} 