import type { <PERSON><PERSON><PERSON> } from "next"
import "./globals.css"
import "./custom-styles.css"
import "./button-styles.css"
import { Header } from "@/components/header"
import { ConnectionStatus } from "@/components/connection-status"
import type React from "react"
import { Mail, Phone, MapPin, Globe, Twitter, Facebook, Instagram, Linkedin, HelpCircle, BookOpen } from "lucide-react"
// Import our mock toast provider instead of the original one
import { ToastProvider } from "@/components/ui/mock-toast"
import { Toaster } from "@/components/ui/mock-toast"
// Import the getCountries function
import { getCountries } from "@/lib/hybrid-data-service"
// Import SEO config and components
import { defaultMetadata, organizationSchema } from "@/lib/seo-config"
import { DoctorsLeagueLocalBusiness, SEOHealthCheck } from "@/components/seo"
import Script from "next/script"
import { NextResponse } from 'next/server'
import { Inter } from 'next/font/google'
import '@fontsource-variable/inter'
import { MobileNavigation } from '@/components/mobile-navigation'
import { reportWebVitals as reportMetric, WebVitalsMetric } from '@/lib/performance'
import Image from 'next/image'
// Import our new performance initializer component
import { PerformanceInitializer } from '@/components/performance-initializer'
import { AuthProvider } from '@/context/AuthContext'
import { DashboardReturnButton } from '@/components/dashboard-return-button'
import Link from "next/link"
// Import the ThemeProvider
import { ThemeProvider } from '@/components/theme-provider'

// Optimize font loading
const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap',
  preload: true,
  fallback: ['system-ui', 'sans-serif']
})

export const metadata: Metadata = defaultMetadata

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
}

// Track which metrics we've already reported to avoid duplicates
const reportedMetrics = new Set<string>();

// Enable Core Web Vitals reporting
export function reportWebVitals(metric: WebVitalsMetric): void {
  // Check if we've already reported this specific metric instance
  const metricKey = `${metric.id}-${metric.name}`;
  if (reportedMetrics.has(metricKey)) {
    console.log(`[RootLayout] Skipping duplicate metric report: ${metric.name}`);
    return;
  }
  
  // Mark this metric as reported
  reportedMetrics.add(metricKey);
  
  // Log that we're handling this metric
  console.log(`[RootLayout] Reporting metric: ${metric.name} (${metric.value.toFixed(2)})`);
  
  // Forward the metric to our custom reportMetric function with enhanced logging
  reportMetric((webVitalsMetric) => {
    // More detailed logging including page URL and improved formatting
    if (process.env.NODE_ENV === 'development') {
      console.log(
        `Web Vitals: ${webVitalsMetric.name} - ${webVitalsMetric.value.toFixed(2)} (${webVitalsMetric.rating}) - ${webVitalsMetric.url || 'unknown page'}`
      );
    }
  });
}

// Update the RootLayout component to fetch countries
export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  // Fetch countries for the footer
  const countries = await getCountries()
  
  // Format countries for mobile navigation if needed
  const formattedCountries = countries.map(country => ({
    name: country.country_name,
    code: country.country_id.toString()
  }))

  return (
    <html lang="en" className={`scroll-smooth ${inter.className}`}>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="canonical" href="https://doctorsleagues.com" />
        
        {/* Remove preload for Inter Variable font since it's causing errors */}
        
        {/* Preconnect to important third-party domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://www.googletagmanager.com" />
        
        {/* Preload the LCP image (Hero Background) with higher priority */}
        <link rel="preload" fetchPriority="high" as="image" href="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/hero-bg.jpg-jFYKbLWbhBg1O4C6J4ACQ2hVfLLFZB.jpeg" type="image/jpeg" />
        
        {/* Load critical fonts */}
        <link 
          href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
          rel="stylesheet"
        />
        
        <Script
          id="schema-org-organization"
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }}
        />
        
        {/* Add Google Analytics - load in idle time */}
        <Script
          id="google-analytics"
          strategy="lazyOnload"
          dangerouslySetInnerHTML={{
            __html: `
              (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
              })(window,document,'script','dataLayer','GTM-XXXXX');
            `,
          }}
        />
      </head>
      <body className="font-sans">
        {/* Google Tag Manager noscript (for browsers with JavaScript disabled) */}
        <noscript>
          <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-XXXXX"
            height="0"
            width="0"
            style={{ display: 'none', visibility: 'hidden' }}
            title="gtm"
          />
        </noscript>
        
        {/* Add our performance initializer component */}
        <PerformanceInitializer />
        
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
        <AuthProvider>
        <ToastProvider>
          <Header />
          <DashboardReturnButton />
          <main className="min-h-[calc(100vh-4rem)] pt-20">
            {children}
            
            {/* Global SEO Health Check */}
            {process.env.NODE_ENV === 'development' && (
              <div id="seo-health-check-container" suppressHydrationWarning>
                <Script id="seo-health-check-script" strategy="afterInteractive">
                  {`
                    if (typeof window !== 'undefined') {
                      setTimeout(() => {
                        const seoElement = document.getElementById('seo-health-check-global');
                        if (!seoElement) {
                          const button = document.createElement('button');
                          button.id = 'seo-health-check-global';
                          button.className = 'fixed bottom-4 right-4 z-50 bg-primary/80 text-primary-foreground border border-primary/40 rounded-md px-3 py-2 text-sm font-medium hover:bg-primary/90 shadow-lg shadow-primary/20 flex items-center gap-1.5 transition-all duration-300 hover:shadow-primary/30';
                          
                          // Create icon element
                          const icon = document.createElement('span');
                          icon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 9-5-5 5-5"/><path d="M10 19h4c4 0 7-3 7-7s-3-7-7-7h-4"/><path d="M14 19v-7c0-4-3.333-7-7-7H3"/></svg>';
                          button.appendChild(icon);
                          
                          const text = document.createElement('span');
                          text.textContent = 'SEO Check';
                          button.appendChild(text);
                          
                          button.onclick = function() {
                            // Check if we're already on the SEO tools page
                            if (window.location.pathname === '/dev/seo-tools') {
                              return;
                            }
                            
                            // Get current page info for the SEO tools
                            const currentUrl = window.location.pathname;
                            const currentTitle = document.title;
                            
                            // Navigate to SEO tools with query params
                            window.location.href = '/dev/seo-tools?url=' + 
                              encodeURIComponent(currentUrl) + 
                              '&title=' + encodeURIComponent(currentTitle);
                          };
                          document.body.appendChild(button);
                        }
                      }, 1000);
                    }
                  `}
                </Script>
              </div>
            )}
          </main>
          
          <footer className="bg-background border-t border-border py-12 footer-dark-theme" style={{
            backgroundColor: 'var(--background)',
            borderColor: 'var(--border)'
          }}>
            {/* Light theme footer styling - applied via CSS class */}
            <style dangerouslySetInnerHTML={{
              __html: `
                html:not(.dark) footer.footer-dark-theme {
                  background-color: hsl(120, 25%, 90%) !important;
                  border-color: hsl(120, 20%, 75%) !important;
                }
                
                html.dark footer.footer-dark-theme {
                  background-color: var(--background) !important;
                  border-color: var(--border) !important;
                }
              `
            }} />
            <div className="container mx-auto px-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
                <div>
                  <div className="flex items-center space-x-2 mb-4">
                    <Image
                      src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo-gO7TTezH8tR3LvsxNVrbtcdUVAqKGB.png"
                      alt="Doctor's Leagues"
                      width={40}
                      height={40}
                      className="h-10 w-10"
                      priority
                    />
                    <span className="text-foreground font-bold text-xl">Doctor's Leagues</span>
                  </div>
                  <p className="text-muted-green mb-4">Where Healthcare Heroes Compete for Your Trust</p>
                  <div className="flex space-x-4">
                    <a href="https://twitter.com/DoctorsLeague" aria-label="Twitter" className="text-muted-green hover:text-primary transition-colors">
                      <Twitter className="h-5 w-5" />
                    </a>
                    <a href="https://facebook.com/DoctorsLeague" aria-label="Facebook" className="text-muted-green hover:text-primary transition-colors">
                      <Facebook className="h-5 w-5" />
                    </a>
                    <a href="https://instagram.com/doctorsleague" aria-label="Instagram" className="text-muted-green hover:text-primary transition-colors">
                      <Instagram className="h-5 w-5" />
                    </a>
                    <a href="https://linkedin.com/company/doctors-league" aria-label="LinkedIn" className="text-muted-green hover:text-primary transition-colors">
                      <Linkedin className="h-5 w-5" />
                    </a>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-4 text-foreground">Quick Links</h3>
                  <ul className="space-y-2">
                    <li>
                      <Link href="/blog" className="text-muted-green hover:text-foreground transition-colors flex items-center gap-1.5">
                        <BookOpen className="h-4 w-4 text-primary" />
                        Medical Insights Blog
                      </Link>
                    </li>
                    <li>
                      <Link href="/help" className="text-muted-green hover:text-foreground transition-colors flex items-center gap-1.5">
                        <HelpCircle className="h-4 w-4 text-primary" />
                        Help Center
                      </Link>
                    </li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-4 text-foreground">Contact Us</h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <MapPin className="h-5 w-5 text-primary mr-2 mt-0.5" />
                      <span className="text-muted-green">123 League Street, Medical City, Bahrain</span>
                    </li>
                    <li className="flex items-center">
                      <Phone className="h-5 w-5 text-primary mr-2" />
                      <span className="text-muted-green">+973 1234 5678</span>
                    </li>
                    <li className="flex items-center">
                      <Mail className="h-5 w-5 text-primary mr-2" />
                      <span className="text-muted-green"><EMAIL></span>
                    </li>
                    <li className="flex items-center">
                      <Globe className="h-5 w-5 text-primary mr-2" />
                      <span className="text-muted-green">www.doctorsleagues.com</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Add development mode tools links */}
              {process.env.NODE_ENV === 'development' && (
                <div className="mt-8 pt-6 border-t border-border flex flex-wrap gap-4 items-center justify-center">
                  <Link 
                    href="/dev/seo-tools" 
                    className="text-sm px-3 py-1.5 rounded-md bg-amber-900/40 hover:bg-amber-900/60 text-amber-300 flex items-center gap-1.5 transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-search-code"><path d="m9 9-5-5 5-5"/><path d="M10 19h4c4 0 7-3 7-7s-3-7-7-7h-4"/><path d="M14 19v-7c0-4-3.333-7-7-7H3"/><circle cx="17" cy="17" r="3"/><path d="m21 21-1.9-1.9"/></svg>
                    SEO Tools
                  </Link>
                  <Link 
                    href="/dev/performance" 
                    className="text-sm px-3 py-1.5 rounded-md bg-blue-900/40 hover:bg-blue-900/60 text-blue-300 flex items-center gap-1.5 transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-bar-chart-3"><path d="M3 3v18h18"/><path d="M18 17V9"/><path d="M13 17V5"/><path d="M8 17v-3"/></svg>
                    Core Vitals
                  </Link>
                </div>
              )}
              
              <div className="border-t border-border mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p className="text-muted-green text-sm">&copy; {new Date().getFullYear()} Doctor's Leagues. All rights reserved.</p>
                <div className="flex space-x-6 mt-4 md:mt-0">
                  <Link href="/policies/privacy" className="text-muted-green hover:text-foreground text-sm transition-colors">
                    Privacy Policy
                  </Link>
                  <Link href="/policies/terms" className="text-muted-green hover:text-foreground text-sm transition-colors">
                    Terms of Service
                  </Link>
                  <Link href="/policies/cookies" className="text-muted-green hover:text-foreground text-sm transition-colors">
                    Cookie Policy
                  </Link>
                  <Link href="/sitemap" className="text-muted-green hover:text-foreground text-sm transition-colors">
                    Sitemap
                  </Link>
                </div>
              </div>
            </div>
          </footer>
          <ConnectionStatus />
          <Toaster />
          <DoctorsLeagueLocalBusiness />
        </ToastProvider>
        <MobileNavigation 
          countries={formattedCountries}
          isLoggedIn={false}
          userName=""
        />
        </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
