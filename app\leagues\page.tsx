"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function LeaguesPage() {
  const router = useRouter();
  
  useEffect(() => {
    // Redirect to the default country division (e.g., 1 for Saudi Arabia)
    router.push("/divisions/1");
  }, [router]);
  
  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-background to-background">
      <div className="text-center">
        <div className="h-12 w-12 border-4 border-t-primary border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin mx-auto"></div>
        <p className="mt-4 text-lg text-foreground/70">Redirecting to leagues...</p>
      </div>
    </div>
  );
} 