// Add function to get profile image URL from Supabase storage
export function getSupabaseProfileImageUrl(imagePath: string | null): string {
  if (!imagePath) return '/placeholder-avatar.png';
  
  // Check if it's already a full URL
  if (imagePath.startsWith('http')) return imagePath;
  
  // If it's a storage path, convert it to a full URL
  if (imagePath.startsWith('/')) imagePath = imagePath.substring(1);
  
  // Handle different path formats
  const bucketName = 'doctor-profiles'; // Updated to the correct bucket name
  const subFolder = 'real_photos'; // The subfolder where doctor profile images are stored
  const supabaseUrl = 'https://uapbzzscckhtptliynyj.supabase.co'; // Your Supabase URL
  
  // Log the path type we're processing (for debugging)
  console.log(`Processing image path: ${imagePath}`);
  
  // Construct the full path within the bucket
  // The imagePath from the database should already include the subfolder, e.g., "real_photos/image.webp"
  // If it doesn't, we might need to prepend it, but based on the upload action, it should.
  let fullStoragePath = imagePath;
  if (!imagePath.startsWith(subFolder + '/')) {
    // This case should ideally not happen if upload is consistent, but as a safeguard
    fullStoragePath = `${subFolder}/${imagePath}`;
  }
  
  // Construct the public URL
  return `${supabaseUrl}/storage/v1/object/public/${bucketName}/${fullStoragePath}`;
}
