"use client"

import React, { useState, useEffect, useCallback } from "react"
import Image from 'next/image'
import { OptimizedImage } from "@/components/optimized-image"
import { PerformanceMonitor } from "@/components/performance-monitor"

import { <PERSON> } from "@/lib/hybrid-data-service"
import { createBrowserClient } from "@/lib/supabase-client"
import { Button } from "@/components/ui/button"
// Optimized imports - only import what we need
import { Star, BellIcon as Whistle, Trophy, Medal, Stethoscope, Ambulance, Dna, Activity } from "lucide-react"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import dynamic from 'next/dynamic' // Import dynamic
import { ECGDivider } from "@/components/ecg-divider"
import { FeaturedDoctorCard } from "@/components/featured-doctor-card"
// Dynamically import the modal
const PremiumMedicalModal = dynamic(() => import('@/components/premium-medical-modal').then(mod => mod.PremiumMedicalModal), { ssr: false })
// Import Shadcn Table components - only what we need
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
// Import Shadcn Card components - only what we need
import { Card } from "@/components/ui/card";
// Import Ad type and action
// Import specific ad fetchers
import {
  Ad,
  getHomepageBannerAds,
  getHomepageSideLeftAds,
  getHomepageSideRightAds,
  getHomepageBottomAds,
  getHomepageInContentAds,
} from "@/actions/ad-actions";
// TestAdDisplay might not be needed here anymore unless used as a fallback within PositionedAdDisplay
// import { TestAdDisplay } from "@/components/ads/test-ad-display";
import { PositionedAdDisplay } from "@/components/ads/positioned-ad-display";
import { getImageUrl, isLocalFilePath } from "@/lib/utils"
import { getSupabaseProfileImageUrl } from "@/app/lib/utils"

// Light theme green color styles - only applied when light theme is active
const lightThemeGreenStyles = `
  /* Light Theme: Make ALL headers dark green for visibility on light background */
  /* Dark Theme: Headers stay white (original) for visibility on dark background */
  
  html:not(.dark) h1,
  html:not(.dark) h2,
  html:not(.dark) h3:not(.featured-doctor-name),
  html:not(.dark) h4,
  html:not(.dark) h5,
  html:not(.dark) h6,
  html:not(.dark) .text-2xl.font-bold,
  html:not(.dark) .text-3xl.font-bold,
  html:not(.dark) .text-4xl.font-bold,
  html:not(.dark) .text-5xl.font-bold {
    color: hsl(140, 50%, 20%) !important; /* Dark green for light theme */
  }

  /* EXCEPTION: Featured Doctor Names - White text with dark green outline (LIGHT THEME ONLY) */
  html:not(.dark) h3.featured-doctor-name {
    color: #ffffff !important;
    text-shadow:
      1px 1px 0 hsl(142, 76%, 25%),
      -1px -1px 0 hsl(142, 76%, 25%),
      1px -1px 0 hsl(142, 76%, 25%),
      -1px 1px 0 hsl(142, 76%, 25%),
      0 1px 0 hsl(142, 76%, 25%),
      1px 0 0 hsl(142, 76%, 25%),
      0 -1px 0 hsl(142, 76%, 25%),
      -1px 0 0 hsl(142, 76%, 25%) !important;
  }
  
  /* Light Theme: Secondary text (gray) becomes lighter green */
  html:not(.dark) .text-foreground\\/60,
  html:not(.dark) .text-foreground\\/70,
  html:not(.dark) .text-foreground\\/80,
  html:not(.dark) .text-foreground\\/90,
  html:not(.dark) p:not(.hero-text),
  html:not(.dark) .text-sm {
    color: hsl(140, 30%, 35%) !important; /* Lighter green for light theme */
  }
  
  /* Light Theme: Background becomes very light green-white */
  html:not(.dark) body,
  html:not(.dark) .homepage-background {
    background-color: hsl(120, 20%, 97%) !important;
  }
  
  /* IMPORTANT: Preserve white text for hero sections in BOTH themes */
  .hero-title,
  .hero-text,
  .hero-title-custom,
  .about-overlay-text {
    color: #ffffff !important; /* Always white for hero overlays */
  }
  
  /* Ensure hero text stays white even in light theme - MAXIMUM SPECIFICITY */
  html:not(.dark) h1.hero-title,
  html:not(.dark) h1.hero-title-custom,
  html:not(.dark) p.hero-text,
  html:not(.dark) .hero-title.text-5xl,
  html:not(.dark) .hero-text.text-lg,
  html:not(.dark) motion-h1.hero-title,
  html:not(.dark) motion-p.hero-text,
  html:not(.dark) .about-overlay-text {
    color: #ffffff !important; /* Force white for hero text in light theme */
    background-color: transparent !important;
  }
  
  /* Override ANY green text in hero sections for light theme */
  html:not(.dark) [class*="hero"] h1,
  html:not(.dark) [class*="hero"] h2,
  html:not(.dark) [class*="hero"] p,
  html:not(.dark) [class*="hero"] span {
    color: #ffffff !important;
  }
  
  /* Dark Theme: Keep original white headers (no changes needed - this is the default) */
`

const SLIDES = [
  {
    title: "Doctors Leagues",
    subtitle: "Where Healthcare Heroes Compete For Your Trust",
    icon: "🏆",
    color: "from-primary/20 to-primary/40",
  },
  {
    title: "Select Your Country",
    subtitle: "Begin by selecting your country from the League's drop-down menu",
    icon: "🌍",
    color: "from-blue-500/20 to-blue-500/40",
  },
  {
    title: "Browse Divisions (Specialties)",
    subtitle: "Explore different medical specialties and find the expertise you need",
    icon: "⚕️",
    color: "from-green-500/20 to-green-500/40",
  },
  {
    title: "View Rankings & Compare Doctors",
    subtitle: "See how doctors compare based on patient feedback and success rates",
    icon: "🏆",
    color: "from-yellow-500/20 to-yellow-500/40",
  },
  {
    title: "Discover Doctor Profiles",
    subtitle:
      "Read detailed profiles, including experience, reviews, and scheduling options. Book a Match with your chosen doctor",
    icon: "👨‍⚕️",
    color: "from-purple-500/20 to-purple-500/40",
  },
]

const SectionDivider = ({ type = "default" }: { type?: "dna" | "heartbeat" | "stethoscope" | "default" }) => (
  <div className="relative py-16">
    <div className="absolute inset-0 flex items-center">
      <div className="w-full h-px bg-gradient-to-r from-transparent via-primary/30 to-transparent"></div>
    </div>
    <div className="relative flex justify-center">
      <motion.div
                        className="bg-gradient-to-b from-background to-background/90 px-6 py-4 rounded-full border border-primary/30 shadow-[0_0_15px_rgba(0,200,255,0.15)]"
        whileHover={{
          scale: 1.05,
          boxShadow: "0 0 20px rgba(0,200,255,0.3)",
          borderColor: "rgba(0,200,255,0.5)"
        }}
        transition={{ duration: 0.3 }}
      >
        {type === "dna" && (
          <motion.div
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
          >
            <Dna className="h-8 w-8 text-primary drop-shadow-[0_0_5px_rgba(0,200,255,0.5)]" />
          </motion.div>
        )}
        {type === "heartbeat" && (
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 1, repeat: Infinity, repeatType: "reverse" }}
          >
            <Activity className="h-8 w-8 text-primary drop-shadow-[0_0_5px_rgba(0,200,255,0.5)]" />
          </motion.div>
        )}
        {type === "stethoscope" && (
          <motion.div
            animate={{ y: [0, -3, 0] }}
            transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
          >
            <Stethoscope className="h-8 w-8 text-primary drop-shadow-[0_0_5px_rgba(0,200,255,0.5)]" />
          </motion.div>
        )}
        {type === "default" && (
          <motion.div
            animate={{ rotate: [0, 15, 0, -15, 0] }}
            transition={{ duration: 5, repeat: Infinity, repeatType: "reverse" }}
          >
            <Star className="h-8 w-8 text-primary drop-shadow-[0_0_5px_rgba(0,200,255,0.5)]" />
          </motion.div>
        )}
      </motion.div>
    </div>
  </div>
)

export default function HomePage() {
  // Update state types
  const [featuredDoctors, setFeaturedDoctors] = useState<Doctor[]>([])
  const [isLoading, setIsLoading] = useState(true) // Combined loading state for doctors
  const [topDoctors, setTopDoctors] = useState<Doctor[]>([])
  // Separate states for different ad placements
  const [bannerAds, setBannerAds] = useState<Ad[]>([]);
  const [sideLeftAds, setSideLeftAds] = useState<Ad[]>([]);
  const [sideRightAds, setSideRightAds] = useState<Ad[]>([]);
  const [bottomAds, setBottomAds] = useState<Ad[]>([]);
  const [inContentAds, setInContentAds] = useState<Ad[]>([]);
  const [currentSlide, setCurrentSlide] = useState(0)
  const [modalType, setModalType] = useState<"patient" | "doctor" | null>(null)

  // Light theme color classes
  const lightThemeStyles = {
    background: 'light:bg-[hsl(120,20%,97%)]',
    primaryText: 'light:text-[hsl(140,50%,20%)]',
    secondaryText: 'light:text-[hsl(140,30%,35%)]',
    cardBg: 'light:bg-[hsl(120,15%,99%)]'
  }

  const openPatientModal = () => setModalType("patient")
  const openDoctorModal = () => setModalType("doctor")
  const closeModal = () => setModalType(null)

  const nextSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev + 1) % SLIDES.length)
  }, [])

  const prevSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev - 1 + SLIDES.length) % SLIDES.length)
  }, [])

  useEffect(() => {
    const timer = setInterval(nextSlide, 5000)
    return () => clearInterval(timer)
  }, [nextSlide])

  // Optimized: Single database call for both top and featured doctors
  useEffect(() => {
    console.log("HOMEPAGE USEEFFECT: Starting to run...");
    async function fetchHomepageDoctors() {
      console.log("HOMEPAGE USEEFFECT: Inside fetchHomepageDoctors function");
      setIsLoading(true);
      try {
        const supabase = createBrowserClient();
        console.log(`[${new Date().toISOString()}] HOME PAGE: Fetching doctors using existing functions`);

        // Use existing working functions from hybrid-data-service
        const { getFeaturedDoctors, getAllDoctors } = await import("@/lib/hybrid-data-service");

        try {
          // Get all doctors to apply consistent logic for both sections
          const allDoctorsData = await getAllDoctors();

          console.log(`[${new Date().toISOString()}] HOME PAGE DEBUG: Retrieved ${allDoctorsData.length} total doctors`);

          // Debug: Log first few doctors to see their structure
          if (allDoctorsData.length > 0) {
            console.log(`[${new Date().toISOString()}] HOME PAGE DEBUG: Sample doctor data:`, {
              doctor_id: allDoctorsData[0].doctor_id,
              fullname: allDoctorsData[0].fullname,
              rating: allDoctorsData[0].rating,
              community_rating: allDoctorsData[0].community_rating,
              review_count: allDoctorsData[0].review_count,
              available_fields: Object.keys(allDoctorsData[0])
            });
          }

          // Separate doctors with community_ratings from those with 0 or null community_ratings
          const ratedDoctors = allDoctorsData.filter(doctor => doctor.community_rating && doctor.community_rating > 0);
          const unratedDoctors = allDoctorsData.filter(doctor => !doctor.community_rating || doctor.community_rating === 0);

          console.log(`[${new Date().toISOString()}] HOME PAGE DEBUG: Filtered doctors - ${ratedDoctors.length} rated, ${unratedDoctors.length} unrated`);

          // Debug: Log some rated doctors if any exist
          if (ratedDoctors.length > 0) {
            console.log(`[${new Date().toISOString()}] HOME PAGE DEBUG: Top rated doctors:`,
              ratedDoctors.slice(0, 3).map(d => ({ name: d.fullname, rating: d.community_rating }))
            );
          }

          // Sort rated doctors by community_rating descending
          const sortedRatedDoctors = ratedDoctors.sort((a, b) => {
            const ratingB = b.community_rating || 0;
            const ratingA = a.community_rating || 0;
            return ratingB - ratingA;
          });

          // Shuffle unrated doctors for randomization
          const shuffledUnratedDoctors = [...unratedDoctors].sort(() => Math.random() - 0.5);

          // Combine: rated doctors first, then random unrated doctors
          const combinedDoctors = [...sortedRatedDoctors, ...shuffledUnratedDoctors];

          // Set both sections from the same combined data
          setFeaturedDoctors(combinedDoctors.slice(0, 6));  // First 6 for featured
          setTopDoctors(combinedDoctors.slice(0, 10));      // First 10 for standings

          console.log(`[${new Date().toISOString()}] HOME PAGE: Retrieved ${allDoctorsData.length} total doctors (${ratedDoctors.length} rated, ${unratedDoctors.length} unrated)`);

        } catch (fetchError) {
          console.error(`[${new Date().toISOString()}] HOME PAGE: Error fetching doctors:`, fetchError);
          setTopDoctors([]);
          setFeaturedDoctors([]);
        }

      } catch (error) {
        console.error(`[${new Date().toISOString()}] HOME PAGE: Exception fetching doctors:`, error);
        setTopDoctors([]);
        setFeaturedDoctors([]);
      } finally {
        setIsLoading(false);
      }
    }

    fetchHomepageDoctors();
  }, []);

  // Fetch ads for specific placements
  useEffect(() => {
    const fetchAds = async () => {
      try {
        // Use supabase browser client directly instead of server actions
        console.log("[HomePage] Fetching ads directly from Supabase...");
        const supabase = createBrowserClient();

        const fetchPlacementAds = async (placements: string[]) => {
          const { data, error } = await supabase
            .from('ads')
            .select('*')
            .eq('status', 'active')
            .or(placements.map(p => `placements.cs.{${p}}`).join(','))
            .order('created_at', { ascending: false });
          
          if (error) {
            console.error(`Error fetching ads for placements ${placements.join(', ')}:`, error);
            return null;
          }
          
          return data;
        };

        // Get ads for each placement type
        const [bannerData, sideLeftData, sideRightData, bottomData, inContentData] = await Promise.all([
          fetchPlacementAds(['home:banner', 'homepage:banner', 'home-page:banner']),
          fetchPlacementAds(['home:side-left', 'homepage:side-left', 'home-page:side-left']),
          fetchPlacementAds(['home:side-right', 'homepage:side-right', 'home-page:side-right']),
          fetchPlacementAds(['home:bottom', 'homepage:bottom', 'home-page:bottom']),
          fetchPlacementAds(['home:in-content', 'homepage:in-content', 'home-page:in-content']),
        ]);

        if (bannerData) setBannerAds(bannerData);
        if (sideLeftData) setSideLeftAds(sideLeftData);
        if (sideRightData) setSideRightAds(sideRightData);
        if (bottomData) setBottomAds(bottomData);
        if (inContentData) setInContentAds(inContentData);

        console.log("[HomePage] Ads fetched directly from Supabase.");
      } catch (error) {
        console.error("[HomePage] Error fetching ads:", error);
      }
    };
    fetchAds();
  }, []);


  return (
    <>
      {/* Performance Monitor - only in development */}
      <PerformanceMonitor pageName="Homepage" />

      <style dangerouslySetInnerHTML={{ __html: lightThemeGreenStyles }} />
      <div className="relative min-h-screen bg-gradient-to-b from-background via-background to-primary/20 dark-theme-gradient homepage-background">
        {/* Side Left Ad (Fixed Position) */}
        <PositionedAdDisplay
          ads={sideLeftAds}
          position={{ left: '20px', top: '200px' }}
          variant="floating"
          showMultiple={true}
          maxWidth={160}
          showTestAd={false}
        />
        {/* Side Right Ad (Fixed Position) */}
        <PositionedAdDisplay
          ads={sideRightAds}
          position={{ right: '20px', top: '200px' }}
          variant="floating"
          showMultiple={true}
          maxWidth={160}
          showTestAd={false}
       />

      {/* Enhanced animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute -top-20 -right-20 w-64 h-64 rounded-full bg-gradient-to-br from-primary/20 to-primary/5"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute top-1/3 -left-20 w-80 h-80 rounded-full bg-gradient-to-tr from-primary/20 to-blue-500/10"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{
            duration: 10,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
            delay: 1,
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-40 h-40 rounded-full bg-gradient-to-bl from-primary/15 to-green-500/10"
          animate={{
            scale: [1, 1.4, 1],
            opacity: [0.1, 0.3, 0.1],
          }}
          transition={{
            duration: 12,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
            delay: 2,
          }}
        />
        {/* Medical-themed animated elements */}
        <motion.div
          className="absolute top-1/4 right-1/3 w-32 h-32"
          animate={{
            y: [0, -10, 0],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{
            duration: 5,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        >
          <svg viewBox="0 0 24 24" className="w-full h-full text-primary/20">
            <path d="M9 12h6m-3-3v6M12 2a10 10 0 100 20 10 10 0 000-20z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
          </svg>
        </motion.div>
        <motion.div
          className="absolute bottom-1/3 left-1/3 w-24 h-24"
          animate={{
            y: [0, 10, 0],
            opacity: [0.1, 0.3, 0.1],
          }}
          transition={{
            duration: 6,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
            delay: 1,
          }}
        >
          <svg viewBox="0 0 24 24" className="w-full h-full text-primary/20">
            <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0016.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 002 8.5c0 2.3 1.5 4.05 3 5.5l7 7 7-7z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
          </svg>
        </motion.div>
      </div>

      {/* Enhanced Hero Section */}
      <div className="container mx-auto px-4 py-8 md:py-12 lg:py-16 relative z-10 dark:bg-gradient-to-r dark:from-background/90 dark:via-background/95 dark:to-background/90">
        <Card className="relative h-[50vh] md:h-[60vh] lg:h-[70vh] overflow-hidden border-primary/30 bg-transparent shadow-[0_0_30px_rgba(0,200,255,0.2)]">
          <div
            className="absolute inset-0 bg-cover bg-center"
            style={{
              backgroundImage: 'url("/stadium-bg.webp")',
              backgroundPosition: 'center',
              backgroundSize: 'cover',
              backgroundRepeat: 'no-repeat'
            }}
          />
          <div className="absolute inset-0 image-overlay">
            {/* Medical soccer field lines backdrop */}
            <div className="absolute inset-0 opacity-30">
              <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-white transform -translate-y-1/2"></div>
              <div className="absolute top-0 bottom-0 left-1/2 w-0.5 bg-white transform -translate-x-1/2"></div>
              <div className="absolute top-1/2 left-1/2 w-24 h-24 border-2 border-white rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
              <div className="absolute top-0 left-0 w-1/5 h-2/5 border-r-2 border-b-2 border-white"></div>
              <div className="absolute top-0 right-0 w-1/5 h-2/5 border-l-2 border-b-2 border-white"></div>
              <div className="absolute bottom-0 left-0 w-1/5 h-2/5 border-r-2 border-t-2 border-white"></div>
              <div className="absolute bottom-0 right-0 w-1/5 h-2/5 border-l-2 border-t-2 border-white"></div>

              {/* Medical cross markers */}
              <div className="absolute top-[20%] left-[20%] text-foreground text-2xl opacity-30">+</div>
              <div className="absolute top-[30%] right-[25%] text-foreground text-2xl opacity-30">+</div>
              <div className="absolute bottom-[25%] left-[30%] text-foreground text-2xl opacity-30">+</div>
              <div className="absolute bottom-[15%] right-[15%] text-foreground text-2xl opacity-30">+</div>
            </div>

            <AnimatePresence mode="wait">
              <motion.div
                key={currentSlide}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5 }}
                className="flex h-full flex-col items-center justify-center px-4 sm:px-6 md:px-8 text-center"
              >
                <motion.div
                  className="mb-6 text-7xl"
                  initial={{ scale: 0.8 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.5, type: "spring" }}
                >
                  {SLIDES[currentSlide].icon}
                </motion.div>
                <motion.h1
                  className="mb-4 text-5xl font-bold tracking-tight hero-title hero-title-custom"
                  style={{
                    color: '#ffffff',
                    textShadow: '4px 4px 15px rgba(0, 0, 0, 0.98), 0 0 12px rgba(0, 0, 0, 0.95), 3px 3px 8px rgba(0, 0, 0, 1), 2px 2px 4px rgba(0, 0, 0, 1)',
                    fontWeight: 900,
                    WebkitTextStroke: '1.2px rgba(0, 0, 0, 0.7)',
                    letterSpacing: '0.08em'
                  }}
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  {SLIDES[currentSlide].title}
                </motion.h1>
                <motion.p
                  className="max-w-2xl text-lg hero-text hero-title-custom px-6 py-3 rounded-full"
                  style={{
                    color: '#ffffff',
                    textShadow: '3px 3px 12px rgba(0, 0, 0, 0.95), 0 0 8px rgba(0, 0, 0, 0.9), 2px 2px 6px rgba(0, 0, 0, 1), 1px 1px 3px rgba(0, 0, 0, 1)',
                    fontWeight: 600,
                    WebkitTextStroke: '1px rgba(0, 0, 0, 0.6)',
                    letterSpacing: '0.04em'
                  }}
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  {SLIDES[currentSlide].subtitle}
                </motion.p>

                <motion.div
                  className="mt-8 flex gap-4"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  <Button
                    variant="default"
                    size="lg"
                    className="hero-button-primary rounded-full px-8 shadow-lg shadow-primary/25 text-base font-medium"
                    onClick={openPatientModal}
                  >
                    Join as Patient
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    className="hero-button-secondary rounded-full px-8 text-base font-medium"
                    onClick={openDoctorModal}
                  >
                    Join as Doctor
                  </Button>
                </motion.div>
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Navigation buttons with stethoscope icon */}
          <Button
            variant="outline"
            className="absolute left-4 top-1/2 -translate-y-1/2 text-foreground hover:bg-accent rounded-full h-12 w-12 border-border backdrop-blur-sm"
            onClick={prevSlide}
          >
            <Stethoscope className="h-6 w-6" />
            <span className="sr-only">Previous slide</span>
          </Button>
          <Button
            variant="outline"
            className="absolute right-4 top-1/2 -translate-y-1/2 text-foreground hover:bg-accent rounded-full h-12 w-12 border-border backdrop-blur-sm"
            onClick={nextSlide}
          >
            <Stethoscope className="h-6 w-6" />
            <span className="sr-only">Next slide</span>
          </Button>

        </Card>

        {/* Slide indicators with ambulance icon - positioned outside Card to prevent overlap */}
        <div className="absolute bottom-2 md:bottom-4 lg:bottom-6 left-1/2 flex -translate-x-1/2 space-x-2 md:space-x-3 z-30">
          {SLIDES.map((_, index) => (
            <button
              key={index}
              className={`flex items-center justify-center h-8 w-8 md:h-10 md:w-10 rounded-full transition-all duration-300 backdrop-blur-sm ${
                index === currentSlide
                  ? "bg-primary text-foreground scale-110 shadow-[0_0_10px_rgba(0,200,255,0.5)]"
                  : "bg-accent text-foreground/60 hover:bg-accent/70"
              }`}
              onClick={() => setCurrentSlide(index)}
            >
              {index === currentSlide ? (
                <Ambulance className="h-4 w-4 md:h-5 md:w-5" />
              ) : (
                <div className="h-1.5 w-1.5 md:h-2 md:w-2 rounded-full bg-current" />
              )}
              <span className="sr-only">Go to slide {index + 1}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Homepage Ad Section */}
      <div className="container mx-auto px-4 py-6 space-y-8">
         <div className="flex justify-center">
           <PositionedAdDisplay
             ads={bannerAds}
             position={{}}
             variant="banner"
             showMultiple={false}
             maxWidth={728}
             showTestAd={false}
           />
         </div>
         <PositionedAdDisplay
              ads={bottomAds}
              position={{ bottom: '20px', left: '50%', transform: 'translateX(-50%)' }}
              variant="floating"
              showMultiple={false}
              maxWidth={728}
              showTestAd={false}
            />
      </div>

      {/* Original ECG Divider */}
      <ECGDivider />
      
      <div className="container mx-auto px-4 py-6">
        {/* Enhanced Scoreboard section */}
        <motion.div
          className="bg-gradient-to-r from-background/95 to-background/95 border border-primary/20 rounded-xl overflow-hidden shadow-[0_0_15px_rgba(0,0,0,0.5)]"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <div className="bg-gradient-to-r from-primary/20 to-transparent p-4 md:p-6 flex flex-col md:flex-row justify-between items-start md:items-center gap-3 md:gap-4">
            <div className="flex items-center">
              <div className="relative mr-3">
                <motion.div 
                  className="absolute -inset-1 rounded-full bg-yellow-500/20"
                  animate={{ 
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 0.8, 0.5]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                />
                <Trophy className="h-8 w-8 text-yellow-500 relative z-10" />
              </div>
              <div>
                <h3 className="text-2xl font-bold tracking-wide standings-header">LEAGUE STANDINGS</h3>
                <p className="text-foreground/60 text-sm">Top performing doctors by rating</p>
              </div>
            </div>
            <div className="bg-gradient-to-r from-primary/10 to-primary/5 backdrop-blur-sm text-foreground/80 text-sm font-medium px-4 py-2 rounded-full border border-primary/30 shadow-inner shadow-primary/5 flex items-center gap-2">
              <span className="inline-block w-2 h-2 rounded-full bg-green-500 animate-pulse"></span>
              LIVE SEASON 2025
            </div>
          </div>
          
          <div className="overflow-x-auto scrollbar-thin scrollbar-thumb-primary/20 scrollbar-track-black/20">
            <Table className="w-full">
              <TableHeader className="bg-background/70">
                <TableRow className="hover:bg-transparent border-b border-primary/20">
                  <TableHead className="text-foreground/70 text-sm font-medium w-16 text-center">#</TableHead>
                  <TableHead className="text-foreground/70 text-sm font-medium">DOCTOR</TableHead>
                  <TableHead className="text-foreground/70 text-sm font-medium hidden md:table-cell">SPECIALTY</TableHead>
                  <TableHead className="text-foreground/70 text-sm font-medium hidden md:table-cell text-center">MATCHES</TableHead>
                  <TableHead className="text-foreground/70 text-sm font-medium hidden md:table-cell text-center">W</TableHead>
                  <TableHead className="text-foreground/70 text-sm font-medium hidden md:table-cell text-center">L</TableHead>
                  <TableHead className="text-foreground/70 text-sm font-medium text-center">RATING</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading && Array.from({ length: 5 }).map((_, i) => (
                  <TableRow key={`loading-${i}`} className="animate-pulse">
                    <TableCell className="text-center font-bold text-lg">
                      <div className="h-6 bg-gray-700 rounded"></div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-700"></div>
                        <div className="h-6 bg-gray-700 rounded w-3/4"></div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="h-6 bg-gray-700 rounded w-1/2"></div>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="h-6 bg-gray-700 rounded w-1/4 mx-auto"></div>
                    </TableCell>
                    <TableCell className="text-center text-green-400">
                      <div className="h-6 bg-gray-700 rounded w-1/4 mx-auto"></div>
                    </TableCell>
                    <TableCell className="text-center text-red-400">
                      <div className="h-6 bg-gray-700 rounded w-1/4 mx-auto"></div>
                    </TableCell>
                    <TableCell className="text-center font-bold">
                      <div className="h-6 bg-gray-700 rounded w-1/4 mx-auto"></div>
                    </TableCell>
                  </TableRow>
                ))}
                {!isLoading && topDoctors.map((doctor, index) => (
                  <TableRow
                    key={doctor.doctor_id}
                    className={`
                      group transition-all duration-200
                      hover:bg-primary/10 border-b border-primary/10
                      ${index < 3 ? 'bg-gradient-to-r from-background/60 to-primary/5' : ''}
                    `}
                  >
                    <TableCell className="font-bold text-center w-16">
                      <div className="flex justify-center">
                        {index === 0 && (
                          <div className="relative">
                            <motion.div
                              className="absolute -inset-1 rounded-full bg-yellow-500/20 group-hover:bg-yellow-500/40"
                              animate={{ scale: [1, 1.1, 1] }}
                              transition={{ duration: 2, repeat: Infinity }}
                            />
                            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-600 flex items-center justify-center text-black font-bold shadow-lg shadow-yellow-500/20 relative z-10">1</div>
                          </div>
                        )}
                        {index === 1 && (
                          <div className="relative">
                            <motion.div
                              className="absolute -inset-1 rounded-full bg-green-400/10 group-hover:bg-green-400/20"
                              animate={{ scale: [1, 1.1, 1] }}
                              transition={{ duration: 2, repeat: Infinity, delay: 0.3 }}
                            />
                            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-gray-300 to-gray-500 flex items-center justify-center text-black font-bold shadow-lg shadow-gray-500/20 relative z-10">2</div>
                          </div>
                        )}
                        {index === 2 && (
                          <div className="relative">
                            <motion.div
                              className="absolute -inset-1 rounded-full bg-amber-700/10 group-hover:bg-amber-700/20"
                              animate={{ scale: [1, 1.1, 1] }}
                              transition={{ duration: 2, repeat: Infinity, delay: 0.6 }}
                            />
                            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-amber-600 to-amber-800 flex items-center justify-center text-foreground font-bold shadow-lg shadow-amber-700/20 relative z-10">3</div>
                          </div>
                        )}
                        {index > 2 && <div className="text-foreground/80 group-hover:text-foreground transition-colors">{index + 1}</div>}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="relative">
                          <div className="absolute -inset-1 rounded-full bg-primary/0 group-hover:bg-primary/10 transition-all duration-300"></div>
                          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-primary/20 to-primary/40 flex items-center justify-center text-foreground overflow-hidden border border-primary/30 group-hover:border-primary/50 transition-all duration-300 relative z-10">
                            {/* Use getSupabaseProfileImageUrl for profile images */}
                            {doctor.profile_image && !isLocalFilePath(doctor.profile_image) ? (
                              <Image src={getSupabaseProfileImageUrl(doctor.profile_image)} alt={doctor.fullname} width={40} height={40} className="w-full h-full object-cover" />
                            ) : (
                              <Image src="/placeholder.svg" alt={doctor.fullname} width={40} height={40} className="w-full h-full object-cover" />
                            )}
                          </div>
                        </div>
                        <div>
                          <Link href={`/doctors/${doctor.doctor_id}`} className="group-hover:text-primary transition-colors">
                            <div className="font-medium text-foreground group-hover:text-primary transition-colors">{doctor.fullname}</div>
                          </Link>
                          <div className="text-xs text-foreground/60 md:hidden">{doctor.specialty}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden md:table-cell text-foreground/80 group-hover:text-foreground/90 transition-colors">
                      <div className="flex items-center gap-2">
                        <Stethoscope className="w-4 h-4 text-primary/60 group-hover:text-primary/80 transition-colors" />
                        <span>{doctor.specialty}</span>
                      </div>
                    </TableCell>
                    <TableCell className="hidden md:table-cell text-center">
                      <div className="inline-flex items-center justify-center px-2.5 py-0.5 bg-primary/5 group-hover:bg-primary/10 transition-colors rounded-md text-foreground/90 text-sm">
                        {((doctor.wins || 0) + (doctor.losses || 0))}
                      </div>
                    </TableCell>
                    <TableCell className="hidden md:table-cell text-center">
                      <div className="inline-flex items-center justify-center px-2.5 py-0.5 bg-green-500/10 group-hover:bg-green-500/20 transition-colors rounded-md text-green-400 text-sm font-medium">
                        {doctor.wins || 0}
                      </div>
                    </TableCell>
                    <TableCell className="hidden md:table-cell text-center">
                      <div className="inline-flex items-center justify-center px-2.5 py-0.5 bg-red-500/10 group-hover:bg-red-500/20 transition-colors rounded-md text-red-400 text-sm font-medium">
                        {doctor.losses || 0}
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center gap-1">
                        <div className="relative">
                          <motion.div 
                            className="absolute -inset-3 rounded-full bg-yellow-500/0 group-hover:bg-yellow-500/10"
                            animate={{ scale: [1, 1.2, 1] }}
                            transition={{ duration: 2, repeat: Infinity }}
                          />
                          <Star className="h-5 w-5 text-yellow-500 fill-yellow-500 drop-shadow-[0_0_3px_rgba(234,179,8,0.3)]" />
                        </div>
                        <span className="text-foreground font-medium text-lg group-hover:text-yellow-300 transition-colors">
                          {doctor.community_rating?.toFixed(1) || "0.0"}
                        </span>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          
          <div className="p-6 text-center bg-gradient-to-t from-primary/5 to-transparent">
            <Button 
              variant="default" 
              className="bg-gradient-to-r from-primary/30 to-primary/20 hover:from-primary/40 hover:to-primary/30 text-foreground border-4 border-primary/30 rounded-full px-8 text-base font-medium shadow-lg shadow-primary/10 transition-all duration-300 group"
              asChild
            >
              <Link href="/standings" className="flex items-center gap-2">
                <span>View Full Standings</span>
                <motion.div
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity, repeatType: "reverse" }}
                >
                  <Trophy className="h-4 w-4 text-yellow-500" />
                </motion.div>
              </Link>
            </Button>
          </div>
        </motion.div>

        {/* In-Content Ad Slot */}
        {inContentAds.length > 0 && (
          <div className="my-8 flex justify-center">
            <PositionedAdDisplay
              ads={inContentAds}
              position={{}}
              variant="banner"
              showMultiple={true}
              maxWidth={728}
              showTestAd={false}
            />
          </div>
        )}

      </div>
      
      {/* Original ECG Divider */}
      <ECGDivider />
      
      <div className="container mx-auto px-4 py-8">
        {/* Enhanced Featured Doctors section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="flex items-center justify-center gap-4 mb-12"
        >
          <div className="h-px bg-gradient-to-r from-transparent to-primary/50 w-16"></div>
          <h2 className="text-3xl font-bold text-center hero-text flex items-center">
            <Medal className="w-8 h-8 text-yellow-500 mr-2" />
            Featured All-Stars
          </h2>
          <div className="h-px bg-gradient-to-r from-primary/50 to-transparent w-16"></div>
        </motion.div>

        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {featuredDoctors.map((doctor, index) => (
              <motion.div
                key={doctor.doctor_id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5, transition: { duration: 0.2 } }}
              >
                <FeaturedDoctorCard doctor={doctor} index={index} />
              </motion.div>
            ))}
          </div>
        )}
      </div>
      
      {/* Original ECG Divider */}
      <ECGDivider />
      
      {/* Elevate Your Medical Career Section */}
      <div className="container mx-auto px-4 py-16">
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 lg:gap-10 items-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          {/* Content Column */}
          <div className="space-y-6">
            <div>
              <p className="text-primary/80 mb-2 font-medium">Join The Doctor's League Today</p>
              <motion.h2 
                className="text-3xl md:text-4xl lg:text-5xl font-bold hero-text"
                initial={{ opacity: 0, y: -10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
              >
                Elevate Your Medical Career
              </motion.h2>
            </div>
            <p className="text-foreground/80 text-lg">
              For thousands of medical professionals who are already part of our network. Get recognized, build your reputation, and connect with patients looking for quality care.
            </p>
            <div className="space-y-4">
              <motion.div 
                className="flex items-start gap-3"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <div className="p-1.5 rounded-full bg-primary/10 border border-primary/30 text-primary mt-0.5">
                  <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <p className="text-foreground/90">Get ranked in our Doctor League system</p>
              </motion.div>
              <motion.div 
                className="flex items-start gap-3"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <div className="p-1.5 rounded-full bg-primary/10 border border-primary/30 text-primary mt-0.5">
                  <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <p className="text-foreground/90">Connect with thousands of potential patients</p>
              </motion.div>
              <motion.div 
                className="flex items-start gap-3"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.4 }}
                viewport={{ once: true }}
              >
                <div className="p-1.5 rounded-full bg-primary/10 border border-primary/30 text-primary mt-0.5">
                  <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <p className="text-foreground/90">Access powerful analytics and insights</p>
              </motion.div>
              <motion.div 
                className="flex items-start gap-3"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.5 }}
                viewport={{ once: true }}
              >
                <div className="p-1.5 rounded-full bg-primary/10 border border-primary/30 text-primary mt-0.5">
                  <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <p className="text-foreground/90">Build your reputation with verified reviews</p>
              </motion.div>
            </div>
            <motion.div 
              className="pt-4"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
              transition={{ duration: 0.5, delay: 0.6 }}
              viewport={{ once: true }}
            >
              <Button 
                variant="default"
                className="bg-primary hover:bg-primary/90 text-foreground rounded-full px-8 py-6 shadow-lg shadow-primary/25 text-base font-medium"
                asChild
              >
                <Link href="/doctor/register">
                  Join Us
                </Link>
              </Button>
            </motion.div>
          </div>
          
          {/* Image/Phone Column */}
          <div className="relative">
            <div className="relative z-10 mx-auto max-w-[300px]">
              <motion.div
                initial={{ y: 10 }}
                animate={{ y: -10 }}
                transition={{ 
                  duration: 2.5, 
                  repeat: Infinity, 
                  repeatType: "reverse",
                  ease: "easeInOut" 
                }}
              >
                <div className="rounded-[40px] border-8 border-black/80 overflow-hidden shadow-2xl shadow-primary/30">
                  <Image 
                    src="/screenshots/app-mockup.svg" 
                    alt="Doctor's League Mobile App" 
                    width={300} 
                    height={600}
                    className="h-auto w-full object-cover"
                  />
                </div>
              </motion.div>
            </div>
            {/* Decorative elements */}
            <div className="absolute top-1/4 -left-10 w-20 h-20 rounded-full bg-gradient-to-r from-primary/30 to-blue-500/20 blur-xl"></div>
            <div className="absolute bottom-1/3 -right-10 w-24 h-24 rounded-full bg-gradient-to-r from-primary/20 to-green-500/10 blur-xl"></div>
          </div>
        </motion.div>
      </div>
      
      {/* Original ECG Divider */}
      <ECGDivider />
      
      {/* Enhanced Call to action section */}
      <div className="container mx-auto px-4 py-16">
        <motion.div
          className="relative overflow-hidden rounded-2xl border border-primary/30 shadow-[0_0_20px_rgba(0,200,255,0.2)]"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          {/* Background pattern */}
                      <div className="absolute inset-0 hero-card">
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-white transform -translate-y-1/2"></div>
              <div className="absolute top-0 bottom-0 left-1/2 w-0.5 bg-white transform -translate-x-1/2"></div>
              <div className="absolute top-1/2 left-1/2 w-40 h-40 border-2 border-white rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
            </div>
          </div>
          
          <div className="relative p-6 md:p-8 lg:p-12 text-center">
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              whileInView={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="mb-6"
            >
              <div className="inline-flex items-center justify-center p-3 bg-primary/20 rounded-full backdrop-blur-sm">
                <Whistle className="h-12 w-12 text-foreground" />
              </div>
            </motion.div>
            <motion.h2
              className="text-2xl md:text-3xl lg:text-4xl font-bold hero-text mb-4"
              initial={{ y: 20, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
            >
              Join the Doctor's League Today
            </motion.h2>
            <motion.p
              className="text-foreground/90 text-lg max-w-2xl mx-auto mb-8"
              initial={{ y: 20, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              viewport={{ once: true }}
            >
              Your reviews and ratings help determine the champions. Join now to make your voice heard and help others
              find the best healthcare professionals.
            </motion.p>
            
            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center"
              initial={{ y: 20, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              viewport={{ once: true }}
            >
              <Button
                size="lg"
                className="bg-primary hover:bg-primary/90 text-foreground rounded-full px-8 shadow-lg shadow-primary/25 text-base font-medium"
                onClick={openPatientModal}
              >
                Join as Patient
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-primary text-primary hover:bg-primary/10 rounded-full px-8 text-base font-medium"
                onClick={openDoctorModal}
              >
                Join as Doctor
              </Button>
            </motion.div>
          </div>
        </motion.div>
      </div>
      <PremiumMedicalModal
        isOpen={modalType !== null}
        onClose={closeModal}
        type={modalType || "patient"}
      />
      </div>
    </>
  )
}
