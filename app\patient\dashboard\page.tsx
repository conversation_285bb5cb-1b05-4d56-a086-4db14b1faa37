"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/context/AuthContext"
import { createClient } from "@supabase/supabase-js"
import {
  Star,
  Trophy,
  User,
  Calendar,
  Search,
  MessageCircle,
  Activity,
  Award,
  Bell,
  Stethoscope,
  ClipboardList,
  LogOut,
  BookOpen,
  Home,
  Edit
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { toast } from "sonner"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { format } from "date-fns"
import { getSupabaseProfileImageUrl } from "@/app/lib/utils"

export default function PatientDashboard() {
  const { isAuthenticated, user: authUser, isLoading: authIsLoading, logout } = useAuth()
  const [dataLoading, setDataLoading] = useState(true)
  const [topDoctors, setTopDoctors] = useState<any[]>([])
  const [userReviews, setUserReviews] = useState<any[]>([])
  const [randomTip, setRandomTip] = useState("")
  const [specialtyStats, setSpecialtyStats] = useState<{name: string, count: number, percentage: number}[]>([])
  const [ratingDistribution, setRatingDistribution] = useState<number[]>([0, 0, 0, 0, 0])
  const [doctorCount, setDoctorCount] = useState<number>(0)
  const [highestRatedSpecialty, setHighestRatedSpecialty] = useState<string>('Cardiology')
  const [mostReviewedSpecialty, setMostReviewedSpecialty] = useState<string>('Family Medicine')
  const [averageRating, setAverageRating] = useState<string>('4.2')
  const [statistics, setStatistics] = useState<any[]>([])
  const [patientName, setPatientName] = useState<string>("")
  const router = useRouter()

  const createServiceRoleClient = () => {
    const supabaseUrl = "https://uapbzzscckhtptliynyj.supabase.co"
    const supabaseServiceKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q"
    return createClient(supabaseUrl, supabaseServiceKey)
  }
  
  const healthTips = [
    "Regular physical activity can help prevent chronic diseases and improve mental health.",
    "Aim for 7-9 hours of quality sleep per night for optimal health and recovery.",
    "Stay hydrated by drinking at least 8 glasses of water daily.",
    "Include a variety of fruits and vegetables in your daily diet for essential vitamins and minerals.",
    "Regular check-ups with your healthcare provider can help detect health issues early.",
    "Practice mindfulness or meditation to reduce stress and improve mental well-being.",
    "Limit processed foods and choose whole, nutrient-dense options instead.",
    "Maintain good posture throughout the day to prevent back and neck pain.",
    "Wash your hands frequently to prevent the spread of germs and infections.",
    "Take short breaks during work to rest your eyes and stretch your muscles.",
    "Eat breakfast daily to jumpstart your metabolism and provide energy for the day.",
    "Limit screen time before bed to improve sleep quality.",
    "Consider using a standing desk or taking walking breaks if you have a sedentary job.",
    "Stay socially connected with friends and family for better mental health.",
    "Practice deep breathing exercises to manage stress and anxiety.",
    "Get regular dental check-ups and maintain good oral hygiene.",
    "Protect your skin from sun damage by using sunscreen daily.",
    "Keep up with recommended vaccinations for your age and health status.",
    "Practice good hand hygiene, especially before eating and after using the bathroom.",
    "Monitor your blood pressure regularly, especially if you have a family history of hypertension."
  ];

  useEffect(() => {
    console.log("Patient Dashboard component mounted");
    
    if (authIsLoading) {
      console.log("PatientDashboard: Waiting for auth state to load...");
      return;
        }
        
    if (!isAuthenticated || !authUser) {
      console.log("No active session, redirecting to home");
      router.push("/");
      return;
        }
        
    if (authUser.userType !== 'patient') {
      console.log("Not a patient account, redirecting to home");
      router.push("/");
      return;
    }
    
    console.log("PatientDashboard: Authenticated as patient:", authUser);
    
    const fetchPatientData = async () => {
      setDataLoading(true);
      try {
        const serviceClient = createServiceRoleClient();
        
        localStorage.setItem('lastLoginDate', new Date().toDateString());

        // Get patient's name from users table if not already available in authUser
        if (!authUser.first_name || !authUser.last_name) {
          const { data: userData, error: userError } = await serviceClient
            .from('users')
            .select('first_name, last_name')
            .eq('user_id', authUser.userId)
            .single();
          
          if (userError) {
            console.error("Error fetching user data:", userError);
          } else if (userData) {
            setPatientName(`${userData.first_name || ''} ${userData.last_name || ''}`.trim());
          }
        } else {
          setPatientName(`${authUser.first_name} ${authUser.last_name}`.trim());
        }
        
        const { data: reviewsData, error: reviewsError } = await serviceClient
          .from('reviews')
          .select(`
            review_id,
            rating,
            review_date,
            additional_comments,
            doctors!inner(
              doctor_id,
              fullname,
              specialty
            )
          `)
        .eq('user_id', authUser.userId)
        .order('review_date', { ascending: false });
        
        if (reviewsError) {
          console.error("Error fetching user reviews:", reviewsError);
        } else {
          setUserReviews(reviewsData || []);
          console.log("User reviews:", reviewsData);
        }
        
        try {
          const { data: doctorsData, error: doctorsError } = await serviceClient
            .from('doctors')
            .select('doctor_id, fullname, specialty, community_rating, review_count, specialty_id')
            .not('community_rating', 'is', null)
            .gt('community_rating', 0)
            .order('community_rating', { ascending: false })
            .limit(5);
          
          if (doctorsError) {
            console.error("Error fetching top doctors:", doctorsError.message);
          } else {
            if (!doctorsData || doctorsData.length === 0) {
              console.log("No doctors with community_ratings found, fetching doctors regardless of community_rating");
              const { data: allDoctorsData, error: allDoctorsError } = await serviceClient
                .from('doctors')
                .select('doctor_id, fullname, specialty, community_rating, review_count')
                .order('community_rating', { ascending: false })
                .limit(5);
                
              if (allDoctorsError) {
                console.error("Error fetching all doctors:", allDoctorsError.message);
              } else {
                setTopDoctors(allDoctorsData || []);
              }
            } else {
              setTopDoctors(doctorsData || []);
            }
          }
          
          try {
            const { data: specialtiesList, error: specialtiesError } = await serviceClient
              .from('specialties')
              .select('specialty_id, specialty_name');

            if (specialtiesError) {
              throw specialtiesError;
            }

            const { data: allDoctors, error: allDoctorsError } = await serviceClient
              .from('doctors')
              .select('fullname, specialty_id, community_rating, review_count');

            if (allDoctorsError) {
              throw allDoctorsError;
            }

            if (specialtiesList && allDoctors) {
              const specialtyCounts: Record<string, number> = {};
              const specialtyIdToName: Record<number, string> = {};
              specialtiesList.forEach(s => {
                specialtyIdToName[s.specialty_id] = s.specialty_name;
              });

              const doctorsWithSpecialtyName = allDoctors.map(doc => ({
                ...doc,
                specialtyName: doc.specialty_id ? specialtyIdToName[doc.specialty_id] : 'Unknown'
              }));

              doctorsWithSpecialtyName.forEach(doctor => {
                if (doctor.specialtyName !== 'Unknown') {
                  specialtyCounts[doctor.specialtyName] = (specialtyCounts[doctor.specialtyName] || 0) + 1;
                }
              });
              
              const total = Object.values(specialtyCounts).reduce((sum, count) => sum + count, 0);
              const specialtyArray = Object.entries(specialtyCounts)
                .map(([name, count]) => ({
                  name,
                  count,
                  percentage: total > 0 ? Math.round((count / total) * 100) : 0
                }))
                .sort((a, b) => b.count - a.count);
              
              setSpecialtyStats(specialtyArray);
              setDoctorCount(total);

              // Calculate other stats
              const specialtyAggregates: Record<string, { totalRating: number, ratingCount: number, reviewCount: number, doctors: any[] }> = {};

              doctorsWithSpecialtyName.forEach(doc => {
                if (doc.specialtyName !== 'Unknown') {
                  if (!specialtyAggregates[doc.specialtyName]) {
                    specialtyAggregates[doc.specialtyName] = { totalRating: 0, ratingCount: 0, reviewCount: 0, doctors: [] };
                  }
                  if (doc.community_rating) {
                    specialtyAggregates[doc.specialtyName].totalRating += doc.community_rating;
                    specialtyAggregates[doc.specialtyName].ratingCount++;
                  }
                  specialtyAggregates[doc.specialtyName].reviewCount += (doc.review_count || 0);
                  specialtyAggregates[doc.specialtyName].doctors.push(doc);
                }
              });

              let highestRatedSpec = 'N/A';
              let maxAvgRating = -1;
              let mostReviewedSpec = 'N/A';
              let maxReviewCount = -1;
              const topSpecialists = [];

              for (const specialtyName in specialtyAggregates) {
                const { totalRating, ratingCount, reviewCount, doctors } = specialtyAggregates[specialtyName];
                
                if (ratingCount > 0) {
                  const avgRating = totalRating / ratingCount;
                  if (avgRating > maxAvgRating) {
                    maxAvgRating = avgRating;
                    highestRatedSpec = specialtyName;
                  }
                }

                if (reviewCount > maxReviewCount) {
                  maxReviewCount = reviewCount;
                  mostReviewedSpec = specialtyName;
                }

                if (doctors.length > 0) {
                  const topDoctor = doctors.reduce((max, d) => (d.community_rating || 0) > (max.community_rating || 0) ? d : max);
                  topSpecialists.push({
                    statistic_name: 'Highest rated doctor in each specialty',
                    additional_info: `${topDoctor.fullname} in ${specialtyName}`
                  });
                }
              }

              setHighestRatedSpecialty(highestRatedSpec);
              setMostReviewedSpecialty(mostReviewedSpec);

              const allRatings = allDoctors.map(d => d.community_rating).filter(r => r !== null && r !== undefined) as number[];
              if (allRatings.length > 0) {
                const totalAverage = allRatings.reduce((sum, r) => sum + r, 0) / allRatings.length;
                setAverageRating(totalAverage.toFixed(1));
              }

              setStatistics(topSpecialists);
            }

            const randomIndex = Math.floor(Math.random() * healthTips.length);
            setRandomTip(healthTips[randomIndex]);
          } catch (err) {
            console.error("Error processing specialty data:", err);
          }
        } catch (err) {
          console.error("Error fetching doctor data:", err);
          toast.error("Could not load dashboard data");
        }
      } catch (err) {
        console.error("Error in fetchPatientData:", err);
        toast.error("Could not load dashboard data");
      } finally {
        setDataLoading(false);
      }
    };
    
    fetchPatientData();
    
    return () => {
      console.log("Patient Dashboard component unmounted");
    };
  }, [authIsLoading, isAuthenticated, authUser, router]);
  
  const handleSignOut = async () => {
    try {
      console.log("Signing out via AuthContext logout");
      logout();
    } catch (error) {
      console.error("Error during sign out:", error);
      toast.error("Sign out failed. Please try again.");
    }
  };

  if (authIsLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-green-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg text-muted-green">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !authUser || authUser.userType !== 'patient') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-green-50">
        <div className="text-center">
          <p className="text-lg text-muted-green">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-background pb-12">
      <div className="bg-gradient-to-r from-primary/20 to-primary/10 border-b border-primary/20">
        <div className="container mx-auto py-8 px-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-foreground">
                Welcome, {patientName || authUser.email}
              </h1>
              <p className="text-foreground/70 mt-1">
                Your health dashboard is ready
              </p>
            </div>
            <button 
              className="flex items-center px-4 py-2 rounded-md border border-primary/30 text-primary hover:bg-primary/10 transition-colors"
              onClick={handleSignOut}
            >
              <LogOut className="h-4 w-4 mr-2" />
              <span>Sign Out</span>
            </button>
          </div>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="space-y-6">
            <Card className="bg-gradient-to-b from-background/90 to-background border-border shadow-xl overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-foreground">Navigation</CardTitle>
                <CardDescription className="text-foreground/70">
                  Access all features
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2 pt-0">
                <nav className="space-y-1">
                  <Link 
                    href={authUser?.country_id ? `/divisions/${authUser.country_id}` : "/divisions/1"} 
                    className="flex items-center space-x-3 p-3 rounded-lg text-foreground hover:bg-primary/10 transition-colors"
                    title={`Find Doctors in ${authUser?.country || 'Your Area'}`}
                  >
                    <Search className="h-5 w-5 text-primary" />
                    <span>Find Doctors{authUser?.country ? ` in ${authUser.country}` : ""}</span>
                  </Link>
                  <Link href="/standings" className="flex items-center space-x-3 p-3 rounded-lg text-foreground hover:bg-primary/10 transition-colors">
                    <Trophy className="h-5 w-5 text-primary" />
                    <span>Doctor Rankings</span>
                  </Link>
                  <Link href="/head-to-head" className="flex items-center space-x-3 p-3 rounded-lg text-foreground hover:bg-primary/10 transition-colors">
                    <Award className="h-5 w-5 text-primary" />
                    <span>Compare Doctors</span>
                  </Link>
                  <div className="flex items-center space-x-3 p-3 rounded-lg text-foreground bg-primary/5 transition-colors">
                    <Calendar className="h-5 w-5 text-primary" />
                    <span>My Appointments</span>
                    <Badge className="ml-auto text-xs bg-primary/20 text-primary hover:bg-primary/30">Coming Soon</Badge>
                  </div>
                  <div className="flex items-center space-x-3 p-3 rounded-lg text-foreground hover:bg-primary/10 transition-colors">
                    <MessageCircle className="h-5 w-5 text-primary" />
                    <span>Messages</span>
                    <Badge className="ml-auto text-xs bg-primary/20 text-primary hover:bg-primary/30">Coming Soon</Badge>
                  </div>
                </nav>
              </CardContent>
            </Card>
            
            <Card className="bg-gradient-to-b from-background/90 to-background border-border shadow-xl overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-foreground">My Profile</CardTitle>
                <CardDescription className="text-foreground/70">
                  Personal information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center overflow-hidden">
                    {authUser?.profile_image ? (
                      <img 
                        src={getSupabaseProfileImageUrl(authUser.profile_image)} 
                        alt={authUser?.first_name || 'User'}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = '/placeholder-avatar.png';
                        }}
                      />
                    ) : (
                      <User className="h-8 w-8 text-primary" />
                    )}
                  </div>
                  <div>
                    <p className="text-foreground font-medium">
                      {authUser?.first_name || ''} {authUser?.last_name || ''}
                    </p>
                    <p className="text-foreground/70 text-sm">{patientName || authUser.email}</p>
                    <Badge className="mt-1 bg-primary/20 text-primary">Patient</Badge>
                  </div>
                </div>
                <div className="pt-2">
                  <Link href="/patient/profile/edit">
                    <Button className="w-full bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 text-foreground font-medium py-2.5 rounded-xl transition-all duration-300 transform hover:translate-y-[-2px] shadow-md hover:shadow-lg hover:shadow-blue-500/30 group overflow-hidden relative">
                      <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-blue-400/0 via-blue-400/30 to-blue-400/0 transform -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></span>
                      <Edit className="h-4 w-4 mr-2" />
                      <span className="relative">Edit Profile</span>
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="space-y-6 md:col-span-2">
          
            <Card className="bg-gradient-to-b from-background/90 to-background border-border shadow-xl overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-foreground flex items-center">
                    <ClipboardList className="h-5 w-5 text-primary mr-2" />
                    My Reviews
                  </CardTitle>
                </div>
                <CardDescription className="text-foreground/70">
                  Doctors you've rated recently
                </CardDescription>
              </CardHeader>
              <CardContent>
                {userReviews.length > 0 ? (
                  <ScrollArea className="h-[250px] pr-4">
                    <div className="space-y-4">
                      {userReviews.map((review) => (
                        <Link 
                          href={`/doctors/${review.doctors.doctor_id}/reviews/${review.review_id}`} 
                          key={review.review_id}
                          className="block"
                        >
                          <div className="p-4 rounded-lg bg-background/90/50 hover:bg-primary/10 transition-colors">
                            <div className="flex justify-between items-start mb-2">
                              <div>
                                <p className="text-foreground font-medium">Dr. {review.doctors.fullname}</p>
                                <p className="text-foreground/70 text-sm">{review.doctors.specialty}</p>
                              </div>
                              <div className="flex items-center bg-yellow-500/20 px-2 py-1 rounded">
                                <Star className="h-3.5 w-3.5 text-yellow-500 fill-yellow-500 mr-1" />
                                <span className="text-foreground text-sm font-medium">{review.rating.toFixed(1)}</span>
                              </div>
                            </div>
                            <div className="text-xs text-foreground/60 mt-1">
                              {format(new Date(review.review_date), 'MMMM d, yyyy')}
                            </div>
                            {review.additional_comments && (
                              <div className="mt-2 p-2 bg-background/50 rounded border-l-2 border-primary/50">
                                <p className="text-sm text-foreground/80 italic">"{review.additional_comments}"</p>
                              </div>
                            )}
                          </div>
                        </Link>
                      ))}
                    </div>
                  </ScrollArea>
                ) : (
                  <div className="text-center py-6 text-foreground/70 bg-background/90/30 rounded-lg">
                    <ClipboardList className="h-12 w-12 text-primary/40 mx-auto mb-2" />
                    <p>You haven't rated any doctors yet</p>
                    <Link href="/standings">
                      <Button className="mt-4 bg-primary hover:bg-primary/90">
                        Find Doctors to Rate
                      </Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>
            
            <Card className="bg-gradient-to-b from-background/90 to-background border-border shadow-xl overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-foreground flex items-center">
                    <Trophy className="h-5 w-5 text-yellow-500 mr-2" />
                    Top Rated Doctors
                  </CardTitle>
                  <Link href="/standings" className="text-primary text-sm hover:underline">
                    View All
                  </Link>
                </div>
                <CardDescription className="text-foreground/70">
                  Highest rated healthcare professionals
                </CardDescription>
              </CardHeader>
              <CardContent>
                {topDoctors.length > 0 ? (
                  <div className="rounded-lg overflow-hidden border border-border">
                    <table className="w-full">
                      <thead>
                        <tr className="bg-background/90">
                          <th className="py-2 px-4 text-left text-xs font-medium text-foreground/70 uppercase tracking-wider">Rank</th>
                          <th className="py-2 px-4 text-left text-xs font-medium text-foreground/70 uppercase tracking-wider">Doctor</th>
                          <th className="py-2 px-4 text-left text-xs font-medium text-foreground/70 uppercase tracking-wider">Specialty</th>
                          <th className="py-2 px-4 text-left text-xs font-medium text-foreground/70 uppercase tracking-wider">Rating</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-700">
                        {topDoctors.map((doctor, index) => (
                          <tr key={doctor.doctor_id} className="hover:bg-background/90/50">
                            <td className="py-3 px-4 text-foreground">{index + 1}</td>
                            <td className="py-3 px-4">
                              <Link href={`/doctors/${doctor.doctor_id}`} className="text-foreground hover:text-primary">
                                {doctor.fullname}
                              </Link>
                            </td>
                            <td className="py-3 px-4 text-foreground/80">{doctor.specialty}</td>
                            <td className="py-3 px-4">
                              <div className="flex items-center">
                                <Star className="h-4 w-4 text-yellow-500 mr-1" />
                                <span className="text-foreground">{doctor.community_rating ? doctor.community_rating.toFixed(1) : 'N/A'}</span>
                                <span className="text-foreground/50 text-xs ml-2">({doctor.review_count || 0})</span>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-6 text-foreground/70">
                    <Trophy className="h-12 w-12 text-primary/40 mx-auto mb-2" />
                    <p>No doctor ratings available yet</p>
                  </div>
                )}
              </CardContent>
            </Card>
            
            <Tabs defaultValue="tips" className="w-full">
              <TabsList className="w-full bg-gradient-to-r from-background/90 to-background border border-border">
                <TabsTrigger value="tips" className="flex-1 data-[state=active]:bg-primary/20">Health Tips</TabsTrigger>
                <TabsTrigger value="stats" className="flex-1 data-[state=active]:bg-primary/20">Health Statistics</TabsTrigger>
              </TabsList>
              <TabsContent value="tips">
                <Card className="bg-gradient-to-b from-background/90 to-background border-border shadow-xl overflow-hidden mt-2">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-foreground">Daily Health Tip</CardTitle>
                    <CardDescription className="text-foreground/70">
                      Advice from top doctors
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="p-5 rounded-lg bg-green-500/10 border border-green-500/20">
                      <h4 className="text-green-400 font-medium flex items-center">
                        <Activity className="h-4 w-4 mr-2" />
                        Today's Health Insight
                      </h4>
                      <p className="text-foreground/80 text-sm mt-2">
                        {randomTip}
                      </p>
                    </div>
                    
                    <div className="mt-6 space-y-4">
                      <div className="p-4 rounded-lg bg-blue-500/10 border border-blue-500/20">
                        <h4 className="text-blue-400 font-medium flex items-center">
                          <BookOpen className="h-4 w-4 mr-2" />
                          Did You Know?
                        </h4>
                        <p className="text-foreground/80 text-sm mt-1">
                          Regular check-ups can detect health problems before they start, increasing your chances of successful treatment.
                        </p>
                      </div>
                      <div className="p-4 rounded-lg bg-amber-500/10 border border-amber-500/20">
                        <h4 className="text-amber-400 font-medium flex items-center">
                          <Bell className="h-4 w-4 mr-2" />
                          Wellness Reminder
                        </h4>
                        <p className="text-foreground/80 text-sm mt-1">
                          Taking short breaks during the day to stretch and move can significantly improve your productivity and reduce stress.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              <TabsContent value="stats">
                <Card className="bg-gradient-to-b from-background/90 to-background border-border shadow-xl overflow-hidden mt-2">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-foreground">Healthcare Statistics</CardTitle>
                    <CardDescription className="text-foreground/70">
                      Visual data insights based on our database
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-foreground text-sm font-medium">Specialties by Popularity</h4>
                      </div>
                      <div className="space-y-3">
                        {specialtyStats.map((specialty) => (
                          <div key={specialty.name}>
                            <div className="flex justify-between items-center mb-1">
                              <span className="text-xs text-foreground">{specialty.name}</span>
                              <span className="text-xs text-foreground/70">{specialty.count} doctors ({specialty.percentage}%)</span>
                            </div>
                            <Progress value={specialty.percentage} className="h-2" />
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  )
} 