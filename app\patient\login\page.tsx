"use client"

import { useState } from "react"
import { LoginDialog } from "@/components/login/login-dialog"
import { useSearchParams, useRouter } from "next/navigation"
import { ChooseRoleDialog } from "@/components/registration/choose-role-dialog"

export default function PatientLoginPage() {
  const [open, setOpen] = useState(true)
  const [showSignUp, setShowSignUp] = useState(false)
  const searchParams = useSearchParams()
  const redirectTo = searchParams ? searchParams.get('redirectTo') : null
  const router = useRouter()

  // Handle login dialog closing - redirect to appropriate page
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen)
    if (!isOpen && !showSignUp) {
      // Only redirect if we're not showing the sign-up form
      // Use redirectTo if available, otherwise default to dashboard
      router.push(redirectTo || '/patient/dashboard')
    }
  }

  // Handle sign-up click
  const handleSignUpClick = () => {
    console.log("Patient login: Sign up clicked");
    setOpen(false);
    // Directly navigate to the patient registration page
    router.push('/patient/register');
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-blue-50 to-white">
      {/* Login Dialog */}
      <LoginDialog 
        open={open} 
        onOpenChange={handleOpenChange} 
        userType="patient"
        onSignUpClick={handleSignUpClick}
        redirectUrl={redirectTo || "/patient/dashboard"}
      />
      
      {/* Registration Dialog - shown when sign up clicked */}
      {showSignUp && (
        <ChooseRoleDialog
          open={showSignUp}
          onOpenChange={(isOpen) => {
            console.log("Registration dialog change:", isOpen);
            setShowSignUp(isOpen);
            if (!isOpen) {
              // If sign-up closed without completing, show login again
              setOpen(true);
            }
          }}
        />
      )}
    </div>
  )
}

