"use client"

import { useState, useEffect } from "react"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from "@supabase/supabase-js"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { toast } from "sonner"
import { Trophy, User, Home, ArrowLeft, Save, Loader2, Lock, MapPin, CalendarDays, Heart } from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useAuth } from '@/context/AuthContext'

export default function EditProfile() {
  const { isAuthenticated, user: authUser, isLoading: authIsLoading } = useAuth();
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [userDetails, setUserDetails] = useState<any>(null)
  
  // Form state
  const [username, setUsername] = useState("")
  const [firstName, setFirstName] = useState("")
  const [lastName, setLastName] = useState("")
  const [age, setAge] = useState("")
  const [gender, setGender] = useState("")
  const [phoneNumber, setPhoneNumber] = useState("")
  const [city, setCity] = useState("")
  const [state, setState] = useState("")
  const [country, setCountry] = useState("")
  const [medicalCondition, setMedicalCondition] = useState("")
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [showPasswordFields, setShowPasswordFields] = useState(false)
  
  const router = useRouter()
  const supabase = createClientComponentClient()
  
  // Create service role client for database operations
  const createServiceRoleClient = () => {
    const supabaseUrl = "https://uapbzzscckhtptliynyj.supabase.co"
    const supabaseServiceKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q"
    return createClient(supabaseUrl, supabaseServiceKey)
  }
  
  useEffect(() => {
    if (authIsLoading) return;
    if (!isAuthenticated || !authUser) {
      router.push("/");
      return;
    }
    if (authUser.userType !== 'patient') {
      router.push("/");
      return;
    }
    // Fetch user details from database using authUser.userId
    const fetchUserDetails = async () => {
      setLoading(true);
      try {
        const serviceClient = createServiceRoleClient();
        if (!authUser) return; // extra null check for TS
        const { data: userData, error: userError } = await serviceClient
          .from('users')
          .select('*')
          .eq('user_id', authUser.userId)
          .single();
        if (userError) {
          toast.error("Could not load your profile data");
          return;
        } 
        if (userData) {
          setUserDetails(userData);
          setUsername(userData.username || "");
          setFirstName(userData.first_name || "");
          setLastName(userData.last_name || "");
          setAge(userData.age ? userData.age.toString() : "");
          setGender(userData.gender || "");
          setPhoneNumber(userData["Phone_Number"] || "");
          setCity(userData.city || "");
          setState(userData["State"] || "");
          setCountry(userData.country || "");
          setMedicalCondition(userData["medical condition"] || "");
        }
      } catch (error) {
        toast.error("An error occurred while loading your profile");
      } finally {
        setLoading(false);
      }
    };
    fetchUserDetails();
  }, [authIsLoading, isAuthenticated, authUser, router]);
  
  const validateForm = () => {
    const errors: Record<string, string> = {}
    
    if (!firstName.trim()) {
      errors.firstName = "First name is required"
    }
    
    if (!lastName.trim()) {
      errors.lastName = "Last name is required"
    }
    
    if (!username.trim()) {
      errors.username = "Username is required"
    }
    
    if (phoneNumber && !/^\+?[0-9\s\-()]{8,20}$/.test(phoneNumber)) {
      errors.phoneNumber = "Please enter a valid phone number"
    }
    
    if (showPasswordFields) {
      if (!currentPassword) {
        errors.currentPassword = "Current password is required to change password"
      }
      
      if (newPassword && newPassword.length < 8) {
        errors.newPassword = "Password must be at least 8 characters"
      }
      
      if (newPassword !== confirmPassword) {
        errors.confirmPassword = "Passwords do not match"
      }
    }
    
    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error("Please correct the errors in the form")
      return
    }
    
    try {
      setSaving(true)
      
      const serviceClient = createServiceRoleClient()
      
      // Check which fields have changed from original data
      const changedFields: Record<string, any> = {}
      
      if (username !== (userDetails?.username || "")) changedFields.username = username
      if (firstName !== (userDetails?.first_name || "")) changedFields.first_name = firstName
      if (lastName !== (userDetails?.last_name || "")) changedFields.last_name = lastName
      if (age !== (userDetails?.age ? userDetails.age.toString() : "")) {
        changedFields.age = age ? parseInt(age) : null
      }
      if (gender !== (userDetails?.gender || "")) changedFields.gender = gender
      if (phoneNumber !== (userDetails?.["Phone_Number"] || "")) changedFields["Phone_Number"] = phoneNumber
      if (city !== (userDetails?.city || "")) changedFields.city = city
      if (state !== (userDetails?.["State"] || "")) changedFields["State"] = state
      if (country !== (userDetails?.country || "")) changedFields.country = country
      if (medicalCondition !== (userDetails?.["medical condition"] || "")) changedFields["medical condition"] = medicalCondition
      
      // Only update if there are changed fields
      if (Object.keys(changedFields).length > 0) {
        // Remove the updated_at timestamp as it doesn't exist in the schema
        // changedFields.updated_at = new Date().toISOString()
        
        console.log("Updating these fields:", changedFields)
        
        // Update user profile in database with only changed fields
        const { error: updateError } = await serviceClient
          .from('users')
          .update(changedFields)
          .eq('user_id', userDetails.user_id)
        
        if (updateError) {
          console.error("Error updating profile:", JSON.stringify(updateError))
          let errorMessage = 'Unknown error'
          
          if (updateError.message) {
            errorMessage = updateError.message
          } else if (updateError.details) {
            errorMessage = updateError.details
          } else if (updateError.hint) {
            errorMessage = updateError.hint
          } else if (typeof updateError === 'object') {
            errorMessage = JSON.stringify(updateError)
          }
          
          toast.error(`Failed to update your profile: ${errorMessage}`)
          return
        }
      }
      
      // Update password if requested
      if (showPasswordFields && currentPassword && newPassword) {
        try {
          console.log("Attempting to update password");
          
          // Use our custom auth API endpoint to update password
          try {
            console.log("Using custom auth endpoint for password change");
            
            const response = await fetch('/api/auth/custom/change-password', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                email: authUser?.email,
                currentPassword: currentPassword,
                newPassword: newPassword,
                userId: userDetails.user_id,
                userType: 'patient'
              }),
            });
            
            const result = await response.json();
            
            if (!response.ok || !result.success) {
              console.error("Password update failed:", result.error);
              toast.error(`Failed to update password: ${result.error || 'Unknown error'}`);
              setSaving(false);
              return;
            }
            
            console.log("Password updated successfully in custom auth system");
            
            // We don't need to update the password in the users table
            // The auth_credentials table is the source of truth for passwords
            /*
            // Also update the password in the users table if needed
            try {
              console.log("Updating password in users table for database consistency");
              const { error: dbPasswordError } = await serviceClient
                .from('users')
                .update({ 
                  password: newPassword // Store the same password in the database for consistency
                })
                .eq('user_id', userDetails.user_id);
              
              if (dbPasswordError) {
                console.error("Error updating password in users table:", dbPasswordError);
                // Non-critical error, don't return as the auth_credentials was updated successfully
              } else {
                console.log("Password successfully updated in users table");
              }
            } catch (passwordDbError) {
              console.error("Exception updating password in users table:", passwordDbError);
              // Still continue as the auth_credentials was updated successfully
            }
            */
            
            toast.success("Profile and password updated successfully");
            
            // Clear password fields
            setCurrentPassword("");
            setNewPassword("");
            setConfirmPassword("");
            setShowPasswordFields(false);
          } catch (updateError) {
            console.error("Exception during password update call:", updateError);
            toast.error(`An unexpected error occurred: ${updateError instanceof Error ? updateError.message : 'Unknown error'}`);
            setSaving(false);
            return;
          }
        } catch (passwordError) {
          console.error("Exception during password update:", passwordError);
          toast.error("An error occurred while updating your password");
          setSaving(false);
          return;
        }
      } else if (Object.keys(changedFields).length > 0) {
        toast.success("Profile updated successfully");
      } else {
        toast.info("No changes were made to your profile");
      }
      
      router.push("/patient/dashboard")
    } catch (error: any) {
      console.error("Profile update error:", error)
      let errorMessage = 'Unknown error'
      
      if (error.message) {
        errorMessage = error.message
      } else if (error.details) {
        errorMessage = error.details
      } else if (error.hint) {
        errorMessage = error.hint
      } else if (typeof error === 'object') {
        errorMessage = JSON.stringify(error)
      }
      
      console.error("Profile update error details:", JSON.stringify(error, null, 2))
      toast.error(`An unexpected error occurred: ${errorMessage}`)
    } finally {
      setSaving(false)
    }
  }
  
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-background flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <div className="w-16 h-16 border-4 border-primary/30 border-t-primary rounded-full animate-spin"></div>
          <p className="text-foreground/80">Loading your profile...</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-background pb-12">
      {/* Remove the navigation bar */}
      
      <div className="container mx-auto max-w-4xl py-12">
        <div className="mb-8">
          <Link href="/patient/dashboard">
            <Button variant="outline" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Dashboard
            </Button>
          </Link>
        </div>

        <div className="space-y-8">
          <Card className="input-group-container">
            <CardHeader>
              <CardTitle className="text-2xl">Account Information</CardTitle>
              <CardDescription>Manage your account settings</CardDescription>
            </CardHeader>
            
            <CardContent className="pt-6">
              <form onSubmit={handleSubmit}>
                <div className="grid gap-6">
                  <div className="mb-4">
                    <Badge className="bg-primary/20 text-primary hover:bg-primary/30">Account Information</Badge>
                  </div>
                  
                  {/* Email (read-only) */}
                  <div className="bg-background/50 rounded-lg p-4 border border-border">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                        <User className="h-5 w-5 text-blue-400" />
                      </div>
                      <div>
                        <p className="text-foreground text-sm">Email</p>
                        <p className="text-foreground/70 text-sm">{userDetails?.email}</p>
                      </div>
                    </div>
                    <div className="mt-3 text-xs text-foreground/60">
                      <p>Your email address cannot be changed</p>
                    </div>
                  </div>
                  
                  {/* Username */}
                  <div className="space-y-2">
                    <Label htmlFor="username" className="text-foreground">Username</Label>
                    <Input
                      id="username"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      className="bg-background/90/50 border-border text-foreground"
                      placeholder="Enter your username"
                    />
                    {formErrors.username && (
                      <p className="text-red-500 text-xs mt-1">{formErrors.username}</p>
                    )}
                  </div>
                  
                  {/* Password section */}
                  <div className="space-y-3 bg-background/50 rounded-lg p-4 border border-border">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Lock className="h-4 w-4 text-primary" />
                        <h3 className="text-foreground font-medium">Password</h3>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="text-primary hover:text-primary/80 hover:bg-primary/10"
                        onClick={() => setShowPasswordFields(!showPasswordFields)}
                      >
                        {showPasswordFields ? "Cancel" : "Change Password"}
                      </Button>
                    </div>
                    
                    {showPasswordFields && (
                      <div className="space-y-3 pt-2">
                        <div className="space-y-2">
                          <Label htmlFor="current-password" className="text-foreground">Current Password</Label>
                          <Input
                            id="current-password"
                            type="password"
                            value={currentPassword}
                            onChange={(e) => setCurrentPassword(e.target.value)}
                            className="bg-background/90/50 border-border text-foreground"
                            placeholder="Enter your current password"
                          />
                          {formErrors.currentPassword && (
                            <p className="text-red-500 text-xs mt-1">{formErrors.currentPassword}</p>
                          )}
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="new-password" className="text-foreground">New Password</Label>
                          <Input
                            id="new-password"
                            type="password"
                            value={newPassword}
                            onChange={(e) => setNewPassword(e.target.value)}
                            className="bg-background/90/50 border-border text-foreground"
                            placeholder="Enter your new password"
                          />
                          {formErrors.newPassword && (
                            <p className="text-red-500 text-xs mt-1">{formErrors.newPassword}</p>
                          )}
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="confirm-password" className="text-foreground">Confirm New Password</Label>
                          <Input
                            id="confirm-password"
                            type="password"
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                            className="bg-background/90/50 border-border text-foreground"
                            placeholder="Confirm your new password"
                          />
                          {formErrors.confirmPassword && (
                            <p className="text-red-500 text-xs mt-1">{formErrors.confirmPassword}</p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <Separator className="bg-background/80" />
                  
                  <div className="mb-2">
                    <Badge className="bg-primary/20 text-primary hover:bg-primary/30">Personal Information</Badge>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="first-name" className="text-foreground">First Name</Label>
                      <Input
                        id="first-name"
                        value={firstName}
                        onChange={(e) => setFirstName(e.target.value)}
                        className="bg-background/90/50 border-border text-foreground"
                        placeholder="Enter your first name"
                      />
                      {formErrors.firstName && (
                        <p className="text-red-500 text-xs mt-1">{formErrors.firstName}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="last-name" className="text-foreground">Last Name</Label>
                      <Input
                        id="last-name"
                        value={lastName}
                        onChange={(e) => setLastName(e.target.value)}
                        className="bg-background/90/50 border-border text-foreground"
                        placeholder="Enter your last name"
                      />
                      {formErrors.lastName && (
                        <p className="text-red-500 text-xs mt-1">{formErrors.lastName}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="age" className="text-foreground">Age</Label>
                      <Input
                        id="age"
                        type="number"
                        value={age}
                        onChange={(e) => setAge(e.target.value)}
                        className="bg-background/90/50 border-border text-foreground"
                        placeholder="Enter your age"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="gender" className="text-foreground">Gender</Label>
                      <Select 
                        value={gender} 
                        onValueChange={setGender}
                      >
                        <SelectTrigger className="bg-background/90/50 border-border text-foreground">
                          <SelectValue placeholder="Select your gender" />
                        </SelectTrigger>
                        <SelectContent className="bg-background/90 border-border text-foreground">
                          <SelectItem value="Male">Male</SelectItem>
                          <SelectItem value="Female">Female</SelectItem>
                          <SelectItem value="Other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="medical-condition" className="text-foreground">Medical Condition</Label>
                    <Textarea
                      id="medical-condition"
                      value={medicalCondition}
                      onChange={(e) => setMedicalCondition(e.target.value)}
                      className="bg-background/90/50 border-border text-foreground"
                      placeholder="Enter any relevant medical conditions"
                    />
                  </div>
                  
                  <Separator className="bg-background/80" />
                  
                  <div className="mb-2">
                    <Badge className="bg-primary/20 text-primary hover:bg-primary/30">Contact Information</Badge>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="phone-number" className="text-foreground">Phone Number</Label>
                    <Input
                      id="phone-number"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      className="bg-background/90/50 border-border text-foreground"
                      placeholder="Enter your phone number"
                    />
                    {formErrors.phoneNumber && (
                      <p className="text-red-500 text-xs mt-1">{formErrors.phoneNumber}</p>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="city" className="text-foreground flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-primary" />
                        <span>City</span>
                      </Label>
                      <Input
                        id="city"
                        value={city}
                        onChange={(e) => setCity(e.target.value)}
                        className="bg-background/90/50 border-border text-foreground"
                        placeholder="Enter your city"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="state" className="text-foreground">State/Province</Label>
                      <Input
                        id="state"
                        value={state}
                        onChange={(e) => setState(e.target.value)}
                        className="bg-background/90/50 border-border text-foreground"
                        placeholder="Enter your state/province"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="country" className="text-foreground">Country</Label>
                      <Input
                        id="country"
                        value={country}
                        onChange={(e) => setCountry(e.target.value)}
                        className="bg-background/90/50 border-border text-foreground"
                        placeholder="Enter your country"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="mt-8 flex items-center justify-end gap-3">
                  <Link href="/patient/dashboard">
                    <Button type="button" variant="outline" className="border-border text-foreground hover:bg-background/90">
                      Cancel
                    </Button>
                  </Link>
                  <Button 
                    type="submit" 
                    className="bg-primary hover:bg-primary/90 text-foreground"
                    disabled={saving}
                  >
                    {saving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 