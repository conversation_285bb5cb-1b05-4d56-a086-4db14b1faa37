"use client"

import { useState } from "react"
import { useRouter } from "next/navigation" // Import useRouter
import { PatientRegistrationDialog } from "@/components/registration/patient-registration-dialog"
import { MedicalSportsModal } from "@/components/medical-sports-modal" // Import MedicalSportsModal
import { Stethoscope } from "lucide-react" // Import Stethoscope icon

export default function PatientRegisterPage() {
  const [open, setOpen] = useState(true)
  const router = useRouter() // Initialize useRouter

  const handleCloseMessage = () => {
    router.push("/") // Navigate to home page
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-blue-50 to-white">
      <PatientRegistrationDialog open={open} onOpenChange={setOpen} />

      {!open && (
        <MedicalSportsModal
          isOpen={!open} // Modal is open when registration form is closed
          onClose={handleCloseMessage} // Close button navigates to home
          title="Registration Closed"
          icon="referee" // Using referee icon for stethoscope as per image
        >
          <div className="text-center p-4">
            {/* The empty rectangle in the image is likely the space for the icon, which is now handled by the modal's icon prop */}
            <div className="flex justify-center mb-4">
              <Stethoscope className="h-16 w-16 text-primary" /> {/* Stethoscope icon as seen in image */}
            </div>
            <p className="text-foreground/90 mb-6">You've closed the registration form. Would you like to register again?</p>
            <button
              onClick={() => setOpen(true)}
              className="px-4 py-2 bg-blue-600 text-foreground rounded-md hover:bg-blue-700 transition-colors"
            >
              Open Registration
            </button>
          </div>
        </MedicalSportsModal>
      )}
    </div>
  )
}
