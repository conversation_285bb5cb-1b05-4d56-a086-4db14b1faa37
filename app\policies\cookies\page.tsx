import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>Left } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import <PERSON>rip<PERSON> from "next/script"
import { Metadata } from "next"
import { generateMetadata } from "@/lib/seo-config"

export const metadata: Metadata = generateMetadata(
  "Cookie Policy | Data Collection Information", 
  "Doctors League cookie policy explaining how we use cookies and similar technologies to enhance your experience on our medical professional comparison platform.",
  [
    "medical website cookies", "healthcare cookie policy", "doctor website tracking", 
    "medical platform cookies", "healthcare site data collection", "medical analytics cookies"
  ],
  "/policies/cookies"
)

const cookiePolicySchema = {
  '@context': 'https://schema.org',
  '@type': 'WebPage',
  'name': 'Cookie Policy | Doctors League',
  'description': 'Our cookie policy explaining data collection through cookies and similar technologies.',
  'publisher': {
    '@type': 'Organization',
    'name': "Doctor's Leagues",
    'logo': 'https://doctorsleagues.com/logo.png'
  },
  'inLanguage': 'en',
  'lastReviewed': '2025-01-05',
  'specialty': 'Medical Data Collection'
}

export default function CookiePolicyPage() {
  return (
    <>
      <Script
        id="schema-cookie-policy"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(cookiePolicySchema) }}
      />
      <div className="min-h-screen bg-gradient-to-b from-background via-background/95 to-primary/5">
        <div className="container mx-auto px-4 py-16">
          {/* Header */}
          <div className="text-center mb-12">
            <Badge className="mb-4 bg-primary/20 text-primary hover:bg-primary/30 border-none">
              <Cookie className="h-4 w-4 mr-1" /> Cookie Policy
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
              Cookie Policy
            </h1>
            <p className="text-foreground/70 max-w-3xl mx-auto mb-6">
              Last Updated: January 5, 2025
            </p>
            <Link href="/">
              <Button variant="outline" className="border-primary/30 text-foreground">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Return to Homepage
              </Button>
            </Link>
          </div>

          {/* Main Content */}
          <div className="max-w-4xl mx-auto space-y-8">
            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">1. Introduction</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80 space-y-4">
                <p>
                  This Cookie Policy explains how Doctors League uses cookies and similar technologies to ensure you understand what data is collected when you use our Platform.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">2. What Are Cookies?</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <p>
                  Cookies are small text files stored on your device when you visit a website. They help us analyze and improve your experience on the Platform.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">3. Types of Cookies Used</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl text-foreground mb-3">Session Cookies</h3>
                    <p>These cookies allow us to maintain your session as you navigate the Platform.</p>
                  </div>
                  <div>
                    <h3 className="text-xl text-foreground mb-3">Analytics Cookies</h3>
                    <p>We use cookies to track usage patterns (e.g., page views, search queries) to improve performance.</p>
                  </div>
                  <div>
                    <h3 className="text-xl text-foreground mb-3">Preference Cookies</h3>
                    <p>These cookies store your country selection and other preferences to tailor content for your needs.</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">4. Third-Party Cookies</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <p className="mb-4">We may partner with third-party service providers that use cookies for advertising or analytics purposes. For example:</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Performance Tracking: We use tools like Google Analytics to analyze Platform performance.</li>
                  <li>Authentication: Supabase may use cookies for user authentication and security.</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">5. How You Can Manage Cookies</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <p className="mb-4">Most browsers allow you to block or delete cookies. If you do so, some features of the Platform may not work as intended.</p>
                <p className="mb-4">To disable cookies:</p>
                <ol className="list-decimal pl-6 space-y-2">
                  <li>Open your browser settings.</li>
                  <li>Navigate to the privacy or security section.</li>
                  <li>Adjust cookie preferences.</li>
                </ol>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">6. Changes to Cookie Policy</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <p>
                  Doctors League reserves the right to update this Cookie Policy at any time. Continued use of the Platform after changes constitutes acceptance of the updated policy.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">7. Contact Us</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <p className="mb-4">For questions or concerns about our cookies, please contact us at:</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Email: <EMAIL></li>
                  <li>Twitter: @DoctorsLeague</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  )
} 