import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Shield, ArrowLeft } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import <PERSON>rip<PERSON> from "next/script"
import { Metadata } from "next"
import { generateMetadata } from "@/lib/seo-config"

export const metadata: Metadata = generateMetadata(
  "Privacy Policy | Data Protection Guidelines", 
  "Doctors League privacy policy detailing how we handle personal and professional data, ensuring transparency and compliance with international privacy standards.",
  [
    "medical privacy policy", "doctor data protection", "healthcare data privacy", 
    "patient data security", "GDPR compliant medical platform", "medical data handling"
  ],
  "/policies/privacy"
)

const privacyPolicySchema = {
  '@context': 'https://schema.org',
  '@type': 'WebPage',
  'name': 'Privacy Policy | Doctors League',
  'description': 'Our comprehensive privacy policy outlining data collection, usage, and protection practices.',
  'publisher': {
    '@type': 'Organization',
    'name': "Doctor's Leagues",
    'logo': 'https://doctorsleagues.com/logo.png'
  },
  'inLanguage': 'en',
  'lastReviewed': '2025-01-05',
  'specialty': 'Medical Data Protection'
}

export default function PrivacyPolicyPage() {
  return (
    <>
      <Script
        id="schema-privacy-policy"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(privacyPolicySchema) }}
      />
      <div className="min-h-screen bg-gradient-to-b from-background via-background/95 to-primary/5">
        <div className="container mx-auto px-4 py-16">
          {/* Header */}
          <div className="text-center mb-12">
            <Badge className="mb-4 bg-primary/20 text-primary hover:bg-primary/30 border-none">
              <Shield className="h-4 w-4 mr-1" /> Privacy Policy
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
              Privacy Policy
            </h1>
            <p className="text-foreground/70 max-w-3xl mx-auto mb-6">
              Last Updated: January 5, 2025
            </p>
            <Link href="/">
              <Button variant="outline" className="border-primary/30 text-foreground">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Return to Homepage
              </Button>
            </Link>
          </div>

          {/* Main Content */}
          <div className="max-w-4xl mx-auto space-y-8">
            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">Introduction</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80 space-y-4">
                <p>
                  Doctors League is committed to safeguarding your privacy while providing a comprehensive platform to rank and compare medical professionals. This Privacy Policy outlines how we handle personal and professional data collected through our application, ensuring transparency and compliance with applicable laws.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">Types of Information Collected</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl text-foreground mb-3">Personal Information</h3>
                    <ul className="list-disc pl-6 space-y-2">
                      <li>Account Details: Name, email address, password (hashed)</li>
                      <li>Usage Data: Interaction history, search queries, comparison lists</li>
                      <li>Location-based Information: Country selection preferences, IP addresses for content filtering</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl text-foreground mb-3">Professional Information</h3>
                    <ul className="list-disc pl-6 space-y-2">
                      <li>Doctor Metrics: Ratings, reviews, performance statistics, affiliated hospitals</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl text-foreground mb-3">Third-Party Services</h3>
                    <ul className="list-disc pl-6 space-y-2">
                      <li>Authentication: Supabase for secure login</li>
                      <li>Analytics & Deployment: Tools like Google Analytics and Vercel for deployment, ensuring data security measures are in place</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">Use of Information</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl text-foreground mb-3">Purpose of Collection</h3>
                    <ul className="list-disc pl-6 space-y-2">
                      <li>To provide and enhance platform features such as rankings, comparisons, and content filtering</li>
                      <li>To improve user experience by analyzing interaction patterns and preferences</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl text-foreground mb-3">Data Utilization</h3>
                    <ul className="list-disc pl-6 space-y-2">
                      <li>Personal information is used to authenticate users and tailor experiences based on country selection and search queries</li>
                      <li>Professional data aids in generating accurate rankings and comparisons, ensuring transparency in ratings based on peer reviews, patient outcomes, and research contributions</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">User Rights</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl text-foreground mb-3">Access & Rectification</h3>
                    <p>Users can access their account details, comparison lists, and update preferences via the platform.</p>
                  </div>
                  <div>
                    <h3 className="text-xl text-foreground mb-3">Deletion & Withdrawal</h3>
                    <p>Requests to delete personal data or professional comparisons can be made through support channels, ensuring prompt action as per regulations.</p>
                  </div>
                  <div>
                    <h3 className="text-xl text-foreground mb-3">Opt-Out Options</h3>
                    <p>Users may opt-out of certain data collection practices by deactivating their accounts or contacting support for assistance.</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <p className="mb-4">For inquiries about this policy, please contact:</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Email: <EMAIL></li>
                  <li>Twitter: @DoctorsLeague</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">Legal Compliance Statement</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <p>
                  Doctors League adheres to international privacy standards, including GDPR and CCPA. By using our platform, you consent to the collection and processing of data as described in this Privacy Policy.
                </p>
                <p className="mt-4">
                  Version 1.0.1
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  )
} 