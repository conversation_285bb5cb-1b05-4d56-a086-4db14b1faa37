import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Scale, ArrowLeft } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import <PERSON>ript from "next/script"
import { Metadata } from "next"
import { generateMetadata } from "@/lib/seo-config"

export const metadata: Metadata = generateMetadata(
  "Terms of Service | User Agreement", 
  "Doctors League terms of service outlining user responsibilities, intellectual property rights, and platform usage guidelines for medical professional comparisons.",
  [
    "medical platform terms", "doctor comparison terms of service", "healthcare platform agreement", 
    "medical service terms", "medical professional ranking terms", "doctor review platform terms"
  ],
  "/policies/terms"
)

const termsSchema = {
  '@context': 'https://schema.org',
  '@type': 'WebPage',
  'name': 'Terms of Service | Doctors League',
  'description': 'Our terms of service agreement outlining the responsibilities and rights of users on our platform.',
  'publisher': {
    '@type': 'Organization',
    'name': "Doctor's Leagues",
    'logo': 'https://doctorsleagues.com/logo.png'
  },
  'inLanguage': 'en',
  'lastReviewed': '2025-01-05',
  'specialty': 'Medical Platform Terms'
}

export default function TermsOfServicePage() {
  return (
    <>
      <Script
        id="schema-terms-service"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(termsSchema) }}
      />
      <div className="min-h-screen bg-gradient-to-b from-background via-background/95 to-primary/5">
        <div className="container mx-auto px-4 py-16">
          {/* Header */}
          <div className="text-center mb-12">
            <Badge className="mb-4 bg-primary/20 text-primary hover:bg-primary/30 border-none">
              <Scale className="h-4 w-4 mr-1" /> Terms of Service
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
              Terms of Service
            </h1>
            <p className="text-foreground/70 max-w-3xl mx-auto mb-6">
              Last Updated: January 5, 2025
            </p>
            <Link href="/">
              <Button variant="outline" className="border-primary/30 text-foreground">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Return to Homepage
              </Button>
            </Link>
          </div>

          {/* Main Content */}
          <div className="max-w-4xl mx-auto space-y-8">
            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">1. Acceptance of Terms</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80 space-y-4">
                <p>
                  By accessing or using Doctors League (the "Platform"), you agree to be bound by these Terms of Service ("Terms"). If you do not agree with these Terms, you may not use the Platform.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">2. User Responsibilities</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl text-foreground mb-3">Account Creation</h3>
                    <p>Users must provide accurate and complete information when creating an account. Failure to do so may result in account termination.</p>
                  </div>
                  <div>
                    <h3 className="text-xl text-foreground mb-3">Password Security</h3>
                    <p>Users are responsible for safeguarding their passwords and ensuring that their accounts remain secure.</p>
                  </div>
                  <div>
                    <h3 className="text-xl text-foreground mb-3">Prohibited Actions</h3>
                    <ul className="list-disc pl-6 space-y-2">
                      <li>You may not use the Platform to collect or distribute unauthorized data about other users or doctors.</li>
                      <li>You may not engage in any activity that disrupts, overburdens, or otherwise harms the Platform's functionality.</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl text-foreground mb-3">Compliance with Laws</h3>
                    <p>Users must comply with all applicable laws and regulations while using the Platform.</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">3. Intellectual Property</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <ul className="list-disc pl-6 space-y-2">
                  <li>Doctors League owns all intellectual property rights to the Platform, including but not limited to its content, design, logos, and trademarks.</li>
                  <li>You may not reproduce, distribute, or modify any part of the Platform without prior written consent from Doctors League.</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">4. Use of Data</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <ul className="list-disc pl-6 space-y-2">
                  <li>Doctors League collects data for operational purposes, including improving Platform functionality, tailoring user experiences, and maintaining security.</li>
                  <li>Users can save favorite doctors, create custom comparisons, and receive updates on rankings (see our <Link href="/policies/privacy" className="text-primary hover:underline">Privacy Policy</Link> for detailed information).</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">5. Disclaimers</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <ul className="list-disc pl-6 space-y-2">
                  <li>The Platform is provided "as is" without any warranties or guarantees, expressed or implied.</li>
                  <li>Doctors League does not guarantee that the Platform will be free of errors, interruptions, or security vulnerabilities.</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">6. Limitation of Liability</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <p className="mb-4">Doctors League shall not be liable for:</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Any indirect, incidental, or consequential damages arising from your use of the Platform.</li>
                  <li>Errors or omissions in doctor rankings, performance metrics, or comparative statistics.</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">7. Termination</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <p>
                  Doctors League reserves the right to terminate or suspend access to the Platform at any time without notice for violations of these Terms or other reasons deemed necessary.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">8. Governing Law</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <p>
                  These Terms are governed by and construed in accordance with the laws of USA, and any disputes arising under these Terms will be resolved exclusively in the courts of that jurisdiction.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">9. Changes to Terms</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <p>
                  Doctors League reserves the right to modify these Terms at any time. Continued use of the Platform after changes constitutes acceptance of the updated Terms.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-background/40 backdrop-blur-md border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">10. Contact Us</CardTitle>
              </CardHeader>
              <CardContent className="text-foreground/80">
                <p className="mb-4">For questions or concerns regarding these Terms, please contact us at:</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Email: <EMAIL></li>
                  <li>Twitter: @DoctorsLeague</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  )
} 