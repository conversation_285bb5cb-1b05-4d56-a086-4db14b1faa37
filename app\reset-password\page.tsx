"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { <PERSON>ertTriangle, CheckCircle, ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function ResetPasswordPage() {
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [token, setToken] = useState<string | null>(null)
  const [validToken, setValidToken] = useState<boolean>(false)
  const [validating, setValidating] = useState<boolean>(true)
  const router = useRouter()
  const searchParams = useSearchParams()
  
  useEffect(() => {
    // First, check for the token in the URL query parameters
    const tokenFromQuery = searchParams?.get('token')
    if (tokenFromQuery) {
      console.log("Found token in URL query:", tokenFromQuery.substring(0, 10) + "...")
      setToken(tokenFromQuery)
      validateToken(tokenFromQuery)
      return
    }
    
    // If no token in query, check the URL hash (legacy Supabase Auth approach)
    const parseHash = async () => {
      try {
        // Check for URL hash (Supabase puts tokens in the hash fragment)
        if (window.location.hash) {
          console.log("Found hash fragment in URL, using legacy flow")
          const hashParams = new URLSearchParams(window.location.hash.substring(1));
          const accessToken = hashParams.get('access_token');
          const type = hashParams.get('type');
          
          if (accessToken && type === 'recovery') {
            console.log("Found legacy recovery access token");
            setValidToken(true)
            setValidating(false)
          } else {
            console.log("No valid recovery token found in URL hash");
            setError("Invalid password reset link. Please request a new one.");
            setValidating(false)
          }
        } else {
          console.log("No token in URL query or hash fragment");
          setError("Invalid or missing reset token. Please request a new password reset link.");
          setValidating(false)
        }
      } catch (err) {
        console.error("Error parsing URL or token:", err);
        setError("Invalid or expired password reset link");
        setValidating(false)
      }
    };
    
    parseHash();
  }, [searchParams]);
  
  // Function to validate the token
  const validateToken = async (tokenToValidate: string) => {
    try {
      // Here we would validate the token, but for now we'll just check if it exists
      // In a real implementation, you might want to make an API call to validate the token
      if (tokenToValidate && tokenToValidate.length > 10) {
        setValidToken(true)
      } else {
        setError("Invalid reset token")
      }
    } catch (err) {
      console.error("Error validating token:", err)
      setError("Error validating reset token")
    } finally {
      setValidating(false)
    }
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate passwords
    if (password !== confirmPassword) {
      setError("Passwords do not match")
      return
    }
    
    if (password.length < 6) {
      setError("Password must be at least 6 characters")
      return
    }
    
    setLoading(true)
    setError(null)
    
    try {
      // Handle both legacy and new flows
      if (token) {
        // New custom token-based flow
        const response = await fetch('/api/auth/reset-password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            token,
            password
          }),
        })
        
        const result = await response.json()
        
        if (!response.ok) {
          console.error("Error resetting password:", result)
          setError(result.error || "Failed to reset password")
        } else {
          setSuccess(true)
          // Clear form
          setPassword("")
          setConfirmPassword("")
        }
      } else {
        // Legacy Supabase Auth flow using client component API
        // The Supabase client should be initialized elsewhere and the session already set
        console.log("Using legacy Supabase Auth flow to update password")
        const { error } = await fetch('/api/auth/custom/change-password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            newPassword: password
          }),
        }).then(res => res.json())
        
        if (error) {
          console.error("Error updating password:", error)
          setError(error.message || "Failed to reset password")
        } else {
          setSuccess(true)
          // Clear form
          setPassword("")
          setConfirmPassword("")
        }
      }
    } catch (err: any) {
      console.error("Exception in resetPassword:", err)
      setError(err.message || "An unexpected error occurred")
    } finally {
      setLoading(false)
    }
  }

  // Show loading state while validating token
  if (validating) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-background p-4">
        <div className="w-full max-w-md bg-background/60 backdrop-blur-md p-8 rounded-xl border border-primary/20 shadow-xl">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-foreground mb-4">Validating Reset Link</h1>
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
            </div>
            <p className="mt-4 text-muted-green">Please wait while we validate your reset link...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-background p-4">
      <div className="w-full max-w-md bg-background/60 backdrop-blur-md p-8 rounded-xl border border-primary/20 shadow-xl">
        <div className="mb-6 text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">Reset Your Password</h1>
          <p className="text-muted-green">Please create a new password for your account</p>
        </div>
        
        {success ? (
          <div className="space-y-4">
            <div className="p-4 rounded-md bg-green-500/20 border border-green-500/50 flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-green-500 shrink-0 mt-0.5" />
              <div>
                <p className="text-foreground font-medium">Password Updated Successfully!</p>
                <p className="text-sm text-foreground/80 mt-1">
                  Your password has been reset. You can now log in with your new password.
                </p>
              </div>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <div className="p-3 rounded-md bg-red-500/20 border border-red-500/50 flex items-start gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500 shrink-0 mt-0.5" />
                <p className="text-sm text-foreground">{error}</p>
              </div>
            )}
            
            {!validToken && (
              <div className="p-3 rounded-md bg-amber-500/20 border border-amber-500/50 flex items-start gap-2">
                <AlertTriangle className="h-5 w-5 text-amber-500 shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm text-foreground">Invalid or expired reset link.</p>
                  <Link 
                    href="/"
                    className="text-xs text-primary hover:underline mt-1 inline-block"
                  >
                    Request a new password reset
                  </Link>
                </div>
              </div>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="password" className="text-foreground">
                New Password
              </Label>
              <Input
                id="password"
                type="password"
                placeholder="Enter your new password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="bg-card border-border text-card-foreground placeholder:text-foreground/50"
                disabled={!validToken}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-foreground">
                Confirm New Password
              </Label>
              <Input
                id="confirmPassword"
                type="password"
                placeholder="Confirm your new password"
                required
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="bg-card border-border text-card-foreground placeholder:text-foreground/50"
                disabled={!validToken}
              />
            </div>
            
            <Button
              type="submit"
              disabled={loading || !validToken}
              className="w-full bg-primary hover:bg-primary/90 text-foreground"
            >
              {loading ? "Resetting Password..." : "Reset Password"}
            </Button>
            
            <div className="text-center mt-4">
              <Link 
                href="/"
                className="inline-flex items-center text-sm text-primary hover:text-primary/80"
              >
                <ArrowLeft className="w-4 h-4 mr-1" />
                Back to Home
              </Link>
            </div>
          </form>
        )}
      </div>
    </div>
  )
} 