"use client"

import { useState, useEffect } from "react"
import { getCountries } from "@/lib/services/countries-service"
import { getSpecialties } from "@/lib/services/specialties-service" 
import { getDoctors } from "@/lib/services/doctors-service"
import { getHospitals } from "@/lib/services/hospitals-service"
import { getDoctorReviews } from "@/lib/services/reviews-service"

export default function ServicesTestPage() {
  const [results, setResults] = useState<any>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function testServices() {
      setLoading(true)
      const testResults: any = {}

      // Test each service individually
      try {
        const countries = await getCountries()
        testResults.countries = {
          success: true,
          count: countries.length,
          sample: countries.length > 0 ? countries[0].country_name : null
        }
      } catch (e: any) {
        testResults.countries = {
          success: false,
          error: e.message
        }
      }

      try {
        const specialties = await getSpecialties()
        testResults.specialties = {
          success: true,
          count: specialties.length,
          sample: specialties.length > 0 ? specialties[0].specialty_name : null
        }
      } catch (e: any) {
        testResults.specialties = {
          success: false,
          error: e.message
        }
      }

      try {
        const doctors = await getDoctors()
        testResults.doctors = {
          success: true,
          count: doctors.length,
          sample: doctors.length > 0 ? doctors[0].fullname : null
        }
      } catch (e: any) {
        testResults.doctors = {
          success: false,
          error: e.message
        }
      }

      try {
        const hospitalsResult = await getHospitals()
        // Handle the possibility that the hospitals result might be structured differently
        const hospitals = Array.isArray(hospitalsResult) 
          ? hospitalsResult 
          : hospitalsResult.hospitals || [];
        
        testResults.hospitals = {
          success: true,
          count: hospitals.length,
          sample: hospitals.length > 0 ? hospitals[0]?.hospital_name : null
        }
      } catch (e: any) {
        testResults.hospitals = {
          success: false,
          error: e.message
        }
      }

      try {
        // Just test with doctor_id 1 for now
        const reviews = await getDoctorReviews(1)
        testResults.reviews = {
          success: true,
          count: reviews.length
        }
      } catch (e: any) {
        testResults.reviews = {
          success: false,
          error: e.message
        }
      }
      
      setResults(testResults)
      setLoading(false)
    }

    testServices()
  }, [])

  return (
    <div className="p-8 max-w-4xl mx-auto bg-background text-foreground">
      <h1 className="text-2xl font-bold mb-6">Services Test</h1>
      
      {loading ? (
        <div className="animate-pulse">Testing services...</div>
      ) : (
        <div className="space-y-6">
          {Object.entries(results).map(([serviceName, result]: [string, any]) => (
            <div key={serviceName} className="border border-primary/30 rounded-lg p-4">
              <h2 className="text-xl font-semibold mb-2 capitalize">{serviceName} Service</h2>
              <div className="flex items-center gap-2 mb-2">
                <div className={`w-4 h-4 rounded-full ${result.success ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span>{result.success ? 'Success' : 'Failed'}</span>
              </div>
              {result.error && (
                <div className="text-red-400 text-sm mt-2">
                  Error: {result.error}
                </div>
              )}
              {result.count !== undefined && (
                <div className="text-green-400 text-sm">
                  Retrieved {result.count} {serviceName}
                  {result.sample && ` (e.g., ${result.sample})`}
                </div>
              )}
            </div>
          ))}
          
          <div className="mt-6 p-4 bg-background/90 rounded-lg">
            <h3 className="font-semibold">Conclusion:</h3>
            <p className="mt-2">
              {Object.values(results).every((result: any) => result.success)
                ? "All services are working properly. If you're still experiencing issues, there might be a problem with how pages use these services."
                : "Some services are failing. Check the errors above for more details."}
            </p>
          </div>
        </div>
      )}
    </div>
  )
} 