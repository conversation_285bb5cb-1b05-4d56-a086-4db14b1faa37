import { generateSitemapXML } from "@/lib/sitemap-enhanced"
import { NextResponse } from "next/server"

export async function GET() {
  try {
    // Generate the sitemap XML content using the enhanced generator
    const sitemapXML = await generateSitemapXML()
    
    // Return the sitemap with appropriate content type
    return new NextResponse(sitemapXML, {
      headers: {
        'Content-Type': 'application/xml',
        // Cache the sitemap for 24 hours (in seconds)
        'Cache-Control': 'public, max-age=86400, s-maxage=86400'
      }
    })
  } catch (error) {
    console.error('Error generating sitemap:', error)
    return new NextResponse('Error generating sitemap', { status: 500 })
  }
} 