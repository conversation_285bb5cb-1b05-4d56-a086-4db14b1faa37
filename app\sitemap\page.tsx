import Link from 'next/link'
import { Separator } from '@/components/ui/separator'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { getSitemapUrlsByCategory } from '@/lib/sitemap-enhanced'
import { <PERSON><PERSON>crumbs, SchemaMarkup } from '@/components/seo'
import { generateMetadata } from '@/lib/seo-config'
import { MapPin } from 'lucide-react'

// SEO metadata for the sitemap page
export const metadata = generateMetadata(
  "Site Map | Doctor's Leagues",
  "Complete sitemap of Doctor's Leagues website, containing links to all our pages on medical professional rankings, doctor comparisons, and healthcare expertise.",
  ["doctor website sitemap", "medical site map", "doctor comparison website pages", "healthcare navigation"],
  "/sitemap"
)

export default async function SitemapPage() {
  // Get all URLs grouped by category
  const urlsByCategory = await getSitemapUrlsByCategory()
  
  // Generate schema markup for the site
  const siteNavigationSchema = {
    '@context': 'https://schema.org',
    '@type': 'ItemList',
    'itemListElement': Object.entries(urlsByCategory).flatMap(([category, urls]) => 
      urls.map((url, index) => ({
        '@type': 'ListItem',
        'position': index + 1,
        'name': url.name,
        'item': url.url
      }))
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Schema markup */}
      <SchemaMarkup schema={siteNavigationSchema} id="site-navigation-schema" />
      
      {/* Breadcrumbs */}
      <Breadcrumbs
        items={[
          { label: 'Sitemap', href: '/sitemap' }
        ]}
        className="mb-8"
      />
      
      {/* Page header */}
      <div className="mb-8 text-center">
        <div className="inline-flex items-center justify-center p-2 bg-primary/10 rounded-full mb-4">
          <MapPin className="h-6 w-6 text-primary" />
        </div>
        <h1 className="text-3xl font-bold mb-2">Site Map</h1>
        <p className="text-muted-green max-w-2xl mx-auto">
          A complete map of all pages on Doctor's Leagues to help you navigate our website.
        </p>
      </div>
      
      {/* Sitemap categories */}
      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
        {Object.entries(urlsByCategory).map(([category, urls]) => (
          <Card key={category} className="border-primary/10">
            <CardHeader className="pb-2">
              <CardTitle>{category}</CardTitle>
              <CardDescription>{urls.length} pages</CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {urls.map((url, index) => (
                  <li key={index}>
                    <Link 
                      href={url.url.replace('https://doctorsleagues.com', '')} 
                      className="text-primary/90 hover:text-primary transition-colors hover:underline"
                    >
                      {url.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
} 