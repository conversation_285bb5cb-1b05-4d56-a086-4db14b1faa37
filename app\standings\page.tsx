"use client"

import { createB<PERSON>erClient } from "@/lib/supabase-client"
import { createClient } from "@supabase/supabase-js"
import { Trophy, Star, Medal, Activity, Shield, Award, TrendingUp, Search, Info, Heart, Brain, Stethoscope, Building, ChevronRight } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ECGDivider } from "@/components/ecg-divider"
import { motion } from "framer-motion"
import { FALLBACK_DOCTORS, FALLBACK_SPECIALTIES } from "@/lib/fallback-data"
import { useEffect, useState, useCallback, ChangeEvent } from "react" // Added ChangeEvent
import Image from "next/image"
import { cn } from "@/lib/utils"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select" // Added Select component
// Import Ad types and fetcher/display components
import { Ad, getAdsForPage } from "@/actions/ad-actions"
import { PositionedAdDisplay } from "@/components/ads/positioned-ad-display";


// Define custom interfaces for our specific needs
interface Doctor {
  doctor_id: string | number
  fullname: string
  specialty: string
  community_rating: number
  wins: number
  losses: number
  draws: number
  points: number
  form: string
  hospital_name?: string
  profile_image?: string
  specialty_id?: string | number
  rank?: number
  facility?: string
  medical_title?: string
  educational_background?: string
  experience?: number
}

interface Specialty {
  specialty_id: string | number
  specialty_name: string
  description?: string
  avgRating?: number
  doctorCount?: number
}

// Added Country interface
interface Country {
  country_id: string | number;
  country_name: string;
}

interface LeagueTableProps {
  title: string
  doctors: Doctor[]
  specialty_id: string | number
}

// This component seems unused in the main page logic, keeping it for now
function LeagueTable({ title, doctors, specialty_id }: LeagueTableProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-4"
    >
      <Card className="bg-background/40 border-primary/20 backdrop-blur-sm shadow-lg overflow-hidden" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-bold flex items-center gap-2 text-foreground">
              <Trophy className="w-6 h-6 text-yellow-500" />
              {title}
            </CardTitle>
            {/* Link seems hardcoded to country 1, might need adjustment if this component is used */}
            <Link href={`/divisions/1/${specialty_id}`}>
              <Button variant="outline" size="sm" className="text-primary border-primary/30 hover:bg-primary/10">
                View League
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-left">
              <thead>
                <tr className="border-b border-border/50">
                  <th className="px-2 py-3 text-xs font-medium text-foreground/60">Rank</th>
                  <th className="px-2 py-3 text-xs font-medium text-foreground/60">Doctor</th>
                  <th className="px-2 py-3 text-xs font-medium text-foreground/60 text-center">Rating</th>
                  <th className="px-2 py-3 text-xs font-medium text-foreground/60 text-center">W</th>
                  <th className="px-2 py-3 text-xs font-medium text-foreground/60 text-center">D</th>
                  <th className="px-2 py-3 text-xs font-medium text-foreground/60 text-center">L</th>
                  <th className="px-2 py-3 text-xs font-medium text-foreground/60 text-center">Pts</th>
                  <th className="px-2 py-3 text-xs font-medium text-foreground/60 text-center">Form</th>
                </tr>
              </thead>
              <tbody>
                {doctors.map((doctor, index) => (
                  <tr
                    key={doctor.doctor_id}
                    className="border-b border-border hover:bg-muted-green/50 transition-colors"
                  >
                    <td className="px-2 py-3 text-sm text-foreground/80">
                      {doctor.rank || index + 1}
                    </td>
                    <td className="px-2 py-3">
                      <Link href={`/doctor/${doctor.doctor_id}`} className="hover:text-primary">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center overflow-hidden">
                            {doctor.profile_image ? (
                              <Image
                                src={doctor.profile_image}
                                alt={doctor.fullname}
                                width={32}
                                height={32}
                                className="object-cover"
                              />
                            ) : (
                              <span className="text-xs text-primary">
                                {doctor.fullname.substring(0, 2)}
                              </span>
                            )}
                          </div>
                          <div>
                            <div className="text-sm font-medium text-foreground">{doctor.fullname}</div>
                            <div className="text-xs text-foreground/60">{doctor.hospital || "Independent Practice"}</div>
                          </div>
                        </div>
                      </Link>
                    </td>
                    <td className="px-2 py-3 text-center">
                      <div className="flex items-center justify-center gap-1">
                        <Star className="w-3 h-3 text-yellow-500 fill-yellow-500" />
                        <span className="text-sm text-foreground/80">{doctor.community_rating?.toFixed(1) || "-"}</span>
                      </div>
                    </td>
                    <td className="px-2 py-3 text-sm text-green-500 text-center font-medium">{doctor.wins}</td>
                    <td className="px-2 py-3 text-sm text-blue-400 text-center font-medium">{doctor.draws}</td>
                    <td className="px-2 py-3 text-sm text-red-500 text-center font-medium">{doctor.losses}</td>
                    <td className="px-2 py-3 text-sm text-foreground font-bold text-center">{doctor.points}</td>
                    <td className="px-2 py-3 text-center">
                      <div className="flex items-center justify-center gap-0.5">
                        {(doctor.form || "").split("").map((result, i) => {
                          const bgColor = result === "W"
                            ? "bg-green-500"
                            : result === "D"
                              ? "bg-blue-500"
                              : "bg-red-500";

                          return (
                            <div
                              key={i}
                              className={`w-4 h-4 rounded-full flex items-center justify-center ${bgColor} text-[10px] font-bold text-foreground`}
                            >
                              {result}
                            </div>
                          );
                        })}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

// Helper function to calculate points to ensure consistency
function calculatePoints(doctor: any): number {
  // 3 points for win, 1 for draw
  return (doctor.wins || 0) * 3 + (doctor.draws || 0);
}

// Helper function to generate display name for specialty card
function getSpecialtyDisplayName(specialty: string): string {
  // Make sure specialty name is properly capitalized and formatted
  return specialty.replace(/\b\w/g, l => l.toUpperCase());
}

// Helper function to format community_rating value with validation
function formatRating(community_rating: number | undefined | null): string {
  // First convert string ratings to numbers if needed
  let numericRating = 0;

  if (community_rating !== undefined && community_rating !== null) {
    if (typeof community_rating === 'string') {
      numericRating = parseFloat(community_rating);
    } else if (typeof community_rating === 'number') {
      numericRating = community_rating;
    }
  }

  // Check if it's a valid number after conversion
  if (isNaN(numericRating)) {
    return "0.0";
  }

  // Limit to one decimal place and clamp between 0-5
  return Math.max(0, Math.min(5, numericRating)).toFixed(1);
}

function doctorInitials(name: string): string {
  if (!name) return "DR";

  // Extract initials from name
  const parts = name.split(/\s+/);
  if (parts.length >= 2) {
    return (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
  } else {
    // If only one name part, use first two letters
    return name.substring(0, 2).toUpperCase();
  }
}

// Map specialty names to icon filenames
const specialtyIconMapping: { [key: string]: string } = {
  'allergy and immunology': 'Allergy and immunology.svg',
  'anaesthesia': 'Anaesthesia.svg',
  'audiology-speech therapy': 'Audiology-Speech therapy.svg',
  'cardiac surgery': 'Cardiac Surgery.svg',
  'cardiology': 'Cardiology.svg',
  'chiropractic care': 'Chiropractic Care.svg',
  'dental': 'Dental.svg',
  'dermatology': 'Dermatology.svg',
  'dermatology & cosmetology': 'Dermatology & cosmetology.svg',
  'dietitian': 'Dietitian.svg',
  'ear, nose and throat (ent)': 'Ear, nose and throat (ent).svg',
  'emergency medicine': 'Emergency medicine.svg',
  'endocrinology': 'Endocrinology.svg',
  'family medicine': 'Family medicine.svg',
  'gastroenterology': 'Gastroenterology.svg',
  'general surgery': 'General surgery.svg',
  'general medicine': 'General medicine.svg',
  'general practitioner': 'General Practitioner.svg',
  'geriatric medicine': 'Geriatric Medicine.svg',
  'hematology': 'Hematology.svg',
  'icu': 'Icu.svg',
  'infectious diseases & clinical microbiology': 'Infectious diseases & clinical microbiology.svg',
  'internal medicine': 'Internal medicine.svg',
  'ivf - in vitro fertilization': 'Ivf - in vitro fertilization.svg',
  'medical genetics': 'Medical genetics.svg',
  'neonatology': 'Neonatology.svg',
  'nephrology': 'Nephrology.svg',
  'neurology': 'Neurology.svg',
  'neurosurgery': 'Neurosurgery.svg',
  'nuclear medicine': 'Nuclear Medicine.svg',
  'nutrition': 'Nutrition.svg',
  'occupational medicine': 'General medicine.svg',
  'obstetrics and gynecology': 'Obstetrics and gynecology.svg',
  'oncology': 'Oncology.svg',
  'ophthalmology': 'Ophthalmology.svg',
  'oral & maxillofacial surgery': 'Oral & maxillofacial surgery.svg',
  'orthodontics': 'Orthodontics.svg',
  'orthopedics': 'Orthopedics.svg',
  'osteopathy': 'Osteopathy.svg',
  'paediatrics': 'Pediatrics.svg',
  'pain management': 'Pain Management.svg',
  'pathology': 'Pathology.svg',
  'pediatric surgery': 'Pediatric Surgery.svg',
  'physiotherapy': 'Physiotherapy.svg',
  'plastic surgery': 'Plastic surgery.svg',
  'podiatry': 'Podiatry.svg',
  'psychiatry': 'Psychiatry.svg',
  'psychiatry and psychology': 'Mental health.svg',
  'psychology': 'Psychology.svg',
  'public health medicine': 'Public Health Medicine.svg',
  'pulmonology': 'Pulmonology.svg',
  'radiology': 'Radiology.svg',
  'reproductive endocrinology and infertility': 'Ivf - in vitro fertilization.svg',
  'rheumatology': 'Rheumatology.svg',
  'sleep medicine': 'Sleep Medicine.svg',
  'spinal surgery': 'Spinal Surgery.svg',
  'urology': 'Urology.svg',
  'vascular medicine': 'Vascular medicine.svg',
  'vascular surgery': 'Vascularsurgery.svg',
};

interface SpecialtyCardProps {
  specialty: Specialty;
  position: number;
  doctorCount: number;
  topDoctors: Doctor[];
  specialtyId: string | number;
  selectedCountryId: string;
}

const SpecialtyCard = ({ 
  specialty, 
  position, 
  doctorCount, 
  topDoctors, 
  specialtyId, 
  selectedCountryId 
}: SpecialtyCardProps) => {
  // Ensure we have valid values for display
  const displayName = specialty.specialty_name || "Unknown Specialty";
  const iconFilename = specialtyIconMapping[displayName.toLowerCase()] || 'General Practitioner.svg';
  const iconPath = `/Green/${iconFilename}`;

  // Add debug info when rendering each card
  useEffect(() => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] STANDINGS PAGE: Rendering specialty card for ${displayName} (ID: ${specialtyId}) with ${topDoctors?.length || 0} doctors`);
    if (topDoctors?.length > 0) {
      console.log(`[${timestamp}] STANDINGS PAGE: Top doctors: ${topDoctors.map(d => `${d.fullname} (${d.community_rating})`).join(', ')}`);
    }
  }, [displayName, specialtyId, topDoctors]);

  // Validate and sort top doctors data by community_rating to ensure consistent display
  const validatedTopDoctors = topDoctors && Array.isArray(topDoctors)
    ? topDoctors
        .filter(doctor => doctor && doctor.doctor_id && doctor.fullname)
        .sort((a, b) => (b.community_rating || 0) - (a.community_rating || 0))
    : [];
    
  // Define the link href outside the JSX to potentially resolve scope issues
  const divisionLink = `/divisions/${selectedCountryId}/${specialtyId}`;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: position * 0.1 }}
      className="h-full"
    >
      <Card className="bg-background/40 backdrop-blur-sm border-primary/20 h-full shadow-xl hover:shadow-primary/20 transition-all duration-300" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="text-foreground flex items-center gap-3">
              <div className="p-2 rounded-full bg-white">
                <Image src={iconPath} alt={`${displayName} icon`} width={32} height={32} className="w-8 h-8" />
              </div>
              {displayName}
            </CardTitle>
            <Badge variant="outline" className="bg-background/30 text-foreground border-primary/30">
              {doctorCount} {doctorCount === 1 ? 'doctor' : 'doctors'}
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="space-y-3 mt-3">
            {doctorCount > 0 ? (
              validatedTopDoctors.length > 0 ? (
                validatedTopDoctors.map((doctor, index) => {
                  const doctorRating = formatRating(doctor.community_rating);
                  const hospitalName = doctor.hospital || "Independent Practice";

                  return (
                    <Link 
                      href={`/doctors/${doctor.doctor_id}`} 
                      key={doctor.doctor_id}
                      className="flex items-center justify-between bg-muted-green/50 hover:bg-card transition-colors rounded-lg p-2.5 group"
                    >
                      <div className="flex items-center flex-1 min-w-0">
                        <div className="relative flex-shrink-0 mr-2 w-8 text-center">
                          {/* Medal indicators with rank numbers */}
                          {index === 0 && (
                            <div className="relative">
                              <div className="absolute -left-3 -top-1 w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center shadow-lg z-10 group-hover:scale-110 transition-transform">
                                <Trophy className="w-3 h-3 text-foreground" />
                              </div>
                              <div className="h-7 w-7 rounded-full bg-gradient-to-br from-yellow-400/20 to-yellow-600/50 flex items-center justify-center border border-yellow-500/30">
                                <span className="text-sm font-bold text-foreground">1</span>
                              </div>
                            </div>
                          )}
                          {index === 1 && (
                            <div className="relative">
                              <div className="absolute -left-3 -top-1 w-5 h-5 bg-green-300 rounded-full flex items-center justify-center shadow-lg z-10 group-hover:scale-110 transition-transform">
                                <Medal className="w-3 h-3 text-muted-green" />
                              </div>
                              <div className="h-7 w-7 rounded-full bg-gradient-to-br from-gray-300/20 to-gray-500/50 flex items-center justify-center border border-green-500/30">
                                <span className="text-sm font-bold text-foreground">2</span>
                              </div>
                            </div>
                          )}
                          {index === 2 && (
                            <div className="relative">
                              <div className="absolute -left-3 -top-1 w-5 h-5 bg-amber-700 rounded-full flex items-center justify-center shadow-lg z-10 group-hover:scale-110 transition-transform">
                                <Medal className="w-3 h-3 text-foreground" />
                              </div>
                              <div className="h-7 w-7 rounded-full bg-gradient-to-br from-amber-600/20 to-amber-800/50 flex items-center justify-center border border-amber-700/30">
                                <span className="text-sm font-bold text-foreground">3</span>
                              </div>
                            </div>
                          )}
                        </div>

                        <div className="flex-1 min-w-0 ml-1">
                          <div className="text-sm font-medium text-foreground truncate">
                            {doctor.fullname}
                          </div>
                          <div className="text-xs text-foreground/60 truncate">{hospitalName}</div>
                        </div>
                      </div>

                      <div className="flex items-center ml-2 flex-shrink-0">
                        <div className="flex items-center text-yellow-500 bg-background/50 px-2 py-1 rounded-full">
                          <Star className="w-3 h-3 fill-yellow-500 mr-0.5" />
                          <span className="text-sm">{doctorRating}</span>
                        </div>
                      </div>
                    </Link>
                  );
                })
              ) : null
            ) : (
              <div className="text-center py-6 text-foreground/60 text-sm bg-background/20 rounded-lg">
                <div className="flex flex-col items-center gap-2">
                  <Info className="w-5 h-5 text-primary/70" />
                  <span>No doctors registered in this specialty for the selected country.</span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
        
        <CardFooter className="pt-0">
          <Link
            href={divisionLink} // Use the defined variable here
            className="text-sm text-primary hover:text-primary/80 transition-colors flex items-center group"
          >
            View Complete Rankings
            <ChevronRight className="ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform" />
          </Link>
        </CardFooter>
      </Card>
    </motion.div>
  );
};

export default function StandingsPage() {
  const [specialties, setSpecialties] = useState<Specialty[]>([])
  const [topDoctorsBySpecialty, setTopDoctorsBySpecialty] = useState<{[key: string]: Doctor[]}>({})
  const [doctorCountBySpecialty, setDoctorCountBySpecialty] = useState<{[key: string]: number}>({})
  const [loading, setLoading] = useState(true)
  const [logMessages, setLogMessages] = useState<string[]>([])
  const [countries, setCountries] = useState<Country[]>([]); // State for countries list
  const [selectedCountryId, setSelectedCountryId] = useState<string>("1") // Default to 1
  // State for ads
  const [bannerAds, setBannerAds] = useState<Ad[]>([]);
  const [sidebarAds, setSidebarAds] = useState<Ad[]>([]);
  const [sideLeftAds, setSideLeftAds] = useState<Ad[]>([]);
  const [sideRightAds, setSideRightAds] = useState<Ad[]>([]);
  const [inContentAds, setInContentAds] = useState<Ad[]>([]);
  const [bottomAds, setBottomAds] = useState<Ad[]>([]);


  // Helper to update the country filter from localStorage
  const updateCountryFilter = useCallback(() => {
    if (typeof window !== 'undefined') {
      const savedCountry = localStorage.getItem('selectedCountryId')
      if (savedCountry) {
        setSelectedCountryId(savedCountry)
        console.log(`[${new Date().toISOString()}] STANDINGS PAGE: Initialized country filter from localStorage: ${savedCountry}`)
      } else {
        console.log(`[${new Date().toISOString()}] STANDINGS PAGE: No country found in localStorage, using default: ${selectedCountryId}`)
      }
    }
  }, [selectedCountryId]) // Dependency added, though it's default

  // Handle country selection change
  const handleCountryChange = (value: string) => {
    if (value) {
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] STANDINGS PAGE: Country selection changed to: ${value}`);
      setSelectedCountryId(value);
      if (typeof window !== 'undefined') {
        localStorage.setItem('selectedCountryId', value);
        console.log(`[${timestamp}] STANDINGS PAGE: Saved selected country ID to localStorage: ${value}`);
      }
    }
  };

  // Initialize country filter on mount and listen for storage changes
  useEffect(() => {
    updateCountryFilter()

    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'selectedCountryId') {
        console.log(`[${new Date().toISOString()}] STANDINGS PAGE: Detected localStorage change for selectedCountryId.`);
        updateCountryFilter();
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [updateCountryFilter])

  // Use useCallback to memoize the addLog function
  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] STANDINGS PAGE: ${message}`);
    setLogMessages(prev => [...prev, `[${timestamp}] ${message}`]);
  }, []);

  // Fetch Ads
  useEffect(() => {
    const fetchPageAds = async () => {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] STANDINGS PAGE: Fetching ads directly from Supabase...`);
        const supabase = createBrowserClient();

        const fetchPlacementAds = async (placement: string) => {
          const pageVariations = ['standings', 'standing'];
          const placementStrings = pageVariations.map(page => `${page}:${placement}`);
          
          const { data, error } = await supabase
            .from('ads')
            .select('*')
            .eq('status', 'active')
            .or(placementStrings.map(p => `placements.cs.{${p}}`).join(','))
            .order('created_at', { ascending: false });
          
          if (error) {
            console.error(`[${timestamp}] STANDINGS PAGE: Error fetching ${placement} ads:`, error);
            return [];
          }
          
          console.log(`[${timestamp}] STANDINGS PAGE: Successfully fetched ${data?.length ?? 0} active ad(s) for ${placementStrings.join(', ')}.`);
          return data || [];
        };

        try {
          const [bannerResult, sidebarResult, sideLeftResult, sideRightResult, inContentResult, bottomResult] = await Promise.all([
            fetchPlacementAds('banner'),
            fetchPlacementAds('sidebar'),
            fetchPlacementAds('side-left'),
            fetchPlacementAds('side-right'),
            fetchPlacementAds('in-content'),
            fetchPlacementAds('bottom')
          ]);

          setBannerAds(bannerResult);
          setSidebarAds(sidebarResult);
          setSideLeftAds(sideLeftResult);
          setSideRightAds(sideRightResult);
          setInContentAds(inContentResult);
          setBottomAds(bottomResult);
          
          console.log(`[${timestamp}] STANDINGS PAGE: All ads fetched directly from Supabase.`);
        } catch (error) {
           console.error(`[${timestamp}] STANDINGS PAGE: Error fetching ads in parallel:`, error);
        }
    };
    fetchPageAds();
  }, []); // Fetch ads once on mount


  // Fetch Countries, Specialties, and Doctors based on selectedCountryId
  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] STANDINGS PAGE: Fetching data for country ID: ${selectedCountryId}`);

      try {
        const supabase = createBrowserClient();

        // Fetch countries
        const { data: countriesData, error: countriesError } = await supabase
          .from("countries")
          .select("country_id, country_name");

        if (countriesError) throw countriesError;
        setCountries(countriesData || []);

        // Fetch specialties with doctor counts using the new RPC
        const { data: specialtiesData, error: specialtiesError } = await supabase.rpc(
          "get_specialties_by_country_with_doctor_count",
          { p_country_id: selectedCountryId }
        );

        if (specialtiesError) throw specialtiesError;

        const specialtiesWithCounts = specialtiesData.map((s: any) => ({
          ...s,
          doctorCount: s.doctor_count,
        }));
        
        setSpecialties(specialtiesWithCounts);

        // Set doctor counts for each specialty
        const specialtyCounts: { [key: string]: number } = {};
        specialtiesWithCounts.forEach((s: any) => {
          specialtyCounts[s.specialty_id] = s.doctor_count;
        });
        setDoctorCountBySpecialty(specialtyCounts);


        // Fetch top doctors for each specialty
        const topDoctorsPromises = specialtiesData.map(async (specialty: any) => {
          const { data: topDoctorsData, error: topDoctorsError } = await supabase
            .from("doctors")
            .select("doctor_id, fullname, community_rating, image_path, hospital")
            .eq("country_id", selectedCountryId)
            .eq("specialty_id", specialty.specialty_id)
            .order("community_rating", { ascending: false })
            .limit(3);

          if (topDoctorsError) {
            console.error(`[${timestamp}] STANDINGS PAGE: Error fetching top doctors for specialty ${specialty.specialty_id}:`, topDoctorsError);
            return { specialtyId: specialty.specialty_id, doctors: [] };
          }
          return { specialtyId: specialty.specialty_id, doctors: topDoctorsData || [] };
        });

        const topDoctorsResults = await Promise.all(topDoctorsPromises);

        const topDoctorsMap = topDoctorsResults.reduce((acc: any, result: any) => {
          acc[result.specialtyId] = result.doctors;
          return acc;
        }, {});
        
        const doctorCountMap = specialtiesData.reduce((acc: any, specialty: any) => {
          acc[specialty.specialty_id] = specialty.doctor_count;
          return acc;
        }, {});

        setTopDoctorsBySpecialty(topDoctorsMap);
        setDoctorCountBySpecialty(doctorCountMap);

      } catch (error: any) {
        const timestamp = new Date().toISOString();
        console.error(`[${timestamp}] STANDINGS PAGE: Error fetching standings data:`, JSON.stringify(error, null, 2));
        setLogMessages(prev => [...prev, `Error: ${error.message}`]);
        // Set fallback data on error
        setSpecialties(FALLBACK_SPECIALTIES);
        setDoctorCountBySpecialty({});
        setTopDoctorsBySpecialty({});
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [selectedCountryId]); // Re-run fetch when selectedCountryId changes

  // Safely pass addLog to specialty cards
  const logSpecialtyInfo = useCallback((message: string) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] STANDINGS PAGE: ${message}`);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-background/95 relative px-4">
      {/* Background medical crosses pattern */}
      <div className="absolute inset-0 overflow-hidden opacity-5 pointer-events-none">
        <div 
          className="absolute inset-0 w-full h-full"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M30 10v40M10 30h40' stroke='%2322C55E' strokeWidth='1'/%3E%3C/svg%3E")`,
            backgroundSize: '60px 60px',
          }}
        />
      </div>

      <div className="container mx-auto pb-20 pt-8 relative">
        {/* Floating Side Ads */}
        {sideLeftAds.length > 0 && (
          <PositionedAdDisplay
            ads={sideLeftAds}
            position={{ left: '20px', top: '150px' }}
            variant="floating"
            showMultiple={true}
            maxWidth={160}
          />
        )}
        {sideRightAds.length > 0 && (
          <PositionedAdDisplay
            ads={sideRightAds}
            position={{ right: '20px', top: '150px' }}
            variant="floating"
            showMultiple={true}
            maxWidth={160}
          />
        )}

        <div className="flex items-center justify-center mb-6">
          <div className="inline-flex items-center justify-center p-3 bg-primary/20 rounded-full backdrop-blur-sm">
            <Trophy className="h-10 w-10 text-foreground" />
          </div>
        </div>

        <h1 className="text-4xl md:text-5xl font-bold text-center mb-2 standings-title">
          Doctors Leagues Standings
        </h1>
        
        <div className="w-24 h-1 bg-gradient-to-r from-primary/50 to-primary mx-auto mb-8 rounded-full"></div>

        <ECGDivider className="my-10" />

        {/* Country Selector */}
        <div className="mb-8 flex justify-center">
          <div className="w-full max-w-xs">
            <Select value={selectedCountryId} onValueChange={handleCountryChange}>
              <SelectTrigger className="w-full bg-background/30 border-primary/30 text-foreground focus:ring-primary">
                <SelectValue placeholder="Select Country" />
              </SelectTrigger>
              <SelectContent className="bg-background border-primary/50 text-foreground">
                {countries.length > 0 ? (
                  countries.map((country) => (
                    <SelectItem 
                      key={country.country_id} 
                      value={String(country.country_id)}
                      className="hover:bg-primary/20 focus:bg-primary/30"
                    >
                      {country.country_name}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="loading" disabled>Loading countries...</SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Banner Ad Slot */}
        {bannerAds.length > 0 && (
          <div className="mb-10">
            <PositionedAdDisplay
              ads={bannerAds}
              position={{}}
              variant="banner"
              showMultiple={false}
              maxWidth={728}
            />
          </div>
        )}
        {/* End Banner Ad Slot */}

        {/* Main content with sidebar layout */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Main content area */}
          <div className="flex-1">
            {loading ? (
              <Card className="bg-background/30 border-primary/20 backdrop-blur-sm shadow-lg p-6">
                <div className="flex flex-col items-center justify-center py-20">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                  <p className="mt-4 text-foreground/80">Loading standings data...</p>
                </div>
              </Card>
            ) : (
              <div>
                <div className="mb-12">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-bold flex items-center gap-2 text-foreground">
                      <Trophy className="w-6 h-6 text-yellow-500" />
                      Medical Specialties
                    </h2>
                    <Badge variant="outline" className="bg-primary/10 text-foreground border-primary/30 py-1.5 px-3">
                      {specialties.length} Specialties
                    </Badge>
                  </div>

                  {specialties.length === 0 ? (
                    <Card className="bg-background/30 border-primary/20 backdrop-blur-sm shadow-lg">
                      <CardContent className="flex flex-col items-center justify-center py-10">
                        <Info className="w-10 h-10 text-amber-400 mb-4" />
                        <p className="text-center text-amber-400">
                          No specialties data available. Please check back later.
                        </p>
                      </CardContent>
                    </Card>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {specialties.map((specialty, index) => {
                        const specialtyId = specialty.specialty_id.toString();
                        const doctors = topDoctorsBySpecialty[specialtyId] || [];
                        const doctorCount = doctorCountBySpecialty[specialtyId] || 0;

                        return (
                          <SpecialtyCard
                            key={specialty.specialty_id || index}
                            specialty={specialty}
                            position={index + 1}
                            doctorCount={doctorCount}
                            topDoctors={doctors}
                            specialtyId={specialty.specialty_id}
                            selectedCountryId={selectedCountryId} 
                          />
                        );
                      })}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          {/* Conditionally render sidebar container only if there are sidebar ads */}
          {sidebarAds.length > 0 && (
            <div className="lg:w-80 w-full mt-8 lg:mt-0">
              <PositionedAdDisplay
                ads={sidebarAds}
                position={{}}
                variant="sidebar"
                showMultiple={true}
                maxWidth={300}
              />
            </div>
          )}
        </div>

        {/* In-Content Ad Slot (After Grid) */}
        {inContentAds.length > 0 && !loading && (
          <div className="my-10">
            <PositionedAdDisplay
              ads={inContentAds}
              position={{}}
              variant="banner"
              showMultiple={true}
              maxWidth={728}
            />
          </div>
        )}

        {/* Bottom Ad Slot (Fixed) */}
        {bottomAds.length > 0 && (
          <PositionedAdDisplay
            ads={bottomAds}
            position={{ bottom: '20px', left: '50%', transform: 'translateX(-50%)' }}
            variant="floating"
            showMultiple={false}
            maxWidth={728}
          />
        )}
      </div>
    </div>
  );
}
