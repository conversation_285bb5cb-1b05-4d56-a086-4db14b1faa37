import { createClient } from '@supabase/supabase-js';
import { notFound } from 'next/navigation';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Mail, Phone, MapPin, Globe, Star, Stethoscope, HeartPulse, Instagram, Map } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "https://uapbzzscckhtptliynyj.supabase.co";
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q";
const supabase = createClient(supabaseUrl, supabaseKey);

interface HospitalPageProps {
  params: {
    id: string;
  };
}

export default async function HospitalPage({ params }: HospitalPageProps) {
  const { data: hospital, error } = await supabase
    .from('hospitals')
    .select('*, countries(country_name)')
    .eq('hospital_id', params.id)
    .single();

  if (error || !hospital) {
    notFound();
  }

  const addressParts = hospital.address
    ? [hospital.address]
    : [hospital.city, hospital.countries?.country_name].filter(Boolean);
  const fullAddress = addressParts.join(', ');

  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-background/95 to-primary/5 text-foreground">
      <div className="container mx-auto px-4 py-16">
        <Card className="bg-gradient-to-b from-background/80 to-background/60 border border-primary/20 shadow-lg" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem' }}>
          <CardHeader className="text-center border-b border-primary/20 pb-4">
            <h1 className="text-4xl md:text-5xl font-bold mb-2 dark:!text-white" style={{ color: 'hsl(142, 76%, 25%)' }}>
              {hospital.hospital_name}
            </h1>
            <div className="flex justify-center items-center text-yellow-500">
              {Array.from({ length: 5 }).map((_, i) => (
                <Star key={i} className={i < Math.round(hospital.rating || 0) ? "fill-current" : "stroke-current"} />
              ))}
              <span className="ml-2 text-lg font-semibold text-foreground/80">{(hospital.rating || 0).toFixed(1)} ({hospital.review_count || 0} reviews)</span>
            </div>
          </CardHeader>
          <CardContent className="p-6 grid md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <h2 className="text-2xl font-semibold text-primary flex items-center"><Stethoscope className="mr-3" /> About</h2>
              <p className="text-foreground/80">{hospital.description || 'No description available.'}</p>

              <h2 className="text-2xl font-semibold text-primary flex items-center"><HeartPulse className="mr-3" /> Departments</h2>
              <div className="flex flex-wrap gap-2">
                {(hospital.departments && hospital.departments.length > 0 ? hospital.departments : 'N/A').split(',').map((dept: string) => (
                  <Badge key={dept.trim()} variant="secondary">{dept.trim()}</Badge>
                ))}
              </div>
            </div>
            <div className="space-y-6">
              <h2 className="text-2xl font-semibold text-primary flex items-center"><MapPin className="mr-3" /> Contact & Location</h2>
              <div className="space-y-3 text-foreground/80">
                {fullAddress && <p className="flex items-center"><MapPin className="w-5 h-5 mr-3 text-primary" /> {fullAddress}</p>}
                {hospital.telephone_info && <p className="flex items-center"><Phone className="w-5 h-5 mr-3 text-primary" /> {hospital.telephone_info}</p>}
                {hospital.email_info && <p className="flex items-center"><Mail className="w-5 h-5 mr-3 text-primary" /> <a href={`mailto:${hospital.email_info}`} className="hover:underline">{hospital.email_info}</a></p>}
                
                {hospital.Website && !hospital.Website.includes('instagram.com') && <p className="flex items-center"><Globe className="w-5 h-5 mr-3 text-primary" /> <a href={hospital.Website} target="_blank" rel="noopener noreferrer" className="hover:underline">Website</a></p>}
                {hospital['Google Map URL'] && <p className="flex items-center"><Map className="w-5 h-5 mr-3 text-primary" /> <a href={hospital['Google Map URL']} target="_blank" rel="noopener noreferrer" className="hover:underline">View on Google Maps</a></p>}
                {hospital.Instagram && <p className="flex items-center"><Instagram className="w-5 h-5 mr-3 text-primary" /> <a href={hospital.Instagram} target="_blank" rel="noopener noreferrer" className="hover:underline">Instagram</a></p>}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 