"use client"

import { PageAds } from "@/components/ads/page-ads"

export function TeamsBannerAd() {
  return (
    <div className="flex justify-center my-12">
      <PageAds 
        pageName="teams" 
        positions={['banner']} 
        showTestAds={false}
      />
    </div>
  )
}

export function TeamsSidebarAd() {
  return (
    <div className="lg:w-80 w-full mt-12">
      <PageAds 
        pageName="teams" 
        positions={['sidebar']} 
        showTestAds={false}
      />
    </div>
  )
}

export function TeamsSideAds() {
  return (
    <div className="hidden md:block">
      <PageAds 
        pageName="teams" 
        positions={['side-left', 'side-right']} 
        showTestAds={false}
      />
    </div>
  )
}

export function TeamsBottomAd() {
  return (
    <div className="flex justify-center my-12">
      <PageAds 
        pageName="teams" 
        positions={['bottom']} 
        showTestAds={false}
      />
    </div>
  )
}
