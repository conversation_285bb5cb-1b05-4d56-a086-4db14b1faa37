// This is a Server Component
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card"
import { Trophy, Users, Activity, Search, Hospital } from "lucide-react"
import { ECGDivider } from "@/components/ecg-divider"
import { Suspense } from "react"
import TeamSearchSection from "./team-search-section"
// Import client components for ads
import { TeamsBannerAd, TeamsSidebarAd, TeamsSideAds, TeamsBottomAd } from "./page-ads"

export const dynamic = "force-dynamic"

export default async function TeamsPage() {
  // No need to fetch ads here - the client components will handle that

  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-background/95 to-primary/5 text-foreground">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 dark:!text-white" style={{ color: 'hsl(142, 76%, 25%)' }}>
            Medical Teams
          </h1>
          <p className="text-foreground/70 max-w-3xl mx-auto">
            Discover the most prestigious medical institutions and their world-class healthcare teams.
            These institutions compete at the highest level of medical excellence and patient care.
          </p>
        </div>

        {/* Feature cards - centered layout with consistent sizing */}
        <div className="flex flex-wrap justify-center gap-6 mb-12 max-w-6xl mx-auto">
          <Card className="bg-gradient-to-b from-background/80 to-background/60 border border-primary/20 hover:border-primary/40 transition-all duration-300 hover:shadow-lg flex-1 min-w-[250px] max-w-[300px]" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
            <CardHeader className="pb-2">
              <CardTitle className="text-foreground flex items-center gap-2">
                <Hospital className="h-5 w-5 text-primary" />
                Top Institutions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-foreground/70">Featuring the most advanced medical facilities and cutting-edge technology.</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-b from-background/80 to-background/60 border border-primary/20 hover:border-primary/40 transition-all duration-300 hover:shadow-lg flex-1 min-w-[250px] max-w-[300px]" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
            <CardHeader className="pb-2">
              <CardTitle className="text-foreground flex items-center gap-2">
                <Users className="h-5 w-5 text-yellow-500" />
                Elite Teams
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-foreground/70">Teams composed of medical specialists who deliver exceptional collaborative care.</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-b from-background/80 to-background/60 border border-primary/20 hover:border-primary/40 transition-all duration-300 hover:shadow-lg flex-1 min-w-[250px] max-w-[300px]" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
            <CardHeader className="pb-2">
              <CardTitle className="text-foreground flex items-center gap-2">
                <Trophy className="h-5 w-5 text-primary" />
                Award Winning
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-foreground/70">Recognized for outstanding achievements in research and patient outcomes.</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-b from-background/80 to-background/60 border border-primary/20 hover:border-primary/40 transition-all duration-300 hover:shadow-lg flex-1 min-w-[250px] max-w-[300px]" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
            <CardHeader className="pb-2">
              <CardTitle className="text-foreground flex items-center gap-2">
                <Activity className="h-5 w-5 text-green-500" />
                Performance Metrics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-foreground/70">Compare teams based on patient recovery rates and treatment success.</p>
            </CardContent>
          </Card>
        </div>

        {/* ECG divider with minimal bottom margin */}
        <div className="-mb-8">
          <ECGDivider />
        </div>

        {/* Banner Ad Slot - centered */}
        <div className="flex justify-center mt-1 mb-2">
          <TeamsBannerAd />
        </div>
        {/* End Banner Ad Slot */}

        {/* Main content with centralized layout */}
        <div className="max-w-6xl mx-auto">
          <div className="mt-0">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Search className="h-6 w-6 text-primary" />
              <h2 className="text-2xl font-bold text-foreground text-center">Healthcare Institutions</h2>
            </div>

            <Suspense fallback={<div className="text-center py-6"><p className="text-foreground/70">Loading medical teams...</p></div>}>
              <TeamSearchSection />
            </Suspense>
          </div>
        </div>

        {/* Side ads - moved outside the main content to not disrupt central alignment */}
        <div className="mt-8">
          <TeamsSideAds />
        </div>

        {/* Bottom Ad - centered */}
        <div className="flex justify-center mt-12">
          <TeamsBottomAd />
        </div>
      </div>
    </div>
  )
}
