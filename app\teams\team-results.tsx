"use client"

import { useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Building, MapPin, Globe2 } from "lucide-react"
// Change the import from supabase-queries to hybrid-data-service
import { getHospitals } from "@/lib/hybrid-data-service"

interface Hospital {
  id: number
  name: string
  country: string
  city: string
  address: string
}

export default function TeamResults() {
  const searchParams = useSearchParams()
  const [hospitals, setHospitals] = useState<Hospital[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchHospitals = async () => {
      setLoading(true)
      setError(null)
      const name = searchParams.get("name") || undefined
      const country = searchParams.get("country") || undefined
      const page = searchParams.get("page") ? Number.parseInt(searchParams.get("page")!) : 1

      const { hospitals, error } = await getHospitals({ name, country, page })

      if (error) {
        setError(error)
      } else {
        setHospitals(hospitals)
      }
      setLoading(false)
    }

    fetchHospitals()
  }, [searchParams])

  if (loading) {
    return <div>Loading hospitals...</div>
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <Building className="mx-auto h-12 w-12 text-muted-green" />
        <h3 className="mt-2 text-sm font-semibold text-green-900">Error</h3>
        <p className="mt-1 text-sm text-muted-green">{error}</p>
      </div>
    )
  }

  if (hospitals.length === 0) {
    return (
      <div className="text-center py-8">
        <Building className="mx-auto h-12 w-12 text-muted-green" />
        <h3 className="mt-2 text-sm font-semibold text-green-900">No hospitals found</h3>
        <p className="mt-1 text-sm text-muted-green">Try adjusting your search criteria</p>
      </div>
    )
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {hospitals.map((hospital) => (
        <Card key={hospital.id} className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5 text-primary" />
              {hospital.name}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Globe2 className="h-4 w-4 text-primary" />
                <span>{hospital.country}</span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-primary" />
                <span>
                  {hospital.city}, {hospital.address}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

