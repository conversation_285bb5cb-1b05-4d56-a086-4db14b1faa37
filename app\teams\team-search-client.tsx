"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"
import { Hospital, MapPin, Users, Search } from "lucide-react"

// This would be connected to a real database in a production app
const MOCK_TEAMS = [
  {
    id: 1,
    name: "Royal Hospital Cardiology Team",
    location: "Manama, Bahrain",
    specialty: "Cardiology",
    members: 12,
    image: "/teams/cardio-team.jpg"
  },
  {
    id: 2,
    name: "National Medical Center Neurology",
    location: "Dubai, UAE",
    specialty: "Neurology",
    members: 8,
    image: "/teams/neuro-team.jpg"
  },
  {
    id: 3,
    name: "Kingdom Hospital Oncology Division",
    location: "Riyadh, Saudi Arabia",
    specialty: "Oncology",
    members: 15,
    image: "/teams/onco-team.jpg"
  },
  {
    id: 4,
    name: "Gulf Medical Pediatrics Group",
    location: "Doha, Qatar",
    specialty: "Pediatrics",
    members: 10,
    image: "/teams/peds-team.jpg"
  }
];

export default function TeamSearchClient() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredTeams, setFilteredTeams] = useState(MOCK_TEAMS);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!searchTerm.trim()) {
      setFilteredTeams(MOCK_TEAMS);
      return;
    }
    
    const filtered = MOCK_TEAMS.filter(team => 
      team.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      team.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      team.specialty.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    setFilteredTeams(filtered);
  };

  return (
    <div>
      <Card className="bg-gradient-to-b from-background/80 to-background/60 border border-primary/20 mb-8">
        <CardContent className="p-6">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="search" className="text-foreground mb-1 block">Search Teams</Label>
                <Input
                  id="search"
                  placeholder="Hospital name, location, or specialty..."
                  className="bg-background/60 border-primary/30 text-foreground placeholder:text-foreground/50"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div className="md:self-end">
                <Button type="submit" className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70">
                  <Search className="h-4 w-4 mr-2" />
                  Find Teams
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {filteredTeams.length > 0 ? (
          filteredTeams.map((team, index) => (
            <motion.div
              key={team.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="flex overflow-hidden h-full bg-gradient-to-b from-background/80 to-background/60 border border-primary/20 hover:border-primary/40 transition-all duration-300 hover:shadow-lg">
                <div className="w-1/3 bg-gradient-to-br from-primary/30 to-primary/5 flex items-center justify-center p-4">
                  <Hospital className="h-12 w-12 text-primary/80" />
                </div>
                <CardContent className="w-2/3 p-4">
                  <h3 className="text-foreground font-semibold text-lg mb-1">{team.name}</h3>
                  
                  <div className="flex items-center text-sm text-foreground/70 mb-1">
                    <MapPin className="h-3 w-3 mr-1 text-primary/80" />
                    {team.location}
                  </div>
                  
                  <div className="text-sm text-foreground/70 mb-2">
                    <span className="text-primary">Specialty:</span> {team.specialty}
                  </div>
                  
                  <div className="flex items-center mt-2">
                    <Users className="h-4 w-4 text-foreground/50 mr-1" />
                    <span className="text-sm text-foreground/70">{team.members} Team Members</span>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))
        ) : (
          <div className="col-span-full text-center py-8">
            <p className="text-foreground/70">No teams found matching your search criteria. Try different keywords.</p>
          </div>
        )}
      </div>
    </div>
  );
} 