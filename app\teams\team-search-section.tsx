"use client";

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { motion } from "framer-motion"
import { Hospital, MapPin, Users, Search, Trophy, Medal, Building, Heart, Filter, Star } from "lucide-react"
import { createClient } from '@supabase/supabase-js'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
  CommandShortcut,
} from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import Link from 'next/link';

// Define types for hospitals and teams
interface Hospital {
  hospital_id: string | number
  hospital_name: string
  country_id: string | number
  city: string
  address?: string
  specialty?: string
  doctor_count?: number
  rating?: number
  description?: string
  specialties?: string
  countries?: {
    country_name: string
  }
}

// Display the hospitals in a sports-themed leaderboard style
const HospitalLeaderboard = ({ 
  hospitals,
  searchTerm,
  cityFilter
}: { 
  hospitals: Hospital[],
  searchTerm: string,
  cityFilter: string
}) => {
  if (hospitals.length === 0) {
    return (
      <div className="text-center py-10 text-foreground/60">
        <p>No medical teams found matching your criteria.</p>
      </div>
    );
  }

  // Sort hospitals by rating in descending order
  const sortedHospitals = [...hospitals].sort((a, b) => {
    // Use 0 as default if rating is undefined or null
    const ratingA = a.rating ?? 0;
    const ratingB = b.rating ?? 0;
    return ratingB - ratingA;
  });

  // Limit to top 10 hospitals for display
  const topHospitals = sortedHospitals.slice(0, 10);

  // Check if we're in search mode (determine if user is searching)
  const isSearchActive = searchTerm.trim() !== '' || cityFilter.trim() !== '';

  return (
    <div className="space-y-6 max-w-4xl mx-auto">
      <div className="bg-gradient-to-b from-background/80 to-background/60 rounded-xl p-4 shadow-lg overflow-hidden">
        <div className="flex items-center mb-4 text-foreground border-b border-primary/20 pb-2">
          <div className="w-10 text-center text-sm font-semibold">#</div>
          <div className="flex-1 font-semibold">Medical Team</div>
          <div className="w-24 text-center text-sm font-semibold">Rating</div>
          <div className="w-32 text-center text-sm font-semibold">Location</div>
          <div className="w-20 text-center text-sm font-semibold">Status</div>
        </div>
        
        <div className="space-y-2 max-h-[500px] overflow-y-auto pr-2">
          {topHospitals.map((hospital, index) => (
            <Link href={`/teams/${hospital.hospital_id}`} key={hospital.hospital_id}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className={`flex items-center p-3 rounded-lg ${index < 3 && !isSearchActive ? 'bg-gradient-to-r from-primary/10 to-background/30' : 'bg-background/20'} hover:bg-background/40 transition-colors cursor-pointer`}
              >
                <div className="w-10 flex justify-center">
                  {!isSearchActive && index === 0 && (
                    <div className="h-7 w-7 bg-yellow-500 rounded-full flex items-center justify-center shadow-md">
                      <Trophy className="h-4 w-4 text-foreground" />
                    </div>
                  )}
                  {!isSearchActive && index === 1 && (
                    <div className="h-7 w-7 bg-green-300 rounded-full flex items-center justify-center shadow-md">
                      <Medal className="h-4 w-4 text-muted-green" />
                    </div>
                  )}
                  {!isSearchActive && index === 2 && (
                    <div className="h-7 w-7 bg-amber-700 rounded-full flex items-center justify-center shadow-md">
                      <Medal className="h-4 w-4 text-foreground" />
                    </div>
                  )}
                  {(isSearchActive || index > 2) && (
                    <span className="text-sm font-bold text-foreground/80">{index + 1}</span>
                  )}
                </div>
                
                <div className="flex-1 ml-2">
                  <div className="text-foreground font-medium">{hospital.hospital_name}</div>
                </div>
                
                <div className="w-24 text-center">
                  <div className="inline-flex items-center px-2 py-1 bg-background/30 rounded text-yellow-500">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <span key={i} className={i < Math.round(hospital.rating || 0) ? "text-yellow-500" : "text-muted-green"}>★</span>
                    ))}
                    <span className="ml-1 text-sm">{(hospital.rating || 0).toFixed(1)}</span>
                  </div>
                </div>
                
                <div className="w-32 text-center text-sm text-foreground/80">
                  <div className="flex items-center justify-center">
                    <MapPin className="h-3 w-3 mr-1 text-primary" />
                    {hospital.city || 'N/A'}
                  </div>
                </div>
                
                <div className="w-20 text-center">
                  <div className="text-xs font-medium bg-green-500/20 text-green-500 py-1 px-2 rounded-full">
                    Active
                  </div>
                </div>
              </motion.div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default function TeamSearchSection() {
  const [searchTerm, setSearchTerm] = useState("");
  const [cityFilter, setCityFilter] = useState("");
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [hospitals, setHospitals] = useState<Hospital[]>([]);
  const [filteredHospitals, setFilteredHospitals] = useState<Hospital[]>([]);
  const [selectedCountry, setSelectedCountry] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [statsLoading, setStatsLoading] = useState(true);
  const [countryName, setCountryName] = useState<string>("");
  const [healthcareStats, setHealthcareStats] = useState({
    hospitalCount: 0,
    doctorCount: 0,
    specialtyCount: 0,
    reviewCount: 0
  });
  const [cities, setCities] = useState<string[]>([]);
  const [countries, setCountries] = useState<any[]>([]);

  // Create Supabase client - we'll use this for both auth and database access
  const supabaseUrl = "https://uapbzzscckhtptliynyj.supabase.co"
  const supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q"
  const supabase = createClient(supabaseUrl, supabaseKey, {
    auth: {
      persistSession: false
    }
  })

  console.log("TeamSearchSection component initialized")
  
  // Get stored country ID from localStorage when component mounts
  useEffect(() => {
    // First let's get all countries for selection
    const fetchCountries = async () => {
      console.log("Attempting to fetch countries list")
      const { data, error } = await supabase
        .from('countries')
        .select('*')
        .order('country_name')
      
      if (error) {
        console.error('Error fetching countries:', error)
        return
      }
      
      if (data && data.length) {
        console.log(`Successfully fetched ${data.length} countries`)
        setCountries(data)
        
        // If we have a stored country preference, use it
        const storedCountryId = localStorage.getItem('selectedCountryId')
        
        console.log("Stored country ID:", storedCountryId)
        
        if (storedCountryId) {
          const countryMatch = data.find(country => country.country_id === parseInt(storedCountryId))
          if (countryMatch) {
            console.log("Found stored country match:", countryMatch)
            setSelectedCountry(countryMatch.country_id)
            setCountryName(countryMatch.country_name)
            fetchHospitalsForCountry(parseInt(storedCountryId))
            fetchHealthcareStats(parseInt(storedCountryId))
            return
          }
        }
        
        // If no stored preference or not found, default to first country
        console.log("Using first country in list:", data[0])
        setSelectedCountry(data[0].country_id)
        setCountryName(data[0].country_name)
        fetchHospitalsForCountry(data[0].country_id)
        fetchHealthcareStats(data[0].country_id)
      } else {
        console.warn("No countries data returned from database")
      }
    }
    
    fetchCountries()
  }, [])
  
  const fetchHospitalsForCountry = async (countryId: number) => {
    setLoading(true)
    console.log(`Fetching hospitals for country ID: ${countryId}`)
    
    try {
      const { data: hospitalData, error } = await supabase
        .from('hospitals')
        .select(`
          *,
          countries(country_name)
        `)
        .eq('country_id', countryId)
      
      if (error) {
        console.error('Error fetching hospitals:', error)
        setLoading(false)
        return
      }
      
      if (hospitalData) {
        console.log(`Retrieved ${hospitalData.length} hospitals for country ID ${countryId}`)
        setHospitals(hospitalData)
        setFilteredHospitals(hospitalData)
        
        // Extract unique cities for filtering
        const uniqueCities = [...new Set(hospitalData.map(hospital => hospital.city))]
        setCities(uniqueCities.filter(Boolean).sort())
        console.log(`Extracted ${uniqueCities.length} unique cities`)
      } else {
        console.warn("No hospital data returned from database")
        setHospitals([])
        setFilteredHospitals([])
        setCities([])
      }
    } catch (err) {
      console.error('Unexpected error fetching hospitals:', err)
      setHospitals([])
      setFilteredHospitals([])
    }
    
    setLoading(false)
  }
  
  const fetchHealthcareStats = async (countryId: number) => {
    console.log(`Fetching healthcare stats for country ID: ${countryId}`)
    setStatsLoading(true) // Ensure we set loading state at the beginning
    
    try {
      // Hospitals count
      const { count: hospitalsCount, error: hospitalError } = await supabase
        .from('hospitals')
        .select('*', { count: 'exact', head: true })
        .eq('country_id', countryId)
      
      if (hospitalError) {
        console.error('Error fetching hospital count:', hospitalError)
      }
      
      // Doctors count
      const { count: doctorsCount, error: doctorsError } = await supabase
        .from('doctors')
        .select('*', { count: 'exact', head: true })
        .eq('country_id', countryId)
      
      if (doctorsError) {
        console.error('Error fetching doctors count:', doctorsError)
      }
      
      // Specialties count (all specialties, as they're not country-specific)
      const { count: specialtiesCount, error: specialtiesError } = await supabase
        .from('specialties')
        .select('*', { count: 'exact', head: true })
      
      if (specialtiesError) {
        console.error('Error fetching specialties count:', specialtiesError)
      }
      
      // Reviews count for doctors in this country
      // First, get doctor IDs from this country
      const { data: doctorsData, error: doctorsQueryError } = await supabase
        .from('doctors')
        .select('doctor_id')
        .eq('country_id', countryId)
        
      let reviewsCount = 0
      
      if (doctorsQueryError) {
        console.error('Error fetching doctor IDs for reviews count:', doctorsQueryError)
      } else if (doctorsData && doctorsData.length > 0) {
        const doctorIds = doctorsData.map(doc => doc.doctor_id)
        
        // Then count reviews for these doctors
        const { count: reviewCount, error: reviewsError } = await supabase
          .from('reviews')
          .select('review_id', { count: 'exact', head: true })
          .in('doctor_id', doctorIds)
          
        if (reviewsError) {
          console.error('Error fetching reviews count:', reviewsError)
        } else {
          reviewsCount = reviewCount || 0
        }
      }
      
      setHealthcareStats({
        hospitalCount: hospitalsCount || 0,
        doctorCount: doctorsCount || 0,
        specialtyCount: specialtiesCount || 0,
        reviewCount: reviewsCount
      })
      
      console.log(`Stats for country ${countryId}:`, {
        hospitals: hospitalsCount,
        doctors: doctorsCount,
        specialties: specialtiesCount,
        reviewCount: reviewsCount
      })
    } catch (err) {
      console.error('Error fetching healthcare stats:', err)
    } finally {
      setStatsLoading(false) // Make sure to set loading to false when done
    }
  }
  
  const handleCountryChange = (country: any) => {
    setSelectedCountry(country.country_id)
    setCountryName(country.country_name)
    setCityFilter('') // Reset city filter when country changes
    setSearchTerm('') // Reset search when country changes
    
    // Store selected country ID in localStorage
    localStorage.setItem('selectedCountryId', country.country_id.toString())
    console.log(`Stored country ID ${country.country_id} in localStorage`)
    
    // Fetch hospitals and stats for this country
    fetchHospitalsForCountry(country.country_id)
    fetchHealthcareStats(country.country_id)
  }
  
  // Filter hospitals based on search term and city filter
  useEffect(() => {
    if (!hospitals.length) return;
    
    const filtered = hospitals.filter(hospital => {
      const matchesSearch = searchTerm && hospital.hospital_name
        ? hospital.hospital_name.toLowerCase().includes(searchTerm.toLowerCase())
        : true;
      const matchesCity = cityFilter && hospital.city
        ? hospital.city.toLowerCase().includes(cityFilter.toLowerCase())
        : true;
      
      return matchesSearch && matchesCity;
    });
    
    setFilteredHospitals(filtered);
  }, [hospitals, searchTerm, cityFilter]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Already filtering in real-time with the useEffect above
  };

  // Sort hospitals by rating for the leaderboard
  const sortedHospitals = [...filteredHospitals]
    .sort((a, b) => (b.rating || 0) - (a.rating || 0))
    .slice(0, 10) // Only show top 10

  return (
    <div className="space-y-8">
      {/* Statistics Tiles - centered row with country name heading */}
      <div className="text-center mb-4">
        <h2 className="text-xl font-bold text-foreground flex items-center justify-center gap-2">
          <Trophy className="h-5 w-5 text-yellow-500" />
          {countryName ? `${countryName}` : "Loading..."} Healthcare Statistics
        </h2>
      </div>
      
      <div className="flex flex-wrap justify-center gap-4 mb-8">
        <Card className="bg-background/20 border border-primary/20 w-full sm:w-[calc(50%-0.5rem)] lg:w-[calc(25%-0.75rem)]">
          <CardContent className="p-4 flex items-center justify-between">
            <div>
              <p className="text-sm text-foreground/60">Total Teams</p>
              {statsLoading ? (
                <div className="h-8 w-16 bg-card animate-pulse rounded"></div>
              ) : (
                <p className="text-2xl font-bold text-foreground">{healthcareStats.hospitalCount}</p>
              )}
            </div>
            <Hospital className="h-6 w-6 text-primary" />
          </CardContent>
        </Card>
        
        <Card className="bg-background/20 border border-primary/20 w-full sm:w-[calc(50%-0.5rem)] lg:w-[calc(25%-0.75rem)]">
          <CardContent className="p-4 flex items-center justify-between">
            <div>
              <p className="text-sm text-foreground/60">Registered Doctors</p>
              {statsLoading ? (
                <div className="h-8 w-16 bg-card animate-pulse rounded"></div>
              ) : (
                <p className="text-2xl font-bold text-foreground">{healthcareStats.doctorCount}</p>
              )}
            </div>
            <Users className="h-6 w-6 text-yellow-500" />
          </CardContent>
        </Card>
        
        <Card className="bg-background/20 border border-primary/20 w-full sm:w-[calc(50%-0.5rem)] lg:w-[calc(25%-0.75rem)]">
          <CardContent className="p-4 flex items-center justify-between">
            <div>
              <p className="text-sm text-foreground/60">Specialties</p>
              {statsLoading ? (
                <div className="h-8 w-16 bg-card animate-pulse rounded"></div>
              ) : (
                <p className="text-2xl font-bold text-foreground">{healthcareStats.specialtyCount}</p>
              )}
            </div>
            <Heart className="h-6 w-6 text-red-500" />
          </CardContent>
        </Card>
        
        <Card className="bg-background/20 border border-primary/20 w-full sm:w-[calc(50%-0.5rem)] lg:w-[calc(25%-0.75rem)]">
          <CardContent className="p-4 flex items-center justify-between">
            <div>
              <p className="text-sm text-foreground/60">Total Reviews</p>
              {statsLoading ? (
                <div className="h-8 w-16 bg-card animate-pulse rounded"></div>
              ) : (
                <p className="text-2xl font-bold text-foreground">{healthcareStats.reviewCount}</p>
              )}
            </div>
            <Star className="h-6 w-6 text-yellow-500" />
          </CardContent>
        </Card>
      </div>

      {/* Search & Filter Form - centered */}
      <div className="max-w-4xl mx-auto">
        <form onSubmit={handleSearch} className="space-y-4">
          <div className="bg-gradient-to-r from-background/40 to-background/60 p-4 rounded-xl border border-primary/20">
            <div className="grid grid-cols-1 md:grid-cols-[1fr_auto] gap-4">
              <div className="flex-1">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name" className="text-foreground mb-1 block text-sm">Team Name</Label>
                    <div className="relative">
                      <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-primary" />
                      <Input
                        type="text"
                        id="name"
                        placeholder="Search medical teams..."
                        className="pl-8 bg-background/30 border-primary/30 text-foreground focus:border-primary focus:ring-primary"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="city" className="text-foreground mb-1 block text-sm">City</Label>
                    <div className="relative">
                      <MapPin className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-primary" />
                      <Input
                        type="text"
                        id="city"
                        placeholder="Filter by city..."
                        className="pl-8 bg-background/30 border-primary/30 text-foreground focus:border-primary focus:ring-primary"
                        value={cityFilter}
                        onChange={(e) => setCityFilter(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex items-end">
                <div className="flex gap-2">
                  <Button
                    type="submit"
                    className="bg-primary hover:bg-primary/90 text-foreground"
                  >
                    <Search className="h-4 w-4 mr-2" />
                    Search Teams
                  </Button>
                  
                  <Button
                    type="button"
                    variant="outline"
                    className="border-primary/50 text-primary hover:bg-primary/10"
                    onClick={() => {
                      setSearchTerm('')
                      setCityFilter('')
                      setFilteredHospitals(hospitals)
                    }}
                  >
                    Reset
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>

      {/* Country Selection - centered */}
      <div className="flex justify-center mb-8">
        <div className="inline-flex rounded-md border border-primary/20 overflow-hidden">
          {countries.slice(0, 5).map((country) => (
            <button
              key={country.country_id}
              onClick={() => handleCountryChange(country)}
              className={`px-4 py-2 text-sm font-medium ${
                selectedCountry === country.country_id 
                  ? 'bg-primary text-foreground' 
                  : 'bg-background/40 text-foreground/70 hover:bg-primary/10'
              }`}
            >
              <Globe className="inline-block h-4 w-4 mr-1 relative -top-px" />
              {country.country_name}
            </button>
          ))}
          
          {countries.length > 5 && (
            <Popover>
              <PopoverTrigger asChild>
                <button
                  className="px-4 py-2 text-sm font-medium bg-background/40 text-foreground/70 hover:bg-primary/10"
                >
                  <ChevronDown className="h-4 w-4" />
                </button>
              </PopoverTrigger>
              <PopoverContent className="p-0 bg-background/90 border border-primary/20 shadow-lg w-48">
                <Command className="bg-transparent">
                  <CommandInput placeholder="Search countries..." />
                  <CommandList>
                    <CommandEmpty>No country found.</CommandEmpty>
                    <CommandGroup className="max-h-60 overflow-auto">
                      {countries.slice(5).map((country) => (
                        <CommandItem
                          key={country.country_id}
                          onSelect={() => handleCountryChange(country)}
                          className="cursor-pointer"
                        >
                          {selectedCountry === country.country_id && <Check className="mr-2 h-4 w-4 text-primary" />}
                          <span>{country.country_name}</span>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          )}
        </div>
      </div>

      {/* Hospital Leaderboard */}
      <HospitalLeaderboard
        hospitals={filteredHospitals}
        searchTerm={searchTerm}
        cityFilter={cityFilter}
      />
    </div>
  )
}

// Additional components for cleaner code
function ChevronDown(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="m6 9 6 6 6-6" />
    </svg>
  )
}

function Check(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M20 6 9 17l-5-5" />
    </svg>
  )
}

function Globe(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <circle cx="12" cy="12" r="10" />
      <line x1="2" x2="22" y1="12" y2="12" />
      <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
    </svg>
  )
} 