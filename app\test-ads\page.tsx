"use client"

import { TestAdDisplay } from "@/components/ads/test-ad-display"
import { Button } from "@/components/ui/button"
import { useState } from "react"

export default function TestAdsPage() {
  const [showLeftAd, setShowLeftAd] = useState(true)
  const [showRightAd, setShowRightAd] = useState(true)
  const [showTopAd, setShowTopAd] = useState(true)
  const [showBottomAd, setShowBottomAd] = useState(true)

  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-background/95 to-primary/5 text-foreground">
      <div className="container mx-auto px-4 py-16">
        <h1 className="text-4xl font-bold text-center mb-8">Ad Placement Test Page</h1>
        
        <div className="max-w-3xl mx-auto bg-background/30 p-8 rounded-lg border border-primary/20">
          <p className="text-lg mb-6">
            This page demonstrates how ads can be placed in the empty spaces around the content without disrupting the layout.
            The ads are positioned absolutely and don't affect the flow of the content.
          </p>
          
          <div className="grid grid-cols-2 gap-4 mb-8">
            <Button 
              onClick={() => setShowLeftAd(!showLeftAd)}
              variant={showLeftAd ? "default" : "outline"}
              className="w-full"
            >
              {showLeftAd ? "Hide" : "Show"} Left Ad
            </Button>
            
            <Button 
              onClick={() => setShowRightAd(!showRightAd)}
              variant={showRightAd ? "default" : "outline"}
              className="w-full"
            >
              {showRightAd ? "Hide" : "Show"} Right Ad
            </Button>
            
            <Button 
              onClick={() => setShowTopAd(!showTopAd)}
              variant={showTopAd ? "default" : "outline"}
              className="w-full"
            >
              {showTopAd ? "Hide" : "Show"} Top Ad
            </Button>
            
            <Button 
              onClick={() => setShowBottomAd(!showBottomAd)}
              variant={showBottomAd ? "default" : "outline"}
              className="w-full"
            >
              {showBottomAd ? "Hide" : "Show"} Bottom Ad
            </Button>
          </div>
          
          <div className="space-y-4">
            <h2 className="text-2xl font-bold">Main Content</h2>
            <p>
              This is the main content of the page. It remains centered and maintains its layout regardless of whether ads are shown or not.
              The ads are positioned in the empty spaces around the content.
            </p>
            
            <div className="bg-background/20 p-4 rounded border border-border/50 my-8">
              <h3 className="text-xl font-semibold mb-2">Content Section</h3>
              <p>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget aliquam ultricies, 
                nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl. Nullam euismod, nisl eget aliquam ultricies,
                nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl.
              </p>
            </div>
            
            <div className="bg-background/20 p-4 rounded border border-border/50 my-8">
              <h3 className="text-xl font-semibold mb-2">Another Section</h3>
              <p>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget aliquam ultricies, 
                nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl. Nullam euismod, nisl eget aliquam ultricies,
                nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl.
              </p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Test Ads */}
      {showLeftAd && (
        <TestAdDisplay 
          position={{ left: '20px', top: '200px' }}
          title="Left Side Ad"
          color="green"
        />
      )}
      
      {showRightAd && (
        <TestAdDisplay 
          position={{ right: '20px', top: '200px' }}
          title="Right Side Ad"
          color="blue"
        />
      )}
      
      {showTopAd && (
        <TestAdDisplay 
          position={{ top: '20px', left: '50%', transform: 'translateX(-50%)' }}
          title="Top Banner Ad"
          width={600}
          height={100}
          color="purple"
        />
      )}
      
      {showBottomAd && (
        <TestAdDisplay 
          position={{ bottom: '20px', left: '50%', transform: 'translateX(-50%)' }}
          title="Bottom Banner Ad"
          width={600}
          height={100}
          color="red"
        />
      )}
    </div>
  )
}
