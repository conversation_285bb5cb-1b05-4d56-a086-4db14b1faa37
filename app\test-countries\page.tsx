'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { createClient } from '@supabase/supabase-js'

// Define the Country interface
interface Country {
  country_id: number;
  country_name: string;
  flag_url?: string | null;
}

export default function TestCountriesPage() {
  const [countries, setCountries] = useState<Country[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Function to fetch countries directly from the database
  const fetchCountries = async () => {
    try {
      setLoading(true)
      
      // Create a direct Supabase client with the service role key
      const supabase = createClient(
        'https://uapbzzscckhtptliynyj.supabase.co',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q'
      )
      
      // Log connection attempt
      console.log('Connecting to Supabase with service role key...')
      
      // Fetch countries from the database
      const { data, error } = await supabase
        .from('countries')
        .select('*')
        .order('country_name')
      
      if (error) {
        console.error('Error fetching countries:', error)
        setError(`Database error: ${error.message}`)
        return
      }
      
      // If no countries were found
      if (!data || data.length === 0) {
        console.warn('No countries found in the database')
        setError('No countries found in the database')
        return
      }
      
      // Add flag URLs to countries
      const flagUrls: Record<string, string> = {
        'Bahrain': 'https://flagcdn.com/w320/bh.png',
        'Kuwait': 'https://flagcdn.com/w320/kw.png',
        'Oman': 'https://flagcdn.com/w320/om.png',
        'Qatar': 'https://flagcdn.com/w320/qa.png',
        'Saudi Arabia': 'https://flagcdn.com/w320/sa.png',
        'UAE': 'https://flagcdn.com/w320/ae.png',
        'USA': 'https://flagcdn.com/w320/us.png'
      }
      
      const countriesWithFlags = data.map((country: any) => ({
        ...country,
        flag_url: flagUrls[country.country_name] || null
      }))
      
      // Successfully fetched countries
      console.log(`Successfully fetched ${countriesWithFlags.length} countries from database`)
      setCountries(countriesWithFlags)
    } catch (err: any) {
      console.error('Unexpected error:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  // Fetch countries on component mount
  useEffect(() => {
    fetchCountries()
  }, [])

  return (
    <div className="container mx-auto pt-20 pb-10">
      <h1 className="text-3xl font-bold mb-4">Test Countries Page</h1>
      <p className="text-muted-green mb-8">This page fetches countries directly from the database using the service role key.</p>
      
      {/* Status section */}
      <div className="bg-green-100 p-4 mb-6 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">Status:</h2>
        <p className="text-sm">
          {loading ? '⏳ Loading countries...' : 
           error ? `❌ Error: ${error}` : 
           `✅ Successfully loaded ${countries.length} countries`}
        </p>
      </div>
      
      {/* Countries dropdown */}
      <div className="mb-8">
        <h2 className="text-xl font-bold mb-4">Countries Dropdown:</h2>
        <div className="relative w-full md:w-72">
          <select 
            className="w-full p-2 border rounded shadow-sm appearance-none bg-white"
            disabled={loading || !!error}
          >
            <option value="">Select a country</option>
            {countries.map(country => (
              <option key={country.country_id} value={country.country_id}>
                {country.country_name}
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
            <svg className="w-4 h-4 text-muted-green" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>
      </div>
      
      {/* Countries list with links */}
      <div>
        <h2 className="text-xl font-bold mb-4">Countries List:</h2>
        {loading ? (
          <div className="animate-pulse">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 bg-green-200 mb-2 rounded"></div>
            ))}
          </div>
        ) : error ? (
          <div className="text-red-500 p-4 border border-red-300 rounded">
            {error}
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {countries.map(country => (
              <Link 
                href={`/divisions/${country.country_id}`}
                key={country.country_id}
                className="flex items-center p-3 border rounded hover:bg-blue-50 transition-colors"
              >
                <div className="w-10 h-6 overflow-hidden rounded-md mr-3 border">
                  {country.flag_url ? (
                    <img 
                      src={country.flag_url} 
                      alt={`${country.country_name} Flag`}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="bg-blue-100 w-full h-full flex items-center justify-center">
                      <span className="text-xs font-bold text-blue-500">
                        {country.country_name.substring(0, 2).toUpperCase()}
                      </span>
                    </div>
                  )}
                </div>
                <span>{country.country_name}</span>
              </Link>
            ))}
          </div>
        )}
      </div>
    </div>
  )
} 