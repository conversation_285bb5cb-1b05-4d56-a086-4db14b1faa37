"use client"

import { useState, useEffect } from "react"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { DashboardReturnButton } from "@/components/dashboard-return-button"

export default function TestDashboardButton() {
  const [authStatus, setAuthStatus] = useState<string>("Checking authentication...")
  const [userInfo, setUserInfo] = useState<any>(null)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const router = useRouter()
  
  // Check authentication on load
  useEffect(() => {
    try {
      checkAuth()
    } catch (err) {
      setAuthStatus("Error during authentication check")
      setErrorMessage(err instanceof Error ? err.message : String(err))
    }
  }, [])
  
  async function checkAuth() {
    try {
      const supabase = createClientComponentClient()
      const { data, error } = await supabase.auth.getSession()
      
      if (error) {
        setAuthStatus(`Authentication error: ${error.message}`)
        setErrorMessage(error.message)
        return
      }
      
      const session = data?.session
      
      if (!session) {
        setAuthStatus("No session found. You are not logged in.")
        return
      }
      
      setAuthStatus(`Authenticated as ${session.user.email}`)
      
      // Get user details
      try {
        // Don't use .single() to avoid the multiple rows returned error
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('auth_id', session.user.id)
        
        if (userError) {
          const errorMsg = userError.message || userError.code || "Unknown error"
          setAuthStatus(`Error fetching user details: ${errorMsg}`)
          setErrorMessage(errorMsg)
          return
        }
        
        if (!userData || userData.length === 0) {
          setAuthStatus("No user data found for this authenticated user")
          return
        }
        
        // Use the first user if multiple are returned
        const user = userData[0]
        setUserInfo(user)
        setAuthStatus(`Logged in as ${user.first_name} ${user.last_name} (${user.user_type})`)
      } catch (userErr) {
        const errorMsg = userErr instanceof Error ? userErr.message : String(userErr)
        setAuthStatus(`Unexpected error during user fetch: ${errorMsg}`)
        setErrorMessage(errorMsg)
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : String(err)
      setAuthStatus(`Unexpected error: ${errorMsg}`)
      setErrorMessage(errorMsg)
    }
  }
  
  const handleLogin = () => {
    router.push("/patient/login")
  }
  
  const handleDashboard = () => {
    router.push("/patient/dashboard")
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-background p-8">
      {/* This shows our component directly on the test page for visibility */}
      <DashboardReturnButton />
      
      <Card className="max-w-2xl mx-auto p-6 bg-gradient-to-b from-background/90 to-background border-border">
        <h1 className="text-2xl font-bold text-foreground mb-4">Dashboard Button Test Page</h1>
        
        <div className="bg-background/50 rounded p-4 mb-6 border border-border">
          <h2 className="text-lg font-medium text-foreground mb-2">Authentication Status:</h2>
          <p className="text-foreground/90">{authStatus}</p>
          
          {errorMessage && (
            <div className="mt-4 bg-red-900/30 p-3 rounded border border-red-800">
              <h3 className="text-md font-medium text-red-400 mb-1">Error Details:</h3>
              <p className="text-foreground/80">{errorMessage}</p>
            </div>
          )}
          
          {userInfo && (
            <div className="mt-4 border-t border-border pt-4">
              <h3 className="text-md font-medium text-foreground mb-2">User Details:</h3>
              <ul className="text-foreground/80 space-y-1">
                <li><span className="text-primary">ID:</span> {userInfo.user_id}</li>
                <li><span className="text-primary">Type:</span> {userInfo.user_type}</li>
                <li><span className="text-primary">Name:</span> {userInfo.first_name} {userInfo.last_name}</li>
                <li><span className="text-primary">Username:</span> {userInfo.username}</li>
                <li><span className="text-primary">Email:</span> {userInfo.email}</li>
              </ul>
            </div>
          )}
        </div>
        
        <div className="bg-blue-900/20 rounded p-4 mb-6 border border-blue-700">
          <h2 className="text-lg font-medium text-foreground mb-2">Instructions:</h2>
          <ol className="text-foreground/90 list-decimal pl-5 space-y-2">
            <li>If you're not logged in as a patient, use the "Login as Patient" button below.</li>
            <li>Once logged in, the "Return to Dashboard" button should appear at the top left of the screen.</li>
            <li>We've also added the button component directly to this page for testing.</li>
            <li>If you don't see the button, check the error information above.</li>
          </ol>
        </div>
        
        <div className="flex gap-4">
          <Button 
            onClick={handleLogin}
            className="bg-primary hover:bg-primary/90"
          >
            Login as Patient
          </Button>
          
          <Button 
            onClick={handleDashboard}
            className="bg-green-600 hover:bg-green-700"
          >
            Go to Dashboard
          </Button>
          
          <Button 
            onClick={() => checkAuth()}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Refresh Auth Status
          </Button>
        </div>
      </Card>
    </div>
  )
} 