"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"

export default function TestDatabaseFixPage() {
  return (
    <div className="container mx-auto py-12 px-4">
      <h1 className="text-3xl font-bold mb-6">Database Connection Test Mode</h1>
      
      <div className="max-w-2xl mx-auto bg-background/80 rounded-lg p-6 border border-primary/30">
        <p className="mb-4">
          This page allows you to test the database connection fix without modifying the original application code.
        </p>
        
        <p className="mb-4">
          A status indicator will appear in the bottom-right corner of each page showing the connection status.
          A detailed test report will appear in the top-right corner on pages we're specifically testing.
        </p>
        
        <h2 className="text-xl font-semibold mt-6 mb-3">Test Pages:</h2>
        
        <div className="space-y-3">
          <div>
            <Link href="/" className="text-primary hover:underline font-medium">Home Page</Link>
            <p className="text-sm text-foreground/70">Tests featured doctors and top doctors fetch.</p>
          </div>
          
          <div>
            <Link href="/standings" className="text-primary hover:underline font-medium">Standings Page</Link>
            <p className="text-sm text-foreground/70">Tests specialties and doctors by specialty fetch.</p>
          </div>
          
          <div>
            <Link href="/head-to-head" className="text-primary hover:underline font-medium">Head-to-Head Page</Link>
            <p className="text-sm text-foreground/70">Tests doctors data fetch.</p>
          </div>
          
          <div>
            <Link href="/teams" className="text-primary hover:underline font-medium">Teams Page</Link>
            <p className="text-sm text-foreground/70">Tests hospitals data fetch.</p>
          </div>
          
          <div>
            <Link href="/divisions/1" className="text-primary hover:underline font-medium">Leagues Page</Link>
            <p className="text-sm text-foreground/70">This page was already working as it used direct client creation.</p>
          </div>
        </div>

        <h2 className="text-xl font-semibold mt-8 mb-3">Connection Status Indicators:</h2>
        
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span>Green: Database connected, using real data</span>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <span>Red: Database not connected, using fallback data</span>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-background/60"></div>
            <span>Gray: Loading, checking connection</span>
          </div>
        </div>
        
        <div className="mt-8 text-center">
          <Link href="/">
            <Button variant="outline" className="bg-primary/10 hover:bg-primary/20 border-primary/30">
              Start Testing
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
} 