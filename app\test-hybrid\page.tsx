import { getCountries, getSpecialties, getCountryById } from "@/lib/hybrid-data-service"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default async function TestHybridPage() {
  // Test the hybrid data service
  const countries = await getCountries()
  const specialties = await getSpecialties()
  const bahrain = await getCountryById("1")

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8 text-foreground">Hybrid Data Test</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Countries (Hybrid)</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5">
              {countries.map((country) => (
                <li key={country.country_id}>{country.country_name}</li>
              ))}
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Specialties (Hybrid)</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5">
              {specialties.map((specialty) => (
                <li key={specialty.specialty_id}>{specialty.specialty_name}</li>
              ))}
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Country Detail (Hybrid)</CardTitle>
          </CardHeader>
          <CardContent>
            {bahrain ? (
              <div>
                <p>
                  <strong>ID:</strong> {bahrain.country_id}
                </p>
                <p>
                  <strong>Name:</strong> {bahrain.country_name}
                </p>
              </div>
            ) : (
              <p>Country not found</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

