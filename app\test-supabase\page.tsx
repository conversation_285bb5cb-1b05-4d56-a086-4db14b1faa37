import { supabase } from "@/lib/supabase-client"

export default async function TestSupabasePage() {
  try {
    // Test the Supabase connection
    const { data: countries, error } = await supabase.from("countries").select("*")

    if (error) {
      throw error
    }

    return (
      <div className="container mx-auto p-8">
        <h1 className="text-2xl font-bold mb-4">Supabase Connection Test</h1>
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          Connection successful! Found {countries.length} countries.
        </div>

        <h2 className="text-xl font-semibold mb-2">Countries from Supabase:</h2>
        <ul className="list-disc pl-5">
          {countries.map((country) => (
            <li key={country.country_id} className="mb-1">
              {country.country_name}
            </li>
          ))}
        </ul>

        <div className="mt-4 p-4 bg-green-100 rounded">
          <h3 className="font-semibold mb-2">Environment Variables:</h3>
          <p>NEXT_PUBLIC_SUPABASE_URL: {process.env.NEXT_PUBLIC_SUPABASE_URL ? "✅ Set" : "❌ Not set"}</p>
          <p>NEXT_PUBLIC_SUPABASE_ANON_KEY: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? "✅ Set" : "❌ Not set"}</p>
        </div>
      </div>
    )
  } catch (error) {
    console.error("Error in TestSupabasePage:", error)
    return (
      <div className="container mx-auto p-8">
        <h1 className="text-2xl font-bold mb-4">Supabase Connection Test</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          Connection failed: {error instanceof Error ? error.message : String(error)}
        </div>

        <div className="mt-4 p-4 bg-green-100 rounded">
          <h3 className="font-semibold mb-2">Environment Variables:</h3>
          <p>NEXT_PUBLIC_SUPABASE_URL: {process.env.NEXT_PUBLIC_SUPABASE_URL ? "✅ Set" : "❌ Not set"}</p>
          <p>NEXT_PUBLIC_SUPABASE_ANON_KEY: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? "✅ Set" : "❌ Not set"}</p>
        </div>
      </div>
    )
  }
}

