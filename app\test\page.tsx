"use client";

import React, { useEffect, useState } from "react";
import { createBrowserClient } from "@/lib/supabase-client";
import { Doctor } from "@/lib/hybrid-data-service";

export default function TestPage() {
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [logMessages, setLogMessages] = useState<string[]>([]);
  const [connectionStatus, setConnectionStatus] = useState("Not checked");
  const [databaseUrl, setDatabaseUrl] = useState("Unknown");

  const addLog = (message: string) => {
    console.log(`TEST PAGE: ${message}`);
    setLogMessages(prev => [...prev, `${new Date().toISOString()}: ${message}`]);
  };

  // Test database connection
  useEffect(() => {
    async function testConnection() {
      addLog("Testing Supabase connection...");
      try {
        const supabase = createBrowserClient();
        addLog("Supabase client created");
        
        // Log database URL (without keys)
        const url = (supabase as any).restUrl || "Unknown";
        setDatabaseUrl(url);
        addLog(`Database URL: ${url}`);
        
        // Try a simple query to test connection
        const { data, error } = await supabase.from("doctors").select("count").limit(1);
        
        if (error) {
          addLog(`CONNECTION ERROR: ${error.message}`);
          setConnectionStatus(`Failed: ${error.message}`);
        } else {
          addLog("Connection successful!");
          setConnectionStatus("Connected successfully");
        }
      } catch (err: any) {
        addLog(`EXCEPTION testing connection: ${err.message}`);
        setConnectionStatus(`Exception: ${err.message}`);
      }
    }
    
    testConnection();
  }, []);

  // Fetch doctors with ratings
  useEffect(() => {
    async function fetchDoctors() {
      try {
        addLog("Directly querying doctors from database...");
        const supabase = createBrowserClient();
        
        // Log query details
        addLog("Executing query: SELECT * FROM doctors WHERE community_rating IS NOT NULL ORDER BY community_rating DESC LIMIT 10");

        const { data, error, count } = await supabase
          .from("doctors")
          .select("*", { count: 'exact' })
          .not('community_rating', 'is', null)  // Filter out null community_ratings
          .order("community_rating", { ascending: false })
          .limit(10);

        if (error) {
          addLog(`ERROR fetching doctors: ${error.message}`);
          setError(error.message);
          return;
        }

        // Log retrieved data
        addLog(`Retrieved ${data?.length || 0} doctors from database (total count: ${count || 'unknown'})`);
        
        if (data && data.length > 0) {
          // Log the first doctor's rating to debug
          addLog(`First doctor: ${data[0].fullname}, Rating: ${data[0].rating}`);
          
          // Check for nulls/undefined
          const nullRatings = data.filter(d => d.rating === null || d.rating === undefined).length;
          if (nullRatings > 0) {
            addLog(`WARNING: Found ${nullRatings} doctors with null ratings`);
          }
          
          // Log all ratings for debugging
          addLog(`All ratings: ${data.map(d => d.rating).join(', ')}`);
        } else {
          addLog("No doctors found in database");
        }

        setDoctors(data || []);
      } catch (err: any) {
        addLog(`EXCEPTION fetching doctors: ${err.message}`);
        setError(`An unexpected error occurred: ${err.message}`);
      } finally {
        setLoading(false);
      }
    }

    fetchDoctors();
  }, []);

  return (
    <div className="p-8 bg-background text-foreground min-h-screen">
      <h1 className="text-3xl font-bold mb-6">Database Test Page</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div className="bg-background/90 p-4 rounded-lg">
          <h2 className="text-xl font-bold mb-2">Connection Status</h2>
          <div className={`p-2 rounded ${connectionStatus.includes("success") ? "bg-green-900" : "bg-red-900"}`}>
            {connectionStatus}
          </div>
          <div className="mt-2">
            <strong>Database URL:</strong> {databaseUrl}
          </div>
        </div>
        
        <div className="bg-background/90 p-4 rounded-lg">
          <h2 className="text-xl font-bold mb-2">Query Status</h2>
          <div className={`p-2 rounded ${error ? "bg-red-900" : "bg-green-900"}`}>
            {loading ? "Loading..." : error ? `Error: ${error}` : `Found ${doctors.length} doctors`}
          </div>
        </div>
      </div>
      
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-4">Log Messages</h2>
        <div className="bg-background p-4 rounded-lg overflow-auto max-h-60">
          {logMessages.map((msg, i) => (
            <div key={i} className="font-mono text-sm">{msg}</div>
          ))}
        </div>
      </div>
      
      {!loading && !error && (
        <div>
          <h2 className="text-2xl font-bold mb-4">Doctors Sorted By Rating</h2>
          
          <div className="overflow-x-auto">
            <table className="min-w-full bg-background rounded-lg overflow-hidden">
              <thead className="bg-background/90">
                <tr>
                  <th className="px-4 py-2 text-left text-foreground">Rank</th>
                  <th className="px-4 py-2 text-left text-foreground">Doctor Name</th>
                  <th className="px-4 py-2 text-left text-foreground">Specialty</th>
                  <th className="px-4 py-2 text-left text-foreground">Rating</th>
                  <th className="px-4 py-2 text-left text-foreground">Raw Rating Value</th>
                  <th className="px-4 py-2 text-left text-foreground">Wins</th>
                  <th className="px-4 py-2 text-left text-foreground">Losses</th>
                </tr>
              </thead>
              <tbody>
                {doctors.map((doctor, index) => (
                  <tr key={doctor.doctor_id} className={index % 2 === 0 ? "bg-background" : "bg-background/90"}>
                    <td className="px-4 py-2 text-foreground">{index + 1}</td>
                    <td className="px-4 py-2 text-foreground">{doctor.fullname}</td>
                    <td className="px-4 py-2 text-foreground">{doctor.specialty}</td>
                    <td className="px-4 py-2 text-foreground font-bold">{doctor.rating?.toFixed(1) || "N/A"}</td>
                    <td className="px-4 py-2 text-foreground font-mono">{JSON.stringify(doctor.rating)}</td>
                    <td className="px-4 py-2 text-green-500">{doctor.wins || 0}</td>
                    <td className="px-4 py-2 text-red-500">{doctor.losses || 0}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          <div className="mt-8">
            <h3 className="text-lg font-bold mb-2">Raw Database Response:</h3>
            <pre className="bg-background/90 p-4 rounded overflow-auto max-h-96 text-sm whitespace-pre-wrap">
              {JSON.stringify(doctors, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}

