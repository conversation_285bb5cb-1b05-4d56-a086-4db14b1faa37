import { jwtDecode } from "jwt-decode";

// Define token payload type
export interface JwtPayload {
  userId: number;
  email: string;
  userType: 'patient' | 'doctor';
  exp: number; // Expiration timestamp
  iat: number; // Issued at timestamp
}

// Token cookie or localStorage key name - must match what's used in the auth context
export const AUTH_TOKEN_KEY = 'jwtToken';

/**
 * Gets the token from localStorage in a client component
 */
export function getAuthToken(): string | null {
  if (typeof window === 'undefined') {
    return null;
  }
  
  try {
    return localStorage.getItem(AUTH_TOKEN_KEY);
  } catch (error) {
    console.error('Error accessing localStorage:', error);
    return null;
  }
}

/**
 * Decodes and verifies the JWT token
 * Returns null if the token is invalid or expired
 */
export function decodeAndVerifyToken(token: string | null): JwtPayload | null {
  if (!token) {
    return null;
  }
  
  try {
    const decoded = jwtDecode<JwtPayload>(token);
    
    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (decoded.exp < currentTime) {
      return null;
    }
    
    return decoded;
  } catch (error) {
    console.error('Error decoding token:', error);
    return null;
  }
}

/**
 * Verifies authentication from localStorage in a client component
 * Returns the decoded payload if authenticated, null otherwise
 */
export function verifyClientAuth(): JwtPayload | null {
  const token = getAuthToken();
  return decodeAndVerifyToken(token);
}

/**
 * Checks if the user is authenticated as a patient
 */
export function isPatient(): boolean {
  const payload = verifyClientAuth();
  return !!payload && payload.userType === 'patient';
}

/**
 * Checks if the user is authenticated as a doctor
 */
export function isDoctor(): boolean {
  const payload = verifyClientAuth();
  return !!payload && payload.userType === 'doctor';
} 