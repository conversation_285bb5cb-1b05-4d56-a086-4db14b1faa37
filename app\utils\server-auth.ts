import { jwtDecode } from "jwt-decode";
import { cookies } from "next/headers";

// Define token payload type
export interface JwtPayload {
  userId: number;
  email: string;
  userType: 'patient' | 'doctor';
  exp: number; // Expiration timestamp
  iat: number; // Issued at timestamp
}

// Token cookie name - must match what's used in the auth context
export const AUTH_COOKIE_NAME = 'doctors_league_auth_token';

/**
 * Gets the token from cookies in a server component
 */
export function getAuthCookie() {
  try {
    // Access cookies in a server component
    const cookieList = cookies();
    return cookieList.get(AUTH_COOKIE_NAME);
  } catch (error) {
    console.error('Error accessing cookies:', error);
    return null;
  }
}

/**
 * Decodes and verifies the JWT token from a raw token string
 * Returns null if the token is invalid or expired
 */
export function decodeAndVerifyToken(token: string | undefined): JwtPayload | null {
  if (!token) {
    return null;
  }
  
  try {
    const decoded = jwtDecode<JwtPayload>(token);
    
    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (decoded.exp < currentTime) {
      return null;
    }
    
    return decoded;
  } catch (error) {
    console.error('Error decoding token:', error);
    return null;
  }
}

/**
 * Verifies authentication from cookies in a server component
 * Returns the decoded payload if authenticated, null otherwise
 */
export function verifyServerAuth(): JwtPayload | null {
  const cookie = getAuthCookie();
  if (!cookie?.value) {
    return null;
  }

  return decodeAndVerifyToken(cookie.value);
} 