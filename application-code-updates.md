# Application Code Updates for ID Reuse System

## Overview
After implementing the Supabase database functions and triggers, you have two options for updating your application code:

## Option 1: Minimal Changes (Recommended)
The triggers will automatically handle ID assignment, so your existing code can work with minimal changes.

### Update 1: Remove Manual ID Generation in `pages/api/auth/custom/register.ts`

**Current Code (Lines 114-131):**
```typescript
// Get the maximum doctor_id from the doctors_registration table
const { data: maxIdData, error: maxIdError } = await supabaseAdmin
  .from('doctors_registration')
  .select('doctor_id')
  .order('doctor_id', { ascending: false })
  .limit(1)
  .single();

if (maxIdError && maxIdError.code !== 'PGRST116') {
  console.error('Error getting max doctor_id:', maxIdError);
  return res.status(500).json({
    success: false,
    error: 'Failed to generate sequential ID'
  });
}

// If no rows returned or error, start with ID 1, otherwise increment the max ID
numericId = maxIdData && maxIdData.doctor_id !== null ? maxIdData.doctor_id + 1 : 1;
console.log(`Generated sequential doctor_id: ${numericId}`);
```

**New Code:**
```typescript
// Let the database trigger handle ID assignment automatically
// We'll get the assigned ID after insertion
numericId = null; // Will be set after insertion
console.log('Database will auto-assign doctor_id using gap-filling logic');
```

### Update 2: Modify the doctors_registration insertion

**Current Code (Lines 160-190):**
```typescript
const { data: doctorRegProfile, error } = await supabaseAdmin
  .from('doctors_registration')
  .insert({
    doctor_id: numericId, // Remove this line
    email: email,
    // ... rest of the fields
  })
```

**New Code:**
```typescript
const { data: doctorRegProfile, error } = await supabaseAdmin
  .from('doctors_registration')
  .insert({
    // Remove doctor_id - let trigger assign it
    email: email,
    // ... rest of the fields
  })
  .select('doctor_id') // Select the assigned ID
  .single();

// Get the assigned ID
if (doctorRegProfile) {
  numericId = doctorRegProfile.doctor_id;
  console.log(`Database assigned doctor_id: ${numericId}`);
}
```

### Update 3: Update `actions/doctor-registration-actions.ts`

**Current Code (Lines 387-413):**
```typescript
// Get the maximum doctor_id from the doctors_registration table to generate sequential ID
let nextDoctorId: number;

try {
  const { data: maxIdData, error: maxIdError } = await supabaseAdmin
    .from('doctors_registration')
    .select('doctor_id')
    .order('doctor_id', { ascending: false })
    .limit(1)
    .single();
  
  if (maxIdError) {
    if (maxIdError.code === 'PGRST116') {
      console.log("No existing doctors found, starting with ID 1");
      nextDoctorId = 1;
    } else {
      console.error("Error getting max doctor_id:", maxIdError);
      return { error: { message: "Failed to generate sequential ID: " + maxIdError.message, details: maxIdError } };
    }
  } else {
    nextDoctorId = (maxIdData?.doctor_id || 0) + 1;
    console.log(`Generated sequential doctor_id: ${nextDoctorId}`);
  }
} catch (idError) {
  console.error("Exception getting max doctor_id:", idError);
  nextDoctorId = Math.floor(Date.now() / 1000);
  console.log(`Fallback to timestamp-based ID: ${nextDoctorId}`);
}
```

**New Code:**
```typescript
// Database trigger will handle ID assignment automatically
console.log("Database will auto-assign doctor_id using gap-filling logic");
```

**And update the insertion:**
```typescript
const dataToInsert: Database['public']['Tables']['doctors_registration']['Insert'] = {
  ...profileData,
  // Remove: doctor_id: nextDoctorId,
  auth_id: userId,
  last_updated: new Date().toISOString(),
};

console.log("Inserting doctor profile data:", JSON.stringify(dataToInsert, null, 2));

const { data: insertedData, error } = await supabaseAdmin
  .from('doctors_registration')
  .insert(dataToInsert)
  .select('doctor_id') // Get the assigned ID
  .single();

if (error) {
  console.error("Error in createDoctorProfile action:", error);
  return { error: { message: error.message || "Failed to create doctor profile.", details: error } };
}

console.log(`Action: createDoctorProfile successful for user: ${userId}, assigned doctor_id: ${insertedData?.doctor_id}`);
return { error: null, doctor_id: insertedData?.doctor_id };
```

## Option 2: Use Database Function (Alternative)
If you prefer to get the next ID before insertion for logging purposes:

```typescript
// Get next available ID from database function
const { data: nextIdData, error: nextIdError } = await supabaseAdmin
  .rpc('reserve_next_doctor_id');

if (nextIdError) {
  console.error('Error getting next doctor_id:', nextIdError);
  return res.status(500).json({
    success: false,
    error: 'Failed to generate sequential ID'
  });
}

const nextDoctorId = nextIdData;
console.log(`Reserved doctor_id: ${nextDoctorId}`);

// Then use it in insertion (optional, trigger will still work)
const { data: doctorRegProfile, error } = await supabaseAdmin
  .from('doctors_registration')
  .insert({
    doctor_id: nextDoctorId, // Explicitly set the reserved ID
    email: email,
    // ... rest of fields
  })
```

## Testing the Implementation

### Test Case 1: Normal Registration
1. Register a new doctor
2. Check that they get the next available ID (filling any gaps)

### Test Case 2: Gap Filling
1. Create doctors with IDs 1, 2, 3, 4, 5
2. Delete doctor with ID 3
3. Register a new doctor
4. Verify the new doctor gets ID 3 (not ID 6)

### Test Case 3: Multiple Gaps
1. Create doctors with IDs 1, 2, 3, 4, 5, 6, 7, 8, 9, 10
2. Delete doctors with IDs 3, 5, 8
3. Register three new doctors
4. Verify they get IDs 3, 5, 8 (in that order)

### SQL Test Queries
```sql
-- Check current doctor IDs
SELECT doctor_id, fullname FROM doctors ORDER BY doctor_id;
SELECT doctor_id, fullname FROM doctors_registration ORDER BY doctor_id;

-- Test the function directly
SELECT get_next_available_doctor_id();

-- Insert test record
INSERT INTO doctors_registration (fullname, email, status) 
VALUES ('Test Doctor', '<EMAIL>', 'pending');

-- Check assigned ID
SELECT doctor_id, fullname FROM doctors_registration WHERE fullname = 'Test Doctor';
```

## Benefits of This Solution

1. **Automatic Gap Filling**: Deleted IDs are automatically reused
2. **No Application Changes Required**: Triggers handle everything
3. **Concurrent Safe**: PostgreSQL handles locking automatically
4. **Consistent**: Works across both doctors and doctors_registration tables
5. **Backward Compatible**: Existing code continues to work
6. **Performance**: Efficient for typical dataset sizes

## Important Notes

- The triggers only assign IDs when `doctor_id` is NULL
- If you manually specify an ID, the trigger won't override it
- The system considers IDs from both tables to avoid conflicts
- For very large datasets (10,000+ doctors), consider adding indexes on doctor_id columns
