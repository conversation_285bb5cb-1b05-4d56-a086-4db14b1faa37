﻿// The correct, detailed Supabase backup script
import { createClient } from '@supabase/supabase-js';

// --- Supabase Configuration ---
const supabaseUrl = 'https://uapbzzscckhtptliynyj.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

const SOURCE_BUCKET = 'doctor-profiles';
const DESTINATION_BUCKET = 'doctor-profiles-backup';

// --- Main Backup Function ---
async function backupBucket() {
  console.log('--- Starting Supabase Bucket Backup ---');
  console.log(`Source Bucket:      '${SOURCE_BUCKET}'`);
  console.log(`Destination Bucket: '${DESTINATION_BUCKET}'`);
  console.log('-----------------------------------------');

  try {
    // --- Step 1: Verify both buckets exist ---
    console.log('\nStep 1: Verifying buckets exist...');
    const { data: sourceBucketData, error: sourceBucketError } = await supabase.storage.getBucket(SOURCE_BUCKET);
    if (sourceBucketError || !sourceBucketData) {
      console.error(`❌ ERROR: Source bucket '${SOURCE_BUCKET}' not found or access denied.`);
      console.error('Supabase error:', sourceBucketError?.message || 'Bucket does not exist.');
      console.error('\nPlease ensure the bucket exists and the service key has read permissions.');
      return;
    }
    console.log(`  ✅ Source bucket '${SOURCE_BUCKET}' found.`);

    const { data: destBucketData, error: destBucketError } = await supabase.storage.getBucket(DESTINATION_BUCKET);
    if (destBucketError || !destBucketData) {
      console.error(`❌ ERROR: Destination bucket '${DESTINATION_BUCKET}' not found or access denied.`);
      console.error('Supabase error:', destBucketError?.message || 'Bucket does not exist.');
      console.error(`\nIMPORTANT: You must create the destination bucket in your Supabase dashboard before running this script.`);
      return;
    }
    console.log(`  ✅ Destination bucket '${DESTINATION_BUCKET}' found.`);

    // --- Step 2: List all files in the source bucket ---
    console.log('\nStep 2: Listing files from source bucket...');
    const { data: files, error: listError } = await supabase.storage
      .from(SOURCE_BUCKET)
      .list('', { recursive: true });

    if (listError) {
      console.error(`❌ ERROR: Could not list files from '${SOURCE_BUCKET}'.`);
      console.error('Supabase error:', listError.message);
      return;
    }

    const actualFiles = files.filter(file => file.id !== null);

    if (actualFiles.length === 0) {
      console.log('  -> Found 0 files. The source bucket is empty. Nothing to back up.');
      return;
    }
    console.log(`  -> Found ${actualFiles.length} files to copy.`);

    // --- Step 3: Loop through and copy each file ---
    console.log('\nStep 3: Copying files one by one...');
    let filesCopied = 0;
    let filesFailed = 0;

    for (const file of actualFiles) {
      const sourcePath = file.name;
      const fromPath = `${SOURCE_BUCKET}/${sourcePath}`;
      const toPath = `${DESTINATION_BUCKET}/${sourcePath}`;

      console.log(`\n  Attempting to copy:`);
      console.log(`  -> FROM: "${fromPath}"`);
      console.log(`  -> TO:   "${toPath}"`);
      
      const { error: copyError } = await supabase.storage.copy(fromPath, toPath);

      if (copyError) {
        console.error(`  ❌ ERROR: Failed to copy "${sourcePath}".`);
        console.error(`  Supabase error: ${copyError.message}`);
        filesFailed++;
      } else {
        console.log(`  ✅ SUCCESS: Copied "${sourcePath}" successfully.`);
        filesCopied++;
      }
    }

    // --- Step 4: Final Summary ---
    console.log('\n-----------------------------------');
    console.log('Backup Process Complete!');
    console.log(`  - Files copied successfully: ${filesCopied}`);
    console.log(`  - Files that failed to copy: ${filesFailed}`);
    console.log('-----------------------------------');

  } catch (err) {
    console.error('\n🚨 AN UNEXPECTED SCRIPT-LEVEL ERROR OCCURRED:');
    console.error(err);
  }
}

// Run the main function
backupBucket();
