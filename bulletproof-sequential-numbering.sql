-- BULL<PERSON><PERSON>OOF SEQUENTIAL NUMBERING SOLUTION
-- This completely takes control of doctor_id assignment and prevents any interference

-- STEP 1: Disable the competing sequence by setting it to a very low value
-- This ensures it never interferes with our logic
SELECT setval('doctors_doctor_id_seq1', 1, false);

-- STEP 2: Create a more robust sequential ID function with locking
CREATE OR REPLACE FUNCTION get_bulletproof_sequential_doctor_id()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    next_id INTEGER;
BEGIN
    -- Use advisory lock to prevent race conditions
    PERFORM pg_advisory_lock(12345);

    -- Get the maximum doctor_id from DOCTORS table ONLY and add 1
    -- This ensures we continue from the last actual doctor record
    SELECT COALESCE(MAX(doctor_id), 0) + 1 INTO next_id
    FROM doctors
    WHERE doctor_id IS NOT NULL;

    -- Release the lock
    PERFORM pg_advisory_unlock(12345);

    RETURN next_id;
END;
$$;

-- STEP 3: Create a more aggressive trigger function that forces the ID
CREATE OR REPLACE FUNCTION force_sequential_doctor_id()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    forced_id INTEGER;
BEGIN
    -- Always force our sequential ID, regardless of what was provided
    forced_id := get_bulletproof_sequential_doctor_id();
    NEW.doctor_id := forced_id;
    
    -- Log for debugging
    RAISE NOTICE 'Forced doctor_id to: %', forced_id;
    
    RETURN NEW;
END;
$$;

-- STEP 4: Replace existing triggers with higher priority ones
-- Drop old triggers
DROP TRIGGER IF EXISTS trigger_set_doctor_id_registration ON doctors_registration;
DROP TRIGGER IF EXISTS trigger_set_doctor_id_doctors ON doctors;

-- Create new triggers that run BEFORE everything else
CREATE TRIGGER force_sequential_doctor_id_registration
    BEFORE INSERT ON doctors_registration
    FOR EACH ROW
    EXECUTE FUNCTION force_sequential_doctor_id();

CREATE TRIGGER force_sequential_doctor_id_doctors
    BEFORE INSERT ON doctors
    FOR EACH ROW
    EXECUTE FUNCTION force_sequential_doctor_id();

-- STEP 5: Create a function to fix any existing interference from other triggers
CREATE OR REPLACE FUNCTION ensure_sequential_after_insert()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    expected_id INTEGER;
    current_max INTEGER;
BEGIN
    -- Get what the ID should be based on current max from DOCTORS table ONLY
    SELECT COALESCE(MAX(doctor_id), 0) INTO current_max
    FROM doctors
    WHERE doctor_id IS NOT NULL AND doctor_id != NEW.doctor_id;

    expected_id := current_max + 1;

    -- If the ID was changed by another trigger, fix it
    IF NEW.doctor_id != expected_id THEN
        RAISE NOTICE 'Correcting doctor_id from % to %', NEW.doctor_id, expected_id;
        NEW.doctor_id := expected_id;
    END IF;

    RETURN NEW;
END;
$$;

-- STEP 6: Add AFTER INSERT triggers to catch any post-processing interference
CREATE TRIGGER ensure_sequential_after_registration_insert
    BEFORE UPDATE ON doctors_registration
    FOR EACH ROW
    WHEN (OLD.doctor_id IS DISTINCT FROM NEW.doctor_id)
    EXECUTE FUNCTION ensure_sequential_after_insert();

CREATE TRIGGER ensure_sequential_after_doctors_insert
    BEFORE UPDATE ON doctors
    FOR EACH ROW
    WHEN (OLD.doctor_id IS DISTINCT FROM NEW.doctor_id)
    EXECUTE FUNCTION ensure_sequential_after_insert();

-- STEP 7: Verification
SELECT 'Bulletproof sequential numbering installed!' as status;

-- Test the function
SELECT 'Next ID will be:' as info, get_bulletproof_sequential_doctor_id() as next_id;
