-- CASCADE DELETE FUNCTIONS FOR AUTH_CREDENTIALS
-- This creates functions and triggers to automatically delete auth_credentials 
-- records when corresponding doctors or users are deleted

-- 1. Function to delete auth_credentials when a doctor is deleted
CREATE OR REPLACE FUNCTION delete_auth_credentials_on_doctor_delete()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Delete from auth_credentials where email matches the deleted doctor's email
    DELETE FROM auth_credentials 
    WHERE email = OLD.email 
    AND user_type = 'doctor';
    
    -- Log the deletion (optional)
    RAISE NOTICE 'Deleted auth_credentials for doctor email: %', OLD.email;
    
    RETURN OLD;
END;
$$;

-- 2. Function to delete auth_credentials when a user is deleted
CREATE OR REPLACE FUNCTION delete_auth_credentials_on_user_delete()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Delete from auth_credentials where email matches the deleted user's email
    DELETE FROM auth_credentials 
    WHERE email = OLD.email 
    AND user_type = 'patient';
    
    -- Log the deletion (optional)
    RAISE NOTICE 'Deleted auth_credentials for user email: %', OLD.email;
    
    RETURN OLD;
END;
$$;

-- 3. Create trigger for doctors table
DROP TRIGGER IF EXISTS trigger_delete_auth_on_doctor_delete ON doctors;
CREATE TRIGGER trigger_delete_auth_on_doctor_delete
    AFTER DELETE ON doctors
    FOR EACH ROW
    EXECUTE FUNCTION delete_auth_credentials_on_doctor_delete();

-- 4. Create trigger for users table
DROP TRIGGER IF EXISTS trigger_delete_auth_on_user_delete ON users;
CREATE TRIGGER trigger_delete_auth_on_user_delete
    AFTER DELETE ON users
    FOR EACH ROW
    EXECUTE FUNCTION delete_auth_credentials_on_user_delete();

-- 5. Verification queries to check if triggers were created
SELECT 
    trigger_name,
    event_manipulation,
    event_object_table,
    action_statement
FROM information_schema.triggers 
WHERE trigger_name IN (
    'trigger_delete_auth_on_doctor_delete',
    'trigger_delete_auth_on_user_delete'
)
ORDER BY event_object_table;

-- Success message
SELECT 'Cascade deletion triggers created successfully!' as status;
