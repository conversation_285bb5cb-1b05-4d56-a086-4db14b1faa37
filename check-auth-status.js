// Script to check authentication status
const { createClient } = require('@supabase/supabase-js');

// Create Supabase client
const supabaseUrl = 'https://uapbzzscckhtptliynyj.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q';
const supabase = createClient(supabaseUrl, supabaseKey);

// Email to check (replace with actual email to check)
const emailToCheck = process.argv[2] || '<EMAIL>';

async function checkAuthStatus() {
  console.log(`Checking authentication status for email: ${emailToCheck}`);
  
  try {
    // Check auth_credentials table
    console.log('\nChecking auth_credentials table...');
    const { data: authData, error: authError } = await supabase
      .from('auth_credentials')
      .select('*')
      .eq('email', emailToCheck);
      
    if (authError) {
      console.error('Error querying auth_credentials:', authError);
      return;
    }
    
    if (!authData || authData.length === 0) {
      console.log('No auth record found for this email.');
      return;
    }
    
    // Show the auth record
    console.log('Auth record found:');
    console.log('  ID:', authData[0].id);
    console.log('  User Profile ID:', authData[0].user_profile_id);
    console.log('  Email:', authData[0].email);
    console.log('  User Type:', authData[0].user_type);
    console.log('  Is Verified:', authData[0].is_verified || 'Not set (NULL)');
    console.log('  Created At:', authData[0].created_at);
    console.log('  Updated At:', authData[0].updated_at);
    
    // Check verification token
    console.log('\nChecking verification tokens...');
    const { data: tokenData, error: tokenError } = await supabase
      .from('verification_tokens')
      .select('*')
      .eq('email', emailToCheck);
      
    if (tokenError) {
      console.log('Error querying verification tokens (table might not exist):', tokenError.message);
    } else if (!tokenData || tokenData.length === 0) {
      console.log('No verification token found for this email.');
    } else {
      console.log('Verification token found:');
      console.log('  Token:', tokenData[0].token);
      console.log('  User ID:', tokenData[0].user_id);
      console.log('  Expires At:', tokenData[0].expires_at);
      const isExpired = new Date(tokenData[0].expires_at) < new Date();
      console.log('  Is Expired:', isExpired);
    }
    
    // Check user profile based on user type
    const profileType = authData[0].user_type;
    const profileId = authData[0].user_profile_id;
    
    console.log(`\nChecking ${profileType} profile with ID ${profileId}...`);
    
    if (profileType === 'doctor') {
      const { data: doctorData, error: doctorError } = await supabase
        .from('doctors')
        .select('*')
        .eq('doctor_id', profileId)
        .single();
        
      if (doctorError) {
        console.error('Error querying doctor profile:', doctorError);
      } else if (doctorData) {
        console.log('Doctor profile found:');
        console.log('  Name:', doctorData.fullname);
        console.log('  Email:', doctorData.email);
        console.log('  Verified:', doctorData.verified);
      }
    } else if (profileType === 'patient') {
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('user_id', profileId)
        .single();
        
      if (userError) {
        console.error('Error querying user profile:', userError);
      } else if (userData) {
        console.log('User profile found:');
        console.log('  Name:', userData.first_name, userData.last_name);
        console.log('  Email:', userData.email);
        console.log('  Verified:', userData.verified);
      }
    }
    
  } catch (err) {
    console.error('Error checking auth status:', err);
  }
}

// Run the check
if (process.argv.length < 3) {
  console.log('Usage: node check-auth-status.js <email>');
  console.log('Example: node check-auth-status.js <EMAIL>');
} else {
  checkAuthStatus();
} 