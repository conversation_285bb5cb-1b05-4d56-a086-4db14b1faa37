// Script to check auth_credentials and doctor records for the test account
const { createClient } = require('@supabase/supabase-js');

// Create Supabase client
const supabaseUrl = 'https://uapbzzscckhtptliynyj.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q';
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  console.log('Checking tables for test doctor account...');
  
  // Get auth credentials for test doctor
  const { data: authData, error: authError } = await supabase
    .from('auth_credentials')
    .select('*')
    .eq('email', '<EMAIL>')
    .single();
  
  if (authError) {
    console.error('Error fetching auth credentials:', authError);
    return;
  }
  
  console.log('Auth credentials found:', {
    id: authData.id,
    email: authData.email,
    user_profile_id: authData.user_profile_id,
    user_type: authData.user_type,
  });
  
  // Get doctor record
  const { data: doctorData, error: doctorError } = await supabase
    .from('doctors')
    .select('*')
    .eq('doctor_id', authData.user_profile_id)
    .single();
  
  if (doctorError) {
    console.error('Error fetching doctor record:', doctorError);
    return;
  }
  
  console.log('Doctor record found:', {
    doctor_id: doctorData.doctor_id,
    fullname: doctorData.fullname,
    email: doctorData.email,
    auth_id: doctorData.auth_id
  });
  
  // Check if there's a mismatch between user_profile_id and auth_id
  if (doctorData.auth_id !== authData.user_profile_id.toString()) {
    console.log('WARNING: Mismatch between auth_id and user_profile_id!');
    console.log(`auth_credentials.user_profile_id = ${authData.user_profile_id}`);
    console.log(`doctors.auth_id = ${doctorData.auth_id}`);
  } else {
    console.log('auth_id and user_profile_id match correctly.');
  }
}

main().catch(console.error); 