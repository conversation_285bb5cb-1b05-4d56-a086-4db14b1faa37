// Direct database check script
const { createClient } = require('@supabase/supabase-js');

// Hardcoded values from .env.local
const SUPABASE_URL = "https://uapbzzscckhtptliynyj.supabase.co";
const SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q";

async function checkDatabase() {
  console.log('===============================================');
  console.log('Direct Database Check Script');
  console.log('===============================================');
  
  // Create Supabase client
  console.log(`Creating Supabase client with URL: ${SUPABASE_URL}`);
  console.log(`Service role key provided: ${SUPABASE_SERVICE_ROLE_KEY ? 'Yes' : 'No'}`);
  
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
  
  try {
    // Check if we can connect
    console.log('\nTesting connection...');
    const { data: testData, error: testError } = await supabase.from('doctors').select('count');
    
    if (testError) {
      console.error('Connection test failed:', testError.message);
      return;
    }
    
    console.log('Connection successful!');
    
    // Query all doctors and check their ratings
    console.log('\nFetching all doctors...');
    const { data: doctors, error: doctorsError } = await supabase
      .from('doctors')
      .select('*');
    
    if (doctorsError) {
      console.error('Error fetching doctors:', doctorsError.message);
      return;
    }
    
    console.log(`Found ${doctors.length} doctors in database`);
    
    // Check community_ratings
    const doctorsWithRating = doctors.filter(d => d.community_rating !== null && d.community_rating !== undefined);
    const doctorsWithZeroRating = doctors.filter(d => d.community_rating === 0);
    const doctorsWithNullRating = doctors.filter(d => d.community_rating === null || d.community_rating === undefined);
    
    console.log(`\nRating statistics:`);
    console.log(`- Doctors with rating: ${doctorsWithRating.length} (${((doctorsWithRating.length / doctors.length) * 100).toFixed(1)}%)`);
    console.log(`- Doctors with zero rating: ${doctorsWithZeroRating.length} (${((doctorsWithZeroRating.length / doctors.length) * 100).toFixed(1)}%)`);
    console.log(`- Doctors with null rating: ${doctorsWithNullRating.length} (${((doctorsWithNullRating.length / doctors.length) * 100).toFixed(1)}%)`);
    
    // Show top 10 doctors by rating
    console.log('\nTop 10 doctors by rating:');
    const topDoctors = [...doctors]
      .filter(d => d.rating !== null && d.rating !== undefined)
      .sort((a, b) => b.rating - a.rating)
      .slice(0, 10);
    
    topDoctors.forEach((doc, index) => {
      console.log(`${index + 1}. ${doc.fullname}: Rating = ${doc.rating} (${typeof doc.rating})`);
    });
    
    // Show doctors with highest non-zero ratings
    console.log('\nDoctors with highest non-zero ratings:');
    const nonZeroDoctors = [...doctors]
      .filter(d => d.rating > 0)
      .sort((a, b) => b.rating - a.rating)
      .slice(0, 10);
    
    if (nonZeroDoctors.length === 0) {
      console.log('No doctors found with non-zero ratings!');
    } else {
      nonZeroDoctors.forEach((doc, index) => {
        console.log(`${index + 1}. ${doc.fullname}: Rating = ${doc.rating} (${typeof doc.rating})`);
      });
    }
    
    // Show the raw database values for a few doctors
    console.log('\nRaw database values for first 5 doctors:');
    doctors.slice(0, 5).forEach((doc, index) => {
      console.log(`Doctor ${index + 1}: ${JSON.stringify(doc)}`);
    });
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

// Run the check
checkDatabase().catch(console.error); 