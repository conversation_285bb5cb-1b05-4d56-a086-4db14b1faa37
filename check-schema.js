// Script to check database schema
const { createClient } = require('@supabase/supabase-js');

// Create Supabase client - Replace with your own credentials
const supabaseUrl = 'https://uapbzzscckhtptliynyj.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q';
const supabase = createClient(supabaseUrl, supabaseKey);

async function getTableInfo(tableName) {
  console.log(`\n=== Checking schema for ${tableName} ===`);
  try {
    // Get table information using pg_catalog
    const { data, error } = await supabase.rpc('schema_details', { target_table: tableName });
    
    if (error) {
      console.error(`Error getting schema for ${tableName}:`, error);
      
      // Try an alternative approach - query a row and get keys
      console.log(`Trying alternative approach for ${tableName}...`);
      const { data: rowData, error: rowError } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);
        
      if (rowError) {
        console.error(`Failed to get row data for ${tableName}:`, rowError);
        return;
      }
      
      if (rowData && rowData.length > 0) {
        console.log(`Fields in ${tableName}:`);
        const fields = Object.keys(rowData[0]);
        fields.forEach(field => {
          console.log(`- ${field}: ${typeof rowData[0][field]}`);
        });
      } else {
        console.log(`No data found in ${tableName}`);
      }
      
      return;
    }
    
    console.log(`Fields in ${tableName}:`);
    data.forEach(column => {
      console.log(`- ${column.column_name}: ${column.data_type} ${column.is_nullable === 'YES' ? '(nullable)' : '(required)'}`);
    });
  } catch (err) {
    console.error(`Error analyzing ${tableName}:`, err);
  }
}

async function main() {
  console.log('======================================');
  console.log('DATABASE SCHEMA ANALYSIS');
  console.log('======================================');
  
  // Check schema for main tables
  await getTableInfo('auth_credentials');
  await getTableInfo('doctors');
  await getTableInfo('users');
  
  console.log('\n======================================');
  console.log('ANALYSIS COMPLETE');
  console.log('======================================');
}

main().catch(err => {
  console.error('Fatal error:', err);
}).finally(() => {
  process.exit(0);
}); 