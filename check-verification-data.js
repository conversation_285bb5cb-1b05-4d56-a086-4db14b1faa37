const { createClient } = require('@supabase/supabase-js');

// Direct values from .env.local
const SUPABASE_URL = "https://uapbzzscckhtptliynyj.supabase.co";
const SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q";

async function checkData() {
  console.log('🔍 Checking verification data and recent reviews...\n');
  
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

  try {
    // Check recent reviews
    console.log('📋 Recent reviews:');
    const { data: reviewsData, error: reviewsError } = await supabase
      .from('reviews')
      .select('review_id, verification_status, review_date, user_id, doctor_id')
      .order('review_date', { ascending: false })
      .limit(10);

    if (reviewsError) {
      console.log('❌ Error fetching reviews:', reviewsError.message);
    } else {
      console.log(`Found ${reviewsData.length} recent reviews:`);
      reviewsData.forEach(review => {
        console.log(`  - Review ID: ${review.review_id}, Status: ${review.verification_status}, Date: ${review.review_date}`);
      });
    }

    // Check verification_proofs table
    console.log('\n📋 Verification proofs:');
    const { data: proofsData, error: proofsError } = await supabase
      .from('verification_proofs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    if (proofsError) {
      console.log('❌ Error fetching verification proofs:', proofsError.message);
    } else {
      console.log(`Found ${proofsData.length} verification proofs:`);
      proofsData.forEach(proof => {
        console.log(`  - Proof ID: ${proof.id}, Review ID: ${proof.review_id}, Image: ${proof.image_path}`);
      });
    }

    // Check for reviews with pending verification status
    console.log('\n📋 Reviews with pending verification:');
    const { data: pendingData, error: pendingError } = await supabase
      .from('reviews')
      .select('review_id, verification_status, review_date, user_id, doctor_id')
      .eq('verification_status', 'pending_verification')
      .order('review_date', { ascending: false });

    if (pendingError) {
      console.log('❌ Error fetching pending reviews:', pendingError.message);
    } else {
      console.log(`Found ${pendingData.length} reviews with pending verification:`);
      pendingData.forEach(review => {
        console.log(`  - Review ID: ${review.review_id}, Date: ${review.review_date}`);
      });
    }

    // Check storage bucket contents
    console.log('\n📋 Storage bucket contents:');
    const { data: storageData, error: storageError } = await supabase.storage
      .from('appointment-verification')
      .list('proofs', { limit: 10 });

    if (storageError) {
      console.log('❌ Error checking storage:', storageError.message);
    } else {
      console.log(`Found ${storageData.length} files in storage:`);
      storageData.forEach(file => {
        console.log(`  - File: ${file.name}, Size: ${file.metadata?.size || 'unknown'}`);
      });
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

checkData();
