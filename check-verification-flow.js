// Script to test the verification flow for both doctor and patient users
const { createClient } = require('@supabase/supabase-js');

// Create Supabase client
const supabaseUrl = 'https://uapbzzscckhtptliynyj.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q';
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to check if a user has the is_verified column
async function checkUserVerificationField(userId, userType) {
  console.log(`\nChecking verification status for ${userType} user ID: ${userId}`);
  
  // Check auth_credentials
  const { data: authData, error: authError } = await supabase
    .from('auth_credentials')
    .select('*')
    .eq('user_profile_id', userId)
    .single();
    
  if (authError || !authData) {
    console.error(`Error or no data for ${userType} in auth_credentials:`, authError);
    return false;
  }
  
  // Check if is_verified exists and its value
  const hasVerifiedField = 'is_verified' in authData;
  console.log(`${userType} - is_verified column exists:`, hasVerifiedField);
  
  if (hasVerifiedField) {
    console.log(`${userType} - is_verified value:`, authData.is_verified);
  }
  
  // Check profile table
  let profileData = null;
  let profileError = null;
  
  if (userType === 'doctor') {
    const result = await supabase
      .from('doctors')
      .select('*')
      .eq('doctor_id', userId)
      .single();
      
    profileData = result.data;
    profileError = result.error;
  } else if (userType === 'patient') {
    const result = await supabase
      .from('users')
      .select('*')
      .eq('user_id', userId)
      .single();
      
    profileData = result.data;
    profileError = result.error;
  }
  
  if (profileError || !profileData) {
    console.error(`Error or no data for ${userType} in profile table:`, profileError);
    return false;
  }
  
  // Check if verified exists in profile table
  const hasProfileVerifiedField = 'verified' in profileData;
  console.log(`${userType} - verified column exists in profile:`, hasProfileVerifiedField);
  
  if (hasProfileVerifiedField) {
    console.log(`${userType} - verified value in profile:`, profileData.verified);
  }
  
  // Check verification tokens
  const { data: tokenData, error: tokenError } = await supabase
    .from('verification_tokens')
    .select('*')
    .eq('user_id', userId.toString())
    .single();
    
  if (tokenError && tokenError.code !== 'PGRST116') {
    console.error(`Error checking tokens for ${userType}:`, tokenError);
  }
  
  if (tokenData) {
    console.log(`${userType} - Unused verification token exists:`, {
      token: tokenData.token.substring(0, 8) + '...',
      expires: new Date(tokenData.expires_at).toLocaleString()
    });
  } else {
    console.log(`${userType} - No unused verification token found (good if user is verified)`);
  }
  
  return true;
}

// Main function to check both user types
async function checkVerificationFlow() {
  // Get a sample doctor ID
  const { data: doctorData } = await supabase
    .from('doctors')
    .select('doctor_id')
    .limit(1)
    .single();
    
  // Get a sample patient ID
  const { data: patientData } = await supabase
    .from('users')
    .select('user_id')
    .limit(1)
    .single();
    
  if (doctorData?.doctor_id) {
    await checkUserVerificationField(doctorData.doctor_id, 'doctor');
  } else {
    console.log('No doctor found to check');
  }
  
  if (patientData?.user_id) {
    await checkUserVerificationField(patientData.user_id, 'patient');
  } else {
    console.log('No patient found to check');
  }
  
  // Check if is_verified column exists in auth_credentials
  const { data: columns, error: columnsError } = await supabase.rpc('get_column_info', {
    target_table: 'auth_credentials'
  });
  
  if (columnsError) {
    console.error('Error checking columns:', columnsError);
    
    // Alternate approach
    const { data: sampleAuth } = await supabase
      .from('auth_credentials')
      .select('*')
      .limit(1)
      .single();
      
    if (sampleAuth) {
      console.log('\nColumns in auth_credentials:', Object.keys(sampleAuth));
      console.log('is_verified column exists:', 'is_verified' in sampleAuth);
    }
  } else {
    console.log('\nColumns in auth_credentials:');
    columns.forEach(col => {
      console.log(`- ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'required'})`);
    });
  }
}

checkVerificationFlow(); 