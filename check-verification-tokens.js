// Script to check verification_tokens table structure
const { createClient } = require('@supabase/supabase-js');

// Create Supabase client - using the same credentials from check-schema.js
const supabaseUrl = 'https://uapbzzscckhtptliynyj.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q';
const supabase = createClient(supabaseUrl, supabaseKey);

async function createCheckTable() {
  console.log('Checking if verification_tokens table exists...');
  
  // Try to create the verification_tokens table if it doesn't exist
  try {
    // First check if the table exists already by running a query
    const { data, error } = await supabase
      .from('verification_tokens')
      .select('*')
      .limit(1);
      
    if (error) {
      console.log('Error querying table, likely does not exist:', error.message);
      console.log('Creating verification_tokens table directly...');
      
      // Create the table directly using SQL
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS verification_tokens (
          id SERIAL PRIMARY KEY,
          user_id TEXT NOT NULL,
          token TEXT NOT NULL,
          email TEXT NOT NULL,
          expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;
      
      try {
        // Try to execute the SQL directly
        const { error: sqlError } = await supabase.rpc('exec_sql', { sql: createTableQuery });
        
        if (sqlError) {
          console.error('Failed to create table with exec_sql:', sqlError);
          return;
        }
        
        console.log('Table created successfully!');
      } catch (sqlErr) {
        console.error('Error running SQL to create table:', sqlErr);
        console.log('Trying to create a reusable function instead...');
        
        // Create a function to create the table
        const createFunctionQuery = `
          CREATE OR REPLACE FUNCTION create_verification_tokens_table_if_not_exists()
          RETURNS VOID AS $$
          BEGIN
            -- Check if the table exists
            IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'verification_tokens') THEN
              -- Create the table
              CREATE TABLE verification_tokens (
                id SERIAL PRIMARY KEY,
                user_id TEXT NOT NULL,
                token TEXT NOT NULL,
                email TEXT NOT NULL, 
                expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              );
            END IF;
          END;
          $$ LANGUAGE plpgsql;
        `;
        
        try {
          const { error: functionError } = await supabase.rpc('exec_sql', { sql: createFunctionQuery });
          
          if (functionError) {
            console.error('Failed to create function:', functionError);
            return;
          }
          
          console.log('Function created successfully! Now trying to run it...');
          
          // Try to run the function
          const { error: runError } = await supabase.rpc('create_verification_tokens_table_if_not_exists');
          
          if (runError) {
            console.error('Failed to run function:', runError);
            return;
          }
          
          console.log('Verification tokens table created successfully via function!');
        } catch (funcErr) {
          console.error('Error creating/running function:', funcErr);
        }
      }
    } else {
      console.log('Table already exists with structure:');
      if (data && data.length > 0) {
        const columns = Object.keys(data[0]);
        columns.forEach(col => {
          console.log(`- ${col}: ${typeof data[0][col]}`);
        });
      } else {
        console.log('No data found, but table exists');
      }
    }
    
  } catch (err) {
    console.error('Fatal error:', err);
  }
}

createCheckTable(); 