const { createClient } = require('@supabase/supabase-js');

// Hardcoded values from .env.local
const SUPABASE_URL = "https://uapbzzscckhtptliynyj.supabase.co";
const SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q";

async function checkVerificationTables() {
  console.log('🔍 Checking verification-related tables and data...\n');
  
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

  try {
    // Check if verification_proofs table exists
    console.log('📋 Checking verification_proofs table...');
    const { data: proofsData, error: proofsError } = await supabase
      .from('verification_proofs')
      .select('*')
      .limit(10);

    if (proofsError) {
      console.log('❌ verification_proofs table error:', proofsError.message);
    } else {
      console.log(`✅ verification_proofs table exists with ${proofsData.length} records`);
      if (proofsData.length > 0) {
        console.log('Sample data:', JSON.stringify(proofsData[0], null, 2));
      }
    }

    // Check if review_flags table exists
    console.log('\n📋 Checking review_flags table...');
    const { data: flagsData, error: flagsError } = await supabase
      .from('review_flags')
      .select('*')
      .limit(10);

    if (flagsError) {
      console.log('❌ review_flags table error:', flagsError.message);
    } else {
      console.log(`✅ review_flags table exists with ${flagsData.length} records`);
      if (flagsData.length > 0) {
        console.log('Sample data:', JSON.stringify(flagsData[0], null, 2));
      }
    }

    // Check reviews table for verification status
    console.log('\n📋 Checking reviews table for verification status...');
    const { data: reviewsData, error: reviewsError } = await supabase
      .from('reviews')
      .select('review_id, verification_status, review_date, user_id, doctor_id')
      .order('review_date', { ascending: false })
      .limit(10);

    if (reviewsError) {
      console.log('❌ reviews table error:', reviewsError.message);
    } else {
      console.log(`✅ reviews table exists with ${reviewsData.length} records`);
      if (reviewsData.length > 0) {
        console.log('Recent reviews:', JSON.stringify(reviewsData, null, 2));
      }
    }

    // Check storage bucket
    console.log('\n📋 Checking appointment-verification storage bucket...');
    const { data: bucketData, error: bucketError } = await supabase.storage
      .from('appointment-verification')
      .list('proofs', { limit: 10 });

    if (bucketError) {
      console.log('❌ Storage bucket error:', bucketError.message);
    } else {
      console.log(`✅ Storage bucket exists with ${bucketData.length} files`);
      if (bucketData.length > 0) {
        console.log('Sample files:', bucketData.map(f => f.name));
      }
    }

    // Check if verification_status column exists in reviews table
    console.log('\n📋 Checking reviews table schema...');
    const { data: schemaData, error: schemaError } = await supabase
      .from('reviews')
      .select('*')
      .limit(1);

    if (!schemaError && schemaData.length > 0) {
      console.log('Reviews table columns:', Object.keys(schemaData[0]));
    }

  } catch (error) {
    console.error('❌ Error checking database:', error);
  }
}

checkVerificationTables();
