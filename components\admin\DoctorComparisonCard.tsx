// File: components/admin/DoctorComparisonCard.tsx - FINAL VERIFIED VERSION

'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, <PERSON>Footer, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { approveRegistrationAction, approveNewDoctorAction, rejectRegistrationAction } from '@/actions/admin-actions';

interface DoctorProfile { id?: number; doctor_id?: number; fullname: string; hospital: string; [key: string]: any; }
interface DoctorComparisonCardProps { pendingDoctor: DoctorProfile; existingDoctor: DoctorProfile | null; }

// Define the fields to display in the comparison
const fieldsToCompare: (keyof DoctorProfile)[] = [
  'fullname', 'hospital', 'medical_title', 'specialty', 'subspecialty', 'experience', 'email', 
  'phone_number', 'educational_background', 'board_certifications', 'languages_spoken', 
  'professional_affiliations', 'procedures_performed', 'treatment_services_expertise',
  'personal_biography', 'work_history', 'timings'
];

const DoctorComparisonCard: React.FC<DoctorComparisonCardProps> = ({ pendingDoctor, existingDoctor }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [isProcessed, setIsProcessed] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const performAction = async (action: (id: number) => Promise<{ success: boolean; error?: string | undefined; }>) => {
    setIsProcessing(true);
    setError(null);
    try {
      const regId = pendingDoctor.id || pendingDoctor.doctor_id;
      if (regId === undefined) throw new Error('Pending doctor ID is missing.');
      const result = await action(regId);
      if (!result.success) throw new Error(result.error || 'Action failed.');
      setIsProcessed(true);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleApprove = () => performAction(existingDoctor ? approveRegistrationAction : approveNewDoctorAction);
  const handleReject = () => performAction(rejectRegistrationAction);

  if (isProcessed) return null;

  return (
    <Card className="bg-card text-card-foreground shadow-lg dark:border-gray-700">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-medium">{pendingDoctor.fullname}</CardTitle>
        <Badge variant="secondary" className="bg-yellow-500 text-white">Pending</Badge>
      </CardHeader>
      <CardContent className="space-y-4 pt-4">
        {error && <p className="text-red-500 dark:text-red-400 text-sm font-bold">Error: {error}</p>}
        
        {/* THIS IS THE RESTORED COMPARISON UI */}
        <div className="text-sm">
          {existingDoctor ? (
            <div className="space-y-2">
              <div className="grid grid-cols-5 gap-x-4 font-semibold text-muted-foreground">
                <div className="col-span-2">Field</div>
                <div className="col-span-3">Pending Data (Existing Data)</div>
              </div>
              {fieldsToCompare.map((field) => {
                const pendingValue = String(pendingDoctor[field] || 'N/A');
                const existingValue = String(existingDoctor[field] || 'N/A');
                const isChanged = pendingValue !== existingValue;
                return (
                  <div className="grid grid-cols-5 gap-x-4 items-center mt-1" key={field}>
                    <div className="col-span-2 capitalize font-medium">{field.replace(/_/g, ' ')}</div>
                    <div className="col-span-3">
                      <span className={cn('font-medium', isChanged ? 'text-yellow-600 dark:text-yellow-400' : '')}>
                        {pendingValue}
                      </span>
                      <span className={cn('ml-2', isChanged ? 'text-red-500 dark:text-red-400 line-through' : 'text-muted-foreground')}>
                        ({existingValue})
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="space-y-2">
              <div className="font-semibold text-muted-foreground mb-2">New Doctor Profile Data</div>
              {fieldsToCompare.map((field) => (
                <div key={field} className="grid grid-cols-3 gap-x-4 items-center">
                  <div className="col-span-1 capitalize font-medium">{field.replace(/_/g, ' ')}</div>
                  <div className="col-span-2 font-medium">{String(pendingDoctor[field] || 'N/A')}</div>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-end gap-2 pt-4">
        <Button variant="destructive" onClick={handleReject} disabled={isProcessing}>
          {isProcessing ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <XCircle className="mr-2 h-4 w-4" />} Reject
        </Button>
        <Button onClick={handleApprove} disabled={isProcessing}>
          {isProcessing ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <CheckCircle className="mr-2 h-4 w-4" />} Approve
        </Button>
      </CardFooter>
    </Card>
  );
};

export default DoctorComparisonCard;
