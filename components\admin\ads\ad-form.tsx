"use client"

"use client"

import React, { useState, useEffect, useCallback } from 'react'; // Import useCallback
import { AdPlacementSelector } from '@/app/admin/ads/ad-placement-selector';
import { MultiAdPlacementSelector } from '@/app/admin/ads/multi-ad-placement-selector';
import { Placement } from '@/app/admin/ads/types'; // Import Placement type
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon, Loader2, UploadCloud, XCircle } from "lucide-react"; // Add icons
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Ad, AdInsert, AdUpdate, uploadAdMedia } from '@/actions/ad-actions'; // Import types and upload action
import { useToast } from '@/hooks/use-toast';
// TODO: Fetch specialties for the dropdown
// import { getSpecialties } from '@/actions/specialty-actions';

// Define Zod schema - remove image_url, add media fields later
const adFormSchema = z.object({
  title: z.string().min(3, { message: "Title must be at least 3 characters." }),
  description: z.string().optional(),
  // image_url removed
  target_url: z.string().url({ message: "Please enter a valid target URL." }),
  start_date: z.date({ required_error: "Start date is required." }),
  end_date: z.date().optional().nullable(),
  status: z.enum(['draft', 'scheduled', 'active', 'paused', 'completed']),
  budget: z.preprocess(
    (val) => (val === '' || val === undefined || val === null) ? null : Number(val),
    z.number().positive({ message: "Budget must be a positive number." }).nullable().optional()
  ),
  // Placement can be a string or array of strings for multiple placements
  placement: z.union([
    z.string().min(3, { message: "Placement is required (e.g., homepage:banner)." }),
    z.array(z.string().min(3)).min(1, { message: "At least one placement is required." })
  ]),
  // Add size field
  size: z.string().optional().nullable(), // e.g., '728x90', '300x250'
  target_specialty_id: z.preprocess(
    (val) => (val === '' || val === undefined || val === null || val === 'none') ? null : Number(val),
    z.number().int().positive().nullable().optional() // Keep this preprocess for specialty ID
  ),
  // Keep the transform for validation output
  target_locations: z.string().optional().transform(val => val ? val.split(',').map(s => s.trim()).filter(Boolean) : []),
  // media_url and media_type will be added manually before submitting to the action
  // Temporarily add mediaFile to schema for testing RHF data passing
  mediaFile: z.any().optional(),
  // Add custom position fields (optional strings, allow empty string)
  custom_top: z.string().optional().nullable(),
  custom_bottom: z.string().optional().nullable(),
  custom_left: z.string().optional().nullable(),
  custom_right: z.string().optional().nullable(),
});

// Type for the form's internal state (before Zod transformation)
// Add mediaFile and custom position fields to the form input state
type AdFormInputData = Omit<z.input<typeof adFormSchema>, 'target_locations'> & {
  target_locations?: string;
  mediaFile?: FileList | null; // For the file input
  // Custom position fields are already included via z.input<typeof adFormSchema>
};

// Type for the validated data (after Zod transformation)
type AdFormData = z.output<typeof adFormSchema>;

interface AdFormProps {
  ad?: Ad | null; // Ad data for editing, null/undefined for creating
  onSubmit: (data: AdInsert | AdUpdate) => Promise<void>;
  onCancel: () => void;
  isLoading: boolean;
}

// Mock specialties - replace with actual data fetching
const mockSpecialties = [
    { specialty_id: 1, specialty_name: 'Cardiology' },
    { specialty_id: 2, specialty_name: 'Dermatology' },
    { specialty_id: 3, specialty_name: 'Neurology' },
    { specialty_id: 4, specialty_name: 'Orthopedics' },
];


export function AdForm({ ad, onSubmit, onCancel, isLoading }: AdFormProps) {
  const { toast } = useToast();
  const [specialties, setSpecialties] = useState<{ specialty_id: number; specialty_name: string }[]>(mockSpecialties);
  const [mediaPreviewUrl, setMediaPreviewUrl] = useState<string | null>(null);
  const [existingMediaUrl, setExistingMediaUrl] = useState<string | null>(ad?.media_url ?? null);
  const [existingMediaType, setExistingMediaType] = useState<string | null>(ad?.media_type ?? null);
  const [isUploading, setIsUploading] = useState(false); // State for upload loading

  // TODO: Fetch specialties
  // useEffect(() => {
  //   const fetchSpecialties = async () => {
  //     const { data } = await getSpecialties(); // Assuming a getSpecialties action exists
  //     if (data) setSpecialties(data);
  //   };
  //   fetchSpecialties();
  // }, []);

  // Use AdFormInputData for default values
  // Use AdFormInputData for default values
  const defaultValues: Partial<AdFormInputData> = {
    title: ad?.title ?? '',
    description: ad?.description ?? '',
    // image_url removed
    target_url: ad?.target_url ?? '',
    start_date: ad?.start_date ? new Date(ad.start_date) : new Date(),
    end_date: ad?.end_date ? new Date(ad.end_date) : null,
    status: (ad?.status as AdFormData['status']) ?? 'draft',
    budget: ad?.budget ?? null,
    placement: ad?.placement ?? '', // Placement is now a string
    size: ad?.size ?? null, // Add size default value
    target_specialty_id: ad?.target_specialty_id ?? null,
    target_locations: ad?.target_locations?.join(', ') ?? undefined,
    // Add default values for custom positions
    custom_top: ad?.custom_top ?? '',
    custom_bottom: ad?.custom_bottom ?? '',
    custom_left: ad?.custom_left ?? '',
    custom_right: ad?.custom_right ?? '',
  };

  // Use AdFormInputData with useForm
  const form = useForm<AdFormInputData>({
    resolver: zodResolver(adFormSchema),
    defaultValues,
  });

  // register, control, errors, watch, setValue types are inferred from AdFormInputData
  // register includes mediaFile now
  const { register, handleSubmit, control, formState: { errors }, watch, setValue } = form;

  const startDate = watch("start_date");
  const mediaFileWatch = watch("mediaFile");
  const currentPlacementValue = watch("placement"); // Watch the placement field

  // Memoize the onPlacementsChange handler
  const handlePlacementsChange = useCallback((placements: Placement[]) => {
    // Convert Placement objects back to string array or single string for the form field
    const placementStrings = placements.map(p => {
      let str = `${p.page}:${p.position}`;
      if (p.position === 'custom_position' && p.customPosition) {
         // Filter out empty values before stringifying
                 const definedPositions = Object.entries(p.customPosition)
                   .filter(([_, value]) => value !== '' && value !== null && value !== undefined)
                   .reduce((obj, [key, value]) => {
                     obj[key] = String(value); // Explicitly cast value to string
                     return obj;
                   }, {} as Record<string, string>);
         if (Object.keys(definedPositions).length > 0) {
            str += `:${JSON.stringify(definedPositions)}`;
         }
      }
      return str;
    });

    // Update the RHF 'placement' field
    if (placementStrings.length === 1) {
      setValue("placement", placementStrings[0], { shouldValidate: true }); // Trigger validation
    } else if (placementStrings.length > 1) {
      setValue("placement", placementStrings, { shouldValidate: true }); // Trigger validation
    } else {
      // Handle empty case if necessary, maybe set to null or empty string/array
      setValue("placement", [], { shouldValidate: true }); // Or handle as needed
    }
  }, [setValue]); // Dependency: setValue from RHF


  // Determine if custom position fields should be shown based on the watched value
  const showCustomPositionFields = React.useMemo(() => {
    const placementsValue = Array.isArray(currentPlacementValue) ? currentPlacementValue : [currentPlacementValue];
    // Check if any placement string includes ':custom_position'
    return placementsValue.some(p => typeof p === 'string' && p.includes(':custom_position'));
  }, [currentPlacementValue]);


  // Effect to create/revoke preview URL
  useEffect(() => {
    if (mediaFileWatch && mediaFileWatch.length > 0) {
      const file = mediaFileWatch[0];
      const newPreviewUrl = URL.createObjectURL(file);
      setMediaPreviewUrl(newPreviewUrl);
      setExistingMediaUrl(null); // Clear existing media if new file is selected
      setExistingMediaType(null);

      // Cleanup function to revoke the object URL
      return () => URL.revokeObjectURL(newPreviewUrl);
    } else {
      // No file selected, clear preview
      setMediaPreviewUrl(null);
      // Restore existing media if editing and file is deselected
      if (ad?.media_url) {
          setExistingMediaUrl(ad.media_url);
          setExistingMediaType(ad.media_type);
      }
    }
  }, [mediaFileWatch, ad?.media_url, ad?.media_type]);


  // Handle form submission including upload
  const handleFormSubmit = async (inputData: AdFormInputData) => {
    setIsUploading(true); // Show upload spinner on submit button

    let uploadedMediaUrl: string | null = ad?.media_url ?? null; // Keep existing if no new file
    let uploadedMediaType: string | null = ad?.media_type ?? null;
    let uploadError: any = null;

    // 1. Upload file if selected
    console.log("AdForm: Checking for media file in inputData:", inputData.mediaFile); // Log file check
    if (inputData.mediaFile && inputData.mediaFile.length > 0) {
      console.log("AdForm: Media file found, preparing FormData for upload."); // Log file found
      const fileData = new FormData();
      fileData.append('mediaFile', inputData.mediaFile[0]);

      console.log("AdForm: Calling uploadAdMedia action..."); // Log before calling action
      const uploadResult = await uploadAdMedia(fileData);
      // Log the *entire* result object structure immediately after return
      console.log("AdForm: Raw uploadResult received from action:", JSON.stringify(uploadResult, null, 2));

      if (uploadResult && uploadResult.error) { // Add null check for uploadResult
        console.log("AdForm: uploadResult.error exists. Assigning to uploadError.");
        uploadError = uploadResult.error; // Assign the nested error object
        uploadedMediaUrl = null; // Ensure no URL is saved if upload fails
        uploadedMediaType = null;
      } else {
        uploadedMediaUrl = uploadResult.mediaUrl;
        uploadedMediaType = uploadResult.mediaType;
      }
    } else if (!ad?.id && !inputData.mediaFile) {
        // If creating a new ad and no file is selected, maybe require it?
        // Or allow creation without media? For now, allow.
    } else if (ad?.id && !inputData.mediaFile && !existingMediaUrl) {
        // If editing and file was removed (no preview, no existing), set media to null
        uploadedMediaUrl = null;
        uploadedMediaType = null;
    }


    setIsUploading(false); // Hide upload spinner
    // Check uploadError itself before using it
    if (uploadError) {
      console.log("AdForm: uploadError object structure:", JSON.stringify(uploadError, null, 2));
      // Use optional chaining and provide a default message
      const displayMessage = uploadError?.message || "Failed to upload media file. Unknown error structure.";
      console.error("Upload error for toast:", displayMessage); // Log the message being sent to toast
      toast({
        title: "Upload Error",
        description: displayMessage,
        variant: "destructive",
      });
      return; // Stop submission if upload failed
    }

    // 2. Prepare data for create/update action
    // Cast the received data to the expected transformed type (AdFormData)
    const validatedData = inputData as unknown as AdFormData;

    // Prepare base data common to both Insert and Update
    // Include placement here; the action will handle conversion.
    const baseData = {
        ...validatedData, // Spread all validated data
        start_date: format(validatedData.start_date, 'yyyy-MM-dd'),
        end_date: validatedData.end_date ? format(validatedData.end_date, 'yyyy-MM-dd') : null,
        budget: validatedData.budget ?? null,
        size: validatedData.size || null,
        target_specialty_id: validatedData.target_specialty_id ?? null,
        media_url: uploadedMediaUrl,
        media_type: uploadedMediaType,
    };

    // Remove mediaFile as it's not a DB column
    delete (baseData as any).mediaFile;

    let submissionData: AdInsert | AdUpdate;

    // If editing, create AdUpdate object with id
    if (ad?.id) {
        submissionData = {
            ...baseData,
            id: ad.id, // Add the required id for AdUpdate
        } as AdUpdate; // Type assertion
    } else {
        // Otherwise, create AdInsert object
        submissionData = baseData as AdInsert; // Type assertion
    }

    // Improved logging
    console.log("AdForm: Data being submitted:", JSON.stringify(submissionData, null, 2));

    // 3. Call the onSubmit prop (createAd or updateAd)
    onSubmit(submissionData).catch((error) => {
        console.error("Form submission error:", error);
        toast({
            title: "Submission Error",
            description: "Failed to save the ad. Please try again.",
            variant: "destructive",
        });
    });
  };

  // Function to clear the selected file and preview
  const clearMediaSelection = () => {
      setValue("mediaFile", null); // Clear file input in form state
      setMediaPreviewUrl(null);
      setExistingMediaUrl(null); // Also clear existing media display
      setExistingMediaType(null);
  };

  // New intermediate function to log data directly from RHF
  const processFormData = (data: AdFormInputData) => {
      console.log("AdForm: RHF handleSubmit received data:", data); // Log data received directly from RHF
      // Now call the original submission logic with this data
      handleFormSubmit(data);
  };


  return (
    // Use the new processFormData function in handleSubmit
    <form onSubmit={handleSubmit(processFormData)} className="space-y-4">
      {/* Title */}
      <div>
        <Label htmlFor="title">Title</Label>
        <Input id="title" {...register("title")} />
        {errors.title && <p className="text-red-500 text-sm mt-1">{errors.title.message}</p>}
      </div>

      {/* Description */}
      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea id="description" {...register("description")} />
        {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>}
      </div>

      {/* Media Upload */}
      <div>
        <Label htmlFor="mediaFile">Ad Media (Image/Video)</Label>
        <Input
          id="mediaFile"
          type="file"
          accept="image/*,video/*"
          {...register("mediaFile")}
          className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary/10 file:text-primary hover:file:bg-primary/20"
        />
        {/* Preview Area */}
        {(mediaPreviewUrl || existingMediaUrl) && (
          <div className="mt-4 relative w-full max-w-sm border rounded p-2">
             <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute top-1 right-1 h-6 w-6 p-0 rounded-full bg-background/80/50 hover:bg-red-500/80 text-foreground"
                onClick={clearMediaSelection}
                aria-label="Remove media"
             >
                <XCircle className="h-4 w-4" />
             </Button>
            { (mediaPreviewUrl && mediaFileWatch?.[0]?.type.startsWith('image')) || (existingMediaUrl && existingMediaType === 'image') ? (
              <img src={mediaPreviewUrl ?? existingMediaUrl!} alt="Ad preview" className="max-w-full h-auto rounded" />
            ) : (mediaPreviewUrl && mediaFileWatch?.[0]?.type.startsWith('video')) || (existingMediaUrl && existingMediaType === 'video') ? (
              <video controls src={mediaPreviewUrl ?? existingMediaUrl!} className="max-w-full h-auto rounded">
                Your browser does not support the video tag.
              </video>
            ) : null }
          </div>
        )}
      </div>

      {/* Target URL */}
      <div>
        <Label htmlFor="target_url">Target URL</Label>
        <Input id="target_url" {...register("target_url")} placeholder="https://..." />
        {errors.target_url && <p className="text-red-500 text-sm mt-1">{errors.target_url.message}</p>}
      </div>

      {/* Multi-Placement Selector */}
      <div>
        <Label htmlFor="placement">Ad Placement</Label>
        <div className="mt-2">
          <MultiAdPlacementSelector
            // Pass the memoized handler
            onPlacementsChange={handlePlacementsChange}
            // Parse initial placements from defaultValues (which should be string/string[])
            initialPlacements={Array.isArray(defaultValues.placement)
              ? defaultValues.placement
              : (typeof defaultValues.placement === 'string' && defaultValues.placement)
                ? [defaultValues.placement]
                : ['home:banner']} // Fallback if undefined/empty
          />
        </div>
        {errors.placement && <p className="text-red-500 text-sm mt-1">{errors.placement.message}</p>}
      </div>

      {/* Conditional Custom Position Fields */}
      {showCustomPositionFields && (
        <div className="p-4 border rounded bg-background/90/30 space-y-3">
           <h4 className="text-sm font-medium text-muted-green mb-2">Custom Positioning (CSS Values)</h4>
           <div className="grid grid-cols-2 gap-4">
             <div>
               <Label htmlFor="custom_top">Top (e.g., 10%, 20px)</Label>
               <Input id="custom_top" {...register("custom_top")} placeholder="e.g., 10%" />
             </div>
             <div>
               <Label htmlFor="custom_bottom">Bottom (e.g., 10%, 20px)</Label>
               <Input id="custom_bottom" {...register("custom_bottom")} placeholder="e.g., 20px" />
             </div>
             <div>
               <Label htmlFor="custom_left">Left (e.g., 10%, 20px)</Label>
               <Input id="custom_left" {...register("custom_left")} placeholder="e.g., 5%" />
             </div>
             <div>
               <Label htmlFor="custom_right">Right (e.g., 10%, 20px)</Label>
               <Input id="custom_right" {...register("custom_right")} placeholder="e.g., 50px" />
             </div>
           </div>
           <p className="text-xs text-muted-green">Enter valid CSS values (like '10px', '5%', 'auto'). Leave blank if not needed.</p>
        </div>
      )}

       {/* Size (Select) */}
      <div>
        <Label htmlFor="size">Ad Size (Optional)</Label>
         <Select
          onValueChange={(value) => setValue("size", value === 'none' ? null : value)}
          defaultValue={defaultValues.size ?? 'none'}
          {...register("size")} // Register size with RHF
        >
          <SelectTrigger>
            <SelectValue placeholder="Select size (optional)" />
          </SelectTrigger>
          <SelectContent>
             <SelectItem value="none">-- Select Size --</SelectItem>
             {/* Common Ad Sizes */}
             <SelectItem value="728x90">728x90 (Leaderboard)</SelectItem>
             <SelectItem value="300x250">300x250 (Medium Rectangle)</SelectItem>
             <SelectItem value="160x600">160x600 (Wide Skyscraper)</SelectItem>
             <SelectItem value="468x60">468x60 (Banner)</SelectItem>
             <SelectItem value="300x600">300x600 (Half Page)</SelectItem>
             <SelectItem value="320x50">320x50 (Mobile Leaderboard)</SelectItem>
             {/* Add other sizes if needed */}
          </SelectContent>
        </Select>
        {/* No error display needed for optional field */}
      </div>

      {/* Dates */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="start_date">Start Date</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !watch("start_date") && "text-muted-green"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {watch("start_date") ? format(watch("start_date"), "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={watch("start_date")}
                onSelect={(date) => setValue("start_date", date as Date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
          {errors.start_date && <p className="text-red-500 text-sm mt-1">{errors.start_date.message}</p>}
        </div>
        <div>
          <Label htmlFor="end_date">End Date (Optional)</Label>
           <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !watch("end_date") && "text-muted-green"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {watch("end_date") ? format(watch("end_date") as Date, "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                // Pass undefined instead of null to the selected prop
                selected={watch("end_date") || undefined}
                onSelect={(date) => setValue("end_date", date)}
                disabled={(date) => date < startDate} // Disable dates before start date
              />
            </PopoverContent>
          </Popover>
          {errors.end_date && <p className="text-red-500 text-sm mt-1">{errors.end_date.message}</p>}
        </div>
      </div>

      {/* Status */}
      <div>
        <Label htmlFor="status">Status</Label>
        {/* Cast value in onValueChange */}
        <Select onValueChange={(value) => setValue("status", value as AdFormInputData['status'])} defaultValue={defaultValues.status}>
          <SelectTrigger>
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="scheduled">Scheduled</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="paused">Paused</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
          </SelectContent>
        </Select>
        {errors.status && <p className="text-red-500 text-sm mt-1">{errors.status.message}</p>}
      </div>

      {/* Budget */}
      <div>
        <Label htmlFor="budget">Budget ($)</Label>
        <Input id="budget" type="number" step="0.01" {...register("budget")} placeholder="e.g., 500.00" />
        {errors.budget && <p className="text-red-500 text-sm mt-1">{errors.budget.message}</p>}
      </div>

      {/* REMOVE Old Placement Select */}
      {/*
      <div>
        <Label htmlFor="placement">Placement</Label>
        <Select ... > ... </Select>
        {errors.placement && <p ... >{errors.placement.message}</p>}
      </div>
      */}

      {/* Target Specialty */}
      <div>
        <Label htmlFor="target_specialty_id">Target Specialty (Optional)</Label>
         {/* Cast value in onValueChange */}
        <Select onValueChange={(value) => setValue("target_specialty_id", value === 'none' ? null : Number(value))} defaultValue={defaultValues.target_specialty_id?.toString() ?? 'none'}>
          <SelectTrigger>
            <SelectValue placeholder="Select specialty" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">None</SelectItem>
            {specialties.map(spec => (
              <SelectItem key={spec.specialty_id} value={spec.specialty_id.toString()}>
                {spec.specialty_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.target_specialty_id && <p className="text-red-500 text-sm mt-1">{errors.target_specialty_id.message}</p>}
      </div>

      {/* Target Locations */}
      <div>
        <Label htmlFor="target_locations">Target Locations (Optional, comma-separated)</Label>
        <Input id="target_locations" {...register("target_locations")} placeholder="e.g., New York, London, Tokyo" />
        {errors.target_locations && <p className="text-red-500 text-sm mt-1">{errors.target_locations.message}</p>}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading || isUploading}>
          {(isLoading || isUploading) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isUploading ? 'Uploading...' : (ad ? 'Update Ad' : 'Create Ad')}
        </Button>
      </div>
    </form>
  );
}
