"use client"

import { motion } from "framer-motion"
import Image from "next/image" // Import next/image
import { Ad } from "@/actions/ad-actions"

interface AdDisplayProps {
  ad: Ad | Ad[]
  variant: 'banner' | 'sidebar'
  showMultiple?: boolean
}

export function AdDisplay({ ad, variant, showMultiple = false }: AdDisplayProps) {
  // Convert single ad to array for consistent handling
  const ads = Array.isArray(ad) ? ad : [ad];

  // If no ads or empty array, return null
  if (!ads || ads.length === 0) return null;

  // Different wrapper classes for banner vs sidebar
  const wrapperClassName = variant === 'banner'
    ? "flex flex-col items-center w-full" // Banner is centered and full width
    : "sticky top-4 w-full flex flex-col gap-4"; // Sidebar is sticky and takes its container width

  // Determine how many ads to show
  const adsToShow = showMultiple ? ads : [ads[0]];

  return (
    <div className={wrapperClassName}>
      {adsToShow.map((adItem, index) => {
        const [adWidth, adHeight] = adItem.size?.split('x').map(Number) ?? [undefined, undefined];

        // Different styling for banner vs sidebar
        const containerStyle = {
          width: variant === 'banner'
            ? (adWidth ? `${adWidth}px` : '100%')
            : (adWidth ? `${adWidth}px` : '300px'), // Default sidebar width if not specified
          maxWidth: variant === 'banner' ? '100%' : '300px' // Limit sidebar width
        };

        const mediaStyle = {
          height: adHeight ? `${adHeight}px` : 'auto',
          width: '100%',
          objectFit: 'cover' as const
        };

        const initial = variant === 'banner' ? { opacity: 0, y: 20 } : { opacity: 0, x: 20 };
        const animate = variant === 'banner' ? { opacity: 1, y: 0 } : { opacity: 1, x: 0 };

        return (
          <div key={adItem.id || index} className={`${variant === 'banner' ? 'mb-12' : 'mb-4'}`}>
            <motion.div
              className="bg-gradient-to-br from-background to-background border border-primary/30 rounded-lg shadow-lg overflow-hidden mx-auto"
              style={containerStyle}
              initial={initial}
              animate={animate}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <a href={adItem.target_url ?? '#'} target="_blank" rel="noopener noreferrer" className="block group">
                {adItem.media_url && adItem.media_type === 'image' && (
                  adWidth && adHeight ? (
                    <Image
                      src={adItem.media_url}
                      alt={adItem.title ?? 'Advertisement'}
                      width={adWidth}
                      height={adHeight}
                      style={{ objectFit: 'cover', width: '100%', height: 'auto' }} // Maintain aspect ratio, fit container
                      className="group-hover:opacity-90 transition-opacity"
                    />
                  ) : (
                    // Fallback if dimensions are missing
                    <img
                      src={adItem.media_url}
                      alt={adItem.title ?? 'Advertisement'}
                      style={mediaStyle}
                      className="group-hover:opacity-90 transition-opacity"
                    />
                  )
                )}
                {adItem.media_url && adItem.media_type === 'video' && (
                  <video src={adItem.media_url} controls={false} muted autoPlay loop style={mediaStyle} className="group-hover:opacity-90 transition-opacity">
                    Your browser does not support the video tag.
                  </video>
                )}
                <div className="p-3">
                  <h3 className="text-md font-semibold text-primary mb-1 group-hover:underline">{adItem.title ?? 'Sponsored Content'}</h3>
                  {adItem.description && (<p className="text-xs text-muted-green line-clamp-2">{adItem.description}</p>)}
                  <p className="text-xs text-muted-green mt-1">Advertisement</p>
                </div>
              </a>
            </motion.div>
          </div>
        );
      })}
    </div>
  );
}
