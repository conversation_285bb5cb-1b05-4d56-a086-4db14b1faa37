import {
  getHomepageBannerAds,
  getHomepageSidebarAds,
  getHomepageSideLeftAds,
  getHomepageSideRightAds,
  getHomepageBottomAds,
  getHomepageInContentAds,
  getAboutUsBannerAds,
  getAboutUsSidebarAds,
  getStandingsBannerAds,
  getStandingsSidebarAds,
  getDivisionsBannerAds,
  getDivisionsSidebarAds,
  getSpecialtiesBannerAds,
  getSpecialtiesSidebarAds,
  getTeamsBannerAds,
  getTeamsSidebarAds,
  getHeadToHeadBannerAds,
  getHeadToHeadSidebarAds,
  getDoctorProfileTopAds,
  getDoctorProfileSidebarAds,
  getDoctorProfileBottomAds,
  getRatingsBannerAds,
  getRatingsSidebarAds
} from '@/actions/ad-actions'

// Map of page names to their ad fetching functions
export const adFetchFunctions = {
  home: {
    banner: getHomepageBannerAds,
    sidebar: getHomepageSidebarAds,
    'side-left': getHomepageSideLeftAds,
    'side-right': getHomepageSideRightAds,
    bottom: getHomepageBottomAds,
    'in-content': getHomepageInContentAds,
    // For positions without specific functions, use the closest match
    'custom': getHomepageBannerAds
  },
  about: {
    banner: getAboutUsBannerAds,
    sidebar: getAboutUsSidebarAds,
    // Map other positions to existing functions
    'side-left': getAboutUsSidebarAds,
    'side-right': getAboutUsSidebarAds,
    'bottom': getAboutUsBannerAds,
    'in-content': getAboutUsBannerAds,
    'custom': getAboutUsBannerAds
  },
  standings: {
    banner: getStandingsBannerAds,
    sidebar: getStandingsSidebarAds,
    // Map other positions to existing functions
    'side-left': getStandingsSidebarAds,
    'side-right': getStandingsSidebarAds,
    'bottom': getStandingsBannerAds,
    'in-content': getStandingsBannerAds,
    'custom': getStandingsBannerAds
  },
  divisions: {
    banner: getDivisionsBannerAds,
    sidebar: getDivisionsSidebarAds,
    // Map other positions to existing functions
    'side-left': getDivisionsSidebarAds,
    'side-right': getDivisionsSidebarAds,
    'bottom': getDivisionsBannerAds,
    'in-content': getDivisionsBannerAds,
    'custom': getDivisionsBannerAds
  },
  specialties: {
    banner: getSpecialtiesBannerAds,
    sidebar: getSpecialtiesSidebarAds,
    // Map other positions to existing functions
    'side-left': getSpecialtiesSidebarAds,
    'side-right': getSpecialtiesSidebarAds,
    'bottom': getSpecialtiesBannerAds,
    'in-content': getSpecialtiesBannerAds,
    'custom': getSpecialtiesBannerAds
  },
  teams: {
    banner: getTeamsBannerAds,
    sidebar: getTeamsSidebarAds,
    // Map other positions to existing functions
    'side-left': getTeamsSidebarAds,
    'side-right': getTeamsSidebarAds,
    'bottom': getTeamsBannerAds,
    'in-content': getTeamsBannerAds,
    'custom': getTeamsBannerAds
  },
  'head-to-head': {
    banner: getHeadToHeadBannerAds,
    sidebar: getHeadToHeadSidebarAds,
    'side-left': getHeadToHeadSidebarAds,
    'side-right': getHeadToHeadSidebarAds,
    'bottom': getHeadToHeadBannerAds,
    'in-content': getHeadToHeadBannerAds,
    'custom': getHeadToHeadBannerAds
  },
  'doctor-profile': {
    banner: getDoctorProfileTopAds,
    sidebar: getDoctorProfileSidebarAds,
    bottom: getDoctorProfileBottomAds,
    // Map other positions to existing functions
    'side-left': getDoctorProfileSidebarAds,
    'side-right': getDoctorProfileSidebarAds,
    'in-content': getDoctorProfileTopAds,
    'custom': getDoctorProfileTopAds
  },
  ratings: {
    banner: getRatingsBannerAds,
    sidebar: getRatingsSidebarAds,
    // Map other positions to existing functions
    'side-left': getRatingsSidebarAds,
    'side-right': getRatingsSidebarAds,
    'bottom': getRatingsBannerAds,
    'in-content': getRatingsBannerAds,
    'custom': getRatingsBannerAds
  }
}

// Helper function to get the appropriate ad fetch function for a page and position
export function getAdFetchFunction(pageName: string, position: string) {
  // Map page name variations to standard page names
  const pageMap: Record<string, string> = {
    'home': 'home',
    'homepage': 'home',
    'home-page': 'home',
    'about': 'about',
    'about-us': 'about',
    'aboutus': 'about',
    'standings': 'standings',
    'standing': 'standings',
    'divisions': 'divisions',
    'division': 'divisions',
    'specialties': 'specialties',
    'specialty': 'specialties',
    'teams': 'teams',
    'team': 'teams',
    'head-to-head': 'head-to-head',
    'headtohead': 'head-to-head',
    'head_to_head': 'head-to-head',
    'doctor-profile': 'doctor-profile',
    'doctorprofile': 'doctor-profile',
    'doctor_profile': 'doctor-profile',
    'ratings': 'ratings',
    'rating': 'ratings'
  };

  // Map position variations to standard positions
  const positionMap: Record<string, string> = {
    'banner': 'banner',
    'top': 'banner',
    'header': 'banner',
    'sidebar': 'sidebar',
    'side': 'sidebar',
    'side-left': 'side-left',
    'left': 'side-left',
    'side-right': 'side-right',
    'right': 'side-right',
    'bottom': 'bottom',
    'footer': 'bottom',
    'in-content': 'in-content',
    'content': 'in-content',
    'custom': 'custom'
  };

  // Normalize the page name and position
  const normalizedPage = pageMap[pageName] || pageName;
  const normalizedPosition = positionMap[position] || position;

  console.log(`[getAdFetchFunction] Getting ad fetch function for page: ${pageName} (normalized: ${normalizedPage}), position: ${position} (normalized: ${normalizedPosition})`);

  const pageFunctions = adFetchFunctions[normalizedPage as keyof typeof adFetchFunctions]
  if (!pageFunctions) {
    console.warn(`No ad fetch functions defined for page: ${pageName} (${normalizedPage})`)
    return null
  }

  // Get the function for the normalized position - no fallbacks to prevent duplication
  const fetchFunction = pageFunctions[normalizedPosition as keyof typeof pageFunctions]

  if (!fetchFunction) {
    console.warn(`No ad fetch function defined for position: ${position} (${normalizedPosition}) on page: ${pageName} (${normalizedPage})`)
    return null
  }

  console.log(`[getAdFetchFunction] Found fetch function for ${normalizedPage}:${normalizedPosition}`);
  return fetchFunction
}
