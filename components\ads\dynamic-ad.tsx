"use client"

import React, { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ExternalLink, Star, Calendar, Stethoscope, Info } from "lucide-react"
import { Ad } from "@/actions/ad-actions"

// SafeImage component to handle image loading errors
function SafeImage({ src, alt, ...props }: React.ComponentProps<typeof Image>) {
  const [imgSrc, setImgSrc] = useState(src)
  
  return (
    <Image 
      {...props}
      src={imgSrc || "/placeholder.svg"}
      alt={alt}
      onError={() => setImgSrc("/placeholder.svg")}
    />
  )
}

interface DynamicAdProps {
  ad: Ad | null
  adType: 'banner' | 'sidebar' | 'side-left' | 'side-right' | 'bottom' | 'in-content'
  fallback?: React.ReactNode
}

export function DynamicAd({ ad, adType, fallback }: DynamicAdProps) {
  // If no ad is provided, show fallback or null
  if (!ad) {
    return fallback || null
  }
  
  // Common props for buttons
  const buttonProps = {
    href: ad.target_url || "#",
    target: "_blank",
    rel: "noopener noreferrer"
  }
  
  // Render banner ad (horizontal, large)
  if (adType === 'banner') {
    return (
      <Card className="w-full overflow-hidden bg-gradient-to-r from-primary/30 to-background/30 border border-primary/20">
        <CardContent className="p-0">
          <div className="flex flex-col md:flex-row items-center">
            {ad.media_url && (
              <div className="relative w-full md:w-1/3 h-48 md:h-full">
                <SafeImage 
                  src={ad.media_url} 
                  alt={ad.title} 
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-r from-transparent to-background/80 md:from-transparent md:to-background/80" />
              </div>
            )}
            
            <div className="p-6 flex-1">
              <Badge variant="outline" className="mb-2 bg-primary/20 text-foreground border-primary/30">
                {ad.media_type || 'Sponsored'}
              </Badge>
              <h3 className="text-xl md:text-2xl font-bold text-foreground mb-2">
                {ad.title}
              </h3>
              {ad.description && (
                <p className="text-foreground/80 mb-4 text-sm md:text-base">
                  {ad.description}
                </p>
              )}
              
              <Button asChild className="group">
                <Link {...buttonProps}>
                  Learn More
                  <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform" />
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  // Render sidebar ad (vertical, medium)
  if (adType === 'sidebar') {
    return (
      <Card className="overflow-hidden border border-primary/20 bg-background/40">
        {ad.media_url && (
          <div className="relative h-40">
            <SafeImage 
              src={ad.media_url} 
              alt={ad.title} 
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-background to-transparent" />
            <Badge className="absolute top-2 right-2 bg-primary text-foreground">
              {ad.media_type || 'Sponsored'}
            </Badge>
          </div>
        )}
        
        <CardContent className="p-4">
          <h4 className="text-lg font-semibold text-foreground mb-2 flex items-center gap-2">
            <Stethoscope className="h-5 w-5 text-primary" />
            {ad.title}
          </h4>
          {ad.description && (
            <p className="text-foreground/70 text-sm mb-4">
              {ad.description}
            </p>
          )}
          <Button variant="default" size="sm" className="w-full" asChild>
            <Link {...buttonProps}>
              Learn More
            </Link>
          </Button>
        </CardContent>
      </Card>
    )
  }
  
  // Render side-left or side-right ad (narrow vertical)
  if (adType === 'side-left' || adType === 'side-right') {
    return (
      <Card className="overflow-hidden border border-primary/20 bg-background/40">
        {ad.media_url && (
          <div className="relative h-56">
            <SafeImage 
              src={ad.media_url} 
              alt={ad.title} 
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-background via-background/50 to-transparent" />
          </div>
        )}
        
        <CardContent className="p-4">
          <Badge className="mb-2 bg-primary text-foreground">
            {ad.media_type || 'Sponsored'}
          </Badge>
          <h4 className="text-base font-semibold text-foreground mb-2">
            {ad.title}
          </h4>
          {ad.description && (
            <p className="text-foreground/70 text-xs mb-4">
              {ad.description}
            </p>
          )}
          <Button variant="outline" size="sm" className="w-full bg-primary/10 border-primary/20 hover:bg-primary/20 text-xs" asChild>
            <Link {...buttonProps}>
              Learn More
            </Link>
          </Button>
        </CardContent>
      </Card>
    )
  }
  
  // Render bottom ad or in-content ad (similar to banner but can be styled differently)
  if (adType === 'bottom' || adType === 'in-content') {
    return (
      <Card className="w-full overflow-hidden bg-gradient-to-r from-background/30 to-primary/30 border border-primary/20">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row items-center gap-4">
            {ad.media_url && (
              <div className="relative w-full sm:w-1/4 h-32 rounded-md overflow-hidden">
                <SafeImage 
                  src={ad.media_url} 
                  alt={ad.title} 
                  fill
                  className="object-cover"
                />
              </div>
            )}
            
            <div className="flex-1 text-center sm:text-left">
              <Badge variant="outline" className="mb-2 bg-primary/20 text-foreground border-primary/30">
                {ad.media_type || 'Sponsored'}
              </Badge>
              <h3 className="text-lg font-bold text-foreground mb-2">
                {ad.title}
              </h3>
              {ad.description && (
                <p className="text-foreground/80 mb-4 text-sm max-w-md">
                  {ad.description}
                </p>
              )}
              
              <Button asChild size="sm" variant="default">
                <Link {...buttonProps}>
                  Learn More
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  // Default return for any unhandled ad types
  return null;
} 