"use client"

import { motion } from "framer-motion"
import Image from "next/image" // Import next/image
import { Ad } from "@/actions/ad-actions"
import { useState, useEffect } from "react"

interface FloatingAdDisplayProps {
  ad: Ad | Ad[]
  position: {
    top?: number | string
    bottom?: number | string
    left?: number | string
    right?: number | string
  }
  showMultiple?: boolean
  maxWidth?: number | string
  zIndex?: number
}

export function FloatingAdDisplay({
  ad,
  position,
  showMultiple = false,
  maxWidth = 200, // Increased default width for better visibility
  zIndex = 10
}: FloatingAdDisplayProps) {
  // Convert single ad to array for consistent handling
  const ads = Array.isArray(ad) ? ad : [ad];
  const [isVisible, setIsVisible] = useState(true);

  // If no ads or empty array, return null
  if (!ads || ads.length === 0) return null;

  // Determine how many ads to show
  const adsToShow = showMultiple ? ads : [ads[0]];

  // Position styles
  const positionStyles = {
    position: 'fixed' as const,
    ...position,
    zIndex,
    display: isVisible ? 'flex' : 'none',
    flexDirection: 'column' as const,
    gap: '20px',
    maxHeight: '80vh',
    overflowY: 'auto' as const
  };

  // Determine animation direction based on position
  const getAnimationDirection = () => {
    if (position.left !== undefined) return { x: -20 };
    if (position.right !== undefined) return { x: 20 };
    if (position.top !== undefined) return { y: -20 };
    if (position.bottom !== undefined) return { y: 20 };
    return { x: -20 }; // Default
  };

  const initial = { opacity: 0, ...getAnimationDirection() };
  const animate = {
    opacity: 1,
    x: 'x' in getAnimationDirection() ? 0 : undefined,
    y: 'y' in getAnimationDirection() ? 0 : undefined
  };

  return (
    <div style={positionStyles}>
      <div className="relative">
        {/* Close button */}
        <button
          onClick={() => setIsVisible(false)}
          className="absolute -top-2 -right-2 bg-primary text-foreground rounded-full w-6 h-6 flex items-center justify-center z-20 text-sm font-bold shadow-md hover:bg-primary/80 transition-colors"
          aria-label="Close advertisement"
        >
          ×
        </button>

        {adsToShow.map((adItem, index) => {
          const [adWidth, adHeight] = adItem.size?.split('x').map(Number) ?? [undefined, undefined];

          // Styling for floating ads
          const containerStyle = {
            width: adWidth ? `${adWidth}px` : typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth,
            maxWidth: typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth
          };

          const mediaStyle = {
            height: adHeight ? `${adHeight}px` : 'auto',
            width: '100%',
            objectFit: 'cover' as const
          };

          return (
            <div key={adItem.id || index} className="mb-4">
              <motion.div
                className="bg-gradient-to-br from-background to-background border-2 border-primary/50 rounded-lg shadow-xl overflow-hidden hover:border-primary/80 transition-all"
                style={containerStyle}
                initial={initial}
                animate={animate}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ scale: 1.03, boxShadow: '0 0 15px rgba(0, 255, 128, 0.3)' }}
              >
                <a href={adItem.target_url ?? '#'} target="_blank" rel="noopener noreferrer" className="block group">
                  {adItem.media_url && adItem.media_type === 'image' && (
                    adWidth && adHeight ? (
                      <Image
                        src={adItem.media_url}
                        alt={adItem.title ?? 'Advertisement'}
                        width={adWidth}
                        height={adHeight}
                        style={{ objectFit: 'cover', width: '100%', height: 'auto' }} // Maintain aspect ratio, fit container
                        className="group-hover:opacity-90 transition-opacity"
                      />
                    ) : (
                      // Fallback if dimensions are missing
                      <img
                        src={adItem.media_url}
                        alt={adItem.title ?? 'Advertisement'}
                        style={mediaStyle}
                        className="group-hover:opacity-90 transition-opacity"
                      />
                    )
                  )}
                  {adItem.media_url && adItem.media_type === 'video' && (
                    <video src={adItem.media_url} controls={false} muted autoPlay loop style={mediaStyle} className="group-hover:opacity-90 transition-opacity">
                      Your browser does not support the video tag.
                    </video>
                  )}
                  <div className="p-2">
                    <h3 className="text-sm font-semibold text-primary mb-1 group-hover:underline">{adItem.title ?? 'Sponsored'}</h3>
                    {adItem.description && (<p className="text-xs text-muted-green line-clamp-1">{adItem.description}</p>)}
                    <p className="text-xs text-muted-green mt-1">Ad</p>
                  </div>
                </a>
              </motion.div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
