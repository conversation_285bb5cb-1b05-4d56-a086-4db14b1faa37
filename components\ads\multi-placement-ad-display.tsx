"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { motion } from "framer-motion"
import { Ad } from "@/actions/ad-actions"
import { TestAdDisplay } from "./test-ad-display"

interface MultiPlacementAdDisplayProps {
  ad: Ad
  currentPage: string
  position?: string
  showTestAd?: boolean
  maxWidth?: number | string
  fallbackColor?: string
  fallbackTitle?: string
}

/**
 * A component that displays an ad in multiple positions based on its placement configuration
 * Only shows the ad if it has a placement matching the current page and position
 */
export function MultiPlacementAdDisplay({
  ad,
  currentPage,
  position,
  showTestAd = false,
  maxWidth = 728,
  fallbackColor = "purple",
  fallbackTitle = "Advertisement"
}: MultiPlacementAdDisplayProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [matchingPlacements, setMatchingPlacements] = useState<string[]>([]);

  // Find matching placements for the current page and position
  useEffect(() => {
    // Handle both single placement and array of placements
    const placements = Array.isArray(ad.placement)
      ? ad.placement
      : ad.placements || [ad.placement];

    // Filter placements that match the current page
    const matching = placements.filter(placement => {
      const [page, pos] = placement.split(':');

      // If position is specified, match both page and position
      if (position) {
        return page === currentPage && pos === position;
      }

      // Otherwise, just match the page
      return page === currentPage;
    });

    setMatchingPlacements(matching);
  }, [ad, currentPage, position]);

  // If no matching placements or not visible, don't render anything
  if (!isVisible || matchingPlacements.length === 0) return null;

  // If we're showing test ads or have no real ads, show test ads
  if (showTestAd) {
    return (
      <TestAdDisplay
        position={{ top: '0', left: '50%', transform: 'translateX(-50%)' }}
        title={fallbackTitle}
        color={fallbackColor}
        width={typeof maxWidth === 'number' ? maxWidth : 300}
        height={90}
        variant="banner" // Add default variant for test ad
      />
    );
  }

  // Get position details from the first matching placement
  const getPositionDetails = (placementString: string) => {
    const [_, pos, customPos] = placementString.split(':');

    // Default styles for common positions
    switch (pos) {
      case 'banner':
        return {
          position: 'relative' as const,
          margin: '0 auto',
          width: '100%',
          maxWidth: typeof maxWidth === 'string' ? maxWidth : `${maxWidth}px`,
          zIndex: 5
        };
      case 'sidebar':
        return {
          position: 'sticky' as const,
          top: '1rem',
          width: '100%',
          maxWidth: '300px',
          zIndex: 5
        };
      case 'side-left':
      case 'side-right':
        return {
          position: 'fixed' as const,
          top: '180px',
          [pos === 'side-left' ? 'left' : 'right']: '20px',
          width: '160px',
          zIndex: 10
        };
      case 'bottom':
        return {
          position: 'relative' as const,
          margin: '0 auto',
          width: '100%',
          maxWidth: typeof maxWidth === 'string' ? maxWidth : `${maxWidth}px`,
          zIndex: 5
        };
      case 'in-content':
        return {
          position: 'relative' as const,
          margin: '2rem auto',
          width: '100%',
          maxWidth: typeof maxWidth === 'string' ? maxWidth : `${maxWidth}px`,
          zIndex: 5
        };
      case 'custom':
        // Parse custom position if available
        if (customPos) {
          try {
            const customPosition = JSON.parse(customPos);
            return {
              position: 'absolute' as const,
              ...customPosition,
              zIndex: 10
            };
          } catch (e) {
            console.error('Error parsing custom position:', e);
          }
        }
        // Fall back to default if custom parsing fails
        return {
          position: 'relative' as const,
          margin: '0 auto',
          width: '100%',
          maxWidth: typeof maxWidth === 'string' ? maxWidth : `${maxWidth}px`,
          zIndex: 5
        };
      default:
        return {
          position: 'relative' as const,
          margin: '0 auto',
          width: '100%',
          maxWidth: typeof maxWidth === 'string' ? maxWidth : `${maxWidth}px`,
          zIndex: 5
        };
    }
  };

  // Get the first matching placement for positioning
  const positionStyles = getPositionDetails(matchingPlacements[0]);

  // Animation properties
  const initial = { opacity: 0, y: 20 };
  const animate = { opacity: 1, y: 0 };

  // Media styles based on ad size
  const [adWidth, adHeight] = ad.size?.split('x').map(Number) ?? [undefined, undefined];

  const mediaStyle = {
    height: adHeight ? `${adHeight}px` : 'auto',
    width: '100%',
    objectFit: 'cover' as const
  };

  // Container style
  const containerStyle = {
    width: adWidth ? `${adWidth}px` : '100%',
    maxWidth: '100%'
  };

  return (
    <div style={positionStyles}>
      <div className="relative">
        {/* Close button */}
        <button
          onClick={() => setIsVisible(false)}
          className="absolute -top-2 -right-2 bg-primary text-foreground rounded-full w-6 h-6 flex items-center justify-center z-20 text-sm font-bold shadow-md hover:bg-primary/80 transition-colors"
          aria-label="Close advertisement"
        >
          ×
        </button>

        <motion.div
          className="bg-gradient-to-br from-background to-background border-2 border-primary/50 rounded-lg shadow-xl overflow-hidden hover:border-primary/80 transition-all"
          style={containerStyle}
          initial={initial}
          animate={animate}
          transition={{ duration: 0.5 }}
          whileHover={{ scale: 1.03, boxShadow: '0 0 15px rgba(0, 255, 128, 0.3)' }}
        >
          <a href={ad.target_url ?? '#'} target="_blank" rel="noopener noreferrer" className="block group">
            {ad.media_url && ad.media_type === 'image' && (
              adWidth && adHeight ? (
                <Image
                  src={ad.media_url}
                  alt={ad.title ?? 'Advertisement'}
                  width={adWidth}
                  height={adHeight}
                  style={{ objectFit: 'cover', width: '100%', height: 'auto' }} // Maintain aspect ratio, fit container
                  className="group-hover:opacity-90 transition-opacity"
                />
              ) : (
                // Fallback if dimensions are missing
                <img
                  src={ad.media_url}
                  alt={ad.title ?? 'Advertisement'}
                  style={mediaStyle}
                  className="group-hover:opacity-90 transition-opacity"
                />
              )
            )}
            {ad.media_url && ad.media_type === 'video' && (
              <video src={ad.media_url} controls={false} muted autoPlay loop style={mediaStyle} className="group-hover:opacity-90 transition-opacity">
                Your browser does not support the video tag.
              </video>
            )}
            <div className="p-2">
              <h3 className="text-sm font-semibold text-primary mb-1 group-hover:underline">{ad.title ?? 'Sponsored'}</h3>
              {ad.description && (<p className="text-xs text-muted-green line-clamp-1">{ad.description}</p>)}
              <p className="text-xs text-muted-green mt-1">Ad</p>
            </div>
          </a>
        </motion.div>
      </div>
    </div>
  );
}
