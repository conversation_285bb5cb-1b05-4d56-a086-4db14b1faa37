"use client"

import { useState, useEffect } from "react"
import { DynamicAd } from "./dynamic-ad"
import { Ad } from "@/actions/ad-actions"

interface PageAdWrapperProps {
  pageName: string
  position: string
  fetchAdsFunction: () => Promise<{ data: Ad[] | null; error: any }>
  className?: string
  showTestAd?: boolean
}

/**
 * A reusable component for displaying ads on any page
 * Uses the provided fetch function to get ads for the specific page and position
 */
export function PageAdWrapper({
  pageName,
  position,
  fetchAdsFunction,
  className = "",
  showTestAd = false,
}: PageAdWrapperProps) {
  const [ad, setAd] = useState<Ad | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchAd = async () => {
      setLoading(true)
      try {
        const result = await fetchAdsFunction()

        // Handle different response formats safely
        let data, error
        if (result && typeof result === 'object') {
          if ('data' in result) {
            data = result.data
            error = result.error
          } else if (Array.isArray(result)) {
            data = result
            error = null
          } else {
            data = result
            error = null
          }
        } else {
          data = result || []
          error = null
        }

        if (error) {
          console.error(`[PageAdWrapper] Error fetching ${pageName}:${position} ad:`, error)
          setError(typeof error === 'string' ? error : 'Failed to load ad')
        } else if (data && data.length > 0) {
          // Get the first ad from the array
          setAd(data[0])
          console.log(`[PageAdWrapper] Successfully loaded ad: ${data[0].id} for ${pageName}:${position}`)
        } else {
          // No ads found for this position
          console.log(`[PageAdWrapper] No ads found for ${pageName}:${position}`)
        }
      } catch (e) {
        console.error(`[PageAdWrapper] Exception fetching ad for ${pageName}:${position}:`, e)
        setError('An unexpected error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchAd()
  }, [pageName, position, fetchAdsFunction])

  // If loading, you can show a placeholder or nothing
  if (loading) {
    return null // Or render a skeleton/placeholder
  }

  // If error and not showing test ads, show nothing or error message
  if (error && !showTestAd) {
    return null // Or a subtle error indicator for admins only
  }

  // If we have a real ad, render it with the DynamicAd component
  if (ad) {
    const adType = position as 'banner' | 'sidebar' | 'side-left' | 'side-right' | 'bottom' | 'in-content'
    return (
      <div className={className}>
        <DynamicAd ad={ad} adType={adType} />
      </div>
    )
  }

  // If showTestAd is true and we have no real ad, we could show a test ad
  if (showTestAd) {
    // Create a test ad object - only include required and supported fields
    const testAd = {
      id: "test-ad",
      title: `Test ${position} Ad for ${pageName}`,
      description: "This is a test ad that appears when no real ads are available.",
      media_url: "/placeholder-ad.jpg",
      target_url: "#",
      status: "active" as const,
      created_at: new Date().toISOString(),
      start_date: new Date().toISOString(),
      placement: `${pageName}:${position}`,
      placements: [`${pageName}:${position}`],
      media_type: "Test",
      
      // Other fields with null values
      advertiser_id: null,
      budget: null,
      custom_bottom: null,
      custom_left: null, 
      custom_right: null,
      custom_top: null,
      end_date: null,
      target_locations: null,
      target_specialty_id: null,
      updated_at: null,
      media_height: null,
      media_width: null,
      priority: null,
      size: null
    } as Ad // Cast to Ad to ensure compatibility

    const adType = position as 'banner' | 'sidebar' | 'side-left' | 'side-right' | 'bottom' | 'in-content'
    return (
      <div className={className}>
        <DynamicAd ad={testAd} adType={adType} />
      </div>
    )
  }

  // If no ad and not showing test ad, return nothing
  return null
}
