"use client"

import { PageAdWrapper } from "./page-ad-wrapper"
import { getAdFetchFunction } from "./ad-helpers"

interface PageAdsProps {
  pageName: string
  positions?: ('banner' | 'sidebar' | 'side-left' | 'side-right' | 'bottom' | 'in-content')[]
  showTestAds?: boolean
}

/**
 * A component that displays all ads for a specific page
 * Automatically handles fetching the appropriate ads for each position
 */
export function PageAds({
  pageName,
  positions = ['banner', 'sidebar', 'side-left'],
  showTestAds = false
}: PageAdsProps) {
  console.log(`[PageAds] Rendering ads for page: ${pageName}, positions: ${positions.join(', ')}`);

  // Determine position-specific wrapper classes
  const getPositionWrapperClass = (position: string) => {
    switch (position) {
      case 'banner':
        return 'w-full max-w-6xl mx-auto my-8'
      case 'sidebar':
        return 'w-full max-w-xs lg:fixed lg:right-6 lg:top-[200px] z-10'
      case 'side-left':
        return 'hidden xl:block fixed left-6 top-[200px] w-48 z-10'
      case 'side-right':
        return 'hidden xl:block fixed right-6 top-[200px] w-48 z-10'
      case 'bottom':
        return 'w-full max-w-4xl mx-auto my-8'
      case 'in-content':
        return 'w-full max-w-4xl mx-auto my-12'
      default:
        return ''
    }
  }

  return (
    <>
      {positions.map(position => {
        const fetchFunction = getAdFetchFunction(pageName, position)
        if (!fetchFunction) {
          console.warn(`[PageAds] No fetch function found for ${pageName}:${position}`)
          return null
        }

        return (
          <div key={`${pageName}-${position}-container`} className={getPositionWrapperClass(position)}>
            <PageAdWrapper
              key={`${pageName}-${position}`}
              pageName={pageName}
              position={position}
              fetchAdsFunction={fetchFunction}
              showTestAd={showTestAds}
            />
          </div>
        )
      })}
    </>
  )
}
