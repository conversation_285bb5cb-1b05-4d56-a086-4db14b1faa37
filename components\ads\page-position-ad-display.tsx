"use client"

import { useState, useEffect } from "react"
import { Ad } from "@/actions/ad-actions"
import { MultiPlacementAdDisplay } from "./multi-placement-ad-display"
import { TestAdDisplay } from "./test-ad-display"

interface PagePositionAdDisplayProps {
  ads: Ad[] | null
  pageName: string
  position: string
  showTestAd?: boolean
  maxAds?: number
  maxWidth?: number | string
}

/**
 * A component that displays ads for a specific page and position
 * Filters ads based on their placement property to match the current page and position
 */
export function PagePositionAdDisplay({
  ads,
  pageName,
  position,
  showTestAd = false,
  maxAds = 3,
  maxWidth = position === 'banner' ? 728 : 300
}: PagePositionAdDisplayProps) {
  const [matchingAds, setMatchingAds] = useState<Ad[]>([]);

  // Filter ads that match the current page and position
  useEffect(() => {
    if (!ads || ads.length === 0) {
      setMatchingAds([]);
      return;
    }

    // Filter ads that have a placement matching the current page and position
    const filtered = ads.filter(ad => {
      // Handle both single placement and array of placements
      const placements = Array.isArray(ad.placement) 
        ? ad.placement 
        : ad.placements || [ad.placement];
      
      // Check if any placement matches the current page and position
      return placements.some(placement => {
        const [page, pos] = placement.split(':');
        return page === pageName && pos === position;
      });
    });

    // Sort by priority if available
    const sorted = [...filtered].sort((a, b) => {
      // Check if either ad has priority (from custom placement data)
      const aHasPriority = Array.isArray(a.placement) 
        ? a.placement.some(p => p.includes('priority:true')) 
        : a.placement.includes('priority:true');
      
      const bHasPriority = Array.isArray(b.placement) 
        ? b.placement.some(p => p.includes('priority:true')) 
        : b.placement.includes('priority:true');
      
      if (aHasPriority && !bHasPriority) return -1;
      if (!aHasPriority && bHasPriority) return 1;
      return 0;
    });

    // Limit to maxAds
    setMatchingAds(sorted.slice(0, maxAds));
  }, [ads, pageName, position, maxAds]);

  // If no matching ads and not showing test ads, don't render anything
  if (matchingAds.length === 0 && !showTestAd) return null;

  // If showing test ads and no real ads, show a test ad
  if (matchingAds.length === 0 && showTestAd) {
    return (
      <TestAdDisplay
        position={{ 
          top: position === 'banner' ? '0' : 'auto',
          left: position === 'banner' || position === 'side-left' ? '0' : 'auto',
          right: position === 'side-right' ? '0' : 'auto',
          bottom: position === 'bottom' ? '0' : 'auto'
        }}
        title={`${position.charAt(0).toUpperCase() + position.slice(1)} Ad`}
        color={position === 'banner' ? 'purple' : (position === 'side-left' ? 'green' : 'blue')}
        width={typeof maxWidth === 'number' ? maxWidth : 300}
        height={position === 'banner' ? 90 : 250}
      />
    );
  }

  // Wrapper class based on position
  const wrapperClassName = 
    position === 'banner' || position === 'bottom' || position === 'in-content'
      ? "flex flex-col items-center w-full gap-8 my-6"
      : position === 'sidebar'
        ? "sticky top-4 w-full flex flex-col gap-4"
        : "w-full flex flex-col gap-4";

  return (
    <div className={wrapperClassName}>
      {matchingAds.map((ad) => (
        <MultiPlacementAdDisplay
          key={ad.id}
          ad={ad}
          currentPage={pageName}
          position={position}
          maxWidth={maxWidth}
        />
      ))}
    </div>
  );
}
