"use client"

import { motion } from "framer-motion"
import Image from "next/image" // Import next/image
import { useState, useEffect, useMemo } from "react" // Import useMemo
import { Ad } from "@/actions/ad-actions"
import { TestAdDisplay } from "./test-ad-display"

interface PositionedAdDisplayProps {
  ads: Ad[] | null
  position: {
    top?: number | string
    bottom?: number | string
    left?: number | string
    right?: number | string
    transform?: string
  }
  variant: 'banner' | 'sidebar' | 'floating'
  showMultiple?: boolean
  maxWidth?: number | string
  fallbackColor?: string
  fallbackTitle?: string
  showTestAd?: boolean
}

/**
 * A component that displays ads in a specific position
 * Falls back to test ads if no real ads are available or if showTestAd is true
 */
export function PositionedAdDisplay({
  ads,
  position,
  variant,
  showMultiple = false,
  maxWidth = variant === 'banner' ? 728 : 300,
  fallbackColor = variant === 'banner' ? 'purple' : (position.left ? 'green' : 'blue'),
  fallbackTitle = `${variant === 'banner' ? 'Banner' : (position.left ? 'Left Side' : 'Right Side')} Ad`,
  showTestAd = false
}: PositionedAdDisplayProps) {
  // --- Hooks (All at the top) ---
  const [isVisible, setIsVisible] = useState(true);
  const [hasRealAds, setHasRealAds] = useState(false);
  const [currentAdIndex, setCurrentAdIndex] = useState(0);

  // Memoize availableAds to ensure stable reference for effects
  const availableAds = useMemo(() => ads || [], [ads]);

  // Effect to update hasRealAds state based on availableAds
  useEffect(() => {
    const hasAds = availableAds.length > 0;
    console.log(`[PositionedAdDisplay] Has real ads: ${hasAds}, count: ${availableAds.length}, variant: ${variant}`);
    setHasRealAds(hasAds);
  }, [availableAds, variant]); // Depend on memoized availableAds

  // Effect for rotating ads - Called unconditionally at top level
  useEffect(() => {
    let timer: NodeJS.Timeout | undefined;
    // Check condition inside the effect
    if (showMultiple && availableAds.length > 1) {
      timer = setInterval(() => {
        // Use functional update to avoid stale state issues if availableAds changes rapidly
        setCurrentAdIndex((prevIndex) => (prevIndex + 1) % availableAds.length);
      }, 15000); // Rotate every 15 seconds
    }
    // Cleanup function
    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
    // Depend on showMultiple and the memoized availableAds array
  }, [showMultiple, availableAds]);

  // --- Calculations (AFTER hooks) ---
  const shouldShowTest = showTestAd;
  const canShowReal = hasRealAds && !shouldShowTest;

  // Select the ad to display (handle potential empty array)
  const adToDisplay = useMemo(() => {
    if (!canShowReal || availableAds.length === 0) {
      return null;
    }
    return showMultiple ? availableAds[currentAdIndex % availableAds.length] : availableAds[0];
  }, [canShowReal, availableAds, showMultiple, currentAdIndex]);

  const isCustomPosition = useMemo(() =>
    adToDisplay?.placements?.includes('custom_position') ?? false,
    [adToDisplay]
  );

  // Determine position strategy based on calculated values
  const positionStrategy = useMemo(() => {
    if (isCustomPosition) return 'fixed' as const;
    if (variant === 'floating') return 'fixed' as const;
    return 'relative' as const;
  }, [isCustomPosition, variant]);

  // Calculate position styles based on strategy and ad data
  const positionStyles = useMemo(() => {
    let baseStyles: React.CSSProperties = {
      position: positionStrategy,
      ...(isCustomPosition ? {} : position), // Apply prop position only if not custom
      zIndex: positionStrategy === 'fixed' ? 10 : 5,
      display: 'flex', // Always flex for consistency in handling single/multiple ads
      flexDirection: 'column' as const,
      alignItems: 'center', // Center items horizontally (including the ad container)
      justifyContent: 'flex-start', // Align multiple ads to the top (if multiple)
      gap: '10px',
      // width: '100%', // Remove width: 100% to allow align-items to center
      maxHeight: positionStrategy === 'fixed' ? '80vh' : 'none',
      overflowY: positionStrategy === 'fixed' ? 'auto' as const : 'visible' as const,
      // Centering is now handled by alignItems: 'center' on the flex container
      pointerEvents: 'auto' as const,
      opacity: 1,
    };

    const customStyles: React.CSSProperties = (isCustomPosition && adToDisplay) ? {
      top: adToDisplay.custom_top ?? undefined,
      bottom: adToDisplay.custom_bottom ?? undefined,
      left: adToDisplay.custom_left ?? undefined,
      right: adToDisplay.custom_right ?? undefined,
      transform: position.transform ?? undefined, // Keep transform from props if needed
    } : {};

    return { ...baseStyles, ...customStyles };
  }, [positionStrategy, isCustomPosition, position, variant, adToDisplay]);


  // --- Conditional Rendering (AFTER hooks and calculations) ---

  if (!isVisible) {
    return null;
  }

  if (shouldShowTest) {
    console.log("[PositionedAdDisplay] Rendering Test Ad");
    return (
      <TestAdDisplay
        position={position} // Pass original position prop for test ad layout
        title={fallbackTitle}
        color={fallbackColor}
        width={typeof maxWidth === 'number' ? maxWidth : 300}
        height={variant === 'banner' ? 90 : 250}
        variant={variant}
      />
    );
  }

  // If not showing test ad, but no real ad is available to display, render nothing
  if (!adToDisplay) {
     console.log("[PositionedAdDisplay] No ad to display (isVisible=true, shouldShowTest=false, adToDisplay=null)");
     return null;
  }

  // --- Render the actual ad ---
  // Log the calculated styles before rendering
  console.log(`[PositionedAdDisplay] Rendering ad ID ${adToDisplay.id} with variant: ${variant}, isCustom: ${isCustomPosition}, styles:`, positionStyles);

  // Determine animation direction based on the *already calculated* positionStyles
  const getAnimationDirection = (styles: React.CSSProperties) => {
    if (styles.left !== undefined) return { x: -20 };
    if (styles.right !== undefined) return { x: 20 };
    if (styles.top !== undefined) return { y: -20 };
    if (styles.bottom !== undefined) return { y: 20 };
    return { x: -20 }; // Default if no direction obvious
  };

  const animationDirection = getAnimationDirection(positionStyles);
  const initial = { opacity: 0, ...animationDirection };
  const animate = {
    opacity: 1,
    x: 'x' in animationDirection ? 0 : undefined,
    y: 'y' in animationDirection ? 0 : undefined
  };

  // --- Render the single selected ad (Ensure adToDisplay is not null) ---
  // This part should only be reached if !shouldShowTest and adToDisplay is valid
  if (!adToDisplay) {
      // This case should ideally not be reached due to earlier checks, but acts as a safeguard
      console.error("[PositionedAdDisplay] Reached render section without a valid adToDisplay.");
      return null;
  }

  const [adWidth, adHeight] = adToDisplay.size?.split('x').map(Number) ?? [undefined, undefined];

  // Styling for the ad container (motion.div)
  const containerStyle = {
    width: adWidth ? `${adWidth}px` : typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth,
    maxWidth: typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth,
    // No margin needed here, parent flex container handles centering via alignItems: 'center'
  };

  console.log(`[PositionedAdDisplay] Ad container style:`, containerStyle, `for ad ID: ${adToDisplay.id}, size: ${adToDisplay.size}`);

  const mediaStyle = {
    height: adHeight ? `${adHeight}px` : 'auto',
    width: '100%',
    objectFit: 'cover' as const
  };

  return (
    // Outer div applies the positioning styles
    <div style={positionStyles}>
      {/* Inner div handles the ad content and animation */}
      <div key={adToDisplay.id} className="relative"> {/* Key ensures animation reruns on ad change */}
        <motion.div
          className="bg-gradient-to-br from-background to-background border-2 border-primary/50 rounded-lg shadow-xl overflow-hidden hover:border-primary/80 transition-all"
          style={containerStyle}
          initial={initial}
          animate={animate}
          transition={{ duration: 0.5 }}
          whileHover={{ scale: 1.03, boxShadow: '0 0 15px rgba(0, 255, 128, 0.3)' }}
        >
          <a href={adToDisplay.target_url ?? '#'} target="_blank" rel="noopener noreferrer" className="block group">
            {adToDisplay.media_url && adToDisplay.media_type === 'image' && (
              adWidth && adHeight ? (
                <Image
                  src={adToDisplay.media_url}
                  alt={adToDisplay.title ?? 'Advertisement'}
                  width={adWidth}
                  height={adHeight}
                  style={{ objectFit: 'cover', width: '100%', height: 'auto' }} // Maintain aspect ratio, fit container
                  className="group-hover:opacity-90 transition-opacity"
                />
              ) : (
                // Fallback if dimensions are missing
                <img
                  src={adToDisplay.media_url}
                  alt={adToDisplay.title ?? 'Advertisement'}
                  style={mediaStyle}
                  className="group-hover:opacity-90 transition-opacity"
                />
              )
            )}
            {adToDisplay.media_url && adToDisplay.media_type === 'video' && (
              <div className="relative pointer-events-none">
                <video src={adToDisplay.media_url} controls={false} muted autoPlay loop style={mediaStyle} className="group-hover:opacity-90 transition-opacity" controlsList="nodownload nofullscreen noremoteplayback">
                  Your browser does not support the video tag.
                </video>
              </div>
            )}
            <div className="p-2">
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setIsVisible(false); // Hide the whole component
                }}
                className="absolute top-2 right-2 bg-background/70 hover:bg-red-600/90 text-foreground rounded-full w-6 h-6 flex items-center justify-center z-50 text-xs font-bold shadow-md transition-colors"
                aria-label="Close advertisement"
              >
                X
              </button>
              <h3 className="text-sm font-semibold text-primary mb-1 group-hover:underline">{adToDisplay.title ?? 'Sponsored'}</h3>
              {adToDisplay.description && (<p className="text-xs text-muted-green line-clamp-1">{adToDisplay.description}</p>)}
              <p className="text-xs text-muted-green mt-1">Ad</p>
            </div>
          </a>
        </motion.div>
      </div>
    </div>
  );
}
