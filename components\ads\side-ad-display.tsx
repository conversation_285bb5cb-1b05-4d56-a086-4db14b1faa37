"use client"

import { motion } from "framer-motion"
import Image from "next/image" // Import next/image
import { Ad } from "@/actions/ad-actions"

interface SideAdDisplayProps {
  ad: Ad | Ad[]
  position: 'left' | 'right'
  showMultiple?: boolean
  offsetTop?: number
}

export function SideAdDisplay({ ad, position, showMultiple = false, offsetTop = 200 }: SideAdDisplayProps) {
  // Convert single ad to array for consistent handling
  const ads = Array.isArray(ad) ? ad : [ad];

  // If no ads or empty array, return null
  if (!ads || ads.length === 0) return null;

  // Determine how many ads to show
  const adsToShow = showMultiple ? ads : [ads[0]];

  // Position styles
  const positionStyles = {
    position: 'fixed' as const,
    top: `${offsetTop}px`,
    [position]: '20px',
    zIndex: 10,
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '20px',
    maxHeight: `calc(100vh - ${offsetTop * 2}px)`,
    overflowY: 'auto' as const
  };

  return (
    <div style={positionStyles}>
      {adsToShow.map((adItem, index) => {
        const [adWidth, adHeight] = adItem.size?.split('x').map(Number) ?? [undefined, undefined];
        
        // Styling for side ads
        const containerStyle = {
          width: adWidth ? `${adWidth}px` : '160px', // Default side ad width
          maxWidth: '160px' // Limit side ad width
        };
        
        const mediaStyle = {
          height: adHeight ? `${adHeight}px` : 'auto',
          width: '100%',
          objectFit: 'cover' as const
        };
        
        const initial = { opacity: 0, x: position === 'left' ? -20 : 20 };
        const animate = { opacity: 1, x: 0 };
        
        return (
          <div key={adItem.id || index} className="mb-4">
            <motion.div
              className="bg-gradient-to-br from-background to-background border border-primary/30 rounded-lg shadow-lg overflow-hidden"
              style={containerStyle}
              initial={initial}
              animate={animate}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <a href={adItem.target_url ?? '#'} target="_blank" rel="noopener noreferrer" className="block group">
                {adItem.media_url && adItem.media_type === 'image' && (
                  adWidth && adHeight ? (
                    <Image
                      src={adItem.media_url}
                      alt={adItem.title ?? 'Advertisement'}
                      width={adWidth}
                      height={adHeight}
                      style={{ objectFit: 'cover', width: '100%', height: 'auto' }} // Maintain aspect ratio, fit container
                      className="group-hover:opacity-90 transition-opacity"
                    />
                  ) : (
                    // Fallback if dimensions are missing
                    <img
                      src={adItem.media_url}
                      alt={adItem.title ?? 'Advertisement'}
                      style={mediaStyle}
                      className="group-hover:opacity-90 transition-opacity"
                    />
                  )
                )}
                {adItem.media_url && adItem.media_type === 'video' && (
                  <video src={adItem.media_url} controls={false} muted autoPlay loop style={mediaStyle} className="group-hover:opacity-90 transition-opacity">
                    Your browser does not support the video tag.
                  </video>
                )}
                <div className="p-2">
                  <h3 className="text-sm font-semibold text-primary mb-1 group-hover:underline">{adItem.title ?? 'Sponsored'}</h3>
                  {adItem.description && (<p className="text-xs text-muted-green line-clamp-1">{adItem.description}</p>)}
                  <p className="text-xs text-muted-green mt-1">Ad</p>
                </div>
              </a>
            </motion.div>
          </div>
        );
      })}
    </div>
  );
}
