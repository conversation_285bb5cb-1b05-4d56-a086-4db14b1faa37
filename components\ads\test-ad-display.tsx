"use client"

import { motion } from "framer-motion"
import { useState } from "react"

interface TestAdDisplayProps {
  position: {
    top?: number | string
    bottom?: number | string
    left?: number | string
    right?: number | string
    transform?: string
  }
  color?: string
  title?: string
  width?: number
  height?: number
  variant: 'banner' | 'sidebar' | 'floating' // Add variant prop
}

/**
 * A test component to display a visible ad for development purposes
 */
export function TestAdDisplay({
  position,
  color = "green",
  title = "Test Advertisement",
  width = 200,
  height = 300,
  variant // Destructure variant
}: TestAdDisplayProps) {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  // Position styles - Conditional based on variant
  const positionStyles = {
    position: variant === 'banner' ? 'relative' as const : 'fixed' as const, // Use relative for banner
    ...position,
    zIndex: variant === 'banner' ? 5 : 999, // Lower z-index for banner
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '20px',
    margin: variant === 'banner' ? '0 auto' : undefined, // Center relative banner
  };

  // Determine animation direction based on position
  const getAnimationDirection = () => {
    if (position.left !== undefined) return { x: -20 };
    if (position.right !== undefined) return { x: 20 };
    if (position.top !== undefined) return { y: -20 };
    if (position.bottom !== undefined) return { y: 20 };
    return { x: -20 }; // Default
  };

  const initial = { opacity: 0, ...getAnimationDirection() };
  const animate = {
    opacity: 1,
    x: 'x' in getAnimationDirection() ? 0 : undefined,
    y: 'y' in getAnimationDirection() ? 0 : undefined
  };

  const containerStyle = {
    width: `${width}px`,
    height: `${height}px`,
  };

  return (
    <div style={positionStyles}>
      <div className="relative">
        {/* Close button - Improved position and z-index */}
        <button
          onClick={() => setIsVisible(false)}
          className="absolute top-2 right-2 bg-background/70 hover:bg-red-600/90 text-foreground rounded-full w-6 h-6 flex items-center justify-center z-50 text-xs font-bold shadow-md transition-colors"
          aria-label="Close advertisement"
        >
          X
        </button>

        <motion.div
          className={`bg-gradient-to-br from-${color}-900/80 to-${color}-800/90 border-2 border-${color}-500/50 rounded-lg shadow-xl overflow-hidden hover:border-${color}-500/80 transition-all`}
          style={containerStyle}
          initial={initial}
          animate={animate}
          whileHover={{ scale: 1.03, boxShadow: `0 0 15px rgba(0, 255, 128, 0.3)` }}
        >
          <div className="p-4 flex flex-col items-center justify-center h-full">
            <h3 className="text-lg font-bold text-foreground mb-2">{title}</h3>
            <p className="text-foreground/80 text-center mb-4">This is a test ad to verify positioning</p>
            <div className="w-full h-32 bg-card rounded flex items-center justify-center mb-4">
              <span className="text-foreground/60">Ad Content</span>
            </div>
            <p className="text-xs text-foreground/50 mt-auto">Advertisement</p>
            {/* Remove the indicators that appear below ads */}
            <div className="hidden">Position Indicators</div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
