"use client"

import { useEffect, useState } from "react"
import { motion } from "framer-motion"

export function AnimatedFootballLine() {
  const [windowWidth, setWindowWidth] = useState(0)

  // Update window width on client side
  useEffect(() => {
    setWindowWidth(window.innerWidth)

    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }

    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // Calculate number of footballs based on screen width
  const numberOfFootballs = Math.max(5, Math.floor(windowWidth / 100))

  return (
    <div className="w-full bg-gradient-to-r from-primary/10 via-primary/30 to-primary/10 py-2 overflow-hidden">
      <div className="flex justify-around items-center">
        {Array.from({ length: numberOfFootballs }).map((_, index) => (
          <motion.div
            key={index}
            className="relative"
            animate={{
              y: [0, -10, 0],
              rotate: [0, 360],
            }}
            transition={{
              duration: 2,
              repeat: Number.POSITIVE_INFINITY,
              repeatType: "reverse",
              delay: (index * 0.2) % 1, // Stagger the animations
              ease: "easeInOut",
            }}
          >
            <FootballIcon className="w-8 h-8 text-foreground" />
          </motion.div>
        ))}
      </div>
    </div>
  )
}

// Football SVG icon component
function FootballIcon({ className }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" className={className} fill="currentColor">
      <path d="M247.5 25.4c-16.8 2.2-30.5 5.6-45.5 11.4-31.3 12.1-59.1 32.5-82.1 60.3-12.1 14.6-26.3 38.5-32.8 55.4-15.1 39.2-18.8 79.7-11.1 121.5 5.1 27.8 15.9 55.6 31.1 80 5.7 9.1 19.7 27.2 27.9 36 30.9 33.1 70.9 55.6 115 64.9 15.9 3.3 28.1 4.6 46 4.6 17.9 0 30.1-1.3 46-4.6 44.1-9.3 84.1-31.8 115-64.9 8.2-8.8 22.2-26.9 27.9-36 15.2-24.4 26-52.2 31.1-80 7.7-41.8 4-82.3-11.1-121.5-6.5-16.9-20.7-40.8-32.8-55.4-23-27.8-50.8-48.2-82.1-60.3-15-5.8-28.7-9.2-45.5-11.4-13.3-1.7-33.7-1.7-47 0zm38.5 18.1c30.8 4 61.1 16.9 85.5 36.4 5.5 4.4 18.5 17.4 22.9 22.9 19.5 24.4 32.4 54.7 36.4 85.5 1.1 8.3 1 33.7-.1 42-4.1 30.8-16.9 61.1-36.4 85.5-4.4 5.5-17.4 18.5-22.9 22.9-24.4 19.5-54.7 32.4-85.5 36.4-8.3 1.1-33.7 1-42-.1-30.8-4.1-61.1-16.9-85.5-36.4-5.5-4.4-18.5-17.4-22.9-22.9-19.5-24.4-32.4-54.7-36.4-85.5-1.1-8.3-1-33.7.1-42 4-30.8 16.9-61.1 36.4-85.5 4.4-5.5 17.4-18.5 22.9-22.9 24.4-19.5 54.7-32.4 85.5-36.4 8.3-1.1 33.7-1 42 .1z" />
      <path d="M241.5 63.4c-3.8.8-9.7 3.2-13.1 5.5-7.1 4.7-11.9 11.6-14.3 20.6-1.1 4.1-1.1 5.4-1.1 40.5 0 35.1 0 36.4 1.1 40.5 2.4 9 7.2 15.9 14.3 20.6 3.4 2.3 9.3 4.7 13.1 5.5 1.6.3 12.5.4 24.2.3 20.2-.3 21.5-.4 25.8-2.5 7.1-3.6 13.4-9.9 17-17 3.6-7.1 4.5-11.9 4.5-25.4 0-13.5-.9-18.3-4.5-25.4-3.6-7.1-9.9-13.4-17-17-4.3-2.1-5.6-2.2-25.8-2.5-11.7-.1-22.6 0-24.2.3zm44.3 19.1c5.2 2.6 9.2 6.6 11.8 11.8 2.1 4.3 2.2 5.6 2.5 25.8.3 11.7.2 22.6-.3 24.2-.8 3.8-3.2 9.7-5.5 13.1-4.7 7.1-11.6 11.9-20.6 14.3-4.1 1.1-5.4 1.1-40.5 1.1-35.1 0-36.4 0-40.5-1.1-9-2.4-15.9-7.2-20.6-14.3-2.3-3.4-4.7-9.3-5.5-13.1-.5-1.6-.6-12.5-.3-24.2.3-20.2.4-21.5 2.5-25.8 2.6-5.2 6.6-9.2 11.8-11.8 4.3-2.1 5.6-2.2 25.8-2.5 11.7-.3 22.6-.2 24.2.3 3.8.8 9.7 3.2 13.1 5.5 1.8 1.2 3.5 2.1 3.8 2.1.3 0 2-1 3.8-2.1 3.4-2.3 9.3-4.7 13.1-5.5 1.6-.5 12.5-.6 24.2-.3 20.2.3 21.5.4 25.8 2.5zM241.5 191.4c-3.8.8-9.7 3.2-13.1 5.5-7.1 4.7-11.9 11.6-14.3 20.6-1.1 4.1-1.1 5.4-1.1 40.5 0 35.1 0 36.4 1.1 40.5 2.4 9 7.2 15.9 14.3 20.6 3.4 2.3 9.3 4.7 13.1 5.5 1.6.3 12.5.4 24.2.3 20.2-.3 21.5-.4 25.8-2.5 7.1-3.6 13.4-9.9 17-17 3.6-7.1 4.5-11.9 4.5-25.4 0-13.5-.9-18.3-4.5-25.4-3.6-7.1-9.9-13.4-17-17-4.3-2.1-5.6-2.2-25.8-2.5-11.7-.1-22.6 0-24.2.3zm44.3 19.1c5.2 2.6 9.2 6.6 11.8 11.8 2.1 4.3 2.2 5.6 2.5 25.8.3 11.7.2 22.6-.3 24.2-.8 3.8-3.2 9.7-5.5 13.1-4.7 7.1-11.6 11.9-20.6 14.3-4.1 1.1-5.4 1.1-40.5 1.1-35.1 0-36.4 0-40.5-1.1-9-2.4-15.9-7.2-20.6-14.3-2.3-3.4-4.7-9.3-5.5-13.1-.5-1.6-.6-12.5-.3-24.2.3-20.2.4-21.5 2.5-25.8 2.6-5.2 6.6-9.2 11.8-11.8 4.3-2.1 5.6-2.2 25.8-2.5 11.7-.3 22.6-.2 24.2.3 3.8.8 9.7 3.2 13.1 5.5 1.8 1.2 3.5 2.1 3.8 2.1.3 0 2-1 3.8-2.1 3.4-2.3 9.3-4.7 13.1-5.5 1.6-.5 12.5-.6 24.2-.3 20.2.3 21.5.4 25.8 2.5zM241.5 319.4c-3.8.8-9.7 3.2-13.1 5.5-7.1 4.7-11.9 11.6-14.3 20.6-1.1 4.1-1.1 5.4-1.1 40.5 0 35.1 0 36.4 1.1 40.5 2.4 9 7.2 15.9 14.3 20.6 3.4 2.3 9.3 4.7 13.1 5.5 1.6.3 12.5.4 24.2.3 20.2-.3 21.5-.4 25.8-2.5 7.1-3.6 13.4-9.9 17-17 3.6-7.1 4.5-11.9 4.5-25.4 0-13.5-.9-18.3-4.5-25.4-3.6-7.1-9.9-13.4-17-17-4.3-2.1-5.6-2.2-25.8-2.5-11.7-.1-22.6 0-24.2.3zm44.3 19.1c5.2 2.6 9.2 6.6 11.8 11.8 2.1 4.3 2.2 5.6 2.5 25.8.3 11.7.2 22.6-.3 24.2-.8 3.8-3.2 9.7-5.5 13.1-4.7 7.1-11.6 11.9-20.6 14.3-4.1 1.1-5.4 1.1-40.5 1.1-35.1 0-36.4 0-40.5-1.1-9-2.4-15.9-7.2-20.6-14.3-2.3-3.4-4.7-9.3-5.5-13.1-.5-1.6-.6-12.5-.3-24.2.3-20.2.4-21.5 2.5-25.8 2.6-5.2 6.6-9.2 11.8-11.8 4.3-2.1 5.6-2.2 25.8-2.5 11.7-.3 22.6-.2 24.2.3 3.8.8 9.7 3.2 13.1 5.5 1.8 1.2 3.5 2.1 3.8 2.1.3 0 2-1 3.8-2.1 3.4-2.3 9.3-4.7 13.1-5.5 1.6-.5 12.5-.6 24.2-.3 20.2.3 21.5.4 25.8 2.5z" />
    </svg>
  )
}

