'use client'

import { Steth<PERSON><PERSON>, TrendingUp, Users, BookOpen } from "lucide-react"
import Link from "next/link"
import { useEffect, useState } from "react"
import { getBlogStats, initializeBlogData } from "@/lib/blog-service"

export function BlogHero() {
  const [stats, setStats] = useState({
    totalCategories: 0,
    totalAuthors: <AUTHORS>
    publishedPosts: 0,
    monthlyViews: 0
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function loadStats() {
      try {
        // Initialize data if needed
        await initializeBlogData()
        
        // Get current stats
        const blogStats = await getBlogStats()
        setStats({
          totalCategories: blogStats.totalCategories,
          totalAuthors: <AUTHORS>
          publishedPosts: blogStats.publishedPosts,
          monthlyViews: blogStats.monthlyViews
        })
      } catch (error) {
        console.error('Error loading blog stats:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadStats()
  }, [])

  return (
    <section className="relative bg-gradient-to-b from-background via-background to-primary/20 text-foreground">
      <div className="absolute inset-0 bg-background/20"></div>
      <div className="relative container mx-auto px-4 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <div className="flex justify-center mb-6">
            <div className="flex items-center space-x-4 text-foreground/70">
              <Stethoscope className="h-8 w-8" />
              <TrendingUp className="h-8 w-8" />
              <Users className="h-8 w-8" />
              <BookOpen className="h-8 w-8" />
            </div>
          </div>
          
          <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
            Medical Insights
            <span className="block text-transparent bg-gradient-to-r from-primary to-blue-400 bg-clip-text">
              That Matter
            </span>
          </h1>
          
          <p className="text-xl md:text-2xl text-foreground/80 mb-8 leading-relaxed max-w-3xl mx-auto">
            Authoritative analysis of medical rankings, in-depth healthcare insights, 
            and expert commentary from the leading platform for medical professionals.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link 
              href="#featured"
              className="px-8 py-4 bg-primary text-foreground font-semibold rounded-lg hover:bg-primary/90 transition-colors"
            >
              Explore Featured Articles
            </Link>
            <Link 
              href="#categories"
              className="px-8 py-4 border-2 border-border text-foreground font-semibold rounded-lg hover:bg-card transition-colors"
            >
              Browse Categories
            </Link>
          </div>
          
          <div className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-primary mb-2">
                {isLoading ? '...' : stats.totalCategories}
              </div>
              <div className="text-sm text-foreground/70">Expert Categories</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary mb-2">
                {isLoading ? '...' : stats.totalAuthors}
              </div>
              <div className="text-sm text-foreground/70">Medical Experts</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary mb-2">
                {isLoading ? '...' : stats.publishedPosts}
              </div>
              <div className="text-sm text-foreground/70">Articles</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary mb-2">
                {isLoading ? '...' : stats.monthlyViews > 0 ? `${Math.floor(stats.monthlyViews / 1000)}k+` : '0'}
              </div>
              <div className="text-sm text-foreground/70">Monthly Readers</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
} 