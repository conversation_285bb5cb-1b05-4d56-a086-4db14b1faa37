"use client"

import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Mail, CheckCircle, AlertCircle } from "lucide-react"
import { useState } from "react"
import { newsletterService } from "@/lib/newsletter-service"

export function BlogNewsletter() {
  const [email, setEmail] = useState("")
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email) return

    setIsLoading(true)
    setError("")
    
    try {
      const result = await newsletterService.subscribe({ email, source: 'blog_newsletter' })
      
      if (result.success) {
        setIsSubscribed(true)
        setEmail("")
      } else {
        setError(result.error || 'Failed to subscribe. Please try again.')
      }
    } catch (error) {
      console.error('Newsletter subscription error:', error)
      setError('An unexpected error occurred. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  if (isSubscribed) {
    return (
      <Card className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-500/30">
        <CardContent className="p-8 text-center">
          <div className="flex justify-center mb-4">
            <CheckCircle className="h-12 w-12 text-green-400" />
          </div>
          <h3 className="text-2xl font-bold text-foreground mb-2">
            Welcome to Our Medical Insights Community!
          </h3>
          <p className="text-foreground/80">
            You'll receive our weekly digest of the latest medical insights, rankings analysis, and expert commentary.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="bg-gradient-to-r from-primary/20 to-blue-500/20 border-primary/30">
      <CardContent className="p-8">
        <div className="max-w-2xl mx-auto text-center">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-primary/20 rounded-full">
              <Mail className="h-8 w-8 text-primary" />
            </div>
          </div>
          
          <h3 className="text-2xl font-bold text-foreground mb-4">
            Stay Informed with Medical Insights
          </h3>
          
          <p className="text-foreground/90 mb-6 leading-relaxed">
            Get weekly updates on the latest medical rankings, breakthrough research, 
            and expert analysis delivered directly to your inbox. Join thousands of 
            healthcare professionals staying ahead of industry trends.
          </p>

          {error && (
            <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg flex items-center gap-2 text-red-200">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          )}
          
          <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <Input
              type="email"
              placeholder="Enter your email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="flex-1"
              disabled={isLoading}
            />
            <Button 
              type="submit" 
              disabled={isLoading || !email}
              className="px-6"
            >
              {isLoading ? "Subscribing..." : "Subscribe"}
            </Button>
          </form>
          
          <p className="text-xs text-foreground/60 mt-4">
            No spam, unsubscribe at any time. We respect your privacy.
          </p>
        </div>
      </CardContent>
    </Card>
  )
} 