'use client'

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { <PERSON><PERSON>hart<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>r<PERSON><PERSON><PERSON>, Heart, ArrowRight, Folder } from "lucide-react"
import Link from "next/link"
import { useEffect, useState } from "react"
import { getBlogCategories, getCategoryPostCounts } from "@/lib/blog-service"
import type { BlogCategory } from "@/lib/blog-service"

// Icon mapping for categories
const categoryIcons: Record<string, any> = {
  'ranking-analysis-insights': BarChart3,
  'medical-deep-dives': BookOpen,
  'doctor-institution-spotlights': UserCheck,
  'patient-caregiver-resources': Heart,
  default: Folder
}

export function CategoryGrid() {
  const [categories, setCategories] = useState<BlogCategory[]>([])
  const [categoryPostCounts, setCategoryPostCounts] = useState<Record<string, number>>({})
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function loadCategories() {
      try {
        const [categoryData, postCounts] = await Promise.all([
          getBlogCategories(),
          getCategoryPostCounts()
        ])
        setCategories(categoryData)
        setCategoryPostCounts(postCounts)
      } catch (error) {
        console.error('Error loading categories:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadCategories()
  }, [])

  if (isLoading) {
    return (
      <section id="categories" className="grid md:grid-cols-2 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="bg-card border-border animate-pulse">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="w-12 h-12 bg-accent rounded-lg"></div>
                <div className="text-right">
                  <div className="w-8 h-6 bg-accent rounded mb-1"></div>
                  <div className="w-12 h-4 bg-accent rounded"></div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0 space-y-4">
              <div className="h-6 bg-accent rounded w-3/4"></div>
              <div className="h-4 bg-accent rounded w-full"></div>
              <div className="h-4 bg-accent rounded w-2/3"></div>
            </CardContent>
          </Card>
        ))}
      </section>
    )
  }

  if (categories.length === 0) {
    return (
      <section id="categories" className="text-center py-12">
        <div className="text-6xl opacity-40 text-foreground mb-4">📁</div>
        <h3 className="text-xl font-semibold text-foreground mb-2">No Categories Yet</h3>
        <p className="text-foreground/70">
          Content categories will appear here once they are created.
        </p>
      </section>
    )
  }

  return (
    <section id="categories" className="grid md:grid-cols-2 gap-6">
      {categories.map((category) => {
        const IconComponent = categoryIcons[category.slug] || categoryIcons.default
        const postCount = categoryPostCounts[category.id] || 0
        
        return (
          <Card 
            key={category.id} 
            className="group hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20 bg-card border-border"
          >
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div 
                  className="p-3 rounded-lg"
                  style={{ backgroundColor: `${category.color}20` }}
                >
                  <IconComponent 
                    className="h-6 w-6"
                    style={{ color: category.color }}
                  />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-foreground">
                    {postCount}
                  </div>
                  <div className="text-sm text-foreground/70">
                    {postCount === 1 ? 'Article' : 'Articles'}
                  </div>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              <div className="space-y-4">
                <div>
                  <h3 className="text-xl font-bold text-foreground mb-2 group-hover:text-primary transition-colors">
                    {category.name}
                  </h3>
                  <p className="text-foreground/80 text-sm leading-relaxed">
                    {category.description}
                  </p>
                </div>
                
                <div className="pt-2">
                  <Link 
                    href={`/blog/category/${category.slug}`}
                    className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors group"
                  >
                    Explore Category
                    <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        )
      })}
    </section>
  )
} 