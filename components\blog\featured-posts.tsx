'use client'

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, User, ArrowRight } from "lucide-react"
import Link from "next/link"
import { useEffect, useState } from "react"
import { getFeaturedBlogPosts } from "@/lib/blog-service"
import type { BlogPost } from "@/lib/blog-service"

export function FeaturedPosts() {
  const [featuredPosts, setFeaturedPosts] = useState<BlogPost[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function loadFeaturedPosts() {
      try {
        const posts = await getFeaturedBlogPosts(3)
        setFeaturedPosts(posts)
      } catch (error) {
        console.error('Error loading featured posts:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadFeaturedPosts()
  }, [])

  if (isLoading) {
    return (
      <section id="featured" className="space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-foreground mb-4">Featured Articles</h2>
          <p className="text-lg text-foreground/80 max-w-2xl mx-auto">
            Our most impactful and insightful content, curated for medical professionals and healthcare enthusiasts
          </p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="h-full bg-card animate-pulse" style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
              <CardHeader className="p-0">
                <div className="aspect-video bg-accent rounded-t-lg"></div>
              </CardHeader>
              <CardContent className="p-6 space-y-4">
                <div className="h-4 bg-accent rounded w-3/4"></div>
                <div className="h-3 bg-accent rounded w-full"></div>
                <div className="h-3 bg-accent rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>
    )
  }

  if (featuredPosts.length === 0) {
    return (
      <section id="featured" className="space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-foreground mb-4">Featured Articles</h2>
          <p className="text-lg text-foreground/80 max-w-2xl mx-auto">
            Our most impactful and insightful content, curated for medical professionals and healthcare enthusiasts
          </p>
        </div>
        <div className="text-center py-12">
          <div className="text-6xl opacity-40 text-foreground mb-4">📝</div>
          <h3 className="text-xl font-semibold text-foreground mb-2">No Featured Articles Yet</h3>
          <p className="text-foreground/70">
            Featured articles will appear here once they are published and marked as featured.
          </p>
        </div>
      </section>
    )
  }

  return (
    <section id="featured" className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-foreground mb-4">Featured Articles</h2>
        <p className="text-lg text-foreground/80 max-w-2xl mx-auto">
          Our most impactful and insightful content, curated for medical professionals and healthcare enthusiasts
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {featuredPosts.map((post, index) => (
          <Card key={post.id} className={`h-full hover:shadow-lg transition-shadow bg-card ${index === 0 ? 'md:col-span-2 lg:col-span-1' : ''}`} style={{ border: '3px solid hsl(142, 76%, 36%)', borderRadius: '1rem', boxShadow: '0 4px 20px rgba(142, 176, 136, 0.15)' }}>
            <CardHeader className="p-0">
              <div className="relative">
                <div className="aspect-video bg-gradient-to-br from-primary/20 to-blue-500/20 rounded-t-lg flex items-center justify-center">
                  {post.featured_image_url ? (
                    <img 
                      src={post.featured_image_url} 
                      alt={post.featured_image_alt || post.title}
                      className="w-full h-full object-cover rounded-t-lg"
                    />
                  ) : (
                    <div className="text-6xl opacity-40 text-foreground">🏥</div>
                  )}
                </div>
                <div className="absolute top-4 left-4 flex gap-2">
                  {post.category && (
                    <Badge 
                      style={{ backgroundColor: post.category.color }}
                      className="text-foreground border-0"
                    >
                      {post.category.name}
                    </Badge>
                  )}
                  {post.is_featured && (
                    <Badge className="bg-orange-500 text-foreground border-0">
                      Featured
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="p-6">
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-foreground line-clamp-2 leading-tight">
                  {post.title}
                </h3>
                
                <p className="text-foreground/80 text-sm line-clamp-3">
                  {post.excerpt || post.content?.substring(0, 150) + '...'}
                </p>
                
                <div className="flex items-center gap-4 text-sm text-foreground/70">
                  <div className="flex items-center gap-1">
                    <User className="h-4 w-4" />
                    <span>{post.author?.name || 'Anonymous'}</span>
                  </div>
                  {post.published_at && (
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{new Date(post.published_at).toLocaleDateString()}</span>
                    </div>
                  )}
                  {post.reading_time_minutes && (
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>{post.reading_time_minutes} min</span>
                    </div>
                  )}
                </div>
                
                <div className="pt-2">
                  <Link 
                    href={`/blog/${post.slug}`}
                    className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors"
                  >
                    Read Article
                    <ArrowRight className="h-4 w-4" />
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  )
} 