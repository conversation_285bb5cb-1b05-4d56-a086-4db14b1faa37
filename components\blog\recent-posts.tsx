'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, User, ArrowRight } from "lucide-react"
import Link from "next/link"
import { useEffect, useState } from "react"
import { getRecentBlogPosts } from "@/lib/blog-service"
import type { BlogPost } from "@/lib/blog-service"

export function RecentPosts() {
  const [recentPosts, setRecentPosts] = useState<BlogPost[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function loadRecentPosts() {
      try {
        console.log('RecentPosts: Loading recent posts...')
        const posts = await getRecentBlogPosts(6)
        console.log('RecentPosts: Received posts:', posts)
        console.log('RecentPosts: Number of posts:', posts.length)
        setRecentPosts(posts)
      } catch (error) {
        console.error('Error loading recent posts:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadRecentPosts()
  }, [])

  if (isLoading) {
    return (
      <section className="space-y-8">
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="h-full bg-card border-border animate-pulse">
              <CardHeader className="pb-4">
                <div className="h-6 bg-accent rounded w-1/3"></div>
              </CardHeader>
              <CardContent className="pt-0 space-y-4">
                <div className="h-5 bg-accent rounded w-3/4"></div>
                <div className="h-4 bg-accent rounded w-full"></div>
                <div className="h-4 bg-accent rounded w-2/3"></div>
                <div className="flex gap-3">
                  <div className="h-3 bg-accent rounded w-20"></div>
                  <div className="h-3 bg-accent rounded w-16"></div>
                  <div className="h-3 bg-accent rounded w-12"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>
    )
  }

  if (recentPosts.length === 0) {
    return (
      <section className="space-y-8">
        <div className="text-center py-12">
          <div className="text-6xl opacity-40 text-foreground mb-4">📰</div>
          <h3 className="text-xl font-semibold text-foreground mb-2">No Recent Articles Yet</h3>
          <p className="text-foreground/70">
            Recent articles will appear here once they are published.
          </p>
        </div>
      </section>
    )
  }

  return (
    <section className="space-y-8">
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {recentPosts.map((post) => (
          <Card key={post.id} className="h-full hover:shadow-lg transition-shadow group bg-card border-border">
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between gap-4">
                {post.category && (
                  <Badge 
                    style={{ backgroundColor: post.category.color }}
                    className="text-foreground border-0 text-xs"
                  >
                    {post.category.name}
                  </Badge>
                )}
                {post.is_featured && (
                  <Badge variant="secondary" className="bg-orange-100 text-orange-700 text-xs">
                    Featured
                  </Badge>
                )}
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              <div className="space-y-4">
                <h3 className="text-lg font-bold text-foreground line-clamp-2 leading-tight group-hover:text-primary transition-colors">
                  {post.title}
                </h3>
                
                <p className="text-foreground/80 text-sm line-clamp-3">
                  {post.excerpt || post.content?.substring(0, 150) + '...'}
                </p>
                
                <div className="flex items-center gap-3 text-xs text-foreground/70">
                  <div className="flex items-center gap-1">
                    <User className="h-3 w-3" />
                    <span>{post.author?.name || 'Anonymous'}</span>
                  </div>
                  {post.published_at && (
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>{new Date(post.published_at).toLocaleDateString()}</span>
                    </div>
                  )}
                  {post.reading_time_minutes && (
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{post.reading_time_minutes} min</span>
                    </div>
                  )}
                </div>
                
                <div className="pt-2">
                  <Link 
                    href={`/blog/${post.slug}`}
                    className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium text-sm transition-colors group"
                  >
                    Read More
                    <ArrowRight className="h-3 w-3 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      <div className="text-center">
        <Link 
          href="/blog/all"
          className="inline-flex items-center gap-2 px-6 py-3 bg-primary text-foreground font-medium rounded-lg hover:bg-primary/90 transition-colors"
        >
          View All Articles
          <ArrowRight className="h-4 w-4" />
        </Link>
      </div>
    </section>
  )
} 