"use client"

import { useEffect, useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Loader2 } from "lucide-react"

interface ConnectionStatus {
  status: "loading" | "connected" | "disconnected"
  message: string
  responseTime?: number
}

export function ConnectionStatus() {
  const [status, setStatus] = useState<ConnectionStatus>({
    status: "loading",
    message: "Checking connection...",
  })

  useEffect(() => {
    const checkConnection = async () => {
      try {
        const response = await fetch("/api/connection-test")
        const data = await response.json()

        if (data.status === "success") {
          setStatus({
            status: "connected",
            message: data.message,
            responseTime: data.responseTime,
          })
        } else {
          setStatus({
            status: "disconnected",
            message: data.message,
            responseTime: data.responseTime,
          })
        }
      } catch (error) {
        setStatus({
          status: "disconnected",
          message: "Failed to check connection",
        })
      }
    }

    checkConnection()
  }, [])

  return (
    <div className="flex items-center gap-2">
      {status.status === "loading" && (
        <>
          <Loader2 className="h-4 w-4 animate-spin text-muted-green" />
          <span className="text-sm text-muted-green">Checking connection...</span>
        </>
      )}

      {status.status === "connected" && (
        <>
          <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
            Connected
          </Badge>
          <span className="text-sm text-muted-green">{status.responseTime ? `${status.responseTime}ms` : ""}</span>
        </>
      )}

      {status.status === "disconnected" && (
        <>
          <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">
            Disconnected
          </Badge>
          <span className="text-sm text-muted-green">{status.message}</span>
        </>
      )}
    </div>
  )
}

