"use client"

import { useState, useEffect } from "react"
import { Check, ChevronDown } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { useRouter } from "next/navigation"
import { getCachedCountries } from "@/lib/supabase-client"
import Image from "next/image"

export interface Country {
  country_id: string
  country_name: string
}

export function CountryDropdown() {
  const [open, setOpen] = useState(false)
  const [countries, setCountries] = useState<Country[]>([])
  const [selectedCountry, setSelectedCountry] = useState<Country | null>(null)
  const router = useRouter()

  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const data = await getCachedCountries()
        setCountries(data)
      } catch (error) {
        console.error("Error fetching countries:", error)
      }
    }

    fetchCountries()
  }, [])

  const handleCountrySelect = (country: Country) => {
    setSelectedCountry(country)
    setOpen(false)
    
    // Save country ID to localStorage for other components to access
    localStorage.setItem('selectedCountryId', country.country_id)
    
    // Create a custom event to notify other components about country change
    if (typeof window !== 'undefined') {
      const event = new Event('countryChanged')
      window.dispatchEvent(event)
    }
    
    // Check if we're on a standings or divisions page and refresh it
    const path = window.location.pathname
    if (path.includes('/standings') || path.includes('/divisions')) {
      // If on standings page, stay there but trigger a refresh
      window.location.reload()
    } else {
      // Otherwise navigate to divisions for the selected country
      router.push(`/divisions/${country.country_id}`)
    }
  }

  // Function to get country flag URL
  const getCountryFlagUrl = (countryName: string) => {
    // Convert country name to a standardized format for flag URLs
    const formattedName = countryName.toLowerCase().replace(/\s+/g, "-")
    return `/flags/${formattedName}.svg`
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" role="combobox" aria-expanded={open} className="w-full justify-between">
          {selectedCountry ? (
            <div className="flex items-center gap-2">
              <div className="relative w-6 h-4 overflow-hidden rounded">
                <Image
                  src={getCountryFlagUrl(selectedCountry.country_name) || "/placeholder.svg"}
                  alt={selectedCountry.country_name}
                  fill
                  className="object-cover"
                  onError={(e) => {
                    // Fallback if flag image fails to load
                    e.currentTarget.src = "/placeholder.svg?height=16&width=24"
                  }}
                />
              </div>
              {selectedCountry.country_name}
            </div>
          ) : (
            "Select a country"
          )}
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0">
        <Command>
          <CommandInput placeholder="Search country..." />
          <CommandList>
            <CommandEmpty>No country found.</CommandEmpty>
            <CommandGroup className="max-h-[300px] overflow-y-auto">
              {countries.map((country) => (
                <CommandItem
                  key={country.country_id}
                  value={country.country_name}
                  onSelect={() => handleCountrySelect(country)}
                >
                  <div className="flex items-center gap-2">
                    <div className="relative w-6 h-4 overflow-hidden rounded">
                      <Image
                        src={getCountryFlagUrl(country.country_name) || "/placeholder.svg"}
                        alt={country.country_name}
                        fill
                        className="object-cover"
                        onError={(e) => {
                          // Fallback if flag image fails to load
                          e.currentTarget.src = "/placeholder.svg?height=16&width=24"
                        }}
                      />
                    </div>
                    {country.country_name}
                  </div>
                  <Check
                    className={cn(
                      "ml-auto h-4 w-4",
                      selectedCountry?.country_id === country.country_id ? "opacity-100" : "opacity-0",
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

