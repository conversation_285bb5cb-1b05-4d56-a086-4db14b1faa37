"use client"

import { useState } from "react"
import Image from "next/image"
import { Globe2 } from "lucide-react"

export interface CountryFlagProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
}

export function CountryFlag({ src, alt, width = 24, height = 16, className = "" }: CountryFlagProps) {
  const [imgSrc, setImgSrc] = useState<string>(src)
  const [hasError, setHasError] = useState<boolean>(false)
  
  // Handle image load error
  const handleError = () => {
    if (!hasError) {
      setHasError(true)
      // We don't set an alternative source but show a fallback component
    }
  }

  if (hasError) {
    return (
      <div className={`flex items-center justify-center bg-primary/10 w-full h-full ${className}`}>
        <Globe2 className="w-6 h-6 text-primary/40" />
      </div>
    )
  }

  return (
    <div className={`relative w-full h-full ${className}`}>
      <Image
        src={imgSrc}
        alt={alt}
        fill
        className="object-cover"
        onError={handleError}
      />
    </div>
  )
} 