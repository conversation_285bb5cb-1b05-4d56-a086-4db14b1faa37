"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/context/AuthContext" // Import AuthContext
import { createClient } from "@supabase/supabase-js"
import Link from "next/link"
import { ArrowLeft, LayoutDashboard } from "lucide-react"
import { motion } from "framer-motion"
import { usePathname } from "next/navigation"

// No longer showing the button since we have a dashboard button in main navigation
export function DashboardReturnButton() {
  // Just return null to not render anything
  return null;
}