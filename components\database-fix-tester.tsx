"use client"

import { useState, useEffect } from "react"
import { usePathname } from "next/navigation"
import <PERSON>S<PERSON>us<PERSON><PERSON><PERSON> from "./database-status-checker"

// List of paths where we want to test database connectivity
const PATHS_TO_TEST = [
  "/",                // Home page
  "/standings",       // Standings page
  "/head-to-head",    // Head-to-head page
  "/teams",           // Teams page
  "/divisions/1",     // Leagues (already working)
]

// Function to create a dynamic import for testing specific pages
const createPageTester = (path: string) => {
  switch (path) {
    case "/":
      return async () => {
        const { getFeaturedDoctors, getTopDoctors } = await import("@/lib/hybrid-data-service")
        const featuredDoctors = await getFeaturedDoctors()
        const topDoctors = await getTopDoctors()
        return { 
          featuredDoctors: {
            success: featuredDoctors.length > 0 && featuredDoctors[0].doctor_id !== "error",
            count: featuredDoctors.length
          },
          topDoctors: {
            success: topDoctors.length > 0 && topDoctors[0].doctor_id !== "error",
            count: topDoctors.length
          }
        }
      }
    case "/standings":
      return async () => {
        const { getSpecialties } = await import("@/lib/hybrid-data-service")
        const specialties = await getSpecialties()
        return {
          specialties: {
            success: specialties.length > 0 && specialties[0].specialty_id !== "error",
            count: specialties.length
          }
        }
      }
    case "/head-to-head":
      return async () => {
        const { getDoctors } = await import("@/lib/services/doctors-service")
        const doctors = await getDoctors()
        return {
          doctors: {
            success: doctors.length > 0 && doctors[0].doctor_id !== "error",
            count: doctors.length
          }
        }
      }
    case "/teams":
      return async () => {
        const { getHospitals } = await import("@/lib/hybrid-data-service")
        const hospitals = await getHospitals()
        const hospitalsArray = Array.isArray(hospitals) ? hospitals : (hospitals as any).hospitals || []
        return {
          hospitals: {
            success: hospitalsArray.length > 0 && hospitalsArray[0]?.hospital_id !== "error",
            count: hospitalsArray.length
          }
        }
      }
    default:
      return async () => ({ noTest: true })
  }
}

export default function DatabaseFixTester({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const [testResults, setTestResults] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function runPageTest() {
      setLoading(true)
      try {
        // Only test on specific paths
        const matchingPath = PATHS_TO_TEST.find(p => pathname === p || pathname.startsWith(p + "/"))
        if (matchingPath) {
          const tester = createPageTester(matchingPath)
          const results = await tester()
          setTestResults(results)
        } else {
          setTestResults({ noTest: true })
        }
      } catch (error) {
        console.error("Error testing page data:", error)
        setTestResults({ error: true })
      } finally {
        setLoading(false)
      }
    }

    runPageTest()
  }, [pathname])

  // Get the currently active test based on the path
  const isTestingCurrentPage = PATHS_TO_TEST.some(p => pathname === p || pathname.startsWith(p + "/"))

  return (
    <>
      {children}
      <DatabaseStatusChecker showDetails={true} />

      {isTestingCurrentPage && !loading && testResults && !testResults.noTest && (
        <div className="fixed top-16 right-4 z-50 bg-background/90 p-4 rounded-lg shadow-lg text-foreground text-sm max-w-xs">
          <h3 className="font-bold mb-2">Page Data Test</h3>
          {Object.entries(testResults).map(([key, value]: [string, any]) => (
            <div key={key} className="mb-1">
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${value.success ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="capitalize">{key}: </span>
                <span>{value.success ? `Connected (${value.count})` : 'Using fallback data'}</span>
              </div>
            </div>
          ))}
        </div>
      )}
    </>
  )
} 