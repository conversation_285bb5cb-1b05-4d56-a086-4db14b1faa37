"use client"

import { useState, useEffect } from "react"
import { createClient } from "@supabase/supabase-js"
import { createBrowserClient } from "@/lib/supabase-client"
import { getFeaturedDoctors } from "@/lib/hybrid-data-service"

export default function DatabaseStatusChecker({ showDetails = false }) {
  const [status, setStatus] = useState<"loading" | "connected" | "error">("loading")
  const [details, setDetails] = useState<any>(null)

  useEffect(() => {
    async function checkConnection() {
      try {
        const results: any = {}
        
        // Check with direct client
        try {
          const directClient = createClient(
            "https://uapbzzscckhtptliynyj.supabase.co",
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q"
          )
          const { data, error } = await directClient.from("countries").select("*").limit(1)
          results.directClient = { success: !error, count: data?.length || 0 }
        } catch (e) {
          results.directClient = { success: false, error: e }
        }
        
        // Check with browser client
        try {
          const browserClient = createBrowserClient()
          const { data, error } = await browserClient.from("countries").select("*").limit(1)
          results.browserClient = { success: !error, count: data?.length || 0 }
        } catch (e) {
          results.browserClient = { success: false, error: e }
        }
        
        // Check with hybrid data service
        try {
          const doctors = await getFeaturedDoctors(2)
          results.hybridService = { 
            success: true, 
            count: doctors.length, 
            isFallback: doctors.length > 0 && doctors[0].doctor_id === "error" 
          }
        } catch (e) {
          results.hybridService = { success: false, error: e }
        }
        
        setDetails(results)
        
        // Determine overall status
        if (results.directClient.success || results.browserClient.success) {
          if (!results.hybridService.isFallback) {
            setStatus("connected")
          } else {
            setStatus("error")
          }
        } else {
          setStatus("error")
        }
      } catch (error) {
        console.error("Error checking database connection:", error)
        setStatus("error")
      }
    }
    
    checkConnection()
  }, [])
  
  return (
    <div className="fixed bottom-0 right-0 p-2 z-50">
      <div className={`flex items-center gap-2 rounded-full px-3 py-1.5 text-xs text-foreground ${
        status === "loading" ? "bg-background/60" : 
        status === "connected" ? "bg-green-500" : 
        "bg-red-500"
      }`}>
        <div className={`w-2 h-2 rounded-full ${
          status === "loading" ? "bg-green-300 animate-pulse" : 
          status === "connected" ? "bg-green-300" : 
          "bg-red-300"
        }`}></div>
        <span>DB: {status}</span>
      </div>
      
      {showDetails && details && (
        <div className="mt-2 p-3 bg-background/90 text-foreground text-xs rounded shadow-lg max-w-xs">
          <div className="mb-1">
            <span className="font-semibold">Direct Client:</span> 
            {details.directClient.success ? "Connected" : "Failed"}
          </div>
          <div className="mb-1">
            <span className="font-semibold">Browser Client:</span> 
            {details.browserClient.success ? "Connected" : "Failed"}
          </div>
          <div>
            <span className="font-semibold">Hybrid Service:</span> 
            {details.hybridService.success 
              ? details.hybridService.isFallback 
                ? "Using fallback data" 
                : "Connected" 
              : "Failed"}
          </div>
        </div>
      )}
    </div>
  )
} 