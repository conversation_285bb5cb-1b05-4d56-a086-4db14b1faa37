import type React from "react"
import { <PERSON> } from "lucide-react"

interface DoctorCardProps {
  doctor: {
    community_rating: number
    review_count: number
  }
}

const DoctorCard: React.FC<DoctorCardProps> = ({ doctor }) => {
  return (
    <div className="flex items-center gap-2">
      <div className="flex items-center">
        <Star className="w-5 h-5 text-yellow-500 fill-yellow-500" />
        <span className="text-xl font-bold ml-1">{doctor.community_rating?.toFixed(1)}</span>
      </div>
      <span className="text-foreground/60">({doctor.review_count} reviews)</span>
    </div>
  )
}

export default DoctorCard

