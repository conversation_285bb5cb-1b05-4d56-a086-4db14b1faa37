"use client"

import { useRef, useEffect, useState } from "react"

export function ECGAnimation() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [pulsePosition, setPulsePosition] = useState({ x: 0, y: 0 })

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions to match container width
    const resizeCanvas = () => {
      if (!canvas) return
      canvas.width = canvas.offsetWidth
      canvas.height = 80 // Fixed height for the ECG
    }

    resizeCanvas()
    window.addEventListener("resize", resizeCanvas)

    let x = 0
    const lineColor = "#00cc66"
    const bgColor = "rgba(0, 0, 0, 0.05)"
    let lastY = canvas.height / 2

    // ECG pattern generator
    const generateECGPoint = (x: number) => {
      const progress = (x % canvas.width) / canvas.width

      // Base ECG pattern
      if (progress < 0.1) {
        return canvas.height / 2 // Baseline
      } else if (progress < 0.2) {
        return canvas.height / 2 - 5 // P wave
      } else if (progress < 0.25) {
        return canvas.height / 2 // Back to baseline
      } else if (progress < 0.27) {
        return canvas.height / 2 - 40 // Q wave
      } else if (progress < 0.3) {
        return canvas.height / 2 + 60 // R wave (high spike)
      } else if (progress < 0.33) {
        return canvas.height / 2 - 20 // S wave
      } else if (progress < 0.4) {
        return canvas.height / 2 // Back to baseline
      } else if (progress < 0.5) {
        return canvas.height / 2 + 10 // T wave
      } else {
        return canvas.height / 2 // Baseline for the rest
      }
    }

    // Animation loop
    const animate = () => {
      // Add slight randomness to make it look more realistic
      const randomFactor = Math.random() * 3 - 1.5

      // Clear a small portion of the canvas ahead of the line
      ctx.fillStyle = bgColor
      ctx.fillRect(x, 0, 3, canvas.height)

      // Draw the ECG line
      const y = generateECGPoint(x) + randomFactor

      ctx.beginPath()
      ctx.moveTo(x - 1, lastY)
      ctx.lineTo(x, y)
      ctx.strokeStyle = lineColor
      ctx.lineWidth = 2
      ctx.stroke()

      lastY = y
      x = (x + 1) % canvas.width

      // If we're at the end of the canvas, clear and start over
      if (x === 0) {
        ctx.clearRect(0, 0, canvas.width, canvas.height)

        // Draw grid lines for the ECG background
        ctx.strokeStyle = "rgba(255, 255, 255, 0.1)"
        ctx.lineWidth = 0.5

        // Vertical grid lines
        for (let i = 0; i < canvas.width; i += 20) {
          ctx.beginPath()
          ctx.moveTo(i, 0)
          ctx.lineTo(i, canvas.height)
          ctx.stroke()
        }

        // Horizontal grid lines
        for (let i = 0; i < canvas.height; i += 20) {
          ctx.beginPath()
          ctx.moveTo(0, i)
          ctx.lineTo(canvas.width, i)
          ctx.stroke()
        }
      }

      // Update pulse position
      setPulsePosition({ x, y })

      requestAnimationFrame(animate)
    }

    animate()

    return () => {
      window.removeEventListener("resize", resizeCanvas)
    }
  }, [])

  return (
    <div className="w-full bg-background/10 overflow-hidden relative">
      <canvas ref={canvasRef} className="w-full h-[80px]" aria-hidden="true" />
      <div
        className="absolute w-3 h-3 rounded-full bg-red-500 shadow-[0_0_10px_rgba(255,0,0,0.7)]"
        style={{
          left: `${pulsePosition.x}px`,
          top: `${pulsePosition.y}px`,
          transform: "translate(-50%, -50%)",
          opacity: 0.8,
        }}
      />
    </div>
  )
}

