"use client"

import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

interface ECGDividerProps {
  className?: string
}

export function ECGDivider({ className }: ECGDividerProps) {
  return (
    <div className={cn("relative py-6 pb-3", className)}>
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-full max-w-5xl mx-auto relative h-[1px] bg-gradient-to-r from-transparent via-primary/30 to-transparent">
          <motion.div
            className="absolute top-1/2 -translate-y-1/2 w-24 h-12"
            initial={{ left: 0 }}
            animate={{ left: "100%" }}
            transition={{
              duration: 3,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
          >
            <svg viewBox="0 0 120 40" fill="none" className="w-full h-full text-primary drop-shadow-[0_0_3px_rgba(0,200,255,0.5)]" preserveAspectRatio="none">
              <motion.path
                d="M0 20h10l5-10 5 20 5-10h10l5-5 5 10 5-15 10 15 10 0 5-10 5 20 5-10h10l5-5 5 10 5-15 10 15"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                vectorEffect="non-scaling-stroke"
                initial={{ pathLength: 0, opacity: 0 }}
                animate={{ pathLength: 1, opacity: 1 }}
                transition={{ duration: 1.5, repeat: Number.POSITIVE_INFINITY, repeatType: "loop" }}
              />
            </svg>
          </motion.div>
        </div>
      </div>
      
      {/* Medical icon in the center */}
      <div className="relative flex justify-center">
        <motion.div 
          className="bg-background px-3 py-3 rounded-full border border-primary/30 shadow-[0_0_10px_rgba(0,200,255,0.2)]"
          whileHover={{ scale: 1.1, boxShadow: "0 0 15px rgba(0,200,255,0.4)" }}
          transition={{ duration: 0.3 }}
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-primary">
            <path d="M18 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V4C20 2.89543 19.1046 2 18 2Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M9 14H15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M12 11V17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M9 7H9.01" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M12 7H12.01" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M15 7H15.01" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </motion.div>
      </div>
    </div>
  )
}

