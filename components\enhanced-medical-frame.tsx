"use client"

import type React from "react"
import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import { Stethoscope, Microscope, Syringe } from "lucide-react"

interface EnhancedMedicalFrameProps {
  children: React.ReactNode
  className?: string
  variant?: "default" | "success" | "warning" | "patient" | "doctor"
  showPulse?: boolean
}

export function EnhancedMedicalFrame({
  children,
  className,
  variant = "default",
  showPulse = true,
}: EnhancedMedicalFrameProps) {
  // Define variant-specific colors and icons
  const variantStyles = {
    default: {
      borderColor: "border-primary",
      gradientFrom: "from-primary/20",
      gradientTo: "to-primary/40",
      iconColor: "text-primary",
      glowColor: "group-hover:shadow-primary/50",
    },
    success: {
      borderColor: "border-green-500",
      gradientFrom: "from-green-500/20",
      gradientTo: "to-green-500/40",
      iconColor: "text-green-500",
      glowColor: "group-hover:shadow-green-500/50",
    },
    warning: {
      borderColor: "border-yellow-500",
      gradientFrom: "from-yellow-500/20",
      gradientTo: "to-yellow-500/40",
      iconColor: "text-yellow-500",
      glowColor: "group-hover:shadow-yellow-500/50",
    },
    patient: {
      borderColor: "border-blue-500",
      gradientFrom: "from-blue-500/20",
      gradientTo: "to-blue-600/40",
      iconColor: "text-blue-500",
      glowColor: "group-hover:shadow-blue-500/50",
    },
    doctor: {
      borderColor: "border-primary",
      gradientFrom: "from-primary/20",
      gradientTo: "to-primary/40",
      iconColor: "text-primary",
      glowColor: "group-hover:shadow-primary/50",
    },
  }

  const styles = variantStyles[variant]

  return (
    <div
      className={cn(
        "relative p-8 rounded-xl overflow-hidden group transition-all duration-500",
        "border-2",
        styles.borderColor,
        "bg-background/90 backdrop-blur-lg",
        "hover:shadow-[0_0_30px_rgba(0,0,0,0.5)]",
        styles.glowColor,
        className,
      )}
    >
      {/* Medical Background Pattern */}
      <div className="absolute inset-0 overflow-hidden opacity-5">
        <svg className="absolute inset-0 w-full h-full" xmlns="http://www.w3.org/2000/svg">
          <pattern id="medical-grid" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
            <path
              d="M20 0v40M0 20h40M15 15h10v10H15z"
              fill="none"
              stroke="currentColor"
              strokeWidth="0.5"
              className={styles.iconColor}
            />
          </pattern>
          <rect width="100%" height="100%" fill="url(#medical-grid)" />
        </svg>
      </div>

      {/* Animated DNA Helix */}
      <div className="absolute inset-0 overflow-hidden opacity-10">
        <motion.div
          className="absolute inset-0"
          animate={{
            backgroundPositionY: ["0%", "100%"],
          }}
          transition={{
            duration: 20,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='120' viewBox='0 0 60 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M30 0c0 30 30 30 30 60s-30 30-30 60M30 0c0 30-30 30-30 60s30 30 30 60' fill='none' stroke='%2322C55E' strokeWidth='1'/%3E%3C/svg%3E")`,
            backgroundSize: "60px 120px",
          }}
        />
      </div>

      {/* Medical ECG Line Animation */}
      <div className="absolute bottom-0 left-0 right-0 h-[2px] overflow-hidden">
        <motion.div
          className={cn("h-full w-full", styles.iconColor)}
          animate={{
            backgroundPosition: ["0% 0%", "100% 0%"],
          }}
          transition={{
            duration: 3,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='200' height='2' viewBox='0 0 200 2' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 1h50l10-1 10 2 10-1h120' stroke='%2322C55E' fill='none'/%3E%3C/svg%3E")`,
            backgroundSize: "200px 2px",
          }}
        />
      </div>

      {/* Medical Symbols in Corners */}
      <div className="absolute top-2 left-2">
        <svg width="24" height="24" viewBox="0 0 24 24" className={styles.iconColor}>
          <path d="M12 2v20M2 12h20" stroke="currentColor" strokeWidth="2" />
        </svg>
      </div>
      <div className="absolute top-2 right-2">
        <svg width="24" height="24" viewBox="0 0 24 24" className={styles.iconColor}>
          <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" strokeWidth="2" />
          <path d="M12 8v8M8 12h8" stroke="currentColor" strokeWidth="2" />
        </svg>
      </div>
      <div className="absolute bottom-2 left-2">
        <svg width="24" height="24" viewBox="0 0 24 24" className={styles.iconColor}>
          <path d="M3 12h4l2-8 2 16 2-8h8" stroke="currentColor" strokeWidth="2" fill="none" />
        </svg>
      </div>
      <div className="absolute bottom-2 right-2">
        <svg width="24" height="24" viewBox="0 0 24 24" className={styles.iconColor}>
          <path
            d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
          />
          <path d="M12 8v8M8 12h8" stroke="currentColor" strokeWidth="2" />
        </svg>
      </div>

      {/* Corner Decorations */}
      <div className="absolute top-0 left-0 w-16 h-16">
        <motion.svg
          viewBox="0 0 64 64"
          className={cn("w-full h-full", styles.iconColor)}
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 2, ease: "easeInOut" }}
        >
          <path d="M2 62C2 28 28 2 62 2" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
          <path d="M14 14v8m-4-4h8" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
        </motion.svg>
      </div>

      {/* Heartbeat Line */}
      <motion.div
        className={cn("absolute -bottom-1 left-0 right-0 h-[2px]", styles.iconColor)}
        initial={{ scaleX: 0 }}
        animate={{ scaleX: 1 }}
        transition={{ duration: 1.5 }}
      >
        <motion.div
          className="absolute inset-0"
          animate={{
            backgroundPosition: ["0% 0%", "100% 0%"],
          }}
          transition={{
            duration: 2,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='200' height='2' viewBox='0 0 200 2' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 1h50l10-1 10 2 10-1h120' stroke='currentColor' fill='none'/%3E%3C/svg%3E")`,
            backgroundSize: "200px 2px",
          }}
        />
      </motion.div>

      {/* Medical Icons Animation */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute -right-8 top-1/4"
          animate={{
            y: [0, -20, 0],
            opacity: [0.2, 0.5, 0.2],
            scale: [0.8, 1, 0.8],
          }}
          transition={{
            duration: 4,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        >
          <Stethoscope className={cn("w-16 h-16", styles.iconColor)} />
        </motion.div>

        <motion.div
          className="absolute -left-8 bottom-1/4"
          animate={{
            y: [0, 20, 0],
            opacity: [0.2, 0.5, 0.2],
            scale: [0.8, 1, 0.8],
          }}
          transition={{
            duration: 4,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
            delay: 1,
          }}
        >
          <Microscope className={cn("w-16 h-16", styles.iconColor)} />
        </motion.div>

        <motion.div
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
          animate={{
            rotate: [0, 360],
            opacity: [0.1, 0.3, 0.1],
          }}
          transition={{
            duration: 20,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
        >
          <Syringe className={cn("w-32 h-32", styles.iconColor)} />
        </motion.div>
      </div>

      {/* Pulse Effect */}
      {showPulse && (
        <motion.div
          className={cn(
            "absolute inset-0 rounded-xl",
            "bg-gradient-to-r",
            styles.gradientFrom,
            styles.gradientTo,
            "opacity-0",
          )}
          animate={{
            opacity: [0, 0.2, 0],
            scale: [0.95, 1, 0.95],
          }}
          transition={{
            duration: 2,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        />
      )}

      {/* Content */}
      <div className="relative z-10">{children}</div>
    </div>
  )
}

