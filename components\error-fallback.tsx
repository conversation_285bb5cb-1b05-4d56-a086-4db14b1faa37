import { AlertTriangle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

interface ErrorFallbackProps {
  title?: string
  message?: string
  showHomeButton?: boolean
}

export function ErrorFallback({
  title = "Error loading data",
  message = "Please try again later or contact support if the problem persists.",
  showHomeButton = true,
}: ErrorFallbackProps) {
  return (
    <div className="text-center py-12">
      <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
      <h2 className="text-xl font-semibold text-foreground mb-2">{title}</h2>
      <p className="text-foreground/70">{message}</p>
      {showHomeButton && (
        <Button className="mt-6" variant="outline" asChild>
          <Link href="/">Return to Home</Link>
        </Button>
      )}
    </div>
  )
}

