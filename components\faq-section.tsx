"use client"

import { useState } from "react"
import { 
  Accordion,
  AccordionContent,
  AccordionI<PERSON>,
  AccordionTrigger
} from "@/components/ui/accordion"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { HelpCircle, Info, User, Hospital, Award, Newspaper, Search, Bolt } from "lucide-react"

// FAQ item interface
interface FAQItem {
  question: string;
  answer: string | JSX.Element;
}

// FAQ category interface
interface FAQCategory {
  id: string;
  label: string;
  icon: React.ReactNode;
  description: string;
  items: FAQItem[];
}

export function FAQSection() {
  // Define FAQ categories with their respective questions and answers
  const faqCategories: FAQCategory[] = [
    {
      id: "general",
      label: "General Information",
      icon: <Info className="h-5 w-5" />,
      description: "Basic information about Doctors League platform",
      items: [
        {
          question: "What is Doctors League?",
          answer: "Doctors League is a comprehensive platform designed to showcase and rank medical professionals across various specialties and countries. Using our sports-themed approach, doctors compete as 'Players' while patients serve as 'Referees' who evaluate and rate their performance. The application provides detailed statistics, comparisons, and rankings to help users identify top medical professionals in different fields."
        },
        {
          question: "Is the data on Doctors League real?",
          answer: "Yes, all doctor data is based on real medical professionals, with rankings calculated using our proprietary algorithm based on referee (patient) reviews, match outcomes (patient recovery), research contributions, and verified credentials."
        },
        {
          question: "How often are the rankings updated?",
          answer: "Rankings are updated on a monthly basis, with major updates occurring quarterly. All data is regularly verified to ensure accuracy and currency to provide the most reliable standings in our medical leagues."
        },
        {
          question: "Is Doctors League available worldwide?",
          answer: "Yes, Doctors League covers medical professionals from around the world, with detailed country-specific divisions and filters to help you find professionals in your region. You can select your country from the Leagues dropdown in the navigation menu."
        },
        {
          question: "Is Doctors League free to use?",
          answer: "Yes, the basic features of Doctors League are free for all spectators and referees. Premium features like detailed analytics and custom comparison reports are available with a subscription."
        }
      ]
    },
    {
      id: "patients",
      label: "For Referees & Spectators",
      icon: <User className="h-5 w-5" />,
      description: "How patients can benefit using Doctors League",
      items: [
        {
          question: "How can Doctors League help me find the right doctor?",
          answer: "Doctors League provides objective rankings and detailed profiles of medical professionals, allowing you to compare doctors based on credentials, expertise, patient outcomes, and referee (patient) reviews. This helps you make informed decisions about your healthcare providers."
        },
        {
          question: "Can I see which specialties a doctor is qualified in?",
          answer: "Yes, each player's (doctor's) profile includes detailed information about their specialties, subspecialties, education, certifications, and areas of expertise."
        },
        {
          question: "How does the rating system work?",
          answer: (
            <div>
              <p>Doctors are rated on a scale from 0 to 5 stars, based on multiple factors:</p>
              <ul className="list-disc pl-5 mt-2 space-y-1">
                <li>Referee (patient) reviews and ratings</li>
                <li>Match outcomes (patient recovery rates)</li>
                <li>Research contributions and publications</li>
                <li>Educational background and continuing education</li>
                <li>Hospital affiliations and positions</li>
              </ul>
            </div>
          )
        },
        {
          question: "Can I compare different doctors directly?",
          answer: "Yes, our Head-to-Head comparison feature allows you to compare up to 4 players (doctors) simultaneously across various metrics, helping you make the best choice for your healthcare needs."
        },
        {
          question: "How can I find doctors near me?",
          answer: "You can use the country selector in the Leagues dropdown menu and search filters to find doctors in your region. You can further filter by city or hospital to narrow down your search to local healthcare providers."
        }
      ]
    },
    {
      id: "doctors",
      label: "For Players (Doctors)",
      icon: <Hospital className="h-5 w-5" />,
      description: "Information for medical professionals",
      items: [
        {
          question: "How can I be listed as a Player on Doctors League?",
          answer: "Medical professionals are added to our database based on verified credentials and practice information. If you're a licensed medical professional and would like to join as a Player, click the 'Be a Referee or a Player' button in the navigation bar and select the Player option."
        },
        {
          question: "How are rankings calculated?",
          answer: (
            <div>
              <p>Rankings are calculated using our proprietary algorithm that takes into account:</p>
              <ul className="list-disc pl-5 mt-2 space-y-1">
                <li>Referee (patient) reviews and ratings</li>
                <li>Match outcomes (patient recovery rates)</li>
                <li>Research contributions and publications</li>
                <li>Educational background and continuing education</li>
                <li>Years of experience and certifications</li>
              </ul>
            </div>
          )
        },
        {
          question: "Can I update my profile information?",
          answer: "Yes, verified Players (doctors) can claim their profiles and update certain information such as current hospital affiliations, recent publications, and additional certifications. All updates are verified before being published."
        },
        {
          question: "What benefits do doctors get from being on Doctors League?",
          answer: "Being listed as a Player on Doctors League increases your visibility to potential patients, provides recognition of your expertise among peers, and offers insights into your professional standing within your specialty."
        },
        {
          question: "How can I improve my ranking?",
          answer: "Rankings are based on objective metrics that reflect professional excellence. Focus on continuing education, publishing research, improving patient outcomes, and maintaining strong professional relationships with your referees (patients)."
        }
      ]
    },
    {
      id: "features",
      label: "Features & Navigation",
      icon: <Bolt className="h-5 w-5" />,
      description: "How to use the platform's key features",
      items: [
        {
          question: "How do I use the Standings page?",
          answer: (
            <div>
              <p>The Standings page displays rankings of doctors organized by medical specialty:</p>
              <ol className="list-decimal pl-5 mt-2 space-y-1">
                <li>Navigate to the Standings page from the main menu</li>
                <li>Browse through different specialty cards</li>
                <li>Each card shows the top 3 players (doctors) in that specialty</li>
                <li>Click on a player's name to view their detailed profile</li>
                <li>Click "View Complete Rankings" to see all players in that specialty</li>
              </ol>
            </div>
          )
        },
        {
          question: "How does the Head-to-Head comparison work?",
          answer: (
            <div>
              <p>To compare doctors:</p>
              <ol className="list-decimal pl-5 mt-2 space-y-1">
                <li>Navigate to the Head-to-Head page</li>
                <li>Click "Add Doctor" to begin</li>
                <li>Search for and select a player (doctor)</li>
                <li>Add up to 4 players for comparison</li>
                <li>View detailed metrics side-by-side</li>
                <li>Remove players by clicking the "X" on their card</li>
              </ol>
            </div>
          )
        },
        {
          question: "What does the Teams page show?",
          answer: "The Teams page showcases medical institutions and hospitals. You can view information about each institution, including their overall rating, number of ranked players (doctors), specialty focus, and more. You can also see which doctors are affiliated with each institution."
        },
        {
          question: "How do I use the Divisions section?",
          answer: (
            <div>
              <p>The Divisions section organizes doctors by both country and specialty:</p>
              <ol className="list-decimal pl-5 mt-2 space-y-1">
                <li>Click on Leagues in the navigation menu</li>
                <li>Select a country from the dropdown menu</li>
                <li>Choose a medical specialty</li>
                <li>View ranked list of players (doctors) within that specialty and country</li>
                <li>Sort the list using column headers</li>
                <li>Click on any player's name to view their profile</li>
              </ol>
            </div>
          )
        },
        {
          question: "How do I find medical events?",
          answer: "Navigate to the Fixtures page to see upcoming conferences, competitions, and healthcare events. You can filter events by country and category, view detailed information about each event, and register interest in attending."
        }
      ]
    },
    {
      id: "technical",
      label: "Technical Support",
      icon: <Search className="h-5 w-5" />,
      description: "Help with technical issues and account management",
      items: [
        {
          question: "Which browsers are supported?",
          answer: "We support the latest versions of Google Chrome, Mozilla Firefox, Microsoft Edge, and Safari for the best experience."
        },
        {
          question: "Is there a mobile app available?",
          answer: "Currently, we offer a fully responsive website that works well on mobile devices. A dedicated mobile app is in development and will be released soon."
        },
        {
          question: "How do I create an account?",
          answer: (
            <div>
              <p>To create an account as a Referee (patient):</p>
              <ol className="list-decimal pl-5 mt-2 space-y-1">
                <li>Click the "Join Match" button in the top-right corner</li>
                <li>Fill in your details and follow the instructions</li>
                <li>Verify your email address</li>
                <li>Complete your profile information</li>
              </ol>
              <p className="mt-3">To create an account as a Player (doctor):</p>
              <ol className="list-decimal pl-5 mt-2 space-y-1">
                <li>Click the "Be a Referee or a Player" button in the top-right corner</li>
                <li>Select "Player" option</li>
                <li>Complete the medical professional registration form</li>
                <li>Submit your credentials for verification</li>
              </ol>
            </div>
          )
        },
        {
          question: "How can I report a bug or suggest a feature?",
          answer: "You can use the 'Contact Us' form in the footer, <NAME_EMAIL> with details about bugs or feature suggestions."
        },
        {
          question: "Is my data secure on Doctors League?",
          answer: "Yes, we take data security very seriously. All personal data is encrypted, and we follow industry best practices for data protection. For more details, please review our Privacy Policy."
        }
      ]
    },
    {
      id: "events",
      label: "Medical Events",
      icon: <Newspaper className="h-5 w-5" />,
      description: "Information about medical conferences and competitions",
      items: [
        {
          question: "What types of events are listed?",
          answer: (
            <div>
              <p>We list various types of medical events, including:</p>
              <ul className="list-disc pl-5 mt-2 space-y-1">
                <li>Medical conferences and symposiums</li>
                <li>Healthcare competitions and championships</li>
                <li>Educational seminars and workshops</li>
                <li>Professional networking events</li>
                <li>Research presentations and forums</li>
              </ul>
            </div>
          )
        },
        {
          question: "How can I register for an event?",
          answer: "On the event details page, click the 'Register Interest' button. For spectators, you'll be directed to join as a match spectator. For players (doctors), you'll be guided through the player registration process."
        },
        {
          question: "Can I submit an event to be listed?",
          answer: "Yes, event organizers can submit their medical events for listing. Use the 'Submit Event' option in the Events section or contact our team <NAME_EMAIL>."
        },
        {
          question: "How can I get notified about new events?",
          answer: "You can subscribe to event notifications by creating an account and selecting your areas of interest in your profile settings. Click the 'Subscribe to Notifications' button on the Fixtures page to get started."
        },
        {
          question: "Are the events verified?",
          answer: "Yes, all events are verified by our team before being listed to ensure they are legitimate and relevant to the medical community."
        }
      ]
    },
    {
      id: "recognition",
      label: "Awards & Recognition",
      icon: <Award className="h-5 w-5" />,
      description: "Information about the recognition system",
      items: [
        {
          question: "What are Doctor Leagues Awards?",
          answer: "Doctor Leagues Awards are annual recognitions given to top-performing players (doctors) in various specialties. They celebrate excellence in healthcare based on our comprehensive ranking system."
        },
        {
          question: "How are award winners selected?",
          answer: "Award winners are selected based on their performance in our ranking system, which considers referee (patient) reviews, match outcomes (patient recovery rates), research contributions, and other objective metrics of professional excellence."
        },
        {
          question: "Can I nominate a doctor for an award?",
          answer: "Yes, as a referee (patient), you can nominate outstanding medical professionals through the Awards section of our website. Your nominations help us recognize exceptional players in the medical field."
        },
        {
          question: "When are awards announced?",
          answer: "Major awards are announced annually in December, with quarterly recognition for emerging talents and specialty-specific achievements."
        },
        {
          question: "How can doctors display their awards?",
          answer: "Recognized players (doctors) receive digital badges and certificates that can be displayed on their profiles, personal websites, and social media. Physical awards are also presented at our annual gala event."
        }
      ]
    }
  ];

  return (
    <section id="faq" className="py-12 bg-background text-foreground">
      <div className="container mx-auto px-4">
        <div className="text-center mb-10">
          <Badge className="mb-2 bg-primary/20 text-primary hover:bg-primary/30 border-none">
            <HelpCircle className="h-4 w-4 mr-1" /> Help Center
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Frequently Asked Questions</h2>
          <p className="text-muted-green max-w-2xl mx-auto">
            Find answers to common questions about Doctors League, its features, and how to make the most of the platform.
          </p>
        </div>

        <Tabs defaultValue="general" className="w-full max-w-5xl mx-auto">
          <div className="mb-8 overflow-x-auto">
            <TabsList className="bg-background/50 border border-primary/20 h-auto p-1 flex justify-start w-max min-w-full md:justify-center md:w-full">
              {faqCategories.map((category) => (
                <TabsTrigger 
                  key={category.id}
                  value={category.id}
                  className="data-[state=active]:bg-primary/30 rounded-md flex items-center gap-1.5 px-4"
                  id={category.id}
                >
                  {category.icon}
                  <span>{category.label}</span>
                </TabsTrigger>
              ))}
            </TabsList>
          </div>
          
          {faqCategories.map((category) => (
            <TabsContent key={category.id} value={category.id}>
              <Card className="bg-background/40 backdrop-blur-md border-primary/20">
                <CardHeader className="pb-2 border-b border-primary/10">
                  <CardTitle className="text-foreground flex items-center gap-2">
                    {category.icon}
                    {category.label}
                  </CardTitle>
                  <CardDescription className="text-muted-green">
                    {category.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                  <Accordion type="single" collapsible className="w-full">
                    {category.items.map((item, index) => (
                      <AccordionItem 
                        key={index} 
                        value={`item-${index}`}
                        className="border-b border-primary/10 last:border-0"
                      >
                        <AccordionTrigger className="text-lg font-medium text-foreground hover:text-primary">
                          {item.question}
                        </AccordionTrigger>
                        <AccordionContent className="text-muted-green">
                          {item.answer}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </section>
  );
} 