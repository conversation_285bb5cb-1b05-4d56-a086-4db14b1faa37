"use client"

import Link from "next/link"
import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Menu, X, Trophy, Users, Calendar, Info, Globe, Building, ChevronDown, Stethoscope, Settings } from "lucide-react"
import { cn } from "@/lib/utils"
import dynamic from 'next/dynamic' // Import dynamic
import { motion } from "framer-motion"
import { ThemeToggle } from "@/components/theme-toggle"
// Dynamically import dialogs
const ChooseRoleDialog = dynamic(() => import('./registration/choose-role-dialog').then(mod => mod.ChooseRoleDialog), { ssr: false })
const ChooseRoleLoginDialog = dynamic(() => import('./login/choose-role-login-dialog').then(mod => mod.ChooseRoleLoginDialog), { ssr: false })
// Import our mock flag components instead of the country-flag-icons package
import { BH, KW, OM, QA, SA, AE } from "@/lib/mock-country-flags"
import Image from "next/image"
import { useAuth } from "@/context/AuthContext" // Import AuthContext

const NAV_ITEMS = [
  {
    name: "About Us",
    href: "/about",
    icon: Info,
  },
  {
    name: "Leagues",
    href: "#",
    icon: Globe,
  },
  {
    name: "Standings",
    href: "/standings",
    icon: Trophy,
  },
  {
    name: "Head-to-Head",
    href: "/head-to-head",
    icon: Users,
  },
  {
    name: "Teams",
    href: "/teams",
    icon: Building,
  },
  {
    name: "Fixtures",
    href: "/fixtures",
    icon: Calendar,
  },
]

const COUNTRY_FLAGS = {
  bahrain: BH,
  kuwait: KW,
  oman: OM,
  qatar: QA,
  "saudi-arabia": SA,
  uae: AE,
}

// Update the type definition to match Supabase database structure
export function HeaderClient({
  countries,
}: {
  countries: {
    country_id: string | number
    country_name: string
    flag_url?: string
    // Add any other fields from your Supabase countries table
  }[]
}) {
  const [isOpen, setIsOpen] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [showRegistration, setShowRegistration] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const [isTabletMenuOpen, setIsTabletMenuOpen] = useState(false)
  const [showLoginDialog, setShowLoginDialog] = useState(false)
  
  // Replace direct Supabase auth with AuthContext
  const { isAuthenticated, user: authUser, isLoading: authIsLoading } = useAuth()

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Remove the old checkAuthStatus function and useEffect that used Supabase auth
  
  return (
    <header
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-500",
        scrolled
                  ? "bg-gradient-to-r from-background/80 via-primary/10 to-background/80 backdrop-blur-lg shadow-lg border-b border-primary/20"
        : "bg-gradient-to-r from-background/90 via-background/95 to-background/90"
      )}
    >
      <div className="container mx-auto px-6">
        <div className="flex h-20 items-center justify-between gap-4">
          <Link href="/" className="flex items-center space-x-2 transition-all duration-300 hover:scale-105 brand-link">
            <motion.div
              initial={{ rotate: 0 }}
              animate={{ rotate: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Infinity, repeatType: "reverse", ease: "easeInOut" }}
              className="brand-logo-container"
            >
              <Image
                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo-gO7TTezH8tR3LvsxNVrbtcdUVAqKGB.png"
                alt="Doctor's Leagues"
                width={48}
                height={48}
                className="h-12 w-12 drop-shadow-[0_0_10px_rgba(0,200,255,0.3)]"
                priority
              />
            </motion.div>
            <div className="brand-text-container">
              <span className="text-primary font-bold text-2xl tracking-tight brand-text">Doctor's <span className="text-foreground brand-text">Leagues</span></span>
              <div className="text-xs text-muted-green -mt-1 brand-text">Where Healthcare Heroes Compete</div>
            </div>
          </Link>
          {/* Desktop Navigation (≥1366px) */}
          <nav className="hidden laptop:flex items-center main-nav-links space-x-0 desktop-nav">
            {NAV_ITEMS.map((item) => {
              if (item.name === "Leagues") {
                return (
                  <div key={item.name} className="relative">
                    <button
                      onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                      className="text-foreground/90 hover:text-foreground transition-colors duration-300 flex items-center gap-1 group relative px-3 py-2 overflow-hidden font-medium nav-menu-item nav-button"
                    >
                      <Globe className="group-hover:scale-110 transition-transform duration-300 text-primary nav-menu-icon" />
                      <span className="relative z-10 group-hover:text-primary transition-colors duration-300">{item.name}</span>
                      <ChevronDown
                        className={cn("transition-transform duration-300 text-primary/70",
                          isDropdownOpen ? "rotate-180" : ""
                        )}
                      />
                      <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-primary/0 via-primary/0 to-primary/0 group-hover:via-primary/10 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span>
                    </button>
                    {isDropdownOpen && (
                      <div className="absolute top-full left-0 mt-2 w-56 bg-gradient-to-b from-background to-background/95 backdrop-blur-md border border-primary/20 rounded-lg shadow-[0_10px_25px_-5px_rgba(0,0,0,0.7)] z-50">
                        <div className="py-2 px-1 max-h-[70vh] overflow-y-auto">
                          {countries && countries.length > 0 ? (
                            <>
                              <div className="px-3 py-2 text-xs text-green-300 border-b border-primary/10 mb-1">
                                Select a country league
                              </div>
                              {countries.map((country) => (
                                <Link
                                  key={country.country_id}
                                  href={`/leagues/${country.country_name.toLowerCase()}`}
                                  className="flex items-center gap-3 px-3 py-2 rounded-md hover:bg-green-600/80 focus:bg-green-600/80 text-foreground/90 hover:text-foreground focus:text-foreground transition-colors duration-200 my-1"
                                  onClick={() => setIsDropdownOpen(false)}
                                >
                                  <Image
                                    src={country.flag_url || '/placeholder-flag.png'}
                                    alt={`${country.country_name} flag`}
                                    width={20}
                                    height={15}
                                    className="rounded-sm"
                                  />
                                  <span>{country.country_name}</span>
                                </Link>
                              ))}
                            </>
                          ) : (
                            <div className="flex flex-col items-center justify-center py-8 text-center text-foreground/60">
                              <Globe className="w-10 h-10 mx-auto mb-2 text-primary/40" />
                              <p>No countries available</p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                )
              }
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-foreground/90 hover:text-foreground transition-colors duration-300 flex items-center gap-1 group relative px-3 py-2 overflow-hidden font-medium nav-menu-item nav-button"
                >
                  <item.icon className="group-hover:scale-110 transition-transform duration-300 text-primary nav-menu-icon" />
                  <span className="relative z-10 group-hover:text-primary transition-colors duration-300">{item.name}</span>
                  <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-primary/0 via-primary/0 to-primary/0 group-hover:via-primary/10 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span>
                </Link>
              )
            })}
          </nav>

          {/* Tablet/Laptop Dropdown Menu (768px-1365px) */}
          <div className="hidden md:flex laptop:hidden items-center tablet-laptop-menu">
            <DropdownMenu open={isTabletMenuOpen} onOpenChange={setIsTabletMenuOpen}>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="text-foreground/90 hover:text-foreground transition-colors duration-300 flex items-center gap-2 group relative px-3 py-2 overflow-hidden font-medium nav-menu-item"
                >
                  <div className="flex flex-col gap-1">
                    <span className={cn("w-5 h-0.5 bg-primary transition-all duration-300", isTabletMenuOpen ? "rotate-45 translate-y-1.5" : "")}></span>
                    <span className={cn("w-5 h-0.5 bg-primary transition-all duration-300", isTabletMenuOpen ? "opacity-0" : "")}></span>
                    <span className={cn("w-5 h-0.5 bg-primary transition-all duration-300", isTabletMenuOpen ? "-rotate-45 -translate-y-1.5" : "")}></span>
                  </div>
                  <span className="relative z-10 group-hover:text-primary transition-colors duration-300">Menu</span>
                  <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-primary/0 via-primary/0 to-primary/0 group-hover:via-primary/10 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="w-72 bg-gradient-to-b from-background to-background/95 backdrop-blur-md border border-primary/20 rounded-xl shadow-[0_20px_40px_-10px_rgba(0,0,0,0.8)] animate-in fade-in-80 slide-in-from-top-2 p-2"
              >
                <div className="space-y-1">
                  {NAV_ITEMS.map((item) => {
                    if (item.name === "Leagues") {
                      return (
                        <div key={item.name} className="space-y-1">
                          <button
                            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                            className="w-full flex items-center justify-between text-foreground/90 hover:text-foreground px-3 py-2.5 rounded-lg transition-all duration-300 hover:bg-primary/10 border border-transparent hover:border-primary/20 font-medium"
                          >
                            <div className="flex items-center gap-3">
                              <Globe className="w-4 h-4 text-primary" />
                              <span>{item.name}</span>
                            </div>
                            <motion.div
                              animate={{ rotate: isDropdownOpen ? 180 : 0 }}
                              transition={{ duration: 0.3 }}
                            >
                              <ChevronDown className="w-4 h-4 text-primary/70" />
                            </motion.div>
                          </button>
                          {isDropdownOpen && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: "auto" }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.3 }}
                              className="ml-6 space-y-1 max-h-48 overflow-y-auto"
                            >
                              {countries && countries.length > 0 ? (
                                countries.map((country) => (
                                  <Link
                                    key={country.country_id}
                                    href={`/leagues/${country.country_name.toLowerCase()}`}
                                    className="flex items-center gap-3 px-3 py-2 rounded-md hover:bg-primary/10 text-foreground/80 hover:text-foreground transition-colors duration-200"
                                    onClick={() => {
                                      setIsDropdownOpen(false)
                                      setIsTabletMenuOpen(false)
                                    }}
                                  >
                                    <Image
                                      src={country.flag_url || '/placeholder-flag.png'}
                                      alt={`${country.country_name} flag`}
                                      width={16}
                                      height={12}
                                      className="rounded-sm"
                                    />
                                    <span className="text-sm">{country.country_name}</span>
                                  </Link>
                                ))
                              ) : (
                                <div className="flex flex-col items-center justify-center py-4 text-center text-foreground/60">
                                  <Globe className="w-6 h-6 mx-auto mb-1 text-primary/40" />
                                  <p className="text-xs">No countries available</p>
                                </div>
                              )}
                            </motion.div>
                          )}
                        </div>
                      )
                    }

                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className="flex items-center gap-3 text-foreground/90 hover:text-foreground px-3 py-2.5 rounded-lg transition-all duration-300 hover:bg-primary/10 border border-transparent hover:border-primary/20 font-medium"
                        onClick={() => setIsTabletMenuOpen(false)}
                      >
                        <item.icon className="w-4 h-4 text-primary" />
                        <span>{item.name}</span>
                      </Link>
                    )
                  })}
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Original Mobile Navigation (hidden on md and up, shown on mobile) */}
          <nav className="md:hidden">
            {NAV_ITEMS.map((item) => {
              if (item.name === "Leagues") {
                return (
                  <DropdownMenu key={item.name} open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        className="text-foreground/90 hover:text-foreground transition-colors duration-300 flex items-center gap-1 group relative px-3 py-2 overflow-hidden font-medium nav-menu-item nav-button"
                        style={{ color: 'hsl(142, 76%, 36%)' }}
                      >
                        <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-primary/0 via-primary/0 to-primary/0 group-hover:via-primary/10 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span>
                        <Globe className="group-hover:scale-110 transition-transform duration-300 text-primary nav-menu-icon" />
                        <span className="relative nav-menu-item">Leagues</span>
                        <ChevronDown
                          className={cn("transition-transform duration-300 text-primary/70",
                            isDropdownOpen ? "rotate-180" : ""
                          )}
                        />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="center"
                      className="w-56 bg-gradient-to-b from-background to-background/95 backdrop-blur-md border border-primary/20 rounded-lg shadow-[0_10px_25px_-5px_rgba(0,0,0,0.7)] animate-in fade-in-80 slide-in-from-top-2"
                    >
                      <div className="py-2 px-1 max-h-[70vh] overflow-y-auto">
                        {countries && countries.length > 0 ? (
                          <>
                            <div className="px-3 py-2 text-xs text-green-300 border-b border-primary/10 mb-1">
                              Select a country league
                            </div>
                            {countries.map((country) => (
                              <DropdownMenuItem
                                key={country.country_id}
                                className="rounded-md hover:bg-green-600/80 focus:bg-green-600/80 text-foreground/90 hover:text-foreground focus:text-foreground transition-colors duration-200 my-1 nav-menu-item"
                              >
                                <Link
                                  href={`/divisions/${country.country_id}`}
                                  className="w-full px-3 py-2 flex items-center group"
                                  onClick={() => {
                                    console.log(`Navigating to country: ${country.country_name}, ID: ${country.country_id}`);
                                    localStorage.setItem('selectedCountryId', String(country.country_id));
                                    setIsDropdownOpen(false);
                                  }}
                                >
                                  <div className="w-6 h-4 mr-2 overflow-hidden rounded-sm shadow-sm transform group-hover:scale-105 transition-transform">
                                    {country.flag_url ? (
                                      <Image src={country.flag_url} alt={country.country_name} width={24} height={16} className="w-full h-full object-cover" />
                                    ) : (
                                      <div className="bg-primary/20 w-full h-full flex items-center justify-center">
                                        <span className="text-[8px] font-bold">{country.country_name.substring(0, 2).toUpperCase()}</span>
                                      </div>
                                    )}
                                  </div>
                                  <span className="group-hover:translate-x-1 transition-transform duration-200">{country.country_name}</span>
                                </Link>
                              </DropdownMenuItem>
                            ))}
                          </>
                        ) : (
                          <div className="px-3 py-6 text-center text-foreground/50">
                            <Globe className="w-10 h-10 mx-auto mb-2 text-primary/40" />
                            <p>No countries available</p>
                          </div>
                        )}
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )
              }

              return (
                <Link key={item.name} href={item.href}>
                  <Button
                    variant="ghost"
                    className="text-foreground/90 hover:text-foreground transition-colors duration-300 flex items-center gap-1 group relative px-3 py-2 overflow-hidden font-medium nav-menu-item nav-button"
                    style={{ color: 'hsl(142, 76%, 36%)' }}
                  >
                    <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-primary/0 via-primary/0 to-primary/0 group-hover:via-primary/10 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span>
                    <item.icon className="group-hover:scale-110 transition-transform duration-300 text-primary nav-menu-icon" />
                    <span className="relative nav-menu-item">{item.name}</span>
                  </Button>
                </Link>
              )
            })}
          </nav>
          <div className="hidden md:flex items-center space-x-3 action-buttons">
            <ThemeToggle />
            <Button
              variant="default"
              className="relative overflow-hidden bg-gradient-to-br from-primary/90 to-primary/70 hover:from-primary hover:to-primary/80 text-foreground shadow-md hover:shadow-[0_0_15px_rgba(0,160,255,0.5)] rounded-md flex items-center gap-1 font-medium transition-all duration-300 border border-primary/20 group transform hover:scale-105 nav-menu-item action-button px-2 md:px-3 py-1.5 text-xs md:text-sm"
              onClick={() => setShowRegistration(true)}
            >
              <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-primary/0 via-primary/0 to-primary/0 group-hover:via-primary/20 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span>
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                className="text-foreground relative"
              >
                <Stethoscope className="w-4 h-4" />
              </motion.div>
              <span className="relative nav-menu-item">
                <span className="hidden laptop:inline">Be a Referee or a Player</span>
                </span>
              <span className="absolute bottom-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-white/50 to-transparent transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></span>
            </Button>


            {/* Only show Join Match button if NOT authenticated */}
            {!isAuthenticated && !authIsLoading && (
              <Button
                variant="outline"
                className="relative overflow-hidden bg-gradient-to-br from-green-600/90 to-green-700/70 hover:from-green-600 hover:to-green-700/80 text-white border border-green-500/30 hover:border-green-500/50 rounded-md transition-all duration-300 hover:shadow-[0_0_10px_rgba(34,197,94,0.4)] group transform hover:scale-105 nav-menu-item action-button px-2 md:px-3 py-1.5 text-xs md:text-sm"
                style={{ color: '#ffffff', background: 'linear-gradient(135deg, hsl(142, 76%, 36%) 0%, hsl(142, 76%, 32%) 100%)' }}
                onClick={() => setShowLoginDialog(true)}
                data-join-match-button="true"
              >
                <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-green-500/0 via-green-500/0 to-green-500/0 group-hover:via-green-500/20 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span>
                <span className="relative nav-menu-item">Join Match</span>
                <span className="absolute bottom-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-white/50 to-transparent transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></span>
              </Button>
            )}

            {/* Show Dashboard button for authenticated patients */}
            {isAuthenticated && authUser?.userType === 'patient' && (
              <Link href="/patient/dashboard">
                <Button
                  variant="outline"
                  className="dashboard-button-light"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Dashboard
                </Button>
              </Link>
            )}

            {/* Show Dashboard button for authenticated doctors */}
            {isAuthenticated && authUser?.userType === 'doctor' && (
              <Link href="/doctor/dashboard-placeholder">
                <Button
                  variant="outline"
                  className="dashboard-button-light"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Dashboard
                </Button>
              </Link>
            )}


          </div>
          <button
            className="md:hidden text-primary hover:text-foreground p-2 rounded-md transition-colors duration-300 relative overflow-hidden group"
            onClick={() => setIsOpen(!isOpen)}
          >
            <span className="absolute inset-0 bg-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-md"></span>
            <motion.div
              animate={isOpen ? { rotate: 90 } : { rotate: 0 }}
              transition={{ duration: 0.3 }}
            >
              {isOpen ? <X size={24} /> : <Menu size={24} />}
            </motion.div>
          </button>
        </div>
      </div>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className="md:hidden bg-gradient-to-b from-background via-background/98 to-background/95 border-t border-primary/10 shadow-[0_10px_30px_-15px_rgba(0,0,0,0.7)]"
        >
          <div className="container mx-auto px-4 py-3">
            <nav className="flex flex-col space-y-1">
              {NAV_ITEMS.map((item) => {
                if (item.name === "Leagues") {
                  return (
                    <div key={item.name} className="space-y-1">
                      <button
                        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                        className="w-full flex items-center justify-between text-foreground/90 hover:text-foreground px-3 py-2.5 rounded-md transition-all duration-300 hover:bg-primary/10 border border-transparent hover:border-primary/20 font-medium nav-menu-item"
                      >
                        <div className="flex items-center gap-2">
                          <Globe className="text-primary nav-menu-icon" />
                          <span className="nav-menu-item">Leagues</span>
                        </div>
                        <motion.div
                          animate={{ rotate: isDropdownOpen ? 180 : 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          <ChevronDown className="w-4 h-4 text-primary/70" />
                        </motion.div>
                      </button>
                      {isDropdownOpen && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3 }}
                          className="bg-background/50 backdrop-blur-sm rounded-md border border-primary/10 mt-1 py-1"
                        >
                          {countries && countries.length > 0 ? (
                            countries.map((country) => (
                              <Link
                                key={country.country_id}
                                href={`/divisions/${country.country_id}`}
                                className="flex items-center px-4 py-2 text-foreground/80 hover:text-foreground hover:bg-primary/20 transition-colors duration-200 nav-menu-item"
                                onClick={() => {
                                  console.log(`Mobile menu: Navigating to country: ${country.country_name}, ID: ${country.country_id}`);
                                  localStorage.setItem('selectedCountryId', String(country.country_id));
                                  setIsOpen(false);
                                  setIsDropdownOpen(false);
                                }}
                              >
                                <div className="w-5 h-3.5 mr-2 overflow-hidden rounded-sm">
                                  {country.flag_url ? (
                                    <Image src={country.flag_url} alt={country.country_name} width={20} height={14} className="w-full h-full object-cover" />
                                  ) : (
                                    <div className="bg-primary/20 w-full h-full flex items-center justify-center">
                                      <span className="text-[8px] font-bold">{country.country_name.substring(0, 2).toUpperCase()}</span>
                                    </div>
                                  )}
                                </div>
                                <span className="nav-menu-item">{country.country_name}</span>
                              </Link>
                            ))
                          ) : (
                            <div className="px-3 py-4 text-center text-foreground/50">
                              <p>No countries available</p>
                            </div>
                          )}
                        </motion.div>
                      )}
                    </div>
                  )
                }
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    onClick={() => setIsOpen(false)}
                    className="flex items-center gap-2 text-foreground/90 hover:text-foreground hover:bg-primary/10 px-3 py-2.5 rounded-md transition-all duration-300 border border-transparent hover:border-primary/20 font-medium nav-menu-item"
                  >
                    <item.icon className="text-primary nav-menu-icon" />
                    <span className="nav-menu-item">{item.name}</span>
                  </Link>
                )
              })}
            </nav>

            <div className="flex flex-col space-y-2 mt-4 pt-4 border-t border-border/50">
              <div className="flex justify-center pb-2">
                <ThemeToggle />
              </div>
              <Button
                variant="default"
                className="bg-gradient-to-br from-primary/90 to-primary/70 hover:from-primary hover:to-primary/80 text-foreground shadow-md hover:shadow-[0_0_15px_rgba(0,160,255,0.5)] rounded-md px-5 py-2 flex items-center justify-center gap-2 font-medium transition-all duration-300 border border-primary/20 w-full transform hover:scale-105 relative overflow-hidden nav-menu-item action-button"
                onClick={() => {
                  setShowRegistration(true);
                  setIsOpen(false);
                }}
              >
                <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-primary/0 via-primary/0 to-primary/0 hover:via-primary/20 transition-all duration-500 transform -translate-x-full hover:translate-x-0"></span>
                <Stethoscope className="w-4 h-4" />
                <span className="relative nav-menu-item">Be a Referee or a Player</span>
                <span className="absolute bottom-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-white/50 to-transparent transform scale-x-0 hover:scale-x-100 transition-transform duration-500"></span>
              </Button>

              {/* Only show Join Match button in mobile menu if NOT authenticated */}
              {!isAuthenticated && !authIsLoading && (
                <Button
                  variant="outline"
                  className="bg-gradient-to-br from-green-600/90 to-green-700/70 hover:from-green-600 hover:to-green-700/80 text-foreground border border-green-500/30 hover:border-green-500/50 rounded-md font-medium transition-all duration-300 hover:shadow-[0_0_10px_rgba(34,197,94,0.4)] w-full transform hover:scale-105 relative overflow-hidden nav-menu-item action-button"
                  onClick={() => {
                    setShowLoginDialog(true);
                    setIsOpen(false);
                  }}
                  data-join-match-button="true"
                >
                  <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-green-500/0 via-green-500/0 to-green-500/0 group-hover:via-green-500/20 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span>
                  <span className="relative nav-menu-item">Join Match</span>
                  <span className="absolute bottom-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-white/50 to-transparent transform scale-x-0 hover:scale-x-100 transition-transform duration-500"></span>
                </Button>
              )}

              {/* Add Dashboard button for authenticated patients in mobile menu */}
              {isAuthenticated && authUser?.userType === 'patient' && (
                <Link href="/patient/dashboard">
                  <Button
                    variant="outline"
                    className="bg-gradient-to-br from-blue-600/90 to-blue-700/70 hover:from-blue-600 hover:to-blue-700/80 text-foreground border border-blue-500/30 hover:border-blue-500/50 rounded-md font-medium transition-all duration-300 hover:shadow-[0_0_10px_rgba(34,97,237,0.4)] w-full transform hover:scale-105 relative overflow-hidden nav-menu-item action-button"
                  >
                    <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-blue-500/0 via-blue-500/0 to-blue-500/0 group-hover:via-blue-500/20 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span>
                    <Settings className="mr-1 h-4 w-4 text-foreground" />
                    <span className="relative nav-menu-item">Dashboard</span>
                    <span className="absolute bottom-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-white/50 to-transparent transform scale-x-0 hover:scale-x-100 transition-transform duration-500"></span>
                  </Button>
                </Link>
              )}

              {/* Add Dashboard button for authenticated doctors in mobile menu */}
              {isAuthenticated && authUser?.userType === 'doctor' && (
                <Link href="/doctor/dashboard">
                  <Button
                    variant="outline"
                    className="bg-gradient-to-br from-blue-600/90 to-blue-700/70 hover:from-blue-600 hover:to-blue-700/80 text-foreground border border-blue-500/30 hover:border-blue-500/50 rounded-md font-medium transition-all duration-300 hover:shadow-[0_0_10px_rgba(34,97,237,0.4)] w-full transform hover:scale-105 relative overflow-hidden nav-menu-item action-button"
                  >
                    <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-blue-500/0 via-blue-500/0 to-blue-500/0 group-hover:via-blue-500/20 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span>
                    <Settings className="mr-1 h-4 w-4 text-foreground" />
                    <span className="relative nav-menu-item">Dashboard</span>
                    <span className="absolute bottom-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-white/50 to-transparent transform scale-x-0 hover:scale-x-100 transition-transform duration-500"></span>
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </motion.div>
      )}

      <ChooseRoleDialog open={showRegistration} onOpenChange={setShowRegistration} />
      
      {/* Only show login dialog if NOT authenticated */}
      {!isAuthenticated && (
        <ChooseRoleLoginDialog open={showLoginDialog} onOpenChange={setShowLoginDialog} />
      )}


    </header>
  )
}
