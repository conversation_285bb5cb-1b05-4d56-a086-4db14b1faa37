import { createClient } from "@supabase/supabase-js"
import { HeaderClient } from "./header-client"
import { FALLBACK_COUNTRIES } from "@/lib/fallback-data"
import { createServiceRoleClient } from "@/lib/supabase-client"
import Link from "next/link"
import Image from "next/image"

export async function Header() {
  console.log("Rendering Header component, accessing database with service role client...");
  
  try {
    // Create a Supabase client using the service role client from the centralized utility
    const supabaseServiceClient = createServiceRoleClient();
    
    // Fetch countries directly from Supabase using the service role client
    console.log("Attempting to fetch countries from database...");
    const { data: countries, error } = await supabaseServiceClient
      .from("countries")
      .select("*")
      .order("country_name");

    if (error) {
      console.error("Error fetching countries from database:", error);
      throw new Error(`Database error: ${error.message}`);
    }

    // Check if countries is empty or undefined
    if (!countries || countries.length === 0) {
      console.error("No countries returned from database (empty result)");
      throw new Error("No countries found in database");
    }

    // Log the countries for debugging
    console.log("Successfully fetched countries from database:", countries.length);
    console.log("Countries data:", JSON.stringify(countries));
    
    // Add flag URLs using a mapping since the flag_url column doesn't exist
    const countriesWithFlags = countries.map(country => {
      // Map of country names to flag URLs
      const flagUrls: Record<string, string> = {
        "Bahrain": "https://flagcdn.com/w320/bh.png",
        "Kuwait": "https://flagcdn.com/w320/kw.png",
        "Oman": "https://flagcdn.com/w320/om.png",
        "Qatar": "https://flagcdn.com/w320/qa.png",
        "Saudi Arabia": "https://flagcdn.com/w320/sa.png",
        "UAE": "https://flagcdn.com/w320/ae.png",
        "USA": "https://flagcdn.com/w320/us.png"
      };
      
      return {
        ...country,
        flag_url: flagUrls[country.country_name] || null
      };
    });
    
    console.log("Added flag URLs to countries data");
    
    // Pass the countries data to the HeaderClient component
    return <HeaderClient countries={countriesWithFlags} />;
  } catch (error) {
    console.error("Critical error in Header component:", error);
    // Fallback to static data in case of error
    console.log("Using fallback countries data due to error");
    return <HeaderClient countries={FALLBACK_COUNTRIES} />;
  }
}

