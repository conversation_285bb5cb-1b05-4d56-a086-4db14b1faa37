"use client"

// Import the MedicalModal component at the top of the file
import { MedicalModal } from "@/components/medical-modal"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"

export function HeroSection() {
  // Inside the component, add state for managing modal visibility
  const [isRefereeModalOpen, setIsRefereeModalOpen] = useState(false)
  const [isJoinMatchModalOpen, setIsJoinMatchModalOpen] = useState(false)

  return (
    <section className="bg-green-100 dark:bg-background py-20">
      <div className="container mx-auto text-center">
        <h1 className="text-4xl font-bold text-green-900 dark:text-foreground mb-4">
          Welcome to the Medical Community Platform
        </h1>
        <p className="text-lg text-muted-green dark:text-muted-green mb-8">
          Connect, collaborate, and contribute to the advancement of healthcare.
        </p>
        <div className="flex justify-center space-x-4">
          <Button
            variant="primary"
            onClick={() => setIsRefereeModalOpen(true)}
            className="action-button"
          >
            <span className="nav-menu-item">Be referee or Player</span>
          </Button>
          <Button
            variant="secondary"
            onClick={() => setIsJoinMatchModalOpen(true)}
            className="action-button"
            data-join-match-button="true"
          >
            <span className="nav-menu-item">Join Match</span>
          </Button>
        </div>
      </div>
      {/* Add the modals at the end of the component, before the return statement closes: */}
      <MedicalModal
        isOpen={isRefereeModalOpen}
        onClose={() => setIsRefereeModalOpen(false)}
        title="Become a Referee or Player"
      >
        <div className="space-y-4">
          <p>
            Join our medical community as a referee or player. Share your expertise and contribute to the advancement of
            healthcare.
          </p>

          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-md">
            <h3 className="font-medium text-blue-700 dark:text-blue-300 mb-2">Benefits:</h3>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Connect with other healthcare professionals</li>
              <li>Participate in medical discussions and debates</li>
              <li>Earn recognition for your expertise</li>
              <li>Help shape the future of healthcare</li>
            </ul>
          </div>

          <form className="space-y-4 mt-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-muted-green dark:text-muted-green mb-1">
                Full Name
              </label>
              <input
                type="text"
                id="name"
                className="w-full px-3 py-2 border border-green-400 dark:border-border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-background/90"
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-muted-green dark:text-muted-green mb-1">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                className="w-full px-3 py-2 border border-green-400 dark:border-border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-background/90"
              />
            </div>

            <div>
              <label htmlFor="specialty" className="block text-sm font-medium text-muted-green dark:text-muted-green mb-1">
                Medical Specialty
              </label>
              <select
                id="specialty"
                className="w-full px-3 py-2 border border-green-400 dark:border-border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-background/90"
              >
                <option value="">Select a specialty</option>
                <option value="cardiology">Cardiology</option>
                <option value="neurology">Neurology</option>
                <option value="pediatrics">Pediatrics</option>
                <option value="surgery">Surgery</option>
                <option value="other">Other</option>
              </select>
            </div>
          </form>
        </div>
      </MedicalModal>

      <MedicalModal isOpen={isJoinMatchModalOpen} onClose={() => setIsJoinMatchModalOpen(false)} title="Join a Match">
        <div className="space-y-4">
          <p>
            Join an upcoming match between medical professionals. Participate in discussions, debates, and
            knowledge-sharing sessions.
          </p>

          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-md">
            <h3 className="font-medium text-blue-700 dark:text-blue-300 mb-2">Available Matches:</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between border-b pb-2">
                <div>
                  <p className="font-medium">Cardiology vs. Cardiac Surgery</p>
                  <p className="text-sm text-muted-green dark:text-muted-green">March 15, 2023 • 2:00 PM</p>
                </div>
                <Button size="sm" className="bg-green-600 hover:bg-green-700 action-button">
                  <span className="nav-menu-item">Join</span>
                </Button>
              </div>

              <div className="flex items-center justify-between border-b pb-2">
                <div>
                  <p className="font-medium">Pediatrics vs. Family Medicine</p>
                  <p className="text-sm text-muted-green dark:text-muted-green">March 18, 2023 • 3:30 PM</p>
                </div>
                <Button size="sm" className="bg-green-600 hover:bg-green-700 action-button">
                  <span className="nav-menu-item">Join</span>
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Neurology vs. Neurosurgery</p>
                  <p className="text-sm text-muted-green dark:text-muted-green">March 22, 2023 • 1:00 PM</p>
                </div>
                <Button size="sm" className="bg-green-600 hover:bg-green-700 action-button">
                  <span className="nav-menu-item">Join</span>
                </Button>
              </div>
            </div>
          </div>

          <div className="mt-4">
            <h3 className="font-medium mb-2">Can't find a suitable match?</h3>
            <Button variant="outline" className="w-full action-button">
              <span className="nav-menu-item">Create Your Own Match</span>
            </Button>
          </div>
        </div>
      </MedicalModal>
    </section>
  )
}

