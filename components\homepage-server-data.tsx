import { createServerClient } from "@/lib/supabase-server"
import { Doctor } from "@/lib/hybrid-data-service"

interface HomepageServerDataProps {
  children: (data: {
    topDoctors: Doctor[]
    featuredDoctors: Doctor[]
    isLoading: boolean
    error: string | null
  }) => React.ReactNode
}

export async function HomepageServerData({ children }: HomepageServerDataProps) {
  let topDoctors: Doctor[] = []
  let featuredDoctors: Doctor[] = []
  let error: string | null = null

  try {
    console.log(`[${new Date().toISOString()}] SERVER: Fetching homepage doctors`)

    const supabase = createServerClient()

    // Single optimized server-side call
    const { data: allDoctors, error: rpcError } = await supabase.rpc('get_doctors_for_homepage', {
      limit_count: 16
    })

    if (rpcError) {
      console.error(`[${new Date().toISOString()}] SERVER: Error fetching doctors:`, rpcError)
      error = rpcError.message
    } else {
      const doctors = allDoctors || []
      console.log(`[${new Date().toISOString()}] SERVER: Retrieved ${doctors.length} doctors`)

      // Split the results: first 10 for standings, first 6 for featured
      topDoctors = doctors.slice(0, 10)
      featuredDoctors = doctors.slice(0, 6)
    }

  } catch (exception) {
    console.error(`[${new Date().toISOString()}] SERVER: Exception fetching doctors:`, exception)
    error = exception instanceof Error ? exception.message : 'Unknown error'
  }

  return (
    <>
      {children({
        topDoctors,
        featuredDoctors,
        isLoading: false, // Server-side data is already loaded
        error
      })}
    </>
  )
}