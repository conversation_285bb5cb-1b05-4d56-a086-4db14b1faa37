"use client"

import { Trophy, Star, Heart, User, Stethoscope, Activity, ChevronRight, Sparkles } from "lucide-react"

export const Icons = {
  logo: function LogoIcon({ className }: { className?: string }) {
    return (
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor" 
        strokeWidth="2" 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        className={className}
      >
        <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />
        <path d="M3.22 12H9.5l.5-1 .5 1h6.28" />
      </svg>
    )
  },
  trophy: Trophy,
  star: Star,
  heart: Heart,
  user: User,
  stethoscope: Stethoscope,
  activity: Activity,
  chevronRight: ChevronRight,
  sparkles: Sparkles,
}

export type Icon = keyof typeof Icons 