"use client"

import { ReactNode, useEffect, useState } from "react"
import { PagePositionAdDisplay } from "@/components/ads/page-position-ad-display"
import { Ad } from "@/actions/ad-actions"
import { createBrowserClient } from "@/lib/supabase-client"

interface PageWithAdsProps {
  children: ReactNode
  pageName: string
  showBannerAd?: boolean
  showSidebarAds?: boolean
  showBottomAd?: boolean
  showTestAds?: boolean
  className?: string
}

/**
 * A layout component that wraps page content with ads in standard positions
 */
export function PageWithAds({
  children,
  pageName,
  showBannerAd = true,
  showSidebarAds = true,
  showBottomAd = true,
  showTestAds = false,
  className = ""
}: PageWithAdsProps) {
  const [ads, setAds] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<any>(null);

  // Fetch ads for this page
  useEffect(() => {
    const fetchAds = async () => {
      setLoading(true);
      try {
        const supabase = createBrowserClient();
        
        // Map page names to possible variations in the database
        const pageVariations: Record<string, string[]> = {
          'home': ['home', 'homepage', 'home-page'],
          'about': ['about', 'about-us', 'aboutus'],
          'standings': ['standings', 'standing'],
          'divisions': ['divisions', 'division'],
          'specialties': ['specialties', 'specialty'],
          'teams': ['teams', 'team'],
          'head-to-head': ['head-to-head', 'headtohead', 'head_to_head'],
          'doctor-profile': ['doctor-profile', 'doctorprofile', 'doctor_profile'],
          'ratings': ['ratings', 'rating']
        };
        
        // Get all possible variations for this page name
        const pageNames = pageVariations[pageName] || [pageName];
        
        // Create query conditions for all possible placements
        const { data, error } = await supabase
          .from('ads')
          .select('*')
          .eq('status', 'active')
          .or(pageNames.map(page => `placements.cs.{${page}}`).join(','))
          .order('created_at', { ascending: false });
        
        if (error) {
          console.error(`Error fetching ads for ${pageName}:`, error);
          setError(error);
          setAds([]);
        } else if (data) {
          setAds(data);
          setError(null);
          console.log(`Fetched ${data.length} ads for ${pageName}`);
        }
      } catch (error) {
        console.error(`Error in fetchAds for ${pageName}:`, error);
        setError(error);
        setAds([]);
      } finally {
        setLoading(false);
      }
    };

    fetchAds();
  }, [pageName]);

  return (
    <div className="relative">
      {/* Left sidebar ads */}
      {showSidebarAds && (
        <div className="fixed left-4 top-[180px] z-10 hidden xl:block">
          <PagePositionAdDisplay
            ads={ads}
            pageName={pageName}
            position="side-left"
            showTestAd={showTestAds || loading || ads.length === 0}
            maxWidth={160}
          />
        </div>
      )}

      {/* Right sidebar ads */}
      {showSidebarAds && (
        <div className="fixed right-4 top-[180px] z-10 hidden xl:block">
          <PagePositionAdDisplay
            ads={ads}
            pageName={pageName}
            position="side-right"
            showTestAd={showTestAds || loading || ads.length === 0}
            maxWidth={160}
          />
        </div>
      )}

      {/* Main content with banner and bottom ads */}
      <div className={`mx-auto max-w-screen-xl px-4 ${className}`}>
        {/* Banner ad */}
        {showBannerAd && (
          <div className="my-6 flex justify-center w-full">
            <PagePositionAdDisplay
              ads={ads}
              pageName={pageName}
              position="banner"
              showTestAd={showTestAds || loading || ads.length === 0}
              maxWidth={728}
            />
          </div>
        )}

        {/* Main content */}
        <main>{children}</main>

        {/* Bottom ad */}
        {showBottomAd && (
          <div className="my-6 flex justify-center w-full">
            <PagePositionAdDisplay
              ads={ads}
              pageName={pageName}
              position="bottom"
              showTestAd={showTestAds || loading || ads.length === 0}
              maxWidth={728}
            />
          </div>
        )}
      </div>
    </div>
  );
}
