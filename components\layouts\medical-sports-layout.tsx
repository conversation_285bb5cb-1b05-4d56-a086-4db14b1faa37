import type React from "react"
import { cn } from "@/lib/utils"

interface MedicalSportsLayoutProps {
  children: React.ReactNode
  className?: string
  variant?: "doctor" | "patient"
}

export function MedicalSportsLayout({ children, className, variant = "doctor" }: MedicalSportsLayoutProps) {
  const bgColor = variant === "doctor" ? "from-primary-800 to-primary-900" : "from-secondary-800 to-secondary-900"

  return (
    <div className={cn("min-h-screen bg-gradient-to-br", bgColor, className)}>
      <div className="container mx-auto py-8 px-4">{children}</div>
    </div>
  )
}

