"use client"

import React, { Suspense, lazy, ComponentType, ComponentProps } from 'react'

interface LazyComponentProps<T extends ComponentType<any>> {
  component: () => Promise<{ default: T }>
  fallback?: React.ReactNode
  props?: ComponentProps<T>
}

/**
 * LazyComponent - A wrapper component that implements code splitting
 * through React.lazy and Suspense to reduce initial bundle size.
 * 
 * @param component - Function that imports the component to be lazy loaded
 * @param fallback - Optional fallback component/element while loading
 * @param props - Props to pass to the lazy loaded component
 */
export function LazyComponent<T extends ComponentType<any>>({
  component,
  fallback = <div className="w-full h-32 flex items-center justify-center">
    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
  </div>,
  props = {} as ComponentProps<T>
}: LazyComponentProps<T>) {
  const LazyComp = lazy(component)
  
  return (
    <Suspense fallback={fallback}>
      <LazyComp {...props} />
    </Suspense>
  )
}

export default LazyComponent 