"use client"

import { Use<PERSON>, Stethos<PERSON>, X, BellIcon as Whistle } from "lucide-react"
import { <PERSON><PERSON>, DialogContent } from "@/lib/mock-radix-dialog"
import { useState, useEffect } from "react"
import { LoginDialog } from "./login-dialog"
import { MedicalSportsFrame } from "../medical-sports-frame"
import { motion } from "framer-motion"
import { ChooseRoleDialog } from "../registration/choose-role-dialog"
import { Button } from "../ui/button"
import { SignUpDialog } from "./sign-up-dialog"

interface ChooseRoleLoginDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  ratingContext?: 'rateDoctor'
  onLoginSuccess?: (role: "patient" | "doctor") => void
  redirectUrl?: string
}

export function ChooseRoleLoginDialog({ 
  open, 
  onOpenChange, 
  ratingContext,
  onLoginSuccess,
  redirectUrl
}: ChooseRoleLoginDialogProps) {
  const [showSignUp, setShowSignUp] = useState(false)
  const [showLogin, setShowLogin] = useState(false)
  const [selectedRole, setSelectedRole] = useState<"patient" | "doctor" | null>(null)

  useEffect(() => {
    if (open && ratingContext === 'rateDoctor') {
      setSelectedRole('patient');
    }
  }, [open, ratingContext]);

  const handleRoleSelect = (role: "patient" | "doctor") => {
    setSelectedRole(role)
    setShowLogin(true)
    onOpenChange(false)
  }

  const handleSignUpClick = () => {
    setShowSignUp(true)
    onOpenChange(false)
  }

  const handleLoginSuccess = (role: "patient" | "doctor") => {
    if (onLoginSuccess) {
      onLoginSuccess(role);
    }
    
    if (redirectUrl) {
      window.location.href = redirectUrl;
    }
  }

  if (selectedRole) {
    return (
      <LoginDialog
        open={showLogin}
        onOpenChange={setShowLogin}
        userType={selectedRole}
        onSignUpClick={handleSignUpClick}
        onLoginSuccess={handleLoginSuccess}
        redirectUrl={redirectUrl}
      />
    )
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-2xl p-0 border-0 choose-role-login-dialog">
          <div className="purple-dialog-bg p-2 rounded-xl overflow-hidden">
            <div className="p-6 space-y-6">
              <motion.div
                className="flex items-center gap-3 bg-primary/10 p-3 rounded-lg border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Whistle className="h-6 w-6 text-primary" />
                <p className="text-sm text-foreground font-medium">Select your role to join the match</p>
              </motion.div>

              <motion.h2
                className="text-2xl font-bold text-center text-foreground"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                Choose Your Role
              </motion.h2>

              <div className="grid grid-cols-2 gap-6">
                <motion.button
                  onClick={() => handleRoleSelect("patient")}
                  className="flex flex-col items-center gap-4 p-6 rounded-lg border-2 border-blue-500/30 hover:border-blue-500 bg-blue-50 hover:bg-blue-100 dark:bg-blue-800 dark:hover:bg-blue-700 transition-all duration-200"
                  whileHover={{ y: -5 }}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.2 }}
                  data-patient="true"
                  data-patient-form="true"
                >
                  <User className="h-12 w-12 text-blue-500" />
                  <div className="space-y-1 text-center">
                    <h3 className="font-semibold text-foreground">Patient</h3>
                    <p className="text-sm text-foreground">Find and rate your healthcare heroes</p>
                  </div>
                </motion.button>

                <motion.button
                  onClick={() => handleRoleSelect("doctor")}
                  className={`flex flex-col items-center gap-4 p-6 rounded-lg border-2 border-primary/30 hover:border-primary bg-green-50 hover:bg-green-100 dark:bg-green-800 dark:hover:bg-green-700 transition-all duration-200 ${
                    ratingContext === 'rateDoctor' ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                  whileHover={ratingContext !== 'rateDoctor' ? { y: -5 } : {}}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.3 }}
                >
                  <Stethoscope className="h-12 w-12 text-primary" />
                  <div className="space-y-1 text-center">
                    <h3 className="font-semibold text-foreground">Doctor</h3>
                    <p className="text-sm text-foreground">Join the league and showcase your expertise</p>
                    {ratingContext === 'rateDoctor' && (
                      <p className="text-xs text-red-400 mt-1">Only patients can rate doctors</p>
                    )}
                  </div>
                </motion.button>
              </div>
            </div>

            <button
              onClick={() => onOpenChange(false)}
              className="absolute right-4 top-4 rounded-full bg-card p-2 text-foreground hover:bg-accent transition-colors"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </button>
          </div>
        </DialogContent>
      </Dialog>

      {showSignUp && (
        <SignUpDialog
          open={showSignUp}
          onOpenChange={setShowSignUp}
          onLoginClick={() => {
            setShowSignUp(false)
            setShowLogin(true)
          }}
        />
      )}
    </>
  )
}

