"use client"

import { useEffect } from "react"
import { <PERSON><PERSON>, DialogContent } from "@/lib/mock-radix-dialog"
import { ChooseRoleDialog } from "../registration/choose-role-dialog"

interface SignUpDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onLoginClick?: () => void
}

export function SignUpDialog({
  open,
  onOpenChange,
  onLoginClick,
}: SignUpDialogProps) {
  // This is a simple redirect wrapper to the ChooseRoleDialog
  return (
    <ChooseRoleDialog 
      open={open} 
      onOpenChange={(isOpen) => {
        onOpenChange(isOpen)
        if (!isOpen && onLoginClick) {
          onLoginClick()
        }
      }} 
    />
  )
} 