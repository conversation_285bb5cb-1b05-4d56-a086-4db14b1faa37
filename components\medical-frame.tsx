"use client"

import type React from "react"

import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import { Heart, Activity, Dna } from "lucide-react"

interface MedicalFrameProps {
  children: React.ReactNode
  className?: string
  variant?: "default" | "success" | "warning" | "patient" | "doctor"
  showPulse?: boolean
}

export function MedicalFrame({ children, className, variant = "default", showPulse = true, ...props }: MedicalFrameProps & React.HTMLAttributes<HTMLDivElement>) {
  // Light theme styles for MedicalFrame
  const lightThemeStyles = `
    /* Light Theme: MedicalFrame styling */
    html:not(.dark) [data-registration-confirmation="true"] {
      background: linear-gradient(135deg, hsl(140, 60%, 95%) 0%, hsl(140, 50%, 90%) 100%) !important;
      border-color: hsl(140, 50%, 60%) !important;
    }
    
    html:not(.dark) [data-registration-confirmation="true"] * {
      color: hsl(140, 70%, 25%) !important;
    }
    
    html:not(.dark) [data-registration-confirmation="true"] h1,
    html:not(.dark) [data-registration-confirmation="true"] h2,
    html:not(.dark) [data-registration-confirmation="true"] h3 {
      color: hsl(140, 80%, 20%) !important;
    }
    
    /* Enhanced Light Theme Support for All Dialog Types */
    html:not(.dark) .medical-frame-container {
      background: linear-gradient(135deg, hsl(210, 20%, 98%) 0%, hsl(210, 15%, 96%) 100%) !important;
    }
    
    /* Patient Dialogs - Blue Theme */
    html:not(.dark) .medical-frame-container.patient-variant {
      background: linear-gradient(135deg, hsl(210, 80%, 98%) 0%, hsl(210, 70%, 96%) 100%) !important;
      border-color: hsl(210, 60%, 70%) !important;
    }
    
    html:not(.dark) .medical-frame-container.patient-variant .text-foreground {
      color: hsl(210, 80%, 25%) !important;
    }
    
    /* Doctor Dialogs - Green Theme */
    html:not(.dark) .medical-frame-container.doctor-variant {
      background: linear-gradient(135deg, hsl(120, 40%, 98%) 0%, hsl(120, 30%, 96%) 100%) !important;
      border-color: hsl(120, 50%, 70%) !important;
    }
    
    html:not(.dark) .medical-frame-container.doctor-variant .text-foreground {
      color: hsl(120, 60%, 25%) !important;
    }
  `

  // Define variant-specific colors and icons
  const variantStyles = {
    default: {
      borderColor: "border-primary",
      gradientFrom: "from-primary/20",
      gradientTo: "to-primary/40",
      iconColor: "text-primary",
    },
    success: {
      borderColor: "border-green-500",
      gradientFrom: "from-green-500/20",
      gradientTo: "to-green-500/40",
      iconColor: "text-green-500",
    },
    warning: {
      borderColor: "border-yellow-500",
      gradientFrom: "from-yellow-500/20",
      gradientTo: "to-yellow-500/40",
      iconColor: "text-yellow-500",
    },
    patient: {
      borderColor: "border-blue-500",
      gradientFrom: "from-blue-500/20",
      gradientTo: "to-blue-500/40",
      iconColor: "text-blue-500",
    },
    doctor: {
      borderColor: "border-primary",
      gradientFrom: "from-primary/20",
      gradientTo: "to-primary/40",
      iconColor: "text-primary",
    },
  }

  const styles = variantStyles[variant]

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: lightThemeStyles }} />
      <div
        {...props}
        className={cn(
          "relative p-8 rounded-xl overflow-hidden medical-frame-container",
          variant === "patient" && "patient-variant",
          variant === "doctor" && "doctor-variant",
          "border-2",
          styles.borderColor,
          "bg-background/90 backdrop-blur-lg",
          className,
        )}
      >
      {/* Animated medical background pattern */}
      <div className="absolute inset-0 overflow-hidden opacity-5 pointer-events-none">
        <motion.div
          className="absolute inset-0 w-full h-full"
          animate={{
            backgroundPosition: ["0% 0%", "100% 100%"],
          }}
          transition={{
            duration: 20,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M30 10v40M10 30h40' stroke='%2322C55E' strokeWidth='1'/%3E%3C/svg%3E")`,
            backgroundSize: "60px 60px",
          }}
        />
      </div>

      {/* Corner decorations */}
      <div className="absolute top-0 left-0 w-16 h-16">
        <svg viewBox="0 0 64 64" className={cn("w-full h-full", styles.iconColor)}>
          <path d="M2 62C2 28 28 2 62 2" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
          <path d="M14 14v8m-4-4h8" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
        </svg>
      </div>

      <div className="absolute top-0 right-0 w-16 h-16">
        <svg viewBox="0 0 64 64" className={cn("w-full h-full", styles.iconColor)}>
          <path d="M62 62C28 62 2 28 2 2" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
          <path d="M14 14C14 18 18 22 22 22" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
        </svg>
      </div>

      <div className="absolute bottom-0 left-0 w-16 h-16">
        <svg viewBox="0 0 64 64" className={cn("w-full h-full", styles.iconColor)}>
          <path d="M2 2C2 36 28 62 62 62" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
          <path d="M20 44l4-8 4 16 4-8h8" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
        </svg>
      </div>

      <div className="absolute bottom-0 right-0 w-16 h-16">
        <svg viewBox="0 0 64 64" className={cn("w-full h-full", styles.iconColor)}>
          <path d="M62 2C62 36 36 62 2 62" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
          <path d="M44 44c-4 4-12 4-16 0s-4-12 0-16 12-4 16 0" fill="none" stroke="currentColor" strokeWidth="2" />
        </svg>
      </div>

      {/* Animated icons */}
      <div className="absolute inset-0 overflow-hidden opacity-10 pointer-events-none">
        <motion.div
          className={cn("absolute -right-8 top-1/4 w-16 h-16", styles.iconColor)}
          animate={{
            y: [0, 10, 0],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 4,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        >
          <Heart className="w-full h-full" />
        </motion.div>

        <motion.div
          className={cn("absolute -left-8 bottom-1/4 w-16 h-16", styles.iconColor)}
          animate={{
            y: [0, -10, 0],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 5,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
            delay: 1,
          }}
        >
          <Activity className="w-full h-full" />
        </motion.div>

        <motion.div
          className={cn("absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-32 h-32", styles.iconColor)}
          animate={{
            rotate: [0, 360],
            opacity: [0.05, 0.1, 0.05],
          }}
          transition={{
            duration: 20,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
        >
          <Dna className="w-full h-full" />
        </motion.div>
      </div>

      {/* Pulse effect */}
      {showPulse && (
        <motion.div
          className={cn(
            "absolute inset-0",
            "bg-gradient-to-r",
            styles.gradientFrom,
            styles.gradientTo,
            "opacity-0 rounded-xl",
          )}
          animate={{
            opacity: [0, 0.15, 0],
          }}
          transition={{
            duration: 2.5,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        />
      )}

      {/* Inner border glow */}
      <div
        className={cn(
          "absolute inset-[1px] rounded-xl pointer-events-none",
          "bg-gradient-to-r",
          styles.gradientFrom,
          styles.gradientTo,
          "opacity-20",
        )}
      />

      {/* Content */}
      <div className="relative z-10">{children}</div>
    </div>
    </>
  )
}

