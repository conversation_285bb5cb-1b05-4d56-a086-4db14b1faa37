"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface MedicalModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
}

export function MedicalModal({ isOpen, onClose, title, children }: MedicalModalProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true)
    } else {
      const timer = setTimeout(() => {
        setIsVisible(false)
      }, 300)
      return () => clearTimeout(timer)
    }
  }, [isOpen])

  if (!isVisible) return null

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-background/50 backdrop-blur-sm transition-opacity duration-300 ${
        isOpen ? "opacity-100" : "opacity-0"
      }`}
      onClick={onClose}
    >
      <div
        className={`relative bg-white dark:bg-background rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-auto transition-all duration-300 transform ${
          isOpen ? "scale-100" : "scale-95"
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Medical-themed header */}
        <div className="bg-gradient-to-r from-blue-600 to-cyan-500 p-4 rounded-t-lg">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-foreground">{title}</h2>
            <Button variant="ghost" size="icon" onClick={onClose} className="text-foreground hover:bg-blue-700/20">
              <X className="h-5 w-5" />
              <span className="sr-only">Close</span>
            </Button>
          </div>

          {/* Decorative ECG line in header */}
          <div className="mt-2 h-6 w-full overflow-hidden">
            <svg viewBox="0 0 400 20" className="w-full">
              <path
                d="M0,10 L30,10 L45,0 L60,20 L75,0 L90,20 L105,10 L120,10 L140,10 L150,0 L160,20 L170,0 L180,10 L200,10 L220,10 L230,0 L240,20 L250,0 L260,10 L280,10 L300,10 L310,0 L320,20 L330,0 L340,10 L360,10 L400,10"
                fill="none"
                stroke="white"
                strokeWidth="1.5"
              />
            </svg>
          </div>
        </div>

        {/* Content area with medical-themed styling */}
        <div className="p-6 bg-white dark:bg-background">
          <div className="border-l-4 border-blue-500 pl-4 mb-6">{children}</div>

          {/* Footer with medical-themed button */}
          <div className="mt-6 flex justify-end">
            <Button onClick={onClose} className="bg-blue-600 hover:bg-blue-700 text-foreground">
              Confirm
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

