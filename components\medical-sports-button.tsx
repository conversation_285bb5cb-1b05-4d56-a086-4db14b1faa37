"use client"

import { type ButtonHTMLAttributes, forwardRef } from "react"
import { cn } from "@/lib/utils"
import { motion } from "framer-motion"

export interface MedicalSportsButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "patient" | "doctor" | "default"
  size?: "sm" | "md" | "lg"
  isLoading?: boolean
}

const MedicalSportsButton = forwardRef<HTMLButtonElement, MedicalSportsButtonProps>(
  ({ className, variant = "default", size = "md", isLoading = false, children, ...props }, ref) => {
    // Set gradient based on variant
    const gradientBg =
      variant === "patient"
        ? "bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600"
        : variant === "doctor"
          ? "bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600"
          : "bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600"

    // Set size classes
    const sizeClasses =
      size === "sm" ? "py-1 px-3 text-sm" : size === "lg" ? "py-3 px-6 text-lg" : "py-2 px-4 text-base"

    return (
      <motion.button
        className={cn(
          "relative rounded-md font-medium text-foreground shadow-md transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-70 disabled:cursor-not-allowed",
          gradientBg,
          sizeClasses,
          className,
        )}
        ref={ref}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        disabled={isLoading || props.disabled}
        {...props}
      >
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <svg
              className="animate-spin h-5 w-5 text-foreground"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </div>
        )}
        <span className={isLoading ? "opacity-0" : ""}>{children}</span>
      </motion.button>
    )
  },
)

MedicalSportsButton.displayName = "MedicalSportsButton"

export { MedicalSportsButton }

