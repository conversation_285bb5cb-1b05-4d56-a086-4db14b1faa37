"use client"

import type { ReactNode } from "react"
import { motion } from "framer-motion"
import { Activity, Stethoscope } from "lucide-react"
import { useEffect, useState } from "react"

interface MedicalSportsFrameProps {
  children: ReactNode
  variant?: "patient" | "doctor" | "default"
  className?: string
  hideDecorations?: boolean
}

export function MedicalSportsFrame({ children, variant = "default", className, hideDecorations = false }: MedicalSportsFrameProps) {
  const [isDark, setIsDark] = useState(false)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
    setIsDark(document.documentElement.classList.contains('dark'))
  }, [])

  // Set colors based on variant - FORCE BLUE FOR PATIENT IN DARK THEME
  const primaryColor = variant === "patient" ? "blue" : variant === "doctor" ? "green" : "blue"
  const primaryHex = variant === "patient" ? "#3b82f6" : variant === "doctor" ? "#10b981" : "#3b82f6"
  const secondaryHex = variant === "patient" ? "#1d4ed8" : variant === "doctor" ? "#059669" : "#1d4ed8"

  // Force blue colors for patient variant (dark theme override)
  const finalPrimaryHex = variant === "patient" ? "#3b82f6" : primaryHex
  const finalSecondaryHex = variant === "patient" ? "#1d4ed8" : secondaryHex
  const patientBlueBackground = "linear-gradient(145deg, hsl(220, 85%, 20%) 0%, hsl(220, 80%, 15%) 100%)"
  const patientBlueBorder = "hsl(220, 75%, 60%)"

  return (
    <div 
      className={`relative light ${className || ''}`}
      data-variant={variant}
      data-patient-form={variant === "patient" ? "true" : "false"}
      data-patient-modal={variant === "patient" ? "true" : "false"}
      style={{
        /* Patient (Blue) Theme */
        '--patient-bg-dark': 'linear-gradient(145deg, hsl(220, 85%, 20%) 0%, hsl(220, 80%, 15%) 100%)',
        '--patient-border-dark': 'hsl(220, 75%, 60%)',
        '--patient-shadow-dark': 'inset 0 0 30px hsl(220, 70%, 50%, 0.3)',
        '--patient-glow-dark': 'radial-gradient(circle at center, hsl(220, 80%, 50%, 0.2), transparent 70%)',
        '--patient-text-dark': '#ffffff',

        '--patient-bg-light': 'linear-gradient(145deg, hsl(210, 80%, 98%) 0%, hsl(210, 70%, 95%) 100%)',
        '--patient-border-light': 'hsl(210, 75%, 80%)',
        '--patient-shadow-light': 'inset 0 0 30px hsl(210, 70%, 50%, 0.1)',
        '--patient-glow-light': 'radial-gradient(circle at center, hsl(210, 80%, 80%, 0.2), transparent 70%)',
        '--patient-text-light': 'hsl(210, 50%, 20%)',
        
        /* Doctor (Green) Theme */
        '--doctor-bg-dark': 'linear-gradient(145deg, hsl(145, 85%, 20%) 0%, hsl(145, 80%, 15%) 100%)',
        '--doctor-border-dark': 'hsl(145, 75%, 60%)',
        '--doctor-shadow-dark': 'inset 0 0 30px hsl(145, 70%, 50%, 0.3)',
        '--doctor-glow-dark': 'radial-gradient(circle at center, hsl(145, 80%, 50%, 0.2), transparent 70%)',
        '--doctor-text-dark': '#ffffff',

        '--doctor-bg-light': 'linear-gradient(145deg, hsl(140, 60%, 97%) 0%, hsl(140, 50%, 94%) 100%)',
        '--doctor-border-light': 'hsl(140, 50%, 75%)',
        '--doctor-shadow-light': 'inset 0 0 30px hsl(140, 50%, 50%, 0.1)',
        '--doctor-glow-light': 'radial-gradient(circle at center, hsl(140, 60%, 80%, 0.2), transparent 70%)',
        '--doctor-text-light': 'hsl(140, 50%, 20%)',
      } as React.CSSProperties}
    >
      {/* Background glow effect */}
      <div
        className="absolute inset-0 blur-xl opacity-30 rounded-3xl"
        style={{
          background: `var(--${variant}-glow-${isMounted && isDark ? 'dark' : 'light'})`,
          transform: "translate(-5%, -5%) scale(1.1)",
        }}
      />

      {/* Animated corner decorations */}
      {!hideDecorations && (
        <>
          <div
            className="absolute -top-3 -left-3 w-16 h-16 border-t-4 border-l-4 rounded-tl-xl z-10"
            style={{ borderColor: `var(--${variant}-border-${isMounted && isDark ? 'dark' : 'light'})` }}
          ></div>
          <div
            className="absolute -bottom-3 -right-3 w-16 h-16 border-b-4 border-r-4 rounded-br-xl z-10"
            style={{ borderColor: `var(--${variant}-border-${isMounted && isDark ? 'dark' : 'light'})` }}
          ></div>
        </>
      )}

      {/* Medical symbols in corners */}
      <div className="absolute top-6 right-6 opacity-20 z-10">
        <Stethoscope 
          className="h-12 w-12" 
          style={{ color: `var(--${variant}-text-${isMounted && isDark ? 'dark' : 'light'})` }} 
        />
      </div>
      <div className="absolute bottom-6 left-6 opacity-20 z-10">
        <Activity 
          className="h-12 w-12" 
          style={{ color: `var(--${variant}-text-${isMounted && isDark ? 'dark' : 'light'})` }} 
        />
      </div>

      {/* Animated pulse rings */}
      <motion.div
        className="absolute inset-0 rounded-2xl opacity-20"
        style={{ border: `2px solid var(--${variant}-border-${isMounted && isDark ? 'dark' : 'light'})` }}
        animate={{ scale: [1, 1.02, 1], opacity: [0.2, 0.3, 0.2] }}
        transition={{ duration: 3, repeat: Number.POSITIVE_INFINITY, ease: "easeInOut" }}
      />

      {/* Main container */}
      <div 
        className="relative z-10 backdrop-blur-sm bg-background/80 p-1 rounded-2xl border border-border"
        style={{
          background: `var(--${variant}-bg-${isMounted && isDark ? 'dark' : 'light'})`,
          borderColor: `var(--${variant}-border-${isMounted && isDark ? 'dark' : 'light'})`
        }}
        data-variant={variant}
        data-patient-form={variant === "patient" ? "true" : "false"}
      >
        <div
          className="rounded-xl overflow-hidden"
          style={{
            background: `var(--${variant}-bg-${isMounted && isDark ? 'dark' : 'light'})`,
            boxShadow: `var(--${variant}-shadow-${isMounted && isDark ? 'dark' : 'light'})`,
            border: `2px solid var(--${variant}-border-${isMounted && isDark ? 'dark' : 'light'})`
          }}
          data-variant={variant}
          data-patient-form={variant === "patient" ? "true" : "false"}
        >
          {/* Decorative header line */}
          <div
            className="h-1 w-full"
            style={{ 
              background: `linear-gradient(to right, transparent, var(--${variant}-border-${isMounted && isDark ? 'dark' : 'light'}), transparent)` 
            }}
          ></div>

          {/* Content */}
          <div 
            className="p-6"
            data-variant={variant}
            data-patient-form={variant === "patient" ? "true" : "false"}
            style={{ color: `var(--${variant}-text-${isMounted && isDark ? 'dark' : 'light'})` }}
          >
            {children}
          </div>

          {/* Decorative footer line */}
          <div
            className="h-1 w-full"
            style={{ 
              background: `linear-gradient(to right, transparent, var(--${variant}-border-${isMounted && isDark ? 'dark' : 'light'}), transparent)` 
            }}
          ></div>
        </div>
      </div>
    </div>
  )
}

