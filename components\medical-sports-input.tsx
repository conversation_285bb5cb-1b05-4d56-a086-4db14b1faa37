"use client"

import { type InputHTMLAttributes, forwardRef } from "react"
import { cn } from "@/lib/utils"
import { HeartPulseIcon as Heartbeat } from "lucide-react"

export interface MedicalSportsInputProps extends InputHTMLAttributes<HTMLInputElement> {
  error?: string
  variant?: "patient" | "doctor" | "default"
}

const MedicalSportsInput = forwardRef<HTMLInputElement, MedicalSportsInputProps>(
  ({ className, error, variant = "default", ...props }, ref) => {
    // Set colors based on variant
    const borderColor =
      variant === "patient"
        ? "border-blue-500/50"
        : variant === "doctor"
          ? "border-green-500/50"
          : "border-purple-500/50"
    const focusBorderColor =
      variant === "patient"
        ? "focus:border-blue-500"
        : variant === "doctor"
          ? "focus:border-green-500"
          : "focus:border-purple-500"
    const focusRingColor =
      variant === "patient"
        ? "focus:ring-blue-500/20"
        : variant === "doctor"
          ? "focus:ring-green-500/20"
          : "focus:ring-purple-500/20"
    const errorBorderColor = "border-red-500"

    return (
      <div className="relative">
        <input
          className={cn(
            "flex h-10 w-full rounded-md border bg-background/50 px-3 py-2 text-sm text-foreground placeholder:text-muted-green focus:outline-none focus:ring-2 transition-colors",
            borderColor,
            focusBorderColor,
            focusRingColor,
            error ? errorBorderColor : "",
            className,
          )}
          ref={ref}
          {...props}
        />
        {error && (
          <div className="flex items-center mt-1 text-xs text-red-500">
            <Heartbeat className="h-3 w-3 mr-1" />
            <span>{error}</span>
          </div>
        )}
      </div>
    )
  },
)

MedicalSportsInput.displayName = "MedicalSportsInput"

export { MedicalSportsInput }

