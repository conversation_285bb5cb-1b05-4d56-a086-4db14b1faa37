"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { X, Award, Trophy, Users } from "lucide-react"
import { Button } from "@/components/ui/button"
import { motion, AnimatePresence } from "framer-motion"

interface MedicalSportsModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  icon?: "referee" | "match" | "trophy"
  children: React.ReactNode
}

export function MedicalSportsModal({ isOpen, onClose, title, icon = "referee", children }: MedicalSportsModalProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true)
    } else {
      const timer = setTimeout(() => {
        setIsVisible(false)
      }, 300)
      return () => clearTimeout(timer)
    }
  }, [isOpen])

  if (!isVisible) return null

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-background/50 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        >
          <motion.div
            className="relative bg-gradient-to-b from-background to-background rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-auto border border-primary/30 medical-sports-modal"
            initial={{ scale: 0.9, y: 20 }}
            animate={{ scale: 1, y: 0 }}
            exit={{ scale: 0.9, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            onClick={(e) => e.stopPropagation()}
          >
            <style jsx>{`
              /* Light theme styling for Medical Sports Modal (Registration Closed) */
              html:not(.dark) .medical-sports-modal {
                background: linear-gradient(to bottom, hsl(120, 40%, 95%), hsl(120, 30%, 90%)) !important;
                border-color: hsl(120, 30%, 75%) !important;
              }
              
              html:not(.dark) .medical-sports-modal .bg-gradient-to-r {
                background: linear-gradient(to right, hsl(120, 40%, 85%), hsl(120, 35%, 80%)) !important;
              }
              
              html:not(.dark) .medical-sports-modal .text-foreground {
                color: hsl(120, 60%, 20%) !important; /* Dark green text */
              }
              
              html:not(.dark) .medical-sports-modal h2 {
                color: hsl(120, 70%, 15%) !important; /* Darker green for headers */
              }
              
              html:not(.dark) .medical-sports-modal p {
                color: hsl(120, 50%, 25%) !important; /* Dark green for content text */
              }
              
              html:not(.dark) .medical-sports-modal .bg-primary {
                background-color: hsl(120, 60%, 40%) !important;
                color: white !important;
              }
              
              html:not(.dark) .medical-sports-modal .bg-primary:hover {
                background-color: hsl(120, 60%, 35%) !important;
              }
              
              html:not(.dark) .medical-sports-modal .bg-background\\/90\\/50 {
                background-color: hsl(120, 20%, 97%) !important;
                border-color: hsl(120, 25%, 85%) !important;
              }
            `}</style>
            {/* Decorative elements */}
            <div className="absolute top-0 left-0 w-full h-full overflow-hidden rounded-xl pointer-events-none">
              {/* ECG Line across the top */}
              <svg className="absolute top-0 left-0 w-full h-12 opacity-20" viewBox="0 0 400 50">
                <path
                  d="M0,25 L30,25 L45,10 L60,40 L75,10 L90,40 L105,25 L120,25 L140,25 L150,10 L160,40 L170,10 L180,25 L200,25 L220,25 L230,10 L240,40 L250,10 L260,25 L280,25 L300,25 L310,10 L320,40 L330,10 L340,25 L360,25 L400,25"
                  fill="none"
                  stroke="white"
                  strokeWidth="1.5"
                />
              </svg>

              {/* Circular pulse animation in the background */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <motion.div
                  className="w-64 h-64 rounded-full border border-primary/20"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.1, 0.2, 0.1],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Number.POSITIVE_INFINITY,
                    ease: "easeInOut",
                  }}
                />
                <motion.div
                  className="absolute top-0 left-0 w-64 h-64 rounded-full border border-primary/20"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.1, 0.15, 0.1],
                  }}
                  transition={{
                    duration: 4,
                    repeat: Number.POSITIVE_INFINITY,
                    ease: "easeInOut",
                    delay: 0.5,
                  }}
                />
              </div>

              {/* Medical cross in one corner */}
              <div className="absolute top-4 right-4 text-red-500/20 text-4xl font-bold">+</div>

              {/* Trophy silhouette in another corner */}
              <Trophy className="absolute bottom-4 left-4 h-8 w-8 text-yellow-500/20" />
            </div>

            {/* Header with gradient */}
            <div className="relative z-10 bg-gradient-to-r from-primary/80 to-primary/60 p-6 rounded-t-xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {icon === "referee" && (
                    <div className="bg-accent p-2 rounded-full">
                      <Users className="h-6 w-6 text-foreground" />
                    </div>
                  )}
                  {icon === "match" && (
                    <div className="bg-accent p-2 rounded-full">
                      <Trophy className="h-6 w-6 text-foreground" />
                    </div>
                  )}
                  {icon === "trophy" && (
                    <div className="bg-accent p-2 rounded-full">
                      <Award className="h-6 w-6 text-foreground" />
                    </div>
                  )}
                  <h2 className="text-xl font-bold text-foreground">{title}</h2>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                  className="text-foreground hover:bg-accent rounded-full"
                >
                  <X className="h-5 w-5" />
                  <span className="sr-only">Close</span>
                </Button>
              </div>

              {/* Animated heartbeat line */}
              <div className="mt-4 h-8 w-full overflow-hidden">
                <motion.div
                  initial={{ x: 0 }}
                  animate={{ x: -400 }}
                  transition={{
                    repeat: Number.POSITIVE_INFINITY,
                    duration: 8,
                    ease: "linear",
                  }}
                  className="w-[800px]"
                >
                  <svg viewBox="0 0 800 30" className="w-full">
                    <path
                      d="M0,15 L30,15 L45,5 L60,25 L75,5 L90,25 L105,15 L120,15 L140,15 L150,5 L160,25 L170,5 L180,15 L200,15 L220,15 L230,5 L240,25 L250,5 L260,15 L280,15 L300,15 L310,5 L320,25 L330,5 L340,15 L360,15 L400,15 L430,15 L445,5 L460,25 L475,5 L490,25 L505,15 L520,15 L540,15 L550,5 L560,25 L570,5 L580,15 L600,15 L620,15 L630,5 L640,25 L650,5 L660,15 L680,15 L700,15 L710,5 L720,25 L730,5 L740,15 L760,15 L800,15"
                      fill="none"
                      stroke="white"
                      strokeWidth="1.5"
                    />
                  </svg>
                </motion.div>
              </div>
            </div>

            {/* Content area with medical-sports themed styling */}
            <div className="relative z-10 p-6">
              <div className="bg-background/90/50 backdrop-blur-sm rounded-lg p-5 border border-primary/20 shadow-inner">
                {children}
              </div>

              {/* Footer with themed button */}
              <div className="mt-6 flex justify-end">
                <Button onClick={onClose} className="bg-primary hover:bg-primary/90 text-primary-foreground">
                  Confirm
                </Button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

