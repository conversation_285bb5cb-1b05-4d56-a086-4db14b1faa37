"use client"

import React, { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { AnimatePresence, motion } from 'framer-motion'
import { Home, Search, Users, Trophy, Menu, X, LogIn, LogOut, User } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Sheet,
  SheetContent,
  SheetTrigger,
  SheetClose
} from '@/components/ui/sheet'

// Define the interface for country objects
interface CountryOption {
  name: string
  code: string
}

// Define the props interface
interface MobileNavigationProps {
  countries?: CountryOption[]
  isLoggedIn?: boolean
  userName?: string
}

// Main component
export function MobileNavigation({
  countries,
  isLoggedIn,
  userName
}: MobileNavigationProps) {
  const [isMobile, setIsMobile] = useState(false);
  const [open, setOpen] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    // Only run on client
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Only render on mobile
  if (!isMobile) return null;

  return (
    <>
      {/* Add bottom padding to ensure content isn't hidden behind the navigation bar */}
      <div className="h-16"></div>

      <nav className="fixed bottom-0 left-0 z-40 w-full bg-white dark:bg-background border-t border-green-300 dark:border-border shadow-lg">
        <div className="flex items-center justify-between h-16 px-4">
          <NavItem href="/" icon={<Home className="h-6 w-6" />} active={pathname === '/'} label="Home" />
          <NavItem href="/doctors" icon={<Users className="h-6 w-6" />} active={pathname?.startsWith('/doctors') || false} label="Doctors" />
          <NavItem href="/leagues" icon={<Trophy className="h-6 w-6" />} active={pathname?.startsWith('/leagues') || false} label="Leagues" />
          <NavItem href="/search" icon={<Search className="h-6 w-6" />} active={pathname?.startsWith('/search') || false} label="Search" />
          
          <Sheet open={open} onOpenChange={setOpen}>
            <SheetTrigger asChild>
              <Button size="sm" variant="ghost" className="h-10 w-10 rounded-full p-0">
                <Menu className="h-6 w-6" />
                <span className="sr-only">Open menu</span>
              </Button>
            </SheetTrigger>
            <MobileMenu 
              countries={countries} 
              isLoggedIn={isLoggedIn || false}
              userName={userName || ''}
              onClose={() => setOpen(false)} 
            />
          </Sheet>
        </div>
      </nav>
    </>
  );
}

// Navigation item component
function NavItem({ 
  href, 
  icon, 
  active, 
  label 
}: { 
  href: string
  icon: React.ReactNode
  active: boolean
  label: string
}) {
  return (
    <Link href={href} className="flex flex-col items-center justify-center w-16 relative">
      <div className={cn(
        "flex flex-col items-center justify-center transition-colors",
        active ? "text-primary" : "text-muted-green hover:text-green-900 dark:text-muted-green dark:hover:text-muted-green"
      )}>
        {icon}
        <span className="text-xs mt-1">{label}</span>
      </div>
      
      {/* Active indicator - animated dot */}
      {active && (
        <motion.div 
          layoutId="activeNavIndicator"
          className="absolute -top-1 w-1.5 h-1.5 rounded-full bg-primary"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        />
      )}
    </Link>
  )
}

// Mobile Menu Component
function MobileMenu({ 
  onClose,
  countries = [],
  isLoggedIn = false,
  userName = ''
}: { 
  onClose: () => void
  countries?: CountryOption[]
  isLoggedIn?: boolean
  userName?: string
}) {
  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b flex items-center justify-between">
        <h2 className="text-lg font-semibold">Menu</h2>
        <SheetClose asChild>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-5 w-5" />
            <span className="sr-only">Close</span>
          </Button>
        </SheetClose>
      </div>
      
      {/* Content */}
      <div className="flex-1 overflow-auto py-4 px-6">
        {/* User section */}
        <div className="mb-6 pb-4 border-b">
          {isLoggedIn ? (
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src="/placeholder-avatar.png" alt={userName} />
                <AvatarFallback>{userName.charAt(0)}</AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">{userName}</p>
                <p className="text-sm text-muted-green dark:text-muted-green">Member</p>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <User className="h-5 w-5 text-muted-green" />
              <span className="text-muted-green">Not signed in</span>
            </div>
          )}
        </div>
        
        {/* Main navigation */}
        <div className="space-y-1 mb-6">
          <Link
            href="/"
            className="flex items-center gap-3 px-2 py-2 rounded-md hover:bg-green-100 dark:hover:bg-background/90"
            onClick={onClose}
          >
            <Home className="h-5 w-5" />
            <span>Home</span>
          </Link>
          
          <Link
            href="/doctors"
            className="flex items-center gap-3 px-2 py-2 rounded-md hover:bg-green-100 dark:hover:bg-background/90"
            onClick={onClose}
          >
            <Users className="h-5 w-5" />
            <span>Doctors</span>
          </Link>
          
          <Link
            href="/leagues"
            className="flex items-center gap-3 px-2 py-2 rounded-md hover:bg-green-100 dark:hover:bg-background/90"
            onClick={onClose}
          >
            <Trophy className="h-5 w-5" />
            <span>Leagues</span>
          </Link>
          
          <Link
            href="/search"
            className="flex items-center gap-3 px-2 py-2 rounded-md hover:bg-green-100 dark:hover:bg-background/90"
            onClick={onClose}
          >
            <Search className="h-5 w-5" />
            <span>Search</span>
          </Link>
        </div>
        
        {/* Countries section */}
        {countries.length > 0 && (
          <div className="mb-6">
            <h3 className="text-sm font-medium text-muted-green dark:text-muted-green mb-2">Countries</h3>
            <div className="space-y-1">
              {countries.slice(0, 5).map((country) => (
                <Link
                  key={country.code}
                  href={`/countries/${country.code}`}
                  className="flex items-center gap-2 px-2 py-2 rounded-md hover:bg-green-100 dark:hover:bg-background/90"
                  onClick={onClose}
                >
                  <span>{country.name}</span>
                </Link>
              ))}
              {countries.length > 5 && (
                <Link
                  href="/countries"
                  className="flex items-center gap-2 px-2 py-2 rounded-md hover:bg-green-100 dark:hover:bg-background/90 text-primary"
                  onClick={onClose}
                >
                  <span>View all countries</span>
                </Link>
              )}
            </div>
          </div>
        )}
      </div>
      
      {/* Footer */}
      <div className="p-4 border-t mobile-navigation-footer">
        <Button
          variant="outline"
          className="w-full justify-start gap-2"
          asChild
          style={{ fontSize: '1rem' }}
        >
          <Link href={isLoggedIn ? "/logout" : "/login"} onClick={onClose}>
            {isLoggedIn ? <LogOut className="h-4 w-4" /> : <LogIn className="h-4 w-4" />}
            <span className="nav-menu-item" style={{ fontSize: '1rem' }}>{isLoggedIn ? "Sign out" : "Sign in"}</span>
          </Link>
        </Button>
      </div>
    </div>
  )
} 