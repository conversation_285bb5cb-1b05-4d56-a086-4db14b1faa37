"use client"

import dynamic from 'next/dynamic'
import { ComponentProps } from 'react'

// Dynamically import Framer Motion components to reduce initial bundle size
export const MotionDiv = dynamic(
  () => import('framer-motion').then(mod => mod.motion.div),
  {
    ssr: false,
    loading: () => <div className="opacity-0" />
  }
)

export const MotionH1 = dynamic(
  () => import('framer-motion').then(mod => mod.motion.h1),
  {
    ssr: false,
    loading: () => <h1 className="opacity-0" />
  }
)

export const MotionP = dynamic(
  () => import('framer-motion').then(mod => mod.motion.p),
  {
    ssr: false,
    loading: () => <p className="opacity-0" />
  }
)

export const AnimatePresence = dynamic(
  () => import('framer-motion').then(mod => mod.AnimatePresence),
  {
    ssr: false,
    loading: () => <div />
  }
)

// Export types for better TypeScript support
export type MotionDivProps = ComponentProps<typeof MotionDiv>
export type MotionH1Props = ComponentProps<typeof MotionH1>
export type MotionPProps = ComponentProps<typeof MotionP>