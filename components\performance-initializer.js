"use client";

import { useEffect } from 'react';
import { initPerformanceOptimizations } from './performance-fallback';

/**
 * Component to initialize performance optimizations
 * This avoids the issues with dynamic imports in Script tags
 */
export function PerformanceInitializer() {
  useEffect(() => {
    // Initialize performance optimizations
    if (typeof window !== 'undefined') {
      console.log('[PerformanceInitializer] Starting performance optimizations');
      initPerformanceOptimizations();
    }
  }, []);

  // This component doesn't render anything
  return null;
} 