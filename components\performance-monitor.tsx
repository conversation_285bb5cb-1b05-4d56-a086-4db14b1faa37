"use client"

import { useEffect } from 'react'

interface PerformanceMonitorProps {
  pageName: string
  enableLogging?: boolean
}

export function PerformanceMonitor({
  pageName,
  enableLogging = process.env.NODE_ENV === 'development'
}: PerformanceMonitorProps) {
  useEffect(() => {
    if (!enableLogging) return

    // Monitor Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'navigation') {
          const navEntry = entry as PerformanceNavigationTiming
          console.log(`🚀 [${pageName}] Navigation Timing:`, {
            'DNS Lookup': `${navEntry.domainLookupEnd - navEntry.domainLookupStart}ms`,
            'TCP Connection': `${navEntry.connectEnd - navEntry.connectStart}ms`,
            'Request': `${navEntry.responseStart - navEntry.requestStart}ms`,
            'Response': `${navEntry.responseEnd - navEntry.responseStart}ms`,
            'DOM Processing': `${navEntry.domContentLoadedEventEnd - navEntry.responseEnd}ms`,
            'Total Load Time': `${navEntry.loadEventEnd - navEntry.navigationStart}ms`
          })
        }

        if (entry.entryType === 'paint') {
          console.log(`🎨 [${pageName}] Paint Timing:`, {
            name: entry.name,
            startTime: `${Math.round(entry.startTime)}ms`
          })
        }

        if (entry.entryType === 'largest-contentful-paint') {
          console.log(`📊 [${pageName}] LCP:`, `${Math.round(entry.startTime)}ms`)
        }

        if (entry.entryType === 'first-input') {
          const fidEntry = entry as PerformanceEventTiming
          console.log(`⚡ [${pageName}] FID:`, `${Math.round(fidEntry.processingStart - fidEntry.startTime)}ms`)
        }

        if (entry.entryType === 'layout-shift') {
          const clsEntry = entry as any
          if (!clsEntry.hadRecentInput) {
            console.log(`📐 [${pageName}] CLS:`, clsEntry.value)
          }
        }
      })
    })

    // Observe different performance metrics
    try {
      observer.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint', 'first-input', 'layout-shift'] })
    } catch (error) {
      console.warn('Performance Observer not fully supported:', error)
    }

    return () => {
      observer.disconnect()
    }
  }, [pageName, enableLogging])

  return null // This component doesn't render anything
}