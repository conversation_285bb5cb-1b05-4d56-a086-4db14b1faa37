import Head from 'next/head'

interface PreloadResourcesProps {
  images?: string[]
  fonts?: string[]
  scripts?: string[]
}

export function PreloadResources({
  images = [],
  fonts = [],
  scripts = []
}: PreloadResourcesProps) {
  return (
    <Head>
      {/* Preload critical images */}
      {images.map((src, index) => (
        <link
          key={`image-${index}`}
          rel="preload"
          as="image"
          href={src}
          type="image/webp"
        />
      ))}

      {/* Preload critical fonts */}
      {fonts.map((href, index) => (
        <link
          key={`font-${index}`}
          rel="preload"
          as="font"
          href={href}
          type="font/woff2"
          crossOrigin="anonymous"
        />
      ))}

      {/* Preload critical scripts */}
      {scripts.map((src, index) => (
        <link
          key={`script-${index}`}
          rel="preload"
          as="script"
          href={src}
        />
      ))}

      {/* DNS prefetch for external domains */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      <link rel="preconnect" href="https://uapbzzscckhtptliynyj.supabase.co" />
    </Head>
  )
}