"use client"

import { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { X, Trophy, Heart, Activity, Stethoscope, Microscope } from "lucide-react"
import { NewPatientRegistration } from "./registration/new-patient-registration"
import { NewDoctorRegistration } from "./registration/new-doctor-registration"

interface PremiumMedicalModalProps {
  isOpen: boolean
  onClose: () => void
  type: "patient" | "doctor"
}

export function PremiumMedicalModal({ isOpen, onClose, type }: PremiumMedicalModalProps) {
  const [showRegistration, setShowRegistration] = useState(false)
  const [particles, setParticles] = useState<
    Array<{ x: number; y: number; size: number; color: string; speed: number }>
  >([])
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()

  // Light theme styles for modals - only apply to light theme
  const lightThemeStyles = `
    /* Light Theme: Patient Modal ("Join as Referee") */
    html:not(.dark) [data-patient-modal="true"] {
      /* 3. Overall background: light blue */
      background: linear-gradient(135deg, hsl(210, 100%, 95%), hsl(210, 100%, 90%)) !important;
    }

    /* 1. Text color: dark blue */
    html:not(.dark) [data-patient-modal="true"] h1,
    html:not(.dark) [data-patient-modal="true"] h2,
    html:not(.dark) [data-patient-modal="true"] h3 {
        color: hsl(210, 80%, 25%) !important;
    }
    html:not(.dark) [data-patient-modal="true"] p {
        color: hsl(210, 70%, 40%) !important;
    }

    /* 2. Button: white text */
    html:not(.dark) [data-patient-modal="true"] .register-button {
        background-color: hsl(210, 80%, 50%) !important;
        color: white !important;
    }
     html:not(.dark) [data-patient-modal="true"] .register-button:hover {
        background-color: hsl(210, 80%, 45%) !important;
    }
    
    /* Keep doctor styles for doctor modal */
    html:not(.dark) [data-type="doctor"]:not([data-patient-modal="true"]) {
      background: linear-gradient(135deg, hsl(140, 60%, 95%) 0%, hsl(140, 50%, 90%) 100%) !important;
    }
    html:not(.dark) [data-type="doctor"]:not([data-patient-modal="true"]) * {
      color: hsl(140, 70%, 25%) !important;
    }
    html:not(.dark) [data-type="doctor"]:not([data-patient-modal="true"]) h1,
    html:not(.dark) [data-type="doctor"]:not([data-patient-modal="true"]) h2,
    html:not(.dark) [data-type="doctor"]:not([data-patient-modal="true"]) h3 {
      color: hsl(140, 80%, 20%) !important;
    }
  `

  // Generate particles
  useEffect(() => {
    if (isOpen) {
      const newParticles = []
      const colors = type === "patient" ? ["#60a5fa", "#3b82f6", "#2563eb"] : ["#4ade80", "#22c55e", "#16a34a"]

      for (let i = 0; i < 50; i++) {
        newParticles.push({
          x: Math.random() * window.innerWidth,
          y: Math.random() * window.innerHeight,
          size: Math.random() * 5 + 1,
          color: colors[Math.floor(Math.random() * colors.length)],
          speed: Math.random() * 1 + 0.5,
        })
      }

      setParticles(newParticles)
    }
  }, [isOpen, type])

  // Animate particles
  useEffect(() => {
    if (!isOpen || !canvasRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    canvas.width = window.innerWidth
    canvas.height = window.innerHeight

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      particles.forEach((particle) => {
        ctx.beginPath()
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
        ctx.fillStyle = particle.color
        ctx.fill()

        particle.y += particle.speed

        if (particle.y > canvas.height) {
          particle.y = 0
          particle.x = Math.random() * canvas.width
        }
      })

      animationRef.current = requestAnimationFrame(animate)
    }

    animate()

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [isOpen, particles])

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (canvasRef.current) {
        canvasRef.current.width = window.innerWidth
        canvasRef.current.height = window.innerHeight
      }
    }

    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  const handleRegistrationClick = () => {
    setShowRegistration(true)
  }

  const handleRegistrationClose = (open: boolean) => {
    if (!open) {
      setShowRegistration(false)
      onClose()
    }
  }

  const primaryColor = type === "patient" ? "blue" : "green"
  const secondaryColor = type === "patient" ? "indigo" : "emerald"
  const tertiaryColor = type === "patient" ? "sky" : "teal"
  const iconColor = type === "patient" ? "text-blue-500" : "text-primary"
  const bgGradient = type === "patient" ? "from-blue-900/40 to-blue-950/40" : "from-green-900/40 to-green-950/40"
  const borderColor = type === "patient" ? "border-blue-500/30" : "border-primary/30"
  const glowColor =
    type === "patient" ? "shadow-[0_0_30px_rgba(59,130,246,0.3)]" : "shadow-[0_0_30px_rgba(34,197,94,0.3)]"
  const buttonGradient =
    type === "patient" ? "from-blue-600 via-blue-500 to-blue-600" : "from-primary via-primary/90 to-primary"
  const hoverColor = type === "patient" ? "hover:bg-blue-600/80" : "hover:bg-primary/80"
  const title = type === "patient" ? "Join as Referee" : "Join as Competitor"
  const description =
    type === "patient"
      ? "Create your account to follow your favorite doctors and track their performance in the medical leagues."
      : "Create your account to compete with other medical professionals and showcase your expertise."
  const buttonText = type === "patient" ? "Register as Referee" : "Register as Competitor"
  const cornerBorderColor = type === "patient" ? "border-blue-400" : "border-green-400"

  if (showRegistration) {
    return type === "patient" ? (
      <NewPatientRegistration open={showRegistration} onOpenChange={handleRegistrationClose} />
    ) : (
      <NewDoctorRegistration open={showRegistration} onOpenChange={handleRegistrationClose} />
    )
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <style dangerouslySetInnerHTML={{ __html: lightThemeStyles }} />
          <motion.div
            className={`
              fixed inset-0 z-[9999] flex min-h-screen items-center justify-center p-4
              bg-gradient-to-br ${bgGradient} bg-background/95 backdrop-blur-lg
            `}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            data-type={type}
            data-patient-modal={type === "patient" ? "true" : "false"}
          >
            <canvas ref={canvasRef} className="absolute inset-0 pointer-events-none" />

            <motion.div
              className={`
                relative z-10 w-full max-w-3xl rounded-2xl border ${borderColor} 
                bg-background/80 backdrop-blur-md shadow-2xl ${glowColor}
              `}
              initial={{ scale: 0.95, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.95, y: 20 }}
              transition={{ type: "spring", damping: 20, stiffness: 200 }}
              data-patient={type === "patient" ? "true" : "false"}
              data-doctor={type === "doctor" ? "true" : "false"}
            >
              <div className="flex justify-between items-center p-6 border-b border-white/10">
                <h1 className={`text-2xl font-bold text-foreground modal-title`}>{title}</h1>
                <motion.button
                  whileHover={{ scale: 1.1, rotate: 90 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={onClose}
                  className={`p-2 rounded-full ${hoverColor} transition-colors`}
                >
                  <X className="h-6 w-6 text-foreground" />
                </motion.button>
              </div>

              <div className="p-8">
                <p className={`text-lg text-foreground/60 mb-8 description-text`}>{description}</p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  {/* Leaderboards */}
                  <motion.div
                    whileHover={{ y: -5 }}
                    className="flex flex-col items-center text-center p-6 rounded-lg bg-background/50"
                  >
                    <motion.div
                      whileHover={{ scale: 1.1, rotate: -5 }}
                      className={`p-4 rounded-full bg-gradient-to-br ${buttonGradient} mb-4`}
                    >
                      <Trophy className={`h-8 w-8 text-white`} />
                    </motion.div>
                    <h3 className={`text-xl font-semibold text-foreground mb-2 feature-title`}>Leaderboards</h3>
                    <p className={`text-sm text-foreground/60 feature-description`}>Track top performers in each specialty</p>
                  </motion.div>

                  {/* Live Matches */}
                  <motion.div
                    whileHover={{ y: -5 }}
                    className="flex flex-col items-center text-center p-6 rounded-lg bg-background/50"
                  >
                    <motion.div
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      className={`p-4 rounded-full bg-gradient-to-br ${buttonGradient} mb-4`}
                    >
                      <Activity className={`h-8 w-8 text-white`} />
                    </motion.div>
                    <h3 className={`text-xl font-semibold text-foreground mb-2 feature-title`}>Live Matches</h3>
                    <p className={`text-sm text-foreground/60 feature-description`}>Follow ongoing medical competitions</p>
                  </motion.div>

                  {/* Support Your Favorites */}
                  <motion.div
                    whileHover={{ y: -5 }}
                    className="flex flex-col items-center text-center p-6 rounded-lg bg-background/50"
                  >
                    <motion.div
                      whileHover={{ scale: 1.1, rotate: -5 }}
                      className={`p-4 rounded-full bg-gradient-to-br ${buttonGradient} mb-4`}
                    >
                      <Heart className={`h-8 w-8 text-white`} />
                    </motion.div>
                    <h3 className={`text-xl font-semibold text-foreground mb-2 feature-title`}>Support Your Favorites</h3>
                    <p className={`text-sm text-foreground/60 feature-description`}>Rate and review doctors to help them climb the rankings</p>
                  </motion.div>
                </div>

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleRegistrationClick}
                  className={`w-full py-4 text-lg font-bold rounded-lg bg-gradient-to-r ${buttonGradient} text-foreground shadow-lg transition-all duration-300 register-button`}
                >
                  {buttonText}
                </motion.button>
              </div>

              {/* Corner decorative borders */}
              <div
                className={`absolute top-2 left-2 w-16 h-16 border-t-2 border-l-2 ${cornerBorderColor} rounded-tl-2xl opacity-50`}
              ></div>
              <div
                className={`absolute bottom-2 right-2 w-16 h-16 border-b-2 border-r-2 ${cornerBorderColor} rounded-br-2xl opacity-50`}
              ></div>
            </motion.div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}