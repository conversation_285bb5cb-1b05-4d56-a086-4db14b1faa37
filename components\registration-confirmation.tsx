"use client"

import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CheckCircle, Mail } from "lucide-react"
import { motion } from "framer-motion"
import { MedicalFrame } from "../medical-frame"

interface RegistrationConfirmationProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  email: string
  userType: "patient" | "doctor"
}

export function RegistrationConfirmation({ open, onOpenChange, email, userType }: RegistrationConfirmationProps) {
  const iconColor = userType === "patient" ? "text-blue-500" : "text-primary"
  const buttonGradient =
    userType === "patient"
      ? "from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
      : "from-primary to-primary/80 hover:from-primary/90 hover:to-primary"

  // Light theme styles for registration confirmation dialog
  const lightThemeStyles = `
    /* Light Theme: Registration Confirmation Dialog */
    html:not(.dark) [data-registration-confirmation="true"] {
      background: linear-gradient(135deg, hsl(140, 60%, 95%) 0%, hsl(140, 50%, 90%) 100%) !important;
    }
    
    html:not(.dark) [data-registration-confirmation="true"] * {
      color: hsl(140, 70%, 25%) !important;
    }
    
    html:not(.dark) [data-registration-confirmation="true"] h1,
    html:not(.dark) [data-registration-confirmation="true"] h2,
    html:not(.dark) [data-registration-confirmation="true"] h3 {
      color: hsl(140, 80%, 20%) !important;
    }
  `

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: lightThemeStyles }} />
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md p-0 border-0 bg-transparent" aria-describedby="registration-confirmation-description">
          <DialogTitle className="sr-only">Registration Successful</DialogTitle>
          <DialogDescription id="registration-confirmation-description" className="sr-only">
            Registration confirmation and email verification instructions
          </DialogDescription>
          <MedicalFrame variant={userType} data-registration-confirmation="true">
          <div className="p-6 space-y-6 text-center">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <CheckCircle className={`w-16 h-16 ${iconColor} mx-auto`} />
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <h2 className="text-2xl font-bold text-foreground">Registration Successful!</h2>
              <p className="text-foreground/80 mt-2">
                Thank you for joining Doctor's Leagues! We're excited to have you on board.
              </p>
            </motion.div>

            <motion.div
              className="bg-background/30 p-4 rounded-lg border border-border/50"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <div className="flex items-center justify-center gap-3 mb-2">
                <Mail className={iconColor} />
                <h3 className="text-lg font-semibold text-foreground">Verify Your Email</h3>
              </div>
              <p className="text-foreground/70 text-sm">
                We've sent a confirmation email to:
                <span className="block font-medium text-foreground mt-1">{email}</span>
              </p>
              <p className="text-foreground/70 text-sm mt-3">
                Please check your inbox and click the verification link to activate your account.
              </p>
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <Button
                onClick={() => {
                  onOpenChange(false);
                  // Redirect to home page after closing confirmation
                  if (typeof window !== 'undefined') {
                    window.location.href = '/';
                  }
                }}
                className={`w-full bg-gradient-to-r ${buttonGradient} text-foreground py-2 rounded-lg font-semibold`}
              >
                Got it
              </Button>
              
              <p className="text-foreground/60 text-xs mt-4">
                Note: You must verify your email before you can log in.
              </p>
            </motion.div>
          </div>
        </MedicalFrame>
      </DialogContent>
    </Dialog>
    </>
  )
}

