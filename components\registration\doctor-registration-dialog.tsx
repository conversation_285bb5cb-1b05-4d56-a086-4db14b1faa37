"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { <PERSON>, CheckCircle, Stethoscope, Upload } from "lucide-react"
import { useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { MedicalSportsButton } from "../medical-sports-button"
import { MedicalSportsInput } from "../medical-sports-input"
import { MedicalSportsFrame } from "../medical-sports-frame"
import { useToast } from "@/components/ui/use-toast"
import { createClient } from "@supabase/supabase-js"
import { insertDoctorRegistration, uploadDoctorProfileImage } from "@/actions/doctor-registration-actions"

// Types for form data
interface DoctorFormData {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  age: string;
  gender: string;
  phoneN<PERSON>ber: string;
  specialty: string;
  specialtyId: string; // Add specialty ID for database relation
  licenseNumber: string;
  hospital: string;
  hospitalId: string; // Add hospital ID for database relation
  city: string;
  state: string;
  country: string;
  countryId: string; // Add country ID for database relation
  yearsOfExperience: string;
  biography: string;
  // New fields for Task 1
  personalBiography: string;
  workHistory: string;
  timings: string;
  // Profile image
  profileImage: File | null;
}

// Add interfaces for dropdown data
interface Specialty {
  specialty_id: string;
  specialty_name: string;
}

interface Country {
  country_id: string;
  country_name: string;
}

interface Hospital {
  hospital_id: string;
  hospital_name: string;
  country_id: string;
}

export function DoctorRegistrationDialog({
  open,
  onOpenChange,
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
}) {
  console.log("=== DOCTOR REGISTRATION DIALOG INITIALIZED ===", { open });

  // Form state
  const [phase, setPhase] = useState(1)
  const [formData, setFormData] = useState<DoctorFormData>({
    email: "",
    password: "",
    confirmPassword: "",
    firstName: "",
    lastName: "",
    age: "",
    gender: "",
    phoneNumber: "",
    specialty: "",
    specialtyId: "",
    licenseNumber: "",
    hospital: "",
    hospitalId: "",
    city: "",
    state: "",
    country: "",
    countryId: "",
    yearsOfExperience: "",
    biography: "",
    // New fields for Task 1
    personalBiography: "",
    workHistory: "",
    timings: "",
    // Profile image
    profileImage: null
  })
  
  // UI state
  const [passwordStrength, setPasswordStrength] = useState(0)
  const [passwordMessage, setPasswordMessage] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [registrationComplete, setRegistrationComplete] = useState(false)
  
  // Add email validation state
  const [emailValidationStatus, setEmailValidationStatus] = useState<'idle' | 'checking' | 'available' | 'taken'>('idle');
  const emailCheckTimer = useRef<NodeJS.Timeout | null>(null);
  
  // Data for dropdowns
  const [specialties, setSpecialties] = useState<Specialty[]>([])
  const [countries, setCountries] = useState<Country[]>([])
  const [hospitals, setHospitals] = useState<Hospital[]>([])
  const [filteredHospitals, setFilteredHospitals] = useState<Hospital[]>([])
  const [isLoadingSpecialties, setIsLoadingSpecialties] = useState(false)
  const [isLoadingCountries, setIsLoadingCountries] = useState(false)
  const [isLoadingHospitals, setIsLoadingHospitals] = useState(false)
  
  const router = useRouter()
  const { toast } = useToast()

  // Initialize Supabase with service role key for admin operations
  const supabaseAdmin = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL || "",
    process.env.SUPABASE_SERVICE_ROLE_KEY || "",
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
  
  // Validate service role key
  useEffect(() => {
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (!serviceRoleKey || serviceRoleKey.length < 20) {
      console.error("SERVICE ROLE KEY ERROR:", 
        !serviceRoleKey ? "Key is undefined or empty" : 
        `Key is too short (${serviceRoleKey.length} chars)`);
    }
  }, []);

  // Fetch specialties, countries and hospitals on component mount
  useEffect(() => {
    fetchSpecialties()
    fetchCountries()
  }, [])

  // When country changes, fetch hospitals for that country
  useEffect(() => {
    if (formData.countryId) {
      fetchHospitalsByCountry(formData.countryId)
    }
  }, [formData.countryId])

  // Fetch specialties from database
  const fetchSpecialties = async () => {
    setIsLoadingSpecialties(true)
    try {
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL || "",
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ""
      )
      
      const { data, error } = await supabase
        .from('specialties')
        .select('specialty_id, specialty_name')
        .order('specialty_name')
      
      if (error) throw error
      setSpecialties(data || [])
    } catch (error) {
      console.error("Error fetching specialties:", error)
      toast({
        title: "Error",
        description: "Failed to load specialties. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoadingSpecialties(false)
    }
  }

  // Fetch countries from database
  const fetchCountries = async () => {
    setIsLoadingCountries(true)
    try {
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL || "",
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ""
      )
      
      console.log("Fetching countries from database...");
      
      const { data, error } = await supabase
        .from('countries')
        .select('country_id, country_name')
        .order('country_name')
      
      if (error) {
        console.error("Error fetching countries:", error);
        throw error;
      }
      
      if (!data || data.length === 0) {
        console.warn("No countries found in database, using fallback data");
        // Provide fallback country data if none found in database
        setCountries([
          { country_id: "1", country_name: "Bahrain" },
          { country_id: "2", country_name: "Kuwait" },
          { country_id: "3", country_name: "Oman" },
          { country_id: "4", country_name: "Qatar" },
          { country_id: "5", country_name: "Saudi Arabia" },
          { country_id: "6", country_name: "UAE" }
        ]);
      } else {
        console.log(`Successfully fetched ${data.length} countries`);
        setCountries(data);
      }
    } catch (error) {
      console.error("Exception fetching countries:", error);
      toast({
        title: "Error",
        description: "Failed to load countries. Using default list.",
        variant: "destructive",
      });
      
      // Fallback to hardcoded countries if there's an error
      setCountries([
        { country_id: "1", country_name: "Bahrain" },
        { country_id: "2", country_name: "Kuwait" },
        { country_id: "3", country_name: "Oman" },
        { country_id: "4", country_name: "Qatar" },
        { country_id: "5", country_name: "Saudi Arabia" },
        { country_id: "6", country_name: "UAE" }
      ]);
    } finally {
      setIsLoadingCountries(false)
    }
  }

  // Fetch hospitals by country
  const fetchHospitalsByCountry = async (countryId: string) => {
    if (!countryId) return
    
    setIsLoadingHospitals(true)
    try {
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL || "",
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ""
      )
      
      console.log(`Fetching hospitals for country ID: ${countryId}`);
      
      const { data, error } = await supabase
        .from('hospitals')
        .select('hospital_id, hospital_name, country_id')
        .eq('country_id', countryId)
        .order('hospital_name')
      
      if (error) {
        console.error("Error fetching hospitals:", error);
        throw error;
      }
      
      if (!data || data.length === 0) {
        console.warn(`No hospitals found for country ID: ${countryId}`);
        
        // Check if country exists to provide better diagnostics
        const { data: countryData, error: countryError } = await supabase
          .from('countries')
          .select('country_name')
          .eq('country_id', countryId)
          .single();
          
        if (countryError) {
          console.error("Error validating country:", countryError);
        } else {
          console.log(`Country name for ID ${countryId}: ${countryData?.country_name}`);
        }
        
        // Provide some sample hospitals for development/testing if needed
        // Uncomment this to add sample hospitals when none exist
        /*
        setHospitals([
          { hospital_id: `${countryId}_1`, hospital_name: `Central Hospital (${countryId})`, country_id: countryId },
          { hospital_id: `${countryId}_2`, hospital_name: `Medical Center (${countryId})`, country_id: countryId },
          { hospital_id: `${countryId}_3`, hospital_name: `General Hospital (${countryId})`, country_id: countryId }
        ]);
        setFilteredHospitals([
          { hospital_id: `${countryId}_1`, hospital_name: `Central Hospital (${countryId})`, country_id: countryId },
          { hospital_id: `${countryId}_2`, hospital_name: `Medical Center (${countryId})`, country_id: countryId },
          { hospital_id: `${countryId}_3`, hospital_name: `General Hospital (${countryId})`, country_id: countryId }
        ]);
        */
        
        // Set empty arrays when no hospitals exist
        setHospitals([]);
        setFilteredHospitals([]);
      } else {
        console.log(`Successfully fetched ${data.length} hospitals for country ID: ${countryId}`);
        setHospitals(data);
        setFilteredHospitals(data);
      }
    } catch (error) {
      console.error(`Exception fetching hospitals for country ID: ${countryId}`, error);
      toast({
        title: "Error",
        description: "Failed to load hospitals for the selected country.",
        variant: "destructive",
      });
      
      setHospitals([]);
      setFilteredHospitals([]);
    } finally {
      setIsLoadingHospitals(false);
    }
  }

  // Filter hospitals by search term
  const filterHospitals = (searchTerm: string) => {
    if (!searchTerm.trim()) {
      setFilteredHospitals(hospitals)
      return
    }
    
    const filtered = hospitals.filter(hospital => 
      hospital.hospital_name.toLowerCase().includes(searchTerm.toLowerCase())
    )
    setFilteredHospitals(filtered)
  }

  // Update form data handler
  const updateFormData = (field: keyof DoctorFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Trigger email check if the field is email
    if (field === 'email') {
      debouncedCheckEmail(value);
    }
  };

  // Password strength checker
  const checkPasswordStrength = (password: string): number => {
    let strength = 0;
    
    if (password.length >= 8) strength += 1;
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[a-z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;
    
    return strength;
  };

  const getPasswordStrengthColor = (strength: number): string => {
    if (strength <= 1) return "bg-red-500";
    if (strength <= 2) return "bg-orange-500";
    if (strength <= 3) return "bg-yellow-500";
    if (strength <= 4) return "bg-blue-500";
    return "bg-green-500";
  };

  const getPasswordStrengthText = (strength: number): string => {
    if (strength <= 1) return "Very Weak";
    if (strength <= 2) return "Weak";
    if (strength <= 3) return "Medium";
    if (strength <= 4) return "Strong";
    return "Very Strong";
  };

  // Update password strength when password changes
  useEffect(() => {
    if (formData.password) {
      setPasswordStrength(checkPasswordStrength(formData.password));
    } else {
      setPasswordStrength(0);
      setPasswordMessage("");
    }
  }, [formData.password]);

  // Modify validatePhase1 to check email
  const validatePhase1 = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.email) newErrors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = "Email is invalid";
    else if (emailValidationStatus === 'taken') {
      newErrors.email = "This email is already registered. Please use a different email address.";
    }

    if (!formData.password) newErrors.password = "Password is required";
    else if (formData.password.length < 8) newErrors.password = "Password must be at least 8 characters";
    else if (checkPasswordStrength(formData.password) < 3) newErrors.password = "Password is too weak";

    if (!formData.confirmPassword) newErrors.confirmPassword = "Please confirm your password";
    else if (formData.password !== formData.confirmPassword) newErrors.confirmPassword = "Passwords do not match";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validatePhase2 = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName) newErrors.firstName = "First name is required";
    if (!formData.lastName) newErrors.lastName = "Last name is required";
    if (!formData.age) newErrors.age = "Age is required";
    if (!formData.gender) newErrors.gender = "Gender is required";
    if (!formData.phoneNumber) newErrors.phoneNumber = "Phone number is required";
    if (!formData.specialty) newErrors.specialty = "Medical specialty is required";
    if (!formData.country) newErrors.country = "Country is required";
    if (!formData.licenseNumber) newErrors.licenseNumber = "License number is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validatePhase3 = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.hospital) newErrors.hospital = "Hospital/Clinic is required";
    if (!formData.city) newErrors.city = "City is required";
    if (!formData.yearsOfExperience) newErrors.yearsOfExperience = "Years of experience is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Modify handleNext to handle async validation
  const handleNext = async () => {
    if (phase === 1) {
      const isValid = await validatePhase1();
      if (isValid) setPhase(2);
    } else if (phase === 2 && validatePhase2()) {
      setPhase(3);
    }
  };

  const handleBack = () => {
    if (phase > 1) {
      setPhase(phase - 1);
    }
  };

  // Handle profile image upload
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Invalid file type",
          description: "Please select an image file.",
          variant: "destructive",
        });
        return;
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        toast({
          title: "File too large",
          description: "Please select an image smaller than 10MB.",
          variant: "destructive",
        });
        return;
      }

      setFormData(prev => ({ ...prev, profileImage: file }));
      toast({
        title: "Image selected",
        description: `Selected: ${file.name}`,
        variant: "default",
      });
    }
  };

  // Helper function to log errors
  const logError = (prefix: string, error: any) => {
    console.error(`${prefix}:`, {
      message: error?.message || "No message",
      code: error?.code || "No code",
      details: error?.details || error?.hint || "No details",
      status: error?.status || "No status"
    });
    
    if (error instanceof Error) {
      console.log("Stack trace:", error.stack);
    }
  };

  // Registration handler
  const handleSubmit = async () => {
    console.log("=== HANDLE SUBMIT CALLED ===");
    console.log("Current form data:", formData);
    console.log("Profile image:", formData.profileImage?.name, formData.profileImage?.size);

    if (!validatePhase3()) {
      console.log("=== VALIDATION FAILED ===");
      return;
    }

    console.log("=== VALIDATION PASSED ===");
    
    setIsLoading(true);
    setErrors({});
    
    try {
      console.log("Creating auth user with admin API...");
      
      // Use the admin client to avoid email rate limits
      const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
        email: formData.email,
        password: formData.password,
        email_confirm: false, // Require email verification
        user_metadata: {
          full_name: `${formData.firstName} ${formData.lastName}`,
          role: "doctor"
        }
      });
      
      if (authError) {
        console.error("Auth error:", authError);
        setErrors({ submit: authError.message });
        setIsLoading(false);
        return;
      }
      
      const userId = authUser.user.id;
      console.log("handleSubmit: Auth user created successfully. User ID:", userId); // Enhanced Log

      // Send verification email
      try {
        console.log("Attempting to send verification email");
        
        // The way that works with Supabase
        const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
        const redirectUrl = `${baseUrl}/auth/verification-success?type=doctor&email=${encodeURIComponent(formData.email)}`;
        
        // Send the actual verification email
        const client = createClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL || "",
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ""
        );
        
        const { error: verificationError } = await client.auth.resend({
          type: 'signup',
          email: formData.email,
          options: {
            emailRedirectTo: redirectUrl
          }
        });
        
        if (verificationError) {
          console.error("Error sending verification email:", verificationError);
        } else {
          console.log("Verification email sent successfully");
        }
      } catch (emailError) {
        console.error("Exception sending verification email:", emailError);
      }
      
      // Insert into doctors_registration table with delay
      console.log("Inserting into doctors_registration table...");
      
      const doctorData = {
        auth_id: userId,
        email: formData.email,
        first_name: formData.firstName,
        last_name: formData.lastName,
        gender: formData.gender,
        age: parseInt(formData.age) || 0,
        specialty: formData.specialty || "",
        specialty_id: formData.specialtyId || "",
        license_number: formData.licenseNumber || "",
        hospital: formData.hospital || "",
        hospital_id: formData.hospitalId || "",
        years_of_experience: parseInt(formData.yearsOfExperience) || 0,
        biography: formData.biography || "",
        city: formData.city,
        state: formData.state,
        country: formData.country,
        country_id: formData.countryId,
        phone_number: formData.phoneNumber || "",
        registration_date: new Date().toISOString(),
        approval_status: "pending",
        // New fields for Task 1
        personal_biography: formData.personalBiography || "",
        work_history: formData.workHistory || "",
        timings: formData.timings || ""
      };
      
      console.log("handleSubmit: Preparing doctorData for insertion:", JSON.stringify(doctorData, null, 2)); // Enhanced Log
      
      // Add delay to ensure connection is established - NOTE: This setTimeout is suspicious and might be unreliable.
      console.log("handleSubmit: Waiting 1 second before attempting database insert..."); // Enhanced Log
      setTimeout(async () => {
        console.log("handleSubmit: Attempting insert into 'doctors_registration'..."); // Enhanced Log
        try {
          const { data: insertData, error: insertError } = await supabaseAdmin
            .from('doctors_registration')
            .insert(doctorData)
            .select();
          
          if (insertError) {
            // Detailed logging for the primary insert error
            console.error("handleSubmit: ERROR inserting into 'doctors_registration'. Details:", {
              message: insertError.message,
              code: insertError.code,
              details: insertError.details,
              hint: insertError.hint,
              fullError: insertError 
            }); // Enhanced Log
            
            console.log("handleSubmit: Attempting fallback insert into 'doctors_copy'..."); // Enhanced Log
            // Try doctors_copy as fallback
            const { error: copyError } = await supabaseAdmin
              .from('doctors_copy') // Assuming 'doctors_copy' exists for fallback
              .insert({ // Inserting minimal data for fallback test
                auth_id: userId,
                email: formData.email,
                fullname: `${formData.firstName} ${formData.lastName}`
              }); // End of fallback insert
            
            if (copyError) {
              // Detailed logging for the fallback insert error
              console.error("handleSubmit: ERROR during fallback insert into 'doctors_copy'. Details:", {
                message: copyError.message,
                code: copyError.code,
                details: copyError.details,
                hint: copyError.hint,
                fullError: copyError
              }); // Enhanced Log
            } else {
              console.log("handleSubmit: Fallback insert into 'doctors_copy' succeeded."); // Enhanced Log
            }
          } else {
            console.log("handleSubmit: Successfully inserted into 'doctors_registration'. Result:", insertData); // Enhanced Log

            // Upload profile image if provided
            if (formData.profileImage && userId) {
              try {
                console.log("=== STARTING IMAGE UPLOAD PROCESS ===");
                console.log("User ID for image upload:", userId);
                console.log("Image file:", formData.profileImage.name, formData.profileImage.size);

                const uploadResult = await uploadDoctorProfileImage(userId, formData.profileImage);
                if (uploadResult.error) {
                  console.error("=== IMAGE UPLOAD FAILED ===");
                  console.error("Upload error details:", uploadResult.error);
                  // Non-critical error, continue with registration
                } else {
                  console.log("=== IMAGE UPLOAD SUCCESSFUL ===");
                  console.log("Profile image uploaded successfully. URL:", uploadResult.profileImageUrl);

                  // Verify the database was updated
                  console.log("Verifying database update...");
                  const { data: verifyData, error: verifyError } = await supabaseAdmin
                    .from('doctors_registration')
                    .select('profile_image')
                    .eq('auth_id', userId)
                    .single();

                  if (verifyError) {
                    console.error("Error verifying database update:", verifyError);
                  } else {
                    console.log("Database verification result:", verifyData);
                    if (verifyData.profile_image) {
                      console.log("✅ SUCCESS: Image path saved to database:", verifyData.profile_image);
                    } else {
                      console.error("❌ CRITICAL: Image path NOT saved to database despite successful upload!");
                    }
                  }
                }
              } catch (uploadError) {
                console.error("=== IMAGE UPLOAD EXCEPTION ===");
                console.error("Upload exception details:", uploadError);
                // Non-critical error, continue with registration
              }
            } else {
              console.log("No image to upload or missing userId");
            }
          }

          // Show success message regardless of DB insert outcome (as per original logic)
          console.log("handleSubmit: Setting registrationComplete = true"); // Enhanced Log
          setRegistrationComplete(true);
          setIsLoading(false);
        } catch (dbError: any) {
          // Catch block for errors within the setTimeout async function
          console.error("handleSubmit: CRITICAL error during database operation (within setTimeout). Details:", {
             message: dbError?.message,
             stack: dbError?.stack,
             fullError: dbError
          }); // Enhanced Log
          setRegistrationComplete(true);  // Still show success since auth user was created (as per original logic)
          setIsLoading(false);
        }
      }, 1000); // End of setTimeout
      
    } catch (error: any) {
      // Catch block for errors in the main handleSubmit function (e.g., auth user creation)
      console.error("handleSubmit: UNEXPECTED error during registration process. Details:", {
        message: error?.message,
        stack: error?.stack,
        fullError: error
      }); // Enhanced Log
      setErrors({ submit: "An unexpected error occurred" });
      setIsLoading(false);
    }
  };

  // Debounced email check function
  const debouncedCheckEmail = (email: string) => {
    // Clear any existing timer
    if (emailCheckTimer.current) {
      clearTimeout(emailCheckTimer.current);
    }
    
    // Don't check empty or invalid emails
    if (!email || !email.includes('@')) {
      setEmailValidationStatus('idle');
      return;
    }
    
    // Set status to checking to show loading indicator
    setEmailValidationStatus('checking');
    
    // Set a new timer
    emailCheckTimer.current = setTimeout(async () => {
      try {
        const isAvailable = await checkEmailAvailability(email);
        setEmailValidationStatus(isAvailable ? 'available' : 'taken');
      } catch (error) {
        console.error("Error checking email:", error);
        setEmailValidationStatus('idle');
      }
    }, 500); // 500ms debounce time
  };

  // Check if email is already registered
  const checkEmailAvailability = async (email: string) => {
    try {
      console.log(`Checking email availability for: ${email}`);
      
      // Check users table for this email
      const { data: existingUsers, error: userError } = await supabaseAdmin
        .from('users')
        .select('email')
        .eq('email', email)
        .limit(1);
      
      if (userError) {
        console.error("Error checking users table:", userError);
      } else if (existingUsers?.length > 0) {
        console.log(`Email ${email} found in users table`);
        return false; // Email is taken
      }
      
      // Check doctors table
      const { data: existingDoctors, error: doctorError } = await supabaseAdmin
        .from('doctors')
        .select('email')
        .eq('email', email)
        .limit(1);
      
      if (doctorError) {
        console.error("Error checking doctors table:", doctorError);
      } else if (existingDoctors?.length > 0) {
        console.log(`Email ${email} found in doctors table`);
        return false; // Email is taken
      }
      
      // If all checks passed, email is available
      console.log(`Email ${email} is available`);
      return true;
    } catch (error) {
      console.error("Exception checking email availability:", error);
      return true; // Allow submission if we can't check (fail open)
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-green-800 text-foreground border-none">
        <DialogHeader>
          <div className="flex justify-between items-center w-full">
            <DialogTitle className="text-2xl font-bold text-foreground">
              MEDICAL COMPETITOR
            </DialogTitle>
          </div>
        </DialogHeader>
        
        {!registrationComplete ? (
          <>
            {/* Scrollable Content */}
            <div className="overflow-y-auto flex-grow mb-4 max-h-[400px] min-h-[400px] pr-2">
              {phase === 1 && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-foreground">Email</Label>
                    <div className="relative">
                      <MedicalSportsInput
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => {
                          updateFormData("email", e.target.value);
                          debouncedCheckEmail(e.target.value);
                        }}
                        error={errors.email}
                        variant="doctor"
                      />
                      
                      {/* Email validation indicator */}
                      {formData.email && formData.email.includes('@') && (
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                          {emailValidationStatus === 'checking' && (
                            <svg className="animate-spin h-5 w-5 text-muted-green" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                          )}
                          
                          {emailValidationStatus === 'available' && (
                            <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          )}
                          
                          {emailValidationStatus === 'taken' && (
                            <svg className="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          )}
                        </div>
                      )}
                    </div>
                    
                    {emailValidationStatus === 'taken' && !errors.email && (
                      <p className="text-red-500 text-xs mt-1">This email is already registered</p>
                    )}
                    
                    {emailValidationStatus === 'available' && (
                      <p className="text-green-500 text-xs mt-1">Email is available</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password" className="text-foreground">Password</Label>
                    <MedicalSportsInput
                      id="password"
                      type="password"
                      value={formData.password}
                      onChange={(e) => {
                        updateFormData("password", e.target.value);
                        setPasswordStrength(checkPasswordStrength(e.target.value));
                      }}
                      error={errors.password}
                      variant="doctor"
                    />
                    
                    {/* Password strength indicator */}
                    {formData.password.length > 0 && (
                      <div className="space-y-1">
                        <div className="flex space-x-1">
                          {Array.from({ length: 5 }).map((_, i) => (
                            <div
                              key={i}
                              className={`h-2 flex-1 rounded-full ${
                                passwordStrength >= i + 1
                                  ? getPasswordStrengthColor(passwordStrength)
                                  : "bg-accent"
                              }`}
                            />
                          ))}
                        </div>
                        <p className={`text-xs ${
                          passwordStrength <= 1 ? "text-red-500" : 
                          passwordStrength <= 2 ? "text-orange-500" : 
                          passwordStrength <= 3 ? "text-yellow-500" : 
                          passwordStrength <= 4 ? "text-blue-500" : 
                          "text-green-500"
                        }`}>
                          {getPasswordStrengthText(passwordStrength)}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword" className="text-foreground">Confirm Password</Label>
                    <MedicalSportsInput
                      id="confirmPassword"
                      type="password"
                      value={formData.confirmPassword}
                      onChange={(e) => updateFormData("confirmPassword", e.target.value)}
                      error={errors.confirmPassword}
                      variant="doctor"
                    />
                  </div>
                </div>
              )}

              {phase === 2 && (
                <div className="space-y-4">
                  <div className="flex gap-4">
                    <div className="space-y-2 flex-1">
                      <Label htmlFor="firstName" className="text-foreground">First Name</Label>
                      <MedicalSportsInput
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => updateFormData("firstName", e.target.value)}
                        error={errors.firstName}
                        variant="doctor"
                      />
                    </div>
                    <div className="space-y-2 flex-1">
                      <Label htmlFor="lastName" className="text-foreground">Last Name</Label>
                      <MedicalSportsInput
                        id="lastName"
                        value={formData.lastName}
                        onChange={(e) => updateFormData("lastName", e.target.value)}
                        error={errors.lastName}
                        variant="doctor"
                      />
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <div className="space-y-2 flex-1">
                      <Label htmlFor="age" className="text-foreground">Age</Label>
                      <MedicalSportsInput
                        id="age"
                        type="number"
                        value={formData.age}
                        onChange={(e) => updateFormData("age", e.target.value)}
                        error={errors.age}
                        variant="doctor"
                      />
                    </div>
                    <div className="space-y-2 flex-1">
                      <Label htmlFor="gender" className="text-foreground">Gender</Label>
                      <select
                        id="gender"
                        value={formData.gender}
                        onChange={(e) => updateFormData("gender", e.target.value)}
                        className={`w-full px-3 py-2 text-foreground bg-background/90 border ${
                          errors.gender ? "border-red-500" : "border-slate-700"
                        } rounded-md focus:outline-none focus:ring-2 focus:ring-green-500`}
                      >
                        <option value="">Select gender</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                      </select>
                      {errors.gender && (
                        <p className="text-red-500 text-sm mt-1">{errors.gender}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phoneNumber" className="text-foreground">Phone Number</Label>
                    <MedicalSportsInput
                      id="phoneNumber"
                      value={formData.phoneNumber}
                      onChange={(e) => updateFormData("phoneNumber", e.target.value)}
                      error={errors.phoneNumber}
                      variant="doctor"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="specialty" className="text-foreground">Medical Specialty</Label>
                    <select
                      id="specialty"
                      value={formData.specialtyId}
                      onChange={(e) => {
                        const selectedId = e.target.value;
                        const selectedSpecialty = specialties.find(s => s.specialty_id === selectedId);
                        updateFormData("specialtyId", selectedId);
                        updateFormData("specialty", selectedSpecialty?.specialty_name || "");
                      }}
                      className={`w-full px-3 py-2 text-foreground bg-background/90 border ${
                        errors.specialty ? "border-red-500" : "border-slate-700"
                      } rounded-md focus:outline-none focus:ring-2 focus:ring-green-500`}
                      disabled={isLoadingSpecialties}
                    >
                      <option value="">Select specialty</option>
                      {specialties.map((specialty) => (
                        <option key={specialty.specialty_id} value={specialty.specialty_id}>
                          {specialty.specialty_name}
                        </option>
                      ))}
                    </select>
                    {isLoadingSpecialties && (
                      <p className="text-foreground/70 text-sm">Loading specialties...</p>
                    )}
                    {errors.specialty && (
                      <p className="text-red-500 text-sm mt-1">{errors.specialty}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="country" className="text-foreground">Country</Label>
                    <select
                      id="country"
                      value={formData.countryId}
                      onChange={(e) => {
                        const selectedId = e.target.value;
                        const selectedCountry = countries.find(c => c.country_id === selectedId);
                        updateFormData("countryId", selectedId);
                        updateFormData("country", selectedCountry?.country_name || "");
                        // Reset hospital when country changes
                        updateFormData("hospital", "");
                        updateFormData("hospitalId", "");
                      }}
                      className={`w-full px-3 py-2 text-foreground bg-background/90 border ${
                        errors.country ? "border-red-500" : "border-slate-700"
                      } rounded-md focus:outline-none focus:ring-2 focus:ring-green-500`}
                      disabled={isLoadingCountries}
                    >
                      <option value="">Select country</option>
                      {countries.map((country) => (
                        <option key={country.country_id} value={country.country_id}>
                          {country.country_name}
                        </option>
                      ))}
                    </select>
                    {isLoadingCountries && (
                      <p className="text-foreground/70 text-sm">Loading countries...</p>
                    )}
                    {errors.country && (
                      <p className="text-red-500 text-sm mt-1">{errors.country}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="licenseNumber" className="text-foreground">License Number</Label>
                    <MedicalSportsInput
                      id="licenseNumber"
                      value={formData.licenseNumber}
                      onChange={(e) => updateFormData("licenseNumber", e.target.value)}
                      error={errors.licenseNumber}
                      variant="doctor"
                    />
                  </div>
                </div>
              )}

              {phase === 3 && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="hospital" className="text-foreground">Hospital/Clinic</Label>
                    <div className="relative">
                      <MedicalSportsInput
                        id="hospital"
                        value={formData.hospital}
                        onChange={(e) => {
                          updateFormData("hospital", e.target.value);
                          filterHospitals(e.target.value);
                        }}
                        error={errors.hospital}
                        variant="doctor"
                        placeholder={formData.countryId ? "Search for hospital/clinic" : "No country selected, please go back"}
                        disabled={!formData.countryId || isLoadingHospitals}
                      />
                      {isLoadingHospitals && (
                        <p className="text-foreground/70 text-sm">Loading hospitals...</p>
                      )}
                      {formData.countryId && filteredHospitals.length > 0 && formData.hospital && !formData.hospitalId && (
                        <div className="absolute z-10 mt-1 w-full bg-background/90 border border-slate-700 rounded-md max-h-60 overflow-auto">
                          {filteredHospitals.map((hospital) => (
                            <div
                              key={hospital.hospital_id}
                              className="px-4 py-2 hover:bg-background/80 cursor-pointer text-foreground"
                              onClick={() => {
                                updateFormData("hospital", hospital.hospital_name);
                                updateFormData("hospitalId", hospital.hospital_id);
                                setFilteredHospitals([]);
                              }}
                            >
                              {hospital.hospital_name}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                    {!formData.countryId && (
                      <p className="text-amber-500 text-sm mt-1">Please go back and select a country first.</p>
                    )}
                    {formData.countryId && filteredHospitals.length === 0 && !isLoadingHospitals && (
                      <div className="mt-2">
                        <p className="text-muted-green text-sm mb-2">No matching hospitals found. You can:</p>
                        <div className="flex flex-col gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-foreground border-border bg-transparent hover:bg-card"
                            onClick={() => {
                              // Use the current input as a custom hospital name
                              if (formData.hospital.trim()) {
                                updateFormData("hospitalId", `new-${Date.now()}`); // Temporary ID for new hospital
                                toast({
                                  title: "Custom hospital added",
                                  description: "Your hospital will be added to our database after review.",
                                });
                              } else {
                                toast({
                                  title: "Enter hospital name",
                                  description: "Please type your hospital name first.",
                                  variant: "destructive",
                                });
                              }
                            }}
                          >
                            Use "{formData.hospital}" as my hospital
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-primary hover:text-primary/80 hover:bg-transparent"
                            onClick={() => {
                              // Clear the search to see all hospitals again
                              updateFormData("hospital", "");
                              setFilteredHospitals(hospitals);
                            }}
                          >
                            Clear search to see all options
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="city" className="text-foreground">City</Label>
                    <MedicalSportsInput
                      id="city"
                      value={formData.city}
                      onChange={(e) => updateFormData("city", e.target.value)}
                      error={errors.city}
                      variant="doctor"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="state" className="text-foreground">State/Province</Label>
                    <MedicalSportsInput
                      id="state"
                      value={formData.state}
                      onChange={(e) => updateFormData("state", e.target.value)}
                      variant="doctor"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="yearsOfExperience" className="text-foreground">Years of Experience</Label>
                    <MedicalSportsInput
                      id="yearsOfExperience"
                      type="number"
                      value={formData.yearsOfExperience}
                      onChange={(e) => updateFormData("yearsOfExperience", e.target.value)}
                      error={errors.yearsOfExperience}
                      variant="doctor"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="biography" className="text-foreground">Biography</Label>
                    <textarea
                      id="biography"
                      value={formData.biography}
                      onChange={(e) => updateFormData("biography", e.target.value)}
                      className="w-full px-3 py-2 text-foreground bg-background/90 border border-slate-700 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 min-h-[100px]"
                      placeholder="Tell us about yourself and your practice..."
                    />
                  </div>

                  {/* New fields for Task 1 */}
                  <div className="space-y-2">
                    <Label htmlFor="personalBiography" className="text-foreground">Personal Biography</Label>
                    <textarea
                      id="personalBiography"
                      value={formData.personalBiography}
                      onChange={(e) => updateFormData("personalBiography", e.target.value)}
                      className="w-full px-3 py-2 text-foreground bg-background/90 border border-slate-700 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 min-h-[100px]"
                      placeholder="Tell us about your personal background and interests..."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="workHistory" className="text-foreground">Work History</Label>
                    <textarea
                      id="workHistory"
                      value={formData.workHistory}
                      onChange={(e) => updateFormData("workHistory", e.target.value)}
                      className="w-full px-3 py-2 text-foreground bg-background/90 border border-slate-700 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 min-h-[100px]"
                      placeholder="Describe your professional work experience and career progression..."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="timings" className="text-foreground">Timings</Label>
                    <textarea
                      id="timings"
                      value={formData.timings}
                      onChange={(e) => updateFormData("timings", e.target.value)}
                      className="w-full px-3 py-2 text-foreground bg-background/90 border border-slate-700 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 min-h-[80px]"
                      placeholder="Specify your availability and working hours (e.g., Mon-Fri 9AM-5PM)..."
                    />
                  </div>

                  {/* Profile Image Upload */}
                  <div className="space-y-2">
                    <Label htmlFor="profileImage" className="text-foreground">Profile Image</Label>
                    <div className="flex items-center gap-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => document.getElementById("profileImage")?.click()}
                        className="px-4 py-2 bg-green-700 hover:bg-green-600 text-foreground border-2 border-green-500 rounded-md flex items-center gap-2 transition-colors duration-200"
                      >
                        <Upload className="w-4 h-4" />
                        Upload Image
                      </Button>
                      <input
                        id="profileImage"
                        name="profileImage"
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                      />
                      {formData.profileImage && (
                        <span className="text-sm text-green-400">
                          {formData.profileImage.name}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Upload a professional profile photo (max 10MB)
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Button Container */}
            <div className="flex-none pt-2 border-t border-green-500/30">
              <div className="flex justify-between">
                {phase > 1 ? (
                  <MedicalSportsButton onClick={handleBack} variant="doctor">
                    Back
                  </MedicalSportsButton>
                ) : (
                  <div></div>
                )}
                {phase < 3 ? (
                  <MedicalSportsButton onClick={handleNext} variant="doctor">
                    Next
                  </MedicalSportsButton>
                ) : (
                  <MedicalSportsButton onClick={handleSubmit} disabled={isLoading} variant="doctor">
                    {isLoading ? "Submitting..." : "Submit"}
                  </MedicalSportsButton>
                )}
              </div>
            </div>
          </>
        ) : (
          <div className="flex-grow flex items-center justify-center">
            <div className="bg-green-600/10 p-6 rounded-lg">
              <div className="w-16 h-16 bg-green-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Stethoscope className="h-8 w-8 text-green-500" />
              </div>
              <h3 className="text-2xl font-bold text-center text-foreground mb-4">Registration Complete!</h3>
              <p className="text-foreground text-center mb-3">
                Thank you for registering! Your account has been created.
              </p>
              <p className="text-foreground text-center mb-3">
                We've sent a verification email to <strong>{formData.email}</strong>
              </p>
              <p className="text-foreground text-center mb-6">
                Please check your inbox (including spam folder) and click the verification link to activate your account.
              </p>
              <MedicalSportsButton onClick={() => router.push("/doctor/login")} variant="doctor">
                Go to Login
              </MedicalSportsButton>
            </div>
          </div>
        )}
        
        {/* Footer */}
        <div className="mt-4 text-center text-sm text-foreground/70">
          DOCTORS LEAGUES • COMPETITOR REGISTRATION
        </div>
      </DialogContent>
    </Dialog>
  )
}
