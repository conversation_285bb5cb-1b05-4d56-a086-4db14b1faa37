"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogClose } from "@/lib/mock-radix-dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  X,
  Upload,
  ChevronRight,
  ChevronLeft,
  Stethoscope,
  Check,
  Trophy,
  Loader2,
  Medal,
  Award,
  Star,
  Activity,
  Microscope,
  Clipboard,
  User,
  ArrowRight,
  Hospital,
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { useToast } from "@/components/ui/mock-toast"
import { RegistrationConfirmation } from "../registration-confirmation"
// Use the correctly initialized singleton client for browser operations
import { supabase } from "@/lib/supabase-client" 
import { Label } from "@/components/ui/label"
// Remove direct createClient import as it's handled in actions or the main client
// import { createClient } from '@supabase/supabase-js' 
// Import server actions for operations requiring admin privileges
import { 
  createDoctorAuthUser, 
  uploadDoctorProfileImage, 
  createDoctorProfile,
  resendDoctorVerificationEmail // Use the action created
} from '@/actions/doctor-registration-actions'; // Use the new actions file

interface NewDoctorRegistrationProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

// REMOVE Insecure client creation from component
// const supabaseAdmin = createClient(...)
// REMOVE Logging of insecure keys from component
// console.log(...)

export function NewDoctorRegistration({ open, onOpenChange }: NewDoctorRegistrationProps) {
  const { toast } = useToast()
  const [currentStage, setCurrentStage] = useState<number>(1)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [confirmedEmail, setConfirmedEmail] = useState("")
  const [isMounted, setIsMounted] = useState(false)

  // Add isMounted state to prevent hydration errors with animations
  useEffect(() => {
    setIsMounted(true)
  }, [])

  const [formData, setFormData] = useState({
    // Stage 1: Account Information
    email: "",
    password: "",
    confirmPassword: "",

    // Stage 2: Basic Professional Information
    fullname: "",
    country: "",
    countryId: "",
    hospital: "",
    facilityId: "",
    medical_title: "",
    specialty: "",
    specialtyId: "",
    subspecialty: "",
    phone_number: "",
    languages_spoken: "",
    profileImage: null as File | null,

    // Stage 3: Detailed Professional Information
    educational_background: "",
    board_certifications: "",
    experience: "",
    publications: "",
    awards_recognitions: "",
    professional_affiliations: "",
    procedures_performed: "",
    treatment_services_expertise: "",

    // New fields for Task 1
    personal_biography: "",
    work_history: "",
    timings: "",
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isCheckingEmail, setIsCheckingEmail] = useState(false)

  // Add states for facilities, specialties, and countries
  const [facilities, setFacilities] = useState<{ id: string; name: string }[]>([])
  const [specialties, setSpecialties] = useState<{ id: string; name: string }[]>([])
  const [countries, setCountries] = useState<{ id: string; name: string }[]>([])
  const [loadingFacilities, setLoadingFacilities] = useState(false)
  const [loadingSpecialties, setLoadingSpecialties] = useState(false)
  const [loadingCountries, setLoadingCountries] = useState(false)
  
  // Modify state to add email validation status
  const [emailValidationStatus, setEmailValidationStatus] = useState<'idle' | 'checking' | 'available' | 'taken'>('idle')
  
  // Add state for filtered facilities
  const [filteredFacilities, setFilteredFacilities] = useState<{ id: string; name: string }[]>([])
  
  // Fetch facilities and specialties on component mount
  useEffect(() => {
    fetchFacilitiesAndSpecialties()
  }, [])

  // Effect to filter facilities when country changes
  useEffect(() => {
    if (formData.countryId) {
      // Filter facilities by country ID
      filterFacilitiesByCountry(formData.countryId);
    } else {
      // No country selected, show all facilities
      setFilteredFacilities(facilities);
    }
  }, [formData.countryId, facilities]);
  
  // Function to fetch facilities and specialties
  const fetchFacilitiesAndSpecialties = async () => {
    setLoadingFacilities(true)
    setLoadingSpecialties(true)
    setLoadingCountries(true)
    
    // Fetch countries
    try {
      console.log("Fetching countries from countries table...")
      
      const { data: countriesData, error: countriesError } = await supabase
        .from('countries')
        .select('country_id, country_name')
        .order('country_name')
        
      if (countriesError) {
        console.error('Error fetching countries:', countriesError)
        console.error('Error details:', {
          message: countriesError.message,
          code: countriesError.code,
          details: countriesError.details
        })
      } else if (countriesData) {
        console.log(`Retrieved ${countriesData.length} countries`)
        if (countriesData.length > 0) {
          console.log('Sample country:', countriesData[0])
        }
        
        // Transform the data to match our expected format
        const formattedCountries = countriesData.map(country => ({
          id: country.country_id.toString(),
          name: country.country_name
        }))
        setCountries(formattedCountries)
      } else {
        console.log('No countries data returned, but no error either')
      }
    } catch (error) {
      console.error('Exception fetching countries:', error)
      if (error instanceof Error) {
        console.error('Error message:', error.message)
        console.error('Error stack:', error.stack)
      }
    } finally {
      setLoadingCountries(false)
    }
    
    // Fetch facilities (hospitals)
    try {
      console.log("Fetching facilities from hospitals table...")
      console.log("Supabase URL:", process.env.NEXT_PUBLIC_SUPABASE_URL)
      
      const { data: facilitiesData, error: facilitiesError } = await supabase
        .from('hospitals')
        .select('hospital_id, hospital_name')
        .order('hospital_name')
        
      if (facilitiesError) {
        console.error('Error fetching facilities:', facilitiesError)
        console.error('Error details:', {
          message: facilitiesError.message,
          code: facilitiesError.code,
          details: facilitiesError.details
        })
      } else if (facilitiesData) {
        console.log(`Retrieved ${facilitiesData.length} facilities`)
        if (facilitiesData.length > 0) {
          console.log('Sample facility:', facilitiesData[0])
        }
        
        // Transform the data to match our expected format
        const formattedFacilities = facilitiesData.map(hospital => ({
          id: hospital.hospital_id.toString(),
          name: hospital.hospital_name
        }))
        setFacilities(formattedFacilities)
      } else {
        console.log('No facilities data returned, but no error either')
      }
    } catch (error) {
      console.error('Exception fetching facilities:', error)
      if (error instanceof Error) {
        console.error('Error message:', error.message)
        console.error('Error stack:', error.stack)
      }
    } finally {
      setLoadingFacilities(false)
    }
    
    // Fetch specialties
    try {
      console.log("Fetching specialties from specialties table...")
      
      const { data: specialtiesData, error: specialtiesError } = await supabase
        .from('specialties')
        .select('specialty_id, specialty_name')
        .order('specialty_name')
        
      if (specialtiesError) {
        console.error('Error fetching specialties:', specialtiesError)
        console.error('Error details:', {
          message: specialtiesError.message,
          code: specialtiesError.code,
          details: specialtiesError.details
        })
      } else if (specialtiesData) {
        console.log(`Retrieved ${specialtiesData.length} specialties`)
        if (specialtiesData.length > 0) {
          console.log('Sample specialty:', specialtiesData[0])
        }
        
        // Transform the data to match our expected format
        const formattedSpecialties = specialtiesData.map(specialty => ({
          id: specialty.specialty_id.toString(),
          name: specialty.specialty_name
        }))
        setSpecialties(formattedSpecialties)
      } else {
        console.log('No specialties data returned, but no error either')
      }
    } catch (error) {
      console.error('Exception fetching specialties:', error)
      if (error instanceof Error) {
        console.error('Error message:', error.message)
        console.error('Error stack:', error.stack)
      }
    } finally {
      setLoadingSpecialties(false)
    }
  }

  // Function to filter facilities by country ID
  const filterFacilitiesByCountry = async (countryId: string) => {
    try {
      console.log(`Filtering hospitals for country ID: ${countryId}`);
      
      // Fetch hospitals for this country
      const { data, error } = await supabase
        .from('hospitals')
        .select('hospital_id, hospital_name')
        .eq('country_id', countryId)
        .order('hospital_name');
        
      if (error) {
        console.error('Error filtering hospitals by country:', error);
        // Fallback to unfiltered list
        setFilteredFacilities(facilities);
      } else if (data && data.length > 0) {
        console.log(`Found ${data.length} hospitals for country ID ${countryId}`);
        // Format hospitals data
        const formattedHospitals = data.map(hospital => ({
          id: hospital.hospital_id.toString(),
          name: hospital.hospital_name
        }));
        setFilteredFacilities(formattedHospitals);
      } else {
        console.log(`No hospitals found for country ID ${countryId}`);
        setFilteredFacilities([]);
      }
    } catch (error) {
      console.error('Exception filtering hospitals by country:', error);
      setFilteredFacilities(facilities);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))

    // Clear error when user types
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }

    // Special handling for facilityId selection
    if (name === "facilityId" && value) {
      const selectedFacility = filteredFacilities.find(f => f.id === value);
      if (selectedFacility) {
        setFormData(prev => ({
          ...prev,
          facilityId: value,
          hospital: selectedFacility.name
        }));
      }
    }

    // Check email availability
    if (name === "email" && value.trim().length > 0 && value.includes("@")) {
      setIsCheckingEmail(true)
      setEmailValidationStatus('checking')
      checkEmailAvailability(value)
    }
  }

  const checkEmailAvailability = async (email: string) => {
    try {
      // Check users table first
      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("email")
        .eq("email", email)
        .limit(1);

      if (userError) {
        console.error("Error checking email in users table:", userError)
      }

      // Check doctors table as well
      const { data: doctorData, error: doctorError } = await supabase
        .from("doctors")
        .select("email")
        .eq("email", email)
        .limit(1);

      if (doctorError) {
        console.error("Error checking email in doctors table:", doctorError)
      }

      // Email is taken if it exists in either table
      if ((userData && userData.length > 0) || (doctorData && doctorData.length > 0)) {
        setErrors((prev) => ({ ...prev, email: "Email already registered" }))
        setEmailValidationStatus('taken')
      } else {
        // Email is available
        setEmailValidationStatus('available')
      }
    } catch (error) {
      console.error("Error checking email:", error)
      setEmailValidationStatus('idle')
    } finally {
      setIsCheckingEmail(false)
    }
  }

  const validateStage = (stage: number): boolean => {
    const newErrors: Record<string, string> = {}

    if (stage === 1) {
      if (!formData.email) newErrors.email = "Email is required"
      else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = "Email is invalid"
      } else if (emailValidationStatus === 'taken') {
        newErrors.email = "Email is already registered";
      }

      if (!formData.password) newErrors.password = "Password is required"
      else if (formData.password.length < 8) {
        newErrors.password = "Password must be at least 8 characters"
      }

      if (!formData.confirmPassword) newErrors.confirmPassword = "Please confirm your password"
      else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = "Passwords do not match"
      }
    }

    if (stage === 2) {
      if (!formData.fullname) newErrors.fullname = "Full name is required"
      if (!formData.country) newErrors.country = "Country is required" 
      if (!formData.hospital) newErrors.hospital = "Hospital is required"
      if (!formData.medical_title) newErrors.medical_title = "Medical title is required"
      if (!formData.specialty) newErrors.specialty = "Specialty is required"
      if (!formData.profileImage) newErrors.profileImage = "Profile image is required"
    }

    if (stage === 3) {
      if (!formData.educational_background) {
        newErrors.educational_background = "Educational background is required"
      }
      if (!formData.experience) newErrors.experience = "Experience is required"
      if (!formData.treatment_services_expertise) {
        newErrors.treatment_services_expertise = "Treatment services expertise is required"
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Function to reset errors
  const resetErrors = () => {
    setErrors({});
  };

  const handleNextStage = () => {
    console.log("handleNextStage called, current stage:", currentStage);

    // For stage 1, make sure email validation has completed
    if (currentStage === 1 && formData.email) {
      if (emailValidationStatus === 'checking') {
        // Email validation is still in progress
        toast({
          title: "Please wait",
          description: "We're still checking if your email is available.",
          variant: "default",
        });
        return;
      }
    }
    
    const isValid = validateStage(currentStage);
    console.log("Form validation result:", isValid);
    
    if (isValid) {
      resetErrors(); // Clear any previous errors
      const nextStage = currentStage + 1;
      console.log("Moving to stage:", nextStage);
      setCurrentStage(nextStage);
    } else {
      console.log("Validation failed, form errors:", errors);
      // Show toast with validation errors
      toast({
        title: "Validation Error",
        description: "Please correct the highlighted fields before continuing.",
        variant: "destructive",
      });
    }
  };

  const handlePrevStage = () => {
    resetErrors(); // Clear errors when going back
    const prevStage = Math.max(currentStage - 1, 1);
    setCurrentStage(prevStage);
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      try {
        // Show file info
        const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
        const fileType = file.type.split('/')[1].toUpperCase();
        
        // Provide feedback about immediate optimization
        toast({
          title: `Image Selected (${fileType})`,
          description: `Size: ${fileSizeMB}MB. Optimizing image now...`,
          variant: "default",
        });
        
        // Optimize image immediately upon selection
        const optimizedImage = await optimizeImage(file);
        
        // Store optimized file in form data
        setFormData((prev) => ({ ...prev, profileImage: optimizedImage }))
        
        // Show optimization results
        const optimizedSizeMB = (optimizedImage.size / (1024 * 1024)).toFixed(2);
        const reduction = Math.round((1 - optimizedImage.size / file.size) * 100);
        
        toast({
          title: "Image Optimized",
          description: `Reduced from ${fileSizeMB}MB to ${optimizedSizeMB}MB (${reduction}% smaller)`,
          variant: "default",
        });
      } catch (error) {
        console.error('Error processing image:', error);
        toast({
          title: "Image processing warning",
          description: "There was an issue optimizing your image. Using original image instead.",
          variant: "destructive",
        });
        
        // Store original file if optimization fails
        setFormData((prev) => ({ ...prev, profileImage: file }))
      }
    }
  }

  // Image optimization function that converts to WebP and resizes
  const optimizeImage = async (file: File): Promise<File> => {
    if (!file.type.startsWith('image/')) {
      return file; // Not an image, return original
    }
    
    // Create an image element to load the file
    const img = document.createElement('img');
    const originalFileSizeMB = file.size / (1024 * 1024);
    console.log(`Original image size: ${originalFileSizeMB.toFixed(2)}MB`);
    
    // Convert to a data URL to manipulate with canvas
    const objectUrl = URL.createObjectURL(file);
    
    try {
      // Wait for the image to load
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = objectUrl;
      });
      
      // Create a canvas and resize the image 
      // Maximum dimensions for a profile photo
      const MAX_WIDTH = 800;
      const MAX_HEIGHT = 800;
      
      let width = img.width;
      let height = img.height;
      
      // Calculate new dimensions while maintaining aspect ratio
      if (width > height) {
        if (width > MAX_WIDTH) {
          height = Math.round(height * (MAX_WIDTH / width));
          width = MAX_WIDTH;
        }
      } else {
        if (height > MAX_HEIGHT) {
          width = Math.round(width * (MAX_HEIGHT / height));
          height = MAX_HEIGHT;
        }
      }
      
      // Create canvas and draw resized image
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error("Couldn't get canvas context");
      
      ctx.drawImage(img, 0, 0, width, height);
      
      // Convert to WebP with decent quality (0.8 = 80%)
      const QUALITY = 0.8;
      const webpDataUrl = canvas.toDataURL('image/webp', QUALITY);
      
      // Convert data URL back to blob/file
      const byteString = atob(webpDataUrl.split(',')[1]);
      const mimeString = webpDataUrl.split(',')[0].split(':')[1].split(';')[0];
      const ab = new ArrayBuffer(byteString.length);
      const ia = new Uint8Array(ab);
      
      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }
      
      // Create optimized file with WebP extension
      const optimizedBlob = new Blob([ab], { type: mimeString });
      const optimizedName = file.name.split('.')[0] + '.webp';
      const optimizedFile = new File([optimizedBlob], optimizedName, { 
        type: mimeString,
        lastModified: file.lastModified 
      });
      
      const optimizedFileSizeMB = optimizedFile.size / (1024 * 1024);
      console.log(`Optimized to WebP: ${optimizedFileSizeMB.toFixed(2)}MB (${Math.round((1 - optimizedFile.size / file.size) * 100)}% reduction)`);
      
      return optimizedFile;
    } finally {
      // Always clean up the object URL
      URL.revokeObjectURL(objectUrl);
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateStage(3)) {
      return;
    }

    setIsSubmitting(true);
    console.log("=== DOCTOR REGISTRATION PROCESS STARTED ===");
    console.log("Form data being submitted:", formData);

    try {
      // Find the facility name based on the selected facility ID
      const selectedFacility = facilities.find(f => f.id === formData.facilityId);
      const hospitalName = selectedFacility ? selectedFacility.name : "";

      // Find the specialty name based on selected specialty ID
      const selectedSpecialty = specialties.find(s => s.id === formData.specialtyId);
      const specialtyName = selectedSpecialty ? selectedSpecialty.name : "";

      // Use the custom auth endpoint for registration
      const customAuthData = {
        email: formData.email,
        password: formData.password,
        userType: 'doctor' as const,
        profileData: {
          fullname: formData.fullname,
          hospital: hospitalName,
          hospital_affiliation: hospitalName,
          medical_title: formData.medical_title,
          medical_specialty: specialtyName,
          facilityId: formData.facilityId,
          specialtyId: formData.specialtyId,
          countryId: formData.countryId,
          country: formData.country,
          educational_board: formData.educational_background,
          phone_number: formData.phone_number,
          experience: formData.experience,
          subspecialization: formData.subspecialty || "",
          certifications: formData.board_certifications || "",
          recognitions: formData.awards_recognitions || "",
          languages: formData.languages_spoken || "",
          professional_affiliation: formData.professional_affiliations || "",
          procedures_performed: formData.procedures_performed || "",
          treatment_services: formData.treatment_services_expertise || "",
          // New fields for Task 1
          personal_biography: formData.personal_biography || "",
          work_history: formData.work_history || "",
          timings: formData.timings || ""
        }
      };
      
      console.log("Sending registration data to custom auth endpoint:", JSON.stringify(customAuthData, null, 2));
      
      // Send registration request to custom auth endpoint
      const response = await fetch('/api/auth/custom/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(customAuthData),
      });
      
      const result = await response.json();
      
      if (!response.ok || !result.success) {
        console.error("Registration failed:", result.error);
        toast({
          title: "Registration failed",
          description: result.error || "Could not create your account. Please try again.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }
      
      console.log("Registration successful with user ID:", result.userId);
      
      // Handle profile image upload if provided
      if (formData.profileImage && result.userId) {
        try {
          console.log("Uploading profile image...");
          // The image should already be optimized from handleFileChange
          // But just in case, let's ensure it's optimized before upload
          let imageToUpload = formData.profileImage;
          
          // Double-check if the image is already in WebP format
          if (!imageToUpload.type.includes('webp')) {
            console.log("Image isn't WebP yet, optimizing now...");
            try {
              imageToUpload = await optimizeImage(formData.profileImage);
            } catch (optimizationError) {
              console.warn("Failed to optimize during upload, using original:", optimizationError);
              // Continue with original image if optimization fails
            }
          }
          
          const uploadResult = await uploadDoctorProfileImage(result.userId, imageToUpload);
          if (uploadResult.error) {
            console.warn("Profile image upload failed:", uploadResult.error);
            // Non-critical error, continue with registration
          } else {
            console.log("Profile image uploaded successfully. URL:", uploadResult.profileImageUrl);
          }
        } catch (uploadError) {
          console.warn("Error uploading profile image:", uploadError);
          // Non-critical error, continue with registration
        }
      }
      
      // Registration successful
      toast({
        title: "Registration Successful",
        description: "Your account has been created! Please check your email to verify your account.",
        duration: 5000,
      });
      
      // Store the email before clearing form data
      setConfirmedEmail(formData.email);
      
      // Show confirmation dialog and reset form state
      setCurrentStage(1); // Reset to first stage for new registration
      setShowConfirmation(true);
      setIsSubmitting(false);
      
      // Clear the form data for when the user returns
      setFormData({
        email: "",
        password: "",
        confirmPassword: "",
        fullname: "",
        country: "",
        countryId: "",
        hospital: "",
        facilityId: "", 
        medical_title: "",
        specialty: "",
        specialtyId: "", 
        subspecialty: "",
        phone_number: "",
        languages_spoken: "",
        profileImage: null,
        educational_background: "",
        board_certifications: "",
        experience: "",
        publications: "",
        awards_recognitions: "",
        professional_affiliations: "",
        procedures_performed: "",
        treatment_services_expertise: "",
      });
      
    } catch (error) {
      console.error("Registration failed:", error);
      if (error instanceof Error) {
        console.error("Error stack:", error.stack);
      }
      setIsSubmitting(false);
      
      toast({
        title: "Registration Failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  const renderField = (
    label: string,
    name: keyof typeof formData,
    placeholder: string,
    type = "text",
    required = true,
    icon?: React.ReactNode,
  ) => {
    const value = formData[name] as string;
    const error = errors[name as string];

    return (
      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground flex items-center gap-2">
          {label}
          {required && <span className="text-red-400 ml-1">*</span>}
        </label>
        <div className="relative">
          {icon && <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-green-400">{icon}</div>}
          {type === 'textarea' ? (
            <Textarea
              name={name as string}
              placeholder={placeholder}
              value={value}
              onChange={handleInputChange}
              className={`${icon ? "pl-10" : ""} bg-card border-border text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary ${error ? "border-destructive" : name === "email" && emailValidationStatus === 'available' ? "border-primary" : ""}`}
            />
          ) : (
            <Input
              type={type}
              name={name as string}
              placeholder={placeholder}
              value={value}
              onChange={handleInputChange}
              className={`${icon ? "pl-10" : ""} bg-card border-border text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary ${error ? "border-destructive" : name === "email" && emailValidationStatus === 'available' ? "border-primary" : ""}`}
            />
          )}
          {name === 'email' && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {emailValidationStatus === 'checking' && (
                <Loader2 className="h-4 w-4 animate-spin text-primary" />
              )}
              {emailValidationStatus === 'available' && (
                <Check className="h-4 w-4 text-green-500" />
              )}
              {emailValidationStatus === 'taken' && (
                <X className="h-4 w-4 text-red-500" />
              )}
            </div>
          )}
        </div>
        {error && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-destructive text-sm flex items-center gap-1"
          >
            <X className="h-3 w-3" /> {error}
          </motion.p>
        )}
        {name === 'email' && emailValidationStatus === 'available' && !error && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-green-500 text-sm flex items-center gap-1"
          >
            <Check className="h-3 w-3" /> Email is available
          </motion.p>
        )}
        {name === 'email' && emailValidationStatus === 'taken' && !error && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-destructive text-sm flex items-center gap-1"
          >
            <X className="h-3 w-3" /> Email already registered
          </motion.p>
        )}
      </div>
    )
  }

  const checkPasswordStrength = (password: string): number => {
    let strength = 0;
    if (password.length >= 8) strength += 1;
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[a-z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;
    return strength;
  };

  const getPasswordStrengthColor = (strength: number): string => {
    if (strength <= 1) return "bg-red-500";
    if (strength <= 2) return "bg-orange-500";
    if (strength <= 3) return "bg-yellow-500";
    if (strength <= 4) return "bg-blue-500";
    return "bg-green-500";
  };

  const getPasswordStrengthText = (strength: number): string => {
    if (strength <= 1) return "Very Weak";
    if (strength <= 2) return "Weak";
    if (strength <= 3) return "Medium";
    if (strength <= 4) return "Strong";
    return "Very Strong";
  };

  // Light theme styles for doctor registration - only apply to light theme
  const lightThemeStyles = `
    /* Light Theme: Doctor Registration Dialog */
    html:not(.dark) .registration-dialog {
      background: linear-gradient(135deg, hsl(140, 60%, 95%) 0%, hsl(140, 50%, 90%) 100%) !important;
      border: 2px solid hsl(140, 80%, 30%) !important;
    }
    
    html:not(.dark) .registration-dialog *:not(svg):not(path) {
      color: hsl(140, 70%, 25%) !important;
    }
    
    html:not(.dark) .registration-dialog h1,
    html:not(.dark) .registration-dialog h2,
    html:not(.dark) .registration-dialog h3 {
      color: hsl(140, 80%, 20%) !important;
    }
    
    html:not(.dark) .registration-dialog .bg-gradient-to-r,
    html:not(.dark) .registration-dialog .bg-gradient-to-b {
      background: linear-gradient(135deg, hsl(140, 70%, 85%) 0%, hsl(140, 60%, 80%) 100%) !important;
    }
    
    /* Fix progress indicators and numbers visibility - ENHANCED */
    html:not(.dark) .registration-dialog .bg-green-800,
    html:not(.dark) .registration-dialog .bg-green-900,
    html:not(.dark) .registration-dialog .bg-green-950,
    html:not(.dark) .registration-dialog .bg-green-950\\/70 {
      background: hsl(140, 30%, 85%) !important;
      color: hsl(140, 90%, 15%) !important;
    }
    
    html:not(.dark) .registration-dialog .text-green-100,
    html:not(.dark) .registration-dialog .text-green-400,
    html:not(.dark) .registration-dialog .text-foreground,
    html:not(.dark) .registration-dialog span.font-bold {
      color: hsl(140, 90%, 15%) !important;
    }
    
    html:not(.dark) .registration-dialog .border-green-700,
    html:not(.dark) .registration-dialog .border-green-800,
    html:not(.dark) .registration-dialog .border-green-600 {
      border-color: hsl(140, 50%, 50%) !important;
    }
    
    /* Fix specific QUARTER and progress elements */
    html:not(.dark) .registration-dialog .bg-green-800.px-2,
    html:not(.dark) .registration-dialog .bg-green-900.px-3 {
      background: hsl(140, 20%, 80%) !important;
      color: hsl(140, 90%, 20%) !important;
    }
    
    html:not(.dark) .registration-dialog .text-lg.font-mono.font-bold,
    html:not(.dark) .registration-dialog .text-sm.font-mono.font-bold {
      color: hsl(140, 90%, 20%) !important;
    }
  `

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: lightThemeStyles }} />
    <Dialog 
      open={open && !showConfirmation} 
      onOpenChange={(value) => {
        console.log("Main dialog state changing:", value ? "opening" : "closing");
        
        // If dialog is being closed and confirmation is showing,
        // redirect to homepage to completely exit the registration flow
        if (!value && showConfirmation) {
          console.log("Main dialog closing while confirmation is showing - redirecting to homepage");
          if (typeof window !== 'undefined') {
            setTimeout(() => {
              window.location.href = '/';
            }, 100);
          }
        }
        
        // Always update the main dialog state
        setShowConfirmation(false);
        onOpenChange(value);
      }}
    >
      <DialogContent className="sm:max-w-2xl p-0 border-0 bg-transparent registration-dialog" style={{ background: 'rgba(255, 255, 255, 0.98)', border: '2px solid hsl(142, 76%, 36%)', boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1)', borderRadius: '1rem' }}>
        <div className="fixed inset-0 pointer-events-none">
          {/* Only render animations on the client side after mounting */}
          {isMounted && (
            <>
              {/* Animation backgrounds */}
              <motion.div
                className="absolute opacity-10"
                initial={{ x: "13.4641%", y: "15.0156%", scale: 0.83 }}
                animate={{ 
                  y: ["15.0156%", "18.0156%", "15.0156%"],
                  scale: [0.83, 0.86, 0.83]
                }}
                transition={{ duration: 8, repeat: Infinity, repeatType: "reverse" }}
              />
              <motion.div
                className="absolute opacity-10"
                initial={{ x: "27.5657%", y: "44.8286%", scale: 0.67 }}
                animate={{ 
                  y: ["44.8286%", "48.8286%", "44.8286%"],
                  scale: [0.67, 0.71, 0.67]
                }}
                transition={{ duration: 10, repeat: Infinity, repeatType: "reverse" }}
              />
              <motion.div
                className="absolute opacity-10"
                initial={{ x: "81.1635%", y: "0.2671%", scale: 0.51 }}
                animate={{ 
                  y: ["0.2671%", "5.2671%", "0.2671%"],
                  scale: [0.51, 0.55, 0.51]
                }}
                transition={{ duration: 9, repeat: Infinity, repeatType: "reverse" }}
              />
              <motion.div
                className="absolute opacity-10"
                initial={{ x: "24.9354%", y: "79.3306%", scale: 0.90 }}
                animate={{ 
                  y: ["79.3306%", "84.3306%", "79.3306%"],
                  scale: [0.90, 0.93, 0.90]
                }}
                transition={{ duration: 11, repeat: Infinity, repeatType: "reverse" }}
              />
              <motion.div
                className="absolute opacity-10"
                initial={{ x: "85.9816%", y: "58.8378%", scale: 0.80 }}
                animate={{ 
                  y: ["58.8378%", "63.8378%", "58.8378%"],
                  scale: [0.80, 0.84, 0.80]
                }}
                transition={{ duration: 7, repeat: Infinity, repeatType: "reverse" }}
              />
              <motion.div
                className="absolute opacity-10"
                initial={{ x: "60.6139%", y: "4.4268%", scale: 0.74 }}
                animate={{ 
                  y: ["4.4268%", "8.4268%", "4.4268%"],
                  scale: [0.74, 0.78, 0.74]
                }}
                transition={{ duration: 12, repeat: Infinity, repeatType: "reverse" }}
              />
            </>
          )}
        </div>
        <div className="relative overflow-hidden">
          {/* Sports-themed decorative elements */}
          <div className="absolute inset-0 z-0">
            {/* Stadium background */}
            <div className="absolute inset-0 bg-gradient-to-b from-green-900 via-green-800 to-green-950 opacity-90"></div>

            {/* Stadium lights */}
            <div className="absolute top-0 left-1/4 w-1 h-1 bg-white rounded-full shadow-[0_0_15px_10px_rgba(255,255,255,0.7)]"></div>
            <div className="absolute top-0 right-1/4 w-1 h-1 bg-white rounded-full shadow-[0_0_15px_10px_rgba(255,255,255,0.7)]"></div>

            {/* Field markings */}
            <div className="absolute inset-x-0 top-1/2 h-px bg-accent"></div>
            <div className="absolute inset-y-0 left-1/2 w-px bg-accent"></div>
            <div className="absolute top-1/2 left-1/2 w-20 h-20 border-2 border-border rounded-full -translate-x-1/2 -translate-y-1/2"></div>
          </div>

          {/* Medical elements overlay */}
          <div className="absolute inset-0 z-0 opacity-10">
            <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <pattern id="medicalCross" patternUnits="userSpaceOnUse" width="60" height="60">
                  <path
                    d="M25,5 L25,25 L5,25 L5,35 L25,35 L25,55 L35,55 L35,35 L55,35 L55,25 L35,25 L35,5 Z"
                    fill="white"
                  />
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#medicalCross)" />
            </svg>
          </div>

          {/* Floating sports equipment */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none z-0">
            {isMounted && [...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute opacity-10"
                initial={{
                  x: `${i * 15 + 10}%`,
                  y: `${i * 15 + 5}%`,
                  scale: 0.5 + (i * 0.1),
                }}
                animate={{
                  y: [`${i * 15 + 5}%`, `${i * 15}%`, `${i * 15 + 5}%`],
                  rotate: [0, i % 2 === 0 ? 15 : -15, 0],
                }}
                transition={{
                  duration: 10 + i,
                  repeat: Number.POSITIVE_INFINITY,
                  repeatType: "reverse"
                }}
              >
                {i % 3 === 0 ? (
                  <Trophy className="text-yellow-300 w-12 h-12" />
                ) : i % 3 === 1 ? (
                  <Stethoscope className="text-foreground w-12 h-12" />
                ) : (
                  <Microscope className="text-foreground w-12 h-12" />
                )}
              </motion.div>
            ))}
          </div>

          {/* Main content container with sports-themed frame */}
          <div className="relative z-10 rounded-xl overflow-hidden shadow-[0_0_25px_rgba(0,0,0,0.3)]">
            {/* Sports scoreboard-inspired header */}
            <div className="bg-gradient-to-r from-green-800 to-green-700 p-4 border-b-4 border-green-500">
  <div className="flex justify-between items-center">
    <div className="flex items-center gap-3">
      <div className="bg-green-600 p-2 rounded-full">
        <Stethoscope className="h-6 w-6 text-foreground" />
      </div>
      <h2 className="text-2xl font-bold text-foreground tracking-wide">MEDICAL COMPETITOR</h2>
    </div>
    <DialogClose className="ml-4 p-2 rounded-sm opacity-70 hover:opacity-100 focus:outline-none">
      <X className="h-6 w-6 text-foreground" />
      <span className="sr-only">Close</span>
    </DialogClose>
  </div>
</div>

            {/* Form container with sports field styling */}
            <div className="bg-gradient-to-b from-green-800/95 to-green-900/95 p-6 relative">
              {/* Center circle */}
              <div className="absolute top-1/2 left-1/2 w-40 h-40 border-2 border-border/50 rounded-full -translate-x-1/2 -translate-y-1/2"></div>

              {/* Progress indicator styled as scoreboard */}
              <div className="mb-6 bg-green-950/70 rounded-md border border-green-700 p-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <div className="bg-green-800 px-2 py-1 rounded border border-green-600">
                      <span className="text-sm font-mono font-bold text-green-100">QUARTER</span>
                    </div>
                    <div className="bg-green-900 px-3 py-1 rounded border border-green-700">
                      <span className="text-lg font-mono font-bold text-foreground">{currentStage}/3</span>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    {[1, 2, 3].map((step) => (
                      <div
                        key={step}
                        className={`h-3 w-8 rounded-sm ${step <= currentStage ? "bg-yellow-500" : "bg-green-800"}`}
                        onClick={() => {
                          console.log("Direct stage selection:", step);
                          resetErrors();
                          setCurrentStage(step);
                        }}
                        style={{ cursor: "pointer" }}
                      >
                        {step <= currentStage && isMounted && (
                          <motion.div
                            className="h-full w-full bg-yellow-400"
                            initial={{ opacity: 0.5 }}
                            animate={{ opacity: [0.5, 1, 0.5] }}
                            transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
                          />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Form content */}
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="max-h-[60vh] overflow-y-auto pr-4 space-y-6">
                  <AnimatePresence mode="wait">
                    {currentStage === 1 && isMounted && (
                      <motion.div
                        key="stage1"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                        className="space-y-4"
                      >
                        <div className="bg-green-950/70 p-4 rounded-md border border-green-700/80">
                          <div className="flex items-center gap-3 text-green-100">
                            <Trophy className="h-5 w-5 text-yellow-300" />
                            <p className="text-sm">
                              Create your account to join the medical competition as a competitor
                            </p>
                          </div>
                        </div>

                        {renderField(
                          "Email",
                          "email",
                          "Enter your email",
                          "email",
                          true,
                          <span className="text-lg">@</span>,
                        )}
                        <div className="space-y-2">
                          <Label htmlFor="password" className="text-foreground">Password *</Label>
                          <Input
                            id="password"
                            type="password"
                            name="password"
                            value={formData.password}
                            onChange={handleInputChange}
                            className={`${errors.password ? "border-destructive" : ""} bg-card border-border text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary`}
                          />
                          
                          {/* Password strength indicator */}
                          {formData.password.length > 0 && (
                            <div className="space-y-1">
                              <div className="flex space-x-1">
                                {Array.from({ length: 5 }).map((_, i) => (
                                  <div
                                    key={i}
                                    className={`h-2 flex-1 rounded-full ${
                                      checkPasswordStrength(formData.password) >= i + 1
                                        ? getPasswordStrengthColor(checkPasswordStrength(formData.password))
                                        : "bg-accent"
                                    }`}
                                  />
                                ))}
                              </div>
                              <p className={`text-xs ${
                                checkPasswordStrength(formData.password) <= 1 ? "text-red-500" : 
                                checkPasswordStrength(formData.password) <= 2 ? "text-orange-500" : 
                                checkPasswordStrength(formData.password) <= 3 ? "text-yellow-500" : 
                                checkPasswordStrength(formData.password) <= 4 ? "text-blue-500" : 
                                "text-green-500"
                              }`}>
                                {getPasswordStrengthText(checkPasswordStrength(formData.password))}
                              </p>
                            </div>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="confirmPassword" className="text-foreground">Confirm Password *</Label>
                          <Input
                            id="confirmPassword"
                            type="password"
                            name="confirmPassword"
                            value={formData.confirmPassword}
                            onChange={handleInputChange}
                            className={`${errors.confirmPassword ? "border-destructive" : ""} bg-card border-border text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary`}
                          />
                        </div>
                      </motion.div>
                    )}

                    {currentStage === 2 && isMounted && (
                      <motion.div
                        key="stage2"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                        className="space-y-4"
                      >
                        <div className="bg-card p-4 rounded-md border border-border">
                          <div className="flex items-center gap-3 text-foreground">
                            <Stethoscope className="h-5 w-5 text-primary" />
                            <p className="text-sm">
                              Enter your basic professional information to establish your competitive profile
                            </p>
                          </div>
                        </div>

                        {renderField(
                          "Full Name",
                          "fullname",
                          "Enter your full name",
                          "text",
                          true,
                          <User className="h-4 w-4" />,
                        )}
                        
                        {/* Country Dropdown */}
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground flex items-center gap-2">
                            Country
                            <span className="text-red-400 ml-1">*</span>
                          </label>
                          <div className="relative">
                            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-4 w-4"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2h2.5a2 2 0 002-2v-1a2 2 0 012-2h1.5a1 1 0 01.9.5l1.5 2a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                              </svg>
                            </div>
                            <select
                              name="countryId"
                              value={formData.countryId}
                              onChange={(e) => {
                                handleInputChange(e);
                                // Also update the country name
                                const selectedCountry = countries.find(c => c.id === e.target.value);
                                if (selectedCountry) {
                                  setFormData(prev => ({
                                    ...prev,
                                    country: selectedCountry.name
                                  }));
                                  
                                  // Reset hospital selection when country changes
                                  setFormData(prev => ({
                                    ...prev,
                                    facilityId: "",
                                    hospital: ""
                                  }));
                                  
                                  // TODO: Filter hospitals by country_id
                                }
                              }}
                              className={`w-full pl-10 py-2 bg-card border-border text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary rounded-md ${
                                errors.country ? "border-destructive" : ""
                              }`}
                              disabled={loadingCountries}
                            >
                              <option value="">Select a country</option>
                              {countries.map((country) => (
                                <option key={country.id} value={country.id}>
                                  {country.name}
                                </option>
                              ))}
                            </select>
                            {loadingCountries && (
                              <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-primary" />
                            )}
                          </div>
                          {errors.country && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="text-destructive text-sm flex items-center gap-1"
                            >
                              <X className="h-3 w-3" /> {errors.country}
                            </motion.p>
                          )}
                        </div>
                        
                        {/* Facility Dropdown */}
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground flex items-center gap-2">
                            Hospital
                            <span className="text-red-400 ml-1">*</span>
                          </label>
                          <div className="relative">
                            <Hospital className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                            <select
                              name="facilityId"
                              value={formData.facilityId}
                              onChange={handleInputChange}
                              className={`w-full pl-10 py-2 bg-card border-border text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary rounded-md ${
                                errors.hospital ? "border-destructive" : ""
                              }`}
                              disabled={loadingFacilities || !formData.countryId}
                            >
                              <option value="">
                                {formData.countryId 
                                  ? "Select a hospital" 
                                  : "Please select a country first"
                                }
                              </option>
                              {filteredFacilities.map((facility) => (
                                <option key={facility.id} value={facility.id}>
                                  {facility.name}
                                </option>
                              ))}
                            </select>
                            {loadingFacilities && (
                              <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-primary" />
                            )}
                          </div>
                          {!formData.countryId && !errors.hospital && (
                            <p className="text-amber-400 text-xs mt-1">
                              Please select a country to see available hospitals
                            </p>
                          )}
                          {formData.countryId && filteredFacilities.length === 0 && !loadingFacilities && (
                            <p className="text-muted-green text-xs mt-1">
                              No hospitals found for the selected country
                            </p>
                          )}
                          {errors.hospital && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="text-destructive text-sm flex items-center gap-1"
                            >
                              <X className="h-3 w-3" /> {errors.hospital}
                            </motion.p>
                          )}
                        </div>
                        
                        {renderField(
                          "Medical Title",
                          "medical_title",
                          "Enter your medical title",
                          "text",
                          true,
                          <Award className="h-4 w-4" />,
                        )}
                        
                        {/* Specialty Dropdown */}
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground flex items-center gap-2">
                            Specialty
                            <span className="text-red-400 ml-1">*</span>
                          </label>
                          <div className="relative">
                            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary">
                              <Microscope className="h-4 w-4" />
                            </div>
                            <select
                              name="specialtyId"
                              value={formData.specialtyId}
                              onChange={(e) => {
                                handleInputChange(e);
                                // Also update the specialty name
                                const selectedSpecialty = specialties.find(s => s.id === e.target.value);
                                if (selectedSpecialty) {
                                  setFormData(prev => ({
                                    ...prev,
                                    specialty: selectedSpecialty.name
                                  }));
                                }
                              }}
                              className={`w-full pl-10 py-2 bg-card border-border text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary rounded-md ${
                                errors.specialty ? "border-destructive" : ""
                              }`}
                              disabled={loadingSpecialties}
                            >
                              <option value="">Select a specialty</option>
                              {specialties.map((specialty) => (
                                <option key={specialty.id} value={specialty.id}>
                                  {specialty.name}
                                </option>
                              ))}
                            </select>
                            {loadingSpecialties && (
                              <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-primary" />
                            )}
                          </div>
                          {errors.specialty && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="text-destructive text-sm flex items-center gap-1"
                            >
                              <X className="h-3 w-3" /> {errors.specialty}
                            </motion.p>
                          )}
                        </div>
                        
                        {renderField(
                          "Subspecialty",
                          "subspecialty",
                          "Enter your subspecialty",
                          "text",
                          false,
                          <Clipboard className="h-4 w-4" />,
                        )}
                        {renderField(
                          "Phone Number (optional)",
                          "phone_number",
                          "Enter your phone number",
                          "tel",
                          false, // Changed to false to make it optional
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                          </svg>,
                        )}
                        
                        {renderField(
                          "Languages Spoken",
                          "languages_spoken",
                          "List languages spoken (optional)",
                          "text",
                          false, // Changed to false to make it optional
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M7 2a1 1 0 011 1v1h3a1 1 0 110 2H9v1h3a1 1 0 110 2H9v1h3a1 1 0 110 2H9v1h3a1 1 0 110 2H9a1 1 0 01-1-1v-1H4a1 1 0 110-2h4v-1H4a1 1 0 110-2h4v-1H4a1 1 0 110-2h4V4H4a1 1 0 110-2h4V1a1 1 0 011-1z"
                              clipRule="evenodd"
                            />
                          </svg>,
                        )}

                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground flex items-center gap-2">
                            Profile Image
                            <span className="text-red-400 ml-1">*</span>
                          </label>
                          <div className="relative">
                            <motion.div
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                              className="flex items-center gap-4"
                            >
                              <Button
                                type="button"
                                variant="outline"
                                onClick={() => document.getElementById("profile_image")?.click()}
                                className="px-4 py-2 bg-green-700 hover:bg-green-600 text-foreground border-2 border-green-500 rounded-md flex items-center gap-2 transition-colors duration-200"
                              >
                                <Upload className="w-4 h-4" />
                                Upload Image
                              </Button>
                              <input
                                id="profile_image"
                                name="profile_image"
                                type="file"
                                accept="image/*"
                                onChange={handleFileChange}
                                className="hidden"
                              />
                              {formData.profileImage && (
                                <div className="flex items-center gap-2 bg-green-800/30 px-3 py-1 rounded-md border border-green-700/30">
                                  <Check className="h-4 w-4 text-green-400" />
                                  <span className="text-sm text-foreground">{formData.profileImage.name}</span>
                                </div>
                              )}
                            </motion.div>
                          </div>
                          {errors.profileImage && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="text-destructive text-sm flex items-center gap-1"
                            >
                              <X className="h-3 w-3" /> {errors.profileImage}
                            </motion.p>
                          )}
                        </div>
                      </motion.div>
                    )}

                    {currentStage === 3 && isMounted && (
                      <motion.div
                        key="stage3"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                        className="space-y-4"
                      >
                        <div className="bg-green-950/70 p-4 rounded-md border border-green-700/80">
                          <div className="flex items-center gap-3 text-green-100">
                            <Medal className="h-5 w-5 text-yellow-300" />
                            <p className="text-sm">
                              Provide your professional achievements to showcase your competitive edge
                            </p>
                          </div>
                        </div>

                        {renderField(
                          "Educational Background",
                          "educational_background",
                          "Enter your educational background",
                          "textarea",
                          true,
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
                          </svg>,
                        )}
                        {renderField(
                          "Board Certifications",
                          "board_certifications",
                          "Enter your board certifications",
                          "textarea",
                          false,
                          <Clipboard className="h-4 w-4" />,
                        )}
                        {renderField(
                          "Experience (Years)",
                          "experience",
                          "Enter years of experience",
                          "number",
                          true,
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                              clipRule="evenodd"
                            />
                          </svg>,
                        )}
                        {renderField(
                          "Publications",
                          "publications",
                          "List your publications",
                          "textarea",
                          false,
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                              clipRule="evenodd"
                            />
                          </svg>,
                        )}
                        {renderField(
                          "Awards & Recognitions",
                          "awards_recognitions",
                          "List your awards and recognitions",
                          "textarea",
                          false,
                          <Trophy className="h-4 w-4" />,
                        )}
                        {renderField(
                          "Professional Affiliations",
                          "professional_affiliations",
                          "List your professional affiliations",
                          "textarea",
                          false,
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                          </svg>,
                        )}
                        {renderField(
                          "Procedures Performed",
                          "procedures_performed",
                          "List procedures you perform",
                          "textarea",
                          false,
                          <Activity className="h-4 w-4" />,
                        )}
                        {renderField(
                          "Treatment Services Expertise",
                          "treatment_services_expertise",
                          "List your treatment services expertise",
                          "textarea",
                          true,
                          <Star className="h-4 w-4" />,
                        )}

                        {/* New fields for Task 1 */}
                        {renderField(
                          "Personal Biography",
                          "personal_biography",
                          "Tell us about your personal background and interests",
                          "textarea",
                          false,
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                              clipRule="evenodd"
                            />
                          </svg>,
                        )}
                        {renderField(
                          "Work History",
                          "work_history",
                          "Describe your professional work experience and career progression",
                          "textarea",
                          false,
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h2zm4-1a1 1 0 00-1 1v1h2V6a1 1 0 00-1-1H10z"
                              clipRule="evenodd"
                            />
                          </svg>,
                        )}
                        {renderField(
                          "Timings",
                          "timings",
                          "Specify your availability and working hours (e.g., Mon-Fri 9AM-5PM)",
                          "textarea",
                          false,
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                              clipRule="evenodd"
                            />
                          </svg>,
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                <div className="flex gap-4 pt-4 border-t border-green-800/50">
                  {currentStage > 1 && (
                    <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                      <Button
                        type="button"
                        onClick={handlePrevStage}
                        disabled={isSubmitting}
                        className="px-6 py-2 bg-green-800 hover:bg-green-700 text-foreground border border-green-700"
                      >
                        <ChevronLeft className="h-5 w-5 mr-1" />
                        Back
                      </Button>
                    </motion.div>
                  )}

                  {currentStage < 3 ? (
                    <motion.div className="flex-1" whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                      <Button
                        type="button"
                        onClick={() => {
                          console.log("Next button clicked, current stage:", currentStage);
                          handleNextStage();
                        }}
                        disabled={isSubmitting}
                        className="px-6 py-2 w-full bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 text-foreground border border-green-700"
                      >
                        <span className="flex items-center justify-center gap-2 text-foreground text-base">
                          {isSubmitting ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Processing...
                            </>
                          ) : (
                            <>Next<ChevronRight className="h-5 w-5 ml-1" /></>
                          )}
                        </span>
                      </Button>
                    </motion.div>
                  ) : (
                    <motion.div className="flex-1" whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        className="w-full px-6 py-2 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 text-foreground border border-green-700"
                      >
                        <span className="flex items-center justify-center gap-2 text-foreground text-base">
                          {isSubmitting ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Processing...
                            </>
                          ) : (
                            <>Submit Registration</>
                          )}
                        </span>
                      </Button>
                    </motion.div>
                  )}
                </div>
              </form>
            </div>

            {/* Sports-themed footer with scoreboard styling */}
            <div className="bg-green-950 border-t border-green-700 p-2 flex justify-center">
              <div className="text-xs text-green-400 font-mono">DOCTORS LEAGUES • COMPETITOR REGISTRATION</div>
            </div>
          </div>
        </div>
      </DialogContent>
      {showConfirmation && (
        <RegistrationConfirmation
          open={showConfirmation}
          onOpenChange={(open) => {
            console.log("Confirmation dialog state changing:", open ? "showing" : "hiding");
            setShowConfirmation(open);
            
            // When confirmation is closed, redirect to home page to completely exit registration flow
            if (!open) {
              console.log("Confirmation closed - redirecting to home page");
              if (typeof window !== 'undefined') {
                setTimeout(() => {
                  window.location.href = '/';
                }, 100);
              }
              onOpenChange(false);
            }
          }}
          email={confirmedEmail}
          userType="doctor"
        />
      )}
    </Dialog>
    </>
  )
}
