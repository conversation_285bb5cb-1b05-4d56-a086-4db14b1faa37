"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON>, DialogContent } from "@/lib/mock-radix-dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { X, CheckCircle, User } from "lucide-react"
import { useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { MedicalSportsButton } from "../medical-sports-button"
import { MedicalSportsInput } from "../medical-sports-input"
import { MedicalSportsFrame } from "../medical-sports-frame"
import { useToast } from "@/components/ui/use-toast"
import { createClient } from "@supabase/supabase-js"
import { supabase } from "@/lib/supabase-client"
import { createPatientProfile } from "@/app/actions/register-user"

// Types for form data
interface PatientFormData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  age: string;
  gender: string;
  phoneNumber: string;
  medicalCondition: string;
  city: string;
  state: string;
  country: string;
  countryId: string;
}

export function PatientRegistrationDialog({
  open,
  onOpenChange,
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
}) {
  // Form state
  const [phase, setPhase] = useState(1)
  const [formData, setFormData] = useState<PatientFormData>({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    firstName: "",
    lastName: "",
    age: "",
    gender: "",
    phoneNumber: "",
    medicalCondition: "",
    city: "",
    state: "",
    country: "",
    countryId: ""
  })
  
  // UI state
  const [passwordStrength, setPasswordStrength] = useState(0)
  const [passwordMessage, setPasswordMessage] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [registrationComplete, setRegistrationComplete] = useState(false)
  
  // Add new state for username validation
  const [usernameValidationStatus, setUsernameValidationStatus] = useState<'idle' | 'checking' | 'available' | 'taken'>('idle');
  const usernameCheckTimer = useRef<NodeJS.Timeout | null>(null);
  
  // Add new state for email validation
  const [emailValidationStatus, setEmailValidationStatus] = useState<'idle' | 'checking' | 'available' | 'taken'>('idle');
  const emailCheckTimer = useRef<NodeJS.Timeout | null>(null);
  
  // Add state for countries
  const [countries, setCountries] = useState<{country_id: string, country_name: string}[]>([]);
  const [loadingCountries, setLoadingCountries] = useState(false);
  
  const router = useRouter()
  const { toast } = useToast()

  // Fetch countries from database when component mounts
  useEffect(() => {
    fetchCountries();
  }, []);
  
  // Function to fetch countries
  const fetchCountries = async () => {
    setLoadingCountries(true);
    try {
      const { data, error } = await supabase
        .from('countries')
        .select('country_id, country_name')
        .order('country_name');
        
      if (error) {
        console.error("Error fetching countries:", error);
        toast({
          title: "Error",
          description: "Failed to load countries. Please try again.",
          variant: "destructive"
        });
      } else if (data) {
        console.log("Loaded countries:", data.length);
        setCountries(data);
      }
    } catch (error) {
      console.error("Exception fetching countries:", error);
    } finally {
      setLoadingCountries(false);
    }
  };

  // Debounced username check function
  const debouncedCheckUsername = (username: string) => {
    // Clear any existing timer
    if (usernameCheckTimer.current) {
      clearTimeout(usernameCheckTimer.current);
    }
    
    // Don't check empty or too short usernames
    if (!username || username.length < 3) {
      setUsernameValidationStatus('idle');
      return;
    }
    
    // Set status to checking to show loading indicator
    setUsernameValidationStatus('checking');
    
    // Set a new timer
    usernameCheckTimer.current = setTimeout(async () => {
      try {
        const isAvailable = await checkUsernameAvailability(username);
        setUsernameValidationStatus(isAvailable ? 'available' : 'taken');
      } catch (error) {
        console.error("Error checking username:", error);
        setUsernameValidationStatus('idle');
      }
    }, 500); // 500ms debounce time
  };

  // Modify updateFormData to trigger email and username checks
  const updateFormData = (field: keyof PatientFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Trigger username check if the field is username
    if (field === 'username') {
      debouncedCheckUsername(value);
    }
    
    // Trigger email check if the field is email
    if (field === 'email') {
      debouncedCheckEmail(value);
    }
  };

  // Modify the checkUsernameAvailability function to use the supabase client
  const checkUsernameAvailability = async (username: string) => {
    try {
      // Check if username exists in users table - this should always exist
      const { data: existingUsers, error: userError } = await supabase
        .from('users')
        .select('username')
        .eq('username', username)
        .limit(1);
      
      if (userError) {
        console.error("Error checking username availability:", userError);
        return true; // Allow submission if we can't check (fail open)
      }
      
      // If a username is found in users table, it's taken
      if (existingUsers?.length) {
        return false;
      }
      
      // Try checking doctors table, but handle case where it might not exist
      try {
        const { data: existingDoctors } = await supabase
          .from('doctors')
          .select('username')
          .eq('username', username)
          .limit(1);
          
        // If doctors with this username are found, it's taken
        if (existingDoctors?.length) {
          return false;
        }
      } catch (doctorTableError) {
        // Just log but continue - the doctors table might not exist yet
        console.log("Note: Could not check doctors table - may not exist yet");
      }
      
      // Username is available if we got here
      return true;
    } catch (error) {
      console.error("Exception checking username:", error);
      return true; // Allow submission if we can't check (fail open)
    }
  };

  // Debounced email check function
  const debouncedCheckEmail = (email: string) => {
    // Clear any existing timer
    if (emailCheckTimer.current) {
      clearTimeout(emailCheckTimer.current);
    }
    
    // Don't check empty or invalid emails
    if (!email || !email.includes('@')) {
      setEmailValidationStatus('idle');
      return;
    }
    
    // Set status to checking to show loading indicator
    setEmailValidationStatus('checking');
    
    // Set a new timer
    emailCheckTimer.current = setTimeout(async () => {
      try {
        const isAvailable = await checkEmailAvailability(email);
        setEmailValidationStatus(isAvailable ? 'available' : 'taken');
      } catch (error) {
        console.error("Error checking email:", error);
        setEmailValidationStatus('idle');
      }
    }, 500); // 500ms debounce time
  };

  // Update the checkEmailAvailability function to use the supabase client
  const checkEmailAvailability = async (email: string) => {
    try {
      console.log(`Checking email availability for: ${email}`);
      
      // Check users table for this email
      const { data: existingUsers, error: userError } = await supabase
        .from('users')
        .select('email')
        .eq('email', email)
        .limit(1);
      
      if (userError) {
        console.error("Error checking users table:", userError);
      } else if (existingUsers?.length > 0) {
        console.log(`Email ${email} found in users table`);
        return false; // Email is taken
      }
      
      // Check doctors table
      const { data: existingDoctors, error: doctorError } = await supabase
        .from('doctors')
        .select('email')
        .eq('email', email)
        .limit(1);
      
      if (doctorError) {
        console.error("Error checking doctors table:", doctorError);
      } else if (existingDoctors?.length > 0) {
        console.log(`Email ${email} found in doctors table`);
        return false; // Email is taken
      }
      
      return true; // Email is available
    } catch (error) {
      console.error("Exception checking email availability:", error);
      return true; // Allow submission if we can't check (fail open)
    }
  };

  // Password strength checker
  const checkPasswordStrength = (password: string) => {
    let strength = 0;
    let message = "";

    if (password.length >= 8) strength += 1;
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[a-z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;

    switch (strength) {
      case 0:
      case 1:
        message = "Very Weak";
        break;
      case 2:
        message = "Weak";
        break;
      case 3:
        message = "Medium";
        break;
      case 4:
        message = "Strong";
        break;
      case 5:
        message = "Very Strong";
        break;
      default:
        message = "";
    }

    setPasswordStrength(strength);
    setPasswordMessage(message);
    return strength;
  };

  // Update password strength when password changes
  useEffect(() => {
    if (formData.password) {
      checkPasswordStrength(formData.password);
    } else {
      setPasswordStrength(0);
      setPasswordMessage("");
    }
  }, [formData.password]);

  // Validation functions
  const validatePhase1 = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.email) newErrors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = "Email is invalid";
    else if (emailValidationStatus === 'taken') {
      newErrors.email = "This email is already registered. Please use a different email address.";
    }

    if (!formData.password) newErrors.password = "Password is required";
    else if (formData.password.length < 8) newErrors.password = "Password must be at least 8 characters";
    else if (checkPasswordStrength(formData.password) < 3) newErrors.password = "Password is too weak";

    if (!formData.confirmPassword) newErrors.confirmPassword = "Please confirm your password";
    else if (formData.password !== formData.confirmPassword) newErrors.confirmPassword = "Passwords do not match";

    if (!formData.username) {
      newErrors.username = "Username is required";
    } else if (usernameValidationStatus === 'taken') {
      newErrors.username = "This username is already taken. Please choose another.";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validatePhase2 = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName) newErrors.firstName = "First name is required";
    if (!formData.lastName) newErrors.lastName = "Last name is required";
    if (!formData.age) newErrors.age = "Age is required";
    if (!formData.gender) newErrors.gender = "Gender is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validatePhase3 = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.city) newErrors.city = "City is required";
    if (!formData.countryId) newErrors.country = "Please select a country";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Navigation handlers
  const nextPhase = () => {
    // Determine which validation to run based on current phase
    let isValid = false;
    if (phase === 1) isValid = validatePhase1();
    else if (phase === 2) isValid = validatePhase2();
    else return;

    if (isValid) {
      setPhase(phase + 1);
    }
  };

  const prevPhase = () => {
    if (phase > 1) {
      setPhase(phase - 1);
    }
  };

  // Registration handler
  const handleSubmit = async () => {
    if (!validatePhase3()) return;
    
    setIsLoading(true);
    
    try {
      // First approach: Try using the custom API endpoint directly
      try {
        // Prepare data for the custom API endpoint
        const apiData = {
          email: formData.email,
          password: formData.password, // Actual password from the form
          userType: "patient",
          profileData: {
            username: formData.username,
            firstName: formData.firstName,
            lastName: formData.lastName,
            gender: formData.gender,
            age: formData.age ? parseInt(formData.age) : 0,
            city: formData.city,
            country: formData.country,
            countryId: formData.countryId,
            medical_condition: formData.medicalCondition,
            phone_number: formData.phoneNumber,
            state_province_region: formData.state,
          }
        };
        
        console.log("Attempting direct API endpoint registration...");
        const response = await fetch('/api/auth/custom/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(apiData),
        });
        
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            console.log("Registration successful via API endpoint");
            setRegistrationComplete(true);
            return; // Exit early since registration was successful
          } else {
            console.warn("API endpoint registration failed:", result.error);
            throw new Error(result.error || "Registration failed");
          }
        } else {
          console.error("API response was not OK:", response.status);
          throw new Error(`API Error: ${response.status}`);
        }
      } catch (apiError) {
        console.warn("Error using API endpoint, falling back to server action:", apiError);
        // Fall back to using server action if API endpoint fails
      }
      
      // Fallback to the server action approach
      // Generate a mock auth_id for demonstration
      const mockAuthId = `auth_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Prepare data for submission according to the PatientProfileData interface
      const patientData = {
        username: formData.username,
        email: formData.email,
        password: formData.password, // Include password
        firstName: formData.firstName,
        lastName: formData.lastName,
        age: formData.age ? parseInt(formData.age) : 0,
        gender: formData.gender,
        phoneNumber: formData.phoneNumber || "",
        medicalCondition: formData.medicalCondition || "",
        city: formData.city,
        state: formData.state || "",
        country: formData.country,
        // Required fields from the interface
        user_type: "patient" as const,
        auth_id: mockAuthId,
        registrationDate: new Date().toISOString()
      };
      
      // If we have countryId, include it (this would need to be handled in the server action)
      if (formData.countryId) {
        // @ts-ignore - country_id is not in the interface but we'll pass it anyway
        patientData.country_id = formData.countryId;
      }
      
      console.log("Submitting patient registration through server action", patientData);
      
      // Create the patient profile
      const { success, error } = await createPatientProfile(patientData);
      
      if (success) {
        setRegistrationComplete(true);
      } else {
        toast({
          title: "Registration Failed",
          description: error || "An unexpected error occurred. Please try again.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Registration error:", error);
      toast({
        title: "Registration Error",
        description: error instanceof Error ? error.message : "An unexpected error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Light theme styles for patient registration - only apply to light theme
  const lightThemeStyles = `
    /* Light Theme: Patient Registration Dialog */
    html:not(.dark) [data-patient-dialog="true"] {
      background: linear-gradient(135deg, hsl(210, 100%, 95%) 0%, hsl(210, 100%, 90%) 100%) !important;
    }
    
    html:not(.dark) [data-patient-form="true"],
    html:not(.dark) [data-patient="true"] {
      background: linear-gradient(135deg, hsl(210, 100%, 95%) 0%, hsl(210, 100%, 90%) 100%) !important;
    }
    
    html:not(.dark) [data-patient-form="true"] *,
    html:not(.dark) [data-patient="true"] * {
      color: hsl(210, 80%, 25%) !important;
    }
    
    html:not(.dark) [data-patient-form="true"] h1,
    html:not(.dark) [data-patient-form="true"] h2,
    html:not(.dark) [data-patient-form="true"] h3,
    html:not(.dark) [data-patient="true"] h1,
    html:not(.dark) [data-patient="true"] h2,
    html:not(.dark) [data-patient="true"] h3 {
      color: hsl(210, 90%, 20%) !important;
    }
    
    html:not(.dark) .patient-registration-dialog {
      background: linear-gradient(135deg, hsl(210, 100%, 95%) 0%, hsl(210, 100%, 90%) 100%) !important;
    }
    
    /* Remove any green elements from patient forms */
    html:not(.dark) [data-patient-form="true"] .bg-blue-600,
    html:not(.dark) [data-patient-form="true"] .bg-green-500,
    html:not(.dark) [data-patient-form="true"] .text-green-500 {
      background: hsl(210, 80%, 60%) !important;
      color: hsl(210, 90%, 95%) !important;
    }
  `

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: lightThemeStyles }} />
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent 
          className="sm:max-w-4xl p-0 border-0 bg-transparent patient-registration-dialog"
          data-patient-form="true"
          data-patient-dialog="true"
        >
        <MedicalSportsFrame 
          className="p-4 w-full min-w-[450px] flex flex-col overflow-hidden" 
          variant="patient"
          data-patient="true"
        >
          <div className="flex justify-between items-center w-full mb-4">
            <h2 className="text-xl font-bold text-foreground" data-patient-form="true">REFEREE SIGN-UP</h2>
            <button
              type="button"
              className="absolute right-4 top-4 rounded-full bg-card p-2 text-foreground hover:bg-accent transition-colors z-50"
              onClick={() => onOpenChange(false)}
              data-patient="true"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </button>
          </div>
          
          {!registrationComplete ? (
            <>
              {/* Progress indicators */}
              <div className="flex justify-center gap-3 mt-2 mb-4">
                {[1, 2, 3].map((p) => (
                  <div
                    key={p}
                    className={cn(
                      "w-8 h-8 rounded-full flex items-center justify-center text-xs shadow-sm",
                      phase === p
                        ? "bg-white text-blue-600 font-medium" 
                        : "bg-blue-600/20 text-foreground",
                    )}
                  >
                    {p}
                  </div>
                ))}
              </div>
              
              {/* Scrollable Content */}
              <div className="overflow-y-auto flex-grow mb-4 max-h-[350px] min-h-[350px] px-4 pr-6">
                {/* Existing phase content... */}
                {phase === 1 && (
                  <div className="space-y-6 px-2">
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-foreground text-sm font-medium">Email</Label>
                      <div className="relative">
                        <MedicalSportsInput
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => updateFormData("email", e.target.value)}
                          error={errors.email}
                          variant="patient"
                          className={cn("w-full", emailValidationStatus === 'available' ? "pr-10 border-green-500" : "")}
                        />
                        
                        {/* Email validation indicator */}
                        {formData.email && formData.email.includes('@') && (
                          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                            {emailValidationStatus === 'checking' && (
                              <svg className="animate-spin h-5 w-5 text-muted-green" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                            )}
                            
                            {emailValidationStatus === 'available' && (
                              <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            )}
                            
                            {emailValidationStatus === 'taken' && (
                              <svg className="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            )}
                          </div>
                        )}
                      </div>
                      
                      {emailValidationStatus === 'taken' && !errors.email && (
                        <p className="text-red-500 text-xs mt-1">This email is already registered</p>
                      )}
                      
                      {emailValidationStatus === 'available' && (
                        <p className="text-green-500 text-xs mt-1">Email is available</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="username" className="text-foreground text-sm font-medium">Username</Label>
                      <div className="relative">
                        <MedicalSportsInput
                          id="username"
                          value={formData.username}
                          onChange={(e) => updateFormData("username", e.target.value)}
                          error={errors.username}
                          variant="patient"
                          className={cn("w-full", usernameValidationStatus === 'available' ? "pr-10 border-green-500" : "")}
                        />
                        
                        {/* Username validation indicator */}
                        {formData.username && formData.username.length >= 3 && (
                          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                            {usernameValidationStatus === 'checking' && (
                              <svg className="animate-spin h-5 w-5 text-muted-green" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                            )}
                            
                            {usernameValidationStatus === 'available' && (
                              <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            )}
                            
                            {usernameValidationStatus === 'taken' && (
                              <svg className="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            )}
                          </div>
                        )}
                      </div>
                      
                      {usernameValidationStatus === 'taken' && !errors.username && (
                        <p className="text-red-500 text-xs mt-1">This username is already taken</p>
                      )}
                      
                      {usernameValidationStatus === 'available' && (
                        <p className="text-green-500 text-xs mt-1">Username is available</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="password" className="text-foreground text-sm font-medium">Password</Label>
                      <MedicalSportsInput
                        id="password"
                        type="password"
                        value={formData.password}
                        onChange={(e) => updateFormData("password", e.target.value)}
                        error={errors.password}
                        variant="patient"
                        className="w-full"
                      />
                      {formData.password && (
                        <div className="mt-2">
                          <div className="flex space-x-1 mb-1">
                            {[1, 2, 3, 4, 5].map((level) => (
                              <div
                                key={level}
                                className={`h-2 w-full rounded-full ${
                                  passwordStrength >= level
                                    ? passwordStrength === 1
                                      ? "bg-red-500"
                                      : passwordStrength === 2
                                        ? "bg-orange-500"
                                        : passwordStrength === 3
                                          ? "bg-yellow-500"
                                          : passwordStrength === 4
                                            ? "bg-green-500"
                                            : "bg-emerald-500"
                                    : "bg-green-300"
                                }`}
                              />
                            ))}
                          </div>
                          <p
                            className={`text-xs ${
                              passwordStrength <= 1
                                ? "text-red-500"
                                : passwordStrength === 2
                                  ? "text-orange-500"
                                  : passwordStrength === 3
                                    ? "text-yellow-500"
                                    : "text-green-500"
                            }`}
                          >
                            {passwordMessage}
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="confirmPassword" className="text-foreground text-sm font-medium">Confirm Password</Label>
                      <MedicalSportsInput
                        id="confirmPassword"
                        type="password"
                        value={formData.confirmPassword}
                        onChange={(e) => updateFormData("confirmPassword", e.target.value)}
                        error={errors.confirmPassword}
                        variant="patient"
                        className="w-full"
                      />
                    </div>
                  </div>
                )}

                {phase === 2 && (
                  <div className="space-y-6 px-2">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="firstName" className="text-foreground text-sm font-medium">First Name</Label>
                        <MedicalSportsInput
                          id="firstName"
                          value={formData.firstName}
                          onChange={(e) => updateFormData("firstName", e.target.value)}
                          error={errors.firstName}
                          variant="patient"
                          className="w-full"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="lastName" className="text-foreground text-sm font-medium">Last Name</Label>
                        <MedicalSportsInput
                          id="lastName"
                          value={formData.lastName}
                          onChange={(e) => updateFormData("lastName", e.target.value)}
                          error={errors.lastName}
                          variant="patient"
                          className="w-full"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="age" className="text-foreground text-sm font-medium">Age *</Label>
                      <MedicalSportsInput
                        id="age"
                        type="number"
                        value={formData.age}
                        onChange={(e) => updateFormData("age", e.target.value)}
                        error={errors.age}
                        variant="patient"
                        className="w-full"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="gender" className="text-foreground text-sm font-medium">Gender *</Label>
                      <div className="flex space-x-4 mt-1">
                        <div 
                          className={`flex-1 p-3 rounded-md cursor-pointer transition-all ${
                            formData.gender === "male" 
                              ? "bg-blue-600 text-foreground border-2 border-white" 
                              : "bg-card text-foreground border border-border hover:bg-accent"
                          }`}
                          onClick={() => updateFormData("gender", "male")}
                        >
                          <div className="flex items-center justify-center">
                            <span className="text-center">Male</span>
                          </div>
                        </div>
                        <div 
                          className={`flex-1 p-3 rounded-md cursor-pointer transition-all ${
                            formData.gender === "female" 
                              ? "bg-blue-600 text-foreground border-2 border-white" 
                              : "bg-card text-foreground border border-border hover:bg-accent"
                          }`}
                          onClick={() => updateFormData("gender", "female")}
                        >
                          <div className="flex items-center justify-center">
                            <span className="text-center">Female</span>
                          </div>
                        </div>
                      </div>
                      {errors.gender && <p className="text-red-500 text-xs mt-1">{errors.gender}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="phoneNumber" className="text-foreground text-sm font-medium">Phone Number <span className="text-xs text-foreground/60">(optional)</span></Label>
                      <MedicalSportsInput
                        id="phoneNumber"
                        value={formData.phoneNumber}
                        onChange={(e) => updateFormData("phoneNumber", e.target.value)}
                        error={errors.phoneNumber}
                        variant="patient"
                        className="w-full"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="medicalCondition" className="text-foreground text-sm font-medium">Medical Condition <span className="text-xs text-foreground/60">(optional)</span></Label>
                      <MedicalSportsInput
                        id="medicalCondition"
                        value={formData.medicalCondition}
                        onChange={(e) => updateFormData("medicalCondition", e.target.value)}
                        placeholder="Describe your medical condition"
                        className="w-full"
                        variant="patient"
                      />
                    </div>
                  </div>
                )}

                {phase === 3 && (
                  <div className="space-y-6 px-2">
                    <div className="space-y-2">
                      <Label htmlFor="city" className="text-foreground text-sm font-medium">City *</Label>
                      <MedicalSportsInput
                        id="city"
                        value={formData.city}
                        onChange={(e) => updateFormData("city", e.target.value)}
                        error={errors.city}
                        variant="patient"
                        className="w-full"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="state" className="text-foreground text-sm font-medium">State/Province</Label>
                      <MedicalSportsInput
                        id="state"
                        value={formData.state}
                        onChange={(e) => updateFormData("state", e.target.value)}
                        variant="patient"
                        className="w-full"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="country" className="text-foreground text-sm font-medium">Country *</Label>
                      <div className="relative">
                        {loadingCountries ? (
                          <div className="bg-card border border-border rounded-md p-2.5 text-foreground">
                            <div className="flex items-center">
                              <svg className="animate-spin h-4 w-4 text-foreground mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              <span>Loading countries...</span>
                            </div>
                          </div>
                        ) : (
                          <select
                            id="country"
                            value={formData.countryId}
                            onChange={(e) => {
                              const selectedCountry = countries.find(country => country.country_id === e.target.value);
                              updateFormData("countryId", e.target.value);
                              updateFormData("country", selectedCountry ? selectedCountry.country_name : "");
                            }}
                            className={`w-full bg-card border ${errors.country ? 'border-red-500' : 'border-border'} text-foreground rounded-md p-2.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500`}
                          >
                            <option value="" className="bg-background/90">Select a country</option>
                            {countries.map(country => (
                              <option 
                                key={country.country_id} 
                                value={country.country_id}
                                className="bg-background/90"
                              >
                                {country.country_name}
                              </option>
                            ))}
                          </select>
                        )}
                      </div>
                      {errors.country && <p className="text-red-500 text-xs mt-1">{errors.country}</p>}
                    </div>
                  </div>
                )}
              </div>

              {/* Navigation Buttons */}
              <div className="flex items-center justify-between mt-auto pt-4 px-4">
                {phase === 1 && (
                  <div /> /* Placeholder to keep Next button on the right for phase 1 */
                )}
                {phase > 1 && (
                  <MedicalSportsButton onClick={prevPhase} variant="default" className="py-2">
                    Previous
                  </MedicalSportsButton>
                )}
                {phase < 3 ? (
                  <MedicalSportsButton onClick={nextPhase} variant="patient" className="py-2">
                    Next
                  </MedicalSportsButton>
                ) : (
                  <MedicalSportsButton 
                    onClick={handleSubmit} 
                    isLoading={isLoading} 
                    variant="patient" 
                    className="py-2"
                    disabled={isLoading} // Disable while loading
                  >
                    {isLoading ? "Submitting..." : "Submit Registration"}
                  </MedicalSportsButton>
                )}
              </div>
            </>
          ) : (
            <div className="flex-grow flex items-center justify-center">
              <div className="bg-blue-600/10 p-8 rounded-lg max-w-md w-full">
                <div className="w-20 h-20 bg-blue-600/20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <User className="h-10 w-10 text-blue-500" />
                </div>
                <h3 className="text-2xl font-bold text-foreground mb-4 text-center">Registration Complete!</h3>
                <p className="text-foreground text-center mb-3">
                  Thank you for registering! Your account has been created.
                </p>
                <p className="text-foreground text-center mb-3">
                  We've sent a verification email to <strong>{formData.email}</strong>
                </p>
                <p className="text-foreground text-center mb-6">
                  Please check your inbox (including spam folder) and click the verification link to activate your account.
                </p>
              </div>
            </div>
          )}
          
          {/* Footer */}
          <div className="flex-none text-center mt-4 mb-2">
            <p className="text-foreground text-xs font-medium">DOCTORS LEAGUES</p>
          </div>
        </MedicalSportsFrame>
      </DialogContent>
    </Dialog>
    </>
  )
}

