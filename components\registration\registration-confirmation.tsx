"use client"

import { 
  <PERSON><PERSON>, 
  <PERSON>alog<PERSON>ontent, 
  DialogTitle,
  DialogDescription 
} from "@/lib/mock-radix-dialog"
import { But<PERSON> } from "@/components/ui/button"
import { CheckCircle, Mail } from "lucide-react"
import { motion } from "framer-motion"
import { MedicalFrame } from "../medical-frame"

interface RegistrationConfirmationProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  email: string
  userType: "patient" | "doctor"
}

export function RegistrationConfirmation({ open, onOpenChange, email, userType }: RegistrationConfirmationProps) {
  const iconColor = userType === "patient" ? "text-blue-500" : "text-primary"
  const buttonGradient =
    userType === "patient"
      ? "from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
      : "from-primary to-primary/80 hover:from-primary/90 hover:to-primary"

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        className="sm:max-w-2xl p-0 border-0 bg-transparent"
        aria-describedby="registration-complete-description"
      >
        <DialogTitle className="sr-only">Registration Successful</DialogTitle>
        <DialogDescription id="registration-complete-description" className="sr-only">
          Your registration is complete. Please verify your email to activate your account.
        </DialogDescription>
        
        <MedicalFrame variant={userType}>
          <div className="p-6 space-y-6 text-center">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <CheckCircle className={`w-16 h-16 ${iconColor} mx-auto`} />
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <h2 className="text-2xl font-bold text-foreground">Registration Successful!</h2>
              <p className="text-foreground/80 mt-2">
                Thank you for joining Doctor's Leagues! We're excited to have you on board.
              </p>
            </motion.div>

            <motion.div
              className="bg-background/30 p-4 rounded-lg border border-border/50"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <div className="flex items-center justify-center gap-3 mb-2">
                <Mail className={iconColor} />
                <h3 className="text-lg font-semibold text-foreground">Verify Your Email</h3>
              </div>
              <p className="text-foreground/70 text-sm">
                We've sent a confirmation email to:
              </p>
              {email ? (
                <p className="font-medium text-base text-foreground mt-2 mb-2 px-2 py-1 bg-card rounded-md inline-block">
                  {email}
                </p>
              ) : (
                <p className="font-medium text-base text-foreground mt-2 mb-2">
                  your registered email address
                </p>
              )}
              <p className="text-foreground/70 text-sm mt-3">
                Please check your inbox and click the verification link to activate your account.
              </p>
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <Button
                onClick={() => {
                  console.log("Got it button clicked - closing confirmation dialog");
                  onOpenChange(false);
                  setTimeout(() => {
                    if (typeof window !== 'undefined') {
                      window.location.href = '/';
                    }
                  }, 300);
                }}
                className={`w-full bg-gradient-to-r ${buttonGradient} text-foreground py-2 rounded-lg font-semibold`}
              >
                Got it
              </Button>
            </motion.div>
          </div>
        </MedicalFrame>
      </DialogContent>
    </Dialog>
  )
}

