"use client"

import { useState } from "react"
import { Dialog, DialogContent } from "@/lib/mock-radix-dialog"
import { But<PERSON> } from "@/components/ui/button"
import { X, CheckCircle, Trophy } from "lucide-react"
import { cn } from "@/lib/utils"
import { MedicalSportsButton } from "../medical-sports-button"
import { useRouter } from "next/navigation"

export function SpectatorRegistrationDialog({
  open,
  onOpenChange,
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
}) {
  const router = useRouter()
  
  // UI states
  const [leaderboardsVisible, setLeaderboardsVisible] = useState(true)
  const [liveMatchesVisible, setLiveMatchesVisible] = useState(true)
  const [favoritesVisible, setFavoritesVisible] = useState(true)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl p-0 border-0 bg-transparent">
        <div className="bg-blue-900 rounded-xl border border-blue-700 overflow-hidden">
          {/* Header with title and close button */}
          <div className="flex items-center justify-between p-4 pb-2 border-b border-blue-800">
            <div className="flex items-center gap-3">
              <Trophy className="h-6 w-6 text-yellow-300" />
              <h2 className="text-xl font-bold text-foreground">Join as Referee</h2>
            </div>
            <button
              onClick={() => onOpenChange(false)}
              className="rounded-full p-1.5 bg-blue-800 text-foreground hover:bg-blue-700 transition-colors"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </button>
          </div>
          
          {/* Main content */}
          <div className="p-6 space-y-6">
            {/* Introduction */}
            <div className="bg-blue-950 p-4 rounded-md border border-blue-800 flex items-start gap-3">
              <div className="flex-shrink-0 text-blue-300">
                <CheckCircle className="h-6 w-6" />
              </div>
              <p className="text-foreground">
                Create your account to follow your favorite doctors and track their performance in the medical leagues.
              </p>
            </div>
            
            {/* Features */}
            <div className="grid grid-cols-2 gap-4">
              {/* Leaderboards */}
              <div 
                className={cn(
                  "bg-blue-800/50 p-6 rounded-md border border-blue-700 flex flex-col items-center space-y-4 cursor-pointer hover:bg-blue-800 transition-colors",
                  leaderboardsVisible && "ring-2 ring-blue-400"
                )}
                onClick={() => setLeaderboardsVisible(!leaderboardsVisible)}
              >
                <div className="p-2 bg-blue-700 rounded-full">
                  <Trophy className="h-6 w-6 text-yellow-300" />
                </div>
                <div className="text-center">
                  <h3 className="font-semibold text-foreground mb-1">Leaderboards</h3>
                  <p className="text-sm text-blue-100">Track top performers in each specialty</p>
                </div>
              </div>
              
              {/* Live Matches */}
              <div 
                className={cn(
                  "bg-blue-800/50 p-6 rounded-md border border-blue-700 flex flex-col items-center space-y-4 cursor-pointer hover:bg-blue-800 transition-colors",
                  liveMatchesVisible && "ring-2 ring-blue-400"
                )}
                onClick={() => setLiveMatchesVisible(!liveMatchesVisible)}
              >
                <div className="p-2 bg-blue-700 rounded-full">
                  <svg className="h-6 w-6 text-red-400" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 12H5M5.5 14.5L7 13M5.5 9.5L7 11M12 3V5M18.5 9.5L17 11M18.5 14.5L17 13M21 12H19M16 8L14 10M8 8L10 10M12 12L12 16M12 12L15 9M12 12L9 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <div className="text-center">
                  <h3 className="font-semibold text-foreground mb-1">Live Matches</h3>
                  <p className="text-sm text-blue-100">Follow ongoing medical competitions</p>
                </div>
              </div>
            </div>
            
            {/* Support your favorites section */}
            <div 
              className={cn(
                "bg-blue-800/50 p-6 rounded-md border border-blue-700 flex items-start gap-4 cursor-pointer hover:bg-blue-800 transition-colors",
                favoritesVisible && "ring-2 ring-blue-400"
              )}
              onClick={() => setFavoritesVisible(!favoritesVisible)}
            >
              <div className="flex-shrink-0 p-2 bg-blue-700 rounded-full">
                <svg className="h-6 w-6 text-red-400" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" fill="currentColor"/>
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-foreground mb-1">Support Your Favorites</h3>
                <p className="text-sm text-blue-100">Rate and review doctors to help them climb the rankings</p>
              </div>
            </div>
            
            {/* Register button */}
            <MedicalSportsButton
              variant="patient"
              className="w-full py-3 text-lg font-semibold flex items-center justify-center gap-2"
              onClick={() => router.push("/auth/register?role=patient")}
            >
              <Trophy className="h-5 w-5" />
              Register as Referee
            </MedicalSportsButton>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 