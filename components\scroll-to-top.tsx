"use client"

import { useEffect } from "react"

/**
 * Component that scrolls the page to the top when mounted
 * Use this in layout or page components to ensure the page starts from the top
 */
export default function ScrollToTop() {
  useEffect(() => {
    // Scroll to top on component mount
    window.scrollTo(0, 0)
    
    // Also try to reset any scroll position in the history state
    if (history.scrollRestoration) {
      history.scrollRestoration = 'manual'
    }
  }, [])

  // This component doesn't render anything
  return null
} 