import { Fragment } from 'react'

interface CountryVariant {
  country: string;  // Country code (e.g., 'us', 'sa', 'ae')
  language: string; // Language code (e.g., 'en', 'ar')
  url: string;      // Full URL for this country/language variant
}

interface CountryMetadataProps {
  variants: CountryVariant[];
  currentCountry: string;
  currentLanguage: string;
  baseUrl?: string;
}

/**
 * Generates hreflang tags for international SEO
 * 
 * This component improves SEO by telling search engines which URLs to show 
 * to users in different countries and languages.
 */
export function CountryMetadata({
  variants,
  currentCountry,
  currentLanguage,
  baseUrl = 'https://doctorsleagues.com'
}: CountryMetadataProps) {
  // Default variant (for users outside target countries)
  const hasDefaultVariant = variants.some(v => v.country === 'x-default');
  
  return (
    <Fragment>
      {/* Current canonical URL */}
      <link 
        rel="canonical" 
        href={`${baseUrl}${variants.find(v => 
          v.country === currentCountry && 
          v.language === currentLanguage
        )?.url || '/'}`} 
      />
      
      {/* Add hreflang tags for each country/language variant */}
      {variants.map((variant) => (
        <link 
          key={`${variant.language}-${variant.country}`}
          rel="alternate" 
          hrefLang={variant.country === 'x-default' 
            ? 'x-default' 
            : `${variant.language}-${variant.country}`
          } 
          href={`${baseUrl}${variant.url}`}
        />
      ))}
      
      {/* Add a default hreflang if not explicitly provided */}
      {!hasDefaultVariant && (
        <link 
          rel="alternate" 
          hrefLang="x-default" 
          href={baseUrl} 
        />
      )}
    </Fragment>
  )
}

/**
 * Generates country-specific metadata for a doctor profile page
 */
export function DoctorCountryMetadata({
  doctorId,
  countries,
  currentCountry,
  currentLanguage = 'en',
  baseUrl = 'https://doctorsleagues.com'
}: {
  doctorId: string;
  countries: string[];
  currentCountry: string;
  currentLanguage?: string;
  baseUrl?: string;
}) {
  const variants = countries.map(country => ({
    country,
    language: country === 'sa' || country === 'ae' || country === 'qa' ? 'ar' : 'en',
    url: `/doctors/${doctorId}?country=${country}`
  }));
  
  // Add default variant
  variants.push({
    country: 'x-default',
    language: 'en',
    url: `/doctors/${doctorId}`
  });
  
  return (
    <CountryMetadata 
      variants={variants}
      currentCountry={currentCountry}
      currentLanguage={currentLanguage}
      baseUrl={baseUrl}
    />
  )
}

/**
 * Generates country-specific metadata for a league/ranking page
 */
export function LeagueCountryMetadata({
  leagueSlug,
  countries,
  currentCountry,
  currentLanguage = 'en',
  baseUrl = 'https://doctorsleagues.com'
}: {
  leagueSlug: string;
  countries: string[];
  currentCountry: string;
  currentLanguage?: string;
  baseUrl?: string;
}) {
  const variants = countries.map(country => ({
    country,
    language: country === 'sa' || country === 'ae' || country === 'qa' ? 'ar' : 'en',
    url: `/leagues/${leagueSlug}?country=${country}`
  }));
  
  // Add default variant
  variants.push({
    country: 'x-default',
    language: 'en',
    url: `/leagues/${leagueSlug}`
  });
  
  return (
    <CountryMetadata 
      variants={variants}
      currentCountry={currentCountry}
      currentLanguage={currentLanguage}
      baseUrl={baseUrl}
    />
  )
} 