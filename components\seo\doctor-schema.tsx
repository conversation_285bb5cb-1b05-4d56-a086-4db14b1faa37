"use client"

import Script from 'next/script'

interface DoctorSchemaProps {
  doctor: {
    id: string
    name: string
    image?: string
    specialty: string
    description?: string
    hospital?: string
    awards?: string[]
    education?: string[]
    rating?: number
    reviewCount?: number
  }
}

/**
 * Component to add structured data for doctor profiles
 * Implements Schema.org Physician markup for better SEO and rich results in search
 */
export function DoctorSchema({ doctor }: DoctorSchemaProps) {
  // Create the schema.org structured data
  const doctorSchema = {
    '@context': 'https://schema.org',
    '@type': 'Physician',
    '@id': `https://doctorsleagues.com/doctor/${doctor.id}`,
    'name': doctor.name,
    'image': doctor.image || 'https://doctorsleagues.com/images/doctor-placeholder.jpg',
    'description': doctor.description || `${doctor.name} is a specialist in ${doctor.specialty}`,
    'medicalSpecialty': doctor.specialty,
    'memberOf': doctor.hospital ? {
      '@type': 'MedicalOrganization',
      'name': doctor.hospital
    } : undefined,
    'award': doctor.awards || [],
    'alumniOf': doctor.education || [],
    'review': doctor.reviewCount && doctor.rating ? {
      '@type': 'Review',
      'reviewRating': {
        '@type': 'Rating',
        'ratingValue': doctor.rating,
        'bestRating': '5'
      },
      'reviewCount': doctor.reviewCount
    } : undefined
  }

  return (
    <Script
      id={`doctor-schema-${doctor.id}`}
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(doctorSchema) }}
    />
  )
}

/**
 * Component to add structured data for doctor comparison lists
 */
export function DoctorComparisonSchema({ doctors }: { doctors: DoctorSchemaProps['doctor'][] }) {
  const comparisonSchema = {
    '@context': 'https://schema.org',
    '@type': 'ItemList',
    'itemListElement': doctors.map((doctor, index) => ({
      '@type': 'ListItem',
      'position': index + 1,
      'item': {
        '@type': 'Physician',
        '@id': `https://doctorsleagues.com/doctor/${doctor.id}`,
        'name': doctor.name,
        'medicalSpecialty': doctor.specialty,
        'memberOf': doctor.hospital ? {
          '@type': 'MedicalOrganization',
          'name': doctor.hospital
        } : undefined
      }
    }))
  }

  return (
    <Script
      id="doctor-comparison-schema"
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(comparisonSchema) }}
    />
  )
} 