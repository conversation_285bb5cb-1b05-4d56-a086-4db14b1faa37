"use client"

import Script from 'next/script'
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from "@/components/ui/accordion"

export interface FAQItem {
  question: string
  answer: string | React.ReactNode
}

interface FAQSchemaProps {
  items: FAQItem[]
  title?: string
  className?: string
}

/**
 * FAQ component with structured data for SEO
 * Implements schema.org FAQPage markup for rich results in search
 */
export function FAQSchema({ items, title, className = '' }: FAQSchemaProps) {
  const faqSchema = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    'mainEntity': items.map((item) => ({
      '@type': 'Question',
      'name': item.question,
      'acceptedAnswer': {
        '@type': 'Answer',
        // Convert to string if the answer is a React node
        'text': typeof item.answer === 'string' 
          ? item.answer 
          : 'Please visit our website for a detailed answer to this question.'
      }
    }))
  }

  return (
    <>
      <Script
        id="faq-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqSchema) }}
      />
      <div className={className}>
        {title && <h2 className="text-2xl font-bold mb-6">{title}</h2>}
        <Accordion type="single" collapsible className="w-full">
          {items.map((item, index) => (
            <AccordionItem key={index} value={`item-${index}`}>
              <AccordionTrigger className="text-left font-medium">
                {item.question}
              </AccordionTrigger>
              <AccordionContent>
                {typeof item.answer === 'string' ? (
                  <p>{item.answer}</p>
                ) : (
                  item.answer
                )}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </>
  )
}

/**
 * Medical-specific FAQ component with proper schema markup
 */
export function MedicalFAQSchema({ items, title, className = '' }: FAQSchemaProps) {
  const medicalFaqSchema = {
    '@context': 'https://schema.org',
    '@type': 'MedicalWebPage',
    'mainEntity': {
      '@type': 'FAQPage',
      'mainEntity': items.map((item) => ({
        '@type': 'Question',
        'name': item.question,
        'acceptedAnswer': {
          '@type': 'Answer',
          'text': typeof item.answer === 'string' 
            ? item.answer 
            : 'Please visit our website for a detailed answer to this question.'
        }
      }))
    },
    'specialty': 'Healthcare Professional Rankings'
  }

  return (
    <>
      <Script
        id="medical-faq-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(medicalFaqSchema) }}
      />
      <div className={className}>
        {title && <h2 className="text-2xl font-bold mb-6">{title}</h2>}
        <Accordion type="single" collapsible className="w-full">
          {items.map((item, index) => (
            <AccordionItem key={index} value={`item-${index}`}>
              <AccordionTrigger className="text-left font-medium">
                {item.question}
              </AccordionTrigger>
              <AccordionContent>
                {typeof item.answer === 'string' ? (
                  <p>{item.answer}</p>
                ) : (
                  item.answer
                )}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </>
  )
} 