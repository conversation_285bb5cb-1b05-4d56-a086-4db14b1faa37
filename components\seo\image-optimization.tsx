"use client"

import Image from 'next/image'
import { useState, useEffect } from 'react'

interface SEOImageProps {
  src: string
  alt: string
  width: number
  height: number
  className?: string
  priority?: boolean
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
  blurDataURL?: string
  lazyBoundary?: string
  loading?: "eager" | "lazy"
  sizes?: string
}

/**
 * SEO-optimized image component that implements lazy loading, WebP format, 
 * responsive sizing, and proper alt text for better search engine indexing.
 * Enhanced for Core Web Vitals optimization with improved CLS handling.
 */
export function SEOImage({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  objectFit = 'cover',
  blurDataURL,
  lazyBoundary = '200px',
  loading,
  sizes,
}: SEOImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [isVisible, setIsVisible] = useState(false)

  // Convert image to WebP format if it's not already (and it's a URL we can modify)
  const imageUrl = src.startsWith('http') && !src.includes('format=webp') && !src.endsWith('.webp') 
    ? `${src}${src.includes('?') ? '&' : '?'}format=webp`
    : src

  // Use Intersection Observer to determine when the image is in viewport
  useEffect(() => {
    // Skip for priority images, they load immediately
    if (priority) {
      setIsVisible(true)
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      {
        rootMargin: `${lazyBoundary}`,
        threshold: 0.01
      }
    )

    let element: HTMLElement | null = null;
    
    // Small delay to ensure the DOM is ready
    setTimeout(() => {
      element = document.querySelector(`[data-img-id="${src}"]`)
      if (element) {
        observer.observe(element)
      }
    }, 100)

    return () => {
      if (element) {
        observer.unobserve(element)
      }
      observer.disconnect()
    }
  }, [src, priority, lazyBoundary])

  // Default responsive sizes if not provided
  const defaultSizes = sizes || `(max-width: 640px) 100vw, (max-width: 768px) 80vw, (max-width: 1024px) 60vw, ${width}px`

  return (
    <div 
      className={`overflow-hidden relative ${isLoading ? 'animate-pulse bg-green-200' : ''}`}
      style={{ 
        aspectRatio: `${width}/${height}`,
        width: '100%',
        maxWidth: width + 'px',
      }}
      data-img-id={src}
    >
      {(isVisible || priority) && (
        <Image
          src={imageUrl}
          alt={alt}
          width={width}
          height={height}
          className={`transition-opacity duration-500 ${
            isLoading ? 'opacity-0' : 'opacity-100'
          } ${className}`}
          style={{
            objectFit,
            maxWidth: '100%',
            height: 'auto',
          }}
          priority={priority}
          quality={75} // Slightly reduced quality for better performance
          placeholder={blurDataURL ? 'blur' : 'empty'}
          blurDataURL={blurDataURL}
          onLoad={() => setIsLoading(false)}
          sizes={defaultSizes}
          loading={loading || (priority ? "eager" : "lazy")}
        />
      )}
    </div>
  )
}

/**
 * Special image component specifically for doctor profile images
 * with optimizations for Core Web Vitals
 */
export function DoctorProfileImage({
  src,
  alt,
  width = 300,
  height = 300,
  className = '',
  priority = false,
}: Omit<SEOImageProps, 'objectFit' | 'blurDataURL'>) {
  // Use a standardized blurDataURL for doctor images
  const blurDataURL = "data:image/jpeg;base64,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"

  return (
    <SEOImage 
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      priority={priority}
      objectFit="cover"
      blurDataURL={blurDataURL}
    />
  )
} 