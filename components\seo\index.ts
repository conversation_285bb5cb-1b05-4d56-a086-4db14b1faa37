/**
 * SEO Components Index
 * 
 * This file exports all SEO-related components and utilities for easy importing.
 */

// Schema markup components
export { DoctorSche<PERSON>, DoctorComparisonSchema } from './doctor-schema'
export { default as LocalBusinessSchema, DoctorsLeagueLocalBusiness } from './local-business'
export { FAQSchema, MedicalFAQSchema } from './faq-schema'
export { Breadcrumbs } from './breadcrumbs'
export { SEOImage, DoctorProfileImage } from './image-optimization'
export { SocialMetaTags, DoctorSocialMetaTags } from './social-meta-tags'

// Schema markup client component and generators
export { SchemaMarkup } from './schema-generator'

// Country-specific metadata with hreflang tags
export { 
  CountryMetadata, 
  DoctorCountryMetadata, 
  LeagueCountryMetadata 
} from './country-metadata'

// Schema generators from schema-utils library (preferred implementation)
export { 
  generateMedicalOrganizationSchema,
  generateDoctorSchema,
  generateFaqSchema,
  generateBreadcrumbSchema,
  generateMedicalWebPageSchema
} from '@/lib/schema-utils'

// Development tools
export { SEOHealthCheck } from './seo-health-check'

// Types
export type { FAQItem } from './faq-schema' 