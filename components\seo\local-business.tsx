"use client"

import Script from 'next/script'

interface Country {
  code: string
  name: string
}

interface Address {
  streetAddress: string
  addressLocality: string
  addressRegion?: string
  postalCode?: string
  addressCountry: string
}

interface LocalBusinessProps {
  name: string
  description: string
  image: string
  telephone: string
  email: string
  url: string
  address: Address
  priceRange: string
  openingHours?: string[]
  servedCountries?: Country[]
}

export default function LocalBusinessSchema({
  name,
  description,
  image,
  telephone,
  email,
  url,
  address,
  priceRange,
  openingHours = ["Mo-Fr 09:00-17:00"],
  servedCountries = [
    { code: "BH", name: "Bahrain" },
    { code: "SA", name: "Saudi Arabia" },
    { code: "AE", name: "United Arab Emirates" },
    { code: "KW", name: "Kuwait" },
    { code: "OM", name: "Oman" },
    { code: "QA", name: "Qatar" }
  ]
}: LocalBusinessProps) {
  // Create schema.org structured data
  const localBusinessSchema = {
    '@context': 'https://schema.org',
    '@type': 'MedicalBusiness',
    'name': name,
    'description': description,
    'image': image,
    'telephone': telephone,
    'email': email,
    'url': url,
    'address': {
      '@type': 'PostalAddress',
      'streetAddress': address.streetAddress,
      'addressLocality': address.addressLocality,
      'addressRegion': address.addressRegion,
      'postalCode': address.postalCode,
      'addressCountry': address.addressCountry
    },
    'openingHours': openingHours,
    'priceRange': priceRange,
    'areaServed': servedCountries.map(country => ({
      '@type': 'Country',
      'name': country.name
    }))
  }

  return (
    <Script
      id="local-business-schema"
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(localBusinessSchema) }}
    />
  )
}

export function DoctorsLeagueLocalBusiness() {
  return (
    <LocalBusinessSchema
      name="Doctor's Leagues"
      description="Where Healthcare Heroes Compete for Your Trust - Compare and rank medical professionals based on peer reviews, patient outcomes, and research contributions."
      image="https://doctorsleagues.com/logo.png"
      telephone="+973 1234 5678"
      email="<EMAIL>"
      url="https://doctorsleagues.com"
      address={{
        streetAddress: "123 League Street",
        addressLocality: "Medical City",
        addressRegion: "Manama",
        postalCode: "12345",
        addressCountry: "BH"
      }}
      priceRange="Free"
      openingHours={["Mo-Fr 09:00-17:00"]}
    />
  )
} 