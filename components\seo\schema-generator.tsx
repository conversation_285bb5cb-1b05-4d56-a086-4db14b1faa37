"use client"

import Script from 'next/script'

interface SchemaMarkupProps {
  schema: Record<string, any>
  id?: string
}

/**
 * Component for rendering JSON-LD schema markup
 * This is a client component that uses next/script to render JSON-LD
 */
export function SchemaMarkup({ schema, id = 'schema-markup' }: SchemaMarkupProps) {
  return (
    <Script
      id={id}
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  )
}

/**
 * Generate schema for medical organization
 */
export function generateMedicalOrganizationSchema(
  name: string,
  url: string,
  logo: string,
  description: string,
  telephone?: string,
  address?: {
    streetAddress: string;
    addressLocality: string;
    addressRegion?: string;
    postalCode?: string;
    addressCountry: string;
  }
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'MedicalBusiness',
    'name': name,
    'url': url,
    'logo': logo,
    'description': description,
    ...(telephone && { 'telephone': telephone }),
    ...(address && {
      'address': {
        '@type': 'PostalAddress',
        'streetAddress': address.streetAddress,
        'addressLocality': address.addressLocality,
        'addressRegion': address.addressRegion,
        'postalCode': address.postalCode,
        'addressCountry': address.addressCountry
      }
    }),
    'medicalSpecialty': 'Multidisciplinary'
  }
}

/**
 * Generate schema for a doctor/physician
 */
export function generateDoctorSchema(
  doctor: {
    id: string;
    name: string;
    image?: string;
    description?: string;
    specialty: string;
    hospital?: string;
    awards?: string[];
    education?: string[];
    rating?: number;
    reviewCount?: number;
  }
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Physician',
    '@id': `https://doctorsleagues.com/doctor/${doctor.id}`,
    'name': doctor.name,
    'image': doctor.image || 'https://doctorsleagues.com/images/doctor-placeholder.jpg',
    'description': doctor.description || `${doctor.name} is a specialist in ${doctor.specialty}`,
    'medicalSpecialty': doctor.specialty,
    ...(doctor.hospital ? {
      'memberOf': {
        '@type': 'MedicalOrganization',
        'name': doctor.hospital
      }
    } : {}),
    ...(doctor.awards && doctor.awards.length > 0 ? { 'award': doctor.awards } : {}),
    ...(doctor.education && doctor.education.length > 0 ? { 'alumniOf': doctor.education } : {}),
    ...(doctor.rating && doctor.reviewCount ? {
      'aggregateRating': {
        '@type': 'AggregateRating',
        'ratingValue': doctor.rating,
        'reviewCount': doctor.reviewCount,
        'bestRating': '5'
      }
    } : {})
  }
}

/**
 * Generate schema for an FAQ page
 */
export function generateFaqSchema(
  questions: Array<{ question: string; answer: string }>
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    'mainEntity': questions.map((item) => ({
      '@type': 'Question',
      'name': item.question,
      'acceptedAnswer': {
        '@type': 'Answer',
        'text': item.answer
      }
    }))
  }
}

/**
 * Generate schema for breadcrumb navigation
 */
export function generateBreadcrumbSchema(
  items: Array<{ name: string; url: string; position: number }>
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    'itemListElement': items.map((item) => ({
      '@type': 'ListItem',
      'position': item.position,
      'name': item.name,
      'item': item.url
    }))
  }
}

/**
 * Generate schema for a medical web page
 */
export function generateMedicalWebPageSchema(
  name: string,
  description: string, 
  url: string,
  datePublished?: string,
  dateModified?: string,
  specialty?: string
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'MedicalWebPage',
    'name': name,
    'description': description,
    'url': url,
    ...(datePublished ? { 'datePublished': datePublished } : {}),
    ...(dateModified ? { 'dateModified': dateModified } : {}),
    'lastReviewed': dateModified || new Date().toISOString().split('T')[0],
    'specialty': specialty || 'Medical Professional Rankings'
  }
} 