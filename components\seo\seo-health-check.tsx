"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Check, AlertTriangle, X } from "lucide-react"

interface SEOHealthCheckProps {
  schemas: Record<string, any>[]
  pageUrl: string
  pageTitle: string
}

/**
 * SEO Health Check component for testing structured data and other SEO elements
 * This component is for development use only and should not be included in production
 */
export function SEOHealthCheck({ schemas, pageUrl, pageTitle }: SEOHealthCheckProps) {
  const [isOpen, setIsOpen] = useState(false)
  
  // Basic schema validation
  const validateSchema = (schema: Record<string, any>) => {
    const errors = []
    
    // Check for required JSON-LD attributes
    if (!schema['@context']) errors.push('Missing @context property')
    if (!schema['@type']) errors.push('Missing @type property')
    
    // Type-specific validation
    if (schema['@type'] === 'FAQPage' && (!schema.mainEntity || !Array.isArray(schema.mainEntity))) {
      errors.push('FAQPage schema is missing mainEntity array')
    }
    
    if (schema['@type'] === 'Organization' && !schema.name) {
      errors.push('Organization schema is missing name')
    }
    
    if ((schema['@type'] === 'MedicalWebPage' || schema['@type'] === 'WebPage') && !schema.url) {
      errors.push('WebPage schema is missing url')
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
  
  // Validate meta tags
  const checkMetaTags = () => {
    if (typeof document === 'undefined') return { valid: false, missing: ['Unable to check meta tags on server'] }
    
    const metaTags = [
      { name: 'title', selector: 'title' },
      { name: 'description', selector: 'meta[name="description"]' },
      { name: 'canonical', selector: 'link[rel="canonical"]' },
      { name: 'og:title', selector: 'meta[property="og:title"]' },
      { name: 'og:description', selector: 'meta[property="og:description"]' },
      { name: 'og:image', selector: 'meta[property="og:image"]' },
      { name: 'twitter:card', selector: 'meta[name="twitter:card"]' }
    ]
    
    const missing = metaTags
      .filter(tag => !document.querySelector(tag.selector))
      .map(tag => tag.name)
    
    return {
      valid: missing.length === 0,
      missing
    }
  }
  
  if (!isOpen) {
    return (
      <Button 
        variant="outline" 
        size="sm" 
        className="fixed bottom-4 right-4 z-50 bg-background/80 text-foreground border-border"
        onClick={() => setIsOpen(true)}
      >
        SEO Health Check
      </Button>
    )
  }
  
  return (
    <div className="fixed inset-0 z-50 bg-background/80 flex items-center justify-center p-4">
      <Card className="w-full max-w-3xl max-h-[90vh] overflow-auto">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>SEO Health Check</span>
            <Button variant="ghost" size="sm" onClick={() => setIsOpen(false)}>
              <X className="h-4 w-4" />
            </Button>
          </CardTitle>
          <CardDescription>Checking structured data and SEO elements for: {pageTitle}</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="schemas">
            <TabsList className="mb-4">
              <TabsTrigger value="schemas">Structured Data</TabsTrigger>
              <TabsTrigger value="meta">Meta Tags</TabsTrigger>
              <TabsTrigger value="raw">Raw JSON-LD</TabsTrigger>
            </TabsList>
            
            <TabsContent value="schemas">
              <div className="space-y-4">
                {schemas.map((schema, index) => {
                  const validation = validateSchema(schema)
                  return (
                    <div key={index} className="border rounded-md p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium">{schema['@type'] || 'Unknown Schema'}</h3>
                        {validation.valid ? (
                          <div className="flex items-center text-green-500">
                            <Check className="h-4 w-4 mr-1" />
                            <span>Valid</span>
                          </div>
                        ) : (
                          <div className="flex items-center text-yellow-500">
                            <AlertTriangle className="h-4 w-4 mr-1" />
                            <span>Issues Found</span>
                          </div>
                        )}
                      </div>
                      
                      {!validation.valid && (
                        <ul className="text-sm text-red-400 mt-2 list-disc pl-5">
                          {validation.errors.map((err, i) => (
                            <li key={i}>{err}</li>
                          ))}
                        </ul>
                      )}
                    </div>
                  )
                })}
              </div>
            </TabsContent>
            
            <TabsContent value="meta">
              <div className="space-y-4">
                <div className="border rounded-md p-4">
                  {typeof window !== 'undefined' && (
                    <>
                      {(() => {
                        const metaCheck = checkMetaTags()
                        return (
                          <>
                            <div className="flex items-center justify-between mb-2">
                              <h3 className="font-medium">Meta Tags</h3>
                              {metaCheck.valid ? (
                                <div className="flex items-center text-green-500">
                                  <Check className="h-4 w-4 mr-1" />
                                  <span>All Required Tags Present</span>
                                </div>
                              ) : (
                                <div className="flex items-center text-yellow-500">
                                  <AlertTriangle className="h-4 w-4 mr-1" />
                                  <span>Missing Tags</span>
                                </div>
                              )}
                            </div>
                            
                            {!metaCheck.valid && (
                              <ul className="text-sm text-red-400 mt-2 list-disc pl-5">
                                {metaCheck.missing.map((tag, i) => (
                                  <li key={i}>Missing {tag}</li>
                                ))}
                              </ul>
                            )}
                          </>
                        )
                      })()}
                    </>
                  )}
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="raw">
              <div className="space-y-4">
                {schemas.map((schema, index) => (
                  <div key={index} className="border rounded-md p-4">
                    <h3 className="font-medium mb-2">{schema['@type'] || 'Unknown Schema'}</h3>
                    <pre className="bg-background p-4 rounded-md overflow-auto text-xs text-muted-green">
                      {JSON.stringify(schema, null, 2)}
                    </pre>
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-green">
            This tool is for development use only.
          </div>
          <Button variant="outline" size="sm" onClick={() => {
            if (typeof window !== 'undefined') {
              window.open(`https://validator.schema.org/#url=${encodeURIComponent(pageUrl)}`, '_blank')
            }
          }}>
            Validate with Schema.org
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
} 