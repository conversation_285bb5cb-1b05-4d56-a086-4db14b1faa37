import Head from 'next/head'

interface SocialMetaTagsProps {
  title: string
  description: string
  canonicalUrl: string
  ogImageUrl?: string
  ogImageAlt?: string
  ogType?: 'website' | 'article' | 'profile'
  twitterCardType?: 'summary' | 'summary_large_image' | 'app' | 'player'
  twitterHandle?: string
  publishedTime?: string
  modifiedTime?: string
  authorName?: string
  siteName?: string
}

/**
 * Enhanced Social Meta Tags component that improves SEO by adding detailed
 * Open Graph and Twitter Card metadata for better social media sharing.
 * 
 * This component should be used on pages where detailed social sharing
 * is important, especially for content pages like blog posts, doctor profiles,
 * and league pages.
 */
export function SocialMetaTags({
  title,
  description,
  canonicalUrl,
  ogImageUrl = 'https://doctorsleagues.com/images/og-default.jpg',
  ogImageAlt = 'Doctor\'s Leagues',
  ogType = 'website',
  twitterCardType = 'summary_large_image',
  twitterHandle = '@DoctorsLeague',
  publishedTime,
  modifiedTime,
  authorName,
  siteName = 'Doctor\'s Leagues'
}: SocialMetaTagsProps) {
  return (
    <Head>
      {/* Essential OpenGraph Meta Tags */}
      <meta property="og:title" content={title} key="og-title" />
      <meta property="og:description" content={description} key="og-desc" />
      <meta property="og:type" content={ogType} key="og-type" />
      <meta property="og:url" content={canonicalUrl} key="og-url" />
      <meta property="og:image" content={ogImageUrl} key="og-image" />
      <meta property="og:image:alt" content={ogImageAlt} key="og-image-alt" />
      <meta property="og:site_name" content={siteName} key="og-site-name" />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={twitterCardType} key="twitter-card" />
      <meta name="twitter:site" content={twitterHandle} key="twitter-site" />
      <meta name="twitter:title" content={title} key="twitter-title" />
      <meta name="twitter:description" content={description} key="twitter-desc" />
      <meta name="twitter:image" content={ogImageUrl} key="twitter-image" />
      <meta name="twitter:image:alt" content={ogImageAlt} key="twitter-image-alt" />
      
      {/* Optional Article Meta Tags */}
      {publishedTime && ogType === 'article' && (
        <meta property="article:published_time" content={publishedTime} key="article-published" />
      )}
      {modifiedTime && ogType === 'article' && (
        <meta property="article:modified_time" content={modifiedTime} key="article-modified" />
      )}
      {authorName && ogType === 'article' && (
        <meta property="article:author" content={authorName} key="article-author" />
      )}
      
      {/* Medical-specific meta tag for healthcare content */}
      <meta name="healthcare:specialty" content="Medical Rankings" key="medical-specialty" />
      
      {/* Additional Meta Tags for SEO */}
      <link rel="canonical" href={canonicalUrl} key="canonical" />
    </Head>
  )
}

/**
 * Specialized version for doctor profiles with medical-specific metadata
 */
export function DoctorSocialMetaTags({
  doctorName,
  specialty,
  description,
  canonicalUrl,
  imageUrl,
  hospitalName,
}: {
  doctorName: string
  specialty: string
  description: string
  canonicalUrl: string
  imageUrl?: string
  hospitalName?: string
}) {
  const title = `Dr. ${doctorName} - ${specialty} | Doctor's Leagues`;
  const ogImageAlt = `Profile photo of Dr. ${doctorName}, ${specialty}`;
  
  return (
    <SocialMetaTags
      title={title}
      description={description}
      canonicalUrl={canonicalUrl}
      ogImageUrl={imageUrl}
      ogImageAlt={ogImageAlt}
      ogType="profile"
      twitterCardType="summary"
      siteName={hospitalName ? `Doctor's Leagues - ${hospitalName}` : "Doctor's Leagues"}
    />
  )
} 