"use client"

import { useEffect, useState, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { reportWebVitals, WebVitalsMetric, getAllPerformanceMetrics } from '@/lib/performance'
import { Button } from '@/components/ui/button'
import { AlertCircle, Download, ExternalLink } from 'lucide-react'

/**
 * Component to track and report web vitals metrics
 * Only rendered in development mode
 */
export function WebVitalsReporter() {
  const [metrics, setMetrics] = useState<WebVitalsMetric[]>([])
  const [performanceData, setPerformanceData] = useState<any>(null)
  const [recommendations, setRecommendations] = useState<{[key: string]: string[]}>({})
  const isInitialized = useRef(false);
  
  useEffect(() => {
    // Only initialize once to prevent duplicate reporting
    if (isInitialized.current) return;
    isInitialized.current = true;
    
    console.log('[WebVitalsReporter] Initializing reporting');
    
    // Collect web vitals metrics
    reportWebVitals((metric) => {
      console.log(`[WebVitalsReporter] Received metric: ${metric.name}`);
      
      setMetrics(prev => {
        // Update metric if it already exists
        const exists = prev.findIndex(m => m.name === metric.name) >= 0
        if (exists) {
          console.log(`[WebVitalsReporter] Updating existing metric: ${metric.name}`);
          return prev.map(m => 
            m.name === metric.name ? metric : m
          )
        }
        // Add new metric
        console.log(`[WebVitalsReporter] Adding new metric: ${metric.name}`);
        return [...prev, metric]
      })
    })

    // Get performance data
    const timer = setTimeout(() => {
      console.log('[WebVitalsReporter] Collecting complete performance data');
      const data = getAllPerformanceMetrics()
      if (data) {
        setPerformanceData(data)
      }
    }, 2000) // Wait for page to load
    
    return () => {
      console.log('[WebVitalsReporter] Cleanup');
      clearTimeout(timer);
    }
  }, [])

  // Generate recommendations based on metric values
  useEffect(() => {
    const newRecommendations: {[key: string]: string[]} = {}
    
    metrics.forEach(metric => {
      if (metric.rating === 'poor' || metric.rating === 'needs-improvement') {
        switch(metric.name) {
          case 'FCP':
            newRecommendations['FCP'] = [
              'Implement critical CSS inlining for above-fold content',
              'Remove render-blocking JavaScript using async/defer attributes',
              'Optimize server response time (TTFB)',
              'Preload key resources like fonts and hero images'
            ]
            break
          case 'LCP':
            newRecommendations['LCP'] = [
              'Optimize and preload hero images',
              'Implement proper image sizing and modern formats (WebP)',
              'Use server-side rendering for faster initial content',
              'Implement resource prioritization'
            ]
            break
          case 'TTFB':
            newRecommendations['TTFB'] = [
              'Use a CDN for assets and content delivery',
              'Optimize server-side code and database queries',
              'Implement proper caching strategies',
              'Consider static site generation for key pages'
            ]
            break
          case 'CLS':
            newRecommendations['CLS'] = [
              'Set explicit width/height for all media elements',
              'Avoid dynamically injected content',
              'Use CSS aspect-ratio or dimension attributes for images',
              'Reserve space for ads, embeds, and iframes'
            ]
            break
          case 'FID':
            newRecommendations['FID'] = [
              'Break up long tasks (>50ms)',
              'Optimize JavaScript execution',
              'Remove unused JavaScript',
              'Use web workers for non-UI operations'
            ]
            break
        }
      }
    })
    
    setRecommendations(newRecommendations)
  }, [metrics])

  // Get progress value as percentage based on rating
  const getProgressValue = (metric: WebVitalsMetric) => {
    switch (metric.rating) {
      case 'good': return 100
      case 'needs-improvement': return 50 
      case 'poor': return 20
      default: return 0
    }
  }

  // Get color class based on rating
  const getColorClass = (rating: string) => {
    switch (rating) {
      case 'good': return 'bg-green-500'
      case 'needs-improvement': return 'bg-amber-500'
      case 'poor': return 'bg-red-500'
      default: return 'bg-background/60'
    }
  }

  return (
    <Card className="shadow-lg w-full max-w-3xl mx-auto bg-background/90 text-foreground">
      <CardHeader>
        <CardTitle className="text-xl font-semibold flex items-center gap-2 text-foreground">
          Core Web Vitals
          {metrics.length === 0 && <Badge className="animate-pulse">Loading...</Badge>}
        </CardTitle>
        <CardDescription className="text-muted-green">
          Performance metrics for this page (scores update as you interact)
        </CardDescription>
      </CardHeader>
      <CardContent>
        {metrics.length > 0 ? (
          <div className="space-y-5">
            {metrics.map((metric) => (
              <div key={metric.name} className="space-y-1">
                <div className="flex justify-between items-center">
                  <div className="font-medium text-foreground">{metric.name}</div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-foreground">{Math.round(metric.value * 100) / 100}</span>
                    <Badge 
                      variant={metric.rating === 'good' ? 'default' : 'outline'}
                      className={`${
                        metric.rating === 'good' 
                          ? 'bg-green-500 hover:bg-green-500' 
                          : metric.rating === 'needs-improvement'
                            ? 'text-amber-500 border-amber-500' 
                            : 'text-red-500 border-red-500'
                      }`}
                    >
                      {metric.rating}
                    </Badge>
                  </div>
                </div>
                <Progress value={getProgressValue(metric)} className="h-2" />
                <div className="text-xs text-muted-green">
                  {getMetricDescription(metric.name)}
                </div>
                
                {/* Show recommendations for poor metrics */}
                {(metric.rating === 'poor' || metric.rating === 'needs-improvement') && recommendations[metric.name] && (
                  <div className="mt-2 p-2 border border-border rounded-md bg-background">
                    <div className="flex items-center gap-1 text-xs font-medium mb-1 text-amber-400">
                      <AlertCircle className="h-3 w-3" />
                      <span>Recommendations</span>
                    </div>
                    <ul className="text-xs space-y-1 pl-4 list-disc text-muted-green">
                      {recommendations[metric.name].map((rec, i) => (
                        <li key={i}>{rec}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}

            {performanceData && (
              <div className="pt-4 border-t border-border">
                <div className="text-sm font-medium mb-3 text-foreground">Navigation Timing</div>
                <div className="grid grid-cols-2 gap-2">
                  <div className="text-xs text-muted-green">TTFB: <span className="font-mono text-foreground">{Math.round(performanceData.derived.ttfb)}ms</span></div>
                  <div className="text-xs text-muted-green">Resources: <span className="font-mono text-foreground">{performanceData.resources.totalResources}</span></div>
                  <div className="text-xs text-muted-green">Dom Interactive: <span className="font-mono text-foreground">{Math.round(performanceData.navigation.domInteractive)}ms</span></div>
                  <div className="text-xs text-muted-green">DOM Complete: <span className="font-mono text-foreground">{Math.round(performanceData.navigation.domComplete)}ms</span></div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-6">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <div className="mt-2 text-sm text-muted-green">Collecting performance data...</div>
          </div>
        )}
      </CardContent>
      {Object.keys(recommendations).length > 0 && (
        <CardFooter className="flex flex-col space-y-4 pt-2 border-t border-border">
          <div className="text-sm text-foreground font-medium">Performance Optimization Actions</div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 w-full">
            <Button variant="outline" size="sm" className="border-border text-muted-green hover:bg-background/80 hover:text-foreground">
              <Download className="h-4 w-4 mr-2" />
              Export Performance Report
            </Button>
            <Button variant="outline" size="sm" className="border-border text-muted-green hover:bg-background/80 hover:text-foreground">
              <ExternalLink className="h-4 w-4 mr-2" />
              View Optimization Guide
            </Button>
          </div>
        </CardFooter>
      )}
    </Card>
  )
}

// Helper function to get metric descriptions
function getMetricDescription(metricName: string): string {
  switch (metricName) {
    case 'CLS':
      return 'Cumulative Layout Shift - measures visual stability (lower is better)'
    case 'FID':
      return 'First Input Delay - measures interactivity in milliseconds (lower is better)'
    case 'LCP':
      return 'Largest Contentful Paint - measures loading performance in milliseconds (lower is better)'
    case 'FCP':
      return 'First Contentful Paint - time until first content is painted in milliseconds'
    case 'TTFB':
      return 'Time to First Byte - time until the first byte of the page is received'
    default:
      return 'Web Vitals metric measuring page performance'
  }
} 