"use client"

import type React from "react"
import { Button } from "@/components/ui/button"

interface SiteHeaderProps {
  openPatientModal: () => void
  openDoctorModal: () => void
}

const SiteHeader: React.FC<SiteHeaderProps> = ({ openPatientModal, openDoctorModal }) => {
  return (
    <header className="bg-white shadow-md py-4">
      <div className="container mx-auto px-4 flex items-center justify-between">
        <div className="text-2xl font-bold text-green-900">FairPlay</div>

        {/* Navigation Buttons */}
        <div className="flex items-center gap-4">
          <Button
            onClick={openPatientModal}
            className="relative overflow-hidden group bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-foreground border-none shadow-md action-button"
            data-join-match-button="true"
          >
            <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-blue-400/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
            <span className="relative z-10 flex items-center">
              <span className="mr-2">🏆</span> <span className="nav-menu-item">Join Match</span>
            </span>
          </Button>
          <Button
            onClick={openDoctorModal}
            className="relative overflow-hidden group bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-foreground border-none shadow-md action-button"
          >
            <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-green-400/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
            <span className="relative z-10 flex items-center">
              <span className="mr-2">⚕️</span> <span className="nav-menu-item">Be referee or player</span>
            </span>
          </Button>
        </div>
      </div>
    </header>
  )
}

export default SiteHeader

