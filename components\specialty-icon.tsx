"use client"

import { useState } from "react"
import Image from "next/image"

export interface SpecialtyIconProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
}

export function SpecialtyIcon({ src, alt, width = 40, height = 40, className = "" }: SpecialtyIconProps) {
  const [imgSrc, setImgSrc] = useState<string>(src)
  const [hasError, setHasError] = useState<boolean>(false)

  // Default fallback icon
  const defaultIcon = "/Green/General medicine.svg"

  // Handle image load error
  const handleError = () => {
    if (!hasError) {
      setHasError(true)
      setImgSrc(defaultIcon)
    }
  }

  return (
    <div className={`relative flex items-center justify-center ${className}`}>
      <Image
        src={imgSrc}
        alt={alt}
        width={width}
        height={height}
        className="object-contain"
        onError={handleError}
      />
    </div>
  )
} 