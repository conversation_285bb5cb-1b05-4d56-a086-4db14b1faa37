"use client"

import type React from "react"

interface ThemedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
}

export function ThemedInput({ label, error, className = "", ...props }: ThemedInputProps) {
  return (
    <div className="mb-4">
      {label && <label className="mb-1 block text-sm font-medium text-blue-800">{label}</label>}
      <input
        className={`w-full rounded-md border border-blue-200 bg-white px-3 py-2 shadow-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 ${
          error ? "border-red-500 bg-red-50" : ""
        } ${className}`}
        {...props}
      />
      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
    </div>
  )
}

interface ThemedSelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string
  error?: string
  options: { value: string; label: string }[]
}

export function ThemedSelect({ label, error, options, className = "", ...props }: ThemedSelectProps) {
  return (
    <div className="mb-4">
      {label && <label className="mb-1 block text-sm font-medium text-blue-800">{label}</label>}
      <select
        className={`w-full rounded-md border border-blue-200 bg-white px-3 py-2 shadow-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 ${
          error ? "border-red-500 bg-red-50" : ""
        } ${className}`}
        {...props}
      >
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
    </div>
  )
}

interface ThemedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "outline"
  isLoading?: boolean
}

export function ThemedButton({
  children,
  variant = "primary",
  isLoading = false,
  className = "",
  ...props
}: ThemedButtonProps) {
  const baseClasses =
    "relative rounded-md px-4 py-2 font-medium transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed"

  const variantClasses = {
    primary:
      "bg-gradient-to-r from-blue-600 to-blue-700 text-foreground hover:from-blue-700 hover:to-blue-800 focus:ring-blue-500 disabled:bg-blue-300",
    secondary:
      "bg-gradient-to-r from-green-600 to-green-700 text-foreground hover:from-green-700 hover:to-green-800 focus:ring-green-500 disabled:bg-green-300",
    outline:
      "border border-blue-500 bg-transparent text-blue-600 hover:bg-blue-50 focus:ring-blue-500 disabled:border-blue-300 disabled:text-blue-300",
  }

  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      disabled={isLoading || props.disabled}
      {...props}
    >
      {isLoading && (
        <span className="absolute inset-0 flex items-center justify-center">
          <svg
            className="h-5 w-5 animate-spin text-foreground"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </span>
      )}
      <span className={isLoading ? "invisible" : ""}>{children}</span>
    </button>
  )
}

export function ThemedProgressSteps({
  steps,
  currentStep,
}: {
  steps: string[]
  currentStep: number
}) {
  return (
    <div className="mb-6">
      <div className="flex justify-between">
        {steps.map((step, index) => (
          <div key={index} className="flex flex-col items-center">
            <div
              className={`flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium ${
                index < currentStep
                  ? "bg-blue-600 text-foreground"
                  : index === currentStep
                    ? "border-2 border-blue-600 bg-white text-blue-600"
                    : "border-2 border-green-400 bg-white text-muted-green"
              }`}
            >
              {index < currentStep ? "✓" : index + 1}
            </div>
            <span className={`mt-1 text-xs ${index <= currentStep ? "text-blue-800" : "text-muted-green"}`}>{step}</span>
          </div>
        ))}
      </div>
      <div className="relative mt-2">
        <div className="absolute inset-0 flex items-center">
          <div className="h-1 w-full bg-green-200"></div>
        </div>
        <div
          className="absolute inset-y-0 left-0 flex items-center"
          style={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
        >
          <div className="h-1 w-full bg-blue-600"></div>
        </div>
      </div>
    </div>
  )
}

