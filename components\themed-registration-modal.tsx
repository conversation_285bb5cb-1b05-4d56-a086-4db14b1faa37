"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { X } from "lucide-react"

interface ThemedRegistrationModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
}

export function ThemedRegistrationModal({ isOpen, onClose, title, children }: ThemedRegistrationModalProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true)
    } else {
      const timer = setTimeout(() => setIsVisible(false), 300)
      return () => clearTimeout(timer)
    }
  }, [isOpen])

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/50 backdrop-blur-sm">
      <div
        className={`w-full max-w-md transform rounded-lg form-friendly p-6 shadow-xl transition-all duration-300 ${
          isOpen ? "scale-100 opacity-100" : "scale-95 opacity-0"
        }`}
      >
        <div className="relative">
          {/* Medical-themed header with pulse line */}
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-bold text-foreground">{title}</h2>
            <button
              onClick={onClose}
              className="rounded-full p-1 text-muted-green transition-colors hover:bg-red-100 hover:text-red-500"
            >
              <X size={20} />
            </button>
          </div>

          {/* ECG Line Divider */}
          <div className="relative mb-6 h-2 w-full overflow-hidden">
            <div className="absolute inset-0 flex items-center">
              <div className="h-px w-full bg-border"></div>
            </div>
            <div className="absolute inset-y-0 left-0 flex w-full items-center justify-center">
              <svg height="20" width="100%" viewBox="0 0 200 20" preserveAspectRatio="none" className="text-primary">
                <path
                  d="M0,10 L5,10 L10,0 L15,20 L20,10 L25,10 L30,10 L35,0 L40,20 L45,10 L50,10 L55,10 L60,0 L65,20 L70,10 L75,10 L80,10 L85,0 L90,20 L95,10 L100,10 L105,10 L110,0 L115,20 L120,10 L125,10 L130,10 L135,0 L140,20 L145,10 L150,10 L155,10 L160,0 L165,20 L170,10 L175,10 L180,10 L185,0 L190,20 L195,10 L200,10"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                />
              </svg>
            </div>
          </div>

          {/* Content */}
          <div>{children}</div>
        </div>
      </div>
    </div>
  )
}

