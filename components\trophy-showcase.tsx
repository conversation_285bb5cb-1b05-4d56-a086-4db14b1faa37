"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Trophy, Medal, Star, Award } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface TrophyData {
  id: number
  name: string
  description: string
  icon: "trophy" | "medal" | "star" | "award"
  color: string
  year: number
}

const SAMPLE_TROPHIES: TrophyData[] = [
  {
    id: 1,
    name: "Doctor of the Year",
    description: "Awarded to the highest-rated doctor across all specialties",
    icon: "trophy",
    color: "text-yellow-500",
    year: 2024,
  },
  {
    id: 2,
    name: "Rising Star",
    description: "Recognizing exceptional new doctors in the platform",
    icon: "star",
    color: "text-blue-500",
    year: 2024,
  },
  {
    id: 3,
    name: "Patient's Choice",
    description: "Voted by patients as the most trusted healthcare provider",
    icon: "award",
    color: "text-green-500",
    year: 2024,
  },
  {
    id: 4,
    name: "Specialty Champion",
    description: "Top-rated doctor in their medical specialty",
    icon: "medal",
    color: "text-purple-500",
    year: 2024,
  },
]

export function TrophyShowcase() {
  const [selectedTrophy, setSelectedTrophy] = useState<TrophyData | null>(null)
  const [trophies, setTrophies] = useState<TrophyData[]>(SAMPLE_TROPHIES)

  useEffect(() => {
    // In a real app, you might fetch trophies from an API
    setTrophies(SAMPLE_TROPHIES)
  }, [])

  const getIcon = (iconName: string, className: string) => {
    switch (iconName) {
      case "trophy":
        return <Trophy className={className} />
      case "medal":
        return <Medal className={className} />
      case "star":
        return <Star className={className} />
      case "award":
        return <Award className={className} />
      default:
        return <Trophy className={className} />
    }
  }

  return (
    <div className="w-full bg-gradient-to-r from-background to-primary/20 p-6 rounded-xl">
      <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center">
        <Trophy className="mr-2 h-6 w-6 text-yellow-500" />
        Trophy Showcase
      </h2>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        {trophies.map((trophy) => (
          <motion.div
            key={trophy.id}
            className={`bg-background/60 p-4 rounded-lg cursor-pointer border-2 ${
              selectedTrophy?.id === trophy.id ? "border-primary" : "border-transparent"
            }`}
            whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
            onClick={() => setSelectedTrophy(trophy)}
          >
            <div className="flex flex-col items-center text-center">
              <div className={`mb-2 ${trophy.color}`}>{getIcon(trophy.icon, "h-10 w-10")}</div>
              <h3 className="font-bold text-foreground text-sm md:text-base">{trophy.name}</h3>
              <p className="text-foreground/70 text-xs mt-1">{trophy.year}</p>
            </div>
          </motion.div>
        ))}
      </div>

      <AnimatePresence mode="wait">
        {selectedTrophy && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-background/80 p-6 rounded-lg border border-primary/30"
          >
            <div className="flex items-center mb-4">
              <div className={`mr-4 ${selectedTrophy.color}`}>{getIcon(selectedTrophy.icon, "h-12 w-12")}</div>
              <div>
                <h3 className="text-xl font-bold text-foreground">{selectedTrophy.name}</h3>
                <p className="text-foreground/70">{selectedTrophy.year}</p>
              </div>
            </div>
            <p className="text-foreground/90 mb-4">{selectedTrophy.description}</p>
            <div className="flex justify-end">
              <Button
                variant="outline"
                className="text-primary border-primary hover:bg-primary/20"
                onClick={() => setSelectedTrophy(null)}
              >
                Close
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

