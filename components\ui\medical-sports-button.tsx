import type React from "react"
import { cn } from "@/lib/utils"
import { transitions } from "@/lib/animations"

interface MedicalSportsButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "doctor" | "patient" | "accent" | "outline" | "ghost"
  size?: "sm" | "md" | "lg"
  isLoading?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  fullWidth?: boolean
}

export function MedicalSportsButton({
  children,
  variant = "doctor",
  size = "md",
  isLoading = false,
  leftIcon,
  rightIcon,
  fullWidth = false,
  className,
  ...props
}: MedicalSportsButtonProps) {
  // Variant styles
  const variantClasses = {
    doctor: "bg-primary-600 text-foreground hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",
    patient: "bg-secondary-600 text-foreground hover:bg-secondary-700 focus:ring-secondary-500 active:bg-secondary-800",
    accent: "bg-accent text-accent-foreground hover:bg-accent/90 focus:ring-accent active:bg-accent/80",
    outline: "bg-transparent border-2 border-current text-foreground hover:bg-card focus:ring-white",
    ghost: "bg-transparent text-foreground hover:bg-card focus:ring-white",
  }

  // Size styles
  const sizeClasses = {
    sm: "text-sm px-3 py-1.5 rounded",
    md: "text-base px-4 py-2 rounded-md",
    lg: "text-lg px-5 py-2.5 rounded-md",
  }

  // Loading indicator
  const LoadingIndicator = () => (
    <div className="loading-dots absolute inset-0 flex items-center justify-center">
      <span className="loading-dot"></span>
      <span className="loading-dot"></span>
      <span className="loading-dot"></span>
    </div>
  )

  return (
    <button
      className={cn(
        "medical-sports-button relative",
        "inline-flex items-center justify-center font-medium",
        "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-primary-900",
        transitions.standard,
        variantClasses[variant],
        sizeClasses[size],
        fullWidth && "w-full",
        isLoading && "text-transparent",
        props.disabled && "opacity-50 cursor-not-allowed hover:bg-opacity-100 active:transform-none",
        className,
      )}
      data-variant={variant}
      data-loading={isLoading}
      {...props}
    >
      {leftIcon && <span className={cn("mr-2", isLoading && "opacity-0")}>{leftIcon}</span>}
      <span className={cn(isLoading && "opacity-0")}>{children}</span>
      {rightIcon && <span className={cn("ml-2", isLoading && "opacity-0")}>{rightIcon}</span>}
      {isLoading && <LoadingIndicator />}
    </button>
  )
}

