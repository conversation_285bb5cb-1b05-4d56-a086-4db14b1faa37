import type React from "react"
import { cn } from "@/lib/utils"
import { transitions } from "@/lib/animations"

interface MedicalSportsCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "doctor" | "patient"
  interactive?: boolean
  elevated?: boolean
}

export function MedicalSportsCard({
  children,
  variant = "doctor",
  interactive = false,
  elevated = false,
  className,
  ...props
}: MedicalSportsCardProps) {
  return (
    <div
      className={cn("medical-sports-card", interactive && transitions.interactive, elevated && "shadow-xl", className)}
      data-variant={variant}
      {...props}
    >
      {children}
    </div>
  )
}

interface MedicalSportsCardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {}

export function MedicalSportsCardHeader({ children, className, ...props }: MedicalSportsCardHeaderProps) {
  return (
    <div className={cn("medical-sports-card-header", className)} {...props}>
      {children}
    </div>
  )
}

interface MedicalSportsCardBodyProps extends React.HTMLAttributes<HTMLDivElement> {}

export function MedicalSportsCardBody({ children, className, ...props }: MedicalSportsCardBodyProps) {
  return (
    <div className={cn("medical-sports-card-body", className)} {...props}>
      {children}
    </div>
  )
}

interface MedicalSportsCardFooterProps extends React.HTMLAttributes<HTMLDivElement> {}

export function MedicalSportsCardFooter({ children, className, ...props }: MedicalSportsCardFooterProps) {
  return (
    <div className={cn("medical-sports-card-footer", className)} {...props}>
      {children}
    </div>
  )
}

