"use client"

import type React from "react"
import { useState } from "react"
import { Eye, EyeOff } from "lucide-react"
import { cn } from "@/lib/utils"
import { transitions } from "@/lib/animations"

interface MedicalSportsInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  icon?: React.ReactNode
  error?: string
  variant?: "doctor" | "patient"
  showPasswordToggle?: boolean
  label?: string
  helperText?: string
  required?: boolean
}

export function MedicalSportsInput({
  icon,
  error,
  variant = "doctor",
  type = "text",
  showPasswordToggle = false,
  label,
  helperText,
  required,
  className,
  ...props
}: MedicalSportsInputProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [isFocused, setIsFocused] = useState(false)

  const variantClasses = {
    doctor: "border-primary-500 focus:ring-primary-400",
    patient: "border-secondary-500 focus:ring-secondary-400",
  }

  const inputType = type === "password" && showPassword ? "text" : type

  return (
    <div className="w-full">
      {label && (
        <label
          htmlFor={props.id}
          className={cn(
            "form-label flex items-center mb-1.5",
            isFocused && variant === "doctor" && "text-primary-400",
            isFocused && variant === "patient" && "text-secondary-400",
          )}
        >
          {label} {required && <span className="text-error ml-1">*</span>}
        </label>
      )}

      <div className={cn("medical-sports-input group", error && "error", transitions.standard)}>
        <div className={cn("absolute left-3 text-foreground/70 group-focus-within:text-foreground", transitions.standard)}>
          {icon}
        </div>

        <input
          type={inputType}
          className={cn(
            "w-full bg-card border border-border rounded-md py-2 pl-10 pr-3 text-foreground placeholder-white/50",
            "focus:ring-2 focus:border-transparent",
            error ? "border-error/70 focus:ring-error" : variantClasses[variant],
            showPasswordToggle && "pr-10",
            transitions.standard,
            className,
          )}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...props}
        />

        {type === "password" && showPasswordToggle && (
          <button
            type="button"
            className={cn("absolute right-3 text-foreground/70 hover:text-foreground", transitions.standard)}
            onClick={() => setShowPassword(!showPassword)}
            tabIndex={-1}
            aria-label={showPassword ? "Hide password" : "Show password"}
          >
            {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
          </button>
        )}
      </div>

      {error ? (
        <p className="medical-sports-input-error animate-slideUp">{error}</p>
      ) : helperText ? (
        <p className="text-foreground/70 text-sm mt-1">{helperText}</p>
      ) : null}
    </div>
  )
}

