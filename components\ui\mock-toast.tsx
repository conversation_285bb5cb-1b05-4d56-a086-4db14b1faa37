"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { X } from "lucide-react"

// Simple context to manage toast state
const ToastContext = React.createContext<{
  toasts: Toast[]
  addToast: (toast: Omit<Toast, "id">) => void
  removeToast: (id: string) => void
}>({
  toasts: [],
  addToast: () => {},
  removeToast: () => {},
})

// Toast types
export type Toast = {
  id: string
  title?: React.ReactNode
  description?: React.ReactNode
  action?: React.ReactNode
  variant?: "default" | "destructive"
  duration?: number
  open: boolean
}

export type ToastActionElement = React.ReactElement

// Toast provider component
export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = React.useState<Toast[]>([])

  const addToast = React.useCallback((toast: Omit<Toast, "id">) => {
    const id = Math.random().toString(36).substring(2, 9)
    const newToast = { ...toast, id, open: true }

    setToasts((prev) => [...prev, newToast])

    // Auto-dismiss toast after duration
    if (toast.duration !== Number.POSITIVE_INFINITY) {
      setTimeout(() => {
        removeToast(id)
      }, toast.duration || 5000)
    }
  }, [])

  const removeToast = React.useCallback((id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id))
  }, [])

  return <ToastContext.Provider value={{ toasts, addToast, removeToast }}>{children}</ToastContext.Provider>
}

// Hook to use toast
export function useToast() {
  const context = React.useContext(ToastContext)

  if (!context) {
    throw new Error("useToast must be used within a ToastProvider")
  }

  const toast = React.useCallback(
    (props: Omit<Toast, "id" | "open">) => {
      context.addToast(props)
    },
    [context],
  )

  return {
    toast,
    toasts: context.toasts,
    dismiss: context.removeToast,
  }
}

// Toast component
export function Toast({
  id,
  title,
  description,
  action,
  variant = "default",
  onOpenChange,
  children,
  className,
  ...props
}: Toast & {
  onOpenChange?: (open: boolean) => void
  children?: React.ReactNode
  className?: string
}) {
  const context = React.useContext(ToastContext)

  const handleClose = () => {
    if (onOpenChange) {
      onOpenChange(false)
    }
    context.removeToast(id)
  }

  return (
    <div
      className={cn(
        "fixed bottom-4 right-4 z-50 flex max-w-md items-center justify-between space-x-4 rounded-md border p-6 shadow-lg",
        variant === "destructive" ? "border-red-500 bg-red-600 text-foreground" : "border-border bg-background/90 text-foreground",
        className,
      )}
      {...props}
    >
      <div className="grid gap-1">
        {title && <div className="text-sm font-semibold">{title}</div>}
        {description && <div className="text-sm opacity-90">{description}</div>}
        {children}
      </div>
      {action}
      <button
        onClick={handleClose}
        className="absolute right-2 top-2 rounded-md p-1 text-muted-green opacity-0 transition-opacity hover:text-foreground focus:opacity-100 group-hover:opacity-100"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  )
}

// Toaster component to render all toasts
export function Toaster() {
  const { toasts } = useToast()

  return (
    <>
      {toasts.map(({ id, title, description, action, ...props }) => (
        <Toast key={id} id={id} title={title} description={description} action={action} {...props} />
      ))}
    </>
  )
}

