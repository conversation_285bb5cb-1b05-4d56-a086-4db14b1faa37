import { cn } from "@/lib/utils"
import { transitions, animations } from "@/lib/animations"

interface NotificationBadgeProps {
  count: number
  max?: number
  variant?: "primary" | "secondary" | "accent" | "error"
  pulse?: boolean
  size?: "sm" | "md" | "lg"
}

export function NotificationBadge({
  count,
  max = 99,
  variant = "primary",
  pulse = false,
  size = "md",
}: NotificationBadgeProps) {
  const displayCount = count > max ? `${max}+` : count

  const variantClasses = {
    primary: "bg-primary text-primary-foreground",
    secondary: "bg-secondary text-secondary-foreground",
    accent: "bg-accent text-accent-foreground",
    error: "bg-error text-error-foreground",
  }

  const sizeClasses = {
    sm: "min-w-[1.25rem] h-5 text-xs",
    md: "min-w-[1.5rem] h-6 text-sm",
    lg: "min-w-[1.75rem] h-7 text-base",
  }

  return (
    <span
      className={cn(
        "inline-flex items-center justify-center rounded-full px-1.5 font-medium",
        variantClasses[variant],
        sizeClasses[size],
        pulse && animations.pulse,
        transitions.standard,
      )}
    >
      {displayCount}
    </span>
  )
}

