import { cn } from "@/lib/utils"
import { transitions } from "@/lib/animations"

interface PasswordStrengthMeterProps {
  strength: number
  maxStrength?: number
  className?: string
  showText?: boolean
  variant?: "doctor" | "patient"
}

export function PasswordStrengthMeter({
  strength,
  maxStrength = 5,
  className,
  showText = true,
  variant = "doctor",
}: PasswordStrengthMeterProps) {
  const getStrengthText = (strength: number): string => {
    switch (strength) {
      case 0:
        return "Very Weak"
      case 1:
        return "Weak"
      case 2:
        return "Fair"
      case 3:
        return "Good"
      case 4:
        return "Strong"
      case 5:
        return "Very Strong"
      default:
        return ""
    }
  }

  const getStrengthColor = (strength: number): string => {
    switch (strength) {
      case 0:
      case 1:
        return "text-error"
      case 2:
        return "text-warning"
      case 3:
        return "text-accent-500"
      case 4:
      case 5:
        return "text-success"
      default:
        return ""
    }
  }

  return (
    <div className={cn("mt-2", className)}>
      <div className="password-strength-meter">
        {Array.from({ length: maxStrength }).map((_, index) => (
          <div
            key={index}
            className={cn(
              "password-strength-segment",
              strength >= index + 1 ? `active-${Math.min(strength, 4)}` : "",
              transitions.standard,
            )}
          />
        ))}
      </div>
      {showText && strength > 0 && (
        <p className={cn("text-xs mt-1 animate-fadeIn", getStrengthColor(strength))}>{getStrengthText(strength)}</p>
      )}
    </div>
  )
}

