import { cn } from "@/lib/utils"
import { transitions } from "@/lib/animations"

interface ProgressBarProps {
  value: number
  max?: number
  variant?: "primary" | "secondary" | "accent" | "success" | "error"
  size?: "sm" | "md" | "lg"
  showLabel?: boolean
  label?: string
  animated?: boolean
}

export function ProgressBar({
  value,
  max = 100,
  variant = "primary",
  size = "md",
  showLabel = false,
  label,
  animated = false,
}: ProgressBarProps) {
  const percentage = Math.min(Math.max(0, (value / max) * 100), 100)

  const variantClasses = {
    primary: "bg-primary",
    secondary: "bg-secondary",
    accent: "bg-accent",
    success: "bg-success",
    error: "bg-error",
  }

  const sizeClasses = {
    sm: "h-1",
    md: "h-2",
    lg: "h-3",
  }

  return (
    <div className="w-full">
      {(showLabel || label) && (
        <div className="flex justify-between mb-1">
          <span className="text-sm text-foreground/80">{label || "Progress"}</span>
          <span className="text-sm text-foreground/80">{Math.round(percentage)}%</span>
        </div>
      )}

      <div className={cn("w-full bg-card rounded-full overflow-hidden", sizeClasses[size])}>
        <div
          className={cn(
            "h-full rounded-full",
            variantClasses[variant],
            animated && "animate-pulse",
            transitions.standard,
          )}
          style={{ width: `${percentage}%` }}
          role="progressbar"
          aria-valuenow={value}
          aria-valuemin={0}
          aria-valuemax={max}
        />
      </div>
    </div>
  )
}

