import { cn } from "@/lib/utils"

interface QuarterIndicatorProps {
  currentQuarter: number
  totalQuarters: number
  className?: string
  variant?: "doctor" | "patient"
  labels?: string[]
  showLabels?: boolean
}

export function QuarterIndicator({
  currentQuarter,
  totalQuarters,
  className,
  variant = "doctor",
  labels,
  showLabels = false,
}: QuarterIndicatorProps) {
  const variantClasses = {
    doctor: {
      bg: "bg-primary-900/50",
      active: "bg-accent-500",
      inactive: "bg-background/70",
      text: "text-foreground",
      quarter: "bg-primary-800",
    },
    patient: {
      bg: "bg-secondary-900/50",
      active: "bg-accent-500",
      inactive: "bg-background/70",
      text: "text-foreground",
      quarter: "bg-secondary-800",
    },
  }

  return (
    <div className={cn("flex flex-col space-y-2", className)}>
      <div className={cn("flex justify-between items-center rounded-lg p-2", variantClasses[variant].bg)}>
        <div className={cn("font-heading font-bold px-2", variantClasses[variant].text)}>QUARTER</div>
        <div className={cn("px-4 py-1 rounded text-foreground font-bold font-heading", variantClasses[variant].quarter)}>
          {currentQuarter}/{totalQuarters}
        </div>
        <div className="flex space-x-1">
          {Array.from({ length: totalQuarters }).map((_, index) => (
            <div
              key={index}
              className={cn(
                "w-8 h-2 rounded transition-all duration-300",
                index + 1 <= currentQuarter ? variantClasses[variant].active : variantClasses[variant].inactive,
                index + 1 === currentQuarter && "animate-pulse",
              )}
            />
          ))}
        </div>
      </div>

      {showLabels && labels && (
        <div className="flex justify-between px-2">
          {labels.map((label, index) => (
            <div
              key={index}
              className={cn("text-xs font-medium", index + 1 <= currentQuarter ? "text-foreground" : "text-foreground/50")}
            >
              {label}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

