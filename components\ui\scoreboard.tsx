import type React from "react"
import Image from "next/image" // Import next/image
import { Trophy, Star, TrendingUp, TrendingDown, Minus } from "lucide-react"
import { cn } from "@/lib/utils"
import { transitions, animations } from "@/lib/animations"

interface ScoreboardProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string
  compact?: boolean
}

export function Scoreboard({ children, title = "Leaderboard", compact = false, className, ...props }: ScoreboardProps) {
  return (
    <div className={cn("scoreboard", animations.fadeIn, className)} {...props}>
      <div className="scoreboard-header">
        <h3 className="font-heading text-foreground text-lg flex items-center">
          <Trophy className="h-5 w-5 text-accent mr-2" />
          {title}
        </h3>
        {!compact && <div className="text-xs text-foreground/70">Updated rankings</div>}
      </div>
      <div className="space-y-1">{children}</div>
    </div>
  )
}

interface ScoreboardRowProps {
  rank: number
  name: string
  score: number | string
  change?: "up" | "down" | "none"
  highlight?: boolean
  avatar?: string
}

export function ScoreboardRow({ rank, name, score, change, highlight = false, avatar }: ScoreboardRowProps) {
  const getRankClass = (rank: number) => {
    if (rank === 1) return "rank-1"
    if (rank === 2) return "rank-2"
    if (rank === 3) return "rank-3"
    return "bg-primary-800/50"
  }

  const getChangeIcon = () => {
    switch (change) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-success" />
      case "down":
        return <TrendingDown className="h-4 w-4 text-error" />
      default:
        return <Minus className="h-4 w-4 text-foreground/50" />
    }
  }

  return (
    <div className={cn("scoreboard-row", highlight && "bg-accent/10", transitions.standard)}>
      <div className="scoreboard-team">
        <div
          className={cn("w-6 h-6 flex items-center justify-center rounded-full text-sm font-bold", getRankClass(rank))}
        >
          {rank}
        </div>

        {avatar && (
          <div className="w-8 h-8 rounded-full overflow-hidden">
            <Image src={avatar || "/placeholder.svg"} alt={name} width={32} height={32} className="w-full h-full object-cover" />
          </div>
        )}

        <div className="ml-2">
          <div className="text-foreground font-medium">{name}</div>
          {change && (
            <div className="flex items-center text-xs">
              {getChangeIcon()}
              <span className="ml-1 text-foreground/70">
                {change === "up" ? "Improved" : change === "down" ? "Dropped" : "No change"}
              </span>
            </div>
          )}
        </div>
      </div>

      <div className="flex items-center">
        <div className="scoreboard-score text-foreground">{score}</div>
        <div className="ml-2 text-accent">
          <Star className="h-4 w-4 fill-current" />
        </div>
      </div>
    </div>
  )
}
