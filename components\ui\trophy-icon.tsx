import { Trophy } from "lucide-react"
import { cn } from "@/lib/utils"

interface TrophyIconProps {
  className?: string
  size?: "sm" | "md" | "lg"
}

export function TrophyIcon({ className, size = "md" }: TrophyIconProps) {
  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-10 h-10",
    lg: "w-12 h-12",
  }

  return (
    <div className={cn("trophy-icon", sizeClasses[size], className)}>
      <Trophy className="h-5 w-5" />
    </div>
  )
}

