import { Badge } from "@/components/ui/badge"
import { Check } from "lucide-react"
import { cn } from "@/lib/utils"

interface VerifiedVisitBadgeProps {
  className?: string
  size?: "sm" | "md" | "lg"
}

export function VerifiedVisitBadge({ className, size = "md" }: VerifiedVisitBadgeProps) {
  const sizeClasses = {
    sm: "text-xs px-2 py-0.5",
    md: "text-sm px-2.5 py-0.5", 
    lg: "text-base px-3 py-1"
  }

  const iconSizes = {
    sm: "h-3 w-3",
    md: "h-4 w-4",
    lg: "h-5 w-5"
  }

  return (
    <Badge 
      variant="secondary" 
      className={cn(
        "bg-green-100 text-green-800 border-green-200 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700/50 dark:hover:bg-green-900/50",
        "inline-flex items-center gap-1.5 font-medium",
        sizeClasses[size],
        className
      )}
    >
      <Check className={cn("shrink-0", iconSizes[size])} />
      Verified Visit
    </Badge>
  )
}
