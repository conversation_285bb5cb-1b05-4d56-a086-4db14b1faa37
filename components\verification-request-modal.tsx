"use client"

import { useState, useTransition } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Upload, FileImage, AlertCircle, CheckCircle } from "lucide-react"
import { requestReviewVerification } from "@/actions/review-actions"
import { toast } from "sonner"

interface VerificationRequestModalProps {
  reviewId: string
  trigger: React.ReactNode
}

export function VerificationRequestModal({ reviewId, trigger }: VerificationRequestModalProps) {
  const [open, setOpen] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isPending, startTransition] = useTransition()
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setError('Please select an image file (JPG, PNG, WebP)')
        return
      }
      
      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        setError('File size must be less than 5MB')
        return
      }
      
      setSelectedFile(file)
      setError(null)
    }
  }

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    
    if (!selectedFile) {
      setError('Please select a file to upload')
      return
    }

    const formData = new FormData()
    formData.append('proof_image', selectedFile)

    startTransition(async () => {
      try {
        const result = await requestReviewVerification(reviewId, formData)
        
        if (result.success) {
          setSuccess(true)
          toast.success('Verification request submitted successfully!')
          setTimeout(() => {
            setOpen(false)
            setSuccess(false)
            setSelectedFile(null)
          }, 2000)
        } else {
          setError(result.error || 'Failed to submit verification request')
        }
      } catch (error) {
        setError('An unexpected error occurred. Please try again.')
      }
    })
  }

  const resetModal = () => {
    setSelectedFile(null)
    setError(null)
    setSuccess(false)
  }

  return (
    <Dialog open={open} onOpenChange={(newOpen) => {
      setOpen(newOpen)
      if (!newOpen) {
        resetModal()
      }
    }}>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileImage className="h-5 w-5 text-primary" />
            Verify Your Appointment
          </DialogTitle>
          <DialogDescription>
            To ensure the authenticity of reviews, please upload a photo of your appointment receipt or confirmation. 
            All sensitive information will be permanently deleted after our team confirms your visit.
          </DialogDescription>
        </DialogHeader>

        {success ? (
          <div className="py-6">
            <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/20">
              <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
              <AlertDescription className="text-green-800 dark:text-green-200">
                Your verification request has been submitted successfully! Our team will review it within 24-48 hours.
              </AlertDescription>
            </Alert>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="proof-image">Appointment Receipt/Confirmation</Label>
              <div className="relative">
                <Input
                  id="proof-image"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
                  disabled={isPending}
                />
                {selectedFile && (
                  <div className="mt-2 flex items-center gap-2 text-sm text-muted-green">
                    <FileImage className="h-4 w-4" />
                    <span>{selectedFile.name}</span>
                    <span className="text-xs">({(selectedFile.size / 1024 / 1024).toFixed(1)}MB)</span>
                  </div>
                )}
              </div>
              <p className="text-xs text-muted-green">
                Accepted formats: JPG, PNG, WebP. Maximum size: 5MB
              </p>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={!selectedFile || isPending}
                className="min-w-[120px]"
              >
                {isPending ? (
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    Uploading...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Upload className="h-4 w-4" />
                    Submit for Verification
                  </div>
                )}
              </Button>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  )
}
