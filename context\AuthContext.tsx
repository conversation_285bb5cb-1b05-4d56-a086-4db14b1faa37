'use client'; // For using context and hooks in client components

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation'; // For potential programmatic navigation

// Define the shape of the user object we'll store
interface AuthUser {
    userId: number; // Changed from number | string, as our DB now uses INTEGER for IDs
    email: string;
    userType: 'patient' | 'doctor';
    // Add additional fields needed by components
    first_name?: string;
    last_name?: string;
    country_id?: number;
    country?: string;
    profile_image?: string; // Add profile image field
    profile?: any; // Add profile object that may contain image information
    // Add any other common fields that might be needed
}

// Define the shape of our AuthContext
interface AuthContextType {
    isAuthenticated: boolean;
    user: AuthUser | null;
    token: string | null;
    login: (userData: AuthUser, token: string) => void;
    logout: () => void;
    isLoading: boolean; // To indicate if auth state is being loaded
}

// Create the context with a default undefined value
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Define the AuthProvider component
export const AuthProvider = ({ children }: { children: ReactNode }) => {
    const [user, setUser] = useState<AuthUser | null>(null);
    const [token, setToken] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(true); // Start true: loading auth state from storage
    const router = useRouter();

    useEffect(() => {
        // This effect runs once on component mount to initialize auth state from localStorage
        try {
            const storedToken = localStorage.getItem('jwtToken');
            const storedUserData = localStorage.getItem('userData');

            if (storedToken && storedUserData) {
                try {
                    const parsedUser = JSON.parse(storedUserData) as AuthUser;
                    
                    // Validate the user data, especially userId
                    if (!parsedUser.userId) {
                        console.error('Missing userId in stored user data, clearing invalid auth state');
                        localStorage.removeItem('jwtToken');
                        localStorage.removeItem('userData');
                        localStorage.removeItem('userRole');
                        setIsLoading(false);
                        return;
                    }
                    
                    // Special case for test doctor - hardcode the known ID
                    if (parsedUser.email === '<EMAIL>' && 
                        (parsedUser.userId === undefined || parsedUser.userId === null)) {
                        console.log('Test doctor account detected in localStorage, fixing userId');
                        parsedUser.userId = 4097;
                        // Update localStorage with fixed data
                        localStorage.setItem('userData', JSON.stringify(parsedUser));
                    }
                    
                    // TODO: Optionally, add token verification here.
                    // For now, if a token and user data exist, we assume the user is authenticated.
                    // A robust implementation might decode the token to check expiry 
                    // or even make a quick API call to a /me endpoint to validate the token server-side.
                    setUser(parsedUser);
                    setToken(storedToken);
                    console.log('AuthContext: User state initialized from localStorage', parsedUser);
                } catch (parseError) {
                    console.error('Failed to parse stored user data:', parseError);
                    // Clear corrupted data
                    localStorage.removeItem('jwtToken');
                    localStorage.removeItem('userData');
                    localStorage.removeItem('userRole');
                }
            } else {
                console.log('AuthContext: No user state found in localStorage.');
            }
        } catch (error) {
            console.error("AuthContext: Error initializing auth state from localStorage:", error);
            // Clear potentially corrupted storage
            localStorage.removeItem('jwtToken');
            localStorage.removeItem('userData');
            localStorage.removeItem('userRole');
        }
        setIsLoading(false); // Finished attempting to load auth state
    }, []);

    const login = (userData: AuthUser, jwtToken: string) => {
        localStorage.setItem('jwtToken', jwtToken);
        localStorage.setItem('userData', JSON.stringify(userData));
        setUser(userData);
        setToken(jwtToken);
        console.log('AuthContext: User logged in', userData);
        // Redirection is handled by the login dialog itself after calling this
    };

    const logout = () => {
        localStorage.removeItem('jwtToken');
        localStorage.removeItem('userData');
        localStorage.removeItem('userRole'); // Also clear userRole if it was set
        setUser(null);
        setToken(null);
        console.log('AuthContext: User logged out');
        // Redirect to homepage or login page after logout
        router.push('/'); // Or your preferred public page
    };

    return (
        <AuthContext.Provider value={{ isAuthenticated: !!token, user, token, login, logout, isLoading }}>
            {children}
        </AuthContext.Provider>
    );
};

// Custom hook to use the AuthContext
export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}; 