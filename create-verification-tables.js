const { createClient } = require('@supabase/supabase-js');

// Load environment variables from .env.local
const fs = require('fs');
const path = require('path');

// Read .env.local file
const envPath = path.join(__dirname, '.env.local');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');
  
  envLines.forEach(line => {
    if (line.trim() && !line.startsWith('#')) {
      const [key, value] = line.split('=');
      if (key && value) {
        process.env[key.trim()] = value.trim();
      }
    }
  });
}

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('Supabase URL:', supabaseUrl ? 'Set' : 'Not set');
console.log('Service Key:', supabaseServiceKey ? 'Set' : 'Not set');

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

// Create Supabase client with service role
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { autoRefreshToken: false, persistSession: false }
});

async function checkAndCreateTables() {
  console.log('🔍 Checking existing tables...');
  
  // Check if verification_proofs table exists
  console.log('\n📋 Checking verification_proofs table...');
  const { error: proofsError } = await supabase
    .from('verification_proofs')
    .select('id')
    .limit(1);
  
  if (proofsError) {
    console.log('❌ verification_proofs table does not exist');
    console.log('📝 Please create it manually in Supabase SQL Editor with this SQL:');
    console.log(`
CREATE TABLE IF NOT EXISTS public.verification_proofs (
    id SERIAL PRIMARY KEY,
    review_id INTEGER NOT NULL REFERENCES public.reviews(review_id) ON DELETE CASCADE,
    image_path TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_verification_proofs_review_id ON public.verification_proofs(review_id);
CREATE INDEX IF NOT EXISTS idx_verification_proofs_created_at ON public.verification_proofs(created_at);
    `);
  } else {
    console.log('✅ verification_proofs table exists');
  }
  
  // Check if review_flags table exists
  console.log('\n📋 Checking review_flags table...');
  const { error: flagsError } = await supabase
    .from('review_flags')
    .select('id')
    .limit(1);
  
  if (flagsError) {
    console.log('❌ review_flags table does not exist');
    console.log('📝 Please create it manually in Supabase SQL Editor with this SQL:');
    console.log(`
CREATE TABLE IF NOT EXISTS public.review_flags (
    id SERIAL PRIMARY KEY,
    review_id INTEGER NOT NULL REFERENCES public.reviews(review_id) ON DELETE CASCADE,
    reporter_user_id INTEGER,
    flag_reason TEXT NOT NULL CHECK (flag_reason IN (
        'inappropriate_content',
        'spam',
        'fake_review',
        'personal_attack',
        'off_topic',
        'harassment',
        'other'
    )),
    flag_description TEXT,
    status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'resolved', 'dismissed')),
    admin_notes TEXT,
    resolved_by INTEGER,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_review_flags_review_id ON public.review_flags(review_id);
CREATE INDEX IF NOT EXISTS idx_review_flags_status ON public.review_flags(status);
    `);
  } else {
    console.log('✅ review_flags table exists');
  }
  
  // Check if verification_status column exists in reviews table
  console.log('\n📋 Checking verification_status column in reviews table...');
  const { error: statusError } = await supabase
    .from('reviews')
    .select('verification_status')
    .limit(1);
  
  if (statusError) {
    console.log('❌ verification_status column does not exist in reviews table');
    console.log('📝 Please add it manually in Supabase SQL Editor with this SQL:');
    console.log(`
ALTER TABLE public.reviews 
ADD COLUMN IF NOT EXISTS verification_status TEXT DEFAULT 'unverified' 
CHECK (verification_status IN ('unverified', 'pending_verification', 'verified', 'rejected'));

CREATE INDEX IF NOT EXISTS idx_reviews_verification_status ON public.reviews(verification_status);

UPDATE public.reviews 
SET verification_status = 'unverified' 
WHERE verification_status IS NULL;
    `);
  } else {
    console.log('✅ verification_status column exists in reviews table');
  }
  
  console.log('\n📦 Storage Bucket Instructions:');
  console.log('Please create the "appointment-verification" storage bucket manually:');
  console.log('1. Go to Supabase Dashboard > Storage');
  console.log('2. Create new bucket named: appointment-verification');
  console.log('3. Set bucket to PRIVATE (not public)');
  console.log('4. Set file size limit: 5MB');
  console.log('5. Set allowed MIME types: image/jpeg, image/jpg, image/png, image/webp');
  
  console.log('\n🎉 Database structure check completed!');
  console.log('Please run the SQL commands above in your Supabase SQL Editor to create missing tables/columns.');
}

// Test database connection first
async function testConnection() {
  console.log('🔍 Testing database connection...');
  
  try {
    const { data, error } = await supabase
      .from('reviews')
      .select('review_id')
      .limit(1);
    
    if (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }
    
    console.log('✅ Database connection successful');
    return true;
    
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    return false;
  }
}

// Main execution
async function main() {
  const connectionOk = await testConnection();
  
  if (!connectionOk) {
    console.error('🛑 Cannot proceed without database connection');
    process.exit(1);
  }
  
  await checkAndCreateTables();
}

// Run the script
main().catch(error => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
