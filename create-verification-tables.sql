-- SQL script to create verification and moderation tables
-- Run this in Supabase SQL Editor

-- 1. Add verification_status column to reviews table if it doesn't exist
ALTER TABLE public.reviews 
ADD COLUMN IF NOT EXISTS verification_status TEXT DEFAULT 'unverified' 
CHECK (verification_status IN ('unverified', 'pending_verification', 'verified', 'rejected'));

-- Create index for better performance on verification status queries
CREATE INDEX IF NOT EXISTS idx_reviews_verification_status ON public.reviews(verification_status);

-- Update existing reviews to have 'unverified' status if they are NULL
UPDATE public.reviews 
SET verification_status = 'unverified' 
WHERE verification_status IS NULL;

-- 2. Create verification_proofs table
CREATE TABLE IF NOT EXISTS public.verification_proofs (
    id SERIAL PRIMARY KEY,
    review_id INTEGER NOT NULL REFERENCES public.reviews(review_id) ON DELETE CASCADE,
    image_path TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_verification_proofs_review_id ON public.verification_proofs(review_id);
CREATE INDEX IF NOT EXISTS idx_verification_proofs_created_at ON public.verification_proofs(created_at);

-- Add comments for documentation
COMMENT ON TABLE public.verification_proofs IS 'Stores appointment receipt images for review verification';
COMMENT ON COLUMN public.verification_proofs.review_id IS 'Foreign key to the reviews table';
COMMENT ON COLUMN public.verification_proofs.image_path IS 'Path to the proof image in Supabase storage';
COMMENT ON COLUMN public.verification_proofs.created_at IS 'Timestamp when the proof was uploaded';
COMMENT ON COLUMN public.verification_proofs.updated_at IS 'Timestamp when the record was last updated';

-- 3. Create review_flags table
CREATE TABLE IF NOT EXISTS public.review_flags (
    flag_id SERIAL PRIMARY KEY,
    review_id INTEGER NOT NULL REFERENCES public.reviews(review_id) ON DELETE CASCADE,
    flagged_by_user_id INTEGER,
    reason TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'resolved', 'dismissed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    moderator_notes TEXT,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by_user_id INTEGER
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_review_flags_review_id ON public.review_flags(review_id);
CREATE INDEX IF NOT EXISTS idx_review_flags_status ON public.review_flags(status);
CREATE INDEX IF NOT EXISTS idx_review_flags_created_at ON public.review_flags(created_at);

-- Add comments for documentation
COMMENT ON TABLE public.review_flags IS 'Stores flags/reports for reviews that need moderation';
COMMENT ON COLUMN public.review_flags.review_id IS 'Foreign key to the reviews table';
COMMENT ON COLUMN public.review_flags.flagged_by_user_id IS 'User who flagged the review';
COMMENT ON COLUMN public.review_flags.reason IS 'Reason for flagging the review';
COMMENT ON COLUMN public.review_flags.status IS 'Status of the flag: open, resolved, or dismissed';

-- 4. Enable Row Level Security (RLS) on verification_proofs
ALTER TABLE public.verification_proofs ENABLE ROW LEVEL SECURITY;

-- Policy: Only authenticated users can view their own verification proofs
CREATE POLICY "Users can view their own verification proofs" ON public.verification_proofs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.reviews 
            WHERE reviews.review_id = verification_proofs.review_id 
            AND reviews.user_id = auth.uid()::integer
        )
    );

-- Policy: Only authenticated users can insert their own verification proofs
CREATE POLICY "Users can insert their own verification proofs" ON public.verification_proofs
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.reviews 
            WHERE reviews.review_id = verification_proofs.review_id 
            AND reviews.user_id = auth.uid()::integer
        )
    );

-- Policy: Only service role can delete verification proofs (for admin cleanup)
CREATE POLICY "Service role can delete verification proofs" ON public.verification_proofs
    FOR DELETE USING (true);

-- Policy: Only service role can update verification proofs
CREATE POLICY "Service role can update verification proofs" ON public.verification_proofs
    FOR UPDATE USING (true);

-- 5. Enable Row Level Security (RLS) on review_flags
ALTER TABLE public.review_flags ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view flags for their own reviews
CREATE POLICY "Users can view flags for their own reviews" ON public.review_flags
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.reviews 
            WHERE reviews.review_id = review_flags.review_id 
            AND reviews.user_id = auth.uid()::integer
        )
    );

-- Policy: Authenticated users can flag reviews
CREATE POLICY "Authenticated users can flag reviews" ON public.review_flags
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Policy: Only service role can update/resolve flags (for admin actions)
CREATE POLICY "Service role can manage flags" ON public.review_flags
    FOR UPDATE USING (true);

-- Policy: Only service role can delete flags
CREATE POLICY "Service role can delete flags" ON public.review_flags
    FOR DELETE USING (true);

-- Success message
SELECT 'Verification and moderation tables created successfully!' as result;
