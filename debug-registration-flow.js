/**
 * Debug script to test the doctor registration flow and identify where image path saving fails
 */

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function debugRegistrationFlow() {
  console.log('=== DEBUGGING DOCTOR REGISTRATION FLOW ===\n');

  try {
    // Step 1: Check current registrations
    console.log('1. Checking current registrations...');
    const { data: currentRegs, error: currentError } = await supabaseAdmin
      .from('doctors_registration')
      .select('doctor_id, auth_id, fullname, email, profile_image, status')
      .order('doctor_id', { ascending: false })
      .limit(5);

    if (currentError) {
      console.error('Error fetching current registrations:', currentError);
      return;
    }

    console.log(`Found ${currentRegs.length} recent registrations:`);
    currentRegs.forEach(reg => {
      console.log(`  - ID: ${reg.doctor_id}, Auth ID: ${reg.auth_id}, Name: ${reg.fullname}`);
      console.log(`    Email: ${reg.email}, Image: ${reg.profile_image || 'NULL'}`);
    });

    // Step 2: Check if there are any registrations with images
    console.log('\n2. Checking registrations with images...');
    const { data: withImages, error: imageError } = await supabaseAdmin
      .from('doctors_registration')
      .select('doctor_id, auth_id, fullname, profile_image')
      .not('profile_image', 'is', null);

    if (imageError) {
      console.error('Error fetching registrations with images:', imageError);
      return;
    }

    console.log(`Found ${withImages.length} registrations with images:`);
    withImages.forEach(reg => {
      console.log(`  - ID: ${reg.doctor_id}, Auth ID: ${reg.auth_id}, Name: ${reg.fullname}`);
      console.log(`    Image: ${reg.profile_image}`);
    });

    // Step 3: Check storage bucket for uploaded images
    console.log('\n3. Checking storage bucket for uploaded images...');

    // Check root level first
    const { data: rootFiles, error: rootError } = await supabaseAdmin.storage
      .from('doctor-profiles')
      .list('', { limit: 10 });

    if (rootError) {
      console.error('Error fetching root storage files:', rootError);
    } else {
      console.log(`Found ${rootFiles.length} items in root:`);
      rootFiles.forEach(item => {
        console.log(`  - ${item.name} (${item.metadata?.size || 'unknown size'} bytes)`);
      });
    }

    // Check real_photos subfolder
    const { data: storageFiles, error: storageError } = await supabaseAdmin.storage
      .from('doctor-profiles')
      .list('real_photos', {
        limit: 10,
        sortBy: { column: 'created_at', order: 'desc' }
      });

    if (storageError) {
      console.error('Error fetching real_photos storage files:', storageError);
    } else {
      console.log(`Found ${storageFiles.length} files in real_photos:`);
      storageFiles.forEach(file => {
        console.log(`  - ${file.name} (${file.metadata?.size || 'unknown size'} bytes)`);
        console.log(`    Created: ${file.created_at}`);
        console.log(`    Full path would be: real_photos/${file.name}`);

        // Check if this looks like a UUID-timestamp format
        const uuidPattern = /^[a-f0-9-]{36}-\d+\./;
        if (uuidPattern.test(file.name)) {
          console.log(`    ✅ This file matches expected UUID-timestamp format`);
        } else {
          console.log(`    ❓ This file does NOT match expected UUID-timestamp format`);
        }
      });
    }

    // Step 4: Analyze the mismatch
    console.log('\n4. Analyzing image upload vs database mismatch...');
    
    if (storageFiles && storageFiles.length > 0 && withImages.length === 0) {
      console.log('❌ CRITICAL ISSUE IDENTIFIED:');
      console.log('   - Images are being uploaded to storage successfully');
      console.log('   - BUT image paths are NOT being saved to database');
      console.log('   - This confirms the database update is failing');
      
      // Try to match storage files with recent registrations
      console.log('\n5. Attempting to match storage files with registrations...');
      for (const file of storageFiles) {
        // Extract UUID from filename (format: UUID-timestamp.ext)
        const uuidMatch = file.name.match(/^([a-f0-9-]{36})-\d+\./);
        if (uuidMatch) {
          const extractedUuid = uuidMatch[1];
          console.log(`File ${file.name} appears to be for UUID: ${extractedUuid}`);
          
          // Check if this UUID exists in registrations
          const matchingReg = currentRegs.find(reg => reg.auth_id === extractedUuid);
          if (matchingReg) {
            console.log(`  ✅ Found matching registration: ${matchingReg.fullname} (${matchingReg.email})`);
            console.log(`  ❌ But profile_image is: ${matchingReg.profile_image || 'NULL'}`);
            console.log(`  📝 Should be: real_photos/${file.name}`);
          } else {
            console.log(`  ❓ No matching registration found for this UUID`);
          }
        }
      }
    } else if (withImages.length > 0) {
      console.log('✅ Some registrations have images saved correctly');
    } else {
      console.log('ℹ️  No images in storage or database to analyze');
    }

    console.log('\n=== DEBUG ANALYSIS COMPLETE ===');
    console.log('\nNext steps:');
    console.log('1. Register a new doctor with an image');
    console.log('2. Check the browser console for detailed logs');
    console.log('3. Run this script again to see if the image path was saved');

  } catch (error) {
    console.error('Debug script failed:', error);
  }
}

// Run the debug
debugRegistrationFlow();
