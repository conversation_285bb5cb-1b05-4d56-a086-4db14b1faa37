// Declare modules for components that TypeScript can't find
declare module '@/components/specialty-icon' {
  export interface SpecialtyIconProps {
    src: string
    alt: string
    width: number
    height: number
    className?: string
  }
  
  export function SpecialtyIcon(props: SpecialtyIconProps): JSX.Element
}

declare module '@/components/country-flag' {
  export interface CountryFlagProps {
    src: string
    alt: string
    width: number
    height: number
    className?: string
  }
  
  export function CountryFlag(props: CountryFlagProps): JSX.Element
} 