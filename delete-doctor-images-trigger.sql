-- SUPABASE STORAGE IMAGE DELETION TRIGGER
-- Automatically deletes image files from Supabase storage when doctor records are deleted

-- STEP 1: Create function to delete image from Supabase storage
CREATE OR REPLACE FUNCTION delete_doctor_image_from_storage()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    image_path TEXT;
    bucket_name TEXT := 'doctor-images'; -- Change this to your actual bucket name
    file_path TEXT;
    result JSONB;
BEGIN
    -- Get the image path from the deleted record
    image_path := OLD.profile_image;
    
    -- Check if image path exists and is not empty
    IF image_path IS NULL OR image_path = '' THEN
        RAISE NOTICE 'No image to delete for doctor ID: %', OLD.doctor_id;
        RETURN OLD;
    END IF;
    
    -- Extract file path from full URL if needed
    -- Handle different URL formats:
    -- 1. Full Supabase URL: https://xxx.supabase.co/storage/v1/object/public/bucket/path
    -- 2. Relative path: folder/filename.jpg
    -- 3. Just filename: filename.jpg
    
    IF image_path LIKE 'https://%' THEN
        -- Extract path after the bucket name from full URL
        file_path := regexp_replace(
            image_path, 
            '^https://[^/]+/storage/v1/object/public/' || bucket_name || '/', 
            ''
        );
    ELSE
        -- Use the path as-is (relative path or filename)
        file_path := image_path;
    END IF;
    
    -- Log the deletion attempt
    RAISE NOTICE 'Attempting to delete image: % from bucket: %', file_path, bucket_name;
    
    -- Delete the file from Supabase storage using the storage.objects table
    -- This uses Supabase's internal storage management
    BEGIN
        DELETE FROM storage.objects 
        WHERE bucket_id = bucket_name 
        AND name = file_path;
        
        -- Check if file was deleted
        IF FOUND THEN
            RAISE NOTICE 'Successfully deleted image: % for doctor ID: %', file_path, OLD.doctor_id;
        ELSE
            RAISE NOTICE 'Image not found in storage: % for doctor ID: %', file_path, OLD.doctor_id;
        END IF;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- Log error but don't fail the deletion of the doctor record
            RAISE NOTICE 'Error deleting image % for doctor ID %: %', file_path, OLD.doctor_id, SQLERRM;
    END;
    
    RETURN OLD;
END;
$$;

-- STEP 2: Create the trigger on doctors table
DROP TRIGGER IF EXISTS trigger_delete_doctor_image ON doctors;
CREATE TRIGGER trigger_delete_doctor_image
    AFTER DELETE ON doctors
    FOR EACH ROW
    EXECUTE FUNCTION delete_doctor_image_from_storage();

-- STEP 3: Alternative function using HTTP requests (if direct storage access doesn't work)
CREATE OR REPLACE FUNCTION delete_doctor_image_via_api()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    image_path TEXT;
    bucket_name TEXT := 'doctor-images'; -- Change this to your actual bucket name
    file_path TEXT;
    api_url TEXT;
    headers JSONB;
    response JSONB;
BEGIN
    -- Get the image path from the deleted record
    image_path := OLD.profile_image;
    
    -- Check if image path exists and is not empty
    IF image_path IS NULL OR image_path = '' THEN
        RAISE NOTICE 'No image to delete for doctor ID: %', OLD.doctor_id;
        RETURN OLD;
    END IF;
    
    -- Extract file path from full URL if needed
    IF image_path LIKE 'https://%' THEN
        file_path := regexp_replace(
            image_path, 
            '^https://[^/]+/storage/v1/object/public/' || bucket_name || '/', 
            ''
        );
    ELSE
        file_path := image_path;
    END IF;
    
    -- Log the deletion attempt
    RAISE NOTICE 'Attempting to delete image via API: % from bucket: %', file_path, bucket_name;
    
    -- Construct API URL for deletion
    api_url := current_setting('app.supabase_url') || '/storage/v1/object/' || bucket_name || '/' || file_path;
    
    -- Set headers with service role key
    headers := jsonb_build_object(
        'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key'),
        'Content-Type', 'application/json'
    );
    
    -- Make HTTP DELETE request (requires http extension)
    BEGIN
        SELECT INTO response extensions.http((
            'DELETE',
            api_url,
            headers,
            NULL,
            NULL
        )::extensions.http_request);
        
        -- Log result
        IF (response->>'status_code')::int BETWEEN 200 AND 299 THEN
            RAISE NOTICE 'Successfully deleted image via API: % for doctor ID: %', file_path, OLD.doctor_id;
        ELSE
            RAISE NOTICE 'API deletion failed for image: % for doctor ID: %, Status: %', 
                file_path, OLD.doctor_id, response->>'status_code';
        END IF;
        
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Error deleting image via API % for doctor ID %: %', file_path, OLD.doctor_id, SQLERRM;
    END;
    
    RETURN OLD;
END;
$$;

-- STEP 4: Check current triggers to ensure proper order
SELECT 
    trigger_name,
    event_manipulation,
    event_object_table,
    action_timing,
    action_order,
    action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'doctors' 
AND event_manipulation = 'DELETE'
ORDER BY action_order;

-- STEP 5: Verification message
SELECT 'Doctor image deletion trigger created successfully!' as status;
SELECT 'Trigger will delete images from bucket: doctor-images' as info;
SELECT 'Make sure to update bucket_name in the function if different' as warning;
