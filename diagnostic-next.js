// diagnostic-next.js
// This is a script to test Supabase connectivity and fetch top doctors
// Run with: node -r dotenv/config diagnostic-next.js dotenv_config_path=.env.local

const fs = require('fs');
const { createClient } = require('@supabase/supabase-js');

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('ERROR: Missing Supabase environment variables.');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in your .env.local file.');
  process.exit(1);
}

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key exists:', !!supabaseKey);

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function fetchTopDoctorsBySpecialty() {
  try {
    console.log('Starting diagnostic test...');
    
    // First fetch all specialties
    console.log('Fetching specialties...');
    const { data: specialties, error: specialtiesError } = await supabase
      .from('specialties')
      .select('*')
      .order('specialty_name');
    
    if (specialtiesError) {
      console.error('Error fetching specialties:', specialtiesError);
      return;
    }
    
    console.log(`Successfully fetched ${specialties.length} specialties`);
    
    // Fetch all doctors with their related data
    console.log('Fetching doctors...');
    const { data: doctors, error: doctorsError } = await supabase
      .from('doctors')
      .select('*')
      .order('rating', { ascending: false });
    
    if (doctorsError) {
      console.error('Error fetching doctors:', doctorsError);
      console.log('Full error object:', JSON.stringify(doctorsError, null, 2));
      return;
    }
    
    console.log(`Successfully fetched ${doctors.length} doctors`);
    
    // Try a simpler fetch to see if relations are causing issues
    console.log('Testing simpler fetch without relations...');
    const { data: simpleData, error: simpleError } = await supabase
      .from('doctors')
      .select('doctor_id, fullname, rating, specialty_id')
      .limit(5);
      
    if (simpleError) {
      console.error('Error with simple fetch:', simpleError);
    } else {
      console.log('Simple fetch success:', simpleData);
    }
    
    // Now try with relations
    console.log('Testing fetch with relations...');
    const { data: relationData, error: relationError } = await supabase
      .from('doctors')
      .select(`
        doctor_id, 
        fullname, 
        rating,
        specialty_id,
        specialties:specialty_id (
          specialty_name
        )
      `)
      .limit(5);
      
    if (relationError) {
      console.error('Error with relation fetch:', relationError);
    } else {
      console.log('Relation fetch success:', JSON.stringify(relationData, null, 2));
    }
    
    // Group doctors by specialty
    const doctorsBySpecialty = {};
    
    // Initialize all specialties with empty arrays
    specialties.forEach(specialty => {
      doctorsBySpecialty[specialty.specialty_id] = [];
    });
    
    // Group doctors by specialty
    doctors.forEach(doctor => {
      if (doctor.specialty_id) {
        if (!doctorsBySpecialty[doctor.specialty_id]) {
          doctorsBySpecialty[doctor.specialty_id] = [];
        }
        
        doctorsBySpecialty[doctor.specialty_id].push({
          doctor_id: doctor.doctor_id,
          fullname: doctor.fullname,
          specialty_id: doctor.specialty_id,
          specialty_name: specialties.find(s => s.specialty_id === doctor.specialty_id)?.specialty_name || 'Unknown',
          rating: parseFloat(doctor.rating) || 0,
          hospital_name: doctor.facility || 'Independent Practice'
        });
      }
    });
    
    // Sort doctors in each specialty by rating
    Object.keys(doctorsBySpecialty).forEach(specialtyId => {
      doctorsBySpecialty[specialtyId].sort((a, b) => (b.rating || 0) - (a.rating || 0));
    });
    
    // Create output text
    let outputText = '# Top Doctors By Specialty\n\n';
    
    specialties.forEach(specialty => {
      const specialtyDoctors = doctorsBySpecialty[specialty.specialty_id] || [];
      const topThree = specialtyDoctors.slice(0, 3);
      
      outputText += `## ${specialty.specialty_name} (ID: ${specialty.specialty_id})\n`;
      
      if (topThree.length === 0) {
        outputText += 'No doctors in this specialty\n\n';
      } else {
        topThree.forEach((doctor, index) => {
          outputText += `${index + 1}. ${doctor.fullname} - Rating: ${doctor.rating.toFixed(1)} - ${doctor.hospital_name}\n`;
        });
        outputText += `\nTotal doctors in this specialty: ${specialtyDoctors.length}\n\n`;
      }
    });
    
    // Write to file
    fs.writeFileSync('top_doctors_by_specialty.txt', outputText);
    console.log('Diagnostic complete. Results written to top_doctors_by_specialty.txt');
    
    // Also print summary to console
    console.log('\n--- SUMMARY ---');
    specialties.forEach(specialty => {
      const count = doctorsBySpecialty[specialty.specialty_id]?.length || 0;
      console.log(`${specialty.specialty_name}: ${count} doctors`);
    });
    
  } catch (err) {
    console.error('Unexpected error in diagnostic script:', err);
  }
}

fetchTopDoctorsBySpecialty(); 