const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Initialize Supabase client with your project details
// These should match what's in your lib/supabase-client.js
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function fetchTopDoctorsBySpecialty() {
  try {
    console.log('Starting diagnostic test...');
    console.log('Supabase URL:', supabaseUrl);
    
    // First fetch all specialties
    const { data: specialties, error: specialtiesError } = await supabase
      .from('specialties')
      .select('*')
      .order('specialty_name');
    
    if (specialtiesError) {
      console.error('Error fetching specialties:', specialtiesError);
      return;
    }
    
    console.log(`Successfully fetched ${specialties.length} specialties`);
    
    // Fetch all doctors with their related data
    const { data: doctors, error: doctorsError } = await supabase
      .from('doctors')
      .select(`
        *,
        hospitals:hospital_id (
          hospital_name
        ),
        countries:country_id (
          country_name
        ),
        specialties:specialty_id (
          specialty_name
        )
      `)
      .order('community_rating', { ascending: false });
    
    if (doctorsError) {
      console.error('Error fetching doctors:', doctorsError);
      console.log('Full error object:', JSON.stringify(doctorsError, null, 2));
      return;
    }
    
    console.log(`Successfully fetched ${doctors.length} doctors`);
    
    // Group doctors by specialty
    const doctorsBySpecialty = {};
    
    // Initialize all specialties with empty arrays
    specialties.forEach(specialty => {
      doctorsBySpecialty[specialty.specialty_id] = [];
    });
    
    // Group doctors by specialty
    doctors.forEach(doctor => {
      if (doctor.specialty_id) {
        if (!doctorsBySpecialty[doctor.specialty_id]) {
          doctorsBySpecialty[doctor.specialty_id] = [];
        }
        
        doctorsBySpecialty[doctor.specialty_id].push({
          doctor_id: doctor.doctor_id,
          fullname: doctor.fullname,
          specialty: doctor.specialties?.specialty_name || 'Unknown',
          specialty_id: doctor.specialty_id,
          rating: parseFloat(doctor.rating) || 0,
          hospital_name: doctor.hospitals?.hospital_name || doctor.facility || 'Independent Practice'
        });
      }
    });
    
    // Sort doctors in each specialty by rating
    Object.keys(doctorsBySpecialty).forEach(specialtyId => {
      doctorsBySpecialty[specialtyId].sort((a, b) => (b.rating || 0) - (a.rating || 0));
    });
    
    // Create output text
    let outputText = '# Top Doctors By Specialty\n\n';
    
    specialties.forEach(specialty => {
      const specialtyDoctors = doctorsBySpecialty[specialty.specialty_id] || [];
      const topThree = specialtyDoctors.slice(0, 3);
      
      outputText += `## ${specialty.specialty_name} (ID: ${specialty.specialty_id})\n`;
      
      if (topThree.length === 0) {
        outputText += 'No doctors in this specialty\n\n';
      } else {
        topThree.forEach((doctor, index) => {
          outputText += `${index + 1}. ${doctor.fullname} - Rating: ${doctor.rating.toFixed(1)} - ${doctor.hospital_name}\n`;
        });
        outputText += `\nTotal doctors in this specialty: ${specialtyDoctors.length}\n\n`;
      }
    });
    
    // Write to file
    fs.writeFileSync('top_doctors_by_specialty.txt', outputText);
    console.log('Diagnostic complete. Results written to top_doctors_by_specialty.txt');
    
    // Also print summary to console
    console.log('\n--- SUMMARY ---');
    specialties.forEach(specialty => {
      const count = doctorsBySpecialty[specialty.specialty_id]?.length || 0;
      console.log(`${specialty.specialty_name}: ${count} doctors`);
    });
    
  } catch (err) {
    console.error('Unexpected error in diagnostic script:', err);
  }
}

fetchTopDoctorsBySpecialty(); 