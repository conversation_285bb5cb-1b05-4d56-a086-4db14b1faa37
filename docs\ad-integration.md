# Dynamic Ad System

This document explains the dynamic ad system implemented in the Doctor's League application.

## Overview

The ad system connects directly to the database to fetch and display ads based on page location and position. Ads are managed through the admin dashboard and dynamically displayed throughout the application.

## Components

### 1. DynamicAd Component

Located at: `components/ads/dynamic-ad.tsx`

This component handles the rendering of individual ads with different styles based on their position (banner, sidebar, side-left, etc.). Features include:

- Safe image loading with fallbacks
- Different layouts for different ad positions (banner, sidebar, side ads)
- Responsive design for all screen sizes
- Visual enhancements (gradients, badges, hover effects)

### 2. PageAdWrapper Component

Located at: `components/ads/page-ad-wrapper.tsx`

This component handles:

- Fetching the ad data from the database using the appropriate fetch function
- Error handling and loading states
- Providing test ad fallbacks when needed (development mode)

### 3. PageAds Component

Located at: `components/ads/page-ads.tsx`

This is the main entry point for embedding ads in pages. It:

- Determines which ad positions to display
- Applies position-specific styling and layout
- Organizes multiple ad slots on a page

### 4. Ad Helpers

Located at: `components/ads/ad-helpers.ts`

This provides utility functions for:

- Mapping page names to their correct ad fetch functions
- Normalizing page names and positions for consistent database queries
- Fallback handling for missing ad positions

## Usage

To add ads to a page, import and use the PageAds component:

```tsx
import { PageAds } from "@/components/ads/page-ads"

// In your page component:
<PageAds 
  pageName="divisions" 
  positions={['banner', 'sidebar', 'side-left']} 
  // Optional: showTestAds={true} // for development only
/>
```

## Position Types

The system supports these ad positions:

- `banner`: Large horizontal ad at the top of content
- `sidebar`: Vertical ad on the right sidebar
- `side-left`: Vertical ad on the left side
- `side-right`: Vertical ad on the right side
- `bottom`: Horizontal ad at the bottom of content
- `in-content`: Ad embedded within page content

## Database Integration

Ads are fetched through server actions defined in `actions/ad-actions.ts`. These functions query the Supabase database to retrieve active ads for specific pages and positions.

## Admin Dashboard

Ads can be managed through the admin dashboard at `/admin/ads`, which allows:

- Creating new ads
- Editing existing ads
- Setting target pages and positions
- Uploading media
- Scheduling ad start/end dates
- Toggling ad status (active/inactive)

## Styling

The ad components use shadcn UI components and custom styling to match the application's design system, with:

- Card-based layouts
- Gradients and background effects
- Badges and labels
- Responsive sizing and positioning 