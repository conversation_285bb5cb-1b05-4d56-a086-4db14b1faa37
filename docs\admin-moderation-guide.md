# Admin Moderation Dashboard Guide

## Overview

The Admin Moderation Dashboard provides a centralized interface for moderators to review and manage content verification requests and flagged reviews. This secure, role-protected interface ensures only authorized administrators can access moderation functions.

## Access

### Login Credentials
- **URL**: `/admin/login`
- **Username**: `admin`
- **Password**: `admin123`

### Navigation
Once logged in, access the moderation dashboard via:
- **URL**: `/admin/moderation`
- **Navigation**: Admin Sidebar → "Moderation" (Shield icon)

## Features

### 1. Verification Queue

The Verification Queue displays pending appointment receipt verifications submitted by users to verify their reviews.

#### What You'll See:
- **Reviewer**: User ID who submitted the verification
- **Review Text**: Rating and comment content
- **Date Submitted**: When the verification was requested
- **Actions**: Available moderation actions

#### Available Actions:
- **View Proof**: Opens a modal displaying the uploaded appointment receipt
- **Approve**: Marks the review as verified and updates doctor scores
- **Reject**: Marks the verification as rejected

#### Process Flow:
1. Click "View Proof" to examine the appointment receipt
2. Verify the receipt appears legitimate and matches the review
3. Click "Approve" if valid, or "Reject" if invalid/suspicious
4. The item is automatically removed from the queue
5. Doctor scores are recalculated if approved

### 2. Flagged Reviews

The Flagged Reviews section shows reviews that have been reported by users for inappropriate content.

#### What You'll See:
- **Review Content**: The flagged review text and rating
- **Reason for Flag**: Why the review was reported (spam, inappropriate content, etc.)
- **Flagged By**: User who reported the review (or "Anonymous")
- **Reporter's Note**: Additional context provided by the reporter

#### Available Actions:
- **Dismiss Flag**: Mark the flag as dismissed (review stays published)
- **Remove Review**: Delete the review and mark flag as resolved

#### Process Flow:
1. Review the flagged content and reporter's reasoning
2. Determine if the content violates community guidelines
3. Choose appropriate action:
   - **Dismiss Flag**: If the review is acceptable
   - **Remove Review**: If the review violates guidelines
4. Add optional admin notes explaining your decision
5. The item is removed from the queue

## Security Features

- **Role-Based Access**: Only users with admin privileges can access moderation functions
- **Action Logging**: All moderation decisions are logged with admin user ID and timestamp
- **Privacy Compliance**: Verification images are automatically deleted after processing
- **Audit Trail**: Complete history of moderation actions for accountability

## Technical Details

### Server Actions Used:
- `getPendingVerifications()`: Fetches pending verification requests
- `decideVerificationRequest()`: Processes verification decisions
- `getOpenReviewFlags()`: Fetches flagged reviews
- `dismissReviewFlag()`: Dismisses a review flag
- `removeFlaggedReview()`: Removes a flagged review

### Data Flow:
1. Page loads with initial data from server actions
2. User interactions trigger client-side state updates
3. Actions are processed via server actions with loading states
4. Page refreshes to reflect updated data
5. Doctor scores are automatically recalculated when needed

## Best Practices

### For Verification Review:
- Carefully examine appointment receipts for authenticity
- Look for matching dates, doctor names, and medical facility information
- Be suspicious of obviously fake or manipulated images
- When in doubt, err on the side of caution and reject

### For Flagged Content:
- Read the full context of the review and flag reason
- Consider if the content violates community standards
- Use admin notes to document reasoning for future reference
- Be consistent in applying moderation policies

### General Guidelines:
- Process items promptly to maintain user trust
- Document decisions clearly in admin notes
- Escalate unclear cases to senior moderators
- Monitor for patterns of abuse or spam

## Troubleshooting

### Common Issues:
- **Images not loading**: Check storage bucket permissions
- **Actions not working**: Verify admin role permissions
- **Data not refreshing**: Check server action responses for errors

### Error Handling:
- All errors are displayed in red alert cards
- Failed actions show specific error messages
- Loading states prevent duplicate submissions
- Page refresh resolves most temporary issues

## Support

For technical issues or policy questions, contact the development team or refer to the main documentation in `/docs/review-verification-moderation-system.md`.
