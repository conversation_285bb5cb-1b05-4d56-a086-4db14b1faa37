# API Documentation

This document provides details about the application's API endpoints.

## Authentication Endpoints

These endpoints are handled by files in `pages/api/auth/custom/`.

### 1. User Registration

*   **Endpoint**: `POST /api/auth/custom/register`
*   **Description**: Registers a new user (doctor or patient).
*   **Request Body**: `application/json`
    ```json
    {
      "email": "<EMAIL>",
      "password": "yourpassword",
      "userType": "doctor" | "patient",
      "profileData": {
        // Profile fields vary based on userType
        // For 'doctor':
        //   "fullname": "Dr. <PERSON>",
        //   "hospital": "General Hospital",
        //   "medical_specialty": "Cardiology",
        //   "educational_board": "Medical University",
        //   "phone_number": "**********",
        //   "experience": "10",
        //   "subspecialization": "Interventional Cardiology", (optional)
        //   "certifications": "Board Certified in Cardiology", (optional)
        //   "recognitions": "Top Doctor Award 2023", (optional)
        //   "languages": "English, Spanish", (optional)
        //   "professional_affiliation": "American College of Cardiology", (optional)
        //   "procedures_performed": "Angioplasty, Stenting", (optional)
        //   "treatment_services": "Heart failure management", (optional)
        //   "facilityId": "1", (optional, hospital_id)
        //   "specialtyId": "2", (optional, specialty_id)
        //   "countryId": "3" (optional, country_id)
        // For 'patient':
        //   "username": "johndoe",
        //   "firstName": "John",
        //   "lastName": "Doe",
        //   "gender": "Male", (optional)
        //   "city": "New York", (optional)
        //   "country": "USA", (optional)
        //   "age": 30, (optional)
        //   "medical_condition": "Hypertension", (optional)
        //   "phone_number": "**********", (optional)
        //   "state_province_region": "NY" (optional)
      }
    }
    ```
*   **Responses**:
    *   `201 Created`: Registration successful.
        ```json
        {
          "success": true,
          "userId": 123, // The numeric ID of the created user/doctor profile
          "message": "Registration successful. Please verify your email."
        }
        ```
    *   `400 Bad Request`: Missing required fields or email already registered.
        ```json
        {
          "success": false,
          "error": "Missing required fields" 
        }
        ```
        ```json
        {
          "success": false,
          "error": "Email already registered"
        }
        ```
    *   `405 Method Not Allowed`: If not a POST request.
        ```json
        {
          "success": false,
          "error": "Method not allowed"
        }
        ```
    *   `500 Internal Server Error`: If an unexpected error occurs.
        ```json
        {
          "success": false,
          "error": "An unexpected error occurred"
        }
        ```
*   **Notes**:
    *   Creates records in `auth_credentials` and either `doctors` or `users` table.
    *   Generates a sequential numeric ID for the user/doctor profile.
    *   Sends a verification email to the user using Mailtrap for SMTP.
    *   The `verification_tokens` table is used to store email verification tokens.

### 2. User Login

*   **Endpoint**: `POST /api/auth/custom/login`
*   **Description**: Logs in an existing user.
*   **Request Body**: `application/json`
    ```json
    {
      "email": "<EMAIL>",
      "password": "yourpassword"
    }
    ```
*   **Responses**:
    *   `200 OK`: Login successful.
        ```json
        {
          "success": true,
          "message": "Login successful.",
          "token": "jwt.token.here",
          "user": {
            "userId": 123, // Numeric ID from auth_credentials.user_profile_id
            "email": "<EMAIL>",
            "userType": "doctor" | "patient",
            "first_name": "John", // (optional)
            "last_name": "Doe",   // (optional)
            "country_id": 1,      // (optional)
            "country": "Country Name" // (optional)
          }
        }
        ```
    *   `400 Bad Request`: Email and password are required.
        ```json
        {
          "success": false,
          "error": "Email and password are required."
        }
        ```
    *   `401 Unauthorized`: Invalid email or password.
        ```json
        {
          "success": false,
          "error": "Invalid email or password."
        }
        ```
    *   `403 Forbidden`: Email not verified.
        ```json
        {
          "success": false,
          "error": "Please verify your email before logging in. Check your inbox for the verification link."
        }
        ```
    *   `405 Method Not Allowed`: If not a POST request.
    *   `500 Internal Server Error`: Server configuration error (e.g., JWT_SECRET missing) or unexpected error.
*   **Notes**:
    *   Verifies credentials against `auth_credentials` table.
    *   Checks if the user's email is verified (`is_verified` field).
    *   Generates a JWT token using `jsonwebtoken` with a 1-hour expiry.
    *   Fetches basic profile data (`first_name`, `last_name`, `country_id`, `country_name`) to include in the response.

### 3. Forgot Password

*   **Endpoint**: `POST /api/auth/custom/forgot-password`
*   **Description**: Initiates the password reset process for a user.
*   **Request Body**: `application/json`
    ```json
    {
      "email": "<EMAIL>"
    }
    ```
*   **Responses**:
    *   `200 OK`: If the request is processed (does not confirm if email exists for security).
        ```json
        {
          "success": true,
          "message": "If your email is registered, you will receive a password reset link"
        }
        ```
    *   `400 Bad Request`: Email is required.
        ```json
        {
          "error": "Email is required"
        }
        ```
    *   `405 Method Not Allowed`: If not a POST request.
    *   `500 Internal Server Error`: If an unexpected error occurs (e.g., failed to generate token, email sending failed, or `password_reset_tokens` table not set up).
*   **Notes**:
    *   Checks if the user exists in `auth_credentials`.
    *   Generates a unique reset token (hex string, 32 bytes) and stores it in `password_reset_tokens` table with a 2-hour expiry.
    *   Sends a password reset link to the user's email via Mailtrap.
    *   The `ensureTableExists` function checks for `password_reset_tokens` table; if not found, it suggests manual creation.

### 4. Change Password (Authenticated User)

*   **Endpoint**: `POST /api/auth/custom/change-password`
*   **Description**: Allows an authenticated user to change their password.
*   **Request Body**: `application/json`
    ```json
    {
      "currentPassword": "oldPassword123",
      "newPassword": "newStrongPassword456",
      "email": "<EMAIL>" 
      // userId and userType are in the code but not strictly validated as required in the initial check.
      // "userId": 123, (optional in body, primarily uses email to find user)
      // "userType": "patient" (optional in body)
    }
    ```
*   **Responses**:
    *   `200 OK`: Password updated successfully.
        ```json
        {
          "success": true,
          "message": "Password updated successfully"
        }
        ```
    *   `400 Bad Request`: Missing required fields or new password too short.
        ```json
        {
          "success": false,
          "error": "Missing required fields: currentPassword, newPassword, and email are required"
        }
        ```
        ```json
        {
          "success": false,
          "error": "New password must be at least 8 characters long"
        }
        ```
    *   `401 Unauthorized`: Current password is incorrect.
        ```json
        {
          "success": false,
          "error": "Current password is incorrect"
        }
        ```
    *   `404 Not Found`: User not found.
        ```json
        {
          "success": false,
          "error": "User not found"
        }
        ```
    *   `405 Method Not Allowed`: If not a POST request.
    *   `500 Internal Server Error`: If an unexpected error occurs.
*   **Notes**:
    *   Verifies the `currentPassword` against the hashed password in `auth_credentials`.
    *   Hashes the `newPassword` using bcrypt.
    *   Updates the `hashed_password` in the `auth_credentials` table.

### 5. Resend Verification Email

*   **Endpoint**: `POST /api/auth/custom/resend-verification`
*   **Description**: Resends the email verification link to a user.
*   **Request Body**: `application/json`
    ```json
    {
      "email": "<EMAIL>"
    }
    ```
*   **Responses**:
    *   `200 OK`: If the request is processed (does not confirm if email exists or if already verified, for security).
        ```json
        {
          "success": true,
          "message": "If your email is registered, a new verification link has been sent." 
        }
        ```
        OR
        ```json
        {
          "success": true,
          "message": "A new verification link has been sent to your email"
        }
        ```
    *   `400 Bad Request`: Email is required, or email is already verified.
        ```json
        {
          "success": false,
          "error": "Email is required"
        }
        ```
        ```json
        {
          "success": false,
          "error": "Your email is already verified. Please login."
        }
        ```
    *   `405 Method Not Allowed`: If not a POST request.
    *   `500 Internal Server Error`: If an unexpected error occurs.
*   **Notes**:
    *   Finds the user in `auth_credentials`.
    *   If the user is not verified, it generates a new verification token (UUID v4) and updates the `verification_token` field in `auth_credentials`.
    *   Sends a new verification email via Mailtrap.
    *   The actual verification token storage seems to be intended for `verification_tokens` table (as seen in register), but this endpoint updates a `verification_token` field directly in `auth_credentials`. This might be an inconsistency or an alternative flow.

### 6. Verify Email

*   **Endpoint**: `GET /api/auth/custom/verify-email`
*   **Description**: Verifies a user's email address using a token and userId from the query parameters.
*   **Query Parameters**:
    *   `token` (string): The verification token.
    *   `userId` (string): The user's profile ID.
*   **Responses**:
    *   `302 Found` (Redirect): Redirects to `/verified.html` (for browser requests) or `/verified` (for API clients) with query parameters `verified` (boolean) and `message` (string).
        *   Example Redirect URL for success: `/verified.html?verified=true&message=Your+email+has+been+verified.+You+can+now+log+in.`
        *   Example Redirect URL for failure: `/verified.html?verified=false&message=Invalid+verification+link.+Missing+token+or+userId.`
    *   `405 Method Not Allowed`: If not a GET request.
*   **Notes**:
    *   Checks `auth_credentials` for the user.
    *   If user is already verified, redirects with a success message.
    *   Validates the token against the `verification_tokens` table.
        *   Checks for token existence and expiry.
        *   Deletes the token from `verification_tokens` after successful use.
    *   In development mode (`process.env.NODE_ENV === 'development'`), token validation can be bypassed for testing.
    *   Updates `is_verified` to `true` in `auth_credentials`.
    *   Optionally updates a `verified` field in the `users` or `doctors` table (non-critical).

### 7. Reset Password (using token)

*   **Endpoint**: `POST /api/auth/reset-password` (Note: This is in `app/api/auth/reset-password/route.ts`)
*   **Description**: Completes the password reset process using a token received by email and a new password. Also supports a legacy Supabase Auth flow.
*   **Request Body**: `application/json`
    *   **Custom Token Flow**:
        ```json
        {
          "token": "user_reset_token_here",
          "password": "newStrongPassword123"
        }
        ```
    *   **Legacy Supabase Auth Flow (for backward compatibility)**:
        ```json
        {
          "email": "<EMAIL>",
          "redirectTo": "https://yourapp.com/reset-success" // (optional)
        }
        ```
*   **Responses**:
    *   **Custom Token Flow**:
        *   `200 OK`: Password reset successfully.
            ```json
            {
              "success": true,
              "message": "Password has been reset successfully"
            }
            ```
        *   `400 Bad Request`: Invalid or expired token, or password too short (min 6 characters).
            ```json
            { "error": "Invalid or expired reset token" }
            ```
            ```json
            { "error": "Password must be at least 6 characters long" }
            ```
        *   `500 Internal Server Error`: Failed to update password.
    *   **Legacy Supabase Auth Flow**:
        *   `200 OK`: Supabase Auth reset email sent.
            ```json
            { "success": true, "message": "Reset email sent" }
            ```
        *   `307 Temporary Redirect`: If Supabase Auth fails, may redirect to `/forgot-password` to use custom flow.
            ```json
            { 
              "error": "Password reset using Supabase Auth failed. Please use the custom reset flow.",
              "redirect": "/forgot-password"
            }
            ```
    *   `400 Bad Request`: Invalid request data (e.g., missing token/password or email).
    *   `500 Internal Server Error`: Unhandled server error.
*   **Notes**:
    *   Includes CORS headers.
    *   For custom flow:
        *   Validates the token against `password_reset_tokens` (checks `used` status and `expires_at`).
        *   Hashes the new password with bcrypt.
        *   Updates `hashed_password` in `auth_credentials` based on the email associated with the token.
        *   Marks the token as `used` in `password_reset_tokens`.
    *   The legacy flow calls `supabaseAdmin.auth.resetPasswordForEmail`.

## Review Endpoints

### 1. Submit a Review

*   **Endpoint**: `POST /api/reviews` (Handled by `app/api/reviews/route.ts`)
*   **Description**: Submits a new review for a doctor.
*   **Request Body**: `application/json`
    ```json
    {
      "doctor_id": "doctor_profile_id_here", // string or number
      "clinical_competence": 5, // integer
      "communication_stats": 4, // integer
      "empathy_compassion": 5,  // integer
      "time_management": 3,     // integer
      "follow_up_care": 4,      // integer
      // "recommendation_rating": 5, // Note: Code comments this out as not in schema
      "additional_comments": "Great doctor!", // string (optional)
      "rating": 4.5 // numeric (overall rating for this review)
    }
    ```
*   **Responses**:
    *   `200 OK`: Review submitted successfully.
        ```json
        {
          "success": true,
          "data": [ /* review data inserted */ ] 
        }
        ```
    *   `400 Bad Request`: Missing required fields (e.g., `doctor_id`).
        ```json
        { "error": "Doctor ID is required" }
        ```
    *   `500 Internal Server Error`: Database error or unexpected error during processing.
        ```json
        { "error": "Database error: <message>" }
        ```
        ```json
        { "error": "Failed to process review submission" }
        ```
*   **Notes**:
    *   Authentication: This endpoint does not appear to have explicit authentication checks in the provided code snippet. It's assumed that authentication might be handled by middleware or at a higher level if required.
    *   Inserts the review into the `reviews` table.
    *   After successful insertion, it calls `updateDoctorRating` helper function.
    *   `updateDoctorRating` fetches all reviews for the specified doctor, calculates the new average `rating` and `review_count`, and updates the corresponding record in the `doctors` table.
    *   The code uses `process.env.NEXT_PUBLIC_service_role` for the Supabase client, which implies it's using a service role key. This should be `process.env.SUPABASE_SERVICE_ROLE_KEY` as used in other auth files for consistency and security.

## Connection Test Endpoint

### 1. Test Supabase Connection

*   **Endpoint**: `GET /api/connection-test` (Handled by `app/api/connection-test/route.ts`)
*   **Description**: Tests the connection to the Supabase database.
*   **Request Body**: None.
*   **Responses**:
    *   `200 OK`: Connection status.
        *   On success:
            ```json
            {
              "status": "success",
              "message": "Connected to Supabase successfully",
              "responseTime": 123 // milliseconds
            }
            ```
        *   On failure:
            ```json
            {
              "status": "error",
              "message": "Error message from Supabase or unknown error",
              "responseTime": 45 // milliseconds (or -1 for unknown error)
            }
            ```
*   **Notes**:
    *   Performs a `select count` query on the `countries` table to verify connectivity.
    *   Uses the Supabase client initialized in `lib/supabase-client.ts`.

## Fixtures Endpoint

### 1. Get Fixtures

*   **Endpoint**: `GET /api/fixtures` (Handled by `app/api/fixtures/route.ts`)
*   **Description**: Retrieves a list of mock fixtures.
*   **Query Parameters**:
    *   `status` (string, optional): Filter by fixture status (e.g., `upcoming`, `completed`, `live`).
    *   `specialty` (string, optional, multiple): Filter by specialty. Can be provided multiple times (e.g., `?specialty=Cardiology&specialty=Neurology`).
    *   `type` (string, optional, multiple): Filter by fixture type. Can be provided multiple times (e.g., `?type=Championship&type=Semifinal`).
*   **Request Body**: None.
*   **Responses**:
    *   `200 OK`: Successfully retrieved fixtures.
        ```json
        {
          "fixtures": [
            {
              "id": "1",
              "title": "Cardiology Championship Finals",
              "date": "2025-03-15T14:00:00",
              "location": "Mayo Clinic, Rochester",
              "teams": [
                { "id": "1", "name": "Heart Specialists Team", "score": null },
                { "id": "2", "name": "Cardiac Excellence Group", "score": null }
              ],
              "type": "Championship",
              "specialty": "Cardiology",
              "status": "upcoming"
            }
            // ... more fixtures
          ],
          "count": 10, // Total number of fixtures returned
          "status": "success"
        }
        ```
    *   `500 Internal Server Error`: If an error occurs while fetching fixtures.
        ```json
        {
          "fixtures": [],
          "count": 0,
          "status": "error",
          "message": "Failed to fetch fixtures"
        }
        ```
*   **Notes**:
    *   Currently returns mock data defined within the route handler.
    *   Filters by `status`, `specialty` (case-insensitive partial match), and `type` (case-insensitive partial match).
    *   Sorts fixtures by date: most recent first for `completed` status, soonest first for other statuses.

## Email Setup Endpoint (Informational)

### 1. Setup Email Configuration (Placeholder)

*   **Endpoint**: `POST /api/setup-email` (Handled by `app/api/setup-email/route.ts`)
*   **Description**: Intended for setting up email configuration. **However, this endpoint currently does not perform any actual configuration changes.** It returns a message advising to configure email settings in the Supabase dashboard.
*   **Request Body**: Not specified, as the endpoint doesn't process it for actual setup.
*   **Responses**:
    *   `200 OK`: Returns an informational message.
        ```json
        {
          "success": true,
          "message": "Email configuration updated successfully. Note: You should set email configuration in the Supabase dashboard."
        }
        ```
    *   `500 Internal Server Error`: If an unexpected error occurs (though unlikely given current implementation).
*   **Notes**:
    *   The code includes example Mailtrap SMTP configuration but does not use it to configure Supabase.
    *   It explicitly states that Supabase email configuration should be done via the Supabase dashboard.
    *   The route comments suggest it should be a protected route, but no authentication checks are implemented in the snippet.

## Teams (Hospitals) Endpoint

### 1. Get Hospitals (Misnamed as Teams)

*   **Endpoint**: `GET /api/teams` (Handled by `app/api/teams/route.ts`)
*   **Description**: Retrieves a paginated list of hospitals. Note: The endpoint path is `/api/teams` but it queries the `hospitals` table.
*   **Query Parameters**:
    *   `name` (string, optional): Filter hospitals by name (case-insensitive partial match using LIKE).
    *   `country` (string, optional): Filter hospitals by country (exact match).
    *   `page` (string/number, optional, default: "1"): Page number for pagination. Page size is fixed at 10.
*   **Request Body**: None.
*   **Responses**:
    *   `200 OK`: Successfully retrieved hospitals.
        ```json
        [
          {
            "hospital_id": 1,
            "hospital_name": "General Hospital",
            "country_id": 101,
            "city": "Metropolis",
            "address": "123 Main St",
            // ... other hospital fields
          }
          // ... more hospitals (up to 10 per page)
        ]
        ```
    *   The structure of the response items will match the columns in the `hospitals` table.
*   **Notes**:
    *   Uses a custom `query` function from `lib/db.ts` to interact with the database.
    *   Implements pagination with a page size of 10.
    *   The endpoint path `/api/teams` does not match the data it serves (`hospitals`). This should ideally be corrected to `/api/hospitals` for clarity.

## Web Vitals Endpoint

### 1. Collect Web Vitals

*   **Endpoint**: `POST /api/vitals` (Handled by `app/api/vitals/route.ts`)
*   **Description**: Collects Web Vitals metrics sent from the client.
*   **Request Body**: `application/json`
    ```json
    {
      "name": "FCP", // e.g., LCP, FCP, CLS, TTFB, INP
      "value": 123.45, // Metric value
      "rating": "good" | "needs-improvement" | "poor",
      "url": "/current/page/path" // (optional)
      // ... other web-vitals specific fields
    }
    ```
*   **Responses**:
    *   `200 OK`: Successfully processed the data (or acknowledged it).
        ```json
        { "success": true }
        ```
    *   `500 Internal Server Error`: If an error occurs during processing.
        ```json
        { "error": "Failed to process web vitals data" }
        ```
*   **Notes**:
    *   Includes CORS headers and an `OPTIONS` handler.
    *   In development mode, it only logs the received data.
    *   In non-development environments:
        *   It attempts to store the metrics in a `web_vitals` table in Supabase.
        *   The table schema expected is roughly: `metric_name`, `metric_value`, `metric_rating`, `page_url`, `user_agent`, `timestamp`.
        *   It checks if the `web_vitals` table exists but notes that actual table creation should be done via migrations.
        *   Errors during Supabase interaction are logged but do not necessarily cause the endpoint to fail the client request, aiming for resilience.
    *   Logs the metric regardless of database storage success for monitoring purposes.

## Database Utility Endpoints

These endpoints are typically for administrative or diagnostic purposes.

### 1. Check Password Reset Table Existence

*   **Endpoint**: `GET /api/check-reset-table` (Handled by `pages/api/check-reset-table.ts`)
*   **Description**: Checks if the `password_reset_tokens` table exists and is accessible in the Supabase database.
*   **Request Body**: None.
*   **Responses**:
    *   `200 OK`:
        *   If table exists:
            ```json
            {
              "tableExists": true,
              "message": "Table exists and is accessible"
            }
            ```
        *   If table does not exist or error checking:
            ```json
            {
              "tableExists": false,
              "error": "Error message from Supabase" 
            }
            ```
    *   `405 Method Not Allowed`: If not a GET request.
    *   `500 Internal Server Error`: If an unhandled server error occurs.
*   **Notes**:
    *   Uses a service role Supabase client.
    *   Performs a `select id limit 1` query on `password_reset_tokens`.

### 2. Create Password Reset Table (Utility)

*   **Endpoint**: `POST /api/create-reset-table` (Handled by `pages/api/create-reset-table.ts`)
*   **Description**: Creates the `password_reset_tokens` table, indexes, RLS, policy, and a cleanup function. **This is a utility endpoint intended to be run once and then secured or removed.**
*   **Request Body**: `application/json`
    ```json
    {
      "secret": "your-secret-migration-key" 
    }
    ```
*   **Responses**:
    *   `200 OK`: Table and related objects created successfully.
        ```json
        {
          "success": true,
          "message": "Password reset tokens table created successfully"
        }
        ```
    *   `401 Unauthorized`: If the provided secret is incorrect.
        ```json
        { "error": "Unauthorized" }
        ```
    *   `405 Method Not Allowed`: If not a POST request.
    *   `500 Internal Server Error`: If SQL execution fails or an unhandled server error occurs.
        ```json
        { "error": "SQL execution failed", "details": "..." }
        ```
*   **Notes**:
    *   Requires a `secret` in the request body to authorize the operation. The default secret in the code is `'your-secret-migration-key'`.
    *   Uses `supabaseAdmin.rpc('execute_sql', { sql: "..." })` to execute raw SQL commands.
    *   Creates the `password_reset_tokens` table with columns: `id`, `user_id`, `email`, `token`, `created_at`, `expires_at`, `used`, `user_type`.
    *   Creates indexes on `token`, `user_id`, and `email`.
    *   Enables Row Level Security (RLS) on the table.
    *   Creates an RLS policy allowing access only by `service_role`.
    *   Creates a PostgreSQL function `cleanup_expired_reset_tokens()` to delete expired or used tokens.

### 3. Direct Create Password Reset Table (Utility)

*   **Endpoint**: `POST /api/direct-create-table` (Handled by `pages/api/direct-create-table.ts`)
*   **Description**: Creates the `password_reset_tokens` table and its indexes, and enables RLS. **This is a utility endpoint, likely for development or initial setup, and should be secured or removed in production.**
*   **Request Body**: None explicitly required by the handler logic shown (it does not check `req.body`).
*   **Responses**:
    *   `200 OK`: Table and indexes created successfully.
        ```json
        {
          "success": true,
          "message": "Password reset tokens table created successfully"
        }
        ```
    *   `405 Method Not Allowed`: If not a POST request.
    *   `500 Internal Server Error`: If SQL execution fails or an unhandled server error occurs.
        ```json
        { "error": "Failed to create table", "details": "..." }
        ```
*   **Notes**:
    *   Uses `supabaseAdmin.rpc('run_sql', { query: "..." })` to execute raw SQL. (Note: `/api/create-reset-table` used `execute_sql` RPC).
    *   Creates the `password_reset_tokens` table with the same schema as defined in `/api/create-reset-table`.
    *   Creates indexes on `token`, `user_id`, and `email`.
    *   Enables Row Level Security (RLS) on the table.
    *   Unlike `/api/create-reset-table`, this endpoint does **not**:
        *   Require a secret key for authorization.
        *   Create an RLS policy.
        *   Create the `cleanup_expired_reset_tokens()` function.
    *   This endpoint appears to be a simpler version for table creation, possibly for environments where direct RPC for DDL is preferred or during early development.

### 4. Run Password Reset Table Migration (Utility)

*   **Endpoint**: `POST /api/run-migration` (Handled by `pages/api/run-migration.ts`)
*   **Description**: Executes a migration script to create the `password_reset_tokens` table, indexes, RLS, policy, and cleanup function. **This is a utility endpoint intended to be run once and then secured or removed.**
*   **Request Body**: `application/json`
    ```json
    {
      "secret": "your-secret-migration-key"
    }
    ```
*   **Responses**:
    *   `200 OK`: Migration executed successfully.
        ```json
        {
          "success": true,
          "message": "Migration executed successfully" 
        }
        ```
        (or `Migration executed successfully via direct SQL` if fallback RPC is used)
    *   `401 Unauthorized`: If the provided secret is incorrect.
    *   `405 Method Not Allowed`: If not a POST request.
    *   `500 Internal Server Error`: If migration fails.
        ```json
        { "error": "Failed to run migration: <error_message>" }
        ```
*   **Notes**:
    *   Requires a `secret` in the request body. Default is `'your-secret-migration-key'`.
    *   The SQL script content is similar to that in `/api/create-reset-table`, setting up the `password_reset_tokens` table and related database objects.
    *   Attempts to execute SQL using Supabase RPC:
        1.  `supabaseAdmin.rpc('pgmigrate', { migration: createTableSQL })`
        2.  If that fails, falls back to `supabaseAdmin.rpc('pgmigrate_direct', { query: createTableSQL })`.
    *   This provides an alternative mechanism for the same database setup task as the other utility endpoints.

## User Profile Endpoint

### 1. Get User Profile

*   **Endpoint**: `GET /api/user/profile` (Handled by `pages/api/user/profile.ts`)
*   **Description**: Retrieves the profile of the currently authenticated user.
*   **Authentication**: Required (JWT Bearer token in `Authorization` header).
*   **Request Body**: None.
*   **Responses**:
    *   `200 OK`: Successfully retrieved profile data.
        ```json
        {
          "success": true,
          "profile": {
            // Profile fields from 'users' or 'doctors' table
            // e.g., for a patient:
            // "id": 123, 
            // "email": "<EMAIL>",
            // "username": "johndoe", ...
            // e.g., for a doctor:
            // "id": 456,
            // "email": "<EMAIL>",
            // "fullname": "Dr. Jane Smith", ...
          }
        }
        ```
    *   `401 Unauthorized`: If authentication fails (handled by `withAuth` middleware).
    *   `404 Not Found`: If the user's profile is not found in the database.
        ```json
        { "success": false, "error": "patient profile not found." } 
        ```
    *   `405 Method Not Allowed`: If not a GET request.
    *   `500 Internal Server Error`: If an error occurs fetching data or an unexpected server error.
*   **Notes**:
    *   Uses the `withAuth` HOC for authentication, which decodes the JWT to get `userId` and `userType`.
    *   Fetches all columns (`select('*')`) from the `users` table (if `userType` is 'patient') or `doctors` table (if `userType` is 'doctor').
    *   The code has `const idColumn = userType === 'patient' ? 'id' : 'id';`. This implies the ID column is named `id` in both tables for this query. However, the database schema shows `user_id` for `users` and `doctor_id` for `doctors`. This might be an inconsistency or `id` is an alias or primary key that's consistent. Based on `auth_credentials.user_profile_id` being an integer, it's likely matching against the primary key of `users` or `doctors` table which might be just `id`.

## Server Actions

Server Actions are functions that run on the server and can be called from server or client components. They are defined in the `actions/` directory.

### Ad Actions (`actions/ad-actions.ts`)

These actions manage advertisements. They typically use a Supabase service role client for database operations.

#### 1. `getAds()`

*   **Description**: Fetches all ads. Intended for admin use.
*   **Parameters**: None.
*   **Returns**: `Promise<{ data: Ad[] | null; error: any }>`
    *   `data`: An array of `Ad` objects or `null` on error.
    *   `error`: Error object if an issue occurred.
*   **Notes**: Uses service role client to bypass RLS. Orders ads by `created_at` descending.

#### 2. `getAdsForPage(pageName: string, position: string)`

*   **Description**: Fetches active ads for a specific page name and position (e.g., 'home', 'banner').
*   **Parameters**:
    *   `pageName` (string): Name of the page (e.g., 'home', 'about', 'standings').
    *   `position` (string): Position of the ad on the page (e.g., 'banner', 'sidebar', 'bottom').
*   **Returns**: `Promise<{ data: Ad[] | null; error: any }>`
*   **Notes**:
    *   Uses service role client.
    *   Filters ads by `status: 'active'`.
    *   Matches `placement` (exact string like `pageName:position`) or `placements` (array containing string like `pageName:position`).
    *   Handles page name variations (e.g., 'home', 'homepage').
    *   Orders ads by `created_at` descending.
    *   This is a generic function used by many specific ad placement fetchers (e.g., `getHomepageBannerAds`, `getAboutUsSidebarAds`).

#### 3. Specific Ad Placement Fetchers

*   **Description**: A collection of functions that wrap `getAdsForPage` for predefined page/position combinations.
*   **Examples**:
    *   `getHomepageBannerAds()`
    *   `getHomepageSidebarAds()`
    *   `getAboutUsBannerAds()`
    *   `getStandingsSidebarAds()`
    *   ...and many more for different pages and positions.
*   **Parameters**: None (page and position are hardcoded).
*   **Returns**: `Promise<{ data: Ad[] | null; error: any }>`

#### 4. `createAd(adData: AdInsert)`

*   **Description**: Creates a new ad.
*   **Parameters**:
    *   `adData` (`AdInsert` object): Data for the new ad.
        ```typescript
        // AdInsert type example (key fields)
        // {
        //   title: string;
        //   target_url: string;
        //   start_date: string; // date string
        //   status?: string; // 'draft', 'active', 'inactive', etc.
        //   description?: string | null;
        //   end_date?: string | null; // date string
        //   budget?: number | null;
        //   size?: string | null; // e.g., "300x250"
        //   target_specialty_id?: number | null;
        //   target_locations?: string[] | null; // Array of location strings
        //   media_url?: string | null;
        //   media_type?: string | null; // 'image', 'video'
        //   placement: string | string[] | null; // Form input for placement(s)
        //   custom_top?: string | null;
        //   custom_bottom?: string | null;
        //   custom_left?: string | null;
        //   custom_right?: string | null;
        // }
        ```
*   **Returns**: `Promise<{ data: Ad | null; error: any }>`
    *   `data`: The created `Ad` object or `null` on error.
*   **Notes**:
    *   Uses service role client.
    *   Handles conversion of `placement` form input (string or array) to database fields `placement` (string) and `placements` (array).
    *   Revalidates paths `/admin/ads` and `/` upon success.

#### 5. `updateAd(adId: string, adData: AdUpdate)`

*   **Description**: Updates an existing ad.
*   **Parameters**:
    *   `adId` (string): The ID of the ad to update.
    *   `adData` (`AdUpdate` object): Data to update. Only provided fields are updated.
        ```typescript
        // AdUpdate type example (key fields, id is mandatory)
        // {
        //   id: string; 
        //   title?: string;
        //   target_url?: string;
        //   // ... other AdInsert fields are optional
        //   placement?: string | string[] | null; // Form input for placement(s)
        // }
        ```
*   **Returns**: `Promise<{ data: Ad | null; error: any }>`
    *   `data`: The updated `Ad` object or `null` on error.
*   **Notes**:
    *   Uses service role client.
    *   Selectively updates fields present in `adData`.
    *   Handles `placement` conversion similar to `createAd`.
    *   Revalidates paths `/admin/ads` and `/` upon success.

#### 6. `deleteAd(adId: string)`

*   **Description**: Deletes an ad.
*   **Parameters**:
    *   `adId` (string): The ID of the ad to delete.
*   **Returns**: `Promise<{ error: any }>`
    *   `error`: Error object if an issue occurred, `null` on success.
*   **Notes**:
    *   Uses service role client.
    *   Revalidates paths `/admin/ads` and `/` upon success.

#### 7. `updateAdStatus(adId: string, newStatus: Ad['status'])`

*   **Description**: Updates the status of an ad.
*   **Parameters**:
    *   `adId` (string): The ID of the ad.
    *   `newStatus` (`Ad['status']`): The new status string (e.g., 'active', 'inactive').
*   **Returns**: `Promise<{ data: Ad | null; error: any }>` (by calling `updateAd`).
*   **Notes**: This is a convenience wrapper around `updateAd`.

#### 8. `uploadAdMedia(formData: FormData)`

*   **Description**: Uploads media (image or video) for an ad to Supabase storage.
*   **Parameters**:
    *   `formData` (`FormData`): Must contain a file under the key `mediaFile`.
*   **Returns**: `Promise<{ mediaUrl: string | null; mediaType: string | null; error: any }>`
    *   `mediaUrl`: Public URL of the uploaded file.
    *   `mediaType`: Detected media type ('image', 'video').
    *   `error`: Error object if upload fails.
*   **Notes**:
    *   Uses service role client for Supabase storage operations.
    *   Uploads to a bucket named `ad-media`.
    *   Generates a UUID for the file name to ensure uniqueness.
    *   Validates file type (allows 'image/*' or 'video/*').

### Doctor Registration Actions (`actions/doctor-registration-actions.ts`)

These actions are specific to the doctor registration flow.

#### 1. `createDoctorAuthUser(email: string, password: string, fullName: string)`

*   **Description**: Creates an authentication user for a doctor using Supabase admin privileges.
*   **Parameters**:
    *   `email` (string): Doctor's email.
    *   `password` (string): Doctor's chosen password.
    *   `fullName` (string): Doctor's full name (stored in user metadata).
*   **Returns**: `Promise<{ user: User | null; error: { message: string; code?: string; details?: any } | null }>`
    *   `user`: Supabase Auth user object on success.
    *   `error`: Error object if creation fails (e.g., email already exists).
*   **Notes**:
    *   Uses service role client.
    *   Sets `email_confirm: false` initially.
    *   Sets `user_metadata: { full_name, role: "doctor" }`.

#### 2. `resendDoctorVerificationEmail(email: string)`

*   **Description**: Resends the verification email to a doctor.
*   **Parameters**:
    *   `email` (string): Doctor's email.
*   **Returns**: `Promise<{ error: { message: string; details?: any } | null }>`
*   **Notes**:
    *   Uses a standard Supabase client (anon key).
    *   Calls `supabaseClient.auth.resend()` with `type: 'signup'`.
    *   `emailRedirectTo` is configured to `${NEXT_PUBLIC_BASE_URL}/auth/verification-success`.

#### 3. `uploadDoctorProfileImage(userId: string, file: File)`

*   **Description**: Uploads a doctor's profile image, performs client-side optimization, and updates the doctor's profile.
*   **Parameters**:
    *   `userId` (string): The doctor's user ID (likely auth_id).
    *   `file` (`File` object): The image file to upload.
*   **Returns**: `Promise<{ profileImageUrl: string | null; error: any }>`
*   **Notes**:
    *   Uses service role client.
    *   **Client-side optimization**: If run in a browser environment, it attempts to resize the image (max 800x800) and convert it to WebP format before uploading. If optimization fails, it uses the original file.
    *   Uploads to Supabase storage bucket `profile-images` under `doctor-profiles/${userId}-${timestamp}.${fileExt}`.
    *   After successful upload, it calls `updateDoctorProfileImage` to save the image path to the doctor's record.

#### 4. `updateDoctorProfileImage(userId: string, imagePath: string)`

*   **Description**: Updates the `profile_image` field in the `doctors` table.
*   **Parameters**:
    *   `userId` (string): The doctor's ID (can be auth_id UUID or numeric doctor_id).
    *   `imagePath` (string): The exact path of the image in Supabase storage.
*   **Returns**: `Promise<{ success: boolean; error: any }>`
*   **Notes**:
    *   Uses service role client.
    *   Attempts to update the `doctors` table first by `auth_id`, then by `doctor_id` if the first attempt fails and `userId` is a parsable integer.

#### 5. `createDoctorProfile(userId: string, profileData: DoctorProfileData)`

*   **Description**: Creates a doctor's profile in the `doctors_registration` table.
*   **Parameters**:
    *   `userId` (string): The doctor's auth_id (UUID).
    *   `profileData` (`DoctorProfileData` object): Contains various profile details for the `doctors_registration` table (e.g., `fullname`, `specialty`, `experience`, etc., excluding `auth_id`, `doctor_id`, `last_updated`).
*   **Returns**: `Promise<{ error: any }>`
*   **Notes**:
    *   Uses service role client.
    *   Generates a sequential `doctor_id` for the `doctors_registration` table.
    *   Links the profile to the auth user via `auth_id`.
    *   Sets `last_updated` to the current timestamp.

### Review Actions (`actions/review-actions.ts`)

These actions handle review submissions.

#### 1. `submitReview(formData: FormData)`

*   **Description**: Submits a review for a doctor.
*   **Parameters**:
    *   `formData` (`FormData` object): Contains the review data. Expected fields:
        *   `user_id`: (string | number) ID of the user submitting the review.
        *   `doctor_id`: (string) ID of the doctor being reviewed.
        *   `clinical_competence`: (string representing a number) Rating for clinical competence.
        *   `communication_stats`: (string representing a number) Rating for communication.
        *   `empathy_compassion`: (string representing a number) Rating for empathy.
        *   `time_management`: (string representing a number) Rating for time management.
        *   `follow_up_care`: (string representing a number) Rating for follow-up care.
        *   `overall_satisfaction`: (string representing a number) Overall satisfaction rating.
        *   `recommendation_rating`: (string representing a number, 1-5) Recommendation rating.
        *   `additional_comments`: (string, optional) Text comments.
*   **Returns**: `Promise<{ success: boolean; error?: string }>`
*   **Notes**:
    *   Uses a service role Supabase client.
    *   The `user_id` is taken directly from form data and is trusted (client-side verification assumed).
    *   Calculates an average `rating` from the individual criteria.
    *   Checks for existing reviews by the same user for the same doctor to prevent duplicates.
    *   Inserts the review into the `reviews` table. The `recommendation_rating` is stored in the `Recommendation` column.
    *   Calls an internal `updateDoctorRating(doctorId)` helper function.
        *   This helper recalculates the average `rating` and `review_count` for the doctor in the `doctors` table based on all their valid reviews.
    *   Revalidates paths `/doctors/${doctorId}` and `/doctors/${doctorId}/reviews` on success.

---

This concludes the documentation for the identified API routes and server actions. If other endpoints or actions are discovered or added, they should be documented similarly.
