# Backend Architecture Overview

This document describes the backend architecture of the Doctors Leagues application, focusing on server-side logic, data processing, API structure, and database interactions.

## 1. Core Technologies

*   **Next.js**: Used as the full-stack framework. The backend leverages:
    *   **API Routes**: Both in the `pages/api/` directory (traditional Next.js API routes) and the `app/api/` directory (App Router Route Handlers).
    *   **Server Actions**: Functions defined with `'use server'` that execute on the server, often used for form submissions and data mutations.
*   **Node.js**: The underlying runtime environment for Next.js server-side code.
*   **TypeScript**: The backend codebase is written in TypeScript.
*   **Supabase**: Used as the primary database (PostgreSQL) and for some BaaS functionalities (like Auth user management, though heavily customized, and Storage).

## 2. API Layer

The application exposes backend logic through a combination of traditional API routes and Server Actions.

### 2.1. API Routes

*   **Location**:
    *   `pages/api/`: Contains several API routes, particularly for authentication (`pages/api/auth/custom/`) and database utility tasks. These follow the traditional Next.js API route pattern (exporting a default handler function).
    *   `app/api/`: Contains newer API routes built with the App Router (Route Handlers), where files like `route.ts` export functions for HTTP methods (e.g., `GET`, `POST`).
*   **Functionality**:
    *   **Authentication**: Custom logic for registration, login, password reset, email verification (see `docs/api-documentation.md` - Authentication Endpoints).
    *   **Data Retrieval**: Endpoints for fetching data like reviews, hospitals (misnamed as teams), fixtures (mock data).
    *   **Data Submission**: Endpoint for submitting reviews.
    *   **Utility/Diagnostic**: Endpoints for checking database table existence, running migrations (development/setup utilities), testing Supabase connection, and collecting web vitals.
*   **Authentication/Authorization**:
    *   Some API routes are protected using the `withAuth` higher-order component from `lib/auth/api-auth-utils.ts`. This HOC checks for a JWT Bearer token in the `Authorization` header and verifies it.
    *   Utility endpoints (like table creation) sometimes use a simple shared secret passed in the request body for authorization, intended for controlled environments.

### 2.2. Server Actions

*   **Location**: Defined in files within the `actions/` directory (e.g., `ad-actions.ts`, `doctor-registration-actions.ts`, `review-actions.ts`).
*   **Declaration**: Marked with the `'use server'` directive.
*   **Functionality**:
    *   Primarily used for data mutations (Create, Update, Delete operations) and complex queries that benefit from being close to the database.
    *   Examples: Creating/updating/deleting ads, managing doctor registrations, submitting reviews (alternative to the API route for reviews).
    *   File uploads (e.g., `uploadAdMedia`, `uploadDoctorProfileImage`) are also handled via server actions interacting with Supabase Storage.
*   **Authentication/Authorization**:
    *   Server Actions can access user session information (e.g., via `createServerActionClient` from `@supabase/auth-helpers-nextjs` or custom logic).
    *   Many actions use a service role Supabase client (`getServiceRoleClient` or `createServiceRoleClient`) to perform operations that might require bypassing RLS or have admin-level privileges. This is common for actions related to ads management or doctor registration steps initiated by admins or system processes.
*   **Data Revalidation**: Server Actions often use `revalidatePath` from `next/cache` to update client-side caches after data mutations.

## 3. Database Interaction

*   **Supabase (PostgreSQL)**: The primary database.
*   **Supabase Client**:
    *   **Service Role Client**: Used extensively in API routes and Server Actions for operations requiring elevated privileges or bypassing RLS. Initialized using `process.env.SUPABASE_SERVICE_ROLE_KEY`.
        *   Example helper: `createServiceRoleClient()` in `lib/supabase-client.ts` or defined locally in action files.
    *   **Authenticated Client / Anon Key Client**:
        *   `createServerActionClient` from `@supabase/auth-helpers-nextjs` is used in some server actions to get a client scoped to the current user's session (if available).
        *   Standard client with an anonymous key (`process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY`) is used for public operations like resending verification emails.
    *   `lib/db.ts`: Contains a custom `query` function for raw SQL execution, used by some older API routes (e.g., `/api/teams`). This is less common than using the Supabase client's query builder.
*   **Data Access Patterns**:
    *   Direct queries using the Supabase JavaScript client's query builder (e.g., `supabase.from('table').select()...`).
    *   RPC calls for executing database functions (e.g., `supabaseAdmin.rpc('execute_sql', ...)` for migrations).
*   **Schema Management**:
    *   TypeScript types for the database are generated (likely from Supabase CLI) and stored in `lib/database.types.ts`.
    *   Utility API routes (`/api/create-reset-table`, `/api/run-migration`) exist for creating specific tables (e.g., `password_reset_tokens`) programmatically, intended for setup or development. These are not a full migration system.

## 4. Authentication and Authorization (Backend)

*   **Custom JWT-based System**:
    *   Login endpoint (`/api/auth/custom/login`) validates credentials against `auth_credentials` table and issues a JWT.
    *   JWTs are signed using `jsonwebtoken` with a secret from `process.env.JWT_SECRET`.
*   **Token Verification**:
    *   **API Routes**: The `withAuth` HOC (`lib/auth/api-auth-utils.ts`) verifies JWTs from the `Authorization: Bearer <token>` header.
    *   **Middleware**: `middleware.ts` (root level) verifies JWTs stored in an `auth_token` cookie for page access control. It uses `jose` library for JWT verification.
*   **User Roles**:
    *   The system distinguishes between 'patient' and 'doctor' user types, stored in `auth_credentials.user_type` and included in the JWT payload.
    *   Backend logic (e.g., fetching profiles in `/api/user/profile`) uses `userType` to query appropriate tables.
*   **Service Role Access**: Many backend operations (especially in Server Actions and utility API routes) use the Supabase service role key to bypass RLS for administrative tasks or when user-specific RLS is not applicable.
    *   **Audit Recommendation**: Regularly audit the use of service role keys. Where possible, prefer user-scoped Supabase clients combined with robust Row Level Security (RLS) policies. For complex operations like updating aggregated data (e.g., doctor ratings after a new review), consider using PostgreSQL triggers or security definer functions which can execute with elevated privileges within the database, rather than relying on service role in application code for all parts of the transaction.
    *   **Specific Concern**: The API route `/app/api/reviews/route.ts` uses `process.env.NEXT_PUBLIC_service_role` which is incorrect and a security risk if it points to the actual service key. It should use `process.env.SUPABASE_SERVICE_ROLE_KEY`. Furthermore, this route lacks explicit authentication checks for review submission, which should be added if it's an active endpoint. The `actions/review-actions.ts` server action `submitReview` is a more robust way to handle this and now includes server-side authentication.

## 5. Key Backend Services and Libraries

*   **`bcryptjs`**: Used for hashing passwords before storing them and for comparing passwords during login.
*   **`jsonwebtoken`**: Used for creating and signing JWTs during login.
*   **`jose`**: Used in `middleware.ts` for verifying JWTs from cookies.
*   **`nodemailer`**: Used for sending emails (e.g., verification, password reset) via an SMTP provider (Mailtrap is configured in examples).
*   **`uuid`**: Used for generating UUIDs (e.g., for verification tokens, file names).
*   **Supabase Client Libraries**: `@supabase/supabase-js`, `@supabase/auth-helpers-nextjs`.

## 6. Error Handling and Logging

*   **`try...catch` blocks**: Standard mechanism for error handling in API routes and Server Actions.
*   **Logging**: `console.log` and `console.error` are used for logging information and errors. No centralized logging framework is apparent from the reviewed files, but Next.js itself provides server-side logging.
*   **HTTP Status Codes**: API routes return appropriate HTTP status codes (e.g., 200, 201, 400, 401, 403, 404, 405, 500) with JSON error messages.

## 7. Environment Variables

The backend relies heavily on environment variables for configuration:
*   `NEXT_PUBLIC_SUPABASE_URL`, `NEXT_PUBLIC_SUPABASE_ANON_KEY`: For client-side Supabase access.
*   `SUPABASE_SERVICE_ROLE_KEY`: For backend Supabase admin access.
*   `JWT_SECRET`: For signing and verifying JWTs.
*   `MAILTRAP_USER`, `MAILTRAP_PASS`: For SMTP email sending.
*   `NEXT_PUBLIC_SITE_URL` or `NEXT_PUBLIC_BASE_URL`: For constructing absolute URLs in emails.

This overview provides a high-level understanding of the backend architecture. For specifics on individual endpoints or actions, refer to `docs/api-documentation.md` and the source code.
