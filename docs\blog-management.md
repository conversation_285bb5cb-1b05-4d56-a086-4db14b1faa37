# Blog Management System Documentation

A comprehensive, reusable system for creating and managing blog posts on the Doctors League platform.

## Table of Contents

1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [Quick Start Guide](#quick-start-guide)
4. [Configuration](#configuration)
5. [Creating Blog Posts](#creating-blog-posts)
6. [Image Management](#image-management)
7. [Tag Management](#tag-management)
8. [API Reference](#api-reference)
9. [Examples](#examples)
10. [Troubleshooting](#troubleshooting)
11. [Best Practices](#best-practices)

## Overview

The Blog Management System provides a standardized, automated way to create blog posts without coding from scratch each time. It includes:

- **Input validation** against predefined rules
- **Automatic content processing** with image insertion
- **Tag creation and linking**
- **SEO optimization** features
- **Reading time calculation**
- **Error handling and logging**

### Key Benefits

- ✅ **Consistency**: Standardized format and validation
- ✅ **Efficiency**: No more coding from scratch
- ✅ **Flexibility**: Support for images, tags, and rich content
- ✅ **Safety**: Comprehensive validation and error handling
- ✅ **Maintainability**: Centralized configuration

## System Architecture

```
scripts/blog-management/
├── blog-config.json          # Configuration and settings
├── create-blog-post.js       # Main blog creation script
└── blog-post-template.json   # Template with examples

docs/
└── blog-management.md        # This documentation
```

### Dependencies

- Node.js with ES6+ support
- `@supabase/supabase-js`
- `dotenv`
- Access to `.env.local` with Supabase credentials

## Quick Start Guide

### 1. Basic Usage - Command Line

```bash
# Navigate to the blog management directory
cd scripts/blog-management

# Create a blog post from JSON file
node create-blog-post.js my-blog-data.json
```

### 2. Basic Usage - Programmatic

```javascript
const BlogPostManager = require('./scripts/blog-management/create-blog-post.js')

const manager = new BlogPostManager()

const blogData = {
  title: "Understanding Telemedicine",
  slug: "understanding-telemedicine",
  content: "<h1>Understanding Telemedicine</h1><p>Content here...</p>",
  category_slug: "technology",
  author_slug: "editorial-team"
}

try {
  const post = await manager.createBlogPost(blogData)
  console.log('Blog post created:', post.id)
} catch (error) {
  console.error('Error:', error.message)
}
```

### 3. Using the Template

1. Copy `blog-post-template.json` to a new file
2. Fill in your content
3. Run the creation script

```bash
cp scripts/blog-management/blog-post-template.json my-post.json
# Edit my-post.json with your content
node scripts/blog-management/create-blog-post.js my-post.json
```

## Configuration

### blog-config.json Structure

```json
{
  "database": {
    "url_env_var": "NEXT_PUBLIC_SUPABASE_URL",
    "service_key_env_var": "SUPABASE_SERVICE_ROLE_KEY"
  },
  "categories": [...],
  "authors": [...],
  "default_settings": {...},
  "validation": {...}
}
```

### Available Categories

- `ranking-analysis-insights` - Data-driven analysis of medical professional rankings, trends, and performance insights
- `medical-deep-dives` - In-depth educational content explaining medical procedures, technologies, and treatments
- `doctor-institution-spotlights` - Interviews and features highlighting leading medical professionals and institutions
- `patient-caregiver-resources` - Practical guides and resources for patients navigating healthcare decisions
- `doctor-insights` - Expert opinions and professional insights from medical practitioners
- `patient-stories` - Real patient experiences and testimonials

### Available Authors

- `editorial-team` - Our expert editorial team

### Validation Rules

| Field | Min Length | Max Length | Pattern | Required |
|-------|-----------|------------|---------|----------|
| title | 10 | 200 | - | ✅ |
| slug | - | 100 | `^[a-z0-9-]+$` | ✅ |
| content | 100 | - | - | ✅ |
| excerpt | - | 300 | - | ❌ |
| meta_title | - | 60 | - | ❌ |
| meta_description | - | 160 | - | ❌ |

## Creating Blog Posts

### Required Fields

Every blog post must include:

```json
{
  "title": "Your Blog Post Title",
  "slug": "your-blog-post-slug", 
  "content": "<h1>Title</h1><p>Content...</p>",
  "category_slug": "patient-caregiver-resources",
  "author_slug": "editorial-team"
}
```

### Optional Enhancements

```json
{
  "excerpt": "Brief summary...",
  "meta_title": "SEO title",
  "meta_description": "SEO description",
  "meta_keywords": ["keyword1", "keyword2"],
  "featured_image_url": "https://example.com/image.jpg",
  "featured_image_alt": "Image description",
  "is_featured": true,
  "is_trending": false,
  "tags": ["tag1", "tag2"],
  "images": [...]
}
```

### Content Guidelines

1. **HTML Format**: Use proper HTML tags (`<h1>`, `<p>`, `<ul>`, etc.)
2. **Semantic Structure**: Use heading hierarchy (h1 → h2 → h3)
3. **Responsive Images**: Images will be automatically made responsive
4. **Links**: Use absolute URLs for external links

## Image Management

### Featured Images

```json
{
  "featured_image_url": "https://uapbzzscckhtptliynyj.supabase.co/storage/v1/object/public/blog/image.jpg",
  "featured_image_alt": "Descriptive alt text"
}
```

### Content Images

Insert images at specific positions in your content:

```json
{
  "images": [
    {
      "url": "https://example.com/image.jpg",
      "alt": "Image description",
      "caption": "Optional caption text",
      "position": {
        "type": "after_heading",
        "heading": "Chapter Title"
      }
    }
  ]
}
```

### Supported Position Types

- `after_heading`: Insert after a specific heading

### Image Best Practices

- Use descriptive alt text for accessibility
- Optimize images for web (WebP format recommended)
- Include captions when helpful for context
- Use consistent styling (automatically applied)

## Tag Management

### Automatic Tag Creation

Tags are automatically created if they don't exist:

```json
{
  "tags": ["oncology", "immunotherapy", "patient care"]
}
```

### Tag Naming Conventions

- Use lowercase
- Separate words with spaces (system converts to slugs)
- Be specific but not overly narrow
- Aim for 3-5 tags per post

### Popular Tag Categories

- **Medical Specialties**: oncology, cardiology, neurology
- **Treatment Types**: surgery, medication, therapy
- **Patient Types**: pediatric, geriatric, chronic-care
- **Technologies**: telemedicine, AI, robotics

## API Reference

### BlogPostManager Class

#### Constructor
```javascript
const manager = new BlogPostManager()
```

#### Methods

##### `createBlogPost(blogData)`
Creates a new blog post from data object.

**Parameters:**
- `blogData` (Object): Blog post data following the template structure

**Returns:**
- Promise resolving to created blog post object

**Throws:**
- Validation errors if data is invalid
- Database errors if creation fails

##### `createFromFile(filePath)`
Creates a blog post from a JSON file.

**Parameters:**
- `filePath` (String): Path to JSON file containing blog data

**Returns:**
- Promise resolving to created blog post object

##### `validateBlogData(blogData)`
Validates blog post data against configuration rules.

**Parameters:**
- `blogData` (Object): Blog post data to validate

**Returns:**
- Array of validation error messages (empty if valid)

## Examples

### Example 1: Simple News Article

```json
{
  "title": "New FDA Approval for Heart Disease Treatment",
  "slug": "new-fda-approval-heart-disease-treatment",
  "content": "<h1>New FDA Approval</h1><p>The FDA has approved...</p>",
  "category_slug": "patient-caregiver-resources",
  "author_slug": "editorial-team",
  "excerpt": "FDA approves breakthrough treatment for heart disease patients.",
  "tags": ["FDA", "heart disease", "treatment", "approval"]
}
```

### Example 2: Medical Deep Dive with Images

```json
{
  "title": "Understanding Diabetes: A Complete Guide",
  "slug": "understanding-diabetes-complete-guide",
  "content": "<h1>Understanding Diabetes</h1><h2>What is Diabetes?</h2><p>Content...</p><h2>Treatment Options</h2><p>More content...</p>",
  "category_slug": "medical-deep-dives",
  "author_slug": "editorial-team",
  "excerpt": "Comprehensive guide to diabetes symptoms, treatment, and management.",
  "meta_title": "Diabetes Guide: Symptoms, Treatment & Management",
  "meta_description": "Learn about diabetes types, symptoms, and treatment options. Complete patient guide from medical experts.",
  "featured_image_url": "https://example.com/diabetes-featured.jpg",
  "featured_image_alt": "Blood glucose monitoring device",
  "is_featured": true,
  "tags": ["diabetes", "endocrinology", "patient education"],
  "images": [
    {
      "url": "https://example.com/glucose-meter.jpg",
      "alt": "Modern glucose meter device",
      "caption": "Modern glucose monitoring technology",
      "position": {
        "type": "after_heading",
        "heading": "Treatment Options"
      }
    }
  ]
}
```

### Example 3: Programmatic Creation

```javascript
const BlogPostManager = require('./scripts/blog-management/create-blog-post.js')

async function createMultiplePosts() {
  const manager = new BlogPostManager()
  
  const posts = [
    {
      title: "Telemedicine Benefits",
      slug: "telemedicine-benefits",
      content: "<h1>Benefits</h1><p>Content...</p>",
      category_slug: "doctor-insights",
      author_slug: "editorial-team"
    },
    {
      title: "Patient Safety Guidelines", 
      slug: "patient-safety-guidelines",
      content: "<h1>Safety</h1><p>Content...</p>",
      category_slug: "patient-caregiver-resources",
      author_slug: "editorial-team"
    }
  ]
  
  for (const postData of posts) {
    try {
      const post = await manager.createBlogPost(postData)
      console.log(`Created: ${post.title}`)
    } catch (error) {
      console.error(`Failed to create ${postData.title}:`, error.message)
    }
  }
}

createMultiplePosts()
```

## Troubleshooting

### Common Error Messages

#### "Missing required environment variables"
**Cause**: `.env.local` file missing or doesn't contain Supabase credentials
**Solution**: 
1. Ensure `.env.local` exists in project root
2. Check that `NEXT_PUBLIC_SUPABASE_URL` and `SUPABASE_SERVICE_ROLE_KEY` are set

#### "Category not found: [slug]"
**Cause**: Invalid category_slug in blog data
**Solution**: Use one of the valid category slugs from the configuration

#### "Validation failed: [field] must be at least [x] characters"
**Cause**: Field doesn't meet minimum length requirements
**Solution**: Ensure content meets the validation rules in the configuration

#### "Failed to create blog post: duplicate key value"
**Cause**: Blog post with the same slug already exists
**Solution**: Use a unique slug or check existing posts

### Debugging Tips

1. **Enable verbose logging**: Set `NODE_ENV=development` for detailed logs
2. **Check database connectivity**: Test Supabase connection separately
3. **Validate JSON**: Use a JSON validator for template files
4. **Test with minimal data**: Start with required fields only

### Performance Considerations

- **Large content**: Break very long posts into multiple parts
- **Many images**: Consider image optimization before upload
- **Batch operations**: Use programmatic creation for multiple posts

## Best Practices

### Content Writing

1. **SEO Optimization**:
   - Include target keywords in title and meta description
   - Use semantic HTML structure
   - Write compelling meta descriptions under 160 characters

2. **Readability**:
   - Use clear, descriptive headings
   - Break content into digestible sections
   - Include bullet points and lists where appropriate

3. **Medical Content**:
   - Ensure accuracy with medical review
   - Include disclaimers where appropriate
   - Link to authoritative sources

### Technical Best Practices

1. **File Organization**:
   ```
   blog-posts/
   ├── drafts/
   │   ├── diabetes-guide.json
   │   └── telemedicine-update.json
   └── published/
       ├── 2024-01-15-car-t-therapy.json
       └── 2024-01-20-heart-disease.json
   ```

2. **Version Control**:
   - Keep blog data files in version control
   - Use descriptive commit messages
   - Tag releases for major content updates

3. **Backup Strategy**:
   - Regular database backups
   - Export blog data periodically
   - Keep local copies of images

### Content Strategy

1. **Categories Usage**:
   - **Ranking Analysis & Insights**: Data-driven analysis of medical professional rankings and trends
   - **Medical Deep Dives**: Comprehensive, research-backed articles
   - **Doctor & Institution Spotlights**: Features highlighting leading medical professionals
   - **Patient & Caregiver Resources**: Practical guides for healthcare navigation
   - **Doctor Insights**: Expert opinions and commentary
   - **Patient Stories**: Real experiences and testimonials

2. **Publishing Schedule**:
   - Plan content calendar in advance
   - Balance categories for diverse content
   - Consider seasonal health topics

3. **SEO Strategy**:
   - Research keywords before writing
   - Optimize for featured snippets
   - Build internal linking between related posts

### Quality Assurance

1. **Pre-Publication Checklist**:
   - [ ] All required fields completed
   - [ ] Content spell-checked and grammar-checked
   - [ ] Images optimized and accessible
   - [ ] Links tested and functional
   - [ ] SEO elements optimized
   - [ ] Medical accuracy verified

2. **Post-Publication**:
   - Monitor for errors or broken links
   - Track performance metrics
   - Update content as needed

---

## Support and Maintenance

For questions or issues with the Blog Management System:

1. Check this documentation first
2. Review error messages and troubleshooting section
3. Check the configuration file for valid options
4. Test with the minimal example template

### Future Enhancements

Planned improvements to the system:

- [ ] Support for draft status and scheduled publishing
- [ ] Automatic image optimization
- [ ] Content templates for different post types
- [ ] Integration with external content management tools
- [ ] Automated SEO analysis and suggestions

---

*Last updated: [Current Date]*
*Version: 1.0.0* 