# Data Migration and Seeding Strategy

This document outlines the strategy for managing database schema migrations and populating initial (seed) data for the Doctors Leagues application.

## 1. Overview

The project uses a combination of Supabase CLI-managed migrations and some standalone SQL scripts for database schema management. Seed data is likely handled by a dedicated SQL script.

## 2. Schema Migrations

### 2.1. Supabase CLI Migrations

*   **Location**: The `migrations/` directory at the root of the project contains timestamped SQL files. This is the standard location for migrations managed by the Supabase CLI.
*   **Naming Convention**: Files follow the `YYYYMMDDHHMMSS_descriptive_name.sql` pattern (e.g., `20230906000001_create_auth_credentials_table.sql`).
*   **Process**:
    1.  **Local Development**: When making schema changes locally, developers should use the Supabase CLI to generate new migration files.
        *   Example: `supabase db diff -f my_new_migration_name` (to generate a diff)
        *   Or, more commonly, developers make changes via Supabase Studio (local or remote) and then pull these changes: `supabase db remote commit` (if changes made on remote) or `supabase db diff --schema public -f migration_name` after local changes.
    2.  **Applying Migrations**:
        *   Locally: `supabase start` (if using local Supabase stack) often applies migrations, or `supabase db reset` for a clean slate.
        *   Staging/Production: `supabase db push` is used to apply new migrations to the linked remote Supabase project.
*   **Key Migrations Found**:
    *   Creation of `auth_credentials` table.
    *   Creation of `password_reset_tokens` table.
    *   Several migrations related to an `ads` table (creation, adding columns, modifying columns).

### 2.2. Standalone SQL Scripts

*   **Location**: Several `.sql` files are present in the root directory of the project:
    *   `add-is-verified-column.sql`
    *   `create_reset_table.sql` (likely superseded by the migration in `migrations/`)
    *   `create-verification-table.sql`
    *   `fix_profile_images.sql`
*   **Purpose**: These appear to be one-off scripts for specific schema adjustments or fixes. They might have been used before a more formal migration process was fully adopted or for tasks not easily captured by diff-based migrations.
*   **Execution**: These scripts would typically be run manually against the database using a SQL client or Supabase Studio's SQL editor.
*   **Caution**: It's important to verify if the changes in these root-level SQL files have been incorporated into the formal Supabase CLI migrations in the `migrations/` folder to avoid conflicts or redundant operations. Ideally, all schema changes should be managed through the Supabase CLI migration workflow.

### 2.3. Utility API Routes for Schema Setup

*   **Endpoints**:
    *   `POST /api/create-reset-table` (from `pages/api/create-reset-table.ts`)
    *   `POST /api/direct-create-table` (from `pages/api/direct-create-table.ts`)
    *   `POST /api/run-migration` (from `pages/api/run-migration.ts`)
*   **Purpose**: These API routes provide programmatic ways to create the `password_reset_tokens` table and related objects.
*   **Usage**: These are primarily intended for initial setup or development environments. They should be used with caution, secured with a secret key (as some attempt to do), and potentially removed or disabled in production environments to prevent unauthorized schema modifications. The Supabase CLI migration workflow is the preferred method for production schema changes.

## 3. Data Seeding

*   **Seed Script**: `populate_db.sql` located in the root directory.
*   **Purpose**: This script is likely used to populate the database with initial data required for the application to function correctly, especially in development and testing environments. This might include:
    *   Default countries and specialties.
    *   Sample doctors, patients, or users.
    *   Initial administrative accounts.
    *   Default application settings if stored in the database.
*   **Execution**:
    *   Typically run manually via a SQL client or Supabase Studio after the schema migrations have been applied.
    *   For automated seeding in development, it could be part of a `supabase db reset` workflow if configured in `supabase/seed.sql` (though `populate_db.sql` is at the root).
*   **CSV Files**: The presence of CSV files like `doctors_rows (6).csv`, `reviews_rows (4).csv`, `specialties_rows.csv`, `users_rows.csv` at the root suggests they might be sources for seed data, potentially imported using Supabase Studio's CSV import feature or referenced by `populate_db.sql`.

## 4. Recommendations for Strategy

*   **Prioritize Supabase CLI Migrations**: All schema changes should ideally be managed through the Supabase CLI and stored as versioned files in the `migrations/` directory. This provides better tracking, consistency, and easier rollbacks.
*   **Consolidate One-Off Scripts**: Review the standalone SQL scripts in the root directory. If their changes are permanent and necessary, incorporate them into the formal migration history in `migrations/` and then archive or remove the root scripts.
*   **Secure Utility Endpoints**: If utility API routes for schema modification are kept, ensure they are robustly secured and only accessible to authorized administrators, ideally restricted by IP or strong authentication, and disabled in production.
*   **Seed Data Management**:
    *   If `populate_db.sql` is the primary seed script, ensure it's up-to-date with the current schema.
    *   For local development, consider integrating it with Supabase CLI's seeding mechanism by placing it at `supabase/seed.sql`. This allows `supabase db reset` to automatically apply migrations and then seed data.
    *   Keep CSV data sources version-controlled if they are essential for seeding.
*   **Environment-Specific Seeding**: Differentiate between data needed for local development (e.g., rich sample data) and data needed for staging or production (e.g., essential lookup values only).

By adhering to a structured migration and seeding process, the project can ensure database consistency across different environments and simplify onboarding for new developers.
