# Database Schema Documentation

## Overview

This document describes the database schema for the application. The database is implemented using Supabase.

## Tables

### ads

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `title` (text, not null)
*   `description` (text, nullable)
*   `target_url` (text, not null)
*   `start_date` (date, not null)
*   `end_date` (date, nullable)
*   `status` (text, not null, default: 'draft')
*   `budget` (numeric, nullable, default: 0)
*   `placement` (character varying, not null)
*   `target_specialty_id` (integer, nullable)
*   `target_locations` (ARRAY, nullable)
*   `advertiser_id` (uuid, nullable)
*   `created_at` (timestamp with time zone, nullable, default: now())
*   `updated_at` (timestamp with time zone, nullable, default: now())
*   `media_url` (text, nullable)
*   `media_type` (text, nullable)
*   `size` (character varying, nullable)
*   `placements` (ARRAY, nullable)
*   `custom_top` (text, nullable)
*   `custom_bottom` (text, nullable)
*   `custom_left` (text, nullable)
*   `custom_right` (text, nullable)
*   `position` (text, nullable)

### analytics\_ad\_clicks

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `impression_id` (uuid, nullable)
*   `ad_id` (uuid, nullable)
*   `ad_type` (character varying, not null)
*   `ad_location` (character varying, not null)
*   `user_id` (uuid, nullable)
*   `session_id` (uuid, nullable)
*   `created_at` (timestamp with time zone, nullable, default: now())

### analytics\_ad\_impressions

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `ad_id` (uuid, nullable)
*   `ad_type` (character varying, not null)
*   `ad_location` (character varying, not null)
*   `user_id` (uuid, nullable)
*   `session_id` (uuid, nullable)
*   `device_type` (character varying, nullable)
*   `created_at` (timestamp with time zone, nullable, default: now())

### analytics\_advertisers

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `name` (character varying, not null)
*   `contact_email` (character varying, nullable)
*   `status` (character varying, nullable, default: 'active')
*   `monthly_spend` (numeric, nullable, default: 0)
*   `ads_count` (integer, nullable, default: 0)
*   `created_at` (timestamp with time zone, nullable, default: now())
*   `updated_at` (timestamp with time zone, nullable, default: now())

### analytics\_daily\_metrics

*   `date` (date, not null)
*   `daily_active_users` (integer, nullable, default: 0)
*   `page_views` (integer, nullable, default: 0)
*   `avg_session_duration` (numeric, nullable, default: 0)
*   `new_registrations` (integer, nullable, default: 0)
*   `registered_users_percent` (numeric, nullable, default: 0)
*   `reviews_submitted` (integer, nullable, default: 0)
*   `avg_rating` (numeric, nullable, default: 0)
*   `ad_impressions` (integer, nullable, default: 0)
*   `ad_clicks` (integer, nullable, default: 0)
*   `ad_ctr` (numeric, nullable, default: 0)
*   `ad_revenue` (numeric, nullable, default: 0)
*   `revenue_per_user` (numeric, nullable, default: 0)
*   `created_at` (timestamp with time zone, nullable, default: now())

### analytics\_feature\_requests

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `title` (character varying, not null)
*   `votes_count` (integer, nullable, default: 0)
*   `status` (character varying, nullable, default: 'under\_review')
*   `created_at` (timestamp with time zone, nullable, default: now())
*   `updated_at` (timestamp with time zone, nullable, default: now())

### analytics\_monthly\_metrics

*   `month` (date, not null)
*   `monthly_active_users` (integer, nullable, default: 0)
*   `total_page_views` (integer, nullable, default: 0)
*   `avg_session_duration` (numeric, nullable, default: 0)
*   `new_registrations` (integer, nullable, default: 0)
*   `total_reviews` (integer, nullable, default: 0)
*   `avg_rating` (numeric, nullable, default: 0)
*   `ad_impressions` (integer, nullable, default: 0)
*   `ad_clicks` (integer, nullable, default: 0)
*   `ad_ctr` (numeric, nullable, default: 0)
*   `ad_revenue` (numeric, nullable, default: 0)
*   `revenue_per_user` (numeric, nullable, default: 0)
*   `user_retention_7day` (numeric, nullable, default: 0)
*   `user_retention_30day` (numeric, nullable, default: 0)
*   `created_at` (timestamp with time zone, nullable, default: now())

### analytics\_page\_views

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `session_id` (uuid, nullable)
*   `user_id` (uuid, nullable)
*   `page_path` (character varying, not null)
*   `page_title` (character varying, nullable)
*   `time_spent_seconds` (integer, nullable)
*   `device_type` (character varying, nullable)
*   `browser` (character varying, nullable)
*   `created_at` (timestamp with time zone, nullable, default: now())

### analytics\_performance\_metrics

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `timestamp` (timestamp with time zone, nullable, default: now())
*   `metric_type` (character varying, not null)
*   `page_path` (character varying, nullable)
*   `value_ms` (integer, not null)
*   `device_type` (character varying, nullable)
*   `browser` (character varying, nullable)
*   `os` (character varying, nullable)
*   `connection_type` (character varying, nullable)
*   `country` (character varying, nullable)
*   `created_at` (timestamp with time zone, nullable, default: now())

### analytics\_registration\_funnel

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `date` (date, not null)
*   `visit_registration_page` (integer, nullable, default: 0)
*   `start_registration` (integer, nullable, default: 0)
*   `email_verification` (integer, nullable, default: 0)
*   `complete_profile` (integer, nullable, default: 0)
*   `specialty_selection` (integer, nullable, default: 0)
*   `completed_registration` (integer, nullable, default: 0)
*   `completion_rate` (numeric, nullable, default: 0)
*   `created_at` (timestamp with time zone, nullable, default: now())

### analytics\_retention\_cohorts

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `cohort_date` (date, not null)
*   `users_count` (integer, not null)
*   `day_1_retention` (integer, nullable, default: 0)
*   `day_7_retention` (integer, nullable, default: 0)
*   `day_14_retention` (integer, nullable, default: 0)
*   `day_30_retention` (integer, nullable, default: 0)
*   `day_60_retention` (integer, nullable, default: 0)
*   `day_90_retention` (integer, nullable, default: 0)
*   `day_1_percent` (numeric, nullable, default: 0)
*   `day_7_percent` (numeric, nullable, default: 0)
*   `day_14_percent` (numeric, nullable, default: 0)
*   `day_30_percent` (numeric, nullable, default: 0)
*   `day_60_percent` (numeric, nullable, default: 0)
*   `day_90_percent` (numeric, nullable, default: 0)
*   `created_at` (timestamp with time zone, nullable, default: now())
*   `updated_at` (timestamp with time zone, nullable, default: now())

### analytics\_reviews\_metrics

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `date` (date, not null)
*   `doctor_id` (integer, nullable)
*   `specialty_id` (integer, nullable)
*   `reviews_count` (integer, nullable, default: 0)
*   `avg_rating` (numeric, nullable, default: 0)
*   `one_star` (integer, nullable, default: 0)
*   `two_star` (integer, nullable, default: 0)
*   `three_star` (integer, nullable, default: 0)
*   `four_star` (integer, nullable, default: 0)
*   `five_star` (integer, nullable, default: 0)
*   `positive_sentiment_percent` (numeric, nullable, default: 0)
*   `neutral_sentiment_percent` (numeric, nullable, default: 0)
*   `negative_sentiment_percent` (numeric, nullable, default: 0)
*   `created_at` (timestamp with time zone, nullable, default: now())

### analytics\_specialty\_interests

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `user_id` (uuid, nullable)
*   `specialty_id` (integer, nullable)
*   `view_count` (integer, nullable, default: 1)
*   `created_at` (timestamp with time zone, nullable, default: now())
*   `updated_at` (timestamp with time zone, nullable, default: now())

### analytics\_support\_metrics

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `date` (date, not null)
*   `tickets_opened` (integer, nullable, default: 0)
*   `tickets_resolved` (integer, nullable, default: 0)
*   `avg_resolution_time_hours` (numeric, nullable, default: 0)
*   `satisfaction_rate` (numeric, nullable, default: 0)
*   `category_account` (integer, nullable, default: 0)
*   `category_doctor` (integer, nullable, default: 0)
*   `category_rating` (integer, nullable, default: 0)
*   `category_payment` (integer, nullable, default: 0)
*   `category_performance` (integer, nullable, default: 0)
*   `category_feature` (integer, nullable, default: 0)
*   `category_other` (integer, nullable, default: 0)
*   `created_at` (timestamp with time zone, nullable, default: now())

### analytics\_user\_demographics

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `user_id` (uuid, nullable)
*   `age_range` (character varying, nullable)
*   `gender` (character varying, nullable)
*   `country` (character varying, nullable)
*   `region` (character varying, nullable)
*   `created_at` (timestamp with time zone, nullable, default: now())
*   `updated_at` (timestamp with time zone, nullable, default: now())

### analytics\_user\_sessions

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `user_id` (uuid, nullable)
*   `session_start` (timestamp with time zone, not null, default: now())
*   `session_end` (timestamp with time zone, nullable)
*   `duration_seconds` (integer, nullable)
*   `device_type` (character varying, nullable)
*   `browser` (character varying, nullable)
*   `os` (character varying, nullable)
*   `referrer` (character varying, nullable)
*   `is_registered` (boolean, nullable)
*   `ip_address` (character varying, nullable)
*   `country` (character varying, nullable)
*   `region` (character varying, nullable)
*   `created_at` (timestamp with time zone, nullable, default: now())

### auth\_credentials

*   `id` (uuid, not null, default: gen\_random\_uuid())
*   `email` (text, not null)
*   `hashed_password` (text, not null)
*   `user_profile_id` (integer, not null)
*   `user_type` (text, not null)
*   `created_at` (timestamp with time zone, not null, default: now())
*   `updated_at` (timestamp with time zone, not null, default: now())
*   `is_verified` (boolean, nullable, default: false)

### countries

*   `country_id` (integer, not null, default: nextval('countries\_country\_id\_seq'::regclass))
*   `country_name` (text, not null)

### doctor\_badges

*   `badge_id` (integer, not null, default: nextval('doctor\_badges\_badge\_id\_seq'::regclass))
*   `doctor_id` (integer, not null)
*   `badge_name` (text, not null)
*   `awarded_date` (date, nullable, default: CURRENT\_DATE)

### doctor\_favorites

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `user_id` (uuid, not null)
*   `doctor_id` (integer, not null)
*   `created_at` (timestamp with time zone, nullable, default: now())

### doctor\_rating\_history

*   `history_id` (integer, not null, default: nextval('doctor\_rating\_history\_history\_id\_seq'::regclass))
*   `doctor_id` (integer, nullable)
*   `period_date` (date, nullable)
*   `average_rating` (numeric, nullable)
*   `review_count` (integer, nullable)

### doctor\_view\_history

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `user_id` (uuid, not null)
*   `doctor_id` (integer, not null)
*   `view_count` (integer, nullable, default: 1)
*   `last_viewed_at` (timestamp with time zone, nullable, default: now())
*   `created_at` (timestamp with time zone, nullable, default: now())

### doctors

*   `doctor_id` (integer, not null)
*   `fullname` (text, nullable)
*   `hospital` (text, nullable)
*   `medical_title` (text, nullable)
*   `specialty` (text, nullable)
*   `subspecialty` (text, nullable)
*   `educational_background` (text, nullable)
*   `board_certifications` (text, nullable)
*   `experience` (integer, nullable)
*   `publications` (text, nullable)
*   `awards_recognitions` (text, nullable)
*   `phone_number` (text, nullable)
*   `email` (text, nullable)
*   `languages_spoken` (text, nullable)
*   `professional_affiliations` (text, nullable)
*   `procedures_performed` (text, nullable)
*   `treatment_services_expertise` (text, nullable)
*   `hospital_id` (integer, nullable)
*   `image_path` (text, nullable)
*   `wins` (integer, nullable, default: 0)
*   `losses` (integer, nullable, default: 0)
*   `form` (text, nullable)
*   `rating` (numeric, nullable)
*   `review_count` (integer, nullable)
*   `country_id` (integer, nullable)
*   `specialty_id` (integer, nullable)
*   `last_updated` (date, nullable)
*   `auth_id` (uuid, nullable)
*   `draws` (integer, nullable, default: 0)
*   `profile_image` (text, nullable)

### doctors\_registration

*   `doctor_id` (integer, nullable)
*   `fullname` (text, nullable)
*   `facility` (text, nullable)
*   `medical_title` (text, nullable)
*   `specialty` (text, nullable)
*   `subspecialty` (text, nullable)
*   `educational_background` (text, nullable)
*   `board_certifications` (text, nullable)
*   `experience` (integer, nullable)
*   `publications` (text, nullable)
*   `awards_recognitions` (text, nullable)
*   `phone_number` (text, nullable)
*   `email` (text, not null)
*   `languages_spoken` (text, nullable)
*   `professional_affiliations` (text, nullable)
*   `procedures_performed` (text, nullable)
*   `treatment_services_expertise` (text, nullable)
*   `hospital_id` (integer, nullable)
*   `image_path` (text, nullable)
*   `country_id` (integer, nullable)
*   `specialty_id` (integer, nullable)
*   `last_updated` (date, nullable)
*   `auth_id` (uuid, nullable)
*   `status` (text, nullable, default: 'pending')
*   `password` (character varying, nullable)

### hospitals

*   `hospital_id` (integer, not null, default: nextval('hospitals\_hospital\_id\_seq'::regclass))
*   `hospital_name` (text, nullable)
*   `country_id` (integer, nullable)
*   `city` (text, nullable)
*   `address` (text, nullable)
*   `email_info` (text, nullable)
*   `telephone_info` (text, nullable)
*   `rating` (numeric, nullable)
*   `review_count` (integer, nullable)
*   `last_updated` (date, nullable)
*   `departments` (text, nullable)

### medical\_events

*   `id` (integer, not null, default: nextval('medical\_events\_id\_seq'::regclass))
*   `event_name` (text, nullable)
*   `created_at` (timestamp without time zone, nullable, default: now())

### password\_reset\_tokens

*   `id` (integer, not null, default: nextval('password\_reset\_tokens\_id\_seq'::regclass))
*   `user_id` (integer, not null)
*   `email` (text, not null)
*   `token` (text, not null)
*   `created_at` (timestamp with time zone, nullable, default: now())
*   `expires_at` (timestamp with time zone, not null)
*   `used` (boolean, nullable, default: false)
*   `user_type` (text, not null)

### premium\_subscriptions

*   `subscription_id` (integer, not null, default: nextval('premium\_subscriptions\_subscription\_id\_seq'::regclass))
*   `entity_type` (text, nullable)
*   `entity_id` (integer, nullable)
*   `plan` (text, nullable)
*   `start_date` (date, nullable)
*   `end_date` (date, nullable)
*   `status` (text, nullable)

### reviews

*   `review_id` (integer, not null, default: nextval('reviews\_review\_id\_seq'::regclass))
*   `user_id` (integer, nullable)
*   `doctor_id` (integer, nullable)
*   `clinical_competence` (integer, nullable)
*   `communication_stats` (integer, nullable)
*   `empathy_compassion` (integer, nullable)
*   `time_management` (integer, nullable)
*   `follow_up_care` (integer, nullable)
*   `overall_satisfaction` (integer, nullable)
*   `additional_comments` (text, nullable)
*   `rating` (numeric, nullable)
*   `review_date` (date, nullable)
*   `sentiment_score` (numeric, nullable)
*   `Recommendation` (integer, nullable)

### specialties

*   `specialty_id` (integer, not null, default: nextval('specialties\_specialty\_id\_seq'::regclass))
*   `specialty_name` (text, not null)
*   `description` (text, nullable)

### specialty\_favorites

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `user_id` (uuid, not null)
*   `specialty_id` (integer, not null)
*   `created_at` (timestamp with time zone, nullable, default: now())

### specialty\_views

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `user_id` (uuid, nullable)
*   `specialty_id` (integer, nullable)
*   `created_at` (timestamp with time zone, nullable, default: now())

### statistics

*   `statistic_name` (character varying, nullable)
*   `statistic_value` (integer, nullable)
*   `additional_info` (character varying, nullable)
*   `id` (integer, not null, default: nextval('statistics\_id\_seq'::regclass))

### user\_preferences

*   `id` (uuid, not null, default: uuid\_generate\_v4())
*   `user_id` (uuid, not null)
*   `favorite_specialties` (ARRAY, nullable, default: '{}')
*   `preferred_languages` (ARRAY, nullable, default: '{}')
*   `ui_preferences` (jsonb, nullable, default: '{}')
*   `created_at` (timestamp with time zone, nullable, default: now())
*   `updated_at` (timestamp with time zone, nullable, default: now())

### users

*   `user_id` (integer, not null, default: nextval('users\_user\_id\_seq'::regclass))
*   `username` (text, not null)
*   `email` (text, not null)
*   `first_name` (text, nullable)
*   `last_name` (text, nullable)
*   `gender` (text, nullable)
*   `city` (text, nullable)
*   `country` (text, nullable)
*   `user_type` (text, nullable)
*   `age` (integer, nullable)
*   `auth_id` (uuid, nullable)
*   `medical condition` (text, nullable)
*   `Registration date` (date, nullable)
*   `State` (text, nullable)
*   `Phone_Number` (numeric, nullable)
*   `profile_image` (text, nullable)

## Views

### vw\_active\_users\_trend

*   `date` (date, nullable)
*   `daily_active_users` (integer, nullable)

### vw\_ad\_performance\_by\_type

*   `ad_type` (character varying, nullable)
*   `impressions` (bigint, nullable)
*   `clicks` (bigint, nullable)
*   `ctr` (numeric, nullable)

### vw\_daily\_metrics\_recent

*   `date` (date, nullable)
*   `daily_active_users` (integer, nullable)
*   `page_views` (integer, nullable)
*   `avg_session_duration` (numeric, nullable)
*   `new_registrations` (integer, nullable)
*   `registered_users_percent` (numeric, nullable)
*   `reviews_submitted` (integer, nullable)
*   `avg_rating` (numeric, nullable)
*   `ad_impressions` (integer, nullable)
*   `ad_clicks` (integer, nullable)
*   `ad_ctr` (numeric, nullable)
*   `ad_revenue` (numeric, nullable)
*   `revenue_per_user` (numeric, nullable)
*   `created_at` (timestamp with time zone, nullable)

### vw\_demographics\_summary

*   `age_range` (character varying, nullable)
*   `user_count` (bigint, nullable)
*   `percentage` (numeric, nullable)

### vw\_registration\_funnel\_recent

*   `id` (uuid, nullable)
*   `date` (date, nullable)
*   `visit_registration_page` (integer, nullable)
*   `start_registration` (integer, nullable)
*   `email_verification` (integer, nullable)
*   `complete_profile` (integer, nullable)
*   `specialty_selection` (integer, nullable)
*   `completed_registration` (integer, nullable)
*   `completion_rate` (numeric, nullable)
*   `created_at` (timestamp with time zone, nullable)

### vw\_specialty\_popularity

*   `specialty_name` (text, nullable)
*   `view_count` (bigint, nullable)
*   `unique_viewers` (bigint, nullable)

## Functions

The `lib/database.types.ts` file also lists the following functions:

*   `check_email_exists(email text)` - Returns boolean
*   `track_ad_click(p_impression_id uuid, p_ad_id uuid, p_ad_type character varying, p_ad_location character varying, p_user_id uuid, p_session_id uuid)` - Returns uuid
*   `track_ad_impression(p_ad_id uuid, p_ad_type character varying, p_ad_location character varying, p_user_id uuid, p_session_id uuid, p_device_type character varying)` - Returns uuid
*   `track_page_view(p_user_id uuid, p_session_id uuid, p_page_path character varying, p_page_title character varying, p_device_type character varying, p_browser character varying)` - Returns uuid
*   `track_performance_metric(p_metric_type character varying, p_page_path character varying, p_value_ms integer, p_device_type character varying, p_browser character varying, p_os character varying, p_connection_type character varying, p_country character varying)` - Returns uuid
*   `update_cohort_retention()` - Returns void
*   `update_daily_metrics(p_date date)` - Returns void
*   `update_monthly_metrics(p_month date)` - Returns void
*   `update_registration_funnel(p_date date)` - Returns void

## Triggers

Information about triggers is not available in the provided files.
