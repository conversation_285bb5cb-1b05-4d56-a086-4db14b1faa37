# Deployment and Environment Setup Guide

This document provides instructions for setting up the local development environment, building the application, and guidelines for deployment.

## 1. Prerequisites

*   **Node.js**: Ensure you have Node.js installed. The specific version should be compatible with the Next.js version used (Next.js 15.x as per `package.json`). It's recommended to use a Node version manager like `nvm`.
*   **Package Manager**: `npm` (usually comes with Node.js) or `yarn`. The project uses a `package-lock.json`, suggesting `npm` is the primary package manager.
*   **Supabase Account**: Access to a Supabase project is required for database and authentication backend.
*   **Git**: For cloning the repository.

## 2. Local Development Setup

### 2.1. Clone the Repository
   ```bash
   git clone <repository-url>
   cd <project-directory>
   ```

### 2.2. Install Dependencies
   Install project dependencies using npm:
   ```bash
   npm install
   ```
   (If `yarn` is preferred and a `yarn.lock` file exists, use `yarn install`.)

### 2.3. Environment Variables
   The application requires several environment variables for connecting to Supabase, JWT secrets, email services, etc.
   1.  Create a `.env.local` file in the root of the project. This file is gitignored and should contain your local development secrets.
   2.  Populate `.env.local` with the necessary variables. Refer to the `.env.example` file if one exists, or use the following template based on backend architecture analysis:

       ```env
       # Supabase Project Connection
       NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
       NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
       SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

       # JWT Secret (must be a strong, random string)
       JWT_SECRET=your_strong_jwt_secret_key

       # Email Configuration (e.g., Mailtrap for development)
       MAILTRAP_USER=your_mailtrap_user
       MAILTRAP_PASS=your_mailtrap_password
       # SMTP_ADMIN_EMAIL=<EMAIL> (if used by nodemailer setup)

       # Site URL (for constructing absolute links in emails, etc.)
       NEXT_PUBLIC_SITE_URL=http://localhost:3000 
       # NEXT_PUBLIC_BASE_URL=http://localhost:3000 (if used as an alternative)

       # Add any other environment variables required by the application
       ```
   **Important**:
    *   Obtain Supabase URL and keys from your Supabase project dashboard.
    *   Generate a strong, unique `JWT_SECRET`.
    *   Configure Mailtrap (or another SMTP service) credentials if you need to test email sending locally.

### 2.4. Database Setup
   *   **Schema**: The database schema should match the one defined and used by the application (see `docs/database-schema.md`).
   *   **Migrations/Initial Setup**:
        *   The application includes utility API routes for creating the `password_reset_tokens` table (e.g., `/api/create-reset-table`, `/api/run-migration`). These are intended for initial setup and require a secret key. **Use these with caution and secure/remove them after setup.**
        *   For other tables, ensure they are created in your Supabase project according to the application's needs. If SQL migration files exist (e.g., `.sql` files at the root or in a `migrations/` or `supabase/migrations/` directory), they should be applied to your Supabase database. (The file listing shows `add-is-verified-column.sql`, `create_reset_table.sql`, `create-verification-table.sql`, `populate_db.sql` at the root, and a `migrations/` directory which should be inspected for formal migration scripts.)
   *   **Supabase Project Setup**:
        *   Ensure your Supabase project has Authentication enabled if using Supabase Auth features (though this project heavily relies on a custom auth system using the `auth_credentials` table).
        *   Set up Storage buckets if used (e.g., `ad-media`, `profile-images`).
        *   Configure email templates and SMTP settings in the Supabase dashboard if relying on Supabase's built-in email sending for auth, or ensure your custom Nodemailer setup is correct.

### 2.5. Running the Development Server
   Start the Next.js development server:
   ```bash
   npm run dev
   ```
   The application should now be accessible at `http://localhost:3000` (or the port specified in `NEXT_PUBLIC_SITE_URL` if different and configured in `next.config.mjs`).

## 3. Building the Application

To create a production-ready build of the application:
```bash
npm run build
```
This command compiles the Next.js application, optimizes assets, and prepares it for deployment. The output will be in the `.next` directory.

## 4. Linting

To check for code quality and style issues:
```bash
npm run lint
```

## 5. Running Tests

A specific test script is available:
```bash
npm run test:registration
```
This runs `__tests__/registration-test.js`. Add or update test scripts as needed for comprehensive testing.

## 6. Deployment

The application is a Next.js project and can be deployed to various platforms.

### 6.1. Vercel (Recommended for Next.js)
   *   Vercel is the platform created by the makers of Next.js and offers seamless deployment.
   *   Connect your Git repository (e.g., GitHub, GitLab, Bitbucket) to Vercel.
   *   Configure environment variables in the Vercel project settings (similar to `.env.local`, but for production values).
   *   Vercel will typically auto-detect Next.js settings and build/deploy on push to the main branch.

### 6.2. Other Platforms (e.g., AWS, Google Cloud, Azure, DigitalOcean)

   *   **Node.js Server**: You can deploy the application as a standalone Node.js server.
        1.  Run `npm run build`.
        2.  Run `npm run start` to start the Next.js production server.
        3.  Use a process manager like PM2 to keep the application running.
        4.  Configure a reverse proxy (e.g., Nginx, Apache) to handle incoming traffic and SSL termination.
   *   **Docker**:
        1.  Create a `Dockerfile` to containerize the Next.js application.
            ```dockerfile
            # Example Dockerfile for Next.js
            FROM node:18-alpine AS base

            # Install dependencies only when needed
            FROM base AS deps
            WORKDIR /app
            COPY package.json package-lock.json* ./
            RUN npm install --frozen-lockfile

            # Rebuild the source code only when needed
            FROM base AS builder
            WORKDIR /app
            COPY --from=deps /app/node_modules ./node_modules
            COPY . .
            RUN npm run build

            # Production image, copy all the files and run next
            FROM base AS runner
            WORKDIR /app

            ENV NODE_ENV production
            # You can set a different port here if needed
            # ENV PORT 3000

            COPY --from=builder /app/public ./public
            COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
            COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

            USER nextjs
            EXPOSE 3000
            CMD ["node", "server.js"]
            ```
           (Note: This Dockerfile assumes `output: 'standalone'` in `next.config.mjs` for optimized image size. Adjust as needed.)
        2.  Build and push the Docker image to a container registry.
        3.  Deploy the container to a cloud platform (e.g., Kubernetes, ECS, Cloud Run).
   *   **Serverless Platforms**: Some serverless platforms support Next.js deployment (often with adapters).

### 6.3. Environment Variables for Production
   Ensure all necessary environment variables (Supabase keys, JWT secret, production SITE_URL, etc.) are securely configured in the production environment. **Do not use development/Mailtrap credentials in production.**

## 7. Database Migrations (Production)

*   If formal database migration scripts are used (e.g., in `migrations/` or `supabase/migrations/`), apply them to the production Supabase database before or during deployment. Supabase CLI can be used for this: `supabase db push`.
*   Avoid using utility API routes like `/api/create-reset-table` in production. These should be one-off setup scripts or part of a proper migration workflow.

This guide provides a general overview. Specific deployment steps may vary based on the chosen hosting platform and infrastructure.
