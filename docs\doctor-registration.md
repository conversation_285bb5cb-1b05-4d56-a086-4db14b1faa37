# Doctor Registration Components

This document explains the doctor registration components in the Doctor's Leagues application and how to maintain them.

## Current Status

There are currently two doctor registration components in the codebase:

1. **New Doctor Registration (Primary)**: `components/registration/new-doctor-registration.tsx`
   - This is the current, actively used component
   - Used in:
     - `components/registration/choose-role-dialog.tsx`
     - `components/premium-medical-modal.tsx`
     - `app/doctor/register/page.tsx`

2. **Old Doctor Registration Dialog (Legacy)**: `components/registration/doctor-registration-dialog.tsx`
   - This is a legacy component that we're transitioning away from

## Migration Plan

We've updated the `/doctor/register` route to use the new component, but the old component still exists in the codebase for reference. Once we're confident that all functionality is working correctly with the new component, the old component can be safely removed.

## Image Upload Issue

If you encounter an error related to image uploads in the doctor registration form showing "Body exceeded 1MB limit" in the server logs, we've implemented two solutions:

1. **Increased Server Action Body Size Limit**:
   - In `next.config.mjs`, we've increased the bodySizeLimit to 4MB:
   ```js
   experimental: {
     serverActions: {
       bodySizeLimit: '4mb'
     }
   }
   ```

2. **Client-side Image Resizing**:
   - We've added image resizing functionality in the `handleFileChange` function.
   - This reduces the image size before it's sent to the server.
   - The resizing function maintains the aspect ratio and converts images to JPEG format with 80% quality.

## Future Maintenance

When making changes to doctor registration:

1. Always work with the **new** component (`new-doctor-registration.tsx`)
2. Test all registration flows after making changes
3. Consider checking both mobile and desktop layouts
4. Be mindful of form validation and error handling

Once all references to the old component have been removed and we're confident in the new implementation, the legacy component can be deleted.

## Registration Data Structure

The form data contains these key sections:

1. **Account Information** (Stage 1)
   - Email, password

2. **Basic Professional Information** (Stage 2)
   - Full name, country, hospital, medical title, specialty, etc.

3. **Detailed Professional Information** (Stage 3)
   - Educational background, experience, awards, etc.

When modifying, ensure that new fields are properly added to the database schema and server-side handling. 