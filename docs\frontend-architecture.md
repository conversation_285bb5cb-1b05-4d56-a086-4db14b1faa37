# Frontend Architecture Overview

This document outlines the architecture of the frontend for the Doctors Leagues application. It covers the directory structure, routing, state management, UI components, styling, and key data handling patterns.

## 1. Core Framework and Language

*   **Next.js**: The application is built using Next.js, leveraging its App Router for routing and server-side capabilities.
*   **React**: React is used as the primary library for building user interfaces.
*   **TypeScript**: The frontend codebase is written in TypeScript, providing static typing and improved developer experience.

## 2. Directory Structure

The main frontend code resides primarily within the `app/`, `components/`, `contexts/`, `hooks/`, and `lib/` directories.

### 2.1. `app/` Directory (App Router)

*   **Layouts and Pages**: Follows Next.js App Router conventions.
    *   `app/layout.tsx`: Defines the root layout for the entire application.
    *   `app/page.tsx`: The main landing page of the application.
    *   Subdirectories within `app/` (e.g., `app/about/`, `app/doctors/`, `app/auth/`) define routes. Each route directory typically contains a `page.tsx` for the UI of that route and may include `layout.tsx` for nested layouts or `loading.tsx` for loading UI.
    *   `app/[locale]/`: Indicates internationalization (i18n) is implemented, with routes potentially prefixed by locale.
*   **API Routes**: While most custom API routes were found in `pages/api/`, some App Router API routes exist in `app/api/` (e.g., `app/api/reviews/route.ts`).
*   **Global Styles**:
    *   `app/globals.css`: Contains global CSS styles and Tailwind CSS base directives.
    *   `app/custom-styles.css`, `app/button-styles.css`: Likely contain additional custom global or base styles.

### 2.2. `components/` Directory

*   **Reusable UI Components**: Contains various React components used throughout the application.
    *   **General Components**: Directly under `components/` (e.g., `header.tsx`, `doctor-card.tsx`, `medical-modal.tsx`).
    *   **UI Primitives/Shadcn UI**: `components/ui/` likely contains components from a UI library like Shadcn UI (e.g., buttons, dialogs, input fields).
    *   **Specialized Subdirectories**:
        *   `components/admin/`: Components specific to admin sections.
        *   `components/ads/`: Components related to displaying advertisements.
        *   `components/countries/`: Components for country-related UI (e.g., dropdowns).
        *   `components/layout/` & `components/layouts/`: Components used for structuring page layouts.
        *   `components/login/`: Components specific to the login process.
        *   `components/registration/`: Components for user registration.
        *   `components/seo/`: Components related to SEO (e.g., meta tags).
*   **Styling**: Component-specific styles might be co-located (e.g., `country-dropdown.module.css`) or primarily rely on Tailwind CSS utility classes.

### 2.3. `contexts/` Directory

*   **Shared State**: Manages global or shared state using React Context API.
    *   `contexts/auth-context.tsx`: Provides authentication state (current user, loading status) and functions (`signIn`, `signOut`) to the application.

### 2.4. `hooks/` Directory

*   **Custom React Hooks**: Contains custom hooks for reusable logic.
    *   `hooks/use-mobile.tsx`: Likely a hook to detect if the application is being viewed on a mobile device.
    *   `hooks/use-toast.ts`: A hook for displaying toast notifications, probably integrated with a UI component.

### 2.5. `lib/` Directory

*   **Utility Functions and Services**: Contains helper functions, client-side services, and type definitions.
    *   `lib/auth-service.ts`: Client-side service for authentication (likely interacts with `CustomAuthService`).
    *   `lib/data-service.ts`: Client-side service for fetching core application data (doctors, countries, etc.).
    *   `lib/hybrid-data-service.ts`: Implements a strategy for fetching data, potentially combining remote sources with local fallbacks.
    *   `lib/fallback-data.ts`: Provides static fallback data for offline or degraded operation.
    *   `lib/supabase-client.ts`: Utility for creating Supabase client instances.
    *   `lib/database.types.ts`: TypeScript definitions for the Supabase database schema.

## 3. Routing

*   **Next.js App Router**: The application primarily uses the file-system-based App Router introduced in Next.js 13+.
    *   Route segments are defined by folder names within `app/`.
    *   Dynamic routes are created using bracket notation (e.g., `app/doctors/[doctorId]/page.tsx`).
    *   `app/[locale]/...`: Suggests that routes are prefixed with a locale for internationalization.
*   **Route Protection**:
    *   `middleware.ts` (at the root level) handles route protection. It checks for an `auth_token` cookie and redirects users based on authentication status and whether the route is protected or an auth-only route (like login/register).
*   **Navigation**: Likely uses Next.js `<Link>` component for client-side navigation and `next/navigation` hooks (`useRouter`, `usePathname`) for programmatic navigation.

## 4. State Management

*   **React Context API**: Used for global state, as seen with `AuthContext`.
*   **React Hooks**:
    *   `useState` and `useEffect` for component-level state.
    *   Custom hooks in `hooks/` for encapsulating reusable stateful logic.
*   **Server Actions**: For mutations and data fetching that involve server-side logic, reducing the need for client-side state management for some operations.

## 5. UI Components and Styling

*   **Component-Based Architecture**: The UI is built using reusable React components.
*   **Styling**:
    *   **Tailwind CSS**: The primary CSS framework, configured in `tailwind.config.js` and `postcss.config.mjs`. Global styles and Tailwind directives are in `app/globals.css`.
    *   **CSS Modules**: Used for component-scoped styles where needed (e.g., `country-dropdown.module.css`).
    *   **Global CSS Files**: `app/custom-styles.css` and `app/button-styles.css` for additional global or base element styling.
*   **UI Library (Shadcn UI likely)**: The `components/ui/` directory suggests the use of a component library like Shadcn UI, which provides unstyled, accessible components built on Radix UI and styled with Tailwind CSS.
*   **Icons**: `components/icons.tsx` likely centralizes icon components, possibly using an icon library like Lucide React (as per user's custom instructions).
*   **Theming**: `components/theme-provider.tsx` suggests support for theming (e.g., light/dark mode).

## 6. Data Fetching and Handling

*   **Server Components & Client Components**: Leverages Next.js distinction for data fetching. Server Components can fetch data directly.
*   **Server Actions**: Used for data mutations (create, update, delete) and some data fetching, as seen in `actions/`.
*   **API Routes**: Client components may fetch data from API routes (in `app/api/` or `pages/api/`).
*   **`lib/data-service.ts`**: Provides client-side methods to fetch application data, likely interacting with Supabase.
*   **`lib/hybrid-data-service.ts`**:
    *   Implements a resilient data fetching strategy.
    *   Likely attempts to fetch from a primary (remote) source (Supabase).
    *   If the primary source fails or is unavailable, it may fall back to using static data from `lib/fallback-data.ts`.
    *   May incorporate a circuit breaker pattern (mentioned in user's custom instructions) to prevent repeated calls to a failing backend.
*   **Connection Status**: `components/connection-status.tsx` and `app/connection-test.tsx` suggest UI elements or mechanisms to display backend connectivity status.

## 7. Internationalization (i18n)

*   The presence of `app/[locale]/`, `i18n/`, and `messages/` directories strongly indicates that the application supports multiple languages.
*   **Routing**: Locale is likely part of the URL path.
*   **Translation Files**: `messages/` probably contains JSON or JS files with translation strings for different locales.
*   **i18n Library**: A library like `next-intl` or `react-i18next` might be used to manage translations and formatting.

## 8. Performance and Optimization

*   **Lazy Loading**: `components/lazy-load.tsx` suggests components might be lazy-loaded to improve initial page load times.
*   **Code Splitting**: Next.js automatically handles code splitting by routes.
*   **Image Optimization**: Next.js Image component (`next/image`) is likely used for optimizing images. The `uploadDoctorProfileImage` action also includes client-side image optimization.
*   **Web Vitals**: `/api/vitals` endpoint collects Web Vitals, indicating a focus on performance monitoring.
*   **Fallback Mechanisms**: `lib/fallback-data.ts` and `components/performance-fallback.js` suggest strategies for graceful degradation if parts of the application are slow or unavailable.

## 9. Error Handling

*   **Error Boundaries**: React Error Boundaries might be used to catch and handle errors in component trees. `components/error-fallback.tsx` could be such a boundary.
*   **API Error Handling**: API routes and server actions include `try...catch` blocks to handle errors and return appropriate responses.
*   **Toast Notifications**: `hooks/use-toast.ts` suggests user-facing error messages are displayed using toasts.

This overview provides a high-level understanding of the frontend architecture. Detailed information on specific implementations can be found by examining the respective files and directories.
