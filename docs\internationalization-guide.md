# Internationalization (i18n) Guide

This document outlines the internationalization (i18n) setup for the Doctors Leagues application, based on the observed file structure and dependencies.

## 1. Overview

The application appears to be set up for internationalization, supporting multiple languages. Key indicators include:
*   The `next-intl` library is listed as a dependency in `package.json`.
*   The `app/[locale]/` directory structure suggests locale-based routing.
*   `messages/en/` and `messages/ar/` directories exist, presumably to hold translation files for English and Arabic.

However, the exact configuration and usage details for `next-intl` (e.g., a central `i18n.ts` configuration file or message loading mechanism) were not definitively identified through automated file analysis. The following guide is based on common `next-intl` practices and the available evidence.

## 2. Core i18n Library

*   **`next-intl`**: Version `^4.0.2` is used, as per `package.json`. This library provides internationalized routing, message formatting, and other i18n utilities for Next.js applications, particularly with the App Router.

## 3. Locale Configuration

*   **Supported Locales**: Based on the `messages/` directory, the application is intended to support at least:
    *   `en` (English)
    *   `ar` (Arabic)
*   **Default Locale**: The default locale configuration was not found in `next.config.mjs` or a readily identifiable `i18n.ts`. This is typically defined as part of the `next-intl` setup.
*   **Locale Detection**: `next-intl` usually handles locale detection from the URL path (`/[locale]/...`). Configuration for this (e.g., in `middleware.ts` or an `i18n.ts` file) was not explicitly found, but the `app/[locale]` structure implies path-based locale handling.

## 4. Message (Translation) Management

*   **Location**: Translation files are expected to be within the `messages/` directory, with subdirectories for each locale (e.g., `messages/en/`, `messages/ar/`).
*   **Format**: `next-intl` typically uses JSON files for translations. For example, `messages/en.json` or `messages/en/common.json`.
    *   **Note**: Automated file listing reported `messages/en/` and `messages/ar/` as empty. This could be a limitation of the tool or indicate that translation files are missing, empty, or named unconventionally. If they are missing, they would need to be created.
*   **Structure**: Messages are usually structured as nested JSON objects:
    ```json
    // Example: messages/en.json
    {
      "HomePage": {
        "title": "Welcome to Doctors Leagues",
        "subtitle": "Find the best doctors near you."
      },
      "Navigation": {
        "home": "Home",
        "about": "About Us"
      }
    }
    ```

## 5. Using Translations in Components

With `next-intl`, translations are typically accessed using hooks or functions provided by the library.

*   **Server Components**:
    ```typescript
    // app/[locale]/page.tsx (Example)
    import { getTranslator } from 'next-intl/server';

    export default async function HomePage({params: {locale}}) {
      const t = await getTranslator(locale, 'HomePage'); // Assuming 'HomePage' is a namespace/key in your JSON
      return <h1>{t('title')}</h1>;
    }
    ```
*   **Client Components**:
    ```typescript
    // app/[locale]/components/MyClientComponent.tsx (Example)
    'use client';
    import { useTranslations } from 'next-intl';

    export default function MyClientComponent() {
      const t = useTranslations('Navigation'); // Assuming 'Navigation' is a namespace/key
      return <nav>{t('home')}</nav>;
    }
    ```

## 6. Adding a New Language

1.  **Configure Locale**: Add the new locale code (e.g., `fr` for French) to your `next-intl` configuration (wherever it is defined - typically an `i18n.ts` or `middleware.ts`). This usually involves updating a list of supported locales.
2.  **Create Translation File**:
    *   Create a new directory under `messages/` for the new locale (e.g., `messages/fr/`).
    *   Inside this directory, create the necessary JSON translation file(s) (e.g., `messages/fr.json` or `messages/fr/common.json`).
    *   Copy the structure from an existing language (e.g., `en.json`) and translate the values.
3.  **Update Locale Switching UI**: If there's a language switcher component, ensure it includes the new language.

## 7. Adding or Updating Translations

1.  **Identify Namespace/File**: Determine which JSON file (namespace) the translation key belongs to (e.g., `HomePage`, `common`).
2.  **Add/Update Key**:
    *   For each supported locale (e.g., `en`, `ar`), open the corresponding JSON file (e.g., `messages/en.json`, `messages/ar.json`).
    *   Add the new key-value pair or update the existing value. Ensure the key is identical across all language files.
    ```json
    // Example: Adding a new button label to messages/en.json
    {
      "Buttons": {
        "submit": "Submit",
        "cancel": "Cancel",
        "learnMore": "Learn More" // New key
      }
    }
    ```
    ```json
    // Example: Adding the same to messages/ar.json
    {
      "Buttons": {
        "submit": "إرسال", // (Example translation)
        "cancel": "إلغاء", // (Example translation)
        "learnMore": "اعرف المزيد" // New key, translated
      }
    }
    ```
3.  **Use in Code**: Use the `t('Namespace.key')` or `t('key')` (if namespaces are implicit) function in your components to display the new translation.

## 8. Configuration File (`i18n.ts` - Assumed)

While not found automatically, a typical `next-intl` setup involves an `i18n.ts` (or similar, e.g., at the root or in `lib/`) file that might look like this:

```typescript
// Example: i18n.ts (Illustrative - actual file not found)
import {getRequestConfig} from 'next-intl/server';
 
export default getRequestConfig(async ({locale}) => {
  // Validate that the incoming `locale` parameter is valid
  // and load the messages for the active locale.
  // The `locale` parameter is received from the middleware.
  return {
    messages: (await import(`./messages/${locale}.json`)).default
    // Or if messages are in subdirectories:
    // messages: (await import(`./messages/${locale}/common.json`)).default
  };
});
```
This file would be responsible for loading the correct message file based on the active locale. The actual implementation in this project needs to be located to confirm the message loading strategy.

## 9. Middleware for Locale Handling

The `middleware.ts` file in the project root is responsible for routing and authentication. For `next-intl` to handle locale-based routing, it often integrates with or replaces the existing middleware. The current `middleware.ts` does not show `next-intl` specific logic for locale handling. If `next-intl`'s routing features are used, there might be another middleware or the existing one needs to be updated/combined with `next-intl`'s middleware functionalities.

Alternatively, `next-intl`'s routing can be configured in `next.config.mjs` using its plugin, but this was also not observed.

**Conclusion**: The project has foundational elements for i18n (dependency, `app/[locale]`, `messages/` folders). However, the precise configuration for `next-intl` (message loading, locale detection in middleware, default locale) needs to be located or clarified to fully understand and manage the i18n system. Developers should look for an `i18n.ts` file, check `next.config.mjs` for any `next-intl` plugin usage, and inspect how `app/[locale]/layout.tsx` or `app/[locale]/page.tsx` might be consuming locale data or translations.
