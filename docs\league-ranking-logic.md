# League Table and Ranking Logic

This document outlines the logic behind generating league tables and ranking doctors within the Doctors Leagues application.

## 1. Overview

The application features league tables that rank doctors based on specific criteria, primarily within the context of a country and a medical specialty. The core data for these rankings comes from the `doctors` table, supplemented by data from `reviews`, `countries`, and `specialties`.

## 2. Key Data Sources for Ranking

*   **`doctors` table**:
    *   `rating` (numeric): The primary field used for ranking individual doctors. This is an average calculated from reviews.
    *   `review_count` (integer): The number of reviews a doctor has received.
    *   `wins`, `losses`, `draws` (integer, nullable): Fields that could potentially be used for a points-based system or tie-breaking, though their direct use in a complex scoring formula is not explicitly detailed in the backend data fetching logic beyond simple sums for "total matches".
    *   `country_id` (integer): Links doctor to a country.
    *   `specialty_id` (integer): Links doctor to a specialty.
*   **`reviews` table**: Individual review scores (e.g., `clinical_competence`, `communication_stats`, `overall_satisfaction`) are used to calculate the `rating` for each doctor.
*   **`countries` table**: Used to filter leagues by country.
*   **`specialties` table**: Used to filter leagues by specialty.

## 3. Ranking Logic

### 3.1. Individual Doctor Ranking

*   **Primary Criterion**: The `community_rating` field in the `doctors` table is the main factor for ranking individual doctors within a specific league (country/specialty).
*   **Data Fetching**: The `getDoctorsByCountryAndSpecialty(countryId, specialtyId)` function in `lib/hybrid-data-service.ts` fetches doctors for a given league.
    *   This function explicitly orders the fetched doctors by `community_rating` in descending order (`order("community_rating", { ascending: false })`).
*   **Display and Client-Side Sorting**: The `app/divisions/[countryId]/[specialtyId]/doctors-table.tsx` component receives this initially sorted list.
    *   It then allows users to perform **client-side sorting** by `community_rating` (default), `wins`, `losses`, `draws`, or `name`.
    *   The "Rank" displayed in the table is the visual index (`index + 1`) based on the currently active client-side sort, not a persisted rank from the database.
    *   **Unused Potential Ranking Logic**: The `DoctorsTable.tsx` component contains a `calculateRank` function:
        ```javascript
        // (doc.wins || 0) * 3 + (doc.draws || 0) * 1 - (doc.losses || 0) + (doc.rating || 0) * 0.5
        ```
        This formula suggests an intended point system (Win=3pts, Draw=1pt, Loss=0pts, adjusted by rating). **However, this function is currently NOT USED for sorting or displaying ranks.**
    *   **Tie-Breaking**: No explicit secondary tie-breaking logic is implemented in the client-side sorting for numeric fields. If multiple doctors have the same value for the sorted field, their relative order might depend on their original order from the server or be unstable.

### 3.2. Specialty Ranking Score (Per Country)

*   **Purpose**: To provide an overall "ranking score" for a specific specialty within a given country.
*   **Calculation**: The `getSpecialtyRankingScore(countryId, specialtyId)` function in `lib/medleague/statistics.ts` calculates this:
    1.  It fetches all doctors belonging to the specified `countryId` and `specialtyId` who have a non-null `rating`.
    2.  It calculates the average of these doctors' `rating` values.
    3.  This average rating (assumed to be on a 0-5 scale) is then converted to a percentage by multiplying by 20 (e.g., an average rating of 4.5 becomes a 90% score).
    4.  If no doctors are found for the specialty in that country, or if no doctors have ratings, the score defaults to 0 or a generated fallback score in some error cases.
*   **Caching**: This score is cached using `next/cache`'s `unstable_cache` with a revalidation time typically set to 3 hours.

## 4. Update Mechanisms

*   **Doctor Rating Updates**: A doctor's `rating` and `review_count` are updated automatically whenever a new review is submitted for them. This logic resides in the `updateDoctorRating` helper function, which is called by:
    *   The `/api/reviews` API endpoint (in `app/api/reviews/route.ts`).
    *   The `submitReview` server action (in `actions/review-actions.ts`).
    This ensures that doctor ratings, and consequently their rankings, are kept relatively up-to-date as new reviews come in.
*   **League Table Display**: When a user navigates to a league page (e.g., `app/divisions/[countryId]/[specialtyId]/page.tsx`), the data is fetched fresh (or from cache, depending on Next.js caching behavior for server components and `unstable_cache` configurations). The `DoctorsTable` component then renders the current rankings.

## 5. Statistics Supporting League Context

Several functions in `lib/medleague/statistics.ts` provide contextual information for league pages:

*   `getSpecialtyDoctorsCount(countryId, specialtyId)`: Total number of doctors in the league.
*   `getActiveDoctorsCount(countryId, specialtyId)`: Number of doctors considered "active" (those with `wins > 0` or `losses > 0` or `draws > 0`).
*   `getTotalMatchesCount(countryId, specialtyId)`: Sum of all `wins`, `losses`, and `draws` for doctors in the league.

These statistics are displayed on the league page to provide more context but do not directly feed into the primary ranking score of individual doctors, which is based on their average review `rating`.

## 6. Potential for Future Enhancements / Undocumented Logic

*   **Points System & `calculateRank` Implementation**:
    *   The existing `calculateRank` function in `DoctorsTable.tsx` provides a good starting point for a more nuanced ranking system.
    *   **Recommendation**:
        1.  Decide if this point-based system should be the primary ranking method or a secondary/tie-breaking one.
        2.  If primary, modify `getDoctorsByCountryAndSpecialty` to calculate these points (either in the database query if possible, or by processing results server-side) and sort by these points. Alternatively, calculate and store these points in the `doctors` table and update them when `wins/losses/draws/rating` change.
        3.  If secondary, the `DoctorsTable.tsx` client-side sort could use this `calculateRank` score as a tie-breaker when sorting by `rating` or other fields.
        4.  The "Rank" column could display a rank based on this point system.
*   **Explicit Tie-Breaking**: Implement clear secondary and tertiary sort criteria for all sortable columns in `DoctorsTable.tsx` to ensure stable and predictable rankings. For example, if sorting by `rating`, tie-break by `review_count`, then by `wins`, then alphabetically by `fullname`.
*   **League Progression/Seasons**: Consider if features like league seasons, historical rankings, or promotion/relegation are desired. This would require significant schema changes (e.g., `seasons` table, `historical_rankings` table) and logic updates.
*   **Performance of `calculateRank`**: If `calculateRank` is used for server-side sorting of many doctors, ensure it's performant. Calculating it in the database query or storing it as a computed/indexed column would be best.

This document reflects the league and ranking logic as inferred from the backend data fetching, statistics calculation functions, and the frontend table component.

## 7. Performance Considerations for Statistic Calculations

The functions in `lib/medleague/statistics.ts` calculate various statistics. While some employ caching, there are areas for potential performance optimization:

*   **Caching**:
    *   `getSpecialtyDoctorsCount` and `getSpecialtyRankingScore` use `next/cache`'s `unstable_cache`.
    *   **Recommendation**: Consider applying `unstable_cache` to other frequently accessed statistic functions like `getCountryDoctorsCount`, `getActiveDoctorsCount`, and `getTotalMatchesCount` if their data can tolerate some staleness.
*   **Database Aggregations**:
    *   `getTotalMatchesCount`: Currently fetches all `wins`, `losses`, `draws` for relevant doctors and sums them in JavaScript.
        *   **Recommendation**: Modify to use database-level aggregation (e.g., `SELECT SUM(COALESCE(wins,0) + COALESCE(losses,0) + COALESCE(draws,0)) AS total_matches ...`) to reduce data transfer and leverage database optimizations.
    *   `getSpecialtyRankingScore`: Fetches all ratings for relevant doctors and calculates the average in JavaScript.
        *   **Recommendation**: Modify to use `SELECT AVG(rating) ...` in SQL. This can also simplify the logic by potentially avoiding a separate preliminary count query.
*   **Query Optimization**:
    *   `getActiveDoctorsCount`: Uses an `OR` condition on `wins`, `losses`, `draws`.
        *   **Recommendation**: Ensure appropriate database indexes are in place for `country_id`, `specialty_id`, and potentially on `wins`, `losses`, `draws` if this query proves to be a bottleneck.
*   **Client-Side Fallbacks**: Many statistic functions in `lib/medleague/statistics.ts` have fallback logic that returns random or mock data if `typeof window !== 'undefined'` (i.e., running on the client) or if database queries fail.
    *   **Note**: While useful for development or extreme fallback scenarios, ensure that server-rendered pages and API-driven data rely on actual database queries for accuracy in production. The use of `NEXT_PUBLIC_SUPABASE_ANON_KEY` in `getSupabase()` within `statistics.ts` is appropriate for public data.
