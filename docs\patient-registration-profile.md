# Patient Registration and Profile Management

This document outlines the workflow for patient registration and profile management in the Doctors Leagues application.

## 1. Patient Registration

### 1.1. Overview
Patients register for an account to access features like submitting reviews for doctors and managing their personal information. The registration process is multi-phased.

### 1.2. Frontend Components
*   **Registration Page**: `app/patient/register/page.tsx`
    *   This page primarily renders the `PatientRegistrationDialog` component.
*   **Registration Dialog**: `components/registration/patient-registration-dialog.tsx`
    *   Handles the multi-step registration form UI and logic.
    *   Collects user information across three phases.

### 1.3. Registration Form - Phases and Fields

*   **Phase 1: Account Credentials**
    *   `Email`: Validated for format. Real-time availability check against `users` and `doctors` tables.
    *   `Username`: Real-time availability check against `users` and `doctors` tables.
    *   `Password`: Validated for strength (minimum 8 characters, complexity).
    *   `Confirm Password`: Must match the password.
*   **Phase 2: Personal Details**
    *   `First Name`: Required.
    *   `Last Name`: Required.
    *   `Age`: Required.
    *   `Gender`: Required (Male/Female/Other options).
    *   `Phone Number`: Optional.
    *   `Medical Condition`: Optional.
*   **Phase 3: Location Details**
    *   `City`: Required.
    *   `State/Province`: Optional.
    *   `Country`: Required (selected from a dropdown populated by fetching from the `countries` table). `countryId` and `country` name are stored.

### 1.4. Backend Processing
*   **Primary Method**: The `PatientRegistrationDialog` component attempts to submit the registration data via a `fetch` call to the `POST /api/auth/custom/register` endpoint (documented in `api-documentation.md`).
    *   `userType` is set to `"patient"`.
    *   `profileData` includes all collected patient information.
    *   This endpoint handles creating records in `auth_credentials` and `users` tables, and initiates email verification.
*   **Fallback Method (Server Action)**:
    *   If the direct API call fails, the dialog has fallback logic to use a server action `createPatientProfile` (from `app/actions/register-user.ts`).
    *   **Note**: This fallback seems to use a mock `auth_id` and might be an older or alternative registration path. The primary and more complete registration logic appears to be within the `/api/auth/custom/register` endpoint.

### 1.5. Post-Registration
*   Upon successful submission, the user is informed that a verification email has been sent.
*   The account needs to be verified via the email link before the patient can log in (as enforced by the `/api/auth/custom/login` endpoint).

## 2. Patient Profile Management

### 2.1. Overview
Authenticated patients can view and edit their profile information.

### 2.2. Frontend Components
*   **Edit Profile Page**: `app/patient/profile/edit/page.tsx`
    *   This page handles fetching the current patient's data and rendering the profile editing form.

### 2.3. Data Fetching
*   The page uses the `useAuth()` hook from `AuthContext` to get the authenticated patient's `userId`.
*   It then fetches the full patient profile from the `users` table using this `userId` via a service role Supabase client.

### 2.4. Editable Profile Fields
Patients can update the following information:
*   `Username`
*   `First Name`
*   `Last Name`
*   `Age`
*   `Gender`
*   `Phone Number`
*   `City`
*   `State/Province`
*   `Country`

The `Email` is displayed but is read-only and cannot be changed through this form.

### 2.5. Password Change
*   The edit profile page includes an optional section to change the password.
*   If the user chooses to change their password, they must provide:
    *   `Current Password`
    *   `New Password` (validated for length >= 8 characters)
    *   `Confirm New Password` (must match new password)
*   Password changes are processed by making a `POST` request to the `/api/auth/custom/change-password` endpoint.

### 2.6. Backend Processing for Profile Updates
*   **Profile Data**:
    *   The `app/patient/profile/edit/page.tsx` component directly updates the `users` table using a service role Supabase client.
    *   It intelligently updates only the fields that have actually been changed by the user.
*   **Password Data**:
    *   Handled by the `/api/auth/custom/change-password` API route, which updates the `hashed_password` in the `auth_credentials` table.

### 2.7. Validation and Feedback
*   Client-side validation is performed for required fields and formats.
*   Toast notifications (using `sonner`) provide feedback to the user on success or failure of profile/password updates.

## 3. Data Storage

*   **`users` Table**: Stores patient-specific profile information (name, age, gender, location, contact, medical condition). Linked to `auth_credentials` via `user_id` (matching `auth_credentials.user_profile_id`) and potentially `auth_id` (UUID).
*   **`auth_credentials` Table**: Stores email, hashed password, user type ('patient'), and verification status.

This workflow allows patients to register and maintain their profile information within the application.
