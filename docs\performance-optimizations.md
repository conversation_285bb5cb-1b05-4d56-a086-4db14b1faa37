# Performance Optimizations Guide

This document outlines existing performance optimization strategies and provides recommendations for further improvements in the Doctors Leagues application.

## 1. Database Query Optimization & Indexing

### 1.1. Existing Indexing
Based on reviewed migration files (`migrations/` directory):
*   **`auth_credentials` Table**:
    *   Primary Key: `id` (indexed).
    *   Unique Constraint: `email` (indexed).
    *   Explicit Indexes: `idx_auth_credentials_email` (on `email`), `idx_auth_credentials_user_profile_id` (on `user_profile_id`).
*   **`password_reset_tokens` Table**:
    *   Primary Key: `id` (indexed).
    *   Unique Constraint: `token` (indexed via `idx_password_reset_tokens_token`).
    *   Explicit Indexes: `idx_password_reset_tokens_user_id`, `idx_password_reset_tokens_email`.
*   **`ads` Table**: Schema and indexes are managed via multiple migration files in `migrations/`.

**Note**: Migration files for core tables like `doctors`, `reviews`, `users`, `countries`, `specialties` were not found in the `migrations/` directory during the review. These tables might have been created manually or via scripts not following the Supabase CLI migration pattern. Their indexing status should be verified directly in the database.

### 1.2. Recommended Indexes for Core Tables
Assuming standard Supabase defaults (primary keys are indexed, foreign key constraints might create indexes but not guaranteed without explicit DDL):

*   **`doctors` Table**:
    *   `doctor_id` (Primary Key): Should be indexed.
    *   `country_id`: **Index strongly recommended** (for filtering by country).
    *   `specialty_id`: **Index strongly recommended** (for filtering by specialty).
    *   `community_rating`: **Index strongly recommended** (primary sort key for leagues).
    *   **Composite Index**: Consider `(country_id, specialty_id, community_rating)` for optimizing league table queries.
    *   `hospital_id`: Index if querying doctors by hospital is frequent.
    *   `auth_id`: Index if used for lookups.
    *   `fullname`: Index if frequent searching/sorting by name.
*   **`reviews` Table**:
    *   `review_id` (Primary Key): Should be indexed.
    *   `doctor_id`: **Index strongly recommended** (for fetching reviews per doctor, updating doctor community_ratings).
    *   `user_id`: **Index strongly recommended** (for checking duplicate reviews).
    *   `review_date`: Index if sorting reviews by date is common.
*   **`users` Table**:
    *   `user_id` (Primary Key): Should be indexed.
    *   `email`, `username`: Indexes recommended due to uniqueness checks during registration and potential lookups.
    *   `auth_id`: Index if used for lookups.
*   **`countries`, `specialties` Tables**:
    *   Primary keys (`country_id`, `specialty_id`) should be indexed.
    *   Indexes on `country_name`, `specialty_name` might be beneficial if these tables grow or are frequently searched/sorted by name.

### 1.3. Query Optimization Techniques
*   **Database-Level Aggregations**:
    *   For functions like `getTotalMatchesCount` and `getSpecialtyRankingScore` in `lib/medleague/statistics.ts`, prefer using SQL aggregate functions (`SUM()`, `AVG()`) directly in database queries (e.g., via Supabase RPC functions) rather than fetching all rows and calculating in JavaScript. This reduces data transfer and leverages database engine optimizations. (Partially addressed by adding comments in the code).
*   **Select Specific Columns**: Avoid `SELECT *` where possible. Fetch only the columns needed for a particular operation or display.
*   **Review Query Plans**: For complex or slow queries, use `EXPLAIN ANALYZE` in PostgreSQL to understand the query plan and identify bottlenecks or missing indexes.

## 2. Frontend Caching and Rendering

### 2.1. Next.js Data Cache (`unstable_cache`)
*   **Current Usage**:
    *   `lib/hybrid-data-service.ts`: `getCountryById`, `getSpecialtiesByCountryId`, `getSpecialties`, `getCountries` are cached.
    *   `lib/medleague/statistics.ts`: `getSpecialtyDoctorsCount`, `getSpecialtyRankingScore`, `getCountryDoctorsCount`, `getActiveDoctorsCount`, `getTotalMatchesCount` are cached.
*   **Recommendations**:
    *   **Review Revalidation Times**: Ensure `revalidate` times in `CACHE_TIMES` are appropriate for the data volatility of each function.
    *   **Cache Tagging**: For more granular cache invalidation, consider using cache tags with `revalidateTag` when underlying data changes (e.g., after a review submission affects a doctor's community_rating).

### 2.2. Client-Side Data Caching
*   **SWR**: The project includes `swr` as a dependency. If used for client-side data fetching:
    *   Ensure optimal configuration (e.g., `revalidateOnFocus`, `dedupingInterval`).
    *   Use for data that might change while the user is on the page or for managing mutations with optimistic updates.
*   **Context API for Global State**: `AuthContext` is used. Ensure context updates are optimized to prevent unnecessary re-renders of consuming components.

### 2.3. Component Rendering Optimization
*   **`React.memo`**: Use for functional components that render often with the same props.
*   **`useMemo`**: For memoizing expensive calculations within components (e.g., `sortedDoctors` in `DoctorsTable.tsx`).
*   **`useCallback`**: For memoizing functions passed as props, especially to memoized child components.
*   **Lazy Loading**:
    *   `components/lazy-load.tsx` exists. Continue to use it or `next/dynamic` for heavy components, images below the fold, or sections not immediately visible to improve initial page load times (Largest Contentful Paint - LCP, Time to Interactive - TTI).
*   **Virtualization**: For very long lists or tables, consider using virtualization libraries (e.g., `react-window`, `react-virtualized`) to render only visible items.

### 2.4. Asset Optimization
*   **Image Optimization**:
    *   Consistently use `next/image` for automatic optimization, responsive sizes, and WebP format delivery (as configured in `next.config.mjs`).
    *   Ensure `sizes` and `priority` props are correctly set.
    *   The server action `uploadDoctorProfileImage` includes client-side image optimization before upload, which is good.
*   **Bundle Size**:
    *   Regularly use the `@next/bundle-analyzer` (script available in `package.json`) to inspect bundle sizes and identify large dependencies or chunks that could be code-split or replaced with smaller alternatives.
*   **Minimize Client-Side JavaScript**:
    *   Leverage Server Components for static or server-fetched content as much as possible.
    *   Ensure Client Components (`"use client"`) are as small as possible and only include logic that truly needs to run in the browser.

### 2.5. Web Vitals Monitoring
*   The `/api/vitals` endpoint for collecting Web Vitals is a good practice.
*   Regularly monitor these metrics (LCP, FID/INP, CLS) to identify and address performance regressions.

By systematically reviewing and applying these database and frontend optimization techniques, the application's performance, scalability, and user experience can be significantly improved.
