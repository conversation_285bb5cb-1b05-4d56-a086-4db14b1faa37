# Review and Rating System - Detailed Workflow

This document describes the detailed workflow of the review and rating system for doctors in the Doctors Leagues application.

## 1. Overview

Patients can submit reviews for doctors, providing ratings across several criteria. These reviews contribute to an overall rating for each doctor, which is a key factor in their ranking within league tables.

## 2. Key Components

*   **Frontend Review Submission Form**: UI elements allowing authenticated patients to write and submit reviews for doctors. (Specific component paths need to be identified by reviewing doctor profile pages or review submission sections).
*   **`actions/review-actions.ts`**: Contains the `submitReview` server action, which is the primary mechanism for handling review submissions.
*   **`app/api/reviews/route.ts`**: An API route that also provides functionality for submitting reviews (potentially used by older parts of the system or for specific client-side scenarios).
*   **`reviews` Database Table**: Stores individual review details.
*   **`doctors` Database Table**: Stores the aggregated `rating` and `review_count` for each doctor.
*   **`updateDoctorRating` Helper Function**: A crucial function (present in both `review-actions.ts` and `app/api/reviews/route.ts`) that recalculates a doctor's overall rating after a new review.

## 3. Review Submission Workflow

1.  **Initiation**: A patient (assumed to be authenticated) navigates to a doctor's profile or a dedicated review submission interface.
2.  **Form Input**: The patient fills out a review form, providing ratings for predefined criteria:
    *   `clinical_competence`
    *   `communication_stats`
    *   `empathy_compassion`
    *   `time_management`
    *   `follow_up_care`
    *   `overall_satisfaction`
    *   `recommendation_rating` (stored in the `Recommendation` column)
    *   `additional_comments` (optional text)
    The form also captures `user_id` (of the patient) and `doctor_id` (of the doctor being reviewed).
3.  **Submission**: The form data is submitted, typically invoking the `submitReview` server action from `actions/review-actions.ts`.
4.  **Server-Side Processing (`submitReview` action)**:
    a.  **Authentication/Authorization**: The action assumes the `user_id` passed from the form is trustworthy (client-side authentication is expected prior to this).
    b.  **Data Parsing**: Form data is extracted. `doctor_id` is parsed to an integer.
    c.  **Average Rating Calculation**: An average `rating` for this specific review is calculated based on the individual criteria scores.
    d.  **Duplicate Review Check**: The system queries the `reviews` table to check if the `user_id` has already submitted a review for the given `doctor_id`. If a review exists, the process is halted, and an error is returned.
    e.  **Database Insertion**: If no duplicate is found, the new review data (including the calculated average `rating` and `review_date`) is inserted into the `reviews` table.
    f.  **Error Handling**: If insertion fails (e.g., database error, foreign key constraint violation), an appropriate error message is returned.
5.  **Doctor's Overall Rating Update (`updateDoctorRating` function)**:
    a.  This function is called immediately after a new review is successfully inserted.
    b.  It fetches all valid (non-null rating) reviews for the specified `doctor_id` from the `reviews` table.
    c.  It calculates the new average `rating` for the doctor by averaging the `rating` field of all their valid reviews.
    d.  It counts the total number of valid reviews to get the new `review_count`.
    e.  These new `rating` and `review_count` values are updated in the corresponding doctor's record in the `doctors` table.
6.  **Cache Revalidation**: After the review is submitted and the doctor's rating is updated, Next.js cache revalidation is triggered for relevant paths:
    *   `/doctors/[doctorId]`
    *   `/doctors/[doctorId]/reviews`
    This ensures that users viewing the doctor's profile or their reviews will see the updated information.
7.  **Response to Client**: A success or error message is returned to the client.

## 4. Data Storage

*   **`reviews` Table**:
    *   Stores each individual review with its criteria ratings, calculated average `rating` for that review, comments, `user_id`, `doctor_id`, and `review_date`.
    *   The `Recommendation` column stores the `recommendation_rating` from the form.
*   **`doctors` Table**:
    *   `community_rating`: Stores the overall average rating for the doctor, recalculated by `updateDoctorRating`. This is the primary rating used for ranking.
    *   `review_count`: Stores the total number of valid reviews for the doctor.

## 5. Moderation and Editing

*   **Moderation**: The current workflow, as inferred from the backend scripts, does not explicitly detail a review moderation process (e.g., admin approval before a review becomes public or affects ratings). If moderation is required, it would need to be implemented as an additional step.
*   **Editing/Deleting Reviews**: The documented actions and API routes do not include functionality for patients or admins to edit or delete existing reviews. Such features would require new server actions or API endpoints.

## 6. Displaying Ratings and Reviews

*   **Doctor Profiles**: Doctor profile pages likely fetch and display the doctor's overall `community_rating` and `review_count` from the `doctors` table.
*   **Review Lists**: Pages displaying individual reviews for a doctor (e.g., `/doctors/[doctorId]/reviews`) would fetch data from the `reviews` table, potentially joining with the `users` table to display reviewer information (as seen in `lib/hybrid-data-service.ts`'s `getDoctorReviews` function).
*   **League Tables**: The `community_rating` from the `doctors` table is used to sort doctors in league displays.

This workflow ensures that doctor ratings are dynamically updated based on patient feedback, directly influencing their standing in the application.
