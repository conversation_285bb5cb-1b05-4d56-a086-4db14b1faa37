# Review Verification and Moderation System

## Overview

The Review Verification and Moderation System provides a secure, privacy-compliant way to verify the authenticity of doctor reviews through appointment receipts and manage inappropriate content through a comprehensive flagging system.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Database Schema](#database-schema)
3. [Server Actions](#server-actions)
4. [Setup Instructions](#setup-instructions)
5. [Admin User Configuration](#admin-user-configuration)
6. [Usage Examples](#usage-examples)
7. [Security Features](#security-features)
8. [API Reference](#api-reference)

## System Architecture

### Components

- **User-Facing Actions**: Allow patients to upload appointment receipts for review verification
- **Admin-Only Actions**: Enable moderators to approve/reject verification requests and manage flagged content
- **Storage System**: Secure, temporary storage for appointment receipt images
- **Database Tables**: Track verification requests, review flags, and admin decisions
- **Role-Based Access**: Ensure only authorized admins can access moderation functions

### Data Flow

1. **User submits review** → Review created with `verification_status: 'unverified'`
2. **User uploads receipt** → Image stored temporarily, status changes to `'pending_verification'`
3. **Admin reviews proof** → Admin approves/rejects, image deleted immediately
4. **Status updated** → Review marked as `'verified'` or `'rejected'`
5. **Scores recalculated** → Doctor rankings updated based on verification status

## Database Schema

### Tables Created

#### `verification_proofs`
Stores temporary appointment receipt images for admin review.

```sql
CREATE TABLE public.verification_proofs (
    id SERIAL PRIMARY KEY,
    review_id INTEGER NOT NULL REFERENCES public.reviews(review_id) ON DELETE CASCADE,
    image_path TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### `review_flags`
Tracks reports of inappropriate reviews for admin moderation.

```sql
CREATE TABLE public.review_flags (
    id SERIAL PRIMARY KEY,
    review_id INTEGER NOT NULL REFERENCES public.reviews(review_id) ON DELETE CASCADE,
    reporter_user_id INTEGER,
    flag_reason TEXT NOT NULL CHECK (flag_reason IN (
        'inappropriate_content', 'spam', 'fake_review', 'personal_attack', 
        'off_topic', 'harassment', 'other'
    )),
    flag_description TEXT,
    status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'resolved', 'dismissed')),
    admin_notes TEXT,
    resolved_by INTEGER,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### `reviews` (Modified)
Added verification status tracking to existing reviews table.

```sql
ALTER TABLE public.reviews 
ADD COLUMN verification_status TEXT DEFAULT 'unverified' 
CHECK (verification_status IN ('unverified', 'pending_verification', 'verified', 'rejected'));
```

#### `auth_credentials` (Modified)
Added admin role tracking for user authorization.

```sql
ALTER TABLE public.auth_credentials 
ADD COLUMN admin_role BOOLEAN DEFAULT false;
```

### Storage Bucket

- **Name**: `appointment-verification`
- **Type**: Private (not public)
- **File Size Limit**: 5MB
- **Allowed Types**: `image/jpeg`, `image/jpg`, `image/png`, `image/webp`

## Server Actions

### User Actions

#### `requestReviewVerification(reviewId: string, formData: FormData)`
Allows authenticated users to upload appointment receipts for review verification.

**Parameters:**
- `reviewId`: ID of the review to verify
- `formData`: Form data containing the proof image file

**Security:**
- ✅ User authentication required
- ✅ File type validation (images only)
- ✅ File size validation (max 5MB)
- ✅ User can only verify their own reviews

### Admin Actions

#### `decideVerificationRequest(reviewId: string, decision: 'approved' | 'rejected')`
Admin-only action to approve or reject verification requests.

**Parameters:**
- `reviewId`: ID of the review to make decision on
- `decision`: Either `'approved'` or `'rejected'`

**Process:**
1. Verify admin role
2. Find and delete proof image (privacy compliance)
3. Update review verification status
4. Delete proof record
5. Recalculate doctor scores

#### `getPendingVerifications()`
Fetches all pending verification requests for admin review.

**Returns:**
- Array of verification requests with review details
- Admin-only access

#### `getOpenReviewFlags()`
Fetches all open review flags for admin moderation.

**Returns:**
- Array of open review flags
- Admin-only access

## Setup Instructions

### Step 1: Database Tables

Run these SQL commands in your **Supabase SQL Editor**:

```sql
-- 1. Create verification_proofs table
CREATE TABLE IF NOT EXISTS public.verification_proofs (
    id SERIAL PRIMARY KEY,
    review_id INTEGER NOT NULL REFERENCES public.reviews(review_id) ON DELETE CASCADE,
    image_path TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create review_flags table
CREATE TABLE IF NOT EXISTS public.review_flags (
    id SERIAL PRIMARY KEY,
    review_id INTEGER NOT NULL REFERENCES public.reviews(review_id) ON DELETE CASCADE,
    reporter_user_id INTEGER,
    flag_reason TEXT NOT NULL CHECK (flag_reason IN (
        'inappropriate_content', 'spam', 'fake_review', 'personal_attack',
        'off_topic', 'harassment', 'other'
    )),
    flag_description TEXT,
    status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'resolved', 'dismissed')),
    admin_notes TEXT,
    resolved_by INTEGER,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Add verification_status to reviews table
ALTER TABLE public.reviews 
ADD COLUMN IF NOT EXISTS verification_status TEXT DEFAULT 'unverified' 
CHECK (verification_status IN ('unverified', 'pending_verification', 'verified', 'rejected'));

-- 4. Add admin_role to auth_credentials table
ALTER TABLE public.auth_credentials 
ADD COLUMN IF NOT EXISTS admin_role BOOLEAN DEFAULT false;

-- 5. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_verification_proofs_review_id ON public.verification_proofs(review_id);
CREATE INDEX IF NOT EXISTS idx_review_flags_review_id ON public.review_flags(review_id);
CREATE INDEX IF NOT EXISTS idx_review_flags_status ON public.review_flags(status);
CREATE INDEX IF NOT EXISTS idx_reviews_verification_status ON public.reviews(verification_status);
CREATE INDEX IF NOT EXISTS idx_auth_credentials_admin_role ON public.auth_credentials(admin_role);
```

### Step 2: Storage Bucket

1. Go to **Supabase Dashboard > Storage**
2. Create new bucket named: `appointment-verification`
3. Set bucket to **PRIVATE** (not public)
4. Set file size limit: **5MB**
5. Set allowed MIME types: `image/jpeg, image/jpg, image/png, image/webp`

### Step 3: Configure Admin Users

See [Admin User Configuration](#admin-user-configuration) section below.

## Admin User Configuration

### Method 1: Using Setup Script (Recommended)

```bash
# List all existing users
node scripts/setup-admin-user.js

# Make a specific user an admin
node scripts/setup-admin-user.js <EMAIL>
```

### Method 2: Manual SQL Setup

```sql
-- Make a specific user an admin
UPDATE public.auth_credentials 
SET admin_role = true 
WHERE email = '<EMAIL>';

-- Verify the admin user was set up correctly
SELECT email, user_type, admin_role 
FROM public.auth_credentials 
WHERE admin_role = true;
```

### Admin Role Verification

The system checks for admin privileges in this order:
1. `admin_role = true` in `auth_credentials` table
2. `user_type = 'admin'` in database
3. `userType = 'admin'` or `role = 'admin'` in JWT token

## Usage Examples

### Frontend Implementation

#### User Verification Request

```typescript
'use client';

import { requestReviewVerification } from '@/actions/review-actions';

export function VerificationUpload({ reviewId }: { reviewId: string }) {
  const handleSubmit = async (formData: FormData) => {
    const result = await requestReviewVerification(reviewId, formData);
    
    if (result.success) {
      alert('Verification request submitted successfully!');
    } else {
      alert(`Error: ${result.error}`);
    }
  };

  return (
    <form action={handleSubmit}>
      <input 
        type="file" 
        name="proof_image" 
        accept="image/*" 
        required 
      />
      <button type="submit">Submit Verification</button>
    </form>
  );
}
```

#### Admin Moderation Dashboard

```typescript
'use client';

import { 
  getPendingVerifications, 
  decideVerificationRequest,
  getOpenReviewFlags 
} from '@/actions/admin-actions';

export function AdminDashboard() {
  const [pendingVerifications, setPendingVerifications] = useState([]);
  const [reviewFlags, setReviewFlags] = useState([]);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    const [verificationsResult, flagsResult] = await Promise.all([
      getPendingVerifications(),
      getOpenReviewFlags()
    ]);

    if (verificationsResult.data) {
      setPendingVerifications(verificationsResult.data);
    }
    
    if (flagsResult.data) {
      setReviewFlags(flagsResult.data);
    }
  };

  const handleVerificationDecision = async (reviewId: string, decision: 'approved' | 'rejected') => {
    const result = await decideVerificationRequest(reviewId, decision);
    
    if (result.success) {
      alert(`Verification ${decision} successfully!`);
      loadData(); // Refresh data
    } else {
      alert(`Error: ${result.error}`);
    }
  };

  return (
    <div>
      <h2>Pending Verifications</h2>
      {pendingVerifications.map((verification) => (
        <div key={verification.id}>
          <p>Review ID: {verification.review_id}</p>
          <button onClick={() => handleVerificationDecision(verification.review_id, 'approved')}>
            Approve
          </button>
          <button onClick={() => handleVerificationDecision(verification.review_id, 'rejected')}>
            Reject
          </button>
        </div>
      ))}

      <h2>Review Flags</h2>
      {reviewFlags.map((flag) => (
        <div key={flag.id}>
          <p>Reason: {flag.flag_reason}</p>
          <p>Description: {flag.flag_description}</p>
        </div>
      ))}
    </div>
  );
}
```

## Security Features

### Privacy Compliance
- ✅ **Immediate Image Deletion**: Proof images are deleted immediately after admin decisions
- ✅ **Temporary Storage**: Images are only stored during the review process
- ✅ **Private Bucket**: Storage bucket is private, not publicly accessible

### Access Control
- ✅ **Role-Based Access**: Only admins can access moderation functions
- ✅ **Authentication Required**: All actions require valid user authentication
- ✅ **User Ownership**: Users can only verify their own reviews

### Data Validation
- ✅ **File Type Validation**: Only image files are accepted
- ✅ **File Size Limits**: Maximum 5MB file size
- ✅ **Input Sanitization**: All inputs are validated and sanitized

### Audit Trail
- ✅ **Action Logging**: All admin actions are logged with timestamps
- ✅ **User Tracking**: Admin decisions are tracked by user ID
- ✅ **Status History**: Review status changes are tracked

## API Reference

### Response Format

All server actions return a consistent response format:

```typescript
interface ActionResponse {
  success: boolean;
  error?: string;
  data?: any;
}
```

### Error Codes

- `"Authentication required"` - User not logged in
- `"Permission denied. Admin access required."` - Non-admin trying to access admin functions
- `"Please provide a valid image file"` - Invalid file type or missing file
- `"Image file must be smaller than 5MB"` - File too large
- `"Review ID and decision are required"` - Missing required parameters

### File Requirements

- **Supported formats**: JPEG, JPG, PNG, WebP
- **Maximum size**: 5MB
- **Minimum size**: 1KB
- **Required**: File must be a valid image

## Troubleshooting

### Common Issues

1. **"Permission denied" errors**
   - Verify user has admin role set in database
   - Check admin setup using verification script

2. **File upload failures**
   - Ensure storage bucket exists and is properly configured
   - Check file size and type requirements

3. **Database errors**
   - Verify all migration scripts have been run
   - Check table permissions and indexes

### Verification Commands

```bash
# Test database connection
node scripts/setup-admin-user.js

# Verify admin user setup
node scripts/setup-admin-user.js <EMAIL>

# Check table structure
# Run in Supabase SQL Editor:
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('verification_proofs', 'review_flags');
```

## Maintenance

### Regular Tasks

1. **Monitor storage usage** - Check appointment-verification bucket size
2. **Review admin logs** - Monitor admin action logs for unusual activity
3. **Update admin users** - Add/remove admin privileges as needed
4. **Database cleanup** - Ensure old verification records are properly cleaned

### Performance Optimization

- All tables include appropriate indexes for common queries
- Storage bucket configured for optimal performance
- Server actions include proper error handling and logging

---

**Last Updated**: January 2025  
**Version**: 1.0  
**Maintainer**: Development Team
