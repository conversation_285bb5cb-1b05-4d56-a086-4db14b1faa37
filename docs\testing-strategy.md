# Testing Strategy

This document outlines the current testing strategy for the Doctors Leagues application, based on the available files and scripts.

## 1. Overview

The project currently has a focused integration test for user registration. A broader, more comprehensive testing strategy covering different types of tests (unit, integration, end-to-end) and utilizing standard testing frameworks would be beneficial for ensuring application quality and stability.

## 2. Current Testing Setup

### 2.1. Registration Integration Test

*   **Location**: `__tests__/registration-test.js`
*   **Purpose**: To test the core user registration functionality for both doctors and patients by directly interacting with the Supabase backend (Auth and Database).
*   **Execution**:
    *   Run via the npm script: `npm run test:registration`
    *   This executes the script directly using Node.js: `node __tests__/registration-test.js`
*   **Environment**:
    *   The script is a Node.js script.
    *   It uses the `dotenv` package to load environment variables from a `.env.local` file (Supabase URL, service role key).
*   **Methodology**:
    1.  **Supabase Admin Client**: A Supabase client is initialized with the service role key, allowing it to perform administrative actions on Supabase Auth and bypass RLS for database operations.
    2.  **Test Data**: Dynamically generated test data (e.g., unique emails and usernames using `Date.now()`) is used for each test run to avoid conflicts.
    3.  **Doctor Registration Test (`testDoctorRegistration`)**:
        *   Creates a doctor auth user using `supabaseAdmin.auth.admin.createUser()`.
        *   Inserts a corresponding record into the `users` table.
        *   Inserts a record into the `doctors_registration` table.
        *   Verifies that the data was correctly inserted into `doctors_registration`.
    4.  **Patient Registration Test (`testPatientRegistration`)**:
        *   Creates a patient auth user.
        *   Inserts a record into the `users` table.
        *   Verifies that the data was correctly inserted into the `users` table.
    5.  **Cleanup**: A `cleanupTestUser(userId)` function is provided to delete the created test users from all relevant tables (`doctors_registration`, `doctors`, `users`) and Supabase Auth after a test (especially on failure, though it could be used more broadly for teardown).
    6.  **Assertions**: The script uses simple `console.log` messages (via a `logResult` helper) to indicate test pass/fail status. It relies on throwing and catching errors for failure detection rather than a formal assertion library (like Chai or Jest's `expect`).
*   **Scope**: This is an integration test focusing on the backend logic of registration. It does not test the frontend UI components or the API endpoints directly (e.g., it doesn't make HTTP requests to `/api/auth/custom/register`).

### 2.2. Testing Libraries and Frameworks

*   **Dependencies**: The `package.json` file does not list common JavaScript testing frameworks like Jest, Mocha, React Testing Library, Cypress, or Playwright in its main dependencies or devDependencies.
*   **Current Approach**: Testing seems to be done via custom Node.js scripts using built-in capabilities or minimal external libraries (like `dotenv`).

## 3. Suggested Enhancements and Future Strategy

To build a more robust testing suite, consider the following:

### 3.1. Unit Tests

*   **Purpose**: To test individual functions, components, and modules in isolation.
*   **Tools**:
    *   **Jest**: A popular JavaScript testing framework.
    *   **React Testing Library**: For testing React components by interacting with them as a user would.
*   **Coverage**:
    *   Utility functions (e.g., in `lib/utils.ts`, `lib/hybrid-data-service.ts` helper functions).
    *   Individual React components (props, rendering, basic interactions).
    *   Logic within Server Actions and API route handlers (mocking database/external calls).

### 3.2. Integration Tests

*   **Purpose**: To test the interaction between different parts of the application.
*   **Current Test**: The existing `registration-test.js` is a good example of a backend integration test. This approach can be expanded.
*   **Tools**:
    *   Jest can also be used for integration tests.
    *   **Supertest** (or similar HTTP assertion library): For testing API endpoints by making actual HTTP requests.
*   **Coverage**:
    *   API Routes: Test request/response cycles, authentication, validation.
    *   Server Actions: Test their behavior when called, including database interactions (potentially with a test database or mocked Supabase client).
    *   Frontend-Backend Interaction: Test data flow from frontend components through API calls/Server Actions to the backend and back.

### 3.3. End-to-End (E2E) Tests

*   **Purpose**: To test complete user flows through the application in a browser-like environment.
*   **Tools**:
    *   **Cypress**: Popular for E2E testing of web applications.
    *   **Playwright**: Another powerful E2E testing tool from Microsoft.
*   **Coverage**:
    *   User registration and login flows.
    *   Doctor search and profile viewing.
    *   Review submission process.
    *   League table navigation and display.
    *   Key patient and doctor dashboard functionalities.

### 3.4. Test Environment

*   **Dedicated Test Database**: Set up a separate Supabase project or schema for testing to avoid polluting development or production data.
*   **Mocking**: Utilize mocking for external services (e.g., Mailtrap for email sending in tests where actual email delivery isn't the focus) or parts of Supabase when unit testing.
*   **CI/CD Integration**: Integrate test runs into the Continuous Integration / Continuous Deployment pipeline to ensure tests pass before deployment.

### 3.5. Test Conventions

*   **File Naming**: Adopt a consistent naming convention for test files (e.g., `*.test.ts`, `*.spec.ts`).
*   **Test Structure**: Use standard test patterns like Arrange-Act-Assert.
*   **Code Coverage**: Aim for a reasonable level of code coverage and monitor it.

## 4. Running Tests

*   Currently, only `npm run test:registration` is defined.
*   If a framework like Jest is adopted, `package.json` scripts would be updated (e.g., `"test": "jest"`).

By expanding on the existing integration test and incorporating unit and E2E tests with appropriate frameworks, the application's reliability and maintainability can be significantly improved.
