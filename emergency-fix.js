// Emergency fix script for doctors dashboard authentication issue
// Run this in the browser console when you're stuck

// Check current auth state
console.log("Checking current auth state...");
try {
  const jwtToken = localStorage.getItem('jwtToken');
  const userData = localStorage.getItem('userData');
  const userRole = localStorage.getItem('userRole');
  
  console.log("JWT Token exists:", !!jwtToken);
  console.log("User data exists:", !!userData);
  console.log("User role:", userRole);
  
  if (userData) {
    const parsedUserData = JSON.parse(userData);
    console.log("User data:", parsedUserData);
    
    // Check if test doctor account but missing ID
    if (parsedUserData.email === '<EMAIL>' && 
        (!parsedUserData.userId || parsedUserData.userId === undefined)) {
      console.log("Test doctor account detected with missing ID. Fixing...");
      
      // Set the correct ID
      parsedUserData.userId = 4097;
      localStorage.setItem('userData', JSON.stringify(parsedUserData));
      console.log("Fixed user data:", parsedUserData);
      console.log("Please refresh the page now.");
    } else if (!parsedUserData.userId) {
      console.log("User ID is missing. Setting emergency ID for test doctor...");
      
      // Force emergency test doctor data
      const emergencyUser = {
        userId: 4097,
        email: '<EMAIL>',
        userType: 'doctor'
      };
      localStorage.setItem('userData', JSON.stringify(emergencyUser));
      localStorage.setItem('userRole', 'doctor');
      console.log("Set emergency user data:", emergencyUser);
      console.log("Please refresh the page now.");
    } else {
      console.log("User ID exists:", parsedUserData.userId);
    }
  } else {
    console.log("No user data in localStorage. Setting emergency test doctor data...");
    
    // Create emergency test doctor data
    const emergencyUser = {
      userId: 4097,
      email: '<EMAIL>',
      userType: 'doctor'
    };
    
    // Only add test data if there's a JWT token
    if (jwtToken) {
      localStorage.setItem('userData', JSON.stringify(emergencyUser));
      localStorage.setItem('userRole', 'doctor');
      console.log("Set emergency user data:", emergencyUser);
      console.log("Please refresh the page now.");
    } else {
      console.log("No JWT token found. Please log in again with your credentials.");
    }
  }
} catch (error) {
  console.error("Error checking auth state:", error);
  
  // Clear broken auth state
  localStorage.removeItem('jwtToken');
  localStorage.removeItem('userData');
  localStorage.removeItem('userRole');
  
  console.log("Cleared potentially corrupt auth data. Please log in again.");
}

// Function to force logout
const forceLogout = () => {
  localStorage.removeItem('jwtToken');
  localStorage.removeItem('userData');
  localStorage.removeItem('userRole');
  console.log("Logged out. Refreshing page...");
  window.location.href = '/doctor/login';
};

// Function to add emergency logout button
const addEmergencyLogoutButton = () => {
  const logoutBtn = document.createElement('button');
  logoutBtn.innerText = 'Emergency Logout';
  logoutBtn.style = 'position: fixed; top: 20px; right: 20px; z-index: 9999; background: red; color: white; padding: 10px 15px; border-radius: 5px; border: none; cursor: pointer;';
  logoutBtn.onclick = forceLogout;
  document.body.appendChild(logoutBtn);
  console.log("Added emergency logout button to the page.");
};

// Add the emergency logout button
addEmergencyLogoutButton();

console.log("To force logout, call forceLogout() or click the emergency button."); 