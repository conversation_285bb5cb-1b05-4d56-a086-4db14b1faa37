// <PERSON>ript to fix auth_id in the doctors table
const { createClient } = require('@supabase/supabase-js');

// Create Supabase client
const supabaseUrl = 'https://uapbzzscckhtptliynyj.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q';
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  console.log('Fixing auth_id for test doctor account...');
  
  // Get auth credentials for test doctor
  const { data: authData, error: authError } = await supabase
    .from('auth_credentials')
    .select('*')
    .eq('email', '<EMAIL>')
    .single();
  
  if (authError) {
    console.error('Error fetching auth credentials:', authError);
    return;
  }
  
  console.log('Auth credentials found:', {
    id: authData.id,
    email: authData.email,
    user_profile_id: authData.user_profile_id,
    user_type: authData.user_type,
  });
  
  // Get table info to see the column type
  const { data: tableInfo, error: tableError } = await supabase
    .rpc('schema_details', { target_table: 'doctors' });
  
  if (tableError) {
    console.error('Error getting table info:', tableError);
  } else {
    console.log('Column info for auth_id:', tableInfo.find(col => col.column_name === 'auth_id'));
  }
  
  // Since auth_id expects a UUID, but we're using auth_credentials.user_profile_id as a number,
  // our approach needs to change
  
  // First, let's modify the dashboard code to look by doctor_id not auth_id (already done in a previous edit)
  console.log('Dashboard code already updated to use doctor_id instead of auth_id');
  
  // Check if we can create a functional database view to help
  // For now, let's just check if the doctor record with the right ID exists
  const { data: doctorCheck, error: doctorCheckError } = await supabase
    .from('doctors')
    .select('doctor_id, fullname, email')
    .eq('doctor_id', authData.user_profile_id)
    .single();
  
  if (doctorCheckError) {
    console.error('Error checking doctor record:', doctorCheckError);
    return;
  }
  
  console.log('Doctor record found (dashboard will use this):', {
    doctor_id: doctorCheck.doctor_id,
    fullname: doctorCheck.fullname,
    email: doctorCheck.email
  });
  
  console.log('Fix approach: We have modified the dashboard code to query the doctors table directly using doctor_id instead of auth_id. This avoids the need to update the auth_id column with a UUID.');
}

main().catch(console.error); 