-- S<PERSON> Script to fix profile image access in Supabase
-- Run this in your Supabase SQL Editor

-- 1. Make sure the profile-images bucket exists (create if not)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM storage.buckets WHERE name = 'profile-images'
    ) THEN
        INSERT INTO storage.buckets (id, name, public)
        VALUES ('profile-images', 'profile-images', true);
    END IF;
END $$;

-- 2. Update bucket to be public if it's not already
UPDATE storage.buckets
SET public = true
WHERE name = 'profile-images';

-- 3. Update the doctors table to ensure profile_image field exists
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'doctors' 
        AND column_name = 'profile_image'
    ) THEN
        ALTER TABLE public.doctors ADD COLUMN profile_image TEXT;
    END IF;
END $$;

-- 4. Update the users table to ensure profile_image field exists
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users' 
        AND column_name = 'profile_image'
    ) THEN
        ALTER TABLE public.users ADD COLUMN profile_image TEXT;
    END IF;
END $$;

-- 5. Update any existing doctors with profile images stored in a different field
UPDATE public.doctors
SET profile_image = image_path
WHERE image_path IS NOT NULL AND profile_image IS NULL;

-- 6. Fix any profile image paths that don't include the bucket name
UPDATE public.doctors
SET profile_image = CONCAT('profile-images/', profile_image)
WHERE profile_image IS NOT NULL 
  AND profile_image NOT LIKE 'profile-images/%'
  AND profile_image NOT LIKE 'http%';

UPDATE public.users
SET profile_image = CONCAT('profile-images/', profile_image)
WHERE profile_image IS NOT NULL 
  AND profile_image NOT LIKE 'profile-images/%'
  AND profile_image NOT LIKE 'http%';

-- 7. Fix any doctors with NULL profile images but have images in storage
-- This requires knowing the pattern of your stored files, adjust as needed
UPDATE public.doctors d
SET profile_image = CONCAT('profile-images/doctor-', d.doctor_id, '.jpg')
WHERE profile_image IS NULL AND doctor_id IS NOT NULL;

-- After running this script, go to the Supabase dashboard:
-- 1. Navigate to Storage > Buckets > profile-images
-- 2. Click "Settings" tab
-- 3. Make sure "Public bucket" is enabled
-- 4. Under "Policies" tab, add these policies manually:
--    - SELECT policy: Allow access to everyone - No conditions
--    - INSERT policy: Allow access to authenticated users
--    - UPDATE policy: Allow access to authenticated users
--    - DELETE policy: Allow access to authenticated users (optional)

-- Done! Your profile images should now be accessible 