<!DOCTYPE html><html lang="en" class="scroll-smooth"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" href="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo-gO7TTezH8tR3LvsxNVrbtcdUVAqKGB.png"/><link rel="stylesheet" href="/_next/static/css/app/layout.css?v=1742306595958" data-precedence="next_static/css/app/layout.css"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack.js?v=1742306595958"/><script src="/_next/static/chunks/main-app.js?v=1742306595958" async=""></script><script src="/_next/static/chunks/app-pages-internals.js" async=""></script><script src="/_next/static/chunks/app/head-to-head/page.js" async=""></script><script src="/_next/static/chunks/app/layout.js" async=""></script><title>Doctor&#x27;s Leagues</title><meta name="description" content="Where Healthcare Heroes Compete for Your Trust"/><meta name="generator" content="v0.dev"/><script src="/_next/static/chunks/polyfills.js" noModule=""></script></head><body class="font-sans"><header class="fixed top-0 left-0 right-0 z-50 transition-all duration-500 bg-gradient-to-r from-black/90 via-black/95 to-black/90"><div class="container mx-auto px-4"><div class="flex h-16 items-center justify-between"><a class="flex items-center space-x-2 transition-all duration-300 hover:scale-105" href="/"><div style="transform:none"><img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo-gO7TTezH8tR3LvsxNVrbtcdUVAqKGB.png" alt="Doctor&#x27;s Leagues" class="h-10 w-10 drop-shadow-[0_0_10px_rgba(0,200,255,0.3)]"/></div><div><span class="text-primary font-bold text-xl tracking-tight">Doctor&#x27;s <span class="text-white">Leagues</span></span><div class="text-xs text-gray-400 -mt-1">Where Healthcare Heroes Compete</div></div></a><nav class="hidden md:flex items-center space-x-1"><a href="/about"><button class="text-white/90 hover:text-primary transition-colors duration-300 flex items-center gap-2 group relative px-4 py-2 overflow-hidden"><span class="absolute inset-0 w-full h-full bg-gradient-to-r from-primary/0 via-primary/0 to-primary/0 group-hover:via-primary/10 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-info w-4 h-4 group-hover:scale-110 transition-transform duration-300 text-primary"><circle cx="12" cy="12" r="10"></circle><path d="M12 16v-4"></path><path d="M12 8h.01"></path></svg><span class="relative">About Us</span></button></a><button class="text-white/90 hover:text-primary transition-colors duration-300 flex items-center gap-2 group relative px-4 py-2 overflow-hidden" type="button" id="radix-«R149l7»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><span class="absolute inset-0 w-full h-full bg-gradient-to-r from-primary/0 via-primary/0 to-primary/0 group-hover:via-primary/10 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe w-4 h-4 group-hover:scale-110 transition-transform duration-300 text-primary"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg><span class="relative">Leagues</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down w-4 h-4 transition-transform duration-300 text-primary/70"><path d="m6 9 6 6 6-6"></path></svg></button><a href="/standings"><button class="text-white/90 hover:text-primary transition-colors duration-300 flex items-center gap-2 group relative px-4 py-2 overflow-hidden"><span class="absolute inset-0 w-full h-full bg-gradient-to-r from-primary/0 via-primary/0 to-primary/0 group-hover:via-primary/10 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trophy w-4 h-4 group-hover:scale-110 transition-transform duration-300 text-primary"><path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"></path><path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"></path><path d="M4 22h16"></path><path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"></path><path d="M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22"></path><path d="M18 2H6v7a6 6 0 0 0 12 0V2Z"></path></svg><span class="relative">Standings</span></button></a><a href="/head-to-head"><button class="text-white/90 hover:text-primary transition-colors duration-300 flex items-center gap-2 group relative px-4 py-2 overflow-hidden"><span class="absolute inset-0 w-full h-full bg-gradient-to-r from-primary/0 via-primary/0 to-primary/0 group-hover:via-primary/10 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-4 h-4 group-hover:scale-110 transition-transform duration-300 text-primary"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg><span class="relative">Head-to-Head</span></button></a><a href="/teams"><button class="text-white/90 hover:text-primary transition-colors duration-300 flex items-center gap-2 group relative px-4 py-2 overflow-hidden"><span class="absolute inset-0 w-full h-full bg-gradient-to-r from-primary/0 via-primary/0 to-primary/0 group-hover:via-primary/10 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-building w-4 h-4 group-hover:scale-110 transition-transform duration-300 text-primary"><rect width="16" height="20" x="4" y="2" rx="2" ry="2"></rect><path d="M9 22v-4h6v4"></path><path d="M8 6h.01"></path><path d="M16 6h.01"></path><path d="M12 6h.01"></path><path d="M12 10h.01"></path><path d="M12 14h.01"></path><path d="M16 10h.01"></path><path d="M16 14h.01"></path><path d="M8 10h.01"></path><path d="M8 14h.01"></path></svg><span class="relative">Teams</span></button></a><a href="/fixtures"><button class="text-white/90 hover:text-primary transition-colors duration-300 flex items-center gap-2 group relative px-4 py-2 overflow-hidden"><span class="absolute inset-0 w-full h-full bg-gradient-to-r from-primary/0 via-primary/0 to-primary/0 group-hover:via-primary/10 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar w-4 h-4 group-hover:scale-110 transition-transform duration-300 text-primary"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg><span class="relative">Fixtures</span></button></a></nav><div class="hidden md:flex items-center space-x-4"><button class="relative overflow-hidden bg-gradient-to-br from-primary/90 to-primary/70 hover:from-primary hover:to-primary/80 text-white shadow-md hover:shadow-[0_0_15px_rgba(0,160,255,0.5)] rounded-md flex items-center gap-2 font-medium transition-all duration-300 border border-primary/20 group transform hover:scale-105"><span class="absolute inset-0 w-full h-full bg-gradient-to-r from-primary/0 via-primary/0 to-primary/0 group-hover:via-primary/20 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span><div class="text-white relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-stethoscope w-4 h-4"><path d="M11 2v2"></path><path d="M5 2v2"></path><path d="M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1"></path><path d="M8 15a6 6 0 0 0 12 0v-3"></path><circle cx="20" cy="10" r="2"></circle></svg></div><span class="relative">Be a Referee or a Player</span><span class="absolute bottom-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-white/50 to-transparent transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></span></button><button class="relative overflow-hidden bg-gradient-to-br from-green-600/90 to-green-700/70 hover:from-green-600 hover:to-green-700/80 text-white border border-green-500/30 hover:border-green-500/50 rounded-md transition-all duration-300 hover:shadow-[0_0_10px_rgba(34,197,94,0.4)] group transform hover:scale-105" data-join-match-button="true"><span class="absolute inset-0 w-full h-full bg-gradient-to-r from-green-500/0 via-green-500/0 to-green-500/0 group-hover:via-green-500/20 transition-all duration-500 transform -translate-x-full group-hover:translate-x-0"></span><span class="relative">Join Match</span><span class="absolute bottom-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-white/50 to-transparent transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></span></button></div><button class="md:hidden text-primary hover:text-white p-2 rounded-md transition-colors duration-300 relative overflow-hidden group"><span class="absolute inset-0 bg-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-md"></span><div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></div></button></div></div></header><main class="min-h-[calc(100vh-4rem)] pt-16"><div class="min-h-screen bg-gradient-to-b from-black via-black/95 to-primary/5 text-white"><div class="container mx-auto px-4 py-16"><div class="text-center mb-12" style="opacity:0;transform:translateY(-20px)"><h1 class="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-white/80 mb-4 flex items-center justify-center gap-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trophy w-8 h-8 text-yellow-500"><path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"></path><path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"></path><path d="M4 22h16"></path><path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"></path><path d="M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22"></path><path d="M18 2H6v7a6 6 0 0 0 12 0V2Z"></path></svg>Head-to-Head Comparison<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trophy w-8 h-8 text-yellow-500"><path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"></path><path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"></path><path d="M4 22h16"></path><path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"></path><path d="M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22"></path><path d="M18 2H6v7a6 6 0 0 0 12 0V2Z"></path></svg></h1><p class="text-white/70 max-w-3xl mx-auto">Compare medical professionals side by side to see how they match up in the Doctor&#x27;s League. Analyze their performance, ratings, and specialties to make informed healthcare decisions.</p><div class="flex flex-wrap justify-center gap-6 mt-8"><div class="flex items-center gap-2 bg-white/5 px-4 py-2 rounded-full"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-medal w-4 h-4 text-yellow-500"><path d="M7.21 15 2.66 7.14a2 2 0 0 1 .13-2.2L4.4 2.8A2 2 0 0 1 6 2h12a2 2 0 0 1 1.6.8l1.6 2.14a2 2 0 0 1 .14 2.2L16.79 15"></path><path d="M11 12 5.12 2.2"></path><path d="m13 12 5.88-9.8"></path><path d="M8 7h8"></path><circle cx="12" cy="17" r="5"></circle><path d="M12 18v-2h-.5"></path></svg><span class="text-white/80">Compare Champions</span></div><div class="flex items-center gap-2 bg-white/5 px-4 py-2 rounded-full"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-activity w-4 h-4 text-primary"><path d="M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2"></path></svg><span class="text-white/80">Performance Stats</span></div><div class="flex items-center gap-2 bg-white/5 px-4 py-2 rounded-full"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart w-4 h-4 text-red-500"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path></svg><span class="text-white/80">Patient Satisfaction</span></div></div></div><div class="relative py-12"><div class="absolute inset-0 flex items-center justify-center"><div class="w-full max-w-5xl mx-auto relative h-[1px] bg-gradient-to-r from-transparent via-primary/30 to-transparent"><div class="absolute top-1/2 -translate-y-1/2 w-24 h-12" style="left:0px"><svg viewBox="0 0 120 40" fill="none" class="w-full h-full text-primary drop-shadow-[0_0_3px_rgba(0,200,255,0.5)]" preserveAspectRatio="none"><path d="M0 20h10l5-10 5 20 5-10h10l5-5 5 10 5-15 10 15 10 0 5-10 5 20 5-10h10l5-5 5 10 5-15 10 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" vector-effect="non-scaling-stroke" opacity="0" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path></svg></div></div></div><div class="relative flex justify-center"><div class="bg-black px-3 py-3 rounded-full border border-primary/30 shadow-[0_0_10px_rgba(0,200,255,0.2)]"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-primary"><path d="M18 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V4C20 2.89543 19.1046 2 18 2Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M9 14H15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12 11V17" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M9 7H9.01" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12 7H12.01" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M15 7H15.01" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></div></div></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 items-start mt-8"><div style="opacity:0;transform:scale(0.9)"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative bg-gradient-to-b from-black/80 to-black/60 border border-primary/20 shadow-lg overflow-hidden"><div class="p-6 pt-0 p-4 space-y-4"><div class="space-y-2"><label class="text-sm font-medium flex items-center gap-2 text-white/90"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-earth w-4 h-4 text-primary"><path d="M21.54 15H17a2 2 0 0 0-2 2v4.54"></path><path d="M7 3.34V5a3 3 0 0 0 3 3a2 2 0 0 1 2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2c0-1.1.9-2 2-2h3.17"></path><path d="M11 21.95V18a2 2 0 0 0-2-2a2 2 0 0 1-2-2v-1a2 2 0 0 0-2-2H2.05"></path><circle cx="12" cy="12" r="10"></circle></svg>Select Country</label><button class="w-full justify-start text-left font-normal bg-black/50 border-primary/30 hover:bg-primary/10 text-white" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-«R4dasuql7»" data-state="closed">Select country...</button></div><div class="space-y-2"><label class="text-sm font-medium flex items-center gap-2 text-white/90"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-building w-4 h-4 text-primary"><rect width="16" height="20" x="4" y="2" rx="2" ry="2"></rect><path d="M9 22v-4h6v4"></path><path d="M8 6h.01"></path><path d="M16 6h.01"></path><path d="M12 6h.01"></path><path d="M12 10h.01"></path><path d="M12 14h.01"></path><path d="M16 10h.01"></path><path d="M16 14h.01"></path><path d="M8 10h.01"></path><path d="M8 14h.01"></path></svg>Select Hospital</label><button class="w-full justify-start text-left font-normal bg-black/50 border-primary/30 hover:bg-primary/10 text-white opacity-50 cursor-not-allowed" disabled="" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-«R4lasuql7»" data-state="closed">Select hospital...</button></div><div class="space-y-2"><label class="text-sm font-medium flex items-center gap-2 text-white/90"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user w-4 h-4 text-primary"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>Select Doctor</label><button class="w-full justify-start text-left font-normal bg-black/50 border-primary/30 hover:bg-primary/10 text-white opacity-50 cursor-not-allowed" disabled="" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-«R4tasuql7»" data-state="closed">Select doctor...</button></div></div></div></div><div style="opacity:0;transform:scale(0.9)"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative bg-gradient-to-b from-black/80 to-black/60 border border-primary/20 shadow-lg overflow-hidden"><div class="p-6 pt-0 p-4 space-y-4"><div class="space-y-2"><label class="text-sm font-medium flex items-center gap-2 text-white/90"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-earth w-4 h-4 text-primary"><path d="M21.54 15H17a2 2 0 0 0-2 2v4.54"></path><path d="M7 3.34V5a3 3 0 0 0 3 3a2 2 0 0 1 2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2c0-1.1.9-2 2-2h3.17"></path><path d="M11 21.95V18a2 2 0 0 0-2-2a2 2 0 0 1-2-2v-1a2 2 0 0 0-2-2H2.05"></path><circle cx="12" cy="12" r="10"></circle></svg>Select Country</label><button class="w-full justify-start text-left font-normal bg-black/50 border-primary/30 hover:bg-primary/10 text-white" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-«R4dcsuql7»" data-state="closed">Select country...</button></div><div class="space-y-2"><label class="text-sm font-medium flex items-center gap-2 text-white/90"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-building w-4 h-4 text-primary"><rect width="16" height="20" x="4" y="2" rx="2" ry="2"></rect><path d="M9 22v-4h6v4"></path><path d="M8 6h.01"></path><path d="M16 6h.01"></path><path d="M12 6h.01"></path><path d="M12 10h.01"></path><path d="M12 14h.01"></path><path d="M16 10h.01"></path><path d="M16 14h.01"></path><path d="M8 10h.01"></path><path d="M8 14h.01"></path></svg>Select Hospital</label><button class="w-full justify-start text-left font-normal bg-black/50 border-primary/30 hover:bg-primary/10 text-white opacity-50 cursor-not-allowed" disabled="" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-«R4lcsuql7»" data-state="closed">Select hospital...</button></div><div class="space-y-2"><label class="text-sm font-medium flex items-center gap-2 text-white/90"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user w-4 h-4 text-primary"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>Select Doctor</label><button class="w-full justify-start text-left font-normal bg-black/50 border-primary/30 hover:bg-primary/10 text-white opacity-50 cursor-not-allowed" disabled="" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-«R4tcsuql7»" data-state="closed">Select doctor...</button></div></div></div></div><div style="opacity:0;transform:scale(0.9)"><button class="w-full h-full min-h-[200px] rounded-xl border-2 border-dashed border-primary/20 hover:border-primary hover:bg-primary/5 p-0 relative group bg-black/30 text-white"><div class="flex flex-col items-center justify-center gap-3"><div class="relative w-12 h-12 flex items-center justify-center"><div class="absolute inset-0 rounded-full bg-primary/10 group-hover:bg-primary/20 transition-colors"></div><div class="relative transform group-hover:scale-110 transition-transform"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-stethoscope w-6 h-6 text-primary"><path d="M11 2v2"></path><path d="M5 2v2"></path><path d="M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1"></path><path d="M8 15a6 6 0 0 0 12 0v-3"></path><circle cx="20" cy="10" r="2"></circle></svg></div></div><span class="text-white/70 group-hover:text-white transition-colors">Add Another Doctor</span></div></button></div></div></div></div><!--$--><!--/$--><!--$--><!--/$--></main><footer class="bg-black text-white py-12"><div class="container mx-auto px-4"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"><div><div class="flex items-center space-x-2 mb-4"><img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo-gO7TTezH8tR3LvsxNVrbtcdUVAqKGB.png" alt="Doctor&#x27;s Leagues" class="h-10 w-10"/><span class="text-white font-bold text-xl">Doctor&#x27;s Leagues</span></div><p class="text-gray-400 mb-4">Where Healthcare Heroes Compete for Your Trust</p><div class="flex space-x-4"><a href="#" class="text-gray-400 hover:text-primary transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-twitter h-5 w-5"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg></a><a href="#" class="text-gray-400 hover:text-primary transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-facebook h-5 w-5"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg></a><a href="#" class="text-gray-400 hover:text-primary transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-instagram h-5 w-5"><rect width="20" height="20" x="2" y="2" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" x2="17.51" y1="6.5" y2="6.5"></line></svg></a><a href="#" class="text-gray-400 hover:text-primary transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-linkedin h-5 w-5"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect width="4" height="12" x="2" y="9"></rect><circle cx="4" cy="4" r="2"></circle></svg></a></div></div><div><h3 class="text-lg font-semibold mb-4 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trophy h-5 w-5 text-primary mr-2"><path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"></path><path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"></path><path d="M4 22h16"></path><path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"></path><path d="M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22"></path><path d="M18 2H6v7a6 6 0 0 0 12 0V2Z"></path></svg>Leagues</h3><ul class="space-y-2"><li><a href="/divisions/1" class="text-gray-400 hover:text-white transition-colors">Bahrain</a></li><li><a href="/divisions/5" class="text-gray-400 hover:text-white transition-colors">Kuwait</a></li><li><a href="/divisions/6" class="text-gray-400 hover:text-white transition-colors">Oman</a></li><li><a href="/divisions/4" class="text-gray-400 hover:text-white transition-colors">Qatar</a></li><li><a href="/divisions/2" class="text-gray-400 hover:text-white transition-colors">Saudi Arabia</a></li><li><a href="/divisions/3" class="text-gray-400 hover:text-white transition-colors">UAE</a></li></ul></div><div><h3 class="text-lg font-semibold mb-4">Quick Links</h3><ul class="space-y-2"><li><a href="/about" class="text-gray-400 hover:text-white transition-colors">About Us</a></li><li><a href="/standings" class="text-gray-400 hover:text-white transition-colors">Standings</a></li><li><a href="/head-to-head" class="text-gray-400 hover:text-white transition-colors">Head-to-Head</a></li><li><a href="/teams" class="text-gray-400 hover:text-white transition-colors">Teams</a></li><li><a href="/fixtures" class="text-gray-400 hover:text-white transition-colors">Fixtures</a></li></ul></div><div><h3 class="text-lg font-semibold mb-4">Contact Us</h3><ul class="space-y-3"><li class="flex items-start"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin h-5 w-5 text-primary mr-2 mt-0.5"><path d="M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"></path><circle cx="12" cy="10" r="3"></circle></svg><span class="text-gray-400">123 League Street, Medical City, Bahrain</span></li><li class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-phone h-5 w-5 text-primary mr-2"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg><span class="text-gray-400">+973 1234 5678</span></li><li class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-5 w-5 text-primary mr-2"><rect width="20" height="16" x="2" y="4" rx="2"></rect><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg><span class="text-gray-400"><EMAIL></span></li><li class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe h-5 w-5 text-primary mr-2"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg><span class="text-gray-400">www.doctorsleagues.com</span></li></ul></div></div><div class="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center"><p class="text-gray-400 text-sm">© 2025 Doctor&#x27;s Leagues. All rights reserved.</p><div class="flex space-x-6 mt-4 md:mt-0"><a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Privacy Policy</a><a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Terms of Service</a><a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Cookie Policy</a></div></div></div></footer><div class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-loader-circle h-4 w-4 animate-spin text-muted-foreground"><path d="M21 12a9 9 0 1 1-6.219-8.56"></path></svg><span class="text-sm text-muted-foreground">Checking connection...</span></div><script src="/_next/static/chunks/webpack.js?v=1742306595958" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"3:\"$Sreact.fragment\"\n5:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"\"]\n6:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"\"]\n8:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"ClientPageRoot\"]\n9:I[\"(app-pages-browser)/./app/head-to-head/page.tsx\",[\"app/head-to-head/page\",\"static/chunks/app/head-to-head/page.js\"],\"default\"]\nd:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"MetadataBoundary\"]\n10:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"OutletBoundary\"]\n17:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"AsyncMetadataOutlet\"]\n1a:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"\"]\n1b:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"ClientSegmentRoot\"]\n1c:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"HTTPAccessFallbackBoundary\"]\n1d:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"ViewportBoundary\"]\n26:\"$Sreact.suspense\"\n27:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js\",[\"app-pages-internals\",\"static/c"])</script><script>self.__next_f.push([1,"hunks/app-pages-internals.js\"],\"AsyncMetadata\"]\n:HL[\"/_next/static/css/app/layout.css?v=1742306595958\",\"style\"]\n2:{\"name\":\"Preloads\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{\"preloadCallbacks\":[\"$E(()=\u003e{ctx.componentMod.preloadStyle(fullHref,ctx.renderOpts.crossOrigin,ctx.nonce)})\"]}}\n1:D\"$2\"\n1:null\n7:{\"name\":\"RootLayout\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{\"children\":[\"$\",\"$L5\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$3\",null,{\"children\":[\"$\",\"$L6\",null,{},null,[],1]},null,[],0],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$Y\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"},null,[],1],\"params\":\"$Y\"}}\n4:D\"$7\"\nb:{\"name\":\"\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{}}\na:D\"$b\"\nc:{\"name\":\"MetadataTree\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{}}\na:D\"$c\"\nf:{\"name\":\"__next_metadata_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":\"$c\",\"stack\":[],\"props\":{}}\ne:D\"$f\"\na:[\"$\",\"$Ld\",null,{\"children\":\"$Le\"},\"$c\",[],1]\n12:{\"name\":\"__next_outlet_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{\"ready\":\"$E(async function getViewportReady() {\\n        await viewport();\\n        return undefined;\\n    })\"}}\n11:D\"$12\"\n14:{\"name\":\"__next_outlet_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{\"ready\":\"$E(async function getMetadataReady() {\\n        // Only warm up metadata() call when it's blocking metadata,\\n        // otherwise it will be fully managed by AsyncMetadata component.\\n        if (!serveStreamingMetadata) {\\n            await metadata();\\n        }\\n        return undefined;\\n    })\"}}\n13:D\"$14\"\n16:{\"name\":\"StreamingMetadataOutlet\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{}}\n15:D\"$16\"\n15:[\"$\",\"$L17\",null,{\"promise\":\"$@18\"},\"$16\",[],1]\n"])</script><script>self.__next_f.push([1,"1e:{\"name\":\"NonIndex\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{\"ctx\":{\"componentMod\":{\"GlobalError\":\"$1a\",\"__next_app__\":{\"require\":\"$E(function __webpack_require__(moduleId) {\\n/******/ \\t\\t// Check if module is in cache\\n/******/ \\t\\tvar cachedModule = __webpack_module_cache__[moduleId];\\n/******/ \\t\\tif (cachedModule !== undefined) {\\n/******/ \\t\\t\\treturn cachedModule.exports;\\n/******/ \\t\\t}\\n/******/ \\t\\t// Create a new module (and put it into the cache)\\n/******/ \\t\\tvar module = __webpack_module_cache__[moduleId] = {\\n/******/ \\t\\t\\tid: moduleId,\\n/******/ \\t\\t\\tloaded: false,\\n/******/ \\t\\t\\texports: {}\\n/******/ \\t\\t};\\n/******/ \\t\\n/******/ \\t\\t// Execute the module function\\n/******/ \\t\\tvar threw = true;\\n/******/ \\t\\ttry {\\n/******/ \\t\\t\\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\\n/******/ \\t\\t\\tthrew = false;\\n/******/ \\t\\t} finally {\\n/******/ \\t\\t\\tif(threw) delete __webpack_module_cache__[moduleId];\\n/******/ \\t\\t}\\n/******/ \\t\\n/******/ \\t\\t// Flag the module as loaded\\n/******/ \\t\\tmodule.loaded = true;\\n/******/ \\t\\n/******/ \\t\\t// Return the exports of the module\\n/******/ \\t\\treturn module.exports;\\n/******/ \\t})\",\"loadChunk\":\"$E(() =\u003e Promise.resolve())\"},\"pages\":[\"C:\\\\Essam\\\\Doctor_League\\\\application2\\\\doctors-leagues (15)\\\\app\\\\head-to-head\\\\page.tsx\"],\"routeModule\":{\"userland\":{\"loaderTree\":[\"\",\"$Y\",\"$Y\"]},\"definition\":\"$Y\"},\"tree\":\"$Y\",\"ClientPageRoot\":\"$8\",\"ClientSegmentRoot\":\"$1b\",\"HTTPAccessFallbackBoundary\":\"$1c\",\"LayoutRouter\":\"$5\",\"MetadataBoundary\":\"$d\",\"OutletBoundary\":\"$10\",\"Postpone\":\"$E(function Postpone({ reason, route }) {\\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    const dynamicTracking = prerenderStore \u0026\u0026 prerenderStore.type === 'prerender-ppr' ? prerenderStore.dynamicTracking : null;\\n    postponeWithTracking(route, reason, dynamicTracking);\\n})\",\"RenderFromTemplateContext\":\"$6\",\"ViewportBoundary\":\"$1d\",\"actionAsyncStorage\":\"$Y\",\"collectSegmentData\":\"$E(async function collectSegmentData(shouldAssumePartialData, fullPageDataBuffer, staleTime, clientModules, serverConsumerManifest, fallbackRouteParams) {\\n    // Traverse the router tree and generate a prefetch response for each segment.\\n    // A mutable map to collect the results as we traverse the route tree.\\n    const resultMap = new Map();\\n    // Before we start, warm up the module cache by decoding the page data once.\\n    // Then we can assume that any remaining async tasks that occur the next time\\n    // are due to hanging promises caused by dynamic data access. Note we only\\n    // have to do this once per page, not per individual segment.\\n    //\\n    try {\\n        await (0, _clientedge.createFromReadableStream)((0, _nodewebstreamshelper.streamFromBuffer)(fullPageDataBuffer), {\\n            serverConsumerManifest\\n        });\\n        await (0, _scheduler.waitAtLeastOneReactRenderTask)();\\n    } catch  {}\\n    // Create an abort controller that we'll use to stop the stream.\\n    const abortController = new AbortController();\\n    const onCompletedProcessingRouteTree = async ()=\u003e{\\n        // Since all we're doing is decoding and re-encoding a cached prerender, if\\n        // serializing the stream takes longer than a microtask, it must because of\\n        // hanging promises caused by dynamic data.\\n        await (0, _scheduler.waitAtLeastOneReactRenderTask)();\\n        abortController.abort();\\n    };\\n    // Generate a stream for the route tree prefetch. While we're walking the\\n    // tree, we'll also spawn additional tasks to generate the segment prefetches.\\n    // The promises for these tasks are pushed to a mutable array that we will\\n    // await once the route tree is fully rendered.\\n    const segmentTasks = [];\\n    const { prelude: treeStream } = await (0, _staticedge.unstable_prerender)(// RootTreePrefetch is not a valid return type for a React component, but\\n    // we need to use a component so that when we decode the original stream\\n    // inside of it, the side effects are transferred to the new stream.\\n    // @ts-expect-error\\n    /*#__PURE__*/ (0, _jsxruntime.jsx)(PrefetchTreeData, {\\n        shouldAssumePartialData: shouldAssumePartialData,\\n        fullPageDataBuffer: fullPageDataBuffer,\\n        fallbackRouteParams: fallbackRouteParams,\\n        serverConsumerManifest: serverConsumerManifest,\\n        clientModules: clientModules,\\n        staleTime: staleTime,\\n        segmentTasks: segmentTasks,\\n        onCompletedProcessingRouteTree: onCompletedProcessingRouteTree\\n    }), clientModules, {\\n        signal: abortController.signal,\\n        onError: onSegmentPrerenderError\\n    });\\n    // Write the route tree to a special `/_tree` segment.\\n    const treeBuffer = await (0, _nodewebstreamshelper.streamToBuffer)(treeStream);\\n    resultMap.set('/_tree', treeBuffer);\\n    // Now that we've finished rendering the route tree, all the segment tasks\\n    // should have been spawned. Await them in parallel and write the segment\\n    // prefetches to the result map.\\n    for (const [segmentPath, buffer] of (await Promise.all(segmentTasks))){\\n        resultMap.set(segmentPath, buffer);\\n    }\\n    return resultMap;\\n})\",\"createMetadataComponents\":\"$E(function createMetadataComponents({ tree, searchParams, metadataContext, getDynamicParamFromSegment, appUsingSizeAdjustment, errorType, createServerParamsForMetadata, workStore, MetadataBoundary, ViewportBoundary, serveStreamingMetadata }) {\\n    function ViewportTree() {\\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\\n            children: [\\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(ViewportBoundary, {\\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Viewport, {})\\n                }),\\n                appUsingSizeAdjustment ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\\\"meta\\\", {\\n                    name: \\\"next-size-adjust\\\",\\n                    content: \\\"\\\"\\n                }) : null\\n            ]\\n        });\\n    }\\n    function MetadataTree() {\\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(MetadataBoundary, {\\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Metadata, {})\\n        });\\n    }\\n    function viewport() {\\n        return getResolvedViewport(tree, searchParams, getDynamicParamFromSegment, createServerParamsForMetadata, workStore, errorType);\\n    }\\n    async function Viewport() {\\n        try {\\n            return await viewport();\\n        } catch (error) {\\n            if (!errorType \u0026\u0026 (0, _httpaccessfallback.isHTTPAccessFallbackError)(error)) {\\n                try {\\n                    return await getNotFoundViewport(tree, searchParams, getDynamicParamFromSegment, createServerParamsForMetadata, workStore);\\n                } catch  {}\\n            }\\n            // We don't actually want to error in this component. We will\\n            // also error in the MetadataOutlet which causes the error to\\n            // bubble from the right position in the page to be caught by the\\n            // appropriate boundaries\\n            return null;\\n        }\\n    }\\n    Viewport.displayName = _metadataconstants.VIEWPORT_BOUNDARY_NAME;\\n    function metadata() {\\n        return getResolvedMetadata(tree, searchParams, getDynamicParamFromSegment, metadataContext, createServerParamsForMetadata, workStore, errorType);\\n    }\\n    async function resolveFinalMetadata() {\\n        let result;\\n        let error = null;\\n        try {\\n            result = await metadata();\\n            return {\\n                metadata: result,\\n                error: null,\\n                digest: undefined\\n            };\\n        } catch (metadataErr) {\\n            error = metadataErr;\\n            if (!errorType \u0026\u0026 (0, _httpaccessfallback.isHTTPAccessFallbackError)(metadataErr)) {\\n                try {\\n                    result = await getNotFoundMetadata(tree, searchParams, getDynamicParamFromSegment, metadataContext, createServerParamsForMetadata, workStore);\\n                    return {\\n                        metadata: result,\\n                        error,\\n                        digest: error == null ? void 0 : error.digest\\n                    };\\n                } catch (notFoundMetadataErr) {\\n                    error = notFoundMetadataErr;\\n                    // In PPR rendering we still need to throw the postpone error.\\n                    // If metadata is postponed, React needs to be aware of the location of error.\\n                    if (serveStreamingMetadata \u0026\u0026 (0, _ispostpone.isPostpone)(notFoundMetadataErr)) {\\n                        throw notFoundMetadataErr;\\n                    }\\n                }\\n            }\\n            // In PPR rendering we still need to throw the postpone error.\\n            // If metadata is postponed, React needs to be aware of the location of error.\\n            if (serveStreamingMetadata \u0026\u0026 (0, _ispostpone.isPostpone)(metadataErr)) {\\n                throw metadataErr;\\n            }\\n            // We don't actually want to error in this component. We will\\n            // also error in the MetadataOutlet which causes the error to\\n            // bubble from the right position in the page to be caught by the\\n            // appropriate boundaries\\n            return {\\n                metadata: result,\\n                error,\\n                digest: error == null ? void 0 : error.digest\\n            };\\n        }\\n    }\\n    async function Metadata() {\\n        const promise = resolveFinalMetadata();\\n        if (serveStreamingMetadata) {\\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {\\n                fallback: null,\\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_asyncmetadata.AsyncMetadata, {\\n                    promise: promise\\n                })\\n            });\\n        }\\n        const metadataState = await promise;\\n        return metadataState.metadata;\\n    }\\n    Metadata.displayName = _metadataconstants.METADATA_BOUNDARY_NAME;\\n    async function getMetadataReady() {\\n        // Only warm up metadata() call when it's blocking metadata,\\n        // otherwise it will be fully managed by AsyncMetadata component.\\n        if (!serveStreamingMetadata) {\\n            await metadata();\\n        }\\n        return undefined;\\n    }\\n    async function getViewportReady() {\\n        await viewport();\\n        return undefined;\\n    }\\n    function StreamingMetadataOutlet() {\\n        if (serveStreamingMetadata) {\\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_asyncmetadata.AsyncMetadataOutlet, {\\n                promise: resolveFinalMetadata()\\n            });\\n        }\\n        return null;\\n    }\\n    return {\\n        ViewportTree,\\n        MetadataTree,\\n        getViewportReady,\\n        getMetadataReady,\\n        StreamingMetadataOutlet\\n    };\\n})\",\"createPrerenderParamsForClientSegment\":\"$E(function createPrerenderParamsForClientSegment(underlyingParams, workStore) {\\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (prerenderStore \u0026\u0026 prerenderStore.type === 'prerender') {\\n        const fallbackParams = workStore.fallbackRouteParams;\\n        if (fallbackParams) {\\n            for(let key in underlyingParams){\\n                if (fallbackParams.has(key)) {\\n                    // This params object has one of more fallback params so we need to consider\\n                    // the awaiting of this params object \\\"dynamic\\\". Since we are in dynamicIO mode\\n                    // we encode this as a promise that never resolves\\n                    return (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`params`');\\n                }\\n            }\\n        }\\n    }\\n    // We're prerendering in a mode that does not abort. We resolve the promise without\\n    // any tracking because we're just transporting a value from server to client where the tracking\\n    // will be applied.\\n    return Promise.resolve(underlyingParams);\\n})\",\"createPrerenderSearchParamsForClientPage\":\"$E(function createPrerenderSearchParamsForClientPage(workStore) {\\n    if (workStore.forceStatic) {\\n        // When using forceStatic we override all other logic and always just return an empty\\n        // dictionary object.\\n        return Promise.resolve({});\\n    }\\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (prerenderStore \u0026\u0026 prerenderStore.type === 'prerender') {\\n        // dynamicIO Prerender\\n        // We're prerendering in a mode that aborts (dynamicIO) and should stall\\n        // the promise to ensure the RSC side is considered dynamic\\n        return (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`searchParams`');\\n    }\\n    // We're prerendering in a mode that does not aborts. We resolve the promise without\\n    // any tracking because we're just transporting a value from server to client where the tracking\\n    // will be applied.\\n    return Promise.resolve({});\\n})\",\"createServerParamsForMetadata\":\"$E(function createServerParamsForServerSegment(underlyingParams, workStore) {\\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (workUnitStore) {\\n        switch(workUnitStore.type){\\n            case 'prerender':\\n            case 'prerender-ppr':\\n            case 'prerender-legacy':\\n                return createPrerenderParams(underlyingParams, workStore, workUnitStore);\\n            default:\\n        }\\n    }\\n    return createRenderParams(underlyingParams, workStore);\\n})\",\"createServerParamsForServerSegment\":\"$E(function createServerParamsForServerSegment(underlyingParams, workStore) {\\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (workUnitStore) {\\n        switch(workUnitStore.type){\\n            case 'prerender':\\n            case 'prerender-ppr':\\n            case 'prerender-legacy':\\n                return createPrerenderParams(underlyingParams, workStore, workUnitStore);\\n            default:\\n        }\\n    }\\n    return createRenderParams(underlyingParams, workStore);\\n})\",\"createServerSearchParamsForMetadata\":\"$E(function createServerSearchParamsForServerPage(underlyingSearchParams, workStore) {\\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (workUnitStore) {\\n        switch(workUnitStore.type){\\n            case 'prerender':\\n            case 'prerender-ppr':\\n            case 'prerender-legacy':\\n                return createPrerenderSearchParams(workStore, workUnitStore);\\n            default:\\n        }\\n    }\\n    return createRenderSearchParams(underlyingSearchParams, workStore);\\n})\",\"createServerSearchParamsForServerPage\":\"$E(function createServerSearchParamsForServerPage(underlyingSearchParams, workStore) {\\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (workUnitStore) {\\n        switch(workUnitStore.type){\\n            case 'prerender':\\n            case 'prerender-ppr':\\n            case 'prerender-legacy':\\n                return createPrerenderSearchParams(workStore, workUnitStore);\\n            default:\\n        }\\n    }\\n    return createRenderSearchParams(underlyingSearchParams, workStore);\\n})\",\"createTemporaryReferenceSet\":\"$E(function(){return new WeakMap})\",\"decodeAction\":\"$E(function(body,serverManifest){var formData=new FormData,action=null;return body.forEach(function(value1,key){key.startsWith(\\\"$ACTION_\\\")?key.startsWith(\\\"$ACTION_REF_\\\")?(value1=decodeBoundActionMetaData(body,serverManifest,value1=\\\"$ACTION_\\\"+key.slice(12)+\\\":\\\"),action=loadServerReference(serverManifest,value1.id,value1.bound)):key.startsWith(\\\"$ACTION_ID_\\\")\u0026\u0026(action=loadServerReference(serverManifest,value1=key.slice(11),null)):formData.append(key,value1)}),null===action?null:action.then(function(fn){return fn.bind(null,formData)})})\",\"decodeFormState\":\"$E(function(actionResult,body,serverManifest){var keyPath=body.get(\\\"$ACTION_KEY\\\");if(\\\"string\\\"!=typeof keyPath)return Promise.resolve(null);var metaData=null;if(body.forEach(function(value1,key){key.startsWith(\\\"$ACTION_REF_\\\")\u0026\u0026(metaData=decodeBoundActionMetaData(body,serverManifest,\\\"$ACTION_\\\"+key.slice(12)+\\\":\\\"))}),null===metaData)return Promise.resolve(null);var referenceId=metaData.id;return Promise.resolve(metaData.bound).then(function(bound){return null===bound?null:[actionResult,keyPath,referenceId,bound.length-1]})})\",\"decodeReply\":\"$E(function(body,webpackMap,options){if(\\\"string\\\"==typeof body){var form=new FormData;form.append(\\\"0\\\",body),body=form}return webpackMap=getChunk(body=createResponse(webpackMap,\\\"\\\",options?options.temporaryReferences:void 0,body),0),close(body),webpackMap})\",\"patchFetch\":\"$E(function patchFetch() {\\n    return (0, _patchfetch.patchFetch)({\\n        workAsyncStorage: _workasyncstorageexternal.workAsyncStorage,\\n        workUnitAsyncStorage: _workunitasyncstorageexternal.workUnitAsyncStorage\\n    });\\n})\",\"preconnect\":\"$E(function preconnect(href, crossOrigin, nonce) {\\n    const opts = {};\\n    if (typeof crossOrigin === 'string') {\\n        opts.crossOrigin = crossOrigin;\\n    }\\n    if (typeof nonce === 'string') {\\n        opts.nonce = nonce;\\n    }\\n    ;\\n    _reactdom.default.preconnect(href, opts);\\n})\",\"preloadFont\":\"$E(function preloadFont(href, type, crossOrigin, nonce) {\\n    const opts = {\\n        as: 'font',\\n        type\\n    };\\n    if (typeof crossOrigin === 'string') {\\n        opts.crossOrigin = crossOrigin;\\n    }\\n    if (typeof nonce === 'string') {\\n        opts.nonce = nonce;\\n    }\\n    _reactdom.default.preload(href, opts);\\n})\",\"preloadStyle\":\"$E(function preloadStyle(href, crossOrigin, nonce) {\\n    const opts = {\\n        as: 'style'\\n    };\\n    if (typeof crossOrigin === 'string') {\\n        opts.crossOrigin = crossOrigin;\\n    }\\n    if (typeof nonce === 'string') {\\n        opts.nonce = nonce;\\n    }\\n    _reactdom.default.preload(href, opts);\\n})\",\"prerender\":\"$E(function(model,webpackMap,options){return new Promise(function(resolve,reject){var request=new RequestInstance(PRERENDER,model,webpackMap,options?options.onError:void 0,options?options.identifierPrefix:void 0,options?options.onPostpone:void 0,options?options.temporaryReferences:void 0,options?options.environmentName:void 0,options?options.filterStackFrame:void 0,function(){resolve({prelude:new ReadableStream({type:\\\"bytes\\\",start:function(){startWork(request)},pull:function(controller){startFlowing(request,controller)},cancel:function(reason){request.destination=null,abort(request,reason)}},{highWaterMark:0})})},reject);if(options\u0026\u0026options.signal){var signal=options.signal;if(signal.aborted)abort(request,signal.reason);else{var listener=function(){abort(request,signal.reason),signal.removeEventListener(\\\"abort\\\",listener)};signal.addEventListener(\\\"abort\\\",listener)}}startWork(request)})})\",\"renderToReadableStream\":\"$E(function(model,webpackMap,options){var request=new RequestInstance(20,model,webpackMap,options?options.onError:void 0,options?options.identifierPrefix:void 0,options?options.onPostpone:void 0,options?options.temporaryReferences:void 0,options?options.environmentName:void 0,options?options.filterStackFrame:void 0,noop,noop);if(options\u0026\u0026options.signal){var signal=options.signal;if(signal.aborted)abort(request,signal.reason);else{var listener=function(){abort(request,signal.reason),signal.removeEventListener(\\\"abort\\\",listener)};signal.addEventListener(\\\"abort\\\",listener)}}return new ReadableStream({type:\\\"bytes\\\",start:function(){startWork(request)},pull:function(controller){startFlowing(request,controller)},cancel:function(reason){request.destination=null,abort(request,reason)}},{highWaterMark:0})})\",\"serverHooks\":\"$Y\",\"taintObjectReference\":\"$E(function notImplemented() {\\n    throw Object.defineProperty(new Error('Taint can only be used with the taint flag.'), \\\"__NEXT_ERROR_CODE\\\", {\\n        value: \\\"E354\\\",\\n        enumerable: false,\\n        configurable: true\\n    });\\n})\",\"workAsyncStorage\":\"$Y\",\"workUnitAsyncStorage\":\"$Y\"},\"url\":\"$Y\",\"renderOpts\":\"$Y\",\"workStore\":\"$Y\",\"parsedRequestHeaders\":\"$Y\",\"getDynamicParamFromSegment\":\"$E(function(segment){let segmentParam=getSegmentParam(segment);if(!segmentParam)return null;let key=segmentParam.param,value1=params[key];if(fallbackRouteParams\u0026\u0026fallbackRouteParams.has(segmentParam.param)?value1=fallbackRouteParams.get(segmentParam.param):Array.isArray(value1)?value1=value1.map(i=\u003eencodeURIComponent(i)):\\\"string\\\"==typeof value1\u0026\u0026(value1=encodeURIComponent(value1)),!value1){let isCatchall=\\\"catchall\\\"===segmentParam.type,isOptionalCatchall=\\\"optional-catchall\\\"===segmentParam.type;if(isCatchall||isOptionalCatchall){let dynamicParamType=dynamicParamTypes[segmentParam.type];return isOptionalCatchall?{param:key,value:null,type:dynamicParamType,treeSegment:[key,\\\"\\\",dynamicParamType]}:{param:key,value:value1=pagePath.split(\\\"/\\\").slice(1).flatMap(pathSegment=\u003e{let param=function(param){let match=param.match(PARAMETER_PATTERN);return match?parseMatchedParameter(match[2]):parseMatchedParameter(param)}(pathSegment);return params[param.key]??param.key}),type:dynamicParamType,treeSegment:[key,value1.join(\\\"/\\\"),dynamicParamType]}}}let type=function(type){let short=dynamicParamTypes[type];if(!short)throw Object.defineProperty(Error(\\\"Unknown dynamic param type\\\"),\\\"__NEXT_ERROR_CODE\\\",{value:\\\"E378\\\",enumerable:!1,configurable:!0});return short}(segmentParam.type);return{param:key,value:value1,treeSegment:[key,Array.isArray(value1)?value1.join(\\\"/\\\"):value1,type],type:type}})\",\"query\":\"$0:f:0:1:2:children:2:children:1:props:children:0:props:searchParams\",\"isPrefetch\":false,\"isAction\":false,\"requestTimestamp\":1742306595958,\"appUsingSizeAdjustment\":false,\"flightRouterState\":\"$undefined\",\"requestId\":\"OK7m3d__N_vreuc0lAzyM\",\"pagePath\":\"/head-to-head\",\"clientReferenceManifest\":\"$Y\",\"assetPrefix\":\"\",\"isNotFoundPath\":false,\"nonce\":\"$undefined\",\"res\":\"$Y\",\"sharedContext\":\"$Y\"}}}\n"])</script><script>self.__next_f.push([1,"19:D\"$1e\"\n19:null\n20:{\"name\":\"ViewportTree\",\"env\":\"Server\",\"key\":\"OK7m3d__N_vreuc0lAzyM\",\"owner\":null,\"stack\":[],\"props\":{}}\n1f:D\"$20\"\n22:{\"name\":\"__next_viewport_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":\"$20\",\"stack\":[],\"props\":{}}\n21:D\"$22\"\n1f:[\"$\",\"$3\",\"OK7m3d__N_vreuc0lAzyM\",{\"children\":[[\"$\",\"$L1d\",null,{\"children\":\"$L21\"},\"$20\",[],1],null]},null,null,0]\n24:{\"name\":\"\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{}}\n23:D\"$24\"\n23:null\n25:[]\n0:{\"P\":\"$1\",\"b\":\"development\",\"p\":\"\",\"c\":[\"\",\"head-to-head\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"head-to-head\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$3\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/app/layout.css?v=1742306595958\",\"precedence\":\"next_static/css/app/layout.css\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"},null,[],0]],\"$L4\"]},null,[],0],{\"children\":[\"head-to-head\",[\"$\",\"$3\",\"c\",{\"children\":[null,[\"$\",\"$L5\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L6\",null,{},null,[],1],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"},null,[],1]]},null,[],0],{\"children\":[\"__PAGE__\",[\"$\",\"$3\",\"c\",{\"children\":[[\"$\",\"$L8\",null,{\"Component\":\"$9\",\"searchParams\":{},\"params\":{}},null,[],1],\"$a\",null,[\"$\",\"$L10\",null,{\"children\":[\"$L11\",\"$L13\",\"$15\"]},null,[],1]]},null,[],0],{},null,false]},null,false]},null,false],[\"$\",\"$3\",\"h\",{\"children\":[\"$19\",\"$1f\",\"$23\"]},null,[],0],false]],\"m\":\"$W25\",\"G\":[\"$1a\",\"$undefined\"],\"s\":false,\"S\":false}\ne:[\"$\",\"$26\",null,{\"fallback\":null,\"children\":[\"$\",\"$L27\",null,{\"promise\":\"$@28\"},\"$f\",[],1]},\"$f\",[],1]\n13:null\n28:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Doctor's Leagues\"},\"$f\",[],0],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Where Healthcare Heroes Compete for Your Trust\"},\"$f\",[],0],[\"$\",\"meta\",\"2\",{\"name\":\"generator\",\"content\":\"v0.dev\"},\"$f\",[],0]],\"error\":null,\"digest\":\"$un"])</script><script>self.__next_f.push([1,"defined\"}\n18:{\"metadata\":\"$28:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n21:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"},\"$12\",[],0],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"},\"$12\",[],0]]\n11:null\n"])</script><script>self.__next_f.push([1,"29:I[\"(app-pages-browser)/./components/ui/mock-toast.tsx\",[\"app/layout\",\"static/chunks/app/layout.js\"],\"ToastProvider\"]\n4a:I[\"(app-pages-browser)/./components/connection-status.tsx\",[\"app/layout\",\"static/chunks/app/layout.js\"],\"ConnectionStatus\"]\n4b:I[\"(app-pages-browser)/./components/ui/mock-toast.tsx\",[\"app/layout\",\"static/chunks/app/layout.js\"],\"Toaster\"]\n2b:{\"name\":\"Header\",\"env\":\"Server\",\"key\":null,\"owner\":\"$7\",\"stack\":[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",48,96]],\"props\":{}}\n2a:D\"$2b\"\n:W[\"log\",[[\"Header\",\"webpack-internal:///(rsc)/./components/header.tsx\",13,13]],\"$2b\",\"Server\",\"Rendering Header component, directly accessing database with service role key...\"]\n:W[\"log\",[[\"Header\",\"webpack-internal:///(rsc)/./components/header.tsx\",18,17]],\"$2b\",\"Server\",\"Attempting to fetch countries from database...\"]\n2d:{\"name\":\"NotFound\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{}}\n2c:D\"$2d\"\n2e:{\"name\":\"HTTPAccessErrorFallback\",\"env\":\"Server\",\"key\":null,\"owner\":\"$2d\",\"stack\":[],\"props\":{\"status\":404,\"message\":\"This page could not be found.\"}}\n2c:D\"$2e\"\n2c:[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"},\"$2e\",[],1],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}},\"$2e\",[],1],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404},\"$2e\",[],1],[\"$\",\"div\",null,{\"style\":{\"display\":\"i"])</script><script>self.__next_f.push([1,"nline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"},\"$2e\",[],1]},\"$2e\",[],1]]},\"$2e\",[],1]},\"$2e\",[],1]]\n30:{\"name\":\"Twitter\",\"env\":\"Server\",\"key\":null,\"owner\":\"$7\",\"stack\":[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",111,146]],\"props\":{\"className\":\"h-5 w-5\"}}\n2f:D\"$30\"\n31:{\"name\":\"\",\"env\":\"Server\",\"key\":null,\"owner\":\"$30\",\"stack\":[],\"props\":{\"ref\":\"$undefined\",\"iconNode\":[[\"path\",{\"d\":\"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z\",\"key\":\"pff0z6\"}]],\"className\":\"lucide-twitter h-5 w-5\"}}\n2f:D\"$31\"\n2f:[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-twitter h-5 w-5\",\"children\":[[\"$\",\"path\",\"pff0z6\",{\"d\":\"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z\"},\"$31\",[],0],\"$undefined\"]},\"$31\",[],1]\n33:{\"name\":\"Facebook\",\"env\":\"Server\",\"key\":null,\"owner\":\"$7\",\"stack\":[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",126,146]],\"props\":{\"className\":\"h-5 w-5\"}}\n32:D\"$33\"\n34:{\"name\":\"\",\"env\":\"Server\",\"key\":null,\"owner\":\"$33\",\"stack\":[],\"props\":{\"ref\":\"$undefined\",\"iconNode\":[[\"path\",{\"d\":\"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z\",\"key\":\"1jg4f8\"}]],\"className\":\"lucide-facebook h-5 w-5\"}}\n32:D\"$34\"\n32:[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-facebook h-5 w-5\",\"children\":[[\"$\",\"path\",\"1jg4f8\",{\"d\":\"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z\"},\"$34\",[],0],\"$undefined\"]},\"$34\",[],1]"])</script><script>self.__next_f.push([1,"\n36:{\"name\":\"Instagram\",\"env\":\"Server\",\"key\":null,\"owner\":\"$7\",\"stack\":[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",141,146]],\"props\":{\"className\":\"h-5 w-5\"}}\n35:D\"$36\"\n37:{\"name\":\"\",\"env\":\"Server\",\"key\":null,\"owner\":\"$36\",\"stack\":[],\"props\":{\"ref\":\"$undefined\",\"iconNode\":[[\"rect\",{\"width\":\"20\",\"height\":\"20\",\"x\":\"2\",\"y\":\"2\",\"rx\":\"5\",\"ry\":\"5\",\"key\":\"2e1cvw\"}],[\"path\",{\"d\":\"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z\",\"key\":\"9exkf1\"}],[\"line\",{\"x1\":\"17.5\",\"x2\":\"17.51\",\"y1\":\"6.5\",\"y2\":\"6.5\",\"key\":\"r4j83e\"}]],\"className\":\"lucide-instagram h-5 w-5\"}}\n35:D\"$37\"\n35:[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-instagram h-5 w-5\",\"children\":[[\"$\",\"rect\",\"2e1cvw\",{\"width\":\"20\",\"height\":\"20\",\"x\":\"2\",\"y\":\"2\",\"rx\":\"5\",\"ry\":\"5\"},\"$37\",[],0],[\"$\",\"path\",\"9exkf1\",{\"d\":\"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z\"},\"$37\",[],0],[\"$\",\"line\",\"r4j83e\",{\"x1\":\"17.5\",\"x2\":\"17.51\",\"y1\":\"6.5\",\"y2\":\"6.5\"},\"$37\",[],0],\"$undefined\"]},\"$37\",[],1]\n39:{\"name\":\"Linkedin\",\"env\":\"Server\",\"key\":null,\"owner\":\"$7\",\"stack\":[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",156,146]],\"props\":{\"className\":\"h-5 w-5\"}}\n38:D\"$39\"\n3a:{\"name\":\"\",\"env\":\"Server\",\"key\":null,\"owner\":\"$39\",\"stack\":[],\"props\":{\"ref\":\"$undefined\",\"iconNode\":[[\"path\",{\"d\":\"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z\",\"key\":\"c2jq9f\"}],[\"rect\",{\"width\":\"4\",\"height\":\"12\",\"x\":\"2\",\"y\":\"9\",\"key\":\"mk3on5\"}],[\"circle\",{\"cx\":\"4\",\"cy\":\"4\",\"r\":\"2\",\"key\":\"bt5ra8\"}]],\"className\":\"lucide-linkedin h-5 w-5\"}}\n38:D\"$3a\"\n38:[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-linkedin h-5 w-5\",\"children\":[[\"$\",\"path\",\"c2jq9f\",{\"d\":\"M16 8a6 6 0 "])</script><script>self.__next_f.push([1,"0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z\"},\"$3a\",[],0],[\"$\",\"rect\",\"mk3on5\",{\"width\":\"4\",\"height\":\"12\",\"x\":\"2\",\"y\":\"9\"},\"$3a\",[],0],[\"$\",\"circle\",\"bt5ra8\",{\"cx\":\"4\",\"cy\":\"4\",\"r\":\"2\"},\"$3a\",[],0],\"$undefined\"]},\"$3a\",[],1]\n3c:{\"name\":\"Trophy\",\"env\":\"Server\",\"key\":null,\"owner\":\"$7\",\"stack\":[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",185,132]],\"props\":{\"className\":\"h-5 w-5 text-primary mr-2\"}}\n3b:D\"$3c\"\n3d:{\"name\":\"\",\"env\":\"Server\",\"key\":null,\"owner\":\"$3c\",\"stack\":[],\"props\":{\"ref\":\"$undefined\",\"iconNode\":[[\"path\",{\"d\":\"M6 9H4.5a2.5 2.5 0 0 1 0-5H6\",\"key\":\"17hqa7\"}],[\"path\",{\"d\":\"M18 9h1.5a2.5 2.5 0 0 0 0-5H18\",\"key\":\"lmptdp\"}],[\"path\",{\"d\":\"M4 22h16\",\"key\":\"57wxv0\"}],\"$Y\",\"$Y\",\"$Y\"],\"className\":\"lucide-trophy h-5 w-5 text-primary mr-2\"}}\n3b:D\"$3d\"\n3b:[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-trophy h-5 w-5 text-primary mr-2\",\"children\":[[\"$\",\"path\",\"17hqa7\",{\"d\":\"M6 9H4.5a2.5 2.5 0 0 1 0-5H6\"},\"$3d\",[],0],[\"$\",\"path\",\"lmptdp\",{\"d\":\"M18 9h1.5a2.5 2.5 0 0 0 0-5H18\"},\"$3d\",[],0],[\"$\",\"path\",\"57wxv0\",{\"d\":\"M4 22h16\"},\"$3d\",[],0],[\"$\",\"path\",\"1nw9bq\",{\"d\":\"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22\"},\"$3d\",[],0],[\"$\",\"path\",\"1np0yb\",{\"d\":\"M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22\"},\"$3d\",[],0],[\"$\",\"path\",\"u46fv3\",{\"d\":\"M18 2H6v7a6 6 0 0 0 12 0V2Z\"},\"$3d\",[],0],\"$undefined\"]},\"$3d\",[],1]\n3f:{\"name\":\"MapPin\",\"env\":\"Server\",\"key\":null,\"owner\":\"$7\",\"stack\":[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",343,140]],\"props\":{\"className\":\"h-5 w-5 text-primary mr-2 mt-0.5\"}}\n3e:D\"$3f\"\n40:{\"name\":\"\",\"env\":\"Server\",\"key\":null,\"owner\":\"$3f\",\"stack\":[],\"props\":{\"ref\":\"$undefined\",\"iconNode\":[[\"path\",{\"d\":\"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0\",\"key\":\"1r0f0z\"}],[\"circle\",{\"cx\":\"12\","])</script><script>self.__next_f.push([1,"\"cy\":\"10\",\"r\":\"3\",\"key\":\"ilqhr7\"}]],\"className\":\"lucide-map-pin h-5 w-5 text-primary mr-2 mt-0.5\"}}\n3e:D\"$40\"\n3e:[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-map-pin h-5 w-5 text-primary mr-2 mt-0.5\",\"children\":[[\"$\",\"path\",\"1r0f0z\",{\"d\":\"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0\"},\"$40\",[],0],[\"$\",\"circle\",\"ilqhr7\",{\"cx\":\"12\",\"cy\":\"10\",\"r\":\"3\"},\"$40\",[],0],\"$undefined\"]},\"$40\",[],1]\n42:{\"name\":\"Phone\",\"env\":\"Server\",\"key\":null,\"owner\":\"$7\",\"stack\":[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",367,140]],\"props\":{\"className\":\"h-5 w-5 text-primary mr-2\"}}\n41:D\"$42\"\n43:{\"name\":\"\",\"env\":\"Server\",\"key\":null,\"owner\":\"$42\",\"stack\":[],\"props\":{\"ref\":\"$undefined\",\"iconNode\":[[\"path\",{\"d\":\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\",\"key\":\"foiqr5\"}]],\"className\":\"lucide-phone h-5 w-5 text-primary mr-2\"}}\n41:D\"$43\"\n41:[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-phone h-5 w-5 text-primary mr-2\",\"children\":[[\"$\",\"path\",\"foiqr5\",{\"d\":\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\"},\"$43\",[],0],\"$undefined\"]},\"$43\",[],1]\n45:{\"name\":\"Mail\",\"env\":\"Server\",\"key\":null,\"owner\":\"$7\",\"stack"])</script><script>self.__next_f.push([1,"\":[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",391,140]],\"props\":{\"className\":\"h-5 w-5 text-primary mr-2\"}}\n44:D\"$45\"\n46:{\"name\":\"\",\"env\":\"Server\",\"key\":null,\"owner\":\"$45\",\"stack\":[],\"props\":{\"ref\":\"$undefined\",\"iconNode\":[[\"rect\",{\"width\":\"20\",\"height\":\"16\",\"x\":\"2\",\"y\":\"4\",\"rx\":\"2\",\"key\":\"18n3k1\"}],[\"path\",{\"d\":\"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7\",\"key\":\"1ocrg3\"}]],\"className\":\"lucide-mail h-5 w-5 text-primary mr-2\"}}\n44:D\"$46\"\n44:[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-mail h-5 w-5 text-primary mr-2\",\"children\":[[\"$\",\"rect\",\"18n3k1\",{\"width\":\"20\",\"height\":\"16\",\"x\":\"2\",\"y\":\"4\",\"rx\":\"2\"},\"$46\",[],0],[\"$\",\"path\",\"1ocrg3\",{\"d\":\"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7\"},\"$46\",[],0],\"$undefined\"]},\"$46\",[],1]\n48:{\"name\":\"Globe\",\"env\":\"Server\",\"key\":null,\"owner\":\"$7\",\"stack\":[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",415,140]],\"props\":{\"className\":\"h-5 w-5 text-primary mr-2\"}}\n47:D\"$48\"\n49:{\"name\":\"\",\"env\":\"Server\",\"key\":null,\"owner\":\"$48\",\"stack\":[],\"props\":{\"ref\":\"$undefined\",\"iconNode\":[[\"circle\",{\"cx\":\"12\",\"cy\":\"12\",\"r\":\"10\",\"key\":\"1mglay\"}],[\"path\",{\"d\":\"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20\",\"key\":\"13o1zl\"}],[\"path\",{\"d\":\"M2 12h20\",\"key\":\"9i4pu4\"}]],\"className\":\"lucide-globe h-5 w-5 text-primary mr-2\"}}\n47:D\"$49\"\n47:[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-globe h-5 w-5 text-primary mr-2\",\"children\":[[\"$\",\"circle\",\"1mglay\",{\"cx\":\"12\",\"cy\":\"12\",\"r\":\"10\"},\"$49\",[],0],[\"$\",\"path\",\"13o1zl\",{\"d\":\"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20\"},\"$49\",[],0],[\"$\",\"path\",\"9i4pu4\",{\"d\":\"M2 12h20\"},\"$49\",[],0],\"$undefined\"]},\"$49\",[],1]\n"])</script><script>self.__next_f.push([1,"4:[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"scroll-smooth\",\"children\":[\"$\",\"body\",null,{\"className\":\"font-sans\",\"children\":[\"$\",\"$L29\",null,{\"children\":[\"$L2a\",[\"$\",\"main\",null,{\"className\":\"min-h-[calc(100vh-4rem)] pt-16\",\"children\":[\"$\",\"$L5\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L6\",null,{},null,[],1],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$2c\",[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"},null,[],1]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",53,96]],1],[\"$\",\"footer\",null,{\"className\":\"bg-black text-white py-12\",\"children\":[\"$\",\"div\",null,{\"className\":\"container mx-auto px-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\"children\":[[\"$\",\"div\",null,{\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-center space-x-2 mb-4\",\"children\":[[\"$\",\"img\",null,{\"src\":\"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo-gO7TTezH8tR3LvsxNVrbtcdUVAqKGB.png\",\"alt\":\"Doctor's Leagues\",\"className\":\"h-10 w-10\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",74,132]],1],[\"$\",\"span\",null,{\"className\":\"text-white font-bold text-xl\",\"children\":\"Doctor's Leagues\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",83,132]],1]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",71,124]],1],[\"$\",\"p\",null,{\"className\":\"text-gray-400 mb-4\",\"children\":\"Where Healthcare Heroes Compete for Your Trust\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",97,124]],1],[\"$\",\"div\",null,{\"className\":\"flex space-x-4\",\"children\":[[\"$\",\"a\",null,{\"href\":\"#\",\"className\":\"text-gray-400 hover:text-primary transition-colors\",\"children\":\"$2f\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",108,132]],1],[\"$\",\"a\",null,{\"href\":\"#\",\"className\":\"text-gray-400 hover:text-primary transition-colors\",\"children\":\"$32\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",123,132]],1],[\"$\",\"a\",null,{\"href\":\"#\",\"className\":\"text-gray-400 hover:text-primary transition-colors\",\"children\":\"$35\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",138,132]],1],[\"$\",\"a\",null,{\"href\":\"#\",\"className\":\"text-gray-400 hover:text-primary transition-colors\",\"children\":\"$38\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",153,132]],1]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",105,124]],1]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",69,116]],1],[\"$\",\"div\",null,{\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold mb-4 flex items-center\",\"children\":[\"$3b\",\"Leagues\"]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",182,124]],1],[\"$\",\"ul\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"li\",\"1\",{\"children\":[\"$\",\"a\",null,{\"href\":\"/divisions/1\",\"className\":\"text-gray-400 hover:text-white transition-colors\",\"children\":\"Bahrain\"},\"$7\",[[\"eval\",\"webpack-internal:///(rsc)/./app/layout.tsx\",202,146],[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,85]],1]},\"$7\",[[\"eval\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,175],[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,85]],0],[\"$\",\"li\",\"5\",{\"children\":[\"$\",\"a\",null,{\"href\":\"/divisions/5\",\"className\":\"text-gray-400 hover:text-white transition-colors\",\"children\":\"Kuwait\"},\"$7\",[[\"eval\",\"webpack-internal:///(rsc)/./app/layout.tsx\",202,146],[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,85]],1]},\"$7\",[[\"eval\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,175],[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,85]],0],[\"$\",\"li\",\"6\",{\"children\":[\"$\",\"a\",null,{\"href\":\"/divisions/6\",\"className\":\"text-gray-400 hover:text-white transition-colors\",\"children\":\"Oman\"},\"$7\",[[\"eval\",\"webpack-internal:///(rsc)/./app/layout.tsx\",202,146],[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,85]],1]},\"$7\",[[\"eval\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,175],[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,85]],0],[\"$\",\"li\",\"4\",{\"children\":[\"$\",\"a\",null,{\"href\":\"/divisions/4\",\"className\":\"text-gray-400 hover:text-white transition-colors\",\"children\":\"Qatar\"},\"$7\",[[\"eval\",\"webpack-internal:///(rsc)/./app/layout.tsx\",202,146],[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,85]],1]},\"$7\",[[\"eval\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,175],[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,85]],0],[\"$\",\"li\",\"2\",{\"children\":[\"$\",\"a\",null,{\"href\":\"/divisions/2\",\"className\":\"text-gray-400 hover:text-white transition-colors\",\"children\":\"Saudi Arabia\"},\"$7\",[[\"eval\",\"webpack-internal:///(rsc)/./app/layout.tsx\",202,146],[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,85]],1]},\"$7\",[[\"eval\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,175],[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,85]],0],[\"$\",\"li\",\"3\",{\"children\":[\"$\",\"a\",null,{\"href\":\"/divisions/3\",\"className\":\"text-gray-400 hover:text-white transition-colors\",\"children\":\"UAE\"},\"$7\",[[\"eval\",\"webpack-internal:///(rsc)/./app/layout.tsx\",202,146],[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,85]],1]},\"$7\",[[\"eval\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,175],[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",201,85]],0]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",199,124]],1]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",180,116]],1],[\"$\",\"div\",null,{\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold mb-4\",\"children\":\"Quick Links\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",229,124]],1],[\"$\",\"ul\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"$\",\"a\",null,{\"href\":\"/about\",\"className\":\"text-gray-400 hover:text-white transition-colors\",\"children\":\"About Us\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",241,146]],1]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",240,132]],1],[\"$\",\"li\",null,{\"children\":[\"$\",\"a\",null,{\"href\":\"/standings\",\"className\":\"text-gray-400 hover:text-white transition-colors\",\"children\":\"Standings\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",256,146]],1]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",255,132]],1],[\"$\",\"li\",null,{\"children\":[\"$\",\"a\",null,{\"href\":\"/head-to-head\",\"className\":\"text-gray-400 hover:text-white transition-colors\",\"children\":\"Head-to-Head\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",271,146]],1]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",270,132]],1],[\"$\",\"li\",null,{\"children\":[\"$\",\"a\",null,{\"href\":\"/teams\",\"className\":\"text-gray-400 hover:text-white transition-colors\",\"children\":\"Teams\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",286,146]],1]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",285,132]],1],[\"$\",\"li\",null,{\"children\":[\"$\",\"a\",null,{\"href\":\"/fixtures\",\"className\":\"text-gray-400 hover:text-white transition-colors\",\"children\":\"Fixtures\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",301,146]],1]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",300,132]],1]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",237,124]],1]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",227,116]],1],[\"$\",\"div\",null,{\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold mb-4\",\"children\":\"Contact Us\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",329,124]],1],[\"$\",\"ul\",null,{\"className\":\"space-y-3\",\"children\":[[\"$\",\"li\",null,{\"className\":\"flex items-start\",\"children\":[\"$3e\",[\"$\",\"span\",null,{\"className\":\"text-gray-400\",\"children\":\"123 League Street, Medical City, Bahrain\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",350,140]],1]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",340,132]],1],[\"$\",\"li\",null,{\"className\":\"flex items-center\",\"children\":[\"$41\",[\"$\",\"span\",null,{\"className\":\"text-gray-400\",\"children\":\"+973 1234 5678\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",374,140]],1]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",364,132]],1],[\"$\",\"li\",null,{\"className\":\"flex items-center\",\"children\":[\"$44\",[\"$\",\"span\",null,{\"className\":\"text-gray-400\",\"children\":\"<EMAIL>\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",398,140]],1]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",388,132]],1],[\"$\",\"li\",null,{\"className\":\"flex items-center\",\"children\":[\"$47\",[\"$\",\"span\",null,{\"className\":\"text-gray-400\",\"children\":\"www.doctorsleagues.com\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",422,140]],1]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",412,132]],1]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",337,124]],1]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",327,116]],1]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",66,108]],1],[\"$\",\"div\",null,{\"className\":\"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\",\"children\":[[\"$\",\"p\",null,{\"className\":\"text-gray-400 text-sm\",\"children\":\"© 2025 Doctor's Leagues. All rights reserved.\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",457,116]],1],[\"$\",\"div\",null,{\"className\":\"flex space-x-6 mt-4 md:mt-0\",\"children\":[[\"$\",\"a\",null,{\"href\":\"#\",\"className\":\"text-gray-400 hover:text-white text-sm transition-colors\",\"children\":\"Privacy Policy\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",468,124]],1],[\"$\",\"a\",null,{\"href\":\"#\",\"className\":\"text-gray-400 hover:text-white text-sm transition-colors\",\"children\":\"Terms of Service\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",477,124]],1],[\"$\",\"a\",null,{\"href\":\"#\",\"className\":\"text-gray-400 hover:text-white text-sm transition-colors\",\"children\":\"Cookie Policy\"},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",486,124]],1]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",465,116]],1]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",454,108]],1]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",63,110]],1]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",61,96]],1],[\"$\",\"$L4a\",null,{},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",518,96]],1],[\"$\",\"$L4b\",null,{},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",523,96]],1]]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",46,98]],1]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",44,94]],1]},\"$7\",[[\"RootLayout\",\"webpack-internal:///(rsc)/./app/layout.tsx\",41,87]],1]\n"])</script><script>self.__next_f.push([1,"4c:I[\"(app-pages-browser)/./components/header-client.tsx\",[\"app/layout\",\"static/chunks/app/layout.js\"],\"HeaderClient\"]\n:W[\"log\",[[\"Header\",\"webpack-internal:///(rsc)/./components/header.tsx\",30,17]],\"$2b\",\"Server\",\"Successfully fetched countries from database:\",7]\n:W[\"log\",[[\"Header\",\"webpack-internal:///(rsc)/./components/header.tsx\",31,17]],\"$2b\",\"Server\",\"Countries data:\",\"[{\\\"country_id\\\":1,\\\"country_name\\\":\\\"Bahrain\\\"},{\\\"country_id\\\":5,\\\"country_name\\\":\\\"Kuwait\\\"},{\\\"country_id\\\":6,\\\"country_name\\\":\\\"Oman\\\"},{\\\"country_id\\\":4,\\\"country_name\\\":\\\"Qatar\\\"},{\\\"country_id\\\":2,\\\"country_name\\\":\\\"Saudi Arabia\\\"},{\\\"country_id\\\":3,\\\"country_name\\\":\\\"UAE\\\"},{\\\"country_id\\\":7,\\\"country_name\\\":\\\"USA\\\"}]\"]\n:W[\"log\",[[\"Header\",\"webpack-internal:///(rsc)/./components/header.tsx\",49,17]],\"$2b\",\"Server\",\"Added flag URLs to countries data\"]\n2a:[\"$\",\"$L4c\",null,{\"countries\":[{\"country_id\":1,\"country_name\":\"Bahrain\",\"flag_url\":\"https://flagcdn.com/w320/bh.png\"},{\"country_id\":5,\"country_name\":\"Kuwait\",\"flag_url\":\"https://flagcdn.com/w320/kw.png\"},{\"country_id\":6,\"country_name\":\"Oman\",\"flag_url\":\"https://flagcdn.com/w320/om.png\"},{\"country_id\":4,\"country_name\":\"Qatar\",\"flag_url\":\"https://flagcdn.com/w320/qa.png\"},{\"country_id\":2,\"country_name\":\"Saudi Arabia\",\"flag_url\":\"https://flagcdn.com/w320/sa.png\"},{\"country_id\":3,\"country_name\":\"UAE\",\"flag_url\":\"https://flagcdn.com/w320/ae.png\"},{\"country_id\":7,\"country_name\":\"USA\",\"flag_url\":\"https://flagcdn.com/w320/us.png\"}]},\"$2b\",[[\"Header\",\"webpack-internal:///(rsc)/./components/header.tsx\",51,91]],1]\n"])</script></body></html>