"use client"

import { useEffect, useState } from "react"

const M<PERSON><PERSON><PERSON>_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = useState<boolean | undefined>(undefined)

  useEffect(() => {
    // This will only run on the client side
    if (typeof window === 'undefined') return;
    
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    
    // Use the modern event listener pattern
    mql.addEventListener("change", onChange)
    
    // Set initial value
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    
    // Cleanup function
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return isMobile ?? false // Return false as default if undefined (during SSR)
}
