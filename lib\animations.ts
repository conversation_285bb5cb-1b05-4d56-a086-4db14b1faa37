export const transitions = {
  // Smooth transitions for interactive elements
  standard: "transition-all duration-200 ease-in-out",
  fast: "transition-all duration-150 ease-in-out",
  slow: "transition-all duration-300 ease-in-out",

  // Specific transitions
  hover: "transition-colors duration-200 ease-in-out",
  scale: "transition-transform duration-200 ease-in-out",
  opacity: "transition-opacity duration-200 ease-in-out",

  // Interactive states
  interactive: "transform hover:scale-[1.02] active:scale-[0.98] transition-transform duration-200",
  buttonPress: "active:scale-[0.97] transition-transform duration-150",

  // Page transitions
  page: "animate-fadeIn",
}

export const animations = {
  fadeIn: "animate-fadeIn",
  slideUp: "animate-slideUp",
  pulse: "animate-pulse",
  bounce: "animate-bounce",
  spin: "animate-spin",

  // Sports-themed animations
  scoreboard: "animate-scoreboard",
  highlight: "animate-highlight",
}

// Keyframes will be defined in globals.css

