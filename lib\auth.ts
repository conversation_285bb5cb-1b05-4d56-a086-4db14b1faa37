import { createBrowserClient } from "./supabase"

export interface AuthUser {
  id: string
  email: string
  user_metadata?: {
    full_name?: string
  }
}

export interface SignUpData {
  email: string
  password: string
  userData: {
    username: string
    first_name?: string
    last_name?: string
    gender?: string
    age?: number
    city?: string
    country?: string
    user_type: "patient" | "doctor"
  }
}

export interface SignInData {
  email: string
  password: string
}

export class AuthService {
  // Sign up a new user
  static async signUp({ email, password, userData }: SignUpData): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = await createBrowserClient()

      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: userData.username,
            full_name: `${userData.first_name || ""} ${userData.last_name || ""}`.trim(),
            user_type: userData.user_type,
          },
        },
      })

      if (authError) throw authError

      // Insert user data into users table
      const { error: insertError } = await supabase.from("users").insert({
        username: userData.username,
        email,
        first_name: userData.first_name,
        last_name: userData.last_name,
        gender: userData.gender,
        age: userData.age,
        city: userData.city,
        country: userData.country,
        user_type: userData.user_type,
      })

      if (insertError) throw insertError

      return { success: true }
    } catch (error: any) {
      console.error("Sign up error:", error)
      return { success: false, error: error.message }
    }
  }

  // Sign in a user
  static async signIn({ email, password }: SignInData): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = await createBrowserClient()
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) throw error
      return { success: true }
    } catch (error: any) {
      console.error("Sign in error:", error)
      return { success: false, error: error.message }
    }
  }

  // Sign out
  static async signOut(): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = await createBrowserClient()
      const { error } = await supabase.auth.signOut()

      if (error) throw error
      return { success: true }
    } catch (error: any) {
      console.error("Sign out error:", error)
      return { success: false, error: error.message }
    }
  }

  // Get current user
  static async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const supabase = await createBrowserClient()
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser()

      if (error || !user) return null
      return user as AuthUser
    } catch (error) {
      console.error("Get current user error:", error)
      return null
    }
  }

  // Check if username exists
  static async checkUsernameExists(username: string): Promise<boolean> {
    try {
      const supabase = await createBrowserClient()
      const { data, error } = await supabase.from("users").select("username").eq("username", username).single()

      if (error && error.code !== "PGRST116") {
        console.error("Error checking username:", error)
      }

      return !!data
    } catch (error) {
      console.error("Error checking username:", error)
      return false
    }
  }

  // Check if email exists
  static async checkEmailExists(email: string): Promise<boolean> {
    try {
      const supabase = await createBrowserClient()
      const { data, error } = await supabase.from("users").select("email").eq("email", email).single()

      if (error && error.code !== "PGRST116") {
        console.error("Error checking email:", error)
      }

      return !!data
    } catch (error) {
      console.error("Error checking email:", error)
      return false
    }
  }
}

