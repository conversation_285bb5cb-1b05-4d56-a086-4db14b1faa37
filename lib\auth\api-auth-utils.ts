import { NextApiRequest, NextApiResponse } from 'next';
import jwt from 'jsonwebtoken';

export interface AuthenticatedUser {
  userId: number;
  email: string;
  userType: 'patient' | 'doctor';
}

/**
 * Middleware for authenticating API requests with JWT
 * @param handler The API route handler
 * @returns A wrapper function that handles authentication
 */
export function withAuth<T>(
  handler: (
    req: NextApiRequest,
    res: NextApiResponse<T>,
    user: AuthenticatedUser
  ) => Promise<void>
) {
  return async (req: NextApiRequest, res: NextApiResponse<T>) => {
    try {
      // Extract token from Authorization header
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Unauthorized: Missing or invalid token.' } as T);
      }
      
      const token = authHeader.split(' ')[1];
      
      if (!token) {
        return res.status(401).json({ error: 'Unauthorized: Missing token.' } as T);
      }
      
      // Verify token
      const jwtSecret = process.env.JWT_SECRET;
      
      if (!jwtSecret) {
        console.error('JWT_SECRET is not defined in environment variables.');
        return res.status(500).json({ error: 'Server configuration error.' } as T);
      }
      
      try {
        // Verify and decode token
        const decoded = jwt.verify(token, jwtSecret) as AuthenticatedUser;
        
        // Call the original handler with the authenticated user
        return await handler(req, res, decoded);
      } catch (error) {
        console.error('Error verifying token:', error);
        return res.status(401).json({ error: 'Unauthorized: Invalid token.' } as T);
      }
    } catch (error) {
      console.error('Auth middleware error:', error);
      return res.status(500).json({ error: 'Internal Server Error.' } as T);
    }
  };
}

/**
 * Extract JWT token from the request
 * @param req The Next.js API request
 * @returns The JWT token or null if not found
 */
export function getTokenFromRequest(req: NextApiRequest): string | null {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    
    const token = authHeader.split(' ')[1];
    return token || null;
  } catch (error) {
    console.error('Error extracting token:', error);
    return null;
  }
}

/**
 * Verify and decode JWT token from the request
 * @param req The Next.js API request
 * @returns The decoded user or null if invalid
 */
export function verifyTokenFromRequest(req: NextApiRequest): AuthenticatedUser | null {
  try {
    const token = getTokenFromRequest(req);
    
    if (!token) {
      return null;
    }
    
    const jwtSecret = process.env.JWT_SECRET;
    
    if (!jwtSecret) {
      console.error('JWT_SECRET is not defined in environment variables.');
      return null;
    }
    
    const decoded = jwt.verify(token, jwtSecret) as AuthenticatedUser;
    return decoded;
  } catch (error) {
    console.error('Error verifying token:', error);
    return null;
  }
} 