import { saveToken, removeToken, isAuthenticated, getCurrentUserId, getCurrentUserType } from './token-utils';
import { supabase } from '../supabase-client';

// Types for registration
export interface BaseProfileData {
  email: string;
}

export interface PatientProfileData extends BaseProfileData {
  username?: string;
  first_name?: string;
  last_name?: string;
  gender?: string;
  city?: string;
  country?: string;
  age?: number;
  phone_number?: string;
  medical_condition?: string;
  state_province_region?: string;
}

export interface DoctorProfileData extends BaseProfileData {
  fullname: string;
  hospital: string;
  medical_specialty: string;
  educational_board: string;
  phone_number: string;
  experience: string;
  subspecialization?: string;
  certifications?: string;
  recognitions?: string;
  languages?: string;
  professional_affiliation?: string;
  procedures_performed?: string;
  treatment_services?: string;
  hospital_affiliation?: string;
}

export type ProfileData = PatientProfileData | DoctorProfileData;

export interface RegistrationData {
  email: string;
  password: string;
  userType: 'patient' | 'doctor';
  profileData: ProfileData;
}

export interface RegistrationResponse {
  success: boolean;
  message?: string;
  error?: string;
  userId?: number;
}

// Types for login
export interface LoginData {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  token?: string;
  user?: {
    userId: number;
    email: string;
    userType: 'patient' | 'doctor';
    profile: any;
    first_name?: string;
    last_name?: string;
    country_id?: number;
    country?: string;
  };
  error?: string;
}

// Custom authentication service
export class CustomAuthService {
  /**
   * Register a new user
   */
  static async register(data: RegistrationData): Promise<RegistrationResponse> {
    try {
      const response = await fetch('/api/auth/custom/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();
      return result;
    } catch (error: any) {
      console.error('Registration error:', error);
      return {
        success: false,
        error: error.message || 'An error occurred during registration',
      };
    }
  }

  /**
   * Log in a user
   */
  static async login(data: LoginData): Promise<LoginResponse> {
    try {
      const response = await fetch('/api/auth/custom/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();
      
      // Handle verification errors
      if (!result.success && response.status === 403 && 
          result.error && result.error.includes('verify your email')) {
        console.log('Email verification required:', data.email);
        return {
          success: false,
          error: result.error,
        };
      }
      
      // Ensure we always have a properly formatted user object
      if (result.success && result.user) {
        // Handle case where the response uses 'id' instead of 'userId'
        if (result.user.id !== undefined && result.user.userId === undefined) {
          console.log('API response has id instead of userId, fixing...');
          result.user.userId = result.user.id;
        }
        
        // Special case for test doctor - hardcode the known ID if email matches
        if (data.email === '<EMAIL>' && 
            (!result.user.userId || result.user.userId === undefined)) {
          console.log('Test doctor account detected, setting known ID');
          result.user.userId = 4097;
        }
        
        // Log the user object for debugging
        console.log('Final user object after fixes:', result.user);
      }
      
      if (result.success && result.token) {
        saveToken(result.token);
      }
      
      return result;
    } catch (error: any) {
      console.error('Login error:', error);
      return {
        success: false,
        error: error.message || 'An error occurred during login',
      };
    }
  }

  /**
   * Log out the current user
   */
  static logout(): void {
    removeToken();
    // Redirect to home page or login page if needed
    window.location.href = '/';
  }

  /**
   * Check if the user is authenticated
   */
  static isAuthenticated(): boolean {
    return isAuthenticated();
  }

  /**
   * Get the current user ID
   */
  static getCurrentUserId(): number | null {
    return getCurrentUserId();
  }

  /**
   * Get the current user type
   */
  static getCurrentUserType(): 'patient' | 'doctor' | null {
    return getCurrentUserType();
  }

  /**
   * Get the current user's profile data
   */
  static async getCurrentUserProfile(): Promise<any> {
    const userId = getCurrentUserId();
    const userType = getCurrentUserType();
    
    if (!userId || !userType) {
      return null;
    }
    
    try {
      const table = userType === 'patient' ? 'users' : 'doctors';
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .eq('id', userId)
        .single();
        
      if (error) {
        console.error('Error fetching user profile:', error);
        return null;
      }
      
      return data;
    } catch (error) {
      console.error('Exception in getCurrentUserProfile:', error);
      return null;
    }
  }
} 