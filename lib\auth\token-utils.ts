import { jwtDecode } from 'jwt-decode';

// Define token payload type
export interface JwtTokenPayload {
  userId: number;
  email: string;
  userType: 'patient' | 'doctor';
  exp: number; // Expiration timestamp
  iat: number; // Issued at timestamp
}

// Local storage key for the token
const TOKEN_STORAGE_KEY = 'doctors_league_auth_token';

/**
 * Save the JWT token to local storage
 */
export function saveToken(token: string): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem(TOKEN_STORAGE_KEY, token);
  }
}

/**
 * Get the JWT token from local storage
 */
export function getToken(): string | null {
  if (typeof window !== 'undefined') {
    return localStorage.getItem(TOKEN_STORAGE_KEY);
  }
  return null;
}

/**
 * Remove the JWT token from local storage
 */
export function removeToken(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(TOKEN_STORAGE_KEY);
  }
}

/**
 * Decode and verify the JWT token
 * Returns null if the token is invalid or expired
 */
export function decodeToken(): JwtTokenPayload | null {
  const token = getToken();
  
  if (!token) {
    return null;
  }
  
  try {
    const decoded = jwtDecode<JwtTokenPayload>(token);
    
    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (decoded.exp < currentTime) {
      removeToken(); // Clean up expired token
      return null;
    }
    
    return decoded;
  } catch (error) {
    console.error('Error decoding token:', error);
    removeToken(); // Clean up invalid token
    return null;
  }
}

/**
 * Check if the user is authenticated
 */
export function isAuthenticated(): boolean {
  return decodeToken() !== null;
}

/**
 * Get the current user ID from the token
 */
export function getCurrentUserId(): number | null {
  const decoded = decodeToken();
  return decoded ? decoded.userId : null;
}

/**
 * Get the current user type from the token
 */
export function getCurrentUserType(): 'patient' | 'doctor' | null {
  const decoded = decodeToken();
  return decoded ? decoded.userType : null;
}

/**
 * Get the current user email from the token
 */
export function getCurrentUserEmail(): string | null {
  const decoded = decodeToken();
  return decoded ? decoded.email : null;
}

/**
 * Check if token is about to expire (within the next 5 minutes)
 */
export function isTokenExpiringSoon(): boolean {
  const decoded = decodeToken();
  if (!decoded) return false;
  
  const currentTime = Math.floor(Date.now() / 1000);
  const fiveMinutesInSeconds = 5 * 60;
  
  return decoded.exp - currentTime < fiveMinutesInSeconds;
} 