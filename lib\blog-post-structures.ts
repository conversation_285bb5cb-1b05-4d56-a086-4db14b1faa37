import { marked } from 'marked';

// Helper to safely parse and stringify JSON
const safeJsonStringify = (obj: any) => {
  try {
    return JSON.stringify(obj, null, 2);
  } catch (error) {
    console.error('Error stringifying JSON:', error);
    return '{"error": "Could not stringify object"}';
  }
};

// Base structure for all article prompts
const getBasePromptStructure = (specialtyName: string, categoryName: string, authorName: string, customInstructions?: string) => {
  return {
    system_prompt: `You are an expert medical writer for "Doctor's League," a platform focused on E-E-A-T (Experience, Expertise, Authoritativeness, Trustworthiness). Your audience includes medical professionals, students, and educated patients. Your tone should be authoritative, insightful, and professional, yet accessible.

You must generate a complete, well-structured, and formatted blog post. The final output must be a single JSON object containing the generated title, slug, excerpt, content (in Markdown), keywords, meta_title, and meta_description.

**Formatting Rules:**
- The 'content' field must be valid Markdown.
- Use headings (##, ###), bold (**text**), italics (*text*), and lists (- item).
- Do not include a "Disclaimer" or "Medical Disclaimer" section. The application will add this automatically.
- Ensure the article flows logically and provides deep, valuable insights.
`,
    user_prompt: {
      specialty: specialtyName,
      category: categoryName,
      author: authorName,
      custom_instructions: customInstructions || 'No additional instructions.',
      required_output_format: {
        title: "string (compelling and SEO-friendly)",
        slug: "string (lowercase, hyphen-separated, based on the title, ending with a unique identifier if possible)",
        excerpt: "string (a concise, engaging summary of the article, 1-2 sentences)",
        content: "string (full article content in Markdown format)",
        meta_keywords: "array of strings (5-7 relevant keywords)",
        meta_title: "string (SEO-optimized title, max 60 characters)",
        meta_description: "string (SEO-optimized description, max 160 characters)"
      },
      article_structure_guidelines: {} // This will be replaced by category-specific guidelines
    }
  };
};

// --- Category-Specific Structures ---

const evidenceBasedMedicineStructure = (specialtyName: string) => ({
  title_template: `Evidence-Based Breakthroughs in ${specialtyName}: A 2024 Review`,
  guidelines: {
    introduction: `Start with a strong hook about the importance of evidence-based medicine (EBM) in ${specialtyName}. Briefly introduce the key studies or clinical trials that will be discussed.`,
    section_1_title: `Decoding the Latest Clinical Trials in ${specialtyName}`,
    section_1_content: `Analyze 2-3 recent, high-impact clinical trials. For each, discuss the methodology, key findings, and statistical significance. Explain the immediate implications for clinical practice in ${specialtyName}.`,
    section_2_title: "Translating Research into Practice: A How-To Guide",
    section_2_content: `Provide actionable steps for ${specialtyName} practitioners to integrate these new findings into their daily workflow. Discuss potential barriers and how to overcome them.`,
    section_3_title: "The Future of EBM in ${specialtyName}",
    section_3_content: `Speculate on upcoming research trends, new methodologies (e.g., use of RWE - Real World Evidence), and how they might shape the future of ${specialtyName}.`,
    conclusion: `Summarize the key takeaways and reiterate the value of staying current with evidence-based practices for improving patient outcomes in ${specialtyName}.`
  }
});

const medicalTechnologyStructure = (specialtyName: string) => ({
  title_template: `The Tech Revolution in ${specialtyName}: AI, Robotics, and Beyond`,
  guidelines: {
    introduction: `Open with an exciting overview of how technology is revolutionizing ${specialtyName}. Mention key areas of innovation like AI, robotics, telehealth, and new diagnostic tools.`,
    section_1_title: `Artificial Intelligence in ${specialtyName} Diagnostics`,
    section_1_content: `Explore how AI-powered algorithms are improving diagnostic accuracy and speed in ${specialtyName}. Provide specific examples of AI tools being used today. Discuss the benefits and limitations.`,
    section_2_title: `Robotics and Minimally Invasive Surgery`,
    section_2_content: `Detail the impact of robotic-assisted surgery in ${specialtyName}. Compare outcomes with traditional methods, focusing on patient recovery, precision, and new capabilities.`,
    section_3_title: "The Rise of Telehealth and Remote Patient Monitoring",
    section_3_content: `Discuss how telehealth platforms and wearable devices are changing patient-provider interactions and chronic disease management in ${specialtyName}.`,
    conclusion: `Summarize the transformative impact of these technologies and offer a glimpse into the future, emphasizing the synergy between human expertise and technological innovation in ${specialtyName}.`
  }
});

const doctorWellnessStructure = (specialtyName: string) => ({
  title_template: `Beyond the Stethoscope: Cultivating Wellness in ${specialtyName}`,
  guidelines: {
    introduction: `Start with an empathetic acknowledgment of the unique pressures and high-stakes environment faced by ${specialtyName} specialists. Introduce the concept of physician wellness as a critical component of quality care.`,
    section_1_title: `Identifying and Combating Burnout in ${specialtyName}`,
    section_1_content: `Discuss the specific drivers of burnout in the ${specialtyName} field. Provide evidence-based strategies for identifying symptoms early and practical, actionable steps for mitigation (e.g., mindfulness, peer support groups, workload management).`,
    section_2_title: "Work-Life Integration: Strategies for Success",
    section_2_content: `Offer realistic advice on achieving better work-life integration. Discuss strategies like effective scheduling, setting boundaries, and leveraging technology to improve efficiency without sacrificing personal time.`,
    section_3_title: "Building a Culture of Support and Resilience",
    section_3_content: `Explore the role of institutions and team leadership in fostering a supportive work environment. Discuss the importance of mentorship, mental health resources, and creating a culture where seeking help is encouraged.`,
    conclusion: `Conclude by framing physician wellness not as a personal luxury but as an ethical imperative for sustaining a healthy and effective medical workforce in ${specialtyName}.`
  }
});

const healthcarePolicyStructure = (specialtyName: string) => ({
  title_template: `Navigating the Maze: Healthcare Policy and its Impact on ${specialtyName}`,
  guidelines: {
    introduction: `Begin by highlighting a recent, significant healthcare policy change (e.g., reimbursement models, scope of practice laws, or public health mandates) and its relevance to ${specialtyName}.`,
    section_1_title: "The Economic Realities: Reimbursement and Financial Pressures",
    section_1_content: `Analyze how current reimbursement models (e.g., value-based care vs. fee-for-service) are affecting clinical decision-making and practice viability in ${specialtyName}.`,
    section_2_title: "Scope of Practice and Interprofessional Collaboration",
    section_2_content: `Discuss the ongoing policy debates around the scope of practice for different healthcare professionals and how this impacts team-based care models in the ${specialtyName} context.`,
    section_3_title: "Advocacy and Influence: A Physician's Role in Shaping Policy",
    section_3_content: `Provide a guide on how ${specialtyName} specialists can become effective advocates for their patients and profession. Discuss engaging with professional societies, policymakers, and the public.`,
    conclusion: `Summarize the key policy challenges and opportunities. End with a call to action for physicians to be informed and engaged participants in the healthcare policy landscape to ensure a better future for ${specialtyName}.`
  }
});


/**
 * Generates a structured prompt for the Gemini AI model to create a blog post.
 *
 * @param categorySlug - The slug of the blog category to determine the article structure.
 * @param specialtyName - The medical specialty to be the focus of the article.
 * @param authorName - The name of the author for context.
 * @param customInstructions - Optional user-provided instructions.
 * @returns A JSON string representing the complete prompt for the AI.
 */
export const generateStructuredPrompt = (
  categorySlug: string,
  specialtyName: string,
  authorName: string,
  customInstructions?: string
): string => {
  let categoryStructure;
  let categoryNameForPrompt: string;

  switch (categorySlug) {
    case 'evidence-based-medicine-and-research':
      categoryStructure = evidenceBasedMedicineStructure(specialtyName);
      categoryNameForPrompt = 'Evidence-based Medicine and Research';
      break;
    case 'medical-technology-and-innovation':
      categoryStructure = medicalTechnologyStructure(specialtyName);
      categoryNameForPrompt = 'Medical Technology and Innovation';
      break;
    case 'doctor-wellness-and-lifestyle':
      categoryStructure = doctorWellnessStructure(specialtyName);
      categoryNameForPrompt = 'Doctor Wellness and Lifestyle';
      break;
    case 'healthcare-policy-and-economics':
      categoryStructure = healthcarePolicyStructure(specialtyName);
      categoryNameForPrompt = 'Healthcare Policy and Economics';
      break;
    default:
      // Fallback to a generic structure if slug doesn't match
      categoryStructure = evidenceBasedMedicineStructure(specialtyName);
      categoryNameForPrompt = 'General Medical Insights';
  }

  const prompt = getBasePromptStructure(specialtyName, categoryNameForPrompt, authorName, customInstructions);
  prompt.user_prompt.required_output_format.title = categoryStructure.title_template;
  prompt.user_prompt.article_structure_guidelines = categoryStructure.guidelines;

  return safeJsonStringify(prompt);
};


/**
 * Converts a Markdown string to an HTML string.
 * Uses the 'marked' library for conversion.
 * @param markdown - The Markdown string to convert.
 * @returns The resulting HTML string.
 */
export const convertMarkdownToHtml = (markdown: string): string => {
  if (!markdown) return '';
  // Configure marked to add some basic classes for styling, if desired
  // marked.setOptions({ ... });
  return marked(markdown);
}; 