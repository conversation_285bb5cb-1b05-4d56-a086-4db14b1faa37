import { supabase } from './supabase-client';

// Using the blog_posts table types from supabase-client
type BlogPostRow = {
  id: string
  title: string
  slug: string
  excerpt?: string
  content: string
  featured_image_url?: string
  featured_image_alt?: string
  category_id: string
  author_id: string
  medical_reviewer_id?: string
  status?: string
  published_at?: string
  meta_title?: string
  meta_description?: string
  reading_time_minutes?: number
  view_count?: number
  is_featured?: boolean
  is_trending?: boolean
  related_doctor_ids?: number[]
  related_team_ids?: number[]
  structured_data?: any
  meta_keywords?: string[]
  raw_data_json?: any
  generation_source?: string
  ai_generation_job_id?: string
  created_at?: string
  updated_at?: string
}

type BlogPostInsert = {
  id?: string
  title: string
  slug: string
  excerpt?: string
  content: string
  featured_image_url?: string
  featured_image_alt?: string
  category_id: string
  author_id: string
  medical_reviewer_id?: string
  status?: string
  published_at?: string
  meta_title?: string
  meta_description?: string
  reading_time_minutes?: number
  view_count?: number
  is_featured?: boolean
  is_trending?: boolean
  related_doctor_ids?: number[]
  related_team_ids?: number[]
  structured_data?: any
  meta_keywords?: string[]
  raw_data_json?: any
  generation_source?: string
  ai_generation_job_id?: string
  created_at?: string
  updated_at?: string
}

type BlogPostUpdate = {
  id?: string
  title?: string
  slug?: string
  excerpt?: string
  content?: string
  featured_image_url?: string
  featured_image_alt?: string
  category_id?: string
  author_id?: string
  medical_reviewer_id?: string
  status?: string
  published_at?: string
  meta_title?: string
  meta_description?: string
  reading_time_minutes?: number
  view_count?: number
  is_featured?: boolean
  is_trending?: boolean
  related_doctor_ids?: number[]
  related_team_ids?: number[]
  structured_data?: any
  meta_keywords?: string[]
  raw_data_json?: any
  generation_source?: string
  ai_generation_job_id?: string
  created_at?: string
  updated_at?: string
}

export type BlogPost = BlogPostRow;
export type { BlogPostInsert, BlogPostUpdate };

/**
 * Service for managing blog posts including AI-generated content
 */
export class BlogPostService {
  /**
   * Create a new blog post
   */
  static async createBlogPost(post: BlogPostInsert): Promise<BlogPost | null> {
    try {
      const { data, error } = await supabase
        .from('blog_posts')
        .insert(post)
        .select()
        .single();

      if (error) {
        console.error('Error creating blog post:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error creating blog post:', error);
      return null;
    }
  }

  /**
   * Get all blog posts with optional filters
   */
  static async getBlogPosts(options?: {
    status?: string;
    category_id?: string;
    generation_source?: string;
    limit?: number;
    offset?: number;
  }): Promise<BlogPost[]> {
    try {
      let query = supabase.from('blog_posts').select('*');

      if (options?.status) {
        query = query.eq('status', options.status);
      }

      if (options?.category_id) {
        query = query.eq('category_id', options.category_id);
      }

      if (options?.generation_source) {
        query = query.eq('generation_source', options.generation_source);
      }

      if (options?.limit) {
        query = query.limit(options.limit);
      }

      if (options?.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 50) - 1);
      }

      query = query.order('created_at', { ascending: false });

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching blog posts:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching blog posts:', error);
      return [];
    }
  }

  /**
   * Get a single blog post by ID
   */
  static async getBlogPostById(id: string): Promise<BlogPost | null> {
    try {
      const { data, error } = await supabase
        .from('blog_posts')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching blog post:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error fetching blog post:', error);
      return null;
    }
  }

  /**
   * Get a single blog post by slug
   */
  static async getBlogPostBySlug(slug: string): Promise<BlogPost | null> {
    try {
      const { data, error } = await supabase
        .from('blog_posts')
        .select('*')
        .eq('slug', slug)
        .single();

      if (error) {
        console.error('Error fetching blog post by slug:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error fetching blog post by slug:', error);
      return null;
    }
  }

  /**
   * Update a blog post
   */
  static async updateBlogPost(id: string, updates: BlogPostUpdate): Promise<BlogPost | null> {
    try {
      const { data, error } = await supabase
        .from('blog_posts')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating blog post:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error updating blog post:', error);
      return null;
    }
  }

  /**
   * Delete a blog post
   */
  static async deleteBlogPost(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('blog_posts')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting blog post:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error deleting blog post:', error);
      return false;
    }
  }

  /**
   * Get published blog posts for public display
   */
  static async getPublishedBlogPosts(options?: {
    category_id?: string;
    limit?: number;
    offset?: number;
  }): Promise<BlogPost[]> {
    return this.getBlogPosts({
      ...options,
      status: 'published'
    });
  }

  /**
   * Get draft blog posts
   */
  static async getDraftBlogPosts(options?: {
    category_id?: string;
    limit?: number;
    offset?: number;
  }): Promise<BlogPost[]> {
    return this.getBlogPosts({
      ...options,
      status: 'draft'
    });
  }

  /**
   * Get AI-generated blog posts
   */
  static async getAIGeneratedPosts(options?: {
    category_id?: string;
    limit?: number;
    offset?: number;
  }): Promise<BlogPost[]> {
    return this.getBlogPosts({
      ...options,
      generation_source: 'ai_generated'
    });
  }

  /**
   * Publish a blog post
   */
  static async publishBlogPost(id: string): Promise<BlogPost | null> {
    return this.updateBlogPost(id, {
      status: 'published',
      published_at: new Date().toISOString()
    });
  }

  /**
   * Unpublish a blog post (set back to draft)
   */
  static async unpublishBlogPost(id: string): Promise<BlogPost | null> {
    return this.updateBlogPost(id, {
      status: 'draft',
      published_at: undefined
    });
  }
} 