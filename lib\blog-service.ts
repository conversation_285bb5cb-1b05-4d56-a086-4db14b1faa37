import { supabase } from './supabase-client'

export interface BlogCategory {
  id: string
  name: string
  slug: string
  description?: string
  color: string
  icon?: string
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface BlogAuthor {
  id: string
  name: string
  slug: string
  email?: string
  bio?: string
  medical_credentials?: string
  specialties?: string[]
  profile_image_url?: string
  social_links?: Record<string, string>
  is_medical_reviewer: boolean
  is_active: boolean
  user_id?: string
  created_at: string
  updated_at: string
}

export interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt?: string
  content: string
  featured_image_url?: string
  featured_image_alt?: string
  category_id: string
  author_id: string
  medical_reviewer_id?: string
  status: 'draft' | 'published' | 'archived'
  published_at?: string
  meta_title?: string
  meta_description?: string
  reading_time_minutes?: number
  view_count: number
  is_featured: boolean
  is_trending: boolean
  related_doctor_ids?: number[]
  related_team_ids?: number[]
  structured_data?: Record<string, any>
  created_at: string
  updated_at: string
  
  // Joined data
  category?: BlogCategory
  author?: BlogAuthor
  medical_reviewer?: BlogAuthor
  tags?: BlogTag[]
}

export interface BlogTag {
  id: string
  name: string
  slug: string
  description?: string
  color: string
  usage_count: number
  created_at: string
  updated_at: string
}

export interface BlogPostTag {
  id: string
  post_id: string
  tag_id: string
  created_at: string
}

// Blog Categories
export async function getBlogCategories(): Promise<BlogCategory[]> {
  const { data, error } = await supabase
    .from('blog_categories')
    .select('*')
    .eq('is_active', true)
    .order('sort_order', { ascending: true })

  if (error) {
    console.error('Error fetching blog categories:', error)
    return []
  }

  return data || []
}

export async function getBlogCategoryBySlug(slug: string): Promise<BlogCategory | null> {
  const { data, error } = await supabase
    .from('blog_categories')
    .select('*')
    .eq('slug', slug)
    .eq('is_active', true)
    .single()

  if (error) {
    console.error('Error fetching blog category:', error)
    return null
  }

  return data
}

// Blog Authors
export async function getBlogAuthors(): Promise<BlogAuthor[]> {
  const { data, error } = await supabase
    .from('blog_authors')
    .select('*')
    .eq('is_active', true)
    .order('name', { ascending: true })

  if (error) {
    console.error('Error fetching blog authors:', error)
    return []
  }

  return data || []
}

export async function getBlogAuthorBySlug(slug: string): Promise<BlogAuthor | null> {
  const { data, error } = await supabase
    .from('blog_authors')
    .select('*')
    .eq('slug', slug)
    .eq('is_active', true)
    .single()

  if (error) {
    console.error('Error fetching blog author:', error)
    return null
  }

  return data
}

// Blog Posts
export async function getBlogPosts(options: {
  limit?: number
  offset?: number
  categoryId?: string
  authorId?: string
  status?: 'draft' | 'published' | 'archived'
  featured?: boolean
  trending?: boolean
} = {}): Promise<BlogPost[]> {
  try {
    console.log('getBlogPosts called with options:', options)
    
    // First, try to get just the basic posts without joins
    let query = supabase
      .from('blog_posts')
      .select('*')

    // Apply filters
    if (options.status) {
      console.log('Filtering by status:', options.status)
      query = query.eq('status', options.status)
    }
    
    if (options.categoryId) {
      query = query.eq('category_id', options.categoryId)
    }
    
    if (options.authorId) {
      query = query.eq('author_id', options.authorId)
    }
    
    if (options.featured !== undefined) {
      query = query.eq('is_featured', options.featured)
    }
    
    if (options.trending !== undefined) {
      query = query.eq('is_trending', options.trending)
    }

    // Apply pagination
    if (options.limit) {
      query = query.limit(options.limit)
    }
    
    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1)
    }

    // Order by creation date (newest first)
    query = query.order('created_at', { ascending: false })

    console.log('Executing query...')
    const { data, error } = await query

    if (error) {
      console.error('Supabase error:', error)
      throw new Error(`Failed to fetch blog posts: ${error.message || 'Unknown error'}`)
    }

    console.log('Raw blog posts data:', data)
    console.log('Number of posts found:', data?.length || 0)

    if (!data || data.length === 0) {
      console.log('No posts found in database')
      return []
    }

    // Now fetch the related data separately
    const posts = await Promise.all(
      data.map(async (post) => {
        try {
          // Fetch category
          let category = null
          if (post.category_id) {
            const { data: categoryData } = await supabase
              .from('blog_categories')
              .select('*')
              .eq('id', post.category_id)
              .single()
            category = categoryData
          }

          // Fetch author
          let author = null
          if (post.author_id) {
            const { data: authorData } = await supabase
              .from('blog_authors')
              .select('*')
              .eq('id', post.author_id)
              .single()
            author = authorData
          }

          return {
            ...post,
            category,
            author
          }
        } catch (error) {
          console.error('Error fetching related data for post:', post.id, error)
          return {
            ...post,
            category: null,
            author: null
          }
        }
      })
    )

    console.log('Final posts with related data:', posts)
    return posts
  } catch (error) {
    console.error('Error in getBlogPosts:', error)
    return []
  }
}

export async function getBlogPostBySlug(slug: string): Promise<BlogPost | null> {
  try {
    console.log('getBlogPostBySlug: Fetching post with slug:', slug)
    
    // First, try to get the basic post data
    const { data: post, error: postError } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('slug', slug)
      .eq('status', 'published')
      .single()

    if (postError) {
      console.error('Error fetching blog post (basic query):', postError)
      return null
    }

    if (!post) {
      console.log('No post found with slug:', slug)
      return null
    }

    console.log('Found post:', post.title)

    // Now fetch related data separately
    let category = null
    let author = null
    let medical_reviewer = null
    let tags: BlogTag[] = []

    // Fetch category
    if (post.category_id) {
      const { data: categoryData } = await supabase
        .from('blog_categories')
        .select('*')
        .eq('id', post.category_id)
        .single()
      category = categoryData
    }

    // Fetch author
    if (post.author_id) {
      const { data: authorData } = await supabase
        .from('blog_authors')
        .select('*')
        .eq('id', post.author_id)
        .single()
      author = authorData
    }

    // Fetch medical reviewer
    if (post.medical_reviewer_id) {
      const { data: reviewerData } = await supabase
        .from('blog_authors')
        .select('*')
        .eq('id', post.medical_reviewer_id)
        .single()
      medical_reviewer = reviewerData
    }

    // Fetch tags
    try {
      tags = await getBlogPostTags(post.id)
    } catch (error) {
      console.warn('Could not fetch tags for post:', post.id, error)
    }

    // Increment view count
    try {
      await supabase
        .from('blog_posts')
        .update({ view_count: post.view_count + 1 })
        .eq('id', post.id)
    } catch (error) {
      console.warn('Could not increment view count:', error)
    }

    // Return combined data
    const enrichedPost: BlogPost = {
      ...post,
      category,
      author,
      medical_reviewer,
      tags
    }

    console.log('Successfully fetched and enriched post:', enrichedPost.title)
    return enrichedPost

  } catch (error) {
    console.error('Error in getBlogPostBySlug:', error)
    return null
  }
}

export async function getFeaturedBlogPosts(limit: number = 3): Promise<BlogPost[]> {
  return getBlogPosts({
    status: 'published',
    featured: true,
    limit
  })
}

export async function getRecentBlogPosts(limit: number = 6): Promise<BlogPost[]> {
  return getBlogPosts({
    status: 'published',
    limit
  })
}

export async function getBlogPostsByCategory(categorySlug: string, limit?: number): Promise<BlogPost[]> {
  const category = await getBlogCategoryBySlug(categorySlug)
  if (!category) return []

  return getBlogPosts({
    status: 'published',
    categoryId: category.id,
    limit
  })
}

export async function getRelatedBlogPosts(postId: string, categoryId: string, limit: number = 3): Promise<BlogPost[]> {
  const { data, error } = await supabase
    .from('blog_posts')
    .select(`
      *,
      category:blog_categories(*),
      author:blog_authors(*)
    `)
    .eq('status', 'published')
    .eq('category_id', categoryId)
    .neq('id', postId)
    .limit(limit)
    .order('published_at', { ascending: false })

  if (error) {
    console.error('Error fetching related blog posts:', error)
    return []
  }

  return data || []
}

// Blog Tags
export async function getBlogTags(): Promise<BlogTag[]> {
  const { data, error } = await supabase
    .from('blog_tags')
    .select('*')
    .order('usage_count', { ascending: false })

  if (error) {
    console.error('Error fetching blog tags:', error)
    return []
  }

  return data || []
}

export async function getBlogPostTags(postId: string): Promise<BlogTag[]> {
  const { data, error } = await supabase
    .from('blog_post_tags')
    .select(`
      tag:blog_tags(*)
    `)
    .eq('post_id', postId)

  if (error) {
    console.error('Error fetching blog post tags:', error)
    return []
  }

  return data?.map((item: any) => item.tag).filter(Boolean) as BlogTag[] || []
}

// Admin functions
export async function createBlogPost(post: Partial<BlogPost>): Promise<BlogPost | null> {
  try {
    console.log('Creating blog post with data:', post)
    
    const { data, error } = await supabase
      .from('blog_posts')
      .insert([post])
      .select()
      .single()

    if (error) {
      console.error('Error creating blog post:', error)
      console.error('Post data that failed:', post)
      throw new Error(`Failed to create blog post: ${error.message}`)
    }

    console.log('Successfully created blog post:', data)
    return data
  } catch (error) {
    console.error('Error in createBlogPost:', error)
    return null
  }
}

export async function createBlogAuthor(author: Partial<BlogAuthor>): Promise<BlogAuthor | null> {
  const { data, error } = await supabase
    .from('blog_authors')
    .insert([author])
    .select()
    .single()

  if (error) {
    console.error('Error creating blog author:', error)
    return null
  }

  return data
}

export async function updateBlogPost(id: string, updates: Partial<BlogPost>): Promise<BlogPost | null> {
  const { data, error } = await supabase
    .from('blog_posts')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating blog post:', error)
    return null
  }

  return data
}

export async function updateBlogAuthor(id: string, updates: Partial<BlogAuthor>): Promise<BlogAuthor | null> {
  const { data, error } = await supabase
    .from('blog_authors')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating blog author:', error)
    return null
  }

  return data
}

export async function deleteBlogPost(id: string): Promise<boolean> {
  const { error } = await supabase
    .from('blog_posts')
    .delete()
    .eq('id', id)

  if (error) {
    console.error('Error deleting blog post:', error)
    return false
  }

  return true
}

export async function deleteBlogAuthor(id: string): Promise<boolean> {
  console.log('Attempting to delete blog author with ID:', id)
  
  const { error } = await supabase
    .from('blog_authors')
    .delete()
    .eq('id', id)

  if (error) {
    console.error('Error deleting blog author:', error)
    console.error('Error details:', error.message, error.details)
    return false
  }

  console.log('Successfully deleted blog author with ID:', id)
  return true
}

// Blog statistics
export async function getBlogStats() {
  try {
    const [postsResult, authorsResult, categoriesResult] = await Promise.all([
      supabase.from('blog_posts').select('status', { count: 'exact' }),
      supabase.from('blog_authors').select('id', { count: 'exact' }).eq('is_active', true),
      supabase.from('blog_categories').select('id', { count: 'exact' }).eq('is_active', true)
    ])

    // Handle errors gracefully
    if (postsResult.error || authorsResult.error || categoriesResult.error) {
      console.warn('Some blog tables may not exist yet, returning default stats')
      return {
        totalPosts: 0,
        publishedPosts: 0,
        draftPosts: 0,
        totalAuthors: <AUTHORS>
        totalCategories: 0,
        totalViews: 0,
        monthlyViews: 0
      }
    }

    const totalPosts = postsResult.count || 0
    const publishedPosts = await supabase
      .from('blog_posts')
      .select('id', { count: 'exact' })
      .eq('status', 'published')
    
    const draftPosts = await supabase
      .from('blog_posts')
      .select('id', { count: 'exact' })
      .eq('status', 'draft')

    const totalViews = await supabase
      .from('blog_posts')
      .select('view_count')
      .eq('status', 'published')

    const viewsSum = totalViews.data?.reduce((sum, post) => sum + (post.view_count || 0), 0) || 0

    return {
      totalPosts,
      publishedPosts: publishedPosts.count || 0,
      draftPosts: draftPosts.count || 0,
      totalAuthors: <AUTHORS>
      totalCategories: categoriesResult.count || 0,
      totalViews: viewsSum,
      monthlyViews: Math.floor(viewsSum * 0.15) // Approximate monthly views
    }
  } catch (error) {
    console.error('Error fetching blog stats:', error)
    // Return default stats if database error
    return {
      totalPosts: 0,
      publishedPosts: 0,
      draftPosts: 0,
      totalAuthors: <AUTHORS>
      totalCategories: 0,
      totalViews: 0,
      monthlyViews: 0
    }
  }
}

// Get post counts per category (only published posts)
export async function getCategoryPostCounts(): Promise<Record<string, number>> {
  try {
    const { data, error } = await supabase
      .from('blog_posts')
      .select('category_id')
      .eq('status', 'published')

    if (error) {
      console.error('Error fetching category post counts:', error)
      return {}
    }

    // Count posts per category
    const counts: Record<string, number> = {}
    data?.forEach(post => {
      if (post.category_id) {
        counts[post.category_id] = (counts[post.category_id] || 0) + 1
      }
    })

    return counts
  } catch (error) {
    console.error('Error fetching category post counts:', error)
    return {}
  }
}

// Initialize basic blog data if tables are empty - for backward compatibility
export async function initializeBlogData() {
  try {
    // Check if we already have categories and authors
    const [categories, authors] = await Promise.all([
      getBlogCategories(),
      getBlogAuthors()
    ])

    console.log(`Found ${categories.length} categories and ${authors.length} authors`)
    
    // Tables exist and have data, no initialization needed
    return {
      success: true,
      message: `Blog system ready: ${categories.length} categories, ${authors.length} authors`
    }
  } catch (error) {
    console.error('Error checking blog data:', error)
    return {
      success: false,
      error: 'Error checking blog system'
    }
  }
} 