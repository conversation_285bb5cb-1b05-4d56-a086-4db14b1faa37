/**
 * Helper utility to preload critical assets for better Core Web Vitals
 */

/**
 * Preload important images
 */
export function preloadCriticalImages() {
  if (typeof window === 'undefined') return;
  
  const criticalImages = [
    "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/hero-bg.jpg-jFYKbLWbhBg1O4C6J4ACQ2hVfLLFZB.jpeg",
    "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo-gO7TTezH8tR3LvsxNVrbtcdUVAqKGB.png"
  ]

  criticalImages.forEach(imageSrc => {
    try {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = imageSrc
      link.as = 'image'
      link.type = 'image/jpeg'
      document.head.appendChild(link)
    } catch (e) {
      console.error('Error preloading image:', e);
    }
  })
}

/**
 * Defer non-critical scripts
 */
export function deferNonCriticalScripts() {
  if (typeof window === 'undefined') return;
  
  // Add observer to load scripts when user is idle
  if ('requestIdleCallback' in window) {
    const loadNonCriticalScripts = () => {
      // Example: Load analytics or other non-critical scripts here
      const scriptTags = [
        { src: 'https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXX', async: true },
      ]

      scriptTags.forEach(script => {
        try {
          const scriptElement = document.createElement('script')
          scriptElement.src = script.src
          if (script.async) scriptElement.async = true
          document.body.appendChild(scriptElement)
        } catch (e) {
          console.error('Error loading script:', e);
        }
      })
    }

    window.requestIdleCallback(loadNonCriticalScripts)
  }
}

/**
 * Initialize performance optimizations
 */
export function initPerformanceOptimizations() {
  if (typeof window === 'undefined') return;
  
  console.log('[Critical Resources] Initializing performance optimizations');
  
  try {
    // Listen for largest contentful paint
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          const lcpEntry = entries[entries.length - 1];
          
          console.log('LCP:', lcpEntry.startTime);
          // Safe access to LCP element (if available)
          if (lcpEntry && 'element' in lcpEntry) {
            console.log('LCP element:', lcpEntry.element);
          }
          
          // Clean up observer
          lcpObserver.disconnect();
        });
        
        lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });
      } catch (e) {
        console.error('LCP observer error:', e);
      }
    }

    // Preload critical images after current task
    setTimeout(() => {
      preloadCriticalImages()
    }, 0)

    // Defer non-critical scripts
    deferNonCriticalScripts()
    
    console.log('[Critical Resources] Performance optimizations initialized successfully');
  } catch (e) {
    console.error('[Critical Resources] Error initializing performance optimizations:', e);
  }
} 