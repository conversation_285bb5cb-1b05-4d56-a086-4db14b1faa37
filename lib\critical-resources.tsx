"use client"

/**
 * Helper utility to preload critical assets for better Core Web Vitals
 */

/**
 * Preload important images
 */
export function preloadCriticalImages() {
  const criticalImages = [
    "/stadium-bg.webp",
    "/logo.webp"
  ]

  criticalImages.forEach(imageSrc => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = imageSrc
    link.as = 'image'
    link.type = 'image/webp'
    document.head.appendChild(link)
  })
}

/**
 * Defer non-critical scripts
 */
export function deferNonCriticalScripts() {
  // Add observer to load scripts when user is idle
  if ('requestIdleCallback' in window) {
    const loadNonCriticalScripts = () => {
      // Example: Load analytics or other non-critical scripts here
      const scriptTags = [
        { src: 'https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXX', async: true },
      ]

      scriptTags.forEach(script => {
        const scriptElement = document.createElement('script')
        scriptElement.src = script.src
        if (script.async) scriptElement.async = true
        document.body.appendChild(scriptElement)
      })
    }

    window.requestIdleCallback(loadNonCriticalScripts)
  }
}

/**
 * Initialize performance optimizations
 */
export function initPerformanceOptimizations() {
  if (typeof window !== 'undefined') {
    // Listen for largest contentful paint
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          const lcpEntry = entries[entries.length - 1];
          
          console.log('LCP:', lcpEntry.startTime);
          // TypeScript safe access to LCP element (if available)
          if (lcpEntry && 'element' in lcpEntry) {
            console.log('LCP element:', (lcpEntry as any).element);
          }
          
          // Clean up observer
          lcpObserver.disconnect();
        });
        
        lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });
      } catch (e) {
        console.error('LCP observer error:', e);
      }
    }

    // Preload critical images after current task
    setTimeout(() => {
      preloadCriticalImages()
    }, 0)

    // Defer non-critical scripts
    deferNonCriticalScripts()
  }
} 