import { getCountries, getCountryById, getCountryByName } from './services/countries-service'
import { getSpecialties, getSpecialtyById } from './services/specialties-service'
import { getDoctors, getDoctorById, getDoctorsByCountryAndSpecialty, getFeaturedDoctors } from './services/doctors-service'
import { getHospitals, getHospitalsByCountry, getHospitalById } from './services/hospitals-service'
import { getDoctorReviews } from './services/reviews-service'

// Re-export service functions for backward compatibility
export {
  getCountries,
  getCountryById,
  getCountryByName,
  getSpecialties,
  getSpecialtyById,
  getDoctorById,
  getDoctorsByCountryAndSpecialty,
  getHospitals,
  getHospitalsByCountry,
  getDoctorReviews
}

// Helper functions that need to be adapted from static to dynamic
export async function getDoctorsBySpecialtyAndCountry(countryId: string, specialtyName: string) {
  // First get the specialty ID by name
  const specialties = await getSpecialties();
  const specialty = specialties.find(s => s.specialty_name === specialtyName);
  
  if (!specialty) {
    return [];
  }
  
  // Use the existing function with the specialty ID
  return getDoctorsByCountryAndSpecialty(countryId, specialty.specialty_id.toString());
}

export async function getDoctorsByHospital(hospitalId: string) {
  const doctors = await getDoctors();
  return doctors.filter(doctor => doctor.hospital_id.toString() === hospitalId);
}

export async function getLeagueStandings(specialty: string) {
  const doctors = await getDoctors();
  return doctors
    .filter(doctor => doctor.specialty === specialty)
    .sort((a, b) => (b.rating || 0) - (a.rating || 0))
    .slice(0, 10)
    .map(doctor => ({
      ...doctor,
      draws: 0,
      points: (doctor.wins || 0) * 3,
    }));
}

export async function getTopSpecialties() {
  const specialties = await getSpecialties();
  return specialties.slice(0, 5);
}

export async function getTopDoctors() {
  const doctors = await getDoctors();
  return doctors
    .sort((a, b) => (b.rating || 0) - (a.rating || 0))
    .slice(0, 4)
    .map(doctor => ({
      ...doctor,
      matches_played: (doctor.wins || 0) + (doctor.losses || 0),
    }));
}

