import { create<PERSON>rowser<PERSON><PERSON> } from "./supabase"
import { COUNTRIE<PERSON>, SPECIALTIES, DOCTORS, HOSPITALS } from "./static-data"

// Types
export interface Doctor {
  doctor_id: string
  fullname: string
  facility: string
  medical_title: string
  specialty: string
  subspecialty?: string
  educational_background: string
  board_certifications?: string
  experience: number
  publications?: string
  awards_recognitions?: string
  phone_number: string
  email: string
  languages_spoken: string
  professional_affiliations?: string
  procedures_performed?: string
  treatment_services_expertise?: string
  hospital_id?: string
  country_id?: string
  image_path?: string
  wins?: number
  losses?: number
  form?: string
  community_rating?: number
  review_count?: number
  last_updated?: string
}

export interface Country {
  country_id: string
  country_name: string
}

export interface Specialty {
  specialty_id: string
  specialty_name: string
  description?: string
}

export interface Hospital {
  hospital_id: string
  hospital_name: string
  country_id: string
  city: string
  address: string
  email_info?: string
  telephone_info?: string
  rating?: number
  review_count?: number
}

export interface Review {
  review_id: string
  user_id: string
  doctor_id: string
  clinical_competence: number
  communication_stats: number
  empathy_compassion: number
  time_management: number
  follow_up_care: number
  overall_satisfaction: number
  additional_comments?: string
  recommendation_rating: number
  rating: number
  review_date: string
}

export interface User {
  user_id: string
  username: string
  email: string
  first_name?: string
  last_name?: string
  gender?: string
  age?: number
  city?: string
  country?: string
  user_type: "patient" | "doctor"
}

// Database service class
export class DatabaseService {
  // Get countries
  static async getCountries(): Promise<Country[]> {
    try {
      const supabase = await createBrowserClient()
      const { data, error } = await supabase.from("countries").select("*").order("country_name")

      if (error) throw error
      return data || COUNTRIES
    } catch (error) {
      console.error("Error fetching countries:", error)
      return COUNTRIES
    }
  }

  // Get specialties
  static async getSpecialties(): Promise<Specialty[]> {
    try {
      const supabase = await createBrowserClient()
      const { data, error } = await supabase.from("specialties").select("*").order("specialty_name")

      if (error) throw error
      return data || SPECIALTIES
    } catch (error) {
      console.error("Error fetching specialties:", error)
      return SPECIALTIES
    }
  }

  // Get doctor by ID
  static async getDoctorById(doctorId: string): Promise<Doctor | null> {
    try {
      const supabase = await createBrowserClient()
      const { data, error } = await supabase
        .from("doctors")
        .select(`
          *,
          hospitals (
            hospital_id,
            hospital_name,
            city,
            address
          ),
          countries (
            country_id,
            country_name
          )
        `)
        .eq("doctor_id", doctorId)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error(`Error fetching doctor with ID ${doctorId}:`, error)
      // Fallback to static data
      const staticDoctor = DOCTORS.find((d) => d.doctor_id.toString() === doctorId)
      return staticDoctor || null
    }
  }

  // Get doctors by country and specialty
  static async getDoctorsByCountryAndSpecialty(countryId: string, specialtyId: string): Promise<Doctor[]> {
    try {
      const supabase = await createBrowserClient()
      const { data, error } = await supabase
        .from("doctors")
        .select(`
          doctor_id,
          fullname,
          facility,
          specialty,
          community_rating,
          wins,
          losses,
          form,
          hospitals (
            hospital_name
          )
        `)
        .eq("country_id", countryId)
        .eq("specialty_id", specialtyId)
        .order("community_rating", { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error("Error fetching doctors by country and specialty:", error)
      // Fallback to static data
      return DOCTORS.filter((d) => d.country_id?.toString() === countryId && d.specialty_id?.toString() === specialtyId)
    }
  }

  // Get hospitals by country
  static async getHospitalsByCountry(countryId: string): Promise<Hospital[]> {
    try {
      const supabase = await createBrowserClient()
      const { data, error } = await supabase
        .from("hospitals")
        .select("*")
        .eq("country_id", countryId)
        .order("hospital_name")

      if (error) throw error
      return data || []
    } catch (error) {
      console.error("Error fetching hospitals by country:", error)
      // Fallback to static data
      return HOSPITALS.filter((h) => h.country_id.toString() === countryId)
    }
  }

  // Get reviews for a doctor
  static async getDoctorReviews(doctorId: string): Promise<Review[]> {
    try {
      const supabase = await createBrowserClient()
      const { data, error } = await supabase
        .from("reviews")
        .select(`
          *,
          users (
            username
          )
        `)
        .eq("doctor_id", doctorId)
        .order("review_date", { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error("Error fetching reviews:", error)
      return []
    }
  }

  // Create a new review
  static async createReview(
    review: Omit<Review, "review_id" | "review_date">,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = await createBrowserClient()
      const { error } = await supabase.from("reviews").insert({
        ...review,
        review_date: new Date().toISOString(),
      })

      if (error) throw error
      return { success: true }
    } catch (error: any) {
      console.error("Error creating review:", error)
      return { success: false, error: error.message }
    }
  }

  // Update doctor profile
  static async updateDoctor(doctorId: string, updates: Partial<Doctor>): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = await createBrowserClient()
      const { error } = await supabase
        .from("doctors")
        .update({
          ...updates,
          last_updated: new Date().toISOString(),
        })
        .eq("doctor_id", doctorId)

      if (error) throw error
      return { success: true }
    } catch (error: any) {
      console.error("Error updating doctor:", error)
      return { success: false, error: error.message }
    }
  }

  // Upload profile image
  static async uploadProfileImage(file: File, userId: string): Promise<{ path?: string; error?: string }> {
    try {
      const supabase = await createBrowserClient()
      const fileExt = file.name.split(".").pop()
      const fileName = `${userId}-${Date.now()}.${fileExt}`
      const filePath = `${fileName}`

      const { error } = await supabase.storage.from("profile-images").upload(filePath, file)

      if (error) throw error

      const { data } = supabase.storage.from("profile-images").getPublicUrl(filePath)

      return { path: data.publicUrl }
    } catch (error: any) {
      console.error("Error uploading image:", error)
      return { error: error.message }
    }
  }
}

