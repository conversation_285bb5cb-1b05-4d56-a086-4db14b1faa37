import sqlite3 from "sqlite3"
import { open } from "sqlite"

let db: any = null

async function openDb() {
  if (!db) {
    db = await open({
      filename: "./doctorsleagues.sqlite",
      driver: sqlite3.Database,
    })
  }
  return db
}

export async function query(sql: string, params: any[] = []) {
  const db = await openDb()
  return db.all(sql, params)
}

export async function run(sql: string, params: any[] = []) {
  const db = await openDb()
  return db.run(sql, params)
}

export async function get(sql: string, params: any[] = []) {
  const db = await openDb()
  return db.get(sql, params)
}

// Initialize the database
export async function initializeDatabase() {
  const db = await openDb()

  // Create tables
  await db.exec(`
    CREATE TABLE IF NOT EXISTS countries (
      country_id INTEGER PRIMARY KEY AUTOINCREMENT,
      country_name TEXT NOT NULL UNIQUE
    );

    CREATE TABLE IF NOT EXISTS hospitals (
      hospital_id INTEGER PRIMARY KEY AUTOINCREMENT,
      hospital_name TEXT NOT NULL,
      country_id INTEGER,
      city TEXT,
      address TEXT,
      email_info TEXT,
      telephone_info TEXT,
      rating REAL DEFAULT 0,
      review_count INTEGER DEFAULT 0,
      FOREIGN KEY (country_id) REFERENCES countries(country_id)
    );

    CREATE TABLE IF NOT EXISTS doctors (
      doctor_id INTEGER PRIMARY KEY AUTOINCREMENT,
      Fullname TEXT NOT NULL,
      facility TEXT,
      medical_title TEXT,
      specialty TEXT,
      subspecialty TEXT,
      educational_background TEXT,
      Board_certifications TEXT,
      experience INTEGER,
      publications TEXT,
      awards_recognitions TEXT,
      phone_number TEXT,
      email TEXT,
      languages_spoken TEXT,
      professional_affiliations TEXT,
      procedures_performed TEXT,
      treatment_services_expertise TEXT,
      hospital_id INTEGER,
      image_path TEXT,
      wins INTEGER DEFAULT 0,
      losses INTEGER DEFAULT 0,
      form TEXT DEFAULT "",
      rating REAL DEFAULT 0,
      review_count INTEGER DEFAULT 0,
      last_updated DATE,
      FOREIGN KEY (hospital_id) REFERENCES hospitals(hospital_id)
    );

    CREATE TABLE IF NOT EXISTS specialties (
      specialty_id INTEGER PRIMARY KEY AUTOINCREMENT,
      specialty_name TEXT NOT NULL,
      description TEXT
    );

    CREATE TABLE IF NOT EXISTS reviews (
      review_id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER,
      doctor_id INTEGER,
      clinical_competence INTEGER CHECK (clinical_competence BETWEEN 1 AND 5),
      communication_stats INTEGER CHECK (communication_stats BETWEEN 1 AND 5),
      empathy_compassion INTEGER CHECK (empathy_compassion BETWEEN 1 AND 5),
      time_management INTEGER CHECK (time_management BETWEEN 1 AND 5),
      follow_up_care INTEGER CHECK (follow_up_care BETWEEN 1 AND 5),
      overall_satisfaction INTEGER CHECK (overall_satisfaction BETWEEN 1 AND 5),
      additional_comments TEXT,
      Recommendation_rating INTEGER CHECK (Recommendation_rating BETWEEN 1 AND 5),
      rating REAL,
      review_date DATE,
      FOREIGN KEY (user_id) REFERENCES users(user_id),
      FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id)
    );

    CREATE TABLE IF NOT EXISTS feedback (
      feedback_id INTEGER PRIMARY KEY AUTOINCREMENT,
      doctor_id INTEGER,
      peer_id INTEGER,
      feedback_text TEXT,
      rating INTEGER CHECK (rating BETWEEN 1 AND 5),
      feedback_date DATE,
      FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id),
      FOREIGN KEY (peer_id) REFERENCES doctors(doctor_id)
    );

    CREATE TABLE IF NOT EXISTS appointments (
      appointment_id INTEGER PRIMARY KEY AUTOINCREMENT,
      doctor_id INTEGER,
      user_id INTEGER,
      appointment_date DATE,
      status TEXT,
      notes TEXT,
      FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id),
      FOREIGN KEY (user_id) REFERENCES users(user_id)
    );

    CREATE TABLE IF NOT EXISTS users (
      user_id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      email TEXT UNIQUE NOT NULL,
      password TEXT NOT NULL,
      first_name TEXT,
      last_name TEXT,
      gender TEXT,
      age INTEGER,
      city TEXT,
      country TEXT,
      user_type TEXT CHECK (user_type IN ('patient', 'doctor'))
    );
  `)

  console.log("Database initialized")
}

// Sample data insertion
export async function insertSampleData() {
  const db = await openDb()

  // Insert sample countries
  await db.run(
    "INSERT INTO countries (country_name) VALUES ('Bahrain'), ('Kuwait'), ('Oman'), ('Qatar'), ('Saudi Arabia'), ('UAE')",
  )

  // Insert sample hospitals
  await db.run(`
    INSERT INTO hospitals (hospital_name, country_id, city, address, email_info, telephone_info, rating, review_count)
    VALUES 
    ('Central Hospital', 1, 'Manama', '123 Main St', '<EMAIL>', '+973 1234 5678', 4.5, 100),
    ('Kuwait Medical Center', 2, 'Kuwait City', '456 Health Ave', '<EMAIL>', '+965 2345 6789', 4.2, 80),
    ('Oman Royal Hospital', 3, 'Muscat', '789 Sultan Qaboos St', '<EMAIL>', '+968 3456 7890', 4.7, 120)
  `)

  // Insert sample doctors
  await db.run(`
    INSERT INTO doctors (Fullname, facility, medical_title, specialty, subspecialty, educational_background, experience, hospital_id, image_path, rating, review_count)
    VALUES 
    ('Dr. Ahmed Al-Mansoori', 'Central Hospital', 'Consultant Cardiologist', 'Cardiology', 'Interventional Cardiology', 'MD from Harvard Medical School', 15, 1, '/images/doctors/ahmed-al-mansoori.jpg', 4.8, 50),
    ('Dr. Fatima Al-Kuwari', 'Kuwait Medical Center', 'Senior Neurologist', 'Neurology', 'Stroke Medicine', 'PhD from Johns Hopkins University', 12, 2, '/images/doctors/fatima-al-kuwari.jpg', 4.6, 40),
    ('Dr. Khalid Al-Balushi', 'Oman Royal Hospital', 'Chief Pediatrician', 'Pediatrics', 'Neonatology', 'Fellowship from Great Ormond Street Hospital', 20, 3, '/images/doctors/khalid-al-balushi.jpg', 4.9, 60)
  `)

  // Insert sample users
  await db.run(`
    INSERT INTO users (username, email, password, first_name, last_name, gender, age, city, country, user_type)
    VALUES 
    ('patient1', '<EMAIL>', 'hashed_password', 'Ali', 'Hassan', 'Male', 35, 'Manama', 'Bahrain', 'patient'),
    ('doctor1', '<EMAIL>', 'hashed_password', 'Ahmed', 'Al-Mansoori', 'Male', 45, 'Manama', 'Bahrain', 'doctor')
  `)

  // Insert sample reviews
  await db.run(`
    INSERT INTO reviews (user_id, doctor_id, clinical_competence, communication_stats, empathy_compassion, time_management, follow_up_care, overall_satisfaction, additional_comments, Recommendation_rating, rating, review_date)
    VALUES 
    (1, 1, 5, 4, 5, 4, 5, 5, 'Excellent doctor, very knowledgeable and caring', 5, 4.8, '2023-06-15'),
    (1, 2, 4, 5, 5, 4, 4, 5, 'Great experience, would highly recommend', 5, 4.6, '2023-07-20')
  `)

  console.log("Sample data inserted")
}

// Call these functions to set up the database
initializeDatabase().then(() => insertSampleData())

