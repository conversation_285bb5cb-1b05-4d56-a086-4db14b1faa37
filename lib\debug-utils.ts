/**
 * Debug utilities for the Doctors League application
 * This file contains utilities for debugging and logging
 */

/**
 * Console logger with timestamps and context
 * Only logs in development environment
 */
export const logger = {
  /**
   * Log an informational message
   */
  info: (context: string, message: string, data?: any) => {
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] [${context}] INFO: ${message}`);
      if (data !== undefined) {
        console.log(`[${timestamp}] [${context}] DATA:`, data);
      }
    }
  },
  
  /**
   * Log a warning message
   */
  warn: (context: string, message: string, data?: any) => {
    if (typeof window !== 'undefined') {
      const timestamp = new Date().toISOString();
      console.warn(`[${timestamp}] [${context}] WARNING: ${message}`);
      if (data !== undefined) {
        console.warn(`[${timestamp}] [${context}] DATA:`, data);
      }
    }
  },
  
  /**
   * Log an error message
   */
  error: (context: string, message: string, error?: any) => {
    const timestamp = new Date().toISOString();
    console.error(`[${timestamp}] [${context}] ERROR: ${message}`);
    if (error !== undefined) {
      console.error(`[${timestamp}] [${context}] ERROR DETAILS:`, error);
    }
  },
  
  /**
   * Log a debug message (only in development)
   */
  debug: (context: string, message: string, data?: any) => {
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      const timestamp = new Date().toISOString();
      console.debug(`[${timestamp}] [${context}] DEBUG: ${message}`);
      if (data !== undefined) {
        console.debug(`[${timestamp}] [${context}] DEBUG DATA:`, data);
      }
    }
  }
};

/**
 * Create a performance measurement utility
 * Only measures in development environment
 */
export const performance = {
  /**
   * Start timing an operation
   */
  start: (operationName: string): (() => void) => {
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      const startTime = Date.now();
      return () => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        logger.info('PERFORMANCE', `Operation '${operationName}' took ${duration}ms`);
      };
    }
    return () => {}; // No-op in production
  }
};

/**
 * Utility to safely stringify objects for logging
 * Prevents circular reference errors
 */
export function safeStringify(obj: any): string {
  const cache = new Set();
  return JSON.stringify(obj, (key, value) => {
    if (typeof value === 'object' && value !== null) {
      if (cache.has(value)) {
        return '[Circular Reference]';
      }
      cache.add(value);
    }
    return value;
  }, 2);
} 