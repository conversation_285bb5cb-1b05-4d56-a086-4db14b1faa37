import * as XLSX from 'xlsx';

export interface SpecialtyFromExcel {
  specialty_id?: number;
  specialty_name: string;
  description?: string;
}

/**
 * Reads specialties from an Excel file
 * @returns Array of specialty objects
 */
export async function readSpecialtiesFromExcel(): Promise<SpecialtyFromExcel[]> {
  try {
    // In a browser environment, we can't directly access the filesystem
    // We'll need to use fetch to get the Excel file
    console.log("Attempting to fetch specialties Excel file");
    
    // Try to fetch the file from the public directory
    const response = await fetch('/data/specialties.xlsx');
    
    if (!response.ok) {
      console.error(`Failed to fetch Excel file: ${response.status} ${response.statusText}`);
      return loadFallbackSpecialties();
    }
    
    // Convert response to array buffer
    const arrayBuffer = await response.arrayBuffer();
    
    // Parse the Excel file using XLSX
    const workbook = XLSX.read(new Uint8Array(arrayBuffer), { type: 'array' });
    
    // Get the first sheet
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    // Convert to JSON
    const specialtiesData = XLSX.utils.sheet_to_json<SpecialtyFromExcel>(worksheet);
    
    console.log(`Successfully loaded ${specialtiesData.length} specialties from Excel`);
    return specialtiesData;
  } catch (error) {
    console.error('Error loading Excel file:', error);
    return loadFallbackSpecialties();
  }
}

/**
 * Provides a set of fallback specialties if Excel loading fails
 */
function loadFallbackSpecialties(): SpecialtyFromExcel[] {
  return [
    { specialty_id: 1, specialty_name: "Cardiology", description: "Heart and cardiovascular system" },
    { specialty_id: 2, specialty_name: "Neurology", description: "Brain and nervous system" },
    { specialty_id: 3, specialty_name: "Orthopedics", description: "Musculoskeletal system" },
    { specialty_id: 4, specialty_name: "Pediatrics", description: "Children's health" },
    { specialty_id: 5, specialty_name: "Psychiatry", description: "Mental health" },
    { specialty_id: 6, specialty_name: "Dermatology", description: "Skin conditions" },
    { specialty_id: 7, specialty_name: "Oncology", description: "Cancer treatment" },
    { specialty_id: 8, specialty_name: "Emergency Medicine", description: "Urgent and critical care" },
    { specialty_id: 9, specialty_name: "Family Medicine", description: "General healthcare for all ages" },
    { specialty_id: 10, specialty_name: "Ophthalmology", description: "Eye health" },
    { specialty_id: 11, specialty_name: "Gastroenterology", description: "Digestive system" },
    { specialty_id: 12, specialty_name: "Endocrinology", description: "Hormonal systems" },
    { specialty_id: 13, specialty_name: "Hematology", description: "Blood disorders" },
    { specialty_id: 14, specialty_name: "Radiology", description: "Medical imaging" },
    { specialty_id: 15, specialty_name: "General Medicine", description: "Comprehensive healthcare" }
  ];
} 