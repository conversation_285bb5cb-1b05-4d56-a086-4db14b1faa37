// List of actual filenames in the /public/Green/ directory
const iconFiles = [
    "Allergy and immunology.svg", "Anaesthesia.svg", "Audiology-Speech therapy.svg",
    "Baromedicine.svg", "Cardiac Surgery.svg", "Cardiology.svg", "Chiropractic Care.svg",
    "Dental.svg", "Dermatology & cosmetology.svg", "Dietician.svg",
    "Ear, nose and throat (ent).svg", "Emergency medicine.svg", "Endocrinology.svg",
    "Family medicine.svg", "Gastroenterology.svg", "General medicine.svg",
    "General surgery.svg", "Geriatric Medicine.svg", "Hematology.svg", "Icu.svg",
    "Infectious diseases & clinical microbiology.svg", "Internal medicine.svg",
    "Ivf - in vitro fertilization.svg", "Medical genetics.svg", "Mental health.svg",
    "Neonatology.svg", "Nephrology.svg", "Neurology.svg", "Neurosurgery.svg",
    "Nutrition.svg", "Obstetrics and gynecology.svg", "Oncology.svg",
    "Ophthalmology.svg", "Orthopedics.svg", "Osteopathy.svg", "Pathology.svg",
    "Pediatric cardiology.svg", "Pediatrics.svg", "Physiotherapy.svg",
    "Plastic surgery.svg", "Podiatry.svg", "Psychiatry.svg",
    "Public Health Medicine.svg", "Pulmonology.svg", "Radiology.svg",
    "Rheumatology.svg", "Sports medicine.svg", "Urology.svg",
    "Vascular medicine.svg", "Vascularsurgery.svg"
];

// Function to normalize strings for comparison
const normalize = (str: string) => str.toLowerCase().replace(/[^a-z0-9]/g, '');

export const getSpecialtyIconPath = (specialtyName: string): string => {
    const normalizedSpecialty = normalize(specialtyName);

    // Create a map for common variations
    const variations: { [key: string]: string } = {
        'anesthesia': 'anaesthesia',
        'dermatology': 'dermatologycosmetology',
        'dietetics': 'dietician',
        'ent': 'earnosethroatent',
        'infectiousdiseases': 'infectiousdiseasesclinicalmicrobiology',
        'ivf': 'ivfinvitrofertilization',
        'occupationalmedicine': 'generalmedicine',
        'psychiatryandpsychology': 'psychiatry',
        'reproductiveendocrinologyandinfertility': 'ivfinvitrofertilization'
    };
    
    const mappedSpecialty = variations[normalizedSpecialty] || normalizedSpecialty;

    let bestMatch = '';
    let highestScore = 0;

    // Find the best matching filename from the actual list
    for (const file of iconFiles) {
        const normalizedFile = normalize(file.replace('.svg', ''));
        
        if (normalizedFile === mappedSpecialty) {
            bestMatch = file;
            break; // Perfect match found
        }
        
        // Simple similarity score for partial matches
        const score = mappedSpecialty.split('').filter(char => normalizedFile.includes(char)).length;

        if (score > highestScore) {
            highestScore = score;
            bestMatch = file;
        }
    }
    
    // Fallback to a default icon if no good match is found
    const finalFileName = bestMatch || 'General medicine.svg'; 

    return `/Green/${finalFileName}`;
}; 