// Import only the singleton client instance
import { supabase } from "./supabase-client" 
import {
  <PERSON><PERSON><PERSON><PERSON>K_COUNTRIES,
  FA<PERSON><PERSON>CK_SPECIALTIES,
  FALL<PERSON><PERSON>K_DOCTORS,
  FALL<PERSON><PERSON><PERSON>_HOSPITALS,
  <PERSON>LL<PERSON>CK_REVIEWS,
} from "./fallback-data"

// Add these imports and cache configuration at the top of the file
import { unstable_cache } from 'next/cache';

// Cache configuration
const HOUR_IN_SECONDS = 60 * 60;
const DAY_IN_SECONDS = 24 * HOUR_IN_SECONDS;

// Configure revalidation times for different entity types
const CACHE_TIMES = {
  COUNTRIES: DAY_IN_SECONDS, // Countries rarely change
  SPECIALTIES: DAY_IN_SECONDS, // Specialties rarely change
  DOCTORS: 3 * HOUR_IN_SECONDS, // Doctor data might change more frequently
  STATS: HOUR_IN_SECONDS, // Stats can change fairly frequently
}

// REMOVE environment check and getClient function
// const isBrowser = typeof window !== 'undefined'
// const getClient = () => { ... }
// console.log(...)

// Types
export interface Doctor {
  doctor_id: string | number
  fullname: string
  hospital: string
  medical_title: string
  specialty: string
  subspecialty?: string
  educational_background: string
  board_certifications?: string
  experience: number
  publications?: string
  awards_recognitions?: string
  phone_number?: string
  email?: string
  languages_spoken?: string
  professional_affiliations?: string
  procedures_performed?: string
  treatment_services_expertise?: string
  hospital_id?: string | number
  country_id?: string | number
  image_path?: string
  profile_image?: string
  wins?: number
  losses?: number
  form?: string
  community_rating?: number | null
  review_count?: number
  last_updated?: string
  specialty_id?: string | number
  verified_rating?: number | null
  ranking_score?: number
}

export interface Country {
  country_id: string | number
  country_name: string
  flag_url?: string
}

// New type including doctor count
export interface CountryWithDoctorCount extends Country {
  doctor_count: number;
}

export interface Specialty {
  specialty_id: string | number
  specialty_name: string
  description?: string
}

export interface Hospital {
  hospital_id: string | number
  hospital_name: string
  country_id: string | number
  city: string
  address: string
  email_info?: string
  telephone_info?: string
  rating?: number
  review_count?: number
}

export interface Review {
  review_id: string
  user_id: string
  doctor_id: string
  clinical_competence: number
  communication_stats: number
  empathy_compassion: number
  time_management: number
  follow_up_care: number
  overall_satisfaction: number
  additional_comments?: string
  recommendation_rating?: number
  Recommendation: number
  rating: number
  review_date: string
  verification_status?: 'unverified' | 'pending_verification' | 'verified' | 'rejected'
  user?: {
    username: string
  }
}

// Helper function to handle ID comparisons safely
const safeIdMatch = (id1: string | number, id2: string | number): boolean => {
  // Convert both to strings for comparison
  return String(id1) === String(id2);
}

// Convert ID to number if it's numeric
const parseId = (id: string | number): string | number => {
  if (typeof id === 'string') {
    const numId = parseInt(id, 10);
    if (!isNaN(numId)) {
      return numId;
    }
  }
  return id;
}

// Ensure country_id in FALLBACK_DOCTORS
FALLBACK_DOCTORS.forEach(doctor => {
  if (!doctor.country_id) {
    doctor.country_id = 1;
  }
  if (!doctor.specialty_id) {
    doctor.specialty_id = 1;
  }
});

// Cache implementation for getCountryById
export const getCountryById = unstable_cache(
  async (countryId: string | number): Promise<CountryWithDoctorCount | null> => {
    if (!countryId) {
      console.error(`getCountryById: Missing countryId parameter`);
      return null;
    }

    console.log(`getCountryById: Fetching country with ID: ${countryId}`);
    
    // Ensure we have a consistent format for the country ID
    const formattedCountryId = String(countryId);
    
    try {
      // First, try to fetch from Supabase
      // Directly use the imported supabase client (Anon Key)
      const { data, error } = await supabase
        .from('countries')
        .select('*')
        .eq('country_id', formattedCountryId)
        .single();
    
      if (!error && data) {
        console.log(`getCountryById: Successfully fetched country from Supabase: ${data.country_name}`);

        // Fetch doctor count for this country
        let doctorCount = 0;
        try {
          const { count, error: countError } = await supabase
            .from('doctors')
            .select('*', { count: 'exact', head: true })
            .eq('country_id', formattedCountryId); // Use the same formatted ID

          if (countError) {
            console.error(`getCountryById: Error fetching doctor count for country ${formattedCountryId}:`, countError);
            // Use a fallback count or handle error appropriately
            doctorCount = 0; // Defaulting to 0 on error
          } else {
            doctorCount = count || 0;
            console.log(`getCountryById: Fetched doctor count: ${doctorCount} for country ${formattedCountryId}`);
          }
        } catch (countCatchError) {
           console.error(`getCountryById: Exception fetching doctor count for country ${formattedCountryId}:`, countCatchError);
           doctorCount = 0; // Fallback on exception
        }

        // Return combined data
        return { ...data, doctor_count: doctorCount };
      }

      // If database lookup fails, use fallback data
      if (error) {
        console.error(`getCountryById: Error fetching country with ID ${formattedCountryId}:`, error);
        console.log(`getCountryById: Falling back to static data`);
      }
      
      // Try fallback data
      const fallbackCountry = FALLBACK_COUNTRIES.find(c => String(c.country_id) === String(countryId));
      
      if (fallbackCountry) {
        console.log(`getCountryById: Found country ${fallbackCountry.country_name} in fallback data`);
        // Provide a fallback count for the fallback country
        const fallbackDoctorCount = FALLBACK_DOCTORS.filter(d => String(d.country_id) === String(countryId)).length;
        console.log(`getCountryById: Using fallback doctor count: ${fallbackDoctorCount}`);
        return { ...fallbackCountry, doctor_count: fallbackDoctorCount } as any;
      }

      console.log(`getCountryById: No country found for ID ${countryId} in fallback data either`);
      return null;
    } catch (error) {
      console.error(`getCountryById: Exception while fetching country with ID ${countryId}:`, error);
      
      // Try to find by ID in fallback data
      const fallbackCountry = FALLBACK_COUNTRIES.find(c => String(c.country_id) === String(countryId));
      
      if (fallbackCountry) {
        console.log(`getCountryById: Found fallback country after exception: ${fallbackCountry.country_name}`);
        // Provide a fallback count for the fallback country
        const fallbackDoctorCount = FALLBACK_DOCTORS.filter(d => String(d.country_id) === String(countryId)).length;
        console.log(`getCountryById: Using fallback doctor count after exception: ${fallbackDoctorCount}`);
        return { ...fallbackCountry, doctor_count: fallbackDoctorCount } as any;
      }

      console.log(`getCountryById: No country found for ID ${countryId} after exception`);
      return null;
    }
  },
  ['country-by-id'],
  { revalidate: CACHE_TIMES.COUNTRIES }
);

// Cache implementation for getSpecialtiesByCountryId
export const getSpecialtiesByCountryId = unstable_cache(
  async (countryId: string | number): Promise<Specialty[]> => {
    console.log(`getSpecialtiesByCountryId: Fetching specialties for country ID: ${countryId}`);
    try {
      // 1. Find distinct specialty_ids for doctors in this country
      const { data: doctorSpecialties, error: doctorError } = await supabase
        .from('doctors')
        .select('specialty_id', { count: 'exact', head: false }) // Select distinct IDs
        .eq('country_id', countryId)
        .not('specialty_id', 'is', null); // Ensure specialty_id is not null
  
      if (doctorError) {
        console.error(`getSpecialtiesByCountryId: Error fetching distinct specialty IDs for country ${countryId}:`, doctorError);
        // Fallback: Return all specialties if we can't determine relevant ones
        return getSpecialties();
      }
  
      if (!doctorSpecialties || doctorSpecialties.length === 0) {
        console.log(`getSpecialtiesByCountryId: No doctors (or specialties) found for country ${countryId}. Returning all specialties.`);
        // Instead of returning an empty array, return all available specialties
        // This ensures we display all possible specialties even if no doctors are assigned yet
        return getSpecialties();
      }
  
      // Extract unique, non-null specialty IDs
      const uniqueSpecialtyIds = [...new Set(doctorSpecialties.map(doc => doc.specialty_id).filter(id => id !== null))];
  
      if (uniqueSpecialtyIds.length === 0) {
         console.log(`getSpecialtiesByCountryId: No unique specialty IDs found for country ${countryId}. Returning all specialties.`);
         // Again, return all specialties instead of empty array
         return getSpecialties();
      }
  
      console.log(`getSpecialtiesByCountryId: Found unique specialty IDs for country ${countryId}:`, uniqueSpecialtyIds);
  
      // 2. Fetch details for these specific specialties
      const { data: specialties, error: specialtiesError } = await supabase
        .from('specialties')
        .select('*')
        .in('specialty_id', uniqueSpecialtyIds)
        .order('specialty_name');
  
      if (specialtiesError) {
        console.error(`getSpecialtiesByCountryId: Error fetching specialty details for IDs [${uniqueSpecialtyIds.join(', ')}]:`, specialtiesError);
        // Fallback: Return static list filtered by known IDs if possible, or empty
        const fallbackFiltered = FALLBACK_SPECIALTIES.filter(spec => uniqueSpecialtyIds.includes(spec.specialty_id));
        console.log(`getSpecialtiesByCountryId: Using fallback specialties filtered by ID, count: ${fallbackFiltered.length}`);
        return fallbackFiltered as any[];
      }
  
      console.log(`getSpecialtiesByCountryId: Successfully fetched ${specialties?.length || 0} specialties for country ${countryId}`);
      return specialties || [];
  
    } catch (error) {
      console.error(`getSpecialtiesByCountryId: Exception fetching specialties for country ${countryId}:`, error);
      // Fallback: Return all specialties on general exception
      console.log(`getSpecialtiesByCountryId: Using all specialties fallback due to exception.`);
      return getSpecialties();
    }
  },
  ['specialties-by-country-id'],
  { revalidate: CACHE_TIMES.SPECIALTIES }
);

// Cache implementation for getSpecialties
export const getSpecialties = unstable_cache(
  async (): Promise<Specialty[]> => {
    console.log(`getSpecialties: Fetching all specialties`);
    try {
      // Directly use the imported supabase client (Anon Key)
      const { data, error } = await supabase
        .from("specialties")
        .select("*")
        .order("specialty_name");
      
      if (error) {
        console.error("Error fetching specialties:", error)
        return FALLBACK_SPECIALTIES as any[]
      }
      
      return data || (FALLBACK_SPECIALTIES as any[])
    } catch (error) {
      console.error("Error fetching specialties:", error)
      return FALLBACK_SPECIALTIES as any[]
    }
  },
  ['all-specialties'],
  { revalidate: CACHE_TIMES.SPECIALTIES }
);

// Cache implementation for getCountries
export const getCountries = unstable_cache(
  async (): Promise<Country[]> => {
    console.log("getCountries: Fetching countries from Supabase...");
    try {
      // Directly use the imported supabase client (Anon Key)
      const { data, error } = await supabase.from("countries").select("*").order("country_name"); 

      if (error) {
        console.error("getCountries: Error fetching countries from Supabase:", error);
        console.log("getCountries: Using fallback countries instead, count:", FALLBACK_COUNTRIES.length);
        return FALLBACK_COUNTRIES as any[];
      }

      if (!data || data.length === 0) {
        console.warn("getCountries: No countries returned from Supabase, using fallback data");
        console.log("getCountries: Fallback countries count:", FALLBACK_COUNTRIES.length);
        return FALLBACK_COUNTRIES as any[];
      }

      console.log(`getCountries: Successfully fetched ${data.length} countries from Supabase`);
      return data;
    } catch (error) {
      console.error("getCountries: Exception while fetching countries:", error);
      console.log("getCountries: Using fallback countries due to exception");
      return FALLBACK_COUNTRIES as any[];
    }
  },
  ['all-countries'],
  { revalidate: CACHE_TIMES.COUNTRIES }
);

// Hybrid data service
export async function getHospitalsByCountry(countryId: string): Promise<Hospital[]> {
  try {
    // Directly use the imported supabase client (Anon Key)
    const { data, error } = await supabase.from("hospitals") 
      .select("*")
      .eq("country_id", countryId)
      .order("hospital_name")

    if (error) {
      console.error("Error fetching hospitals:", error)
      return FALLBACK_HOSPITALS as any[]
    }

    return data || (FALLBACK_HOSPITALS as any[])
  } catch (error) {
    console.error("Error fetching hospitals:", error)
    return FALLBACK_HOSPITALS as any[]
  }
}

export async function getDoctorsByHospital(hospitalId: string): Promise<Doctor[]> {
  try {
    // Directly use the imported supabase client (Anon Key)
    const { data, error } = await supabase.from("doctors").select("*").eq("hospital_id", hospitalId) 

    if (error) {
      console.error("Error fetching doctors:", error)
      return FALLBACK_DOCTORS as any[]
    }

    return data || (FALLBACK_DOCTORS as any[])
  } catch (error) {
    console.error("Error fetching doctors:", error)
    return FALLBACK_DOCTORS as any[]
  }
}

export async function getSpecialtyById(specialtyId: string): Promise<Specialty | null> {
  try {
    // Directly use the imported supabase client (Anon Key)
    const { data, error } = await supabase.from("specialties").select("*").eq("specialty_id", specialtyId).single() 

    if (error) {
      console.error("Error fetching specialty:", error)
      return FALLBACK_SPECIALTIES.find((s) => s.specialty_id === Number(specialtyId)) || null
    }

    return data
  } catch (error) {
    console.error("Error fetching specialty:", error)
    return FALLBACK_SPECIALTIES.find((s) => s.specialty_id === Number(specialtyId)) || null
  }
}

export const getDoctorsByCountryAndSpecialty = unstable_cache(
  async (countryId: string, specialtyId: string): Promise<Doctor[]> => {
    console.log(`Fetching doctors for country ${countryId} and specialty ${specialtyId}`);
    try {
      // Simpler query to avoid join issues and ensure all fields are returned.
      const { data, error } = await supabase
        .from('doctors')
        .select('*')
        .eq('country_id', countryId)
        .eq('specialty_id', specialtyId);

      if (error) {
        console.error('Error fetching doctors by country and specialty:', error);
        return [];
      }
      
      console.log('Fetched doctors data:', data);

      return data || [];
    } catch (err) {
      console.error('Exception fetching doctors by country and specialty:', err);
      return [];
    }
  },
  ['doctors-by-country-specialty'],
  { revalidate: CACHE_TIMES.DOCTORS }
);

// Helper function to generate random form data
function generateRandomForm(): string {
  const results = ["W", "L", "D"];
  let form = "";
  
  // Generate 5 random results
  for (let i = 0; i < 5; i++) {
    const randomIndex = Math.floor(Math.random() * results.length);
    form += results[randomIndex];
  }
  
  return form;
}

export async function getHospitals({
  name,
  country,
  page = 1,
  pageSize = 10,
}: {
  name?: string
  country?: string
  page?: number
  pageSize?: number
} = {}): Promise<{ hospitals: Hospital[]; count: number; error: string | null }> {
  try {
    // Directly use the imported supabase client (Anon Key)
    let query = supabase.from("hospitals").select("*", { count: "exact" }) 

    if (name) {
      query = query.ilike("hospital_name", `%${name}%`)
    }

    if (country) {
      query = query.eq("country_id", country)
    }

    const from = (page - 1) * pageSize
    const to = from + pageSize - 1

    const { data, error, count } = await query.range(from, to).order("hospital_name")

    if (error) {
      console.error("Error fetching hospitals:", error)
      return { hospitals: [], count: 0, error: error.message }
    }

    return { hospitals: data || [], count: count || 0, error: null }
  } catch (error: any) {
    console.error("Error fetching hospitals:", error)
    return { hospitals: [], count: 0, error: error.message }
  }
}

export async function getSpecialtiesByCountry(countryId: string): Promise<Specialty[]> {
  try {
    // First, try to get doctors from this country to find what specialties are available
    // Directly use the imported supabase client (Anon Key)
    const { data: doctors, error: doctorsError } = await supabase.from("doctors") 
      .select("specialty_id")
      .eq("country_id", countryId)
      .order("specialty_id")
    
    if (doctorsError) {
      console.error("Error fetching doctors by country:", doctorsError)
      // Fall back to static data if there's an error
      return FALLBACK_SPECIALTIES as any[]
    }
    
    if (!doctors || doctors.length === 0) {
      console.log(`No doctors found for country ${countryId}, returning all specialties`)
      // If no doctors, return all specialties as fallback
      // Directly use the imported supabase client (Anon Key)
      const { data: allSpecialties, error: specialtiesError } = await supabase.from("specialties") 
        .select("*")
        .order("specialty_name")
      
      if (specialtiesError) {
        console.error("Error fetching all specialties:", specialtiesError)
        return FALLBACK_SPECIALTIES as any[]
      }
      
      return allSpecialties || (FALLBACK_SPECIALTIES as any[])
    }
    
    // Get unique specialty IDs from the doctors
    const specialtyIds = [...new Set(doctors.map((doc: any) => doc.specialty_id))]
    
    // Fetch the specialties by their IDs
    // Directly use the imported supabase client (Anon Key)
    const { data: specialties, error: specialtiesError } = await supabase.from("specialties") 
      .select("*")
      .in("specialty_id", specialtyIds)
      .order("specialty_name")
    
    if (specialtiesError) {
      console.error("Error fetching specialties by IDs:", specialtiesError)
      // Filter fallback specialties to match the specialty IDs we found
      // For fallback data, we can use the specialty field to match since specialty_id might not exist
      const filteredSpecialties = FALLBACK_SPECIALTIES.filter(spec => {
        const fallbackDoctors = FALLBACK_DOCTORS.filter(
          d => d.country_id === Number(countryId) && d.specialty === spec.specialty_name
        );
        return fallbackDoctors.length > 0;
      });
      return filteredSpecialties as any[]
    }
    
    return specialties || (FALLBACK_SPECIALTIES as any[])
  } catch (error) {
    console.error("Error in getSpecialtiesByCountry:", error)
    return FALLBACK_SPECIALTIES as any[]
  }
}

export async function getDoctorById(doctorId: string): Promise<Doctor | null> {
  try {
    // First get the doctor data
    // Directly use the imported supabase client (Anon Key)
    const { data: doctor, error: doctorError } = await supabase 
      .from("doctors")
      .select(
        `
        *,
        hospitals!doctors_hospital_id_fkey (
          hospital_name
        )
      `,
      )
      .eq("doctor_id", doctorId)
      .single()

    if (doctorError) {
      console.error("Error fetching doctor:", doctorError)
      return FALLBACK_DOCTORS.find((d) => d.doctor_id === Number(doctorId)) || null
    }
    
    // Get actual review count directly from reviews table
    if (doctor) {
      // Directly use the imported supabase client (Anon Key)
      const { count: reviewCount, error: reviewCountError } = await supabase 
        .from("reviews")
        .select("*", { count: "exact", head: true })
        .eq("doctor_id", doctorId);
        
      if (!reviewCountError && reviewCount !== null) {
        console.log(`Found ${reviewCount} reviews for doctor ${doctorId}`);
        // Update the doctor object with the accurate count
        doctor.review_count = reviewCount;
      } else {
        console.error("Error counting reviews:", reviewCountError);
      }
    }

    return doctor
  } catch (error) {
    console.error("Error fetching doctor:", error)
    return FALLBACK_DOCTORS.find((d) => d.doctor_id === Number(doctorId)) || null
  }
}

export async function getDoctorReviews(doctorId: string): Promise<Review[]> {
  try {
    const normalizedDoctorId = parseId(doctorId);
    console.log(`getDoctorReviews: Fetching reviews for doctor ID: ${normalizedDoctorId}`);
    
    // Query with join to get usernames from users table
    // Directly use the imported supabase client (Anon Key)
    const { data, error } = await supabase 
      .from("reviews")
      .select(`
        *,
        users (
          username,
          first_name,
          last_name
        )
      `)
      .eq("doctor_id", normalizedDoctorId)
      .order("review_date", { ascending: false });

    if (error) {
      console.error(`getDoctorReviews: Error fetching reviews:`, error);
      console.log(`getDoctorReviews: Using fallback reviews for doctor ${normalizedDoctorId}`);
      return FALLBACK_REVIEWS.filter(r => safeIdMatch(r.doctor_id, normalizedDoctorId)) as any[];
    }

    if (!data || data.length === 0) {
      console.log(`getDoctorReviews: No reviews found for doctor ${normalizedDoctorId}, using fallback`);
      return FALLBACK_REVIEWS.filter(r => safeIdMatch(r.doctor_id, normalizedDoctorId)) as any[];
    }

    console.log(`getDoctorReviews: Successfully fetched ${data.length} reviews from Supabase`);
    
    // Map data to include username in the expected format
    return data.map(review => ({
      ...review,
      user: review.users || null
    }));
  } catch (error) {
    console.error(`getDoctorReviews: Exception while fetching reviews:`, error);
    return FALLBACK_REVIEWS.filter(r => safeIdMatch(r.doctor_id, doctorId)) as any[];
  }
}

export async function getDoctorsBySpecialtyAndCountry(countryId: string, specialtyName: string): Promise<Doctor[]> {
  try {
    // Directly use the imported supabase client (Anon Key)
    const { data, error } = await supabase 
      .from("doctors")
      .select(
        `
        *,
        hospitals (
          hospital_name
        )
      `,
      )
      .eq("country_id", countryId)
      .eq("specialty", specialtyName)

    if (error) {
      console.error("Error fetching doctors:", error)
      return FALLBACK_DOCTORS as any[]
    }

    return data || (FALLBACK_DOCTORS as any[])
  } catch (error) {
    console.error("Error fetching doctors:", error)
    return FALLBACK_DOCTORS as any[]
  }
}

export async function getCountryByName(countryName: string): Promise<Country | null> {
  try {
    // Directly use the imported supabase client (Anon Key)
    const { data, error } = await supabase.from("countries").select("*").ilike("country_name", countryName).single() 

    if (error) {
      console.error("Error fetching country:", error)
      return FALLBACK_COUNTRIES.find((c) => c.country_name === countryName) || null
    }

    return data
  } catch (error) {
    console.error("Error fetching country:", error)
    return FALLBACK_COUNTRIES.find((c) => c.country_name === countryName) || null
  }
}

export async function getFeaturedDoctors(limit = 6): Promise<Doctor[]> {
  try {
    console.log(`Fetching featured doctors with limit: ${limit} from database...`);
    // Directly use the imported supabase client (Anon Key)
    const { data, error } = await supabase 
      .from("doctors")
      .select(
        `
        *,
        hospitals (
          hospital_name
        )
      `,
      )
      .not('community_rating', 'is', null)  // Filter out null community_ratings
      .order("community_rating", { ascending: false })
      .limit(limit);

    if (error) {
      console.error("Error fetching featured doctors from database:", error);
      throw error; // Let the calling function handle this error
    }

    if (!data || data.length === 0) {
      console.log("No featured doctors found in database");
      return []; // Return empty array if no data
    }

    console.log(`Successfully fetched ${data.length} featured doctors from database`);
    console.log("Database featured doctors:", data.map(d => ({
      name: d.fullname,
      rating: d.community_rating
    })));
    
    return data;
  } catch (error) {
    console.error("Exception in getFeaturedDoctors:", error);
    throw error; // Rethrow so the calling function can handle it
  }
}

export async function getTopDoctors(limit = 10): Promise<Doctor[]> {
  try {
    console.log(`Fetching top doctors with limit: ${limit} from database...`);
    // Directly use the imported supabase client (Anon Key)
    const { data, error } = await supabase 
      .from("doctors")
      .select("*")
      .not('community_rating', 'is', null)  // Filter out null community_ratings
      .order("community_rating", { ascending: false })
      .limit(limit);

    if (error) {
      console.error("Error fetching top doctors from database:", error);
      throw error; // Let the calling function handle this error
    }

    if (!data || data.length === 0) {
      console.log("No doctors found in database");
      return []; // Return empty array if no data
    }

    console.log(`Successfully fetched ${data.length} doctors from database`);
    console.log("Database doctors:", data.map(d => ({
      name: d.fullname,
      rating: d.community_rating
    })));
    
    return data;
  } catch (error) {
    console.error("Exception in getTopDoctors:", error);
    throw error; // Rethrow so the calling function can handle it
  }
}

/**
 * Generate a dynamic fallback review
 */
function generateDynamicFallbackReview(reviewId: string, doctorId?: string): Review {
  const seedValue = parseInt(reviewId) % 5; // Create a seed value from the review ID
  const docId = doctorId || (1000 + (parseInt(reviewId) % 10)).toString();
  const baseRating = 3 + (seedValue / 10); // Base rating between 3.0 and 3.4
  const recommendationValue = 3 + (seedValue % 3); // Value between 3-5
  
  return {
    review_id: reviewId,
    user_id: "999",
    doctor_id: docId,
    clinical_competence: 3 + (seedValue % 3),
    communication_stats: 4,
    empathy_compassion: 3 + (seedValue % 3),
    time_management: 3 + ((seedValue + 1) % 3),
    follow_up_care: 3 + ((seedValue + 2) % 3),
    overall_satisfaction: 4,
    additional_comments: `This is an automatically generated review for demonstration purposes. Review ID: ${reviewId}`,
    recommendation_rating: recommendationValue, // For backward compatibility
    Recommendation: recommendationValue, // Primary field in database
    rating: baseRating + (seedValue / 10),
    review_date: new Date().toISOString().split('T')[0],
    user: {
      username: "Anonymous User"
    }
  };
}

// Helper function to format review data consistently
function formatReviewData(reviewData: any): Review {
  // Check if we have either version of the recommendation column
  const recommendationValue = 
    reviewData.Recommendation !== undefined ? Number(reviewData.Recommendation) : 
    reviewData.recommendation_rating !== undefined ? Number(reviewData.recommendation_rating) : 
    3; // Default value
    
  return {
    review_id: String(reviewData.review_id),
    user_id: String(reviewData.user_id || 999),
    doctor_id: String(reviewData.doctor_id || 1000),
    clinical_competence: Number(reviewData.clinical_competence) || 0,
    communication_stats: Number(reviewData.communication_stats) || 0,
    empathy_compassion: Number(reviewData.empathy_compassion) || 0,
    time_management: Number(reviewData.time_management) || 0,
    follow_up_care: Number(reviewData.follow_up_care) || 0,
    overall_satisfaction: Number(reviewData.overall_satisfaction) || 0,
    additional_comments: reviewData.additional_comments,
    recommendation_rating: reviewData.recommendation_rating !== undefined ? Number(reviewData.recommendation_rating) : recommendationValue,
    Recommendation: recommendationValue,
    rating: Number(reviewData.rating) || 0,
    review_date: reviewData.review_date || new Date().toISOString().split('T')[0],
    user: reviewData.users || reviewData.user || { username: "Anonymous" }
  };
}

/**
 * Get a specific review by ID
 */
export async function getReviewById(reviewId: string): Promise<Review | null> {
  try {
    console.log(`[getReviewById] Starting fetch for review ID: ${reviewId}`);
    
    // First check if we have this review in fallback data
    const numericReviewId = parseInt(reviewId, 10);
    const fallbackReview = FALLBACK_REVIEWS.find(r => 
      String(r.review_id) === String(reviewId) || 
      (numericReviewId && Number(r.review_id) === numericReviewId)
    );
    
    if (fallbackReview) {
      console.log(`[getReviewById] Found review in static fallback data for ID ${reviewId}`);
      return formatReviewData(fallbackReview);
    }
    
    // Get the database client - use the imported singleton
    const client = supabase; 
    const env = typeof window !== 'undefined' ? "browser" : "server"; // Re-check env if needed for logging
    console.log(`[getReviewById] Using Supabase client, environment: ${env}`);
    
    // Diagnose the table structure
    try {
      console.log(`[getReviewById] Testing database connection...`);
      const { data: testData, error: testError } = await client
        .from('reviews')
        .select('review_id')
        .limit(1);
      
      if (testError) {
        console.error(`[getReviewById] Database test failed:`, JSON.stringify(testError));
        return generateDynamicFallbackReview(reviewId);
      }
      
      if (testData && testData.length > 0) {
        console.log(`[getReviewById] Database test successful. First review ID:`, testData[0].review_id);
        console.log(`[getReviewById] First review ID type:`, typeof testData[0].review_id);
      } else {
        console.log(`[getReviewById] Database connection OK but no reviews found`);
      }
    } catch (connError) {
      console.error(`[getReviewById] Database connection error:`, connError);
      return generateDynamicFallbackReview(reviewId);
    }
    
    // Try different query approaches
    const approaches = [
      // 1. Try string ID
      async () => {
        console.log(`[getReviewById] Approach 1: Using string ID "${reviewId}"`);
        const { data, error } = await supabase // Use singleton
          .from('reviews')
          .select('*')
          .eq('review_id', reviewId)
          .single();
          
        if (error) {
          console.log(`[getReviewById] Approach 1 error:`, JSON.stringify(error));
          return null;
        }
        
        if (data) {
          console.log(`[getReviewById] Approach 1 successful`);
          return formatReviewData(data);
        }
        
        return null;
      },
      
      // 2. Try numeric ID
      async () => {
        if (isNaN(numericReviewId)) return null;
        
        console.log(`[getReviewById] Approach 2: Using numeric ID ${numericReviewId}`);
        const { data, error } = await supabase // Use singleton
          .from('reviews')
          .select('*')
          .eq('review_id', numericReviewId)
          .single();
          
        if (error) {
          console.log(`[getReviewById] Approach 2 error:`, JSON.stringify(error));
          return null;
        }
        
        if (data) {
          console.log(`[getReviewById] Approach 2 successful`);
          return formatReviewData(data);
        }
        
        return null;
      },
      
      // 3. Try using a list query and find manually
      async () => {
        console.log(`[getReviewById] Approach 3: Getting all reviews and filtering`);
        const { data, error } = await supabase // Use singleton
          .from('reviews')
          .select('*')
          .limit(100);
          
        if (error) {
          console.log(`[getReviewById] Approach 3 error:`, JSON.stringify(error));
          return null;
        }
        
        if (!data || data.length === 0) {
          console.log(`[getReviewById] Approach 3: No reviews found`);
          return null;
        }
        
        console.log(`[getReviewById] Approach 3: Retrieved ${data.length} reviews`);
        
        // Try to find the matching review
        const matchingReview = data.find(r => 
          String(r.review_id) === String(reviewId) || 
          r.review_id === numericReviewId
        );
        
        if (matchingReview) {
          console.log(`[getReviewById] Approach 3: Found matching review`);
          return formatReviewData(matchingReview);
        }
        
        console.log(`[getReviewById] Approach 3: No matching review found. Sample IDs:`, 
          data.slice(0, 5).map(r => r.review_id));
        return null;
      },
      
      // 4. Try using text comparison
      async () => {
        console.log(`[getReviewById] Approach 4: Using text comparison`);
        const { data, error } = await supabase // Use singleton
          .from('reviews')
          .select('*')
          .textSearch('review_id', reviewId)
          .limit(10);
          
        if (error) {
          console.log(`[getReviewById] Approach 4 error:`, JSON.stringify(error));
          return null;
        }
        
        if (data && data.length > 0) {
          console.log(`[getReviewById] Approach 4: Found ${data.length} potential matches`);
          const exactMatch = data.find(r => String(r.review_id) === String(reviewId));
          
          if (exactMatch) {
            console.log(`[getReviewById] Approach 4: Found exact match`);
            return formatReviewData(exactMatch);
          }
        }
        
        return null;
      }
    ];
    
    // Try each approach in sequence
    for (const approach of approaches) {
      try {
        const result = await approach();
        if (result) return result;
      } catch (err) {
        console.error(`[getReviewById] Approach error:`, err);
      }
    }
    
    // If all approaches failed, generate a dynamic fallback
    console.log(`[getReviewById] All database approaches failed, generating fallback review`);
    return generateDynamicFallbackReview(reviewId);
    
  } catch (error) {
    console.error(`[getReviewById] Unexpected error:`, error);
    return generateDynamicFallbackReview(reviewId);
  }
}

export async function getAllDoctors(): Promise<Doctor[]> {
  try {
    console.log("Fetching all doctors from database...");
    // Directly use the imported supabase client (Anon Key)
    // Order by community_rating descending (nulls last), then by doctor_id
    const { data, error } = await supabase
      .from("doctors")
      .select("*")
      .order("community_rating", { ascending: false, nullsFirst: false })
      .order("doctor_id", { ascending: true });

    if (error) {
      console.error("Error fetching all doctors from database:", error);
      return FALLBACK_DOCTORS as any[];
    }

    if (!data || data.length === 0) {
      console.log("No doctors found in database, using fallback data");
      return FALLBACK_DOCTORS as any[];
    }

    console.log(`Successfully fetched ${data.length} doctors from database`);

    // Debug: Log first few doctors to see structure and ratings
    if (data.length > 0) {
      console.log("DEBUG getAllDoctors - Sample doctors with ratings:");
      data.slice(0, 5).forEach((doctor, index) => {
        console.log(`${index + 1}. ${doctor.fullname}: community_rating=${doctor.community_rating}, rating=${doctor.rating}`);
      });
    }

    return data;
  } catch (error) {
    console.error("Exception in getAllDoctors:", error);
    return FALLBACK_DOCTORS as any[];
  }
}
