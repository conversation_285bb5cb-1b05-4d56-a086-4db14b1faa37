// Map of specialty names to their corresponding icon paths
export const SPECIALTY_ICONS = {
  Anesthesia: "/icons/medical/anesthesiology.svg",
  "Audiology / Speech Therapy": "/icons/medical/audiology.svg",
  Cardiology: "/icons/medical/cardiology.svg",
  Dental: "/icons/medical/dental.svg",
  Dermatology: "/icons/medical/dermatology.svg",
  "Emergency Medicine": "/icons/medical/emergency.svg",
  Endocrinology: "/icons/medical/endocrinology.svg",
  ENT: "/icons/medical/ent.svg",
  "Family Medicine": "/icons/medical/family-medicine.svg",
  Gastroenterology: "/icons/medical/gastroenterology.svg",
  "General Practice": "/icons/medical/general-practice.svg",
  ICU: "/icons/medical/icu.svg",
  "Internal Medicine": "/icons/medical/internal-medicine.svg",
  "IVF Centre": "/icons/medical/ivf.svg",
  "Medical Genetics": "/icons/medical/genetics.svg",
  "Mental Health": "/icons/medical/mental-health.svg",
  Nephrology: "/icons/medical/nephrology.svg",
  Neonatology: "/icons/medical/neonatology.svg",
  Neurology: "/icons/medical/neurology.svg",
  Neurosurgery: "/icons/medical/neurosurgery.svg",
  "Obstetrics and Gynecology": "/icons/medical/obstetrics.svg",
  Oncology: "/icons/medical/oncology.svg",
  Ophthalmology: "/icons/medical/ophthalmology.svg",
  Orthopedics: "/icons/medical/orthopedics.svg",
  Osteopathy: "/icons/medical/osteopathy.svg",
  Pediatrics: "/icons/medical/pediatrics.svg",
  Physiotherapy: "/icons/medical/physiotherapy.svg",
  Rheumatology: "/icons/medical/rheumatology.svg",
  Surgery: "/icons/medical/surgery.svg",
  Urology: "/icons/medical/urology.svg",
} as const

// Medical-themed divider SVGs for section breaks
export const MEDICAL_DIVIDERS = {
  dna: `<svg viewBox="0 0 1200 100" xmlns="http://www.w3.org/2000/svg" fill="currentColor">
    <path d="M600 70c-320 0-580-45-580-45s130-25 580-25 580 25 580 25-260 45-580 45z"/>
    <circle cx="150" cy="35" r="5"/>
    <circle cx="450" cy="35" r="5"/>
    <circle cx="750" cy="35" r="5"/>
    <circle cx="1050" cy="35" r="5"/>
  </svg>`,
  heartbeat: `<svg viewBox="0 0 1200 100" xmlns="http://www.w3.org/2000/svg" fill="currentColor">
    <path d="M0 50h300l50-30 100 60 50-30 100 60 50-30 100 60 50-30 100 60 50-30 250-30"/>
  </svg>`,
  stethoscope: `<svg viewBox="0 0 1200 100" xmlns="http://www.w3.org/2000/svg" fill="currentColor">
    <path d="M600 20c200 0 300 60 300 60s-100 60-300 60-300-60-300-60 100-60 300-60z"/>
    <circle cx="450" cy="50" r="15"/>
    <circle cx="750" cy="50" r="15"/>
  </svg>`,
}

