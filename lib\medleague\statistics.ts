import { createClient } from "@supabase/supabase-js"
import { unstable_cache } from 'next/cache';

// Type definitions for doctor data
interface DoctorRating {
  community_rating: number | null
}

interface DoctorMatches {
  wins: number | null
  losses: number | null
  draws: number | null
}

// Create Supabase client with proper error handling
const getSupabase = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  // Use the anon key for client-side and service role key for server-side
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials')
    throw new Error('Missing Supabase credentials')
  }
  
  return createClient(supabaseUrl, supabaseKey)
}

// Helper function to generate a random score between 70-100
const generateRandomScore = (id: string): number => {
  const specialtyIdNum = parseInt(id, 10) || 0
  return ((specialtyIdNum * 17) % 30) + 70 // Score between 70-100
}

// Helper function to generate a random doctor count between 20-70
const generateRandomDoctorCount = (id: string): number => {
  const specialtyIdNum = parseInt(id, 10) || 0
  return ((specialtyIdNum * 13) % 50) + 20 // Between 20-70 doctors
}

// Cache times in seconds
const HOUR_IN_SECONDS = 60 * 60;
const CACHE_TIMES = {
  DOCTOR_COUNT: 3 * HOUR_IN_SECONDS, // 3 hours
  RANKING_SCORE: 3 * HOUR_IN_SECONDS, // 3 hours
}

// Get the number of doctors for a specific country
export async function getCountryDoctorsCount(countryId: string): Promise<number> {
  try {
    if (typeof window !== 'undefined') {
      // We're on the client-side, use fallback data for now
      return 450 // Fallback number for client-side
    }
    
    const supabase = getSupabase()
    
    const { count, error } = await supabase
      .from('doctors')
      .select('*', { count: 'exact', head: true })
      .eq('country_id', countryId)
    
    if (error) {
      console.error('Error fetching country doctors count:', error)
      return 450 // Fallback to a reasonable number
    }
    
    return count || 0
  } catch (err) {
    console.error('Exception fetching country doctors count:', err)
    return 450 // Fallback to a reasonable number
  }
}

// Get the number of active doctors for a specialty within a country
export async function getActiveDoctorsCount(countryId: string, specialtyId: string): Promise<number> {
  try {
    if (typeof window !== 'undefined') {
      // We're on the client-side, use fallback data for now
      return 0; // Return 0 as we assume no reviews initially client-side
    }
    
    const supabase = getSupabase()
    
    // An active doctor is one with at least one review.
    const { count, error } = await supabase
      .from('doctors')
      .select('*', { count: 'exact', head: true })
      .eq('country_id', countryId)
      .eq('specialty_id', specialtyId)
      .gt('review_count', 0)
    
    if (error) {
      console.error('Error fetching active doctors count:', error)
      return 0; // Fallback to 0 on error
    }
    
    return count || 0
  } catch (err) {
    console.error('Exception fetching active doctors count:', err)
    return 0; // Fallback to 0 on exception
  }
}

// Get the number of doctors for a specific specialty and country - Direct DB connection
export const getSpecialtyDoctorsCount = unstable_cache(
  async (countryId: string, specialtyId: string): Promise<number> => {
    console.log(`Getting doctor count for country: ${countryId}, specialty: ${specialtyId}`);
    try {
      const supabase = getSupabase()
      
      const { count, error } = await supabase
        .from('doctors')
        .select('*', { count: 'exact', head: true })
        .eq('country_id', countryId)
        .eq('specialty_id', specialtyId)
      
      if (error) {
        console.error('Error fetching specialty doctors count:', error)
        // Fallback to a generated number only if absolutely necessary
        const specialtyIdNum = parseInt(specialtyId, 10) || 0
        return ((specialtyIdNum * 13) % 50) + 20 // Between 20-70 doctors as fallback
      }
      
      return count || 0
    } catch (err) {
      console.error('Exception fetching specialty doctors count:', err)
      // Fallback to a generated number only if absolutely necessary
      const specialtyIdNum = parseInt(specialtyId, 10) || 0
      return ((specialtyIdNum * 13) % 50) + 20 // Between 20-70 doctors as fallback
    }
  },
  ['specialty-doctors-count'],
  { revalidate: CACHE_TIMES.DOCTOR_COUNT }
)

// Calculate the total number of matches for doctors in a specialty and country
export async function getTotalMatchesCount(countryId: string, specialtyId: string): Promise<number> {
  try {
    if (typeof window !== 'undefined') {
      // We're on the client-side, use fallback data for now
      const doctorCount = generateRandomDoctorCount(specialtyId)
      return doctorCount * 5 // Average 5 matches per doctor as fallback
    }
    
    const supabase = getSupabase()
    
    const { data, error } = await supabase
      .from('doctors')
      .select('wins, losses, draws')
      .eq('country_id', countryId)
      .eq('specialty_id', specialtyId)
    
    if (error) {
      console.error('Error fetching matches count:', error)
      const doctorCount = generateRandomDoctorCount(specialtyId)
      return doctorCount * 5 // Fallback
    }
    
    // Sum all wins, losses, and draws to get total matches
    const totalMatches = data.reduce((sum: number, doctor: DoctorMatches) => {
      return sum + (doctor.wins || 0) + (doctor.losses || 0) + (doctor.draws || 0)
    }, 0)
    
    return totalMatches
  } catch (err) {
    console.error('Exception fetching total matches count:', err)
    const doctorCount = generateRandomDoctorCount(specialtyId)
    return doctorCount * 5 // Fallback
  }
}

/**
 * Retrieves the ranking score (as a percentage) for a specialty within a country
 * Ranking is based on average doctor community_ratings
 */
export const getSpecialtyRankingScore = unstable_cache(
  async (countryId: string, specialtyId: string): Promise<number> => {
    try {
      const supabase = getSupabase();
      
      const { data, error } = await supabase
        .from('doctors')
        .select('community_rating')
        .eq('country_id', countryId)
        .eq('specialty_id', specialtyId)
        .not('community_rating', 'is', null);
      
      if (error) {
        console.error(`Error fetching community_ratings for specialty ${specialtyId}:`, error);
        return 0; // Return 0 on error
      }

      if (!data || data.length === 0) {
        return 0; // No doctors with community_ratings, so score is 0
      }

      // Calculate average community_rating
      const totalRating = data.reduce((sum, doctor) => sum + (doctor.community_rating || 0), 0);
      const avgRating = totalRating / data.length;
      
      // Convert to percentage (assuming rating is 0-5, multiply by 20 to get percentage)
      // and ensure it's a whole number between 0 and 100.
      const percentScore = Math.round(Math.max(0, Math.min(avgRating, 5)) * 20);
      
      return percentScore;
    } catch (error) {
      console.error(`Exception in getSpecialtyRankingScore for specialty ${specialtyId}:`, error);
      return 0; // Final fallback
    }
  },
  ['specialty-ranking-score'],
  { revalidate: CACHE_TIMES.RANKING_SCORE }
); 