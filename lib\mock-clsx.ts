// This file provides a simplified mock implementation of the clsx library

/**
 * Mock implementation of clsx
 * Joins classNames together, handling various input types like:
 * - Strings
 * - Objects (key as className, value as boolean)
 * - Arrays
 * - null/undefined/false (ignored)
 */
export function clsx(...args: any[]): string {
  const classes: string[] = []

  for (const arg of args) {
    if (!arg) continue

    const argType = typeof arg

    if (argType === "string" || argType === "number") {
      classes.push(arg as string)
    } else if (Array.isArray(arg)) {
      const inner = clsx(...arg)
      if (inner) {
        classes.push(inner)
      }
    } else if (argType === "object") {
      for (const key in arg) {
        if (Object.prototype.hasOwnProperty.call(arg, key) && arg[key]) {
          classes.push(key)
        }
      }
    }
  }

  return classes.join(" ")
}

// Also export as default for compatibility
export default clsx

// Create a ClassValue type to mimic the original library
export type ClassValue = string | number | ClassArray | ClassDictionary | undefined | null | false
export type ClassArray = ClassValue[]
export type ClassDictionary = Record<string, any>

