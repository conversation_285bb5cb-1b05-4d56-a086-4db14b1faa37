// This file provides enhanced mock implementations of country flag components
// to replace the country-flag-icons/react/3x2 package

import type React from "react"

// A more detailed flag component that displays a real-looking flag
const FlagBase: React.FC<{ 
  colors: string[]; 
  symbol?: string;
  pattern?: string;
}> = ({ colors, symbol, pattern }) => {
  return (
    <div
      style={{
        width: "100%",
        height: "100%",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        position: "relative",
        overflow: "hidden",
        borderRadius: "2px",
      }}
    >
      {pattern === "horizontal" && colors.map((color, index) => (
        <div 
          key={index}
          style={{
            position: "absolute",
            left: 0,
            width: "100%",
            height: `${100 / colors.length}%`,
            top: `${(index * 100) / colors.length}%`,
            backgroundColor: color,
          }}
        />
      ))}
      
      {pattern === "vertical" && colors.map((color, index) => (
        <div 
          key={index}
          style={{
            position: "absolute",
            top: 0,
            height: "100%",
            width: `${100 / colors.length}%`,
            left: `${(index * 100) / colors.length}%`,
            backgroundColor: color,
          }}
        />
      ))}
      
      {!pattern && (
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            backgroundColor: colors[0],
          }}
        />
      )}
      
      {symbol && (
        <div
          style={{
            position: "relative",
            zIndex: 1,
            color: "#fff",
            fontSize: "10px",
            fontWeight: "bold",
            textAlign: "center",
          }}
        >
          {symbol}
        </div>
      )}
    </div>
  )
}

// Bahrain - red and white with serrated line
export const BH: React.FC = () => (
  <div style={{ width: "100%", height: "100%", position: "relative", backgroundColor: "#ce1126", borderRadius: "2px" }}>
    <div style={{ position: "absolute", left: "22%", width: "78%", height: "100%", display: "flex", flexDirection: "column" }}>
      {Array.from({ length: 5 }).map((_, index) => (
        <div key={index} style={{ height: "20%", backgroundColor: "white" }} />
      ))}
    </div>
  </div>
)

// Kuwait - horizontal stripes (green, white, red) with black trapezoid
export const KW: React.FC = () => (
  <FlagBase 
    colors={["#007a3d", "#ffffff", "#ce1126"]} 
    pattern="horizontal" 
  />
)

// Oman - red with green and white stripes on the left
export const OM: React.FC = () => (
  <div style={{ width: "100%", height: "100%", position: "relative", backgroundColor: "#db161b", borderRadius: "2px" }}>
    <div style={{ position: "absolute", left: 0, top: 0, width: "25%", height: "100%", backgroundColor: "#ffffff" }}>
      <div style={{ position: "absolute", left: 0, top: 0, width: "100%", height: "33.3%", backgroundColor: "#008000" }}></div>
    </div>
  </div>
)

// Qatar - maroon with white serrated side
export const QA: React.FC = () => (
  <div style={{ width: "100%", height: "100%", position: "relative", backgroundColor: "#8d1b3d", borderRadius: "2px" }}>
    <div style={{ position: "absolute", left: 0, width: "30%", height: "100%", backgroundColor: "white" }}>
      <div style={{ position: "absolute", right: 0, top: 0, width: "25%", height: "100%", display: "flex", flexDirection: "column" }}>
        {Array.from({ length: 9 }).map((_, index) => (
          <div key={index} style={{ height: "11.1%", backgroundColor: "#8d1b3d" }} />
        ))}
      </div>
    </div>
  </div>
)

// Saudi Arabia - green with Arabic script and sword
export const SA: React.FC = () => (
  <FlagBase colors={["#006c35"]} symbol="شهادة" />
)

// UAE - horizontal stripes (green, white, black) with red vertical band
export const AE: React.FC = () => (
  <div style={{ width: "100%", height: "100%", position: "relative", borderRadius: "2px" }}>
    <div style={{ position: "absolute", top: 0, height: "33.3%", width: "100%", backgroundColor: "#00732f" }}></div>
    <div style={{ position: "absolute", top: "33.3%", height: "33.3%", width: "100%", backgroundColor: "#ffffff" }}></div>
    <div style={{ position: "absolute", top: "66.6%", height: "33.3%", width: "100%", backgroundColor: "#000000" }}></div>
    <div style={{ position: "absolute", top: 0, height: "100%", width: "25%", left: 0, backgroundColor: "#ff0000" }}></div>
  </div>
)

