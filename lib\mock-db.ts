interface Doctor {
  doctor_id: number
  Fullname: string
  facility: string
  medical_title: string
  specialty: string
  subspecialty: string
  educational_background: string
  experience: number
  hospital_id: number
  image_path: string
  rating: number
  review_count: number
}

export interface Hospital {
  hospital_id: number
  hospital_name: string
  country_id: number
  city: string
  address: string
  email_info: string
  telephone_info: string
  rating: number
  review_count: number
  country: string
}

const mockDoctors: Doctor[] = [
  {
    doctor_id: 1,
    Fullname: "Dr. <PERSON>",
    facility: "Central Hospital",
    medical_title: "Consultant Cardiologist",
    specialty: "Cardiology",
    subspecialty: "Interventional Cardiology",
    educational_background: "MD from Harvard Medical School",
    experience: 15,
    hospital_id: 1,
    image_path: "/images/doctors/ahmed-al-mansoori.jpg",
    rating: 4.8,
    review_count: 50,
  },
  {
    doctor_id: 2,
    Fullname: "Dr. <PERSON><PERSON>",
    facility: "Kuwait Medical Center",
    medical_title: "Senior Neurologist",
    specialty: "Neurology",
    subspecialty: "Stroke Medicine",
    educational_background: "PhD from Johns Hopkins University",
    experience: 12,
    hospital_id: 2,
    image_path: "/images/doctors/fatima-al-kuwari.jpg",
    rating: 4.6,
    review_count: 40,
  },
  {
    doctor_id: 3,
    Fullname: "Dr. <PERSON>",
    facility: "Oman Royal Hospital",
    medical_title: "Chief Pediatrician",
    specialty: "Pediatrics",
    subspecialty: "Neonatology",
    educational_background: "Fellowship from Great Ormond Street Hospital",
    experience: 20,
    hospital_id: 3,
    image_path: "/images/doctors/khalid-al-balushi.jpg",
    rating: 4.9,
    review_count: 60,
  },
]

const mockHospitals: Hospital[] = [
  {
    hospital_id: 1,
    hospital_name: "Central Hospital",
    country_id: 1,
    city: "Manama",
    address: "123 Main St",
    email_info: "<EMAIL>",
    telephone_info: "+973 1234 5678",
    rating: 4.5,
    review_count: 100,
    country: "Bahrain",
  },
  {
    hospital_id: 2,
    hospital_name: "Kuwait Medical Center",
    country_id: 2,
    city: "Kuwait City",
    address: "456 Health Ave",
    email_info: "<EMAIL>",
    telephone_info: "+965 2345 6789",
    rating: 4.2,
    review_count: 80,
    country: "Kuwait",
  },
  {
    hospital_id: 3,
    hospital_name: "Oman Royal Hospital",
    country_id: 3,
    city: "Muscat",
    address: "789 Sultan Qaboos St",
    email_info: "<EMAIL>",
    telephone_info: "+968 3456 7890",
    rating: 4.7,
    review_count: 120,
    country: "Oman",
  },
]

export function getFeaturedDoctors(): Doctor[] {
  return mockDoctors.sort((a, b) => b.rating - a.rating).slice(0, 3)
}

export function getDoctorById(id: number): Doctor | undefined {
  return mockDoctors.find((doctor) => doctor.doctor_id === id)
}

export function getHospitalById(id: number): Hospital | undefined {
  return mockHospitals.find((hospital) => hospital.hospital_id === id)
}

export function getCountries(): string[] {
  return ["Bahrain", "Kuwait", "Oman", "Qatar", "Saudi Arabia", "UAE"]
}

interface GetHospitalsParams {
  name?: string
  country?: string
  page?: number
  pageSize?: number
}

export function getHospitals(params: GetHospitalsParams): Hospital[] {
  let filteredHospitals = mockHospitals

  if (params.name) {
    filteredHospitals = filteredHospitals.filter((hospital) =>
      hospital.hospital_name.toLowerCase().includes(params.name!.toLowerCase()),
    )
  }

  if (params.country && params.country !== "all") {
    filteredHospitals = filteredHospitals.filter((hospital) => hospital.country === params.country)
  }

  const startIndex = ((params.page || 1) - 1) * (params.pageSize || 10)
  const endIndex = startIndex + (params.pageSize || 10)

  return filteredHospitals.slice(startIndex, endIndex)
}

