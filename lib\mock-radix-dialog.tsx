"use client"

import * as React from "react"
import { X } from "lucide-react"

// Mock implementation of Radix UI Dialog components

// Root Dialog component
const Dialog = ({
  children,
  open,
  onOpenChange,
  modal = true,
}: {
  children: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
  modal?: boolean
}) => {
  const [isOpen, setIsOpen] = React.useState(open || false)

  // Make sure the internal open state always matches the prop
  React.useEffect(() => {
    if (open !== undefined) {
      console.log("Dialog effect: Setting isOpen to", open);
      setIsOpen(open)
    }
  }, [open])

  const handleOpenChange = (newOpen: boolean) => {
    console.log("Dialog handleOpenChange called with", newOpen);
    setIsOpen(newOpen)
    if (onOpenChange) {
      console.log("Calling parent onOpenChange with", newOpen);
      onOpenChange(newOpen)
    }
  }

  // Create a context to share the open state and handleOpenChange function
  const context = React.useMemo(
    () => ({
      open: isOpen,
      onOpenChange: handleOpenChange,
    }),
    [isOpen, handleOpenChange]
  )

  return <DialogContext.Provider value={context}>{children}</DialogContext.Provider>
}

// Create a context for the Dialog state
const DialogContext = React.createContext<{
  open: boolean
  onOpenChange: (open: boolean) => void
}>({
  open: false,
  onOpenChange: () => {},
})

// Trigger component
const DialogTrigger = ({
  children,
  asChild,
}: {
  children: React.ReactNode
  asChild?: boolean
}) => {
  const { onOpenChange } = React.useContext(DialogContext)

  const handleClick = () => {
    onOpenChange(true)
  }

  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children, {
      onClick: (e: React.MouseEvent) => {
        handleClick()
        children.props.onClick?.(e)
      },
    })
  }

  return (
    <button type="button" onClick={handleClick}>
      {children}
    </button>
  )
}

// Portal component (simplified)
const DialogPortal = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>
}

// Content component
const DialogContent = ({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  children: React.ReactNode
  forceMount?: boolean
}) => {
  const { open, onOpenChange } = React.useContext(DialogContext)

  if (!open) {
    return null
  }

  // Simple implementation that renders a modal-like UI
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div 
        className="fixed inset-0 bg-background/50" 
        onClick={() => onOpenChange(false)}
      />
      <div className={`relative bg-background p-6 shadow-lg rounded-lg ${className || ""}`} {...props}>
        {children}
      </div>
    </div>
  )
}

// Close component - simplified implementation
const DialogClose = ({
  children,
  className,
  ...props
}: React.ButtonHTMLAttributes<HTMLButtonElement> & {
  children?: React.ReactNode
}) => {
  const { onOpenChange } = React.useContext(DialogContext)
  
  return (
    <button 
      type="button" 
      className={className} 
      onClick={() => onOpenChange(false)}
      {...props}
    >
      {children || <X className="h-4 w-4" />}
    </button>
  )
}

// Title component
const DialogTitle = ({ children, className, ...props }: React.HTMLAttributes<HTMLHeadingElement>) => {
  return (
    <h2 className={`text-lg font-semibold ${className || ""}`} {...props}>
      {children}
    </h2>
  )
}

// Description component
const DialogDescription = ({ children, className, ...props }: React.HTMLAttributes<HTMLParagraphElement>) => {
  return (
    <p className={`text-sm text-muted-green ${className || ""}`} {...props}>
      {children}
    </p>
  )
}

// Header component
const DialogHeader = ({ children, className, ...props }: React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <div className={`space-y-1.5 ${className || ""}`} {...props}>
      {children}
    </div>
  )
}

// Footer component
const DialogFooter = ({ children, className, ...props }: React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <div className={`flex justify-end gap-2 ${className || ""}`} {...props}>
      {children}
    </div>
  )
}

// Export all components
export {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogClose,
  DialogTitle,
  DialogDescription,
  DialogHeader,
  DialogFooter,
  DialogPortal,
}