// This is a mock implementation of the Supabase client for the v0 environment
// In your actual project, you'll use the real Supabase client

import { COUNTRIES, SPECIALTIES, DOCTORS, HOSPITALS } from "./static-data"

// Types to match Supabase client API
type SupabaseQueryBuilder<T> = {
  select: (columns: string) => SupabaseQueryBuilder<T>
  eq: (column: string, value: any) => SupabaseQueryBuilder<T>
  neq: (column: string, value: any) => SupabaseQueryBuilder<T>
  gt: (column: string, value: any) => SupabaseQueryBuilder<T>
  lt: (column: string, value: any) => SupabaseQueryBuilder<T>
  gte: (column: string, value: any) => SupabaseQueryBuilder<T>
  lte: (column: string, value: any) => SupabaseQueryBuilder<T>
  like: (column: string, value: string) => SupabaseQueryBuilder<T>
  ilike: (column: string, value: string) => SupabaseQueryBuilder<T>
  is: (column: string, value: any) => SupabaseQueryBuilder<T>
  in: (column: string, values: any[]) => SupabaseQueryBuilder<T>
  contains: (column: string, value: any) => SupabaseQueryBuilder<T>
  containedBy: (column: string, value: any) => SupabaseQueryBuilder<T>
  rangeLt: (column: string, range: any) => SupabaseQueryBuilder<T>
  rangeGt: (column: string, range: any) => SupabaseQueryBuilder<T>
  rangeGte: (column: string, range: any) => SupabaseQueryBuilder<T>
  rangeLte: (column: string, range: any) => SupabaseQueryBuilder<T>
  overlaps: (column: string, value: any) => SupabaseQueryBuilder<T>
  textSearch: (column: string, query: string, options?: { config?: string }) => SupabaseQueryBuilder<T>
  filter: (column: string, operator: string, value: any) => SupabaseQueryBuilder<T>
  not: (column: string, operator: string, value: any) => SupabaseQueryBuilder<T>
  or: (filters: string, options?: { foreignTable?: string }) => SupabaseQueryBuilder<T>
  order: (
    column: string,
    options?: { ascending?: boolean; nullsFirst?: boolean; foreignTable?: string },
  ) => SupabaseQueryBuilder<T>
  limit: (count: number, options?: { foreignTable?: string }) => SupabaseQueryBuilder<T>
  range: (from: number, to: number, options?: { foreignTable?: string }) => SupabaseQueryBuilder<T>
  single: () => Promise<{ data: T | null; error: Error | null }>
  maybeSingle: () => Promise<{ data: T | null; error: Error | null }>
  then: (onfulfilled?: (value: { data: T[] | null; error: Error | null }) => any) => Promise<any>
}

// Mock data store
const mockDataStore = {
  countries: [...COUNTRIES],
  specialties: [...SPECIALTIES],
  doctors: [...DOCTORS],
  hospitals: [...HOSPITALS],
  users: [
    {
      user_id: "1",
      username: "testuser",
      email: "<EMAIL>",
      first_name: "Test",
      last_name: "User",
      gender: "male",
      age: 30,
      city: "Test City",
      country: "Test Country",
      user_type: "patient",
    },
  ],
  reviews: [],
}

// Mock query builder
function createQueryBuilder<T>(table: any[]): SupabaseQueryBuilder<T> {
  let filteredData = [...table]
  let selectedColumns: string | null = null

  const builder: SupabaseQueryBuilder<T> = {
    select: (columns) => {
      selectedColumns = columns
      return builder
    },
    eq: (column, value) => {
      filteredData = filteredData.filter((item) => item[column] == value) // Use == for type coercion
      return builder
    },
    neq: (column, value) => {
      filteredData = filteredData.filter((item) => item[column] != value)
      return builder
    },
    gt: (column, value) => {
      filteredData = filteredData.filter((item) => item[column] > value)
      return builder
    },
    lt: (column, value) => {
      filteredData = filteredData.filter((item) => item[column] < value)
      return builder
    },
    gte: (column, value) => {
      filteredData = filteredData.filter((item) => item[column] >= value)
      return builder
    },
    lte: (column, value) => {
      filteredData = filteredData.filter((item) => item[column] <= value)
      return builder
    },
    like: (column, value) => {
      const regex = new RegExp(value.replace(/%/g, ".*"))
      filteredData = filteredData.filter((item) => regex.test(String(item[column])))
      return builder
    },
    ilike: (column, value) => {
      const regex = new RegExp(value.replace(/%/g, ".*"), "i")
      filteredData = filteredData.filter((item) => regex.test(String(item[column])))
      return builder
    },
    is: (column, value) => {
      if (value === null) {
        filteredData = filteredData.filter((item) => item[column] === null)
      } else {
        filteredData = filteredData.filter((item) => item[column] === value)
      }
      return builder
    },
    in: (column, values) => {
      filteredData = filteredData.filter((item) => values.includes(item[column]))
      return builder
    },
    contains: (column, value) => {
      filteredData = filteredData.filter((item) => {
        const columnValue = item[column]
        if (Array.isArray(columnValue)) {
          return value.every((v) => columnValue.includes(v))
        } else if (typeof columnValue === "object" && columnValue !== null) {
          return Object.keys(value).every((k) => columnValue[k] === value[k])
        }
        return false
      })
      return builder
    },
    containedBy: (column, value) => {
      filteredData = filteredData.filter((item) => {
        const columnValue = item[column]
        if (Array.isArray(columnValue) && Array.isArray(value)) {
          return columnValue.every((v) => value.includes(v))
        } else if (typeof columnValue === "object" && columnValue !== null && typeof value === "object") {
          return Object.keys(columnValue).every((k) => value[k] === columnValue[k])
        }
        return false
      })
      return builder
    },
    rangeLt: () => builder,
    rangeGt: () => builder,
    rangeGte: () => builder,
    rangeLte: () => builder,
    overlaps: () => builder,
    textSearch: () => builder,
    filter: () => builder,
    not: () => builder,
    or: () => builder,
    order: (column, options = {}) => {
      const { ascending = true } = options
      filteredData.sort((a, b) => {
        if (a[column] < b[column]) return ascending ? -1 : 1
        if (a[column] > b[column]) return ascending ? 1 : -1
        return 0
      })
      return builder
    },
    limit: (count) => {
      filteredData = filteredData.slice(0, count)
      return builder
    },
    range: (from, to) => {
      filteredData = filteredData.slice(from, to + 1)
      return builder
    },
    single: async () => {
      if (filteredData.length === 0) {
        return { data: null, error: { message: "No rows found", code: "PGRST116" } }
      }
      if (filteredData.length > 1) {
        return { data: null, error: { message: "More than one row returned", code: "PGRST102" } }
      }
      return { data: filteredData[0] as unknown as T, error: null }
    },
    maybeSingle: async () => {
      if (filteredData.length === 0) {
        return { data: null, error: null }
      }
      return { data: filteredData[0] as unknown as T, error: null }
    },
    then: async (onfulfilled) => {
      const result = { data: filteredData as unknown as T[], error: null }
      return onfulfilled ? onfulfilled(result) : result
    },
  }

  return builder
}

// Mock storage
const mockStorage = {
  from: (bucket: string) => ({
    upload: async (path: string, file: File) => {
      console.log(`Mock uploading ${file.name} to ${bucket}/${path}`)
      return { data: { path }, error: null }
    },
    getPublicUrl: (path: string) => {
      return { data: { publicUrl: `https://mock-supabase-storage.com/${bucket}/${path}` } }
    },
  }),
}

// Mock auth
const mockAuth = {
  getSession: async () => {
    return { data: { session: null }, error: null }
  },
  getUser: async () => {
    return { data: { user: null }, error: null }
  },
  signUp: async ({ email, password, options }: any) => {
    const userId = `user_${Date.now()}`
    const user = {
      id: userId,
      email,
      user_metadata: options?.data || {},
    }
    return { data: { user }, error: null }
  },
  signInWithPassword: async ({ email, password }: any) => {
    const user = mockDataStore.users.find((u) => u.email === email)
    if (!user) {
      return { data: { user: null }, error: { message: "Invalid login credentials" } }
    }
    return {
      data: {
        user: {
          id: user.user_id,
          email: user.email,
          user_metadata: {
            username: user.username,
            full_name: `${user.first_name} ${user.last_name}`,
            user_type: user.user_type,
          },
        },
      },
      error: null,
    }
  },
  signOut: async () => {
    return { error: null }
  },
}

// Mock Supabase client
export const createMockSupabaseClient = () => {
  return {
    from: (table: string) => {
      const tableData = mockDataStore[table as keyof typeof mockDataStore] || []
      return createQueryBuilder(tableData)
    },
    storage: mockStorage,
    auth: mockAuth,
  }
}

// Export a function that mimics the createBrowserSupabaseClient function
export const createBrowserSupabaseClient = () => {
  return createMockSupabaseClient()
}

// Export a function that mimics the createServerSupabaseClient function
export const createServerSupabaseClient = () => {
  return createMockSupabaseClient()
}

