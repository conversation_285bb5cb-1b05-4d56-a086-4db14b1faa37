/**
 * Simplified mock implementation of tailwind-merge's twMerge function
 * This is a basic implementation that just joins class names
 * The actual library does intelligent merging of conflicting Tailwind classes
 */
export function twMerge(...classLists: string[]): string {
  // For this simplified mock, we'll just concatenate all class strings
  // In the real library, it would handle conflicting Tailwind classes intelligently
  return classLists.filter(Boolean).join(" ").trim()
}

