import { createBrowserClient } from '@/lib/supabase-client'

export interface NewsletterSubscription {
  id: string
  email: string
  status: 'active' | 'inactive' | 'unsubscribed'
  subscription_date: string
  unsubscribe_token: string
  confirmed_at: string | null
  source: string
  preferences: Record<string, any>
  created_at: string
  updated_at: string
}

export interface CreateNewsletterSubscriptionData {
  email: string
  source?: string
  preferences?: Record<string, any>
}

interface SubscriptionData {
  status: 'active' | 'inactive' | 'unsubscribed'
  subscription_date: string
}

class NewsletterService {
  private supabase = createBrowserClient()

  async subscribe(data: CreateNewsletterSubscriptionData): Promise<{ success: boolean; error?: string; subscription?: NewsletterSubscription }> {
    try {
      // Check if email already exists
      const { data: existing, error: checkError } = await this.supabase
        .from('newsletter_subscriptions')
        .select('*')
        .eq('email', data.email)
        .single()

      if (checkError && checkError.code !== 'PGRST116') {
        console.error('Error checking existing subscription:', checkError)
        return { success: false, error: 'Failed to check existing subscription' }
      }

      if (existing) {
        // If user exists but is unsubscribed, reactivate them
        if (existing.status === 'unsubscribed') {
          const { data: updatedSubscription, error: updateError } = await this.supabase
            .from('newsletter_subscriptions')
            .update({ 
              status: 'active',
              subscription_date: new Date().toISOString(),
              source: data.source || 'blog_newsletter'
            })
            .eq('id', existing.id)
            .select()
            .single()

          if (updateError) {
            console.error('Error reactivating subscription:', updateError)
            return { success: false, error: 'Failed to reactivate subscription' }
          }

          return { success: true, subscription: updatedSubscription }
        } else {
          // Already subscribed
          return { success: true, subscription: existing }
        }
      }

      // Create new subscription
      const { data: newSubscription, error: createError } = await this.supabase
        .from('newsletter_subscriptions')
        .insert({
          email: data.email,
          source: data.source || 'blog_newsletter',
          preferences: data.preferences || {},
          status: 'active'
        })
        .select()
        .single()

      if (createError) {
        console.error('Error creating subscription:', createError)
        return { success: false, error: 'Failed to create subscription' }
      }

      return { success: true, subscription: newSubscription }
    } catch (error) {
      console.error('Unexpected error in newsletter subscription:', error)
      return { success: false, error: 'An unexpected error occurred' }
    }
  }

  async unsubscribe(token: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await this.supabase
        .from('newsletter_subscriptions')
        .update({ status: 'unsubscribed' })
        .eq('unsubscribe_token', token)

      if (error) {
        console.error('Error unsubscribing:', error)
        return { success: false, error: 'Failed to unsubscribe' }
      }

      return { success: true }
    } catch (error) {
      console.error('Unexpected error in unsubscribe:', error)
      return { success: false, error: 'An unexpected error occurred' }
    }
  }

  async getSubscriptionStats(): Promise<{
    total: number
    active: number
    unsubscribed: number
    thisMonth: number
  }> {
    try {
      const { data, error } = await this.supabase
        .from('newsletter_subscriptions')
        .select('status, subscription_date')

      if (error) {
        console.error('Error getting subscription stats:', error)
        return { total: 0, active: 0, unsubscribed: 0, thisMonth: 0 }
      }

      const now = new Date()
      const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)

      const stats = (data || []).reduce(
        (acc: { total: number; active: number; unsubscribed: number; thisMonth: number }, sub: SubscriptionData) => {
          acc.total++
          if (sub.status === 'active') acc.active++
          if (sub.status === 'unsubscribed') acc.unsubscribed++
          if (new Date(sub.subscription_date) >= thisMonthStart) acc.thisMonth++
          return acc
        },
        { total: 0, active: 0, unsubscribed: 0, thisMonth: 0 }
      )

      return stats
    } catch (error) {
      console.error('Unexpected error getting stats:', error)
      return { total: 0, active: 0, unsubscribed: 0, thisMonth: 0 }
    }
  }

  async getAllSubscriptions(limit: number = 50, offset: number = 0): Promise<{
    subscriptions: NewsletterSubscription[]
    total: number
  }> {
    try {
      const { data, error, count } = await this.supabase
        .from('newsletter_subscriptions')
        .select('*', { count: 'exact' })
        .order('subscription_date', { ascending: false })
        .range(offset, offset + limit - 1)

      if (error) {
        console.error('Error getting subscriptions:', error)
        return { subscriptions: [], total: 0 }
      }

      return { subscriptions: data || [], total: count || 0 }
    } catch (error) {
      console.error('Unexpected error getting subscriptions:', error)
      return { subscriptions: [], total: 0 }
    }
  }
}

export const newsletterService = new NewsletterService() 