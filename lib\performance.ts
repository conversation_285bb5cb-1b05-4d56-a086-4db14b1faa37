import { type Metric, onCLS, onFID, onLCP, onFCP, onTTFB, onINP } from 'web-vitals';

// Add a tracking counter for reporting calls
let reportCount = 0;

/**
 * Core Web Vitals metrics to monitor page performance
 * and report to analytics or an endpoint
 */
export interface WebVitalsMetric {
  id: string;
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  navigationType: string;
  // Additional properties for enhanced reporting
  url?: string;
  pageLoadTime?: number;
}

// Cache to avoid repeat calculations
const metricsCache = new Map<string, WebVitalsMetric>();

/**
 * Determine if the metric value indicates good, needs improvement, or poor performance
 */
function getRating(name: string, value: number): 'good' | 'needs-improvement' | 'poor' {
  switch (name) {
    case 'CLS':
      return value < 0.1 ? 'good' : value < 0.25 ? 'needs-improvement' : 'poor';
    case 'FID':
      return value < 100 ? 'good' : value < 300 ? 'needs-improvement' : 'poor';
    case 'LCP':
      return value < 2500 ? 'good' : value < 4000 ? 'needs-improvement' : 'poor';
    case 'FCP':
      return value < 1800 ? 'good' : value < 3000 ? 'needs-improvement' : 'poor';
    case 'TTFB':
      return value < 800 ? 'good' : value < 1800 ? 'needs-improvement' : 'poor';
    case 'INP': // Interaction to Next Paint (new)
      return value < 200 ? 'good' : value < 500 ? 'needs-improvement' : 'poor';
    default:
      return 'needs-improvement';
  }
}

/**
 * Send Core Web Vitals metrics to an analytics endpoint
 * This function is designed to batch send metrics to avoid multiple API calls
 */
export function sendToAnalytics(metric: WebVitalsMetric) {
  // Don't send duplicates in same session
  if (metricsCache.has(metric.id)) return;
  metricsCache.set(metric.id, metric);

  // In production, this would send data to your analytics service
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Web Vitals] ${metric.name}: ${metric.value.toFixed(2)} (${metric.rating})`);
  } else {
    // Use beacon API for more reliable data transmission that doesn't block page unload
    try {
      const analyticsEndpoint = '/api/vitals';
      console.log(`[Performance Utility] Attempting to send ${metric.name} metric to ${analyticsEndpoint}`);
      
      // Check if the endpoint exists first
      fetch(analyticsEndpoint, { method: 'HEAD' })
        .then(response => {
          if (!response.ok) {
            console.warn(`[Performance Utility] Endpoint ${analyticsEndpoint} returned status ${response.status}. Skipping analytics transmission.`);
            return false;
          }
          return true;
        })
        .then(endpointExists => {
          if (!endpointExists) return;
          
          // Only send if browser supports sendBeacon or fetch
          if (navigator.sendBeacon) {
            console.log(`[Performance Utility] Using sendBeacon for ${metric.name} metric`);
            const blob = new Blob([JSON.stringify(metric)], { type: 'application/json' });
            const success = navigator.sendBeacon(analyticsEndpoint, blob);
            if (!success) {
              console.warn(`[Performance Utility] sendBeacon failed for ${metric.name}`);
            }
          } else if (fetch) {
            // Use fetch with keepalive as a fallback
            console.log(`[Performance Utility] Using fetch for ${metric.name} metric`);
            fetch(analyticsEndpoint, {
              body: JSON.stringify(metric),
              method: 'POST',
              keepalive: true,
              headers: { 'Content-Type': 'application/json' }
            }).catch((error) => {
              console.warn(`[Performance Utility] Fetch failed for ${metric.name}: ${error.message}`);
            });
          }
        })
        .catch(error => {
          console.warn(`[Performance Utility] Error checking endpoint ${analyticsEndpoint}: ${error.message}`);
        });
    } catch (e) {
      // Log error but don't interrupt user experience if analytics fails
      console.warn(`[Performance Utility] Error in sendToAnalytics: ${e instanceof Error ? e.message : String(e)}`);
    }
  }
}

// Debounce function to limit metric reporting frequency
function debounce(func: Function, wait: number) {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  return function executedFunction(...args: any[]) {
    const later = () => {
      timeout = null;
      func(...args);
    };
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Debounced version of sendToAnalytics to prevent too many calls
const debouncedSendToAnalytics = debounce(sendToAnalytics, 1000);

/**
 * Reports all Core Web Vitals metrics
 */
export function reportWebVitals(onReport: (metric: WebVitalsMetric) => void) {
  // Only run in browser environment
  if (typeof window === 'undefined') return;
  
  // Track how many times this is called
  reportCount++;
  console.log(`[Performance Utility] reportWebVitals called ${reportCount} times`);

  const handler = (metric: Metric) => {
    const currentUrl = window.location.href;
    const pageLoadTime = window.performance && 
      window.performance.timing ? 
      window.performance.timing.loadEventEnd - window.performance.timing.navigationStart : 
      undefined;
      
    const vitalsMetric: WebVitalsMetric = {
      id: metric.id,
      name: metric.name,
      value: metric.value,
      rating: getRating(metric.name, metric.value),
      delta: metric.delta,
      navigationType: metric.navigationType || '',
      url: currentUrl,
      pageLoadTime
    };
    
    // Pass to callback
    onReport(vitalsMetric);
    
    // Also send to analytics in a controlled manner
    debouncedSendToAnalytics(vitalsMetric);
  };

  // Use requestIdleCallback or setTimeout to avoid blocking the main thread
  const registerMetrics = () => {
    // Standard Core Web Vitals
    onCLS(handler);
    onFID(handler);
    onLCP(handler);
    onFCP(handler);
    onTTFB(handler);
    // New experimental metrics
    onINP(handler); // Interaction to Next Paint
  };

  if ('requestIdleCallback' in window) {
    window.requestIdleCallback(() => registerMetrics());
  } else {
    setTimeout(registerMetrics, 1);
  }
}

/**
 * Provides guidance on how to improve Core Web Vitals
 */
export function getPerformanceGuidance(metric: string): string {
  switch (metric) {
    case 'CLS':
      return 'Improve Cumulative Layout Shift by adding size attributes to images, not inserting content above existing content, and using transform animations instead of animations that affect layout.';
    case 'FID':
      return 'Improve First Input Delay by breaking up long tasks, optimizing JavaScript execution, and reducing JavaScript blocking time.';
    case 'LCP':
      return 'Improve Largest Contentful Paint by optimizing server response times, implementing resource prioritization, and optimizing CSS and image loading.';
    case 'FCP':
      return 'Improve First Contentful Paint by eliminating render-blocking resources, minifying CSS, and optimizing critical rendering path.';
    case 'TTFB':
      return 'Improve Time to First Byte by optimizing server processing, implementing CDN, and optimizing database queries.';
    case 'INP':
      return 'Improve Interaction to Next Paint by optimizing event handlers, reducing main thread blocking, and improving input responsiveness.';
    default:
      return 'Optimize performance through code splitting, lazy loading, and image optimization.';
  }
}

/**
 * Get all performance metrics available from the browser
 */
export function getAllPerformanceMetrics() {
  if (typeof window !== 'undefined' && window.performance) {
    try {
      const navigation = window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const resources = window.performance.getEntriesByType('resource');
      
      // Parse Navigation Timing API metrics
      const navigationMetrics = navigation ? {
        domComplete: navigation.domComplete || 0,
        domInteractive: navigation.domInteractive || 0,
        loadEventEnd: navigation.loadEventEnd || 0,
        responseEnd: navigation.responseEnd || 0,
        responseStart: navigation.responseStart || 0,
        fetchStart: navigation.fetchStart || 0,
        redirectEnd: navigation.redirectEnd || 0,
        redirectStart: navigation.redirectStart || 0,
      } : null;
      
      // Calculate derived metrics
      const derivedMetrics = navigationMetrics ? {
        ttfb: navigationMetrics.responseStart - navigationMetrics.fetchStart,
        resourceLoadTime: navigationMetrics.loadEventEnd - navigationMetrics.responseEnd,
        redirectTime: navigationMetrics.redirectEnd - navigationMetrics.redirectStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        firstPaint: getFirstPaint(),
      } : null;
      
      // Get resource metrics
      const resourceMetrics = resources.length > 0 ? {
        totalResources: resources.length,
        totalResourceSize: resources.reduce((total, resource) => {
          return total + ((resource as any).encodedBodySize || 0);
        }, 0),
        slowestResource: resources.reduce((prev, current) => 
          (current.duration || 0) > (prev.duration || 0) ? current : prev),
        resourcesByType: getResourcesByType(resources),
      } : null;
      
      return {
        navigation: navigationMetrics,
        derived: derivedMetrics,
        resources: resourceMetrics,
        memory: getMemoryInfo(),
        connection: getConnectionInfo(),
      };
    } catch (e) {
      console.error('Error collecting performance metrics:', e);
      return null;
    }
  }
  
  return null;
}

/**
 * Get first paint metric from Performance Timeline API
 */
function getFirstPaint(): number | null {
  if (typeof window === 'undefined') return null;
  
  const paintEntries = window.performance.getEntriesByType('paint');
  const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
  return firstPaint ? firstPaint.startTime : null;
}

/**
 * Get resources grouped by type
 */
function getResourcesByType(resources: PerformanceResourceTiming[]): Record<string, number> {
  const types: Record<string, number> = {};
  
  resources.forEach(resource => {
    const fileType = resource.initiatorType || 'other';
    types[fileType] = (types[fileType] || 0) + 1;
  });
  
  return types;
}

/**
 * Get memory info if available
 */
function getMemoryInfo() {
  if (typeof window !== 'undefined' && (window.performance as any).memory) {
    const memory = (window.performance as any).memory;
    return {
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      totalJSHeapSize: memory.totalJSHeapSize,
      usedJSHeapSize: memory.usedJSHeapSize,
    };
  }
  return null;
}

/**
 * Get connection info if available 
 */
function getConnectionInfo() {
  if (typeof navigator !== 'undefined' && (navigator as any).connection) {
    const connection = (navigator as any).connection;
    return {
      effectiveType: connection.effectiveType,
      downlink: connection.downlink,
      rtt: connection.rtt,
      saveData: connection.saveData,
    };
  }
  return null;
}

/**
 * Helper function to detect if JavaScript is enabled and 
 * which critical browser features are available
 */
export function detectFeatures() {
  if (typeof window === 'undefined') return {};
  
  return {
    webp: detectWebPSupport(),
    avif: detectAvifSupport(),
    intersectionObserver: typeof IntersectionObserver !== 'undefined',
    localStorage: detectLocalStorageSupport(),
    sessionStorage: detectSessionStorageSupport(),
    serviceWorker: 'serviceWorker' in navigator,
  };
}

/**
 * Detect WebP Support
 */
function detectWebPSupport(): boolean {
  if (typeof window === 'undefined') return false;
  
  try {
    const canvas = document.createElement('canvas');
    if (canvas.getContext && canvas.getContext('2d')) {
      // Check for WebP support
      return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    }
  } catch (e) {
    return false;
  }
  
  return false;
}

/**
 * Detect AVIF Support
 */
function detectAvifSupport(): boolean {
  if (typeof window === 'undefined') return false;
  
  // Use a synchronous approach instead of async
  try {
    return document.createElement('canvas')
      .toDataURL('image/avif')
      .indexOf('data:image/avif') === 0;
  } catch (e) {
    return false;
  }
}

/**
 * General image support detection - synchronous version
 */
function detectImageSupport(data: string): boolean {
  try {
    // For synchronous image format detection, we'll rely on feature detection
    // through the canvas API similar to WebP detection
    return typeof window !== 'undefined';
  } catch (e) {
    return false;
  }
}

/**
 * Detect localStorage Support
 */
function detectLocalStorageSupport(): boolean {
  if (typeof window === 'undefined') return false;
  
  try {
    localStorage.setItem('test', 'test');
    localStorage.removeItem('test');
    return true;
  } catch (e) {
    return false;
  }
}

/**
 * Detect sessionStorage Support
 */
function detectSessionStorageSupport(): boolean {
  if (typeof window === 'undefined') return false;
  
  try {
    sessionStorage.setItem('test', 'test');
    sessionStorage.removeItem('test');
    return true;
  } catch (e) {
    return false;
  }
} 