import type { Metadata } from 'next'

/**
 * Default organization schema for the site
 */
export const organizationSchema = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  'name': "Doctor's Leagues",
  'url': 'https://doctorsleagues.com',
  'logo': 'https://doctorsleagues.com/logo.png',
  'description': 'The premier platform for ranking medical professionals based on peer reviews, patient outcomes, and research contributions.',
  'sameAs': [
    'https://twitter.com/DoctorsLeague',
    'https://facebook.com/DoctorsLeague',
    'https://linkedin.com/company/doctors-league',
    'https://instagram.com/doctorsleague'
  ],
  'address': {
    '@type': 'PostalAddress',
    'addressCountry': 'Bahrain'
  }
}

/**
 * Base URL for the site
 */
export const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://doctorsleagues.com'

/**
 * Default metadata for the site
 */
export const defaultMetadata: Metadata = {
  title: {
    default: "Doctor's Leagues | Medical Professional Rankings",
    template: "%s | Doctor's Leagues"
  },
  description: "Doctor's Leagues is the premier platform for ranking medical professionals based on peer reviews, patient outcomes, and research contributions.",
  applicationName: "Doctor's Leagues",
  authors: [{ name: "Doctor's Leagues Team" }],
  generator: 'Next.js',
  keywords: [
    'doctor rankings', 
    'medical professional ratings', 
    'healthcare providers', 
    'physicians comparison', 
    'medical leagues'
  ],
  creator: "Doctor's Leagues",
  publisher: "Doctor's Leagues",
  formatDetection: {
    telephone: true,
    address: true,
    email: true,
  },
  metadataBase: new URL(baseUrl),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: baseUrl,
    siteName: "Doctor's Leagues",
    title: "Doctor's Leagues | Medical Professional Rankings",
    description: "Doctor's Leagues is the premier platform for ranking medical professionals based on peer reviews, patient outcomes, and research contributions.",
    images: [{
      url: `${baseUrl}/images/og-default.jpg`,
      width: 1200,
      height: 630,
      alt: "Doctor's Leagues - Where Healthcare Heroes Compete for Your Trust"
    }]
  },
  twitter: {
    card: 'summary_large_image',
    site: '@DoctorsLeague',
    title: "Doctor's Leagues | Medical Professional Rankings",
    description: "Doctor's Leagues is the premier platform for ranking medical professionals based on peer reviews, patient outcomes, and research contributions.",
    creator: '@DoctorsLeague',
    images: [`${baseUrl}/images/twitter-default.jpg`]
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-video-preview': -1,
      'max-snippet': -1
    }
  },
  verification: {
    google: 'google-site-verification-code',
  },
  category: 'healthcare'
}

/**
 * Generate metadata for a specific page
 */
export function generateMetadata(
  title: string,
  description: string,
  keywords: string[] = [],
  path: string = '/'
): Metadata {
  const url = path.startsWith('http') ? path : `${baseUrl}${path}`

  return {
    title,
    description,
    keywords: [...defaultMetadata.keywords as string[], ...keywords],
    alternates: {
      canonical: url,
    },
    openGraph: {
      title,
      description,
      url,
      siteName: "Doctor's Leagues",
      type: 'website',
      locale: 'en_US',
      images: [{
        url: `${baseUrl}/images/og-default.jpg`,
        width: 1200,
        height: 630,
        alt: `Doctor's Leagues - ${title}`
      }]
    },
    twitter: {
      card: 'summary_large_image',
      site: '@DoctorsLeague',
      title,
      description,
      creator: '@DoctorsLeague',
      images: [`${baseUrl}/images/twitter-default.jpg`]
    }
  }
}

/**
 * Generate metadata for a doctor profile page
 */
export function generateDoctorMetadata(
  doctorName: string,
  specialty: string,
  description: string,
  doctorId: string,
  imageUrl?: string
): Metadata {
  const title = `Dr. ${doctorName} - ${specialty} | Doctor's Leagues`
  const url = `${baseUrl}/doctor/${doctorId}`
  
  return {
    title,
    description,
    alternates: {
      canonical: url,
    },
    openGraph: {
      title,
      description,
      url,
      type: 'profile',
      siteName: "Doctor's Leagues",
      locale: 'en_US',
      images: [{
        url: imageUrl || `${baseUrl}/images/doctor-placeholder.jpg`,
        width: 1200,
        height: 630,
        alt: `Dr. ${doctorName}, ${specialty} - Doctor's Leagues`
      }],
    },
    twitter: {
      card: 'summary_large_image',
      site: '@DoctorsLeague',
      title,
      description,
      creator: '@DoctorsLeague',
      images: [imageUrl || `${baseUrl}/images/doctor-placeholder.jpg`]
    }
  }
}

/**
 * Generate metadata for an article page (blog, news)
 */
export function generateArticleMetadata(
  title: string,
  description: string,
  slug: string,
  publishDate: string,
  modifiedDate?: string,
  authorName?: string,
  imageUrl?: string,
  tags?: string[]
): Metadata {
  const url = `${baseUrl}/blog/${slug}`
  
  return {
    title,
    description,
    keywords: [...(tags || [])],
    alternates: {
      canonical: url,
    },
    authors: authorName ? [{ name: authorName }] : undefined,
    openGraph: {
      title,
      description,
      url,
      type: 'article',
      siteName: "Doctor's Leagues",
      locale: 'en_US',
      images: [{
        url: imageUrl || `${baseUrl}/images/blog-placeholder.jpg`,
        width: 1200,
        height: 630,
        alt: title
      }],
      publishedTime: publishDate,
      modifiedTime: modifiedDate,
      authors: authorName ? [authorName] : undefined,
      tags
    },
    twitter: {
      card: 'summary_large_image',
      site: '@DoctorsLeague',
      title,
      description,
      creator: '@DoctorsLeague',
      images: [imageUrl || `${baseUrl}/images/blog-placeholder.jpg`]
    }
  }
} 