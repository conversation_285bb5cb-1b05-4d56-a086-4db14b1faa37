import { supabase } from "../supabase-client"
import { createUser, getUserByEmail } from "./users-service"

export interface SignUpData {
  email: string
  password: string
  username: string
  firstName?: string
  lastName?: string
  userType: "patient" | "doctor"
}

export interface SignInData {
  email: string
  password: string
}

export async function signUp(data: SignUpData) {
  try {
    // First, create the auth user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: data.email,
      password: data.password,
    })

    if (authError) {
      console.error("Auth error during sign up:", authError)
      return { user: null, error: authError.message }
    }

    // Then, insert the user data into the users table
    const user = await createUser({
      username: data.username,
      email: data.email,
      first_name: data.firstName || null,
      last_name: data.lastName || null,
      user_type: data.userType,
    })

    if (!user) {
      console.error("Error creating user record")
      return { user: null, error: "Failed to create user record" }
    }

    return { user, error: null }
  } catch (error: any) {
    console.error("Exception in signUp:", error)
    return { user: null, error: error.message || "An unexpected error occurred" }
  }
}

export async function signIn(data: SignInData) {
  try {
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: data.email,
      password: data.password,
    })

    if (authError) {
      console.error("Auth error during sign in:", authError)
      return { session: null, user: null, error: authError.message }
    }

    // Get the user data from the users table
    const user = await getUserByEmail(data.email)

    return { session: authData.session, user, error: null }
  } catch (error: any) {
    console.error("Exception in signIn:", error)
    return { session: null, user: null, error: error.message || "An unexpected error occurred" }
  }
}

export async function signOut() {
  try {
    const { error } = await supabase.auth.signOut()

    if (error) {
      console.error("Error during sign out:", error)
      return { error: error.message }
    }

    return { error: null }
  } catch (error: any) {
    console.error("Exception in signOut:", error)
    return { error: error.message || "An unexpected error occurred" }
  }
}

export async function getCurrentSession() {
  try {
    const { data, error } = await supabase.auth.getSession()

    if (error) {
      console.error("Error getting session:", error)
      return { session: null, error: error.message }
    }

    return { session: data.session, error: null }
  } catch (error: any) {
    console.error("Exception in getCurrentSession:", error)
    return { session: null, error: error.message || "An unexpected error occurred" }
  }
}

export async function getCurrentUser() {
  try {
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession()

    if (sessionError || !sessionData.session) {
      return { user: null, error: sessionError?.message || "No active session" }
    }

    const { data: userData, error: userError } = await supabase.auth.getUser()

    if (userError) {
      console.error("Error getting user:", userError)
      return { user: null, error: userError.message }
    }

    // Get the user data from the users table
    const user = await getUserByEmail(userData.user.email || "")

    return { user, error: null }
  } catch (error: any) {
    console.error("Exception in getCurrentUser:", error)
    return { user: null, error: error.message || "An unexpected error occurred" }
  }
}

export async function resetPassword(email: string) {
  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    })

    if (error) {
      console.error("Error sending password reset email:", error)
      return { error: error.message }
    }

    return { error: null }
  } catch (error: any) {
    console.error("Exception in resetPassword:", error)
    return { error: error.message || "An unexpected error occurred" }
  }
}

export async function updatePassword(password: string) {
  try {
    const { error } = await supabase.auth.updateUser({
      password,
    })

    if (error) {
      console.error("Error updating password:", error)
      return { error: error.message }
    }

    return { error: null }
  } catch (error: any) {
    console.error("Exception in updatePassword:", error)
    return { error: error.message || "An unexpected error occurred" }
  }
}

