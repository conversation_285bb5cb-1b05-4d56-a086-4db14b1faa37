import { supabase, type Database } from "../supabase-client"

export type Country = Database["public"]["Tables"]["countries"]["Row"]

export async function getCountries(): Promise<Country[]> {
  try {
    // Fetch countries from Supabase database
    const { data, error } = await supabase.from("countries").select("*").order("country_name")

    if (error) {
      console.error("Error fetching countries from Supabase:", error)
      return []
    }

    return data
  } catch (error) {
    console.error("Exception in getCountries:", error)
    return []
  }
}

export async function getCountryById(countryId: string | number): Promise<Country | null> {
  try {
    const { data, error } = await supabase.from("countries").select("*").eq("country_id", countryId).single()

    if (error) {
      console.error(`Error fetching country with ID ${countryId}:`, error)
      return null
    }

    return data
  } catch (error) {
    console.error(`Exception in getCountryById for ID ${countryId}:`, error)
    return null
  }
}

export async function getCountryByName(countryName: string): Promise<Country | null> {
  // Normalize the country name
  const normalizedName = countryName
    .split("-")
    .map((part) => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
    .join(" ")

  try {
    const { data, error } = await supabase.from("countries").select("*").ilike("country_name", normalizedName).single()

    if (error) {
      console.error(`Error fetching country with name ${normalizedName}:`, error)
      return null
    }

    return data
  } catch (error) {
    console.error(`Exception in getCountryByName for name ${normalizedName}:`, error)
    return null
  }
}

export async function createCountry(countryName: string): Promise<Country | null> {
  try {
    const { data, error } = await supabase.from("countries").insert({ country_name: countryName }).select().single()

    if (error) {
      console.error("Error creating country:", error)
      return null
    }

    return data
  } catch (error) {
    console.error("Exception in createCountry:", error)
    return null
  }
}

export async function updateCountry(countryId: number, countryName: string): Promise<Country | null> {
  try {
    const { data, error } = await supabase
      .from("countries")
      .update({ country_name: countryName })
      .eq("country_id", countryId)
      .select()
      .single()

    if (error) {
      console.error(`Error updating country with ID ${countryId}:`, error)
      return null
    }

    return data
  } catch (error) {
    console.error(`Exception in updateCountry for ID ${countryId}:`, error)
    return null
  }
}

export async function deleteCountry(countryId: number): Promise<boolean> {
  try {
    const { error } = await supabase.from("countries").delete().eq("country_id", countryId)

    if (error) {
      console.error(`Error deleting country with ID ${countryId}:`, error)
      return false
    }

    return true
  } catch (error) {
    console.error(`Exception in deleteCountry for ID ${countryId}:`, error)
    return false
  }
}

