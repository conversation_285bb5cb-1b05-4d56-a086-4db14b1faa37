import { supabase, type Database } from "../supabase-client"

export type Doctor = Database["public"]["Tables"]["doctors"]["Row"]
export type DoctorInsert = Database["public"]["Tables"]["doctors"]["Insert"]
export type DoctorUpdate = Database["public"]["Tables"]["doctors"]["Update"]

export async function getDoctors(): Promise<Doctor[]> {
  try {
    const { data, error } = await supabase
      .from("doctors")
      .select(`
        *,
        hospitals (
          hospital_name,
          city,
          address
        ),
        countries (
          country_name
        )
      `)
      .order("community_rating", { ascending: false })

    if (error) {
      console.error("Error fetching doctors:", error)
      return []
    }

    return data
  } catch (error) {
    console.error("Exception in getDoctors:", error)
    return []
  }
}

export async function getDoctorById(doctorId: string | number): Promise<Doctor | null> {
  // Convert the doctorId to a number if it's a numeric string
  const id = typeof doctorId === "string" ? Number.parseInt(doctorId, 10) : doctorId
  const searchId = !isNaN(id) ? id : doctorId

  try {
    const { data, error } = await supabase
      .from("doctors")
      .select(`
        *,
        hospitals (
          hospital_name,
          city,
          address
        ),
        countries (
          country_id,
          country_name
        )
      `)
      .eq("doctor_id", searchId)
      .single()

    if (error) {
      console.error(`Error fetching doctor with ID ${searchId}:`, error)
      return null
    }

    return data
  } catch (error) {
    console.error(`Exception in getDoctorById for ID ${searchId}:`, error)
    return null
  }
}

export async function getDoctorsByCountryAndSpecialty(
  countryId: string | number,
  specialtyId: string | number,
): Promise<Doctor[]> {
  try {
    const { data, error } = await supabase
      .from("doctors")
      .select(`
        *,
        hospitals (
          hospital_name
        )
      `)
      .eq("country_id", countryId)
      .eq("specialty_id", specialtyId)
      .order("community_rating", { ascending: false })

    if (error) {
      console.error(`Error fetching doctors for country ID ${countryId} and specialty ID ${specialtyId}:`, error)
      return []
    }

    return data
  } catch (error) {
    console.error(`Exception in getDoctorsByCountryAndSpecialty:`, error)
    return []
  }
}

export async function getFeaturedDoctors(limit = 6): Promise<Doctor[]> {
  try {
    const { data, error } = await supabase
      .from("doctors")
      .select(`
        *,
        hospitals (
          hospital_name
        )
      `)
      .order("community_rating", { ascending: false })
      .limit(limit)

    if (error) {
      console.error("Error fetching featured doctors:", error)
      return []
    }

    // Add rank property to each doctor
    return data.map((doctor, index) => ({
      ...doctor,
      rank: index + 1,
    }))
  } catch (error) {
    console.error("Exception in getFeaturedDoctors:", error)
    return []
  }
}

export async function createDoctor(doctor: DoctorInsert): Promise<Doctor | null> {
  try {
    const { data, error } = await supabase.from("doctors").insert(doctor).select().single()

    if (error) {
      console.error("Error creating doctor:", error)
      return null
    }

    return data
  } catch (error) {
    console.error("Exception in createDoctor:", error)
    return null
  }
}

export async function updateDoctor(doctorId: number, updates: DoctorUpdate): Promise<Doctor | null> {
  try {
    const { data, error } = await supabase.from("doctors").update(updates).eq("doctor_id", doctorId).select().single()

    if (error) {
      console.error(`Error updating doctor with ID ${doctorId}:`, error)
      return null
    }

    return data
  } catch (error) {
    console.error(`Exception in updateDoctor for ID ${doctorId}:`, error)
    return null
  }
}

export async function deleteDoctor(doctorId: number): Promise<boolean> {
  try {
    const { error } = await supabase.from("doctors").delete().eq("doctor_id", doctorId)

    if (error) {
      console.error(`Error deleting doctor with ID ${doctorId}:`, error)
      return false
    }

    return true
  } catch (error) {
    console.error(`Exception in deleteDoctor for ID ${doctorId}:`, error)
    return false
  }
}

