import { supabase, type Database } from "../supabase-client"

export type Hospital = Database["public"]["Tables"]["hospitals"]["Row"]
export type HospitalInsert = Database["public"]["Tables"]["hospitals"]["Insert"]
export type HospitalUpdate = Database["public"]["Tables"]["hospitals"]["Update"]

export async function getHospitals(
  params: {
    name?: string
    country?: string
    page?: number
    pageSize?: number
  } = {},
): Promise<{ hospitals: Hospital[]; count: number; error: string | null }> {
  const { name, country, page = 1, pageSize = 12 } = params

  try {
    let query = supabase.from("hospitals").select("*", { count: "exact" })

    if (name) {
      query = query.ilike("hospital_name", `%${name}%`)
    }

    if (country) {
      query = query.eq("country_id", country)
    }

    // Add pagination
    const from = (page - 1) * pageSize
    const to = from + pageSize - 1
    query = query.range(from, to).order("hospital_name")

    const { data, count, error } = await query

    if (error) {
      console.error("Error fetching hospitals:", error)
      return {
        hospitals: [],
        count: 0,
        error: error.message,
      }
    }

    return {
      hospitals: data || [],
      count: count || 0,
      error: null,
    }
  } catch (error: any) {
    console.error("Exception in getHospitals:", error)
    return {
      hospitals: [],
      count: 0,
      error: error.message,
    }
  }
}

export async function getHospitalsByCountry(countryId: string | number): Promise<Hospital[]> {
  try {
    const { data, error } = await supabase
      .from("hospitals")
      .select("*")
      .eq("country_id", countryId)
      .order("hospital_name")

    if (error) {
      console.error(`Error fetching hospitals for country ID ${countryId}:`, error)
      return []
    }

    return data
  } catch (error) {
    console.error(`Exception in getHospitalsByCountry for country ID ${countryId}:`, error)
    return []
  }
}

export async function getHospitalById(hospitalId: string | number): Promise<Hospital | null> {
  try {
    const { data, error } = await supabase.from("hospitals").select("*").eq("hospital_id", hospitalId).single()

    if (error) {
      console.error(`Error fetching hospital with ID ${hospitalId}:`, error)
      return null
    }

    return data
  } catch (error) {
    console.error(`Exception in getHospitalById for ID ${hospitalId}:`, error)
    return null
  }
}

export async function createHospital(hospital: HospitalInsert): Promise<Hospital | null> {
  try {
    const { data, error } = await supabase.from("hospitals").insert(hospital).select().single()

    if (error) {
      console.error("Error creating hospital:", error)
      return null
    }

    return data
  } catch (error) {
    console.error("Exception in createHospital:", error)
    return null
  }
}

export async function updateHospital(hospitalId: number, updates: HospitalUpdate): Promise<Hospital | null> {
  try {
    const { data, error } = await supabase
      .from("hospitals")
      .update(updates)
      .eq("hospital_id", hospitalId)
      .select()
      .single()

    if (error) {
      console.error(`Error updating hospital with ID ${hospitalId}:`, error)
      return null
    }

    return data
  } catch (error) {
    console.error(`Exception in updateHospital for ID ${hospitalId}:`, error)
    return null
  }
}

export async function deleteHospital(hospitalId: number): Promise<boolean> {
  try {
    const { error } = await supabase.from("hospitals").delete().eq("hospital_id", hospitalId)

    if (error) {
      console.error(`Error deleting hospital with ID ${hospitalId}:`, error)
      return false
    }

    return true
  } catch (error) {
    console.error(`Exception in deleteHospital for ID ${hospitalId}:`, error)
    return false
  }
}

