import { supabase, type Database } from "../supabase-client"

export type Review = Database["public"]["Tables"]["reviews"]["Row"]
export type ReviewInsert = Database["public"]["Tables"]["reviews"]["Insert"]
export type ReviewUpdate = Database["public"]["Tables"]["reviews"]["Update"]

export async function getDoctorReviews(doctorId: string | number): Promise<Review[]> {
  try {
    const { data, error } = await supabase
      .from("reviews")
      .select(`
        *,
        users (
          username
        )
      `)
      .eq("doctor_id", doctorId)
      .order("review_date", { ascending: false })

    if (error) {
      console.error(`Error fetching reviews for doctor with ID ${doctorId}:`, error)
      return []
    }

    return data
  } catch (error) {
    console.error(`Exception in getDoctorReviews for doctor ID ${doctorId}:`, error)
    return []
  }
}

export async function createReview(review: ReviewInsert): Promise<Review | null> {
  try {
    // First, insert the review
    const { data, error } = await supabase
      .from("reviews")
      .insert({
        ...review,
        review_date: new Date().toISOString(),
      })
      .select()
      .single()

    if (error) {
      console.error("Error creating review:", error)
      return null
    }

    // Then, update the doctor's community_rating and review count
    if (data) {
      await updateDoctorRating(data.doctor_id)
    }

    return data
  } catch (error) {
    console.error("Exception in createReview:", error)
    return null
  }
}

export async function updateReview(reviewId: number, updates: ReviewUpdate): Promise<Review | null> {
  try {
    const { data, error } = await supabase.from("reviews").update(updates).eq("review_id", reviewId).select().single()

    if (error) {
      console.error(`Error updating review with ID ${reviewId}:`, error)
      return null
    }

    // Update the doctor's community_rating if the review rating changed
    if (data && updates.rating !== undefined) {
      await updateDoctorRating(data.doctor_id)
    }

    return data
  } catch (error) {
    console.error(`Exception in updateReview for ID ${reviewId}:`, error)
    return null
  }
}

export async function deleteReview(reviewId: number): Promise<boolean> {
  try {
    // First, get the review to know which doctor to update
    const { data: review } = await supabase.from("reviews").select("doctor_id").eq("review_id", reviewId).single()

    // Then delete the review
    const { error } = await supabase.from("reviews").delete().eq("review_id", reviewId)

    if (error) {
      console.error(`Error deleting review with ID ${reviewId}:`, error)
      return false
    }

    // Finally, update the doctor's community_rating
    if (review) {
      await updateDoctorRating(review.doctor_id)
    }

    return true
  } catch (error) {
    console.error(`Exception in deleteReview for ID ${reviewId}:`, error)
    return false
  }
}

// Helper function to update a doctor's community_rating and review count
async function updateDoctorRating(doctorId: number): Promise<void> {
  try {
    // Get all reviews for the doctor
    const { data: reviews, error: reviewsError } = await supabase
      .from("reviews")
      .select("rating")
      .eq("doctor_id", doctorId)

    if (reviewsError) {
      console.error(`Error fetching reviews for doctor ${doctorId}:`, reviewsError)
      return
    }

    // Calculate the new average community_rating
    const reviewCount = reviews.length
    const totalRating = reviews.reduce((sum, review) => sum + (review.rating || 0), 0)
    const averageRating = reviewCount > 0 ? totalRating / reviewCount : 0

    // Update the doctor's community_rating and review count
    const { error: updateError } = await supabase
      .from("doctors")
      .update({
        community_rating: averageRating,
        review_count: reviewCount,
      })
      .eq("doctor_id", doctorId)

    if (updateError) {
      console.error(`Error updating doctor ${doctorId} community_rating:`, updateError)
    }
  } catch (error) {
    console.error(`Exception in updateDoctorRating for doctor ${doctorId}:`, error)
  }
}

