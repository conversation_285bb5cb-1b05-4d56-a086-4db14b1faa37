import { supabase, type Database } from "../supabase-client"

export type Specialty = Database["public"]["Tables"]["specialties"]["Row"]

export async function getSpecialties(): Promise<Specialty[]> {
  try {
    const { data, error } = await supabase.from("specialties").select("*").order("specialty_name")

    if (error) {
      console.error("Error fetching specialties:", error)
      return []
    }

    return data
  } catch (error) {
    console.error("Exception in getSpecialties:", error)
    return []
  }
}

export async function getSpecialtyById(specialtyId: string | number): Promise<Specialty | null> {
  try {
    const { data, error } = await supabase.from("specialties").select("*").eq("specialty_id", specialtyId).single()

    if (error) {
      console.error(`Error fetching specialty with ID ${specialtyId}:`, error)
      return null
    }

    return data
  } catch (error) {
    console.error(`Exception in getSpecialtyById for ID ${specialtyId}:`, error)
    return null
  }
}

export async function createSpecialty(specialtyName: string, description?: string): Promise<Specialty | null> {
  try {
    const { data, error } = await supabase
      .from("specialties")
      .insert({
        specialty_name: specialtyName,
        description,
      })
      .select()
      .single()

    if (error) {
      console.error("Error creating specialty:", error)
      return null
    }

    return data
  } catch (error) {
    console.error("Exception in createSpecialty:", error)
    return null
  }
}

export async function updateSpecialty(
  specialtyId: number,
  updates: { specialty_name?: string; description?: string },
): Promise<Specialty | null> {
  try {
    const { data, error } = await supabase
      .from("specialties")
      .update(updates)
      .eq("specialty_id", specialtyId)
      .select()
      .single()

    if (error) {
      console.error(`Error updating specialty with ID ${specialtyId}:`, error)
      return null
    }

    return data
  } catch (error) {
    console.error(`Exception in updateSpecialty for ID ${specialtyId}:`, error)
    return null
  }
}

export async function deleteSpecialty(specialtyId: number): Promise<boolean> {
  try {
    const { error } = await supabase.from("specialties").delete().eq("specialty_id", specialtyId)

    if (error) {
      console.error(`Error deleting specialty with ID ${specialtyId}:`, error)
      return false
    }

    return true
  } catch (error) {
    console.error(`Exception in deleteSpecialty for ID ${specialtyId}:`, error)
    return false
  }
}

