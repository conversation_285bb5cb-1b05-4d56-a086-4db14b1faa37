import { supabase } from "../supabase-client"
import type { Database } from "@/types/supabase"

export type User = Database["public"]["Tables"]["users"]["Row"]
export type UserInsert = Database["public"]["Tables"]["users"]["Insert"]
export type UserUpdate = Database["public"]["Tables"]["users"]["Update"]

export async function getUserById(userId: string | number): Promise<User | null> {
  try {
    const { data, error } = await supabase.from("users").select("*").eq("user_id", userId).single()

    if (error) {
      console.error(`Error fetching user with ID ${userId}:`, error)
      return null
    }

    return data
  } catch (error) {
    console.error(`Exception in getUserById for ID ${userId}:`, error)
    return null
  }
}

export async function getUserByUsername(username: string): Promise<User | null> {
  try {
    const { data, error } = await supabase.from("users").select("*").eq("username", username).single()

    if (error) {
      console.error(`Error fetching user with username ${username}:`, error)
      return null
    }

    return data
  } catch (error) {
    console.error(`Exception in getUserByUsername for username ${username}:`, error)
    return null
  }
}

export async function getUserByEmail(email: string): Promise<User | null> {
  try {
    const { data, error } = await supabase.from("users").select("*").eq("email", email).single()

    if (error) {
      console.error(`Error fetching user with email ${email}:`, error)
      return null
    }

    return data
  } catch (error) {
    console.error(`Exception in getUserByEmail for email ${email}:`, error)
    return null
  }
}

export async function createUser(user: UserInsert): Promise<User | null> {
  try {
    const { data, error } = await supabase.from("users").insert(user).select().single()

    if (error) {
      console.error("Error creating user:", error)
      return null
    }

    return data
  } catch (error) {
    console.error("Exception in createUser:", error)
    return null
  }
}

export async function updateUser(userId: number, updates: UserUpdate): Promise<User | null> {
  try {
    const { data, error } = await supabase.from("users").update(updates).eq("user_id", userId).select().single()

    if (error) {
      console.error(`Error updating user with ID ${userId}:`, error)
      return null
    }

    return data
  } catch (error) {
    console.error(`Exception in updateUser for ID ${userId}:`, error)
    return null
  }
}

export async function deleteUser(userId: number): Promise<boolean> {
  try {
    const { error } = await supabase.from("users").delete().eq("user_id", userId)

    if (error) {
      console.error(`Error deleting user with ID ${userId}:`, error)
      return false
    }

    return true
  } catch (error) {
    console.error(`Exception in deleteUser for ID ${userId}:`, error)
    return false
  }
}

export async function isUsernameAvailable(username: string): Promise<boolean> {
  try {
    const { data, error } = await supabase.from("users").select("username").eq("username", username).single()

    if (error && error.code === "PGRST116") {
      // PGRST116 means no rows returned, so username is available
      return true
    }

    return !data
  } catch (error) {
    console.error(`Exception in isUsernameAvailable for username ${username}:`, error)
    return false
  }
}

export async function isEmailAvailable(email: string): Promise<boolean> {
  try {
    const { data, error } = await supabase.from("users").select("email").eq("email", email).single()

    if (error && error.code === "PGRST116") {
      // PGRST116 means no rows returned, so email is available
      return true
    }

    return !data
  } catch (error) {
    console.error(`Exception in isEmailAvailable for email ${email}:`, error)
    return false
  }
}

