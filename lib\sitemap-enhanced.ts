import { getCountries } from "./services/countries-service"

// Interface for sitemap URLs
export interface SitemapURL {
  url: string
  lastModified?: string
  changeFrequency?: 
    | 'always'
    | 'hourly'
    | 'daily'
    | 'weekly'
    | 'monthly'
    | 'yearly'
    | 'never'
  priority?: number
  category?: string
  name?: string
}

/**
 * Sitemap URL generator with different categories
 */
export class SitemapGenerator {
  private staticPages: SitemapURL[] = []
  private categoryPages: Record<string, SitemapURL[]> = {}
  private dynamicPages: SitemapURL[] = []
  private baseUrl: string

  constructor(baseUrl: string = 'https://doctorsleagues.com') {
    this.baseUrl = baseUrl
    this.initializeStaticPages()
  }

  /**
   * Initialize static pages for the sitemap
   */
  private initializeStaticPages(): void {
    // Main navigation pages
    this.addStaticPage('/', 'Homepage', 'Main', new Date().toISOString(), 'daily', 1.0)
    this.addStaticPage('/about', 'About Us', 'Main', new Date().toISOString(), 'monthly', 0.8)
    this.addStaticPage('/standings', 'Standings', 'Main', new Date().toISOString(), 'daily', 0.9)
    this.addStaticPage('/head-to-head', 'Head to Head', 'Main', new Date().toISOString(), 'weekly', 0.8)
    this.addStaticPage('/teams', 'Teams', 'Main', new Date().toISOString(), 'weekly', 0.8)
    this.addStaticPage('/fixtures', 'Fixtures', 'Main', new Date().toISOString(), 'daily', 0.9)
    this.addStaticPage('/help', 'Help Center', 'Main', new Date().toISOString(), 'monthly', 0.7)
    
    // Policy pages
    this.addStaticPage('/policies/privacy', 'Privacy Policy', 'Policies', new Date().toISOString(), 'yearly', 0.5)
    this.addStaticPage('/policies/terms', 'Terms of Service', 'Policies', new Date().toISOString(), 'yearly', 0.5)
    this.addStaticPage('/policies/cookies', 'Cookie Policy', 'Policies', new Date().toISOString(), 'yearly', 0.5)
  }

  /**
   * Add a static page to the sitemap
   */
  public addStaticPage(
    path: string, 
    name: string,
    category: string = 'Other',
    lastModified: string = new Date().toISOString(),
    changeFrequency: SitemapURL['changeFrequency'] = 'monthly',
    priority: number = 0.5
  ): void {
    const url = path.startsWith('http') ? path : `${this.baseUrl}${path}`
    const page: SitemapURL = { 
      url, 
      name,
      category,
      lastModified, 
      changeFrequency, 
      priority 
    }
    
    this.staticPages.push(page)
    
    // Add to category pages
    if (!this.categoryPages[category]) {
      this.categoryPages[category] = []
    }
    this.categoryPages[category].push(page)
  }

  /**
   * Add a dynamic page to the sitemap
   */
  public addDynamicPage(
    path: string,
    name: string,
    category: string = 'Dynamic',
    lastModified: string = new Date().toISOString(),
    changeFrequency: SitemapURL['changeFrequency'] = 'weekly',
    priority: number = 0.7
  ): void {
    const url = path.startsWith('http') ? path : `${this.baseUrl}${path}`
    const page: SitemapURL = { 
      url, 
      name,
      category,
      lastModified, 
      changeFrequency, 
      priority 
    }
    
    this.dynamicPages.push(page)
    
    // Add to category pages
    if (!this.categoryPages[category]) {
      this.categoryPages[category] = []
    }
    this.categoryPages[category].push(page)
  }

  /**
   * Get all URLs in the sitemap
   */
  public getAllUrls(): SitemapURL[] {
    return [...this.staticPages, ...this.dynamicPages]
  }

  /**
   * Get URLs grouped by category
   */
  public getUrlsByCategory(): Record<string, SitemapURL[]> {
    return this.categoryPages
  }

  /**
   * Add dynamic country pages from the database
   */
  public async addCountryPages(): Promise<void> {
    try {
      const countries = await getCountries()
      
      countries.forEach(country => {
        this.addDynamicPage(
          `/divisions/${country.country_id}`,
          `${country.country_name} Divisions`,
          'Countries',
          new Date().toISOString(),
          'weekly',
          0.8
        )
      })
    } catch (error) {
      console.error('Error fetching countries for sitemap:', error)
    }
  }

  /**
   * Generate XML sitemap content
   */
  public generateXmlSitemap(): string {
    const allUrls = this.getAllUrls()
    
    const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allUrls.map(page => `  <url>
    <loc>${page.url}</loc>
    <lastmod>${page.lastModified}</lastmod>
    <changefreq>${page.changeFrequency}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('\n')}
</urlset>`

    return xmlContent
  }

  /**
   * Generate sitemap index for multiple sitemaps
   */
  public generateSitemapIndex(sitemaps: {url: string, lastModified: string}[]): string {
    const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemaps.map(sitemap => `  <sitemap>
    <loc>${sitemap.url}</loc>
    <lastmod>${sitemap.lastModified}</lastmod>
  </sitemap>`).join('\n')}
</sitemapindex>`

    return xmlContent
  }
}

// Create default sitemap generator instance
export const defaultSitemapGenerator = new SitemapGenerator()

// Function to generate the sitemap XML
export async function generateSitemapXML(): Promise<string> {
  const generator = new SitemapGenerator()
  
  // Add dynamic pages
  await generator.addCountryPages()
  
  // Return the XML
  return generator.generateXmlSitemap()
}

// Function to get urls by category for HTML sitemap
export async function getSitemapUrlsByCategory(): Promise<Record<string, SitemapURL[]>> {
  const generator = new SitemapGenerator()
  
  // Add dynamic pages
  await generator.addCountryPages()
  
  // Return the URLs by category
  return generator.getUrlsByCategory()
} 