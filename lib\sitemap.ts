import { getCountries } from "./services/countries-service"

// Interface for sitemap URLs
interface SitemapURL {
  url: string
  lastModified?: string
  changeFrequency?: 
    | 'always'
    | 'hourly'
    | 'daily'
    | 'weekly'
    | 'monthly'
    | 'yearly'
    | 'never'
  priority?: number
}

// Main static pages
const staticPages: SitemapURL[] = [
  {
    url: 'https://doctorsleagues.com/',
    lastModified: new Date().toISOString(),
    changeFrequency: 'daily',
    priority: 1.0,
  },
  {
    url: 'https://doctorsleagues.com/about',
    lastModified: new Date().toISOString(),
    changeFrequency: 'monthly',
    priority: 0.8,
  },
  {
    url: 'https://doctorsleagues.com/standings',
    lastModified: new Date().toISOString(),
    changeFrequency: 'daily',
    priority: 0.9,
  },
  {
    url: 'https://doctorsleagues.com/head-to-head',
    lastModified: new Date().toISOString(),
    changeFrequency: 'weekly',
    priority: 0.8,
  },
  {
    url: 'https://doctorsleagues.com/teams',
    lastModified: new Date().toISOString(),
    changeFrequency: 'weekly',
    priority: 0.8,
  },
  {
    url: 'https://doctorsleagues.com/fixtures',
    lastModified: new Date().toISOString(),
    changeFrequency: 'daily',
    priority: 0.9,
  },
  {
    url: 'https://doctorsleagues.com/help',
    lastModified: new Date().toISOString(),
    changeFrequency: 'monthly',
    priority: 0.7,
  },
  {
    url: 'https://doctorsleagues.com/policies/privacy',
    lastModified: new Date().toISOString(),
    changeFrequency: 'yearly',
    priority: 0.5,
  },
  {
    url: 'https://doctorsleagues.com/policies/terms',
    lastModified: new Date().toISOString(),
    changeFrequency: 'yearly',
    priority: 0.5,
  },
  {
    url: 'https://doctorsleagues.com/policies/cookies',
    lastModified: new Date().toISOString(),
    changeFrequency: 'yearly',
    priority: 0.5,
  },
]

// Function to generate sitemap XML
export async function generateSitemapXML(): Promise<string> {
  // Collect all URLs
  const allUrls: SitemapURL[] = [...staticPages]
  
  // Add dynamic country-based URLs
  try {
    const countries = await getCountries()
    
    countries.forEach(country => {
      allUrls.push({
        url: `https://doctorsleagues.com/divisions/${country.country_id}`,
        lastModified: new Date().toISOString(),
        changeFrequency: 'weekly',
        priority: 0.8,
      })
    })
  } catch (error) {
    console.error('Error fetching countries for sitemap:', error)
  }
  
  // Generate XML content
  const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allUrls.map(page => `  <url>
    <loc>${page.url}</loc>
    <lastmod>${page.lastModified}</lastmod>
    <changefreq>${page.changeFrequency}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('\n')}
</urlset>`

  return xmlContent
} 