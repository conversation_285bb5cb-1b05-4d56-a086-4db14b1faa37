import { createClient, SupabaseClient } from "@supabase/supabase-js"
import { logger } from "@/lib/debug-utils"
// Re-import Database type if it exists elsewhere, or keep definition if needed
// Assuming it might be defined in '@/lib/database.types' like in ad-actions.ts
// import { Database } from '@/lib/database.types'; 

// If Database type is not defined elsewhere, keep the definition below
// Define Database type here instead of importing to avoid conflicts
export type Database = {
  public: {
    Tables: {
      countries: {
        Row: {
          country_id: number
          country_name: string
          flag_url?: string
        }
        Insert: {
          country_id?: number
          country_name: string
          flag_url?: string
        }
        Update: {
          country_id?: number
          country_name?: string
          flag_url?: string
        }
      }
      specialties: {
        Row: {
          specialty_id: number
          specialty_name: string
        }
        Insert: {
          specialty_id?: number
          specialty_name: string
        }
        Update: {
          specialty_id?: number
          specialty_name?: string
        }
      }
      doctors: {
        Row: {
          doctor_id: string | number
          fullname: string
          facility: string
          specialty: string
          specialty_id: number
          country_id: number
          hospital_id: number
          community_rating?: number
          review_count?: number
          wins?: number
          losses?: number
          form?: string
          // Add other fields as needed
        }
        Insert: {
          doctor_id?: string | number
          fullname: string
          facility: string
          specialty: string
          specialty_id: number
          country_id: number
          hospital_id: number
          community_rating?: number
          review_count?: number
          wins?: number
          losses?: number
          form?: string
          // Add other fields as needed
        }
        Update: {
          doctor_id?: string | number
          fullname?: string
          facility?: string
          specialty?: string
          specialty_id?: number
          country_id?: number
          hospital_id?: number
          community_rating?: number
          review_count?: number
          wins?: number
          losses?: number
          form?: string
          // Add other fields as needed
        }
      }
      hospitals: {
        Row: {
          hospital_id: number
          hospital_name: string
          city: string
          address: string
          country_id: number
          // Add other fields as needed
        }
        Insert: {
          hospital_id?: number
          hospital_name: string
          city: string
          address: string
          country_id: number
          // Add other fields as needed
        }
        Update: {
          hospital_id?: number
          hospital_name?: string
          city?: string
          address?: string
          country_id?: number
          // Add other fields as needed
        }
      }
      reviews: {
        Row: {
          review_id: number
          doctor_id: number
          user_id: string
          rating: number
          review_text?: string
          review_date: string
        }
        Insert: {
          review_id?: number
          doctor_id: number
          user_id: string
          rating: number
          review_text?: string
          review_date?: string
        }
        Update: {
          review_id?: number
          doctor_id?: number
          user_id?: string
          rating?: number
          review_text?: string
          review_date?: string
        }
      }
      // Blog content generation tables
      blog_categories: {
        Row: {
          id: string
          name: string
          slug: string
          description?: string
          color?: string
          icon?: string
          sort_order?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          description?: string
          color?: string
          icon?: string
          sort_order?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          description?: string
          color?: string
          icon?: string
          sort_order?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      blog_authors: {
        Row: {
          id: string
          name: string
          slug: string
          email?: string
          bio?: string
          medical_credentials?: string
          specialties?: string[]
          profile_image_url?: string
          social_links?: any
          is_medical_reviewer?: boolean
          is_active?: boolean
          user_id?: string
          created_at?: string
          updated_at?: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          email?: string
          bio?: string
          medical_credentials?: string
          specialties?: string[]
          profile_image_url?: string
          social_links?: any
          is_medical_reviewer?: boolean
          is_active?: boolean
          user_id?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          email?: string
          bio?: string
          medical_credentials?: string
          specialties?: string[]
          profile_image_url?: string
          social_links?: any
          is_medical_reviewer?: boolean
          is_active?: boolean
          user_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      blog_posts: {
        Row: {
          id: string
          title: string
          slug: string
          excerpt?: string
          content: string
          featured_image_url?: string
          featured_image_alt?: string
          category_id: string
          author_id: string
          medical_reviewer_id?: string
          status?: string
          published_at?: string
          meta_title?: string
          meta_description?: string
          reading_time_minutes?: number
          view_count?: number
          is_featured?: boolean
          is_trending?: boolean
          related_doctor_ids?: number[]
          related_team_ids?: number[]
          structured_data?: any
          meta_keywords?: string[]
          raw_data_json?: any
          generation_source?: string
          ai_generation_job_id?: string
          created_at?: string
          updated_at?: string
        }
        Insert: {
          id?: string
          title: string
          slug: string
          excerpt?: string
          content: string
          featured_image_url?: string
          featured_image_alt?: string
          category_id: string
          author_id: string
          medical_reviewer_id?: string
          status?: string
          published_at?: string
          meta_title?: string
          meta_description?: string
          reading_time_minutes?: number
          view_count?: number
          is_featured?: boolean
          is_trending?: boolean
          related_doctor_ids?: number[]
          related_team_ids?: number[]
          structured_data?: any
          meta_keywords?: string[]
          raw_data_json?: any
          generation_source?: string
          ai_generation_job_id?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          slug?: string
          excerpt?: string
          content?: string
          featured_image_url?: string
          featured_image_alt?: string
          category_id?: string
          author_id?: string
          medical_reviewer_id?: string
          status?: string
          published_at?: string
          meta_title?: string
          meta_description?: string
          reading_time_minutes?: number
          view_count?: number
          is_featured?: boolean
          is_trending?: boolean
          related_doctor_ids?: number[]
          related_team_ids?: number[]
          structured_data?: any
          meta_keywords?: string[]
          raw_data_json?: any
          generation_source?: string
          ai_generation_job_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      blog_content_templates: {
        Row: {
          id: string
          name: string
          description?: string
          template_type: string
          category_id: string
          default_author_id?: string
          title_template: string
          content_structure: any
          seo_template?: any
          prompt_template: string
          target_word_count?: number
          required_sections?: string[]
          optional_sections?: string[]
          data_sources?: string[]
          auto_publish?: boolean
          requires_review?: boolean
          default_tags?: string[]
          usage_count?: number
          success_rate?: number
          avg_engagement_score?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Insert: {
          id?: string
          name: string
          description?: string
          template_type: string
          category_id: string
          default_author_id?: string
          title_template: string
          content_structure: any
          seo_template?: any
          prompt_template: string
          target_word_count?: number
          required_sections?: string[]
          optional_sections?: string[]
          data_sources?: string[]
          auto_publish?: boolean
          requires_review?: boolean
          default_tags?: string[]
          usage_count?: number
          success_rate?: number
          avg_engagement_score?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          template_type?: string
          category_id?: string
          default_author_id?: string
          title_template?: string
          content_structure?: any
          seo_template?: any
          prompt_template?: string
          target_word_count?: number
          required_sections?: string[]
          optional_sections?: string[]
          data_sources?: string[]
          auto_publish?: boolean
          requires_review?: boolean
          default_tags?: string[]
          usage_count?: number
          success_rate?: number
          avg_engagement_score?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      blog_content_generation_jobs: {
        Row: {
          id: string
          job_name: string
          template_id: string
          target_data?: any
          generation_parameters?: any
          scheduled_for?: string
          status?: string
          progress_percentage?: number
          error_message?: string
          retry_count?: number
          max_retries?: number
          generated_post_id?: string
          generated_title?: string
          generated_content?: string
          generated_metadata?: any
          quality_score?: number
          started_at?: string
          completed_at?: string
          failed_at?: string
          created_at?: string
          updated_at?: string
        }
        Insert: {
          id?: string
          job_name: string
          template_id: string
          target_data?: any
          generation_parameters?: any
          scheduled_for?: string
          status?: string
          progress_percentage?: number
          error_message?: string
          retry_count?: number
          max_retries?: number
          generated_post_id?: string
          generated_title?: string
          generated_content?: string
          generated_metadata?: any
          quality_score?: number
          started_at?: string
          completed_at?: string
          failed_at?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          job_name?: string
          template_id?: string
          target_data?: any
          generation_parameters?: any
          scheduled_for?: string
          status?: string
          progress_percentage?: number
          error_message?: string
          retry_count?: number
          max_retries?: number
          generated_post_id?: string
          generated_title?: string
          generated_content?: string
          generated_metadata?: any
          quality_score?: number
          started_at?: string
          completed_at?: string
          failed_at?: string
          created_at?: string
          updated_at?: string
        }
      }
      blog_ai_content_sources: {
        Row: {
          id: string
          name: string
          source_type: string
          api_endpoint?: string
          api_credentials?: any
          data_schema?: any
          refresh_frequency?: string
          data_quality_rules?: any
          last_successful_fetch?: string
          last_error_message?: string
          fetch_success_rate?: number
          total_requests?: number
          successful_requests?: number
          rate_limit_per_hour?: number
          current_hour_requests?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Insert: {
          id?: string
          name: string
          source_type: string
          api_endpoint?: string
          api_credentials?: any
          data_schema?: any
          refresh_frequency?: string
          data_quality_rules?: any
          last_successful_fetch?: string
          last_error_message?: string
          fetch_success_rate?: number
          total_requests?: number
          successful_requests?: number
          rate_limit_per_hour?: number
          current_hour_requests?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          source_type?: string
          api_endpoint?: string
          api_credentials?: any
          data_schema?: any
          refresh_frequency?: string
          data_quality_rules?: any
          last_successful_fetch?: string
          last_error_message?: string
          fetch_success_rate?: number
          total_requests?: number
          successful_requests?: number
          rate_limit_per_hour?: number
          current_hour_requests?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      blog_content_generation_analytics: {
        Row: {
          id: string
          post_id: string
          template_id?: string
          job_id?: string
          word_count?: number
          readability_score?: number
          seo_score?: number
          content_uniqueness?: number
          medical_accuracy_score?: number
          views_24h?: number
          views_7d?: number
          views_30d?: number
          total_views?: number
          avg_time_on_page?: number
          bounce_rate?: number
          social_shares?: number
          comments_count?: number
          generation_time_seconds?: number
          ai_confidence_score?: number
          human_review_required?: boolean
          human_review_score?: number
          last_metrics_update?: string
          created_at?: string
          updated_at?: string
        }
        Insert: {
          id?: string
          post_id: string
          template_id?: string
          job_id?: string
          word_count?: number
          readability_score?: number
          seo_score?: number
          content_uniqueness?: number
          medical_accuracy_score?: number
          views_24h?: number
          views_7d?: number
          views_30d?: number
          total_views?: number
          avg_time_on_page?: number
          bounce_rate?: number
          social_shares?: number
          comments_count?: number
          generation_time_seconds?: number
          ai_confidence_score?: number
          human_review_required?: boolean
          human_review_score?: number
          last_metrics_update?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          post_id?: string
          template_id?: string
          job_id?: string
          word_count?: number
          readability_score?: number
          seo_score?: number
          content_uniqueness?: number
          medical_accuracy_score?: number
          views_24h?: number
          views_7d?: number
          views_30d?: number
          total_views?: number
          avg_time_on_page?: number
          bounce_rate?: number
          social_shares?: number
          comments_count?: number
          generation_time_seconds?: number
          ai_confidence_score?: number
          human_review_required?: boolean
          human_review_score?: number
          last_metrics_update?: string
          created_at?: string
          updated_at?: string
        }
      }
      blog_content_generation_rules: {
        Row: {
          id: string
          rule_name: string
          description?: string
          template_id: string
          trigger_type: string
          schedule_expression?: string
          schedule_timezone?: string
          trigger_conditions?: any
          data_source_filters?: any
          content_requirements?: any
          max_generations_per_day?: number
          max_generations_per_week?: number
          min_days_between_similar?: number
          min_quality_threshold?: number
          auto_publish_threshold?: number
          require_medical_review?: boolean
          is_active?: boolean
          success_count?: number
          failure_count?: number
          last_triggered?: string
          next_scheduled_run?: string
          created_at?: string
          updated_at?: string
        }
        Insert: {
          id?: string
          rule_name: string
          description?: string
          template_id: string
          trigger_type: string
          schedule_expression?: string
          schedule_timezone?: string
          trigger_conditions?: any
          data_source_filters?: any
          content_requirements?: any
          max_generations_per_day?: number
          max_generations_per_week?: number
          min_days_between_similar?: number
          min_quality_threshold?: number
          auto_publish_threshold?: number
          require_medical_review?: boolean
          is_active?: boolean
          success_count?: number
          failure_count?: number
          last_triggered?: string
          next_scheduled_run?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          rule_name?: string
          description?: string
          template_id?: string
          trigger_type?: string
          schedule_expression?: string
          schedule_timezone?: string
          trigger_conditions?: any
          data_source_filters?: any
          content_requirements?: any
          max_generations_per_day?: number
          max_generations_per_week?: number
          min_days_between_similar?: number
          min_quality_threshold?: number
          auto_publish_threshold?: number
          require_medical_review?: boolean
          is_active?: boolean
          success_count?: number
          failure_count?: number
          last_triggered?: string
          next_scheduled_run?: string
          created_at?: string
          updated_at?: string
        }
      }
      blog_content_generation_feedback: {
        Row: {
          id: string
          post_id: string
          job_id?: string
          feedback_source: string
          feedback_user_id?: string
          feedback_type: string
          feedback_rating?: number
          feedback_text?: string
          improvement_suggestions?: any
          action_taken?: string
          resolved?: boolean
          resolution_notes?: string
          created_at?: string
          updated_at?: string
        }
        Insert: {
          id?: string
          post_id: string
          job_id?: string
          feedback_source: string
          feedback_user_id?: string
          feedback_type: string
          feedback_rating?: number
          feedback_text?: string
          improvement_suggestions?: any
          action_taken?: string
          resolved?: boolean
          resolution_notes?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          post_id?: string
          job_id?: string
          feedback_source?: string
          feedback_user_id?: string
          feedback_type?: string
          feedback_rating?: number
          feedback_text?: string
          improvement_suggestions?: any
          action_taken?: string
          resolved?: boolean
          resolution_notes?: string
          created_at?: string
          updated_at?: string
        }
      }
      // Add other tables as needed
    }
  }
} // <-- Add missing closing brace for Database type

// --- Environment Variables ---
// Ensure these are defined in your .env.local file
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// --- Client Creation ---

// Singleton instance for client-side usage (using Anon Key)
// Use a function to ensure env vars are loaded
let supabaseBrowserClient: SupabaseClient<Database> | null = null;

function getBrowserClient(): SupabaseClient<Database> {
  if (supabaseBrowserClient) {
    logger.debug('SupabaseClient', 'Returning existing singleton Supabase browser client');
    return supabaseBrowserClient;
  }

  if (!supabaseUrl) {
    const error = "Supabase URL is required. Missing NEXT_PUBLIC_SUPABASE_URL in .env.local";
    logger.error('SupabaseClient', error);
    throw new Error(error);
  }
  if (!supabaseAnonKey) {
    // This is likely the cause of the "supabaseKey is required" error
    const error = "Supabase Anon Key is required. Missing NEXT_PUBLIC_SUPABASE_ANON_KEY in .env.local";
    logger.error('SupabaseClient', error);
    throw new Error(error);
  }

  try {
    logger.info('SupabaseClient', 'Initializing singleton Supabase browser client...');
    logger.debug('SupabaseClient', `Using URL: ${supabaseUrl.substring(0, 20)}...`);
    
    supabaseBrowserClient = createClient<Database>(supabaseUrl, supabaseAnonKey, {
      auth: {
        // Configure client-side auth persistence as needed
        persistSession: true, 
        autoRefreshToken: true,
        detectSessionInUrl: true // Important for OAuth redirects
      }
    });
    
    logger.info('SupabaseClient', 'Singleton Supabase browser client initialized successfully');
    return supabaseBrowserClient;
  } catch (error) {
    logger.error('SupabaseClient', 'Failed to initialize Supabase client:', error);
    throw error;
  }
}

// Export the singleton instance directly for convenience in client components
// Note: This relies on top-level execution in the browser environment.
// If issues arise (e.g., during SSR), use createBrowserClient() instead.
export const supabase = getBrowserClient(); 

// Function to create a new browser client instance if needed (e.g., for specific contexts)
// DEPRECATED: Prefer using the singleton 'supabase' export for consistency.
// Kept for potential backward compatibility if used elsewhere, but should be refactored.
export const createBrowserClient = (): SupabaseClient<Database> => {
  logger.warn('SupabaseClient', 'DEPRECATED: createBrowserClient() is called. Prefer using the exported "supabase" singleton.');
  if (!supabaseUrl) {
    const error = "Missing NEXT_PUBLIC_SUPABASE_URL";
    logger.error('SupabaseClient', error);
    throw new Error(error);
  }
  if (!supabaseAnonKey) {
    const error = "Missing NEXT_PUBLIC_SUPABASE_ANON_KEY";
    logger.error('SupabaseClient', error);
    throw new Error(error);
  }
  
  try {
    logger.debug('SupabaseClient', 'Creating new Supabase browser client instance');
    return createClient<Database>(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: true, 
        autoRefreshToken: true,
        detectSessionInUrl: true 
      }
    });
  } catch (error) {
    logger.error('SupabaseClient', 'Failed to create new Supabase client instance:', error);
    throw error;
  }
};


// --- Server-Side Client Creation (Example - Adapt as needed) ---
// These should ideally live in separate files or be handled by Supabase Auth Helpers (@supabase/auth-helpers-nextjs)

// Example: Function to create a client for Server Components (using Auth Helpers is recommended)
// import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
// import { cookies } from 'next/headers';
// export const createSupabaseServerClient = () => {
//   const cookieStore = cookies();
//   return createServerComponentClient<Database>({ cookies: () => cookieStore });
// }

// Example: Function to create a client for API Routes (using Auth Helpers is recommended)
// import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
// import { cookies } from 'next/headers';
// export const createSupabaseRouteHandlerClient = () => {
//   const cookieStore = cookies();
//   return createRouteHandlerClient<Database>({ cookies: () => cookieStore });
// }

// Example: Function to create a Service Role client (ONLY FOR SERVER-SIDE)
// Ensure SUPABASE_SERVICE_ROLE_KEY is defined in .env.local (NOT prefixed with NEXT_PUBLIC_)
export const createServiceRoleClient = (): SupabaseClient<Database> => {
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  if (!supabaseUrl) {
    const error = "Missing NEXT_PUBLIC_SUPABASE_URL";
    logger.error('SupabaseClient', error);
    throw new Error(error);
  }
  if (!serviceRoleKey) {
    const error = "Missing SUPABASE_SERVICE_ROLE_KEY";
    logger.error('SupabaseClient', error);
    throw new Error(error);
  }

  try {
    logger.debug('SupabaseClient', 'Creating Supabase service role client');
    // Use { auth: { persistSession: false } } for service role clients
    return createClient<Database>(supabaseUrl, serviceRoleKey, {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      }
    });
  } catch (error) {
    logger.error('SupabaseClient', 'Failed to create Supabase service role client:', error);
    throw error;
  }
};


// --- Deprecated Exports (Remove if not used elsewhere) ---
// These likely contribute to confusion and should be phased out.
// export const createServerClient = createServiceRoleClient; // Incorrectly named previously
// export const createActionClient = createServiceRoleClient; // Incorrectly named previously
// export const createBrowserSupabaseClient = createBrowserClient;
// export const createServerSupabaseClient = createServiceRoleClient; // Incorrectly named previously


// --- Cached Data Functions (Keep if used) ---
// Consider moving these to a dedicated data service file.
// <-- Remove extraneous closing brace from here

// Cache for frequently accessed data
let cachedCountries: any[] | null = null
let cachedSpecialties: any[] | null = null
let cachedDoctors: any[] | null = null
let cachedHospitals: any[] | null = null

// Helper functions to get cached data
export const getCachedCountries = async () => {
  if (cachedCountries) return cachedCountries

  try {
    const { data, error } = await supabase.from("countries").select("*")
    if (error) throw error
    cachedCountries = data
    return data
  } catch (error) {
    console.error("Error fetching countries:", error)
    return []
  }
}

export const getCachedSpecialties = async () => {
  if (cachedSpecialties) return cachedSpecialties

  try {
    const { data, error } = await supabase.from("specialties").select("*")
    if (error) throw error
    cachedSpecialties = data
    return data
  } catch (error) {
    console.error("Error fetching specialties:", error)
    return []
  }
}

export const getCachedDoctors = async () => {
  if (cachedDoctors) return cachedDoctors

  try {
    const { data, error } = await supabase.from("doctors").select("*")
    if (error) throw error
    cachedDoctors = data
    return data
  } catch (error) {
    console.error("Error fetching doctors:", error)
    return []
  }
}

export const getCachedHospitals = async () => {
  if (cachedHospitals) return cachedHospitals

  try {
    const { data, error } = await supabase.from("hospitals").select("*")
    if (error) throw error
    cachedHospitals = data
    return data
  } catch (error) {
    console.error("Error fetching hospitals:", error)
    return []
  }
}

// Helper functions for accessing data from Supabase
export const getCountryById = async (id: string) => {
  return supabase.from("countries").select("*").eq("country_id", id).single()
}

export const getSpecialtyById = async (id: string) => {
  return supabase.from("specialties").select("*").eq("specialty_id", id).single()
}

export const getDoctorById = async (id: string) => {
  return supabase.from("doctors").select("*").eq("doctor_id", id).single()
}

export const getHospitalById = async (id: string) => {
  return supabase.from("hospitals").select("*").eq("hospital_id", id).single()
}

export const getCountriesBySpecialty = async (specialtyId: string) => {
  // Query the appropriate join table or relation in your database
  const { data: doctorData } = await supabase.from("doctors").select("country_id").eq("specialty_id", specialtyId)
  const countryIds = [...new Set((doctorData || []).map(d => d.country_id))]
  
  if (countryIds.length > 0) {
    return supabase.from("countries").select("*").in("country_id", countryIds)
  }
  
  return { data: [], error: null }
}

export const getSpecialtiesByCountry = async (countryId: string) => {
  // Query the appropriate join table or relation in your database
  const { data: doctorData } = await supabase.from("doctors").select("specialty_id").eq("country_id", countryId)
  const specialtyIds = [...new Set((doctorData || []).map(d => d.specialty_id))]
  
  if (specialtyIds.length > 0) {
    return supabase.from("specialties").select("*").in("specialty_id", specialtyIds)
  }
  
  return { data: [], error: null }
}

export const getDoctorsByCountryAndSpecialty = async (countryId: string, specialtyId: string) => {
  return supabase.from("doctors").select("*").eq("country_id", countryId).eq("specialty_id", specialtyId)
}

export const getHospitalsByCountry = async (countryId: string) => {
  return supabase.from("hospitals").select("*").eq("country_id", countryId)
}

export const searchDoctors = async (query: string) => {
  query = query.toLowerCase()
  return supabase.from("doctors").select("*").or(`fullname.ilike.%${query}%,facility.ilike.%${query}%,specialty.ilike.%${query}%`)
}

export const searchHospitals = async (query: string) => {
  query = query.toLowerCase()
  return supabase.from("hospitals").select("*").or(`hospital_name.ilike.%${query}%,city.ilike.%${query}%,address.ilike.%${query}%`)
}
