import { supabase } from "./supabase-client"
import { FALL<PERSON>CK_COUNTRIES, FALLBACK_SPECIALTIES, FALLBACK_DOCTORS } from "./fallback-data"

export async function getDoctorsByCountryAndSpecialty(countryId: string, specialtyId: string) {
  if (!supabase) {
    console.warn("Supabase client not initialized, returning fallback doctors")
    return FALLBACK_DOCTORS.filter(
      (doctor) =>
        doctor.country_id === Number.parseInt(countryId) && doctor.specialty_id === Number.parseInt(specialtyId),
    )
  }

  try {
    const { data, error } = await supabase
      .from("doctors")
      .select(`*, hospitals(hospital_name)`)
      .eq("country_id", countryId)
      .eq("specialty_id", specialtyId)

    if (error) {
      console.error("Error fetching doctors:", error)
      return FALLBACK_DOCTORS.filter(
        (doctor) =>
          doctor.country_id === Number.parseInt(countryId) && doctor.specialty_id === Number.parseInt(specialtyId),
      )
    }

    return data || []
  } catch (error) {
    console.error("Error fetching doctors:", error)
    return FALLBACK_DOCTORS.filter(
      (doctor) =>
        doctor.country_id === Number.parseInt(countryId) && doctor.specialty_id === Number.parseInt(specialtyId),
    )
  }
}

export async function getCountryById(countryId: string) {
  if (!supabase) {
    console.warn("Supabase client not initialized, returning fallback country")
    return FALLBACK_COUNTRIES.find((country) => country.country_id === Number.parseInt(countryId))
  }

  try {
    const { data, error } = await supabase.from("countries").select("*").eq("country_id", countryId).single()

    if (error) {
      console.error("Error fetching country:", error)
      return FALLBACK_COUNTRIES.find((country) => country.country_id === Number.parseInt(countryId))
    }

    return data
  } catch (error) {
    console.error("Error fetching country:", error)
    return FALLBACK_COUNTRIES.find((country) => country.country_id === Number.parseInt(countryId))
  }
}

export async function getSpecialtyById(specialtyId: string) {
  if (!supabase) {
    console.warn("Supabase client not initialized, returning fallback specialty")
    return FALLBACK_SPECIALTIES.find((specialty) => specialty.specialty_id === Number.parseInt(specialtyId))
  }

  try {
    const { data, error } = await supabase.from("specialties").select("*").eq("specialty_id", specialtyId).single()

    if (error) {
      console.error("Error fetching specialty:", error)
      return FALLBACK_SPECIALTIES.find((specialty) => specialty.specialty_id === Number.parseInt(specialtyId))
    }

    return data
  } catch (error) {
    console.error("Error fetching specialty:", error)
    return FALLBACK_SPECIALTIES.find((specialty) => specialty.specialty_id === Number.parseInt(specialtyId))
  }
}

export async function getDoctorsBySpecialtyAndCountry(countryId: string, specialtyName: string) {
  if (!supabase) {
    console.warn("Supabase client not initialized, returning fallback doctors")
    return FALLBACK_DOCTORS.filter(
      (doctor) => doctor.country_id === Number.parseInt(countryId) && doctor.specialty === specialtyName,
    )
  }

  try {
    const { data, error } = await supabase
      .from("doctors")
      .select(`*, hospitals(hospital_name)`)
      .eq("country_id", countryId)
      .eq("specialty", specialtyName)

    if (error) {
      console.error("Error fetching doctors:", error)
      return FALLBACK_DOCTORS.filter(
        (doctor) => doctor.country_id === Number.parseInt(countryId) && doctor.specialty === specialtyName,
      )
    }

    return data || []
  } catch (error) {
    console.error("Error fetching doctors:", error)
    return FALLBACK_DOCTORS.filter(
      (doctor) => doctor.country_id === Number.parseInt(countryId) && doctor.specialty === specialtyName,
    )
  }
}

