// This file sets up the Supabase client for both browser and server environments

// For the browser environment
export const createBrowserClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

  // Dynamic import to avoid issues with SSR
  return import("next/dynamic").then(async ({ default: dynamic }) => {
    const { createClientComponentClient } = await import("@supabase/auth-helpers-nextjs")
    return createClientComponentClient({
      supabaseUrl,
      supabaseKey: supabaseAnonKey,
      options: {
        db: {
          schema: "public",
        },
        // Set auth options during Client initialization
        global: {
          fetch: (url: RequestInfo | URL, init?: RequestInit) => {
            // Custom fetch options if needed
            return fetch(url, { ...init, cache: 'no-store' });
          },
        },
      },
      // Set cookie options to disable persistence
      cookies: {
        options: {
          maxAge: 60 * 60, // Short session timeout (1 hour)
          sameSite: 'lax',
          secure: process.env.NODE_ENV === 'production',
        }
      }
    })
  })
}

// For server components and API routes
export const createServerClient = async () => {
  const { createServerComponentClient } = await import("@supabase/auth-helpers-nextjs")
  const { cookies } = await import("next/headers")

  return createServerComponentClient({
    cookies,
  })
}

// For server actions
export const createActionClient = async () => {
  const { createServerActionClient } = await import("@supabase/auth-helpers-nextjs")
  const { cookies } = await import("next/headers")

  return createServerActionClient({
    cookies,
  })
}

