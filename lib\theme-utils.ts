/**
 * Theme-aware utility classes and migration helpers
 * This file provides mappings from hardcoded dark theme styles to semantic theme-aware classes
 */

export const themeClassMappings = {
  // Text colors
  'text-white': 'text-foreground',
  'text-white/70': 'text-muted-green',
  'text-white/60': 'text-muted-green',
  'text-white/90': 'text-foreground',
  'text-white/80': 'text-foreground/80',
  'text-white/50': 'text-muted-green',
  
  // Background colors
  'bg-white/10': 'bg-card',
  'bg-white/5': 'bg-muted-green/50',
  'bg-white/20': 'bg-accent',
  'bg-black': 'bg-background',
  'bg-black/90': 'bg-background/90',
  'bg-black/95': 'bg-background/95',
  
  // Border colors
  'border-white/20': 'border-border',
  'border-white/30': 'border-border',
  'border-white/10': 'border-border/50',
  
  // Hover states
  'hover:bg-white/10': 'hover:bg-accent',
  'hover:bg-white/20': 'hover:bg-accent',
  'hover:text-white': 'hover:text-foreground',
  
  // Button variants - commonly used patterns
  'border-white/30 text-white hover:bg-white/10': 'border-border text-foreground hover:bg-accent',
  'bg-white/10 border-white/20 text-white': 'bg-card border-border text-card-foreground',
}

/**
 * Get theme-aware class for a given hardcoded class
 */
export function getThemeClass(hardcodedClass: string): string {
  return themeClassMappings[hardcodedClass as keyof typeof themeClassMappings] || hardcodedClass
}

/**
 * Replace multiple hardcoded classes with theme-aware equivalents
 */
export function replaceThemeClasses(classString: string): string {
  let result = classString
  
  // Replace each hardcoded class with its theme-aware equivalent
  Object.entries(themeClassMappings).forEach(([hardcoded, themeAware]) => {
    // Use word boundaries to avoid partial matches
    const regex = new RegExp(`\\b${hardcoded.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g')
    result = result.replace(regex, themeAware)
  })
  
  return result
}

/**
 * Common theme-aware component class patterns
 */
export const themeComponents = {
  // Card patterns
  card: 'bg-card border-border text-card-foreground',
  cardHeader: 'text-card-foreground',
  
  // Button patterns
  buttonOutline: 'border-border text-foreground hover:bg-accent hover:text-accent-foreground',
  buttonGhost: 'text-foreground hover:bg-accent hover:text-accent-foreground',
  
  // Input patterns
  input: 'bg-background border-border text-foreground placeholder:text-muted-green',
  textarea: 'bg-background border-border text-foreground placeholder:text-muted-green',
  
  // Navigation patterns
  navItem: 'text-foreground/90 hover:text-foreground hover:bg-accent',
  navItemActive: 'text-foreground bg-accent',
  
  // Modal/Dialog patterns
  modalOverlay: 'bg-background/80 backdrop-blur-sm',
  modalContent: 'bg-card border-border text-card-foreground',
  
  // Badge patterns
  badgeSecondary: 'bg-secondary text-secondary-foreground',
  badgeOutline: 'border-border text-foreground',
  
  // Alert patterns
  alertInfo: 'bg-info/10 border-info/20 text-info-foreground',
  alertSuccess: 'bg-success/10 border-success/20 text-success-foreground',
  alertWarning: 'bg-warning/10 border-warning/20 text-warning-foreground',
  alertError: 'bg-error/10 border-error/20 text-error-foreground',
}

/**
 * Utility to generate theme-aware classes for common components
 */
export function cn(...classes: (string | undefined)[]): string {
  return classes.filter(Boolean).join(' ')
}

/**
 * Convert a component's className to be theme-aware
 */
export function makeThemeAware(className: string): string {
  return replaceThemeClasses(className)
}

/**
 * Common color palette for theme-aware design
 */
export const themeColors = {
  primary: 'hsl(var(--primary))',
  primaryForeground: 'hsl(var(--primary-foreground))',
  secondary: 'hsl(var(--secondary))',
  secondaryForeground: 'hsl(var(--secondary-foreground))',
  accent: 'hsl(var(--accent))',
  accentForeground: 'hsl(var(--accent-foreground))',
  background: 'hsl(var(--background))',
  foreground: 'hsl(var(--foreground))',
  card: 'hsl(var(--card))',
  cardForeground: 'hsl(var(--card-foreground))',
  popover: 'hsl(var(--popover))',
  popoverForeground: 'hsl(var(--popover-foreground))',
  muted: 'hsl(var(--muted))',
  mutedForeground: 'hsl(var(--muted-foreground))',
  border: 'hsl(var(--border))',
  input: 'hsl(var(--input))',
  ring: 'hsl(var(--ring))',
  success: 'hsl(var(--success))',
  successForeground: 'hsl(var(--success-foreground))',
  warning: 'hsl(var(--warning))',
  warningForeground: 'hsl(var(--warning-foreground))',
  error: 'hsl(var(--error))',
  errorForeground: 'hsl(var(--error-foreground))',
  info: 'hsl(var(--info))',
  infoForeground: 'hsl(var(--info-foreground))',
} 