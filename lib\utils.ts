// Import both mock implementations
import { type ClassValue, clsx } from "@/lib/mock-clsx"
import { twMerge } from "@/lib/mock-tailwind-merge"
import { logger } from "@/lib/debug-utils"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Determines if a path is a local file path (Windows or Unix/Linux)
 */
export function isLocalFilePath(path: string | undefined): boolean {
  if (!path) return false;
  // Check for Windows paths or absolute paths
  return path.match(/^([A-Z]:\\|\/)/i) !== null;
}

/**
 * Gets a safe display image URL, replacing local file paths with placeholder
 * Added improved error handling and logging
 */
export function getImageUrl(path: string | undefined, fallback: string = "/placeholder.svg"): string {
  try {
    // Log the path for debugging
    logger.debug('getImageUrl', `Called with path: "${path || 'undefined'}", fallback: "${fallback}"`);
    
    // Handle undefined or null paths
    if (!path) {
      logger.debug('getImageUrl', `Path is undefined or null, returning fallback: "${fallback}"`);
      return fallback;
    }
    
    // Handle local file paths
    if (isLocalFilePath(path)) {
      logger.debug('getImageUrl', `Path is a local file path, returning fallback: "${fallback}"`);
      return fallback;
    }
    
    // Return the valid URL
    logger.debug('getImageUrl', `Returning valid URL: "${path}"`);
    return path;
  } catch (error) {
    logger.error('getImageUrl', 'Error processing image URL:', error);
    return fallback;
  }
}

/**
 * Gets a doctor avatar image URL based on their ID
 * Added improved error handling
 */
export function getDoctorImageUrl(path: string | undefined, doctorId: string | number): string {
  try {
    if (!path || isLocalFilePath(path)) {
      // Use a deterministic doctor image based on ID modulo 5
      const idMod = (typeof doctorId === 'number' ? doctorId : parseInt(doctorId.toString(), 10)) % 5 + 1;
      const fallbackPath = `/doctors/default-doctor-${idMod}.jpg`;
      logger.debug('getDoctorImageUrl', `Using fallback image for doctor ${doctorId}: ${fallbackPath}`);
      return fallbackPath;
    }
    logger.debug('getDoctorImageUrl', `Using provided image path for doctor ${doctorId}: ${path}`);
    return path;
  } catch (error) {
    logger.error('getDoctorImageUrl', `Error generating doctor image URL for doctor ${doctorId}:`, error);
    return '/doctors/default-doctor-1.jpg';
  }
}

/**
 * Checks if a URL is valid and is pointing to an image
 * Can be used to pre-validate image URLs before displaying them
 */
export async function isValidImageUrl(url: string): Promise<boolean> {
  if (!url) return false;
  
  // Check if it's a valid URL format
  try {
    new URL(url);
  } catch (e) {
    logger.debug('isValidImageUrl', `Invalid URL format: ${url}`);
    return false;
  }
  
  // If we're in a browser environment, we can try a HEAD request
  if (typeof window !== 'undefined') {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      if (!response.ok) {
        logger.debug('isValidImageUrl', `URL returned status ${response.status}: ${url}`);
        return false;
      }
      
      // Check if content type is an image
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.startsWith('image/')) {
        logger.debug('isValidImageUrl', `URL is not an image (${contentType}): ${url}`);
        return false;
      }
      
      return true;
    } catch (error) {
      logger.debug('isValidImageUrl', `Error checking URL: ${url}`, error);
      return false;
    }
  }
  
  // In server environment, just assume it's valid if it's a well-formed URL
  return true;
}

/**
 * A more robust image URL getter that checks validity
 * Use this for more critical image displays where you need to ensure validity
 */
export async function getValidatedImageUrl(
  path: string | undefined, 
  fallback: string = "/placeholder.svg"
): Promise<string> {
  try {
    // First apply our regular logic
    const imageUrl = getImageUrl(path, fallback);
    
    // If it's already the fallback, no need to validate
    if (imageUrl === fallback) {
      return fallback;
    }
    
    // Validate the URL
    const isValid = await isValidImageUrl(imageUrl);
    if (!isValid) {
      logger.debug('getValidatedImageUrl', `Image URL validation failed, using fallback: ${fallback}`);
      return fallback;
    }
    
    return imageUrl;
  } catch (error) {
    logger.error('getValidatedImageUrl', 'Error validating image URL:', error);
    return fallback;
  }
}

