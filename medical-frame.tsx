"use client"

import type React from "react"

import { cn } from "@/lib/utils"
import { motion } from "framer-motion"

interface MedicalFrameProps {
  children: React.ReactNode
  className?: string
  variant?: "default" | "success" | "warning" | "patient" | "doctor"
  showPulse?: boolean
}

export function MedicalFrame({ children, className, variant = "default", showPulse = true }: MedicalFrameProps) {
  // Define variant-specific colors and icons
  const variantStyles = {
    default: {
      borderColor: "border-primary",
      gradientFrom: "from-primary/20",
      gradientTo: "to-primary/40",
      iconColor: "text-primary",
    },
    success: {
      borderColor: "border-green-500",
      gradientFrom: "from-green-500/20",
      gradientTo: "to-green-500/40",
      iconColor: "text-green-500",
    },
    warning: {
      borderColor: "border-yellow-500",
      gradientFrom: "from-yellow-500/20",
      gradientTo: "to-yellow-500/40",
      iconColor: "text-yellow-500",
    },
    patient: {
      borderColor: "border-blue-500",
      gradientFrom: "from-blue-500/20",
      gradientTo: "to-blue-500/40",
      iconColor: "text-blue-500",
    },
    doctor: {
      borderColor: "border-primary",
      gradientFrom: "from-primary/20",
      gradientTo: "to-primary/40",
      iconColor: "text-primary",
    },
  }

  const styles = variantStyles[variant]

  return (
    <div
      className={cn(
        "relative p-8 rounded-xl overflow-hidden",
        "border-2",
        styles.borderColor,
        "bg-background/90 backdrop-blur-lg",
        className,
      )}
    >
      {/* Animated medical background pattern */}
      <div className="absolute inset-0 overflow-hidden opacity-5 pointer-events-none">
        <motion.div
          className="absolute inset-0 w-full h-full"
          animate={{
            backgroundPosition: ["0% 0%", "100% 100%"],
          }}
          transition={{
            duration: 20,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M30 10v40M10 30h40' stroke='%2322C55E' strokeWidth='1'/%3E%3C/svg%3E")`,
            backgroundSize: "60px 60px",
          }}
        />
      </div>

      {/* Corner decorations */}
      <div className="absolute top-0 left-0 w-16 h-16">
        <svg viewBox="0 0 64 64" className={cn("w-full h-full", styles.iconColor)}>
          <path d="M2 62C2 28 28 2 62 2" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
          <path d="M14 14v8m-4-4h8" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
        </svg>
      </div>

      <div className="absolute top-0 right-0 w-16 h-16">
        <svg viewBox="0 0 64 64" className={cn("w-full h-full", styles.iconColor)}>
          <path d="M62 62C28 62 2 28 2 2" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
          <path d="M14 14C14 18 18 22 22 22" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
        </svg>
      </div>

      <div className="absolute bottom-0 left-0 w-16 h-16">
        <svg viewBox="0 0 64 64" className={cn("w-full h-full", styles.iconColor)}>
          <path d="M2 2C2 36 28 62 62 62" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
          <path d="M20 44l4-8 4 16 4-8h8" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
        </svg>
      </div>

      <div className="absolute bottom-0 right-0 w-16 h-16">
        <svg viewBox="0 0 64 64" className={cn("w-full h-full", styles.iconColor)}>
          <path d="M62 2C62 36 36 62 2 62" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
          <path d="M44 44c-4 4-12 4-16 0s-4-12 0-16 12-4 16 0" fill="none" stroke="currentColor" strokeWidth="2" />
        </svg>
      </div>

      {/* Animated icons */}
      {/* Pulse effect */}
      {showPulse && (
        <motion.div
          className={cn(
            "absolute inset-0",
            "bg-gradient-to-r",
            styles.gradientFrom,
            styles.gradientTo,
            "opacity-0 rounded-xl",
          )}
          animate={{
            opacity: [0, 0.15, 0],
          }}
          transition={{
            duration: 2.5,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        />
      )}

      {/* Inner border glow */}
      <div
        className={cn(
          "absolute inset-[1px] rounded-xl pointer-events-none",
          "bg-gradient-to-r",
          styles.gradientFrom,
          styles.gradientTo,
          "opacity-20",
        )}
      />

      {/* Content */}
      <div className="relative z-10">{children}</div>
    </div>
  )
}

