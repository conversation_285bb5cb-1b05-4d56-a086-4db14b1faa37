import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { jwtVerify } from 'jose';

// Routes that require authentication
const PROTECTED_ROUTES = [
  '/dashboard',
  '/profile',
  '/appointments',
  '/messages',
  '/settings',
];

// Routes that are only accessible if NOT authenticated
const AUTH_ROUTES = [
  '/login',
  '/register',
  '/forgot-password',
];

export async function middleware(request: NextRequest) {
  const token = request.cookies.get('auth_token')?.value;
  const { pathname } = request.nextUrl;

  // Check if route requires auth
  const isProtectedRoute = PROTECTED_ROUTES.some(route => pathname.startsWith(route));
  const isAuthRoute = AUTH_ROUTES.some(route => pathname.startsWith(route));

  try {
    // If no token and trying to access protected route, redirect to login
    if (!token && isProtectedRoute) {
      const url = new URL('/login', request.url);
      url.searchParams.set('redirect', encodeURIComponent(pathname));
      return NextResponse.redirect(url);
    }

    // If token exists, verify it
    if (token) {
      try {
        // Verify token - use same secret as in login API
        const secret = new TextEncoder().encode(process.env.JWT_SECRET || 'fallback_secret_for_development_only');
        const { payload } = await jwtVerify(token, secret);

        // If authenticated and trying to access auth routes (login, register, etc.), redirect to dashboard
        if (isAuthRoute) {
          return NextResponse.redirect(new URL('/dashboard', request.url));
        }
      } catch (error) {
        // Token is invalid - clear it
        const response = NextResponse.redirect(new URL('/login', request.url));
        response.cookies.delete('auth_token');
        return response;
      }
    }

    // Otherwise, continue
    return NextResponse.next();
  } catch (error) {
    console.error('Middleware error:', error);
    return NextResponse.next();
  }
}

// Only run middleware on specific paths
export const config = {
  matcher: [
    /*
     * Match all request paths except for:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (e.g. robots.txt)
     * - api routes (they handle their own auth)
     */
    '/((?!_next/static|_next/image|favicon.ico|public|api).*)',
  ],
};

