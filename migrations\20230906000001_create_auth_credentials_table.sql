-- Migration: create_auth_credentials_table
-- Description: Creates the auth_credentials table for custom authentication

CREATE TABLE IF NOT EXISTS public.auth_credentials (
    id SERIAL PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    hashed_password TEXT NOT NULL,
    user_profile_id INTEGER NOT NULL,
    user_type TEXT NOT NULL CHECK (user_type IN ('patient', 'doctor')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_auth_credentials_email ON public.auth_credentials(email);
CREATE INDEX idx_auth_credentials_user_profile_id ON public.auth_credentials(user_profile_id);

-- Add comments
COMMENT ON TABLE public.auth_credentials IS 'Stores custom authentication credentials separate from Supabase Auth';
COMMENT ON COLUMN public.auth_credentials.email IS 'User email address used for login';
COMMENT ON COLUMN public.auth_credentials.hashed_password IS 'Bcrypt-hashed password';
COMMENT ON COLUMN public.auth_credentials.user_profile_id IS 'ID reference to user profile in users or doctors table';
COMMENT ON COLUMN public.auth_credentials.user_type IS 'Type of user (patient or doctor)';

-- Enable Row Level Security
ALTER TABLE public.auth_credentials ENABLE ROW LEVEL SECURITY;

-- Create policy to restrict access to auth_credentials
-- Only authenticated users with service_role can access the auth_credentials table
CREATE POLICY "Auth credentials are accessible only by service role" ON public.auth_credentials
    USING (auth.jwt() IS NOT NULL AND auth.jwt()->>'role' = 'service_role');

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_auth_credentials_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update updated_at automatically
CREATE TRIGGER update_auth_credentials_updated_at_trigger
BEFORE UPDATE ON public.auth_credentials
FOR EACH ROW
EXECUTE FUNCTION update_auth_credentials_updated_at(); 