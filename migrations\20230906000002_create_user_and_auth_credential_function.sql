-- Migration: create_user_and_auth_credential_function
-- Description: Creates a function to insert users and authentication credentials in one transaction

CREATE OR REPLACE FUNCTION create_user_and_auth_credential(
    p_email TEXT,
    p_hashed_password TEXT,
    p_user_type TEXT,
    -- Patient profile fields
    p_username TEXT,
    p_first_name TEXT,
    p_last_name TEXT,
    p_gender TEXT,
    p_patient_city TEXT,
    p_patient_country TEXT,
    p_age INTEGER,
    p_patient_phone_number TEXT,
    p_medical_condition TEXT,
    p_patient_state_province_region TEXT,
    -- Doctor profile fields
    p_fullname TEXT,
    p_facility TEXT,
    p_medical_title TEXT,
    p_specialty TEXT,
    p_subspecialty TEXT,
    p_educational_background TEXT,
    p_board_certifications TEXT,
    p_experience INTEGER,
    p_publications TEXT,
    p_awards_recognitions TEXT,
    p_phone_number TEXT,
    p_languages_spoken TEXT,
    p_professional_affiliations TEXT,
    p_procedures_performed TEXT,
    p_treatment_services_expertise TEXT,
    p_hospital_id INTEGER
)
RETURNS TABLE(profile_id INTEGER) AS $$
DECLARE
    v_profile_id INTEGER;
BEGIN
    -- Validate user type
    IF p_user_type NOT IN ('patient', 'doctor') THEN
        RAISE EXCEPTION 'Invalid user type: %', p_user_type;
    END IF;

    -- For debug purposes
    RAISE NOTICE 'Creating % with email %', p_user_type, p_email;

    -- Create transaction
    BEGIN
        -- Insert user profile data based on user type
        IF p_user_type = 'patient' THEN
            -- Insert into users table (patient)
            INSERT INTO public.users (
                username,
                email,
                "First_Name",
                "Last_Name",
                gender,
                city,
                country,
                age,
                "Phone_Number",
                "medical condition",
                "State/Province/Region"
            ) VALUES (
                p_username,
                p_email,
                p_first_name,
                p_last_name,
                p_gender,
                p_patient_city,
                p_patient_country,
                p_age,
                p_patient_phone_number,
                p_medical_condition,
                p_patient_state_province_region
            )
            RETURNING id INTO v_profile_id;
        ELSE
            -- Insert into doctors table
            BEGIN
                -- Log fields
                RAISE NOTICE 'Doctor parameters: fullname=%, facility=%, specialty=%, background=%, phone=%, exp=%', 
                    p_fullname, p_facility, p_specialty, p_educational_background, p_phone_number, p_experience;
                
                INSERT INTO public.doctors (
                    fullname,
                    facility,
                    medical_title,
                    specialty,
                    subspecialty,
                    educational_background,
                    board_certifications,
                    experience,
                    publications,
                    awards_recognitions,
                    phone_number,
                    email,
                    languages_spoken,
                    professional_affiliations,
                    procedures_performed,
                    treatment_services_expertise,
                    hospital_id,
                    password,
                    wins,
                    losses,
                    draws,
                    form,
                    rating,
                    review_count
                ) VALUES (
                    p_fullname,
                    p_facility,
                    p_medical_title,
                    p_specialty,
                    p_subspecialty,
                    p_educational_background,
                    p_board_certifications,
                    p_experience,
                    p_publications,
                    p_awards_recognitions,
                    p_phone_number,
                    p_email,
                    p_languages_spoken,
                    p_professional_affiliations,
                    p_procedures_performed,
                    p_treatment_services_expertise,
                    p_hospital_id,
                    p_hashed_password,
                    0, -- wins
                    0, -- losses
                    0, -- draws
                    'New', -- form
                    5.0, -- rating
                    0  -- review_count
                )
                RETURNING doctor_id INTO v_profile_id;
            EXCEPTION
                WHEN OTHERS THEN
                    RAISE EXCEPTION 'An unexpected error occurred in doctor insert: %', SQLERRM;
            END;
        END IF;

        -- Insert auth credentials
        BEGIN
            INSERT INTO public.auth_credentials (
                email,
                hashed_password,
                user_profile_id,
                user_type
            ) VALUES (
                p_email,
                p_hashed_password,
                v_profile_id,
                p_user_type
            );
        EXCEPTION
            WHEN OTHERS THEN
                RAISE EXCEPTION 'An unexpected error occurred in auth_credentials insert: %', SQLERRM;
        END;

        -- Return the profile ID
        RETURN QUERY SELECT v_profile_id;
    EXCEPTION
        WHEN OTHERS THEN
            -- Roll back on error
            RAISE EXCEPTION 'An unexpected error occurred in create_user_and_auth_credential: %', SQLERRM;
    END;
END;
$$ LANGUAGE plpgsql; 