-- Migration: create_password_reset_tokens_table
-- Description: Creates the password_reset_tokens table for custom password reset

CREATE TABLE IF NOT EXISTS public.password_reset_tokens (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    email TEXT NOT NULL,
    token TEXT NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    user_type TEXT NOT NULL CHECK (user_type IN ('patient', 'doctor'))
);

-- Create indexes for better performance
CREATE INDEX idx_password_reset_tokens_token ON public.password_reset_tokens(token);
CREATE INDEX idx_password_reset_tokens_user_id ON public.password_reset_tokens(user_id);
CREATE INDEX idx_password_reset_tokens_email ON public.password_reset_tokens(email);

-- Add comments
COMMENT ON TABLE public.password_reset_tokens IS 'Stores password reset tokens for custom authentication';
COMMENT ON COLUMN public.password_reset_tokens.token IS 'Unique token for password reset';
COMMENT ON COLUMN public.password_reset_tokens.expires_at IS 'Expiration timestamp for the token';
COMMENT ON COLUMN public.password_reset_tokens.used IS 'Whether the token has been used';

-- Enable Row Level Security
ALTER TABLE public.password_reset_tokens ENABLE ROW LEVEL SECURITY;

-- Create policy to restrict access to password_reset_tokens
-- Only authenticated users with service_role can access the password_reset_tokens table
CREATE POLICY "Password reset tokens are accessible only by service role" ON public.password_reset_tokens
    USING (auth.jwt() IS NOT NULL AND auth.jwt()->>'role' = 'service_role');

-- Create a function to clean up expired tokens (run as a cron job)
CREATE OR REPLACE FUNCTION cleanup_expired_reset_tokens()
RETURNS void AS $$
BEGIN
    DELETE FROM public.password_reset_tokens
    WHERE expires_at < NOW() OR used = TRUE;
END;
$$ LANGUAGE plpgsql;

-- Add comment to the function
COMMENT ON FUNCTION cleanup_expired_reset_tokens IS 'Removes expired or used password reset tokens'; 