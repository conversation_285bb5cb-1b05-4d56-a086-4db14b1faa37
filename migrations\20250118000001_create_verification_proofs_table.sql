-- Migration: create_verification_proofs_table
-- Description: Creates the verification_proofs table for storing appointment receipt images

-- Create the verification_proofs table
CREATE TABLE IF NOT EXISTS public.verification_proofs (
    id SERIAL PRIMARY KEY,
    review_id INTEGER NOT NULL REFERENCES public.reviews(review_id) ON DELETE CASCADE,
    image_path TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_verification_proofs_review_id ON public.verification_proofs(review_id);
CREATE INDEX IF NOT EXISTS idx_verification_proofs_created_at ON public.verification_proofs(created_at);

-- Add comments for documentation
COMMENT ON TABLE public.verification_proofs IS 'Stores appointment receipt images for review verification';
COMMENT ON COLUMN public.verification_proofs.review_id IS 'Foreign key to the reviews table';
COMMENT ON COLUMN public.verification_proofs.image_path IS 'Path to the proof image in Supabase storage';
COMMENT ON COLUMN public.verification_proofs.created_at IS 'Timestamp when the proof was uploaded';
COMMENT ON COLUMN public.verification_proofs.updated_at IS 'Timestamp when the record was last updated';

-- Add Row Level Security (RLS) policies
ALTER TABLE public.verification_proofs ENABLE ROW LEVEL SECURITY;

-- Policy: Only authenticated users can view their own verification proofs
CREATE POLICY "Users can view their own verification proofs" ON public.verification_proofs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.reviews 
            WHERE reviews.review_id = verification_proofs.review_id 
            AND reviews.user_id = auth.uid()::integer
        )
    );

-- Policy: Only authenticated users can insert their own verification proofs
CREATE POLICY "Users can insert their own verification proofs" ON public.verification_proofs
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.reviews 
            WHERE reviews.review_id = verification_proofs.review_id 
            AND reviews.user_id = auth.uid()::integer
        )
    );

-- Policy: Only service role can delete verification proofs (for admin cleanup)
CREATE POLICY "Service role can delete verification proofs" ON public.verification_proofs
    FOR DELETE USING (true);

-- Policy: Only service role can update verification proofs
CREATE POLICY "Service role can update verification proofs" ON public.verification_proofs
    FOR UPDATE USING (true);
