-- Migration: create_review_flags_table
-- Description: Creates the review_flags table for reporting inappropriate reviews

-- Create the review_flags table
CREATE TABLE IF NOT EXISTS public.review_flags (
    id SERIAL PRIMARY KEY,
    review_id INTEGER NOT NULL REFERENCES public.reviews(review_id) ON DELETE CASCADE,
    reporter_user_id INTEGER,
    flag_reason TEXT NOT NULL CHECK (flag_reason IN (
        'inappropriate_content',
        'spam',
        'fake_review',
        'personal_attack',
        'off_topic',
        'harassment',
        'other'
    )),
    flag_description TEXT,
    status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'resolved', 'dismissed')),
    admin_notes TEXT,
    resolved_by INTEGER,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_review_flags_review_id ON public.review_flags(review_id);
CREATE INDEX IF NOT EXISTS idx_review_flags_status ON public.review_flags(status);
CREATE INDEX IF NOT EXISTS idx_review_flags_created_at ON public.review_flags(created_at);
CREATE INDEX IF NOT EXISTS idx_review_flags_reporter ON public.review_flags(reporter_user_id);

-- Add comments for documentation
COMMENT ON TABLE public.review_flags IS 'Stores flags/reports for inappropriate reviews';
COMMENT ON COLUMN public.review_flags.review_id IS 'Foreign key to the reviews table';
COMMENT ON COLUMN public.review_flags.reporter_user_id IS 'ID of the user who reported the review (nullable for anonymous reports)';
COMMENT ON COLUMN public.review_flags.flag_reason IS 'Predefined reason for flagging the review';
COMMENT ON COLUMN public.review_flags.flag_description IS 'Additional description provided by the reporter';
COMMENT ON COLUMN public.review_flags.status IS 'Current status of the flag (open, resolved, dismissed)';
COMMENT ON COLUMN public.review_flags.admin_notes IS 'Notes added by admin during review';
COMMENT ON COLUMN public.review_flags.resolved_by IS 'ID of the admin who resolved the flag';
COMMENT ON COLUMN public.review_flags.resolved_at IS 'Timestamp when the flag was resolved';

-- Add Row Level Security (RLS) policies
ALTER TABLE public.review_flags ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view flags they created
CREATE POLICY "Users can view their own flags" ON public.review_flags
    FOR SELECT USING (reporter_user_id = auth.uid()::integer);

-- Policy: Users can create flags
CREATE POLICY "Users can create flags" ON public.review_flags
    FOR INSERT WITH CHECK (
        reporter_user_id = auth.uid()::integer OR reporter_user_id IS NULL
    );

-- Policy: Only service role can update flags (for admin actions)
CREATE POLICY "Service role can update flags" ON public.review_flags
    FOR UPDATE USING (true);

-- Policy: Only service role can delete flags
CREATE POLICY "Service role can delete flags" ON public.review_flags
    FOR DELETE USING (true);

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_review_flags_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER trigger_update_review_flags_updated_at
    BEFORE UPDATE ON public.review_flags
    FOR EACH ROW
    EXECUTE FUNCTION update_review_flags_updated_at();
