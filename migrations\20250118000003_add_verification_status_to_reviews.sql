-- Migration: add_verification_status_to_reviews
-- Description: Adds verification_status column to reviews table if it doesn't exist

-- Add verification_status column to reviews table
ALTER TABLE public.reviews 
ADD COLUMN IF NOT EXISTS verification_status TEXT DEFAULT 'unverified' 
CHECK (verification_status IN ('unverified', 'pending_verification', 'verified', 'rejected'));

-- Create index for better performance on verification status queries
CREATE INDEX IF NOT EXISTS idx_reviews_verification_status ON public.reviews(verification_status);

-- Add comment for documentation
COMMENT ON COLUMN public.reviews.verification_status IS 'Status of review verification: unverified, pending_verification, verified, or rejected';

-- Update existing reviews to have 'unverified' status if they are NULL
UPDATE public.reviews 
SET verification_status = 'unverified' 
WHERE verification_status IS NULL;
