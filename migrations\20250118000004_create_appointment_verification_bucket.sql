-- Migration: create_appointment_verification_bucket
-- Description: Creates the appointment-verification storage bucket and sets up policies

-- Create the storage bucket for appointment verification images
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'appointment-verification',
    'appointment-verification',
    false, -- Private bucket for security
    5242880, -- 5MB file size limit
    ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for the appointment-verification bucket

-- Policy: Allow authenticated users to upload their own verification images
CREATE POLICY "Users can upload verification images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'appointment-verification' 
        AND auth.role() = 'authenticated'
        AND (storage.foldername(name))[1] = 'proofs'
    );

-- Policy: Allow service role to read all verification images (for admin review)
CREATE POLICY "Service role can read verification images" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'appointment-verification'
        AND auth.role() = 'service_role'
    );

-- Policy: Allow service role to delete verification images (for cleanup after decisions)
CREATE POLICY "Service role can delete verification images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'appointment-verification'
        AND auth.role() = 'service_role'
    );

-- Policy: Allow users to read their own verification images
CREATE POLICY "Users can read their own verification images" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'appointment-verification'
        AND auth.role() = 'authenticated'
        AND (storage.foldername(name))[1] = 'proofs'
    );

-- Add comment for documentation
COMMENT ON TABLE storage.buckets IS 'Storage buckets configuration';

-- Note: The bucket policies above ensure that:
-- 1. Only authenticated users can upload images to the proofs folder
-- 2. Only service role (admin) can read all images for moderation
-- 3. Only service role can delete images for privacy compliance
-- 4. Users can read their own uploaded images
