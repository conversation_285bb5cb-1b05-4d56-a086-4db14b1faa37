-- Migration: add_admin_role_to_auth_credentials
-- Description: Adds admin_role column to auth_credentials table for admin user management

-- Add admin_role column to auth_credentials table
ALTER TABLE public.auth_credentials 
ADD COLUMN IF NOT EXISTS admin_role BOOLEAN DEFAULT false;

-- Create index for better performance on admin role queries
CREATE INDEX IF NOT EXISTS idx_auth_credentials_admin_role ON public.auth_credentials(admin_role);

-- Add comment for documentation
COMMENT ON COLUMN public.auth_credentials.admin_role IS 'Indicates if the user has admin privileges for moderation and verification management';

-- Update any existing users that should be admins (optional - you can modify this)
-- Example: UPDATE public.auth_credentials SET admin_role = true WHERE email = '<EMAIL>';
