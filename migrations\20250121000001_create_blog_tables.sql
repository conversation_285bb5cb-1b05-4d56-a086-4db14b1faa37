-- Blog Categories Table
CREATE TABLE IF NOT EXISTS public.blog_categories (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    name character varying(100) NOT NULL UNIQUE,
    slug character varying(100) NOT NULL UNIQUE,
    description text,
    color character varying(7) DEFAULT '#3B82F6', -- Hex color for UI
    icon character varying(50), -- Lucide icon name
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Insert the 4 main categories from documentation
INSERT INTO public.blog_categories (name, slug, description, color, icon, sort_order) VALUES
('Ranking Analysis & Insights', 'ranking-analysis', 'Data-driven analysis of medical professional rankings, trends, and performance insights', '#10B981', 'BarChart3', 1),
('Medical Deep Dives', 'medical-deep-dives', 'In-depth educational content explaining medical procedures, technologies, and treatments', '#3B82F6', 'BookOpen', 2),
('Doctor & Institution Spotlights', 'doctor-spotlights', 'Interviews and features highlighting leading medical professionals and institutions', '#8B5CF6', 'UserCheck', 3),
('Patient & Caregiver Resources', 'patient-resources', 'Practical guides and resources for patients navigating healthcare decisions', '#F59E0B', 'Heart', 4);

-- Blog Authors Table
CREATE TABLE IF NOT EXISTS public.blog_authors (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    name character varying(255) NOT NULL,
    slug character varying(255) NOT NULL UNIQUE,
    email character varying(255) UNIQUE,
    bio text,
    medical_credentials text, -- MD, PhD, etc. for E-E-A-T
    specialties text[], -- Array of specialties
    profile_image_url text,
    social_links jsonb, -- {twitter, linkedin, etc.}
    is_medical_reviewer boolean DEFAULT false,
    is_active boolean DEFAULT true,
    user_id uuid, -- Link to existing user if applicable
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Blog Posts Table
CREATE TABLE IF NOT EXISTS public.blog_posts (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    title character varying(500) NOT NULL,
    slug character varying(500) NOT NULL UNIQUE,
    excerpt text,
    content text NOT NULL,
    featured_image_url text,
    featured_image_alt text,
    category_id uuid NOT NULL REFERENCES public.blog_categories(id) ON DELETE RESTRICT,
    author_id uuid NOT NULL REFERENCES public.blog_authors(id) ON DELETE RESTRICT,
    medical_reviewer_id uuid REFERENCES public.blog_authors(id) ON DELETE SET NULL,
    status character varying(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    published_at timestamp with time zone,
    meta_title character varying(60), -- SEO title
    meta_description character varying(160), -- SEO description
    reading_time_minutes integer,
    view_count integer DEFAULT 0,
    is_featured boolean DEFAULT false,
    is_trending boolean DEFAULT false,
    related_doctor_ids integer[], -- Array of related doctor IDs
    related_team_ids integer[], -- Array of related team IDs
    structured_data jsonb, -- Schema.org structured data
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Blog Tags Table
CREATE TABLE IF NOT EXISTS public.blog_tags (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    name character varying(100) NOT NULL UNIQUE,
    slug character varying(100) NOT NULL UNIQUE,
    description text,
    color character varying(7) DEFAULT '#6B7280',
    usage_count integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Blog Post Tags Junction Table
CREATE TABLE IF NOT EXISTS public.blog_post_tags (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id uuid NOT NULL REFERENCES public.blog_posts(id) ON DELETE CASCADE,
    tag_id uuid NOT NULL REFERENCES public.blog_tags(id) ON DELETE CASCADE,
    created_at timestamp with time zone DEFAULT now(),
    UNIQUE(post_id, tag_id)
);

-- Blog Comments Table (for future expansion)
CREATE TABLE IF NOT EXISTS public.blog_comments (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id uuid NOT NULL REFERENCES public.blog_posts(id) ON DELETE CASCADE,
    parent_id uuid REFERENCES public.blog_comments(id) ON DELETE CASCADE,
    author_name character varying(255) NOT NULL,
    author_email character varying(255) NOT NULL,
    content text NOT NULL,
    status character varying(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'spam')),
    ip_address character varying(45),
    user_agent text,
    is_verified_user boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_blog_posts_category_id ON public.blog_posts(category_id);
CREATE INDEX IF NOT EXISTS idx_blog_posts_author_id ON public.blog_posts(author_id);
CREATE INDEX IF NOT EXISTS idx_blog_posts_status ON public.blog_posts(status);
CREATE INDEX IF NOT EXISTS idx_blog_posts_published_at ON public.blog_posts(published_at DESC);
CREATE INDEX IF NOT EXISTS idx_blog_posts_slug ON public.blog_posts(slug);
CREATE INDEX IF NOT EXISTS idx_blog_posts_featured ON public.blog_posts(is_featured);
CREATE INDEX IF NOT EXISTS idx_blog_post_tags_post_id ON public.blog_post_tags(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_tags_tag_id ON public.blog_post_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_blog_comments_post_id ON public.blog_comments(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_authors_slug ON public.blog_authors(slug);
CREATE INDEX IF NOT EXISTS idx_blog_categories_slug ON public.blog_categories(slug);

-- Create a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply the trigger to relevant tables
CREATE TRIGGER update_blog_posts_updated_at BEFORE UPDATE ON public.blog_posts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blog_authors_updated_at BEFORE UPDATE ON public.blog_authors
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blog_categories_updated_at BEFORE UPDATE ON public.blog_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blog_tags_updated_at BEFORE UPDATE ON public.blog_tags
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert a sample medical author for initial setup
INSERT INTO public.blog_authors (name, slug, bio, medical_credentials, is_medical_reviewer, is_active) VALUES
('Dr. Medical Editor', 'dr-medical-editor', 'Chief Medical Editor with oversight of all medical content for E-E-A-T compliance.', 'MD, Board Certified', true, true); 