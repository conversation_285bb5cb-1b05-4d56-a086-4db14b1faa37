-- Add AI generation columns to existing blog_posts table
-- This migration extends the blog_posts table with fields needed for automated content generation

-- Add meta keywords column for SEO
ALTER TABLE public.blog_posts ADD COLUMN IF NOT EXISTS meta_keywords text[];

-- Add raw JSON data column for storing generation source data
ALTER TABLE public.blog_posts ADD COLUMN IF NOT EXISTS raw_data_json jsonb;

-- Add generation source tracking
ALTER TABLE public.blog_posts ADD COLUMN IF NOT EXISTS generation_source character varying(50) DEFAULT 'manual' CHECK (generation_source IN ('manual', 'ai_generated', 'template_based'));

-- Add AI generation job ID for tracking
ALTER TABLE public.blog_posts ADD COLUMN IF NOT EXISTS ai_generation_job_id character varying(255);

-- Create indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_blog_posts_generation_source ON public.blog_posts(generation_source);
CREATE INDEX IF NOT EXISTS idx_blog_posts_ai_job_id ON public.blog_posts(ai_generation_job_id);

-- Add comment for documentation
COMMENT ON COLUMN public.blog_posts.meta_keywords IS 'SEO keywords array for article optimization';
COMMENT ON COLUMN public.blog_posts.raw_data_json IS 'Raw data used for content generation (JSON format)';
COMMENT ON COLUMN public.blog_posts.generation_source IS 'Source of content generation: manual, ai_generated, or template_based';
COMMENT ON COLUMN public.blog_posts.ai_generation_job_id IS 'Unique ID linking to AI generation job for tracking'; 