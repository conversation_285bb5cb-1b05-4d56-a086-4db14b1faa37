-- Blog Content Generation Templates Table
-- Stores predefined templates for different types of automated content
CREATE TABLE IF NOT EXISTS public.blog_content_templates (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    name character varying(255) NOT NULL,
    description text,
    template_type character varying(50) NOT NULL CHECK (template_type IN (
        'ranking_analysis', 'doctor_spotlight', 'medical_procedure', 
        'treatment_overview', 'research_summary', 'patient_guide', 
        'institution_review', 'specialty_trends', 'health_news'
    )),
    category_id uuid NOT NULL REFERENCES public.blog_categories(id) ON DELETE RESTRICT,
    default_author_id uuid REFERENCES public.blog_authors(id) ON DELETE SET NULL,
    
    -- Template structure and prompts
    title_template text NOT NULL, -- Template for generating titles
    content_structure jsonb NOT NULL, -- Structured template for content sections
    seo_template jsonb, -- Template for meta titles and descriptions
    prompt_template text NOT NULL, -- AI prompt template
    
    -- Content generation parameters
    target_word_count integer DEFAULT 1500,
    required_sections text[], -- Array of required content sections
    optional_sections text[], -- Array of optional content sections
    data_sources text[], -- Array of data source types needed
    
    -- Publishing settings
    auto_publish boolean DEFAULT false,
    requires_review boolean DEFAULT true,
    default_tags text[], -- Array of default tags to apply
    
    -- Performance tracking
    usage_count integer DEFAULT 0,
    success_rate numeric(5,2) DEFAULT 0.00,
    avg_engagement_score numeric(5,2) DEFAULT 0.00,
    
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Blog Content Generation Jobs Table  
-- Tracks individual content generation tasks and their status
CREATE TABLE IF NOT EXISTS public.blog_content_generation_jobs (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_name character varying(255) NOT NULL,
    template_id uuid NOT NULL REFERENCES public.blog_content_templates(id) ON DELETE CASCADE,
    
    -- Job configuration
    target_data jsonb, -- Specific data for this generation (doctor IDs, specialty, etc.)
    generation_parameters jsonb, -- Custom parameters for this job
    scheduled_for timestamp with time zone,
    
    -- Job status and progress
    status character varying(20) DEFAULT 'pending' CHECK (status IN (
        'pending', 'processing', 'generating', 'reviewing', 'completed', 
        'failed', 'cancelled', 'requires_manual_review'
    )),
    progress_percentage integer DEFAULT 0,
    error_message text,
    retry_count integer DEFAULT 0,
    max_retries integer DEFAULT 3,
    
    -- Generated content results
    generated_post_id uuid REFERENCES public.blog_posts(id) ON DELETE SET NULL,
    generated_title character varying(500),
    generated_content text,
    generated_metadata jsonb, -- SEO data, tags, etc.
    quality_score numeric(5,2), -- AI-assessed content quality
    
    -- Processing timestamps
    started_at timestamp with time zone,
    completed_at timestamp with time zone,
    failed_at timestamp with time zone,
    
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- AI Content Data Sources Table
-- Manages different data sources used for content generation
CREATE TABLE IF NOT EXISTS public.blog_ai_content_sources (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    name character varying(255) NOT NULL,
    source_type character varying(50) NOT NULL CHECK (source_type IN (
        'doctor_database', 'medical_research', 'news_api', 'ranking_data',
        'patient_reviews', 'medical_journals', 'hospital_data', 'specialty_trends',
        'treatment_data', 'medical_devices', 'pharmaceutical_data'
    )),
    
    -- Data source configuration
    api_endpoint text,
    api_credentials jsonb, -- Encrypted API keys and auth data
    data_schema jsonb, -- Expected data structure
    refresh_frequency character varying(20) DEFAULT 'daily', -- hourly, daily, weekly, manual
    
    -- Data quality and validation
    data_quality_rules jsonb, -- Rules for validating data quality
    last_successful_fetch timestamp with time zone,
    last_error_message text,
    fetch_success_rate numeric(5,2) DEFAULT 100.00,
    
    -- Usage tracking
    total_requests integer DEFAULT 0,
    successful_requests integer DEFAULT 0,
    rate_limit_per_hour integer,
    current_hour_requests integer DEFAULT 0,
    
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Content Generation History and Analytics
-- Tracks performance and analytics of generated content
CREATE TABLE IF NOT EXISTS public.blog_content_generation_analytics (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id uuid NOT NULL REFERENCES public.blog_posts(id) ON DELETE CASCADE,
    template_id uuid REFERENCES public.blog_content_templates(id) ON DELETE SET NULL,
    job_id uuid REFERENCES public.blog_content_generation_jobs(id) ON DELETE SET NULL,
    
    -- Content metrics
    word_count integer,
    readability_score numeric(5,2),
    seo_score numeric(5,2),
    content_uniqueness numeric(5,2), -- Plagiarism check score
    medical_accuracy_score numeric(5,2), -- Medical review score
    
    -- Performance metrics (tracked over time)
    views_24h integer DEFAULT 0,
    views_7d integer DEFAULT 0,
    views_30d integer DEFAULT 0,
    total_views integer DEFAULT 0,
    
    avg_time_on_page numeric(8,2) DEFAULT 0.00, -- seconds
    bounce_rate numeric(5,2) DEFAULT 0.00,
    social_shares integer DEFAULT 0,
    comments_count integer DEFAULT 0,
    
    -- AI generation metrics
    generation_time_seconds integer, -- Time taken to generate
    ai_confidence_score numeric(5,2), -- AI's confidence in the content
    human_review_required boolean DEFAULT true,
    human_review_score numeric(5,2), -- Human reviewer's assessment
    
    -- Update tracking
    last_metrics_update timestamp with time zone DEFAULT now(),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Content Generation Rules and Automation Settings
-- Defines rules for when and how content should be automatically generated
CREATE TABLE IF NOT EXISTS public.blog_content_generation_rules (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    rule_name character varying(255) NOT NULL,
    description text,
    template_id uuid NOT NULL REFERENCES public.blog_content_templates(id) ON DELETE CASCADE,
    
    -- Trigger conditions
    trigger_type character varying(50) NOT NULL CHECK (trigger_type IN (
        'schedule', 'data_update', 'trending_topic', 'ranking_change',
        'new_doctor_added', 'specialty_milestone', 'manual_trigger',
        'content_gap_detected', 'competitor_content', 'seasonal_trigger'
    )),
    
    -- Schedule configuration (for scheduled triggers)
    schedule_expression character varying(100), -- Cron-like expression
    schedule_timezone character varying(50) DEFAULT 'UTC',
    
    -- Condition parameters
    trigger_conditions jsonb, -- Specific conditions that must be met
    data_source_filters jsonb, -- Filters for data sources
    content_requirements jsonb, -- Requirements for generated content
    
    -- Generation limits and controls
    max_generations_per_day integer DEFAULT 1,
    max_generations_per_week integer DEFAULT 7,
    min_days_between_similar integer DEFAULT 7, -- Prevent duplicate topics
    
    -- Quality controls
    min_quality_threshold numeric(5,2) DEFAULT 80.00,
    auto_publish_threshold numeric(5,2) DEFAULT 90.00,
    require_medical_review boolean DEFAULT true,
    
    -- Rule status and performance
    is_active boolean DEFAULT true,
    success_count integer DEFAULT 0,
    failure_count integer DEFAULT 0,
    last_triggered timestamp with time zone,
    next_scheduled_run timestamp with time zone,
    
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Content Generation Feedback and Learning
-- Stores feedback to improve future content generation
CREATE TABLE IF NOT EXISTS public.blog_content_generation_feedback (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id uuid NOT NULL REFERENCES public.blog_posts(id) ON DELETE CASCADE,
    job_id uuid REFERENCES public.blog_content_generation_jobs(id) ON DELETE SET NULL,
    
    -- Feedback source
    feedback_source character varying(50) NOT NULL CHECK (feedback_source IN (
        'human_reviewer', 'reader_comments', 'engagement_metrics',
        'seo_performance', 'medical_expert', 'automated_analysis'
    )),
    feedback_user_id uuid, -- If from a specific user
    
    -- Feedback content
    feedback_type character varying(50) NOT NULL CHECK (feedback_type IN (
        'accuracy', 'readability', 'relevance', 'completeness',
        'medical_accuracy', 'seo_optimization', 'engagement',
        'factual_error', 'improvement_suggestion'
    )),
    feedback_rating integer CHECK (feedback_rating BETWEEN 1 AND 5),
    feedback_text text,
    improvement_suggestions jsonb,
    
    -- Action taken
    action_taken character varying(100),
    resolved boolean DEFAULT false,
    resolution_notes text,
    
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_content_templates_type ON public.blog_content_templates(template_type);
CREATE INDEX IF NOT EXISTS idx_content_templates_category ON public.blog_content_templates(category_id);
CREATE INDEX IF NOT EXISTS idx_content_templates_active ON public.blog_content_templates(is_active);

CREATE INDEX IF NOT EXISTS idx_generation_jobs_status ON public.blog_content_generation_jobs(status);
CREATE INDEX IF NOT EXISTS idx_generation_jobs_template ON public.blog_content_generation_jobs(template_id);
CREATE INDEX IF NOT EXISTS idx_generation_jobs_scheduled ON public.blog_content_generation_jobs(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_generation_jobs_post ON public.blog_content_generation_jobs(generated_post_id);

CREATE INDEX IF NOT EXISTS idx_ai_sources_type ON public.blog_ai_content_sources(source_type);
CREATE INDEX IF NOT EXISTS idx_ai_sources_active ON public.blog_ai_content_sources(is_active);

CREATE INDEX IF NOT EXISTS idx_generation_analytics_post ON public.blog_content_generation_analytics(post_id);
CREATE INDEX IF NOT EXISTS idx_generation_analytics_template ON public.blog_content_generation_analytics(template_id);
CREATE INDEX IF NOT EXISTS idx_generation_analytics_views ON public.blog_content_generation_analytics(total_views DESC);

CREATE INDEX IF NOT EXISTS idx_generation_rules_active ON public.blog_content_generation_rules(is_active);
CREATE INDEX IF NOT EXISTS idx_generation_rules_trigger ON public.blog_content_generation_rules(trigger_type);
CREATE INDEX IF NOT EXISTS idx_generation_rules_next_run ON public.blog_content_generation_rules(next_scheduled_run);

CREATE INDEX IF NOT EXISTS idx_generation_feedback_post ON public.blog_content_generation_feedback(post_id);
CREATE INDEX IF NOT EXISTS idx_generation_feedback_type ON public.blog_content_generation_feedback(feedback_type);
CREATE INDEX IF NOT EXISTS idx_generation_feedback_resolved ON public.blog_content_generation_feedback(resolved);

-- Apply update triggers
CREATE TRIGGER update_content_templates_updated_at BEFORE UPDATE ON public.blog_content_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_generation_jobs_updated_at BEFORE UPDATE ON public.blog_content_generation_jobs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_sources_updated_at BEFORE UPDATE ON public.blog_ai_content_sources
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_generation_analytics_updated_at BEFORE UPDATE ON public.blog_content_generation_analytics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_generation_rules_updated_at BEFORE UPDATE ON public.blog_content_generation_rules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_generation_feedback_updated_at BEFORE UPDATE ON public.blog_content_generation_feedback
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Note: Sample data has been moved to a separate migration file for safer execution
-- Run migrations/20250121000004_insert_content_generator_sample_data.sql after this migration succeeds
-- This ensures no conflicts with existing blog categories or data 