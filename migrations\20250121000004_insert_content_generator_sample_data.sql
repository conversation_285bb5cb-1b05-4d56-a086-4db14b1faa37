-- Sample Data for Blog Content Generator
-- Run this AFTER the main content generator tables migration is successful
-- This inserts sample templates, data sources, and rules for the automated content generator

-- First, let's verify we have the required blog categories and authors
DO $$
BEGIN
    -- Check if required categories exist
    IF NOT EXISTS (SELECT 1 FROM public.blog_categories WHERE slug = 'ranking-analysis') THEN
        RAISE EXCEPTION 'Required blog category "ranking-analysis" not found. Please ensure blog categories are properly set up.';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM public.blog_categories WHERE slug = 'medical-deep-dives') THEN
        RAISE EXCEPTION 'Required blog category "medical-deep-dives" not found. Please ensure blog categories are properly set up.';
    END IF;
    
    -- Check if at least one author exists
    IF NOT EXISTS (SELECT 1 FROM public.blog_authors LIMIT 1) THEN
        RAISE EXCEPTION 'No blog authors found. Please ensure at least one blog author exists.';
    END IF;
    
    RAISE NOTICE 'All required dependencies found. Proceeding with sample data insertion.';
END
$$;

-- Insert sample content templates
-- Only insert if they don't already exist
INSERT INTO public.blog_content_templates (
    name, description, template_type, category_id, 
    title_template, content_structure, prompt_template,
    target_word_count, required_sections, auto_publish, requires_review
)
SELECT 
    'Doctor Ranking Analysis Template',
    'Template for generating automated ranking analysis of doctors in specific specialties',
    'ranking_analysis',
    (SELECT id FROM public.blog_categories WHERE slug = 'ranking-analysis' LIMIT 1),
    'Top {{specialty}} Doctors in {{location}}: {{year}} Performance Analysis',
    '{
        "sections": [
            {"name": "introduction", "min_words": 200, "required": true},
            {"name": "methodology", "min_words": 150, "required": true},
            {"name": "top_performers", "min_words": 400, "required": true},
            {"name": "trending_analysis", "min_words": 300, "required": true},
            {"name": "patient_insights", "min_words": 250, "required": false},
            {"name": "conclusion", "min_words": 150, "required": true}
        ]
    }',
    'Generate a comprehensive ranking analysis for {{specialty}} doctors in {{location}}. Include performance metrics, patient feedback trends, and notable achievements. Maintain medical accuracy and professional tone.',
    1500,
    ARRAY['introduction', 'methodology', 'top_performers', 'conclusion'],
    false,
    true
WHERE NOT EXISTS (
    SELECT 1 FROM public.blog_content_templates WHERE name = 'Doctor Ranking Analysis Template'
);

INSERT INTO public.blog_content_templates (
    name, description, template_type, category_id, 
    title_template, content_structure, prompt_template,
    target_word_count, required_sections, auto_publish, requires_review
)
SELECT 
    'Medical Procedure Deep Dive Template', 
    'Template for detailed explanations of medical procedures and treatments',
    'medical_procedure',
    (SELECT id FROM public.blog_categories WHERE slug = 'medical-deep-dives' LIMIT 1),
    'Understanding {{procedure_name}}: A Complete Patient Guide',
    '{
        "sections": [
            {"name": "overview", "min_words": 200, "required": true},
            {"name": "preparation", "min_words": 300, "required": true},
            {"name": "procedure_steps", "min_words": 400, "required": true},
            {"name": "recovery", "min_words": 300, "required": true},
            {"name": "risks_benefits", "min_words": 250, "required": true},
            {"name": "alternatives", "min_words": 200, "required": false},
            {"name": "conclusion", "min_words": 150, "required": true}
        ]
    }',
    'Create a comprehensive guide about {{procedure_name}} that explains the procedure in patient-friendly language while maintaining medical accuracy. Include preparation, process, recovery, and important considerations.',
    1800,
    ARRAY['overview', 'preparation', 'procedure_steps', 'recovery', 'risks_benefits'],
    false,
    true
WHERE NOT EXISTS (
    SELECT 1 FROM public.blog_content_templates WHERE name = 'Medical Procedure Deep Dive Template'
);

-- Insert sample doctor spotlight template
INSERT INTO public.blog_content_templates (
    name, description, template_type, category_id, 
    title_template, content_structure, prompt_template,
    target_word_count, required_sections, auto_publish, requires_review
)
SELECT 
    'Doctor Spotlight Template', 
    'Template for featuring individual doctors and their achievements',
    'doctor_spotlight',
    (SELECT id FROM public.blog_categories WHERE slug = 'doctor-spotlights' LIMIT 1),
    'Spotlight: Dr. {{doctor_name}} - Leading {{specialty}} Expert',
    '{
        "sections": [
            {"name": "introduction", "min_words": 150, "required": true},
            {"name": "background", "min_words": 250, "required": true},
            {"name": "achievements", "min_words": 300, "required": true},
            {"name": "patient_impact", "min_words": 200, "required": true},
            {"name": "insights", "min_words": 250, "required": false},
            {"name": "conclusion", "min_words": 100, "required": true}
        ]
    }',
    'Create an engaging profile of Dr. {{doctor_name}} highlighting their expertise in {{specialty}}, notable achievements, and impact on patient care. Include quotes and specific examples.',
    1200,
    ARRAY['introduction', 'background', 'achievements', 'patient_impact'],
    false,
    true
WHERE NOT EXISTS (
    SELECT 1 FROM public.blog_content_templates WHERE name = 'Doctor Spotlight Template'
);

-- Insert sample AI content sources
-- Only insert if they don't already exist
INSERT INTO public.blog_ai_content_sources (
    name, source_type, data_schema, refresh_frequency, 
    rate_limit_per_hour, is_active
)
SELECT 
    'Internal Doctor Database',
    'doctor_database', 
    '{
        "fields": ["doctor_id", "name", "specialty", "rating", "review_count", "hospital", "location"],
        "relationships": ["reviews", "rankings", "performance_metrics"]
    }',
    'daily',
    1000,
    true
WHERE NOT EXISTS (
    SELECT 1 FROM public.blog_ai_content_sources WHERE name = 'Internal Doctor Database'
);

INSERT INTO public.blog_ai_content_sources (
    name, source_type, data_schema, refresh_frequency, 
    rate_limit_per_hour, is_active
)
SELECT 
    'Medical Research Publications',
    'medical_research',
    '{
        "fields": ["title", "authors", "publication_date", "journal", "abstract", "keywords"],
        "filters": ["peer_reviewed", "recent", "high_impact"]
    }',
    'weekly', 
    100,
    true
WHERE NOT EXISTS (
    SELECT 1 FROM public.blog_ai_content_sources WHERE name = 'Medical Research Publications'
);

INSERT INTO public.blog_ai_content_sources (
    name, source_type, data_schema, refresh_frequency, 
    rate_limit_per_hour, is_active
)
SELECT 
    'Healthcare Trending Topics',
    'specialty_trends',
    '{
        "fields": ["topic", "specialty", "trend_score", "search_volume", "related_keywords"],
        "metrics": ["engagement", "medical_relevance", "patient_interest"]
    }',
    'daily',
    500,
    true
WHERE NOT EXISTS (
    SELECT 1 FROM public.blog_ai_content_sources WHERE name = 'Healthcare Trending Topics'
);

-- Insert sample generation rules
-- Only insert if they don't already exist and templates exist
INSERT INTO public.blog_content_generation_rules (
    rule_name, description, template_id, trigger_type,
    schedule_expression, max_generations_per_week,
    min_quality_threshold, auto_publish_threshold, is_active
)
SELECT 
    'Weekly Specialty Rankings',
    'Generate weekly ranking analysis for high-performing specialties',
    t.id,
    'schedule',
    '0 9 * * 1', -- Every Monday at 9 AM
    2,
    85.00,
    92.00,
    true
FROM public.blog_content_templates t
WHERE t.name = 'Doctor Ranking Analysis Template'
AND NOT EXISTS (
    SELECT 1 FROM public.blog_content_generation_rules WHERE rule_name = 'Weekly Specialty Rankings'
);

INSERT INTO public.blog_content_generation_rules (
    rule_name, description, template_id, trigger_type,
    schedule_expression, max_generations_per_week,
    min_quality_threshold, auto_publish_threshold, is_active
)
SELECT 
    'Trending Procedure Guides',
    'Generate procedure guides when new trending medical procedures are detected',
    t.id,
    'trending_topic',
    NULL,
    3,
    80.00,
    88.00,
    true
FROM public.blog_content_templates t
WHERE t.name = 'Medical Procedure Deep Dive Template'
AND NOT EXISTS (
    SELECT 1 FROM public.blog_content_generation_rules WHERE rule_name = 'Trending Procedure Guides'
);

INSERT INTO public.blog_content_generation_rules (
    rule_name, description, template_id, trigger_type,
    schedule_expression, max_generations_per_week,
    min_quality_threshold, auto_publish_threshold, is_active
)
SELECT 
    'Monthly Doctor Spotlights',
    'Generate doctor spotlight articles for top-rated doctors',
    t.id,
    'schedule',
    '0 10 1 * *', -- First day of each month at 10 AM
    4,
    88.00,
    95.00,
    true
FROM public.blog_content_templates t
WHERE t.name = 'Doctor Spotlight Template'
AND NOT EXISTS (
    SELECT 1 FROM public.blog_content_generation_rules WHERE rule_name = 'Monthly Doctor Spotlights'
);

-- Verify sample data was inserted
DO $$
DECLARE
    template_count INTEGER;
    source_count INTEGER;
    rule_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO template_count FROM public.blog_content_templates;
    SELECT COUNT(*) INTO source_count FROM public.blog_ai_content_sources;
    SELECT COUNT(*) INTO rule_count FROM public.blog_content_generation_rules;
    
    RAISE NOTICE 'Sample data insertion complete:';
    RAISE NOTICE '- Content Templates: %', template_count;
    RAISE NOTICE '- AI Content Sources: %', source_count;
    RAISE NOTICE '- Generation Rules: %', rule_count;
    
    IF template_count = 0 THEN
        RAISE WARNING 'No content templates were inserted. This may indicate missing blog categories.';
    END IF;
END
$$; 