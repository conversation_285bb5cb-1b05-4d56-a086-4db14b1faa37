-- Migration: create_ads_table
-- Description: Creates the ads table to store advertising campaign details.

CREATE TABLE public.ads (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    title text NOT NULL,
    description text NULL,
    image_url text NULL,
    target_url text NOT NULL,
    start_date date NOT NULL,
    end_date date NULL,
    status text NOT NULL DEFAULT 'draft', -- e.g., 'draft', 'scheduled', 'active', 'paused', 'completed'
    budget numeric NULL DEFAULT 0,
    placement text NOT NULL, -- e.g., 'search_results', 'sidebar', 'homepage'
    target_specialty_id integer NULL REFERENCES public.specialties(specialty_id) ON DELETE SET NULL,
    target_locations text[] NULL,
    advertiser_id uuid NULL REFERENCES public.analytics_advertisers(id) ON DELETE SET NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Add comments to clarify column purposes
COMMENT ON COLUMN public.ads.status IS 'Ad status (e.g., ''draft'', ''scheduled'', ''active'', ''paused'', ''completed'')';
COMMENT ON COLUMN public.ads.placement IS 'Where the ad appears (e.g., ''search_results'', ''sidebar'', ''homepage'')';
COMMENT ON COLUMN public.ads.target_specialty_id IS 'Foreign key to specialties.specialty_id (if targeting a specialty)';
COMMENT ON COLUMN public.ads.target_locations IS 'Array of targeted location names (e.g., cities, countries)';
COMMENT ON COLUMN public.ads.advertiser_id IS 'Foreign key to analytics_advertisers.id (optional link)';

-- Create indexes for performance
CREATE INDEX idx_ads_status ON public.ads(status);
CREATE INDEX idx_ads_placement ON public.ads(placement);
CREATE INDEX idx_ads_target_specialty_id ON public.ads(target_specialty_id);
CREATE INDEX idx_ads_advertiser_id ON public.ads(advertiser_id);
CREATE INDEX idx_ads_start_date ON public.ads(start_date);
CREATE INDEX idx_ads_end_date ON public.ads(end_date);

-- Enable Row Level Security (RLS) - Assuming admin access control is handled elsewhere or will be added later
-- Consider enabling RLS and creating policies if fine-grained access control is needed.
-- ALTER TABLE public.ads ENABLE ROW LEVEL SECURITY;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = now();
   RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at on row update
CREATE TRIGGER update_ads_updated_at
BEFORE UPDATE ON public.ads
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
