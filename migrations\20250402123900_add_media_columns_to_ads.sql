-- Migration: add_media_columns_to_ads
-- Description: Adds media_url and media_type columns for image/video uploads and removes the old image_url column.

-- Add new columns
ALTER TABLE public.ads
ADD COLUMN media_url text NULL,
ADD COLUMN media_type text NULL; -- e.g., 'image', 'video'

-- Add comments for new columns
COMMENT ON COLUMN public.ads.media_url IS 'URL of the uploaded ad media (image or video) in Supabase Storage.';
COMMENT ON COLUMN public.ads.media_type IS 'Type of the uploaded media (e.g., ''image'', ''video'').';

-- Drop the old image_url column
ALTER TABLE public.ads
DROP COLUMN image_url;
