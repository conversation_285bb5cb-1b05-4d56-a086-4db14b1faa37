-- Add the new 'size' column to the ads table
ALTER TABLE public.ads
ADD COLUMN size VARCHAR(50); -- Allows for standard sizes like '728x90' or custom ones

-- Update existing ads with a default size (optional, choose a common default)
-- UPDATE public.ads SET size = '300x250' WHERE size IS NULL;

-- Modify the 'placement' column
-- First, drop the constraint if it's an ENUM (assuming the enum type name was 'ad_placement_enum')
-- Note: You might need to find the actual constraint name and enum type name in your DB schema
-- Example: ALTER TABLE public.ads DROP CONSTRAINT ads_placement_check;
-- Example: DROP TYPE public.ad_placement_enum;

-- Then, change the column type to VARCHAR
ALTER TABLE public.ads
ALTER COLUMN placement TYPE VARCHAR(100); -- Increase size to accommodate 'page:position' format

-- Update existing placements to the new format (example)
-- You'll need to decide on your specific page:position identifiers
-- UPDATE public.ads SET placement = 'homepage:banner' WHERE placement = 'homepage';
-- UPDATE public.ads SET placement = 'homepage:sidebar' WHERE placement = 'sidebar';

-- Examples of new placement formats for various pages:
-- 'about-us:banner'       - Banner ad on the About Us page
-- 'about-us:sidebar'      - Sidebar ad on the About Us page
-- 'standings:banner'      - Banner ad on the Standings page
-- 'standings:sidebar'     - Sidebar ad on the Standings page
-- 'divisions:banner'      - Banner ad on the Divisions/Specialties page
-- 'divisions:sidebar'     - Sidebar ad on the Divisions/Specialties page
-- 'teams:banner'          - Banner ad on the Teams page
-- 'teams:sidebar'         - Sidebar ad on the Teams page
-- 'head-to-head:banner'   - Banner ad on the Head-to-Head comparison page
-- 'head-to-head:sidebar'  - Sidebar ad on the Head-to-Head comparison page
-- 'doctor-profile:top'    - Top ad on the Doctor Profile page
-- 'doctor-profile:bottom' - Bottom ad on the Doctor Profile page
-- 'ratings:banner'        - Banner ad on the Ratings page
-- 'ratings:sidebar'       - Sidebar ad on the Ratings page

-- Add comments to explain the new columns/formats
COMMENT ON COLUMN public.ads.size IS 'Specifies the intended display size of the ad (e.g., "728x90", "300x250").';
COMMENT ON COLUMN public.ads.placement IS 'Specifies the target page and position using format "page:position" (e.g., "homepage:banner", "about-us:sidebar", "doctor-profile:top").';
