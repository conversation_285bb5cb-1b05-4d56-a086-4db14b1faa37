-- Migration: support_multiple_placements
-- Description: Adds support for storing multiple placements for a single ad

-- First, create a new column to store an array of placements
ALTER TABLE public.ads
ADD COLUMN placements VARCHAR(100)[] NULL;

-- Add comment to explain the new column
COMMENT ON COLUMN public.ads.placements IS 'Array of placement strings in "page:position" format for ads that appear in multiple locations.';

-- Create a migration function to populate the new placements array from existing placement values
CREATE OR REPLACE FUNCTION migrate_placements() RETURNS void AS $$
BEGIN
    -- Copy existing single placement values to the new array column
    UPDATE public.ads
    SET placements = ARRAY[placement]
    WHERE placement IS NOT NULL AND placements IS NULL;
END;
$$ LANGUAGE plpgsql;

-- Execute the migration function
SELECT migrate_placements();

-- Drop the migration function after use
DROP FUNCTION migrate_placements();

-- Add an index on the new placements column for better query performance
CREATE INDEX idx_ads_placements ON public.ads USING GIN (placements);

-- Note: We're keeping the original 'placement' column for backward compatibility
-- In a future migration, we might want to make 'placement' nullable or remove it entirely
