-- Migration: Add stats aggregation functions
-- Description: Creates SQL functions to calculate total matches and average rating for specialties.

-- Function to get total matches for doctors in a specific country and specialty
CREATE OR REPLACE FUNCTION public.get_total_matches_for_specialty(p_country_id INT, p_specialty_id INT)
RETURNS INT AS $$
DECLARE
    total_matches INT;
BEGIN
    SELECT SUM(COALESCE(wins, 0) + COALESCE(losses, 0) + COALESCE(draws, 0))
    INTO total_matches
    FROM public.doctors
    WHERE country_id = p_country_id AND specialty_id = p_specialty_id;
    
    RETURN COALESCE(total_matches, 0);
END;
$$ LANGUAGE plpgsql STABLE;

COMMENT ON FUNCTION public.get_total_matches_for_specialty(INT, INT) IS 'Calculates the sum of wins, losses, and draws for all doctors in a given country and specialty.';

-- Function to get average rating for doctors in a specific country and specialty
CREATE OR REPLACE FUNCTION public.get_average_rating_for_specialty(p_country_id INT, p_specialty_id INT)
RETURNS NUMERIC AS $$
DECLARE
    average_rating NUMERIC;
BEGIN
    SELECT AVG(rating)
    INTO average_rating
    FROM public.doctors
    WHERE country_id = p_country_id AND specialty_id = p_specialty_id AND rating IS NOT NULL;
    
    RETURN COALESCE(average_rating, 0.0);
END;
$$ LANGUAGE plpgsql STABLE;

COMMENT ON FUNCTION public.get_average_rating_for_specialty(INT, INT) IS 'Calculates the average rating for all doctors with non-null ratings in a given country and specialty.';
