-- Migration: Add Core Table Indexes
-- Description: Adds recommended indexes to doctors, reviews, and users tables for performance.

-- Indexes for 'doctors' table
CREATE INDEX IF NOT EXISTS idx_doctors_country_id ON public.doctors(country_id);
CREATE INDEX IF NOT EXISTS idx_doctors_specialty_id ON public.doctors(specialty_id);
CREATE INDEX IF NOT EXISTS idx_doctors_community_rating ON public.doctors(community_rating DESC NULLS LAST); -- For sorting by community_rating
CREATE INDEX IF NOT EXISTS idx_doctors_hospital_id ON public.doctors(hospital_id);
CREATE INDEX IF NOT EXISTS idx_doctors_auth_id ON public.doctors(auth_id);
CREATE INDEX IF NOT EXISTS idx_doctors_fullname ON public.doctors(fullname text_pattern_ops); -- For LIKE searches if needed, or standard for sorting

-- Composite index for common league queries
CREATE INDEX IF NOT EXISTS idx_doctors_country_specialty_community_rating ON public.doctors(country_id, specialty_id, community_rating DESC NULLS LAST);

-- Indexes for 'reviews' table
CREATE INDEX IF NOT EXISTS idx_reviews_doctor_id ON public.reviews(doctor_id);
CREATE INDEX IF NOT EXISTS idx_reviews_user_id ON public.reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_reviews_review_date ON public.reviews(review_date DESC);

-- Indexes for 'users' table
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON public.users(username);
CREATE INDEX IF NOT EXISTS idx_users_auth_id ON public.users(auth_id);

COMMENT ON INDEX public.idx_doctors_country_id IS 'Index for filtering doctors by country.';
COMMENT ON INDEX public.idx_doctors_specialty_id IS 'Index for filtering doctors by specialty.';
COMMENT ON INDEX public.idx_doctors_community_rating IS 'Index for sorting doctors by community_rating.';
COMMENT ON INDEX public.idx_doctors_country_specialty_community_rating IS 'Composite index for efficient querying of league tables.';
COMMENT ON INDEX public.idx_reviews_doctor_id IS 'Index for fetching reviews for a specific doctor.';
COMMENT ON INDEX public.idx_reviews_user_id IS 'Index for checking duplicate reviews by a user.';
