-- Migration: Create get_doctors_for_homepage RPC function
-- Description: Creates an optimized function to fetch doctors for homepage display
-- This function returns rated doctors first, then fills with random unrated doctors

CREATE OR REPLACE FUNCTION get_doctors_for_homepage(limit_count INTEGER DEFAULT 16)
RETURNS TABLE(
    doctor_id INTEGER,
    fullname TEXT,
    hospital TEXT,
    medical_title TEXT,
    specialty TEXT,
    subspecialty TEXT,
    educational_background TEXT,
    board_certifications TEXT,
    experience INTEGER,
    publications TEXT,
    awards_recognitions TEXT,
    phone_number TEXT,
    email TEXT,
    languages_spoken TEXT,
    professional_affiliations TEXT,
    procedures_performed TEXT,
    treatment_services_expertise TEXT,
    hospital_id INTEGER,
    image_path TEXT,
    wins INTEGER,
    losses INTEGER,
    form TEXT,
    community_rating NUMERIC,
    review_count INTEGER,
    country_id INTEGER,
    specialty_id INTEGER,
    last_updated DATE,
    auth_id UUID,
    draws INTEGER,
    profile_image TEXT,
    personal_biography TEXT,
    work_history TEXT,
    timings TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH rated_doctors AS (
        -- Get doctors with community_ratings, ordered by community_rating descending
        SELECT d.*
        FROM doctors d
        WHERE d.community_rating IS NOT NULL AND d.community_rating > 0
        ORDER BY d.community_rating DESC, d.review_count DESC
        LIMIT limit_count
    ),
    random_doctors AS (
        -- Get random doctors without community_ratings to fill remaining slots
        SELECT d.*
        FROM doctors d
        WHERE (d.community_rating IS NULL OR d.community_rating = 0)
        ORDER BY RANDOM()
        LIMIT GREATEST(0, limit_count - (SELECT COUNT(*) FROM rated_doctors))
    )
    -- Combine rated doctors first, then random doctors
    SELECT * FROM rated_doctors
    UNION ALL
    SELECT * FROM random_doctors
    ORDER BY
        CASE WHEN community_rating IS NOT NULL AND community_rating > 0 THEN 0 ELSE 1 END,
        community_rating DESC NULLS LAST,
        review_count DESC NULLS LAST;
END;
$$ LANGUAGE plpgsql STABLE;

-- Add comment for documentation
COMMENT ON FUNCTION get_doctors_for_homepage(INTEGER) IS 'Optimized function to fetch doctors for homepage display. Returns rated doctors first, then fills with random unrated doctors.';

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_doctors_for_homepage(INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_doctors_for_homepage(INTEGER) TO anon;
