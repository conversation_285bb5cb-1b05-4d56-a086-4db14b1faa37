{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test:registration": "node __tests__/registration-test.js"}, "dependencies": {"@emotion/is-prop-valid": "latest", "@fontsource-variable/inter": "^5.2.6", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "latest", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@types/marked": "^5.0.2", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "critters": "^0.0.23", "date-fns": "latest", "embla-carousel-react": "8.5.1", "framer-motion": "latest", "input-otp": "1.4.1", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.454.0", "marked": "^15.0.12", "next": "^15.2.2", "next-intl": "^4.0.2", "next-themes": "^0.4.4", "node-fetch": "^3.3.2", "node-gyp": "latest", "nodemailer": "^7.0.3", "react": "^18.2.0", "react-country-flag": "^3.1.0", "react-day-picker": "8.10.1", "react-dom": "^18.2.0", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.54.1", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-resizable-panels": "^2.1.7", "react-simple-star-rating": "^5.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "sqlite": "latest", "sqlite3": "latest", "swr": "^2.3.3", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "typescript": "5.4.2", "uuid": "^11.1.0", "vaul": "^0.9.6", "web-vitals": "^4.2.4", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.0", "@types/bcryptjs": "^2.4.6", "@types/canvas-confetti": "^1.9.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "dotenv": "^16.6.1", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}