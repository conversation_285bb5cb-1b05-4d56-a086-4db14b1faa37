import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

// Types
type ChangePasswordResponse = {
  success: boolean;
  message?: string;
  error?: string;
};

// Main handler
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ChangePasswordResponse>
) {
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  // Get request data
  const { currentPassword, newPassword, userId, email, userType } = req.body;

  // Validate request
  if (!currentPassword || !newPassword || !email) {
    return res.status(400).json({
      success: false,
      error: 'Missing required fields: currentPassword, newPassword, and email are required',
    });
  }

  // Validate password strength (optional but recommended)
  if (newPassword.length < 8) {
    return res.status(400).json({
      success: false,
      error: 'New password must be at least 8 characters long',
    });
  }

  // Create Supabase client with service role for accessing auth_credentials
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://uapbzzscckhtptliynyj.supabase.co';
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q';

  const supabase = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  try {
    console.log(`[API] Change password attempt for email: ${email}`);

    // Step 1: Get auth credentials
    const { data: authCredential, error: selectError } = await supabase
      .from('auth_credentials')
      .select('id, hashed_password, user_profile_id, user_type')
      .eq('email', email)
      .single();

    if (selectError || !authCredential) {
      console.error('[API] Error retrieving auth credentials:', selectError);
      return res.status(404).json({
        success: false,
        error: 'User not found',
      });
    }

    // Step 2: Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(
      currentPassword,
      authCredential.hashed_password
    );

    if (!isCurrentPasswordValid) {
      console.log('[API] Current password validation failed');
      return res.status(401).json({
        success: false,
        error: 'Current password is incorrect',
      });
    }

    // Step 3: Generate bcrypt hash for new password
    const saltRounds = 10;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    // Step 4: Update the password in auth_credentials
    const { error: updateError } = await supabase
      .from('auth_credentials')
      .update({ hashed_password: hashedNewPassword })
      .eq('id', authCredential.id);

    if (updateError) {
      console.error('[API] Error updating auth credentials:', updateError);
      return res.status(500).json({
        success: false,
        error: 'Failed to update password',
      });
    }

    // Everything succeeded
    console.log('[API] Password updated successfully for user:', email);
    return res.status(200).json({
      success: true,
      message: 'Password updated successfully',
    });
  } catch (error) {
    console.error('[API] Unhandled error in change-password:', error);
    return res.status(500).json({
      success: false,
      error: 'An unexpected error occurred',
    });
  }
} 