import { NextApiRequest, NextApiResponse } from 'next'
import { createClient } from '@supabase/supabase-js'
import { v4 as uuidv4 } from 'uuid'
import nodemailer from 'nodemailer'
import { randomBytes } from 'crypto'

// Set a fixed expiration time for reset tokens (2 hours in milliseconds)
const RESET_TOKEN_EXPIRY = 2 * 60 * 60 * 1000 // 2 hours

// Add this function before the handler
async function ensureTableExists(supabaseAdmin: any) {
  try {
    // Check if the table exists
    const { error: checkError } = await supabaseAdmin
      .from('password_reset_tokens')
      .select('id')
      .limit(1)
    
    if (checkError && checkError.message.includes('does not exist')) {
      console.log('Table does not exist, creating it...')
      
      // Skip creating the table - we'll manually create it in the Supabase dashboard
      return {
        exists: false,
        error: 'Table does not exist. Please create it manually in the Supabase dashboard.'
      }
    }
    
    return { exists: true }
  } catch (error) {
    console.error('Error checking/creating table:', error)
    return { 
      exists: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Extract email from request body
    const { email } = req.body

    if (!email) {
      return res.status(400).json({ error: 'Email is required' })
    }

    console.log(`Processing password reset request for: ${email}`)

    // Initialize Supabase client with service role
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Check if the user exists in auth_credentials
    const { data: userData, error: userError } = await supabaseAdmin
      .from('auth_credentials')
      .select('id, email, user_profile_id, user_type')
      .eq('email', email)
      .single()

    if (userError || !userData) {
      // Don't reveal if the email exists or not (security best practice)
      console.log('User not found or error:', userError)
      return res.status(200).json({ 
        success: true, 
        message: 'If your email is registered, you will receive a password reset link' 
      })
    }

    // Check if the password_reset_tokens table exists
    const tableCheck = await ensureTableExists(supabaseAdmin)
    if (!tableCheck.exists) {
      console.error('Table check failed:', tableCheck.error)
      return res.status(500).json({ 
        error: 'Password reset feature is not fully set up', 
        details: tableCheck.error 
      })
    }

    // Generate a unique reset token
    const resetToken = randomBytes(32).toString('hex')
    const resetTokenExpiry = new Date(Date.now() + RESET_TOKEN_EXPIRY).toISOString()

    // Create or update password_reset_tokens record
    const { error: tokenError } = await supabaseAdmin
      .from('password_reset_tokens')
      .upsert({
        user_id: userData.user_profile_id,
        email: email,
        token: resetToken,
        expires_at: resetTokenExpiry,
        user_type: userData.user_type
      })

    if (tokenError) {
      console.error('Error creating reset token details:', tokenError);
      console.error('Error message:', tokenError.message);
      console.error('Error details:', tokenError.details);
      console.error('Error hint:', tokenError.hint);
      console.error('Error code:', tokenError.code);
      return res.status(500).json({ error: 'Failed to generate reset token', details: tokenError.message })
    }

    // Create reset link
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
    const resetUrl = `${baseUrl}/reset-password?token=${resetToken}`

    // Set up nodemailer with Mailtrap
    const transporter = nodemailer.createTransport({
      host: 'sandbox.smtp.mailtrap.io',
      port: 587,
      secure: false,
      auth: {
        user: process.env.MAILTRAP_USER || 'f5849f3bfce859',
        pass: process.env.MAILTRAP_PASS || '971bf6348490c1'
      },
      tls: {
        // Do not fail on invalid certs
        rejectUnauthorized: false
      }
    })

    // Email content
    const mailOptions = {
      from: '"Doctors League" <<EMAIL>>',
      to: email,
      subject: 'Reset Your Password - Doctors League',
      text: `Hello,\n\nYou requested to reset your password. Please click the link below to reset your password:\n\n${resetUrl}\n\nThis link will expire in 2 hours.\n\nIf you did not request this reset, please ignore this email.\n\nThank you,\nDoctors League Team`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2 style="color: #333; text-align: center;">Password Reset Request</h2>
          <p>Hello,</p>
          <p>You requested to reset your password for your Doctors League account. Please click the button below to reset your password:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" style="background-color: #4a90e2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Reset Password</a>
          </div>
          <p>Or copy and paste this link in your browser:</p>
          <p style="word-break: break-all; color: #4a90e2;">${resetUrl}</p>
          <p><strong>This link will expire in 2 hours.</strong></p>
          <p>If you did not request this reset, please ignore this email.</p>
          <p>Thank you,<br>Doctors League Team</p>
        </div>
      `
    }

    // Send the email
    await transporter.sendMail(mailOptions)

    console.log('Password reset email sent to:', email)
    
    // Return success (don't reveal if the email exists or not)
    return res.status(200).json({ 
      success: true, 
      message: 'If your email is registered, you will receive a password reset link' 
    })

  } catch (error) {
    console.error('Unhandled error in forgot-password:', error)
    return res.status(500).json({ error: 'An unexpected error occurred' })
  }
} 