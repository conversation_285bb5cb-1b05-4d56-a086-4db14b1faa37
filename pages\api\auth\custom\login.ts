import type { NextApiRequest, NextApiResponse } from 'next';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { createServiceRoleClient } from '@/lib/supabase-client';
// Assuming Database types might be useful, though not strictly for this file yet.
// import { Database } from '@/lib/database.types'; 

interface LoginRequestBody {
    email: string;
    password: string;
}

interface LoginResponse {
    success: boolean;
    message?: string;
    token?: string;
    error?: string;
    // Optionally include some non-sensitive user info if needed by client immediately
    user?: {
        userId: number | string; // Based on your profile_id type (now INTEGER)
        email: string;
        userType: 'patient' | 'doctor';
        // Add fields that components need
        first_name?: string;
        last_name?: string;
        country_id?: number;
        country?: string;
    };
}

export default async function handler(
    req: NextApiRequest,
    res: NextApiResponse<LoginResponse>
) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).json({ success: false, error: `Method ${req.method} Not Allowed` });
    }

    const { email, password } = req.body as LoginRequestBody;

    if (!email || !password) {
        return res.status(400).json({ success: false, error: 'Email and password are required.' });
    }

    const supabase = createServiceRoleClient();
    const jwtSecret = process.env.JWT_SECRET;

    if (!jwtSecret) {
        console.error('JWT_SECRET is not defined in environment variables.');
        return res.status(500).json({ success: false, error: 'Server configuration error: JWT secret missing.' });
    }

    try {
        // 1. Fetch user from auth_credentials by email
        const { data: authCredential, error: selectError } = await supabase
            .from('auth_credentials')
            .select('user_profile_id, hashed_password, user_type, email, is_verified') // Added is_verified field
            .eq('email', email)
            .single(); // Use .single() as email should be unique in auth_credentials

        if (selectError || !authCredential) {
            if (selectError && selectError.code === 'PGRST116') { // PGRST116: "The result contains 0 rows"
                 console.log(`Login attempt: No user found with email ${email}`);
            } else if (selectError) {
                console.error('Error fetching auth credential:', selectError);
            }
            return res.status(401).json({ success: false, error: 'Invalid email or password.' });
        }

        // 2. Verify password
        const passwordIsValid = await bcrypt.compare(password, authCredential.hashed_password);

        if (!passwordIsValid) {
            console.log(`Login attempt: Invalid password for email ${email}`);
            return res.status(401).json({ success: false, error: 'Invalid email or password.' });
        }
        
        // 3. Check if email is verified
        if (!authCredential.is_verified) {
            console.log(`Login attempt: Unverified email ${email}`);
            return res.status(403).json({ 
                success: false, 
                error: 'Please verify your email before logging in. Check your inbox for the verification link.' 
            });
        }

        // 4. Generate JWT
        const tokenPayload = {
            userId: authCredential.user_profile_id, // This is an INTEGER
            email: authCredential.email,
            userType: authCredential.user_type,
            // Add any other claims you find necessary, e.g., roles, permissions (if you add them)
        };

        const token = jwt.sign(
            tokenPayload,
            jwtSecret,
            { expiresIn: '1h' } // Token expiration time (e.g., 1 hour, 1 day: '1d', etc.)
        );

        // 5. Fetch additional user profile data based on user type
        let profileData: any = {};
        try {
            if (authCredential.user_type === 'patient') {
                // Fetch basic profile data from users table
                const { data: userData, error: userError } = await supabase
                    .from('users')
                    .select('first_name, last_name, country_id')
                    .eq('user_id', authCredential.user_profile_id)
                    .single();
                
                if (!userError && userData) {
                    profileData = {
                        first_name: userData.first_name,
                        last_name: userData.last_name,
                        country_id: userData.country_id
                    };
                    
                    // Fetch country name if country_id exists
                    if (userData.country_id) {
                        const { data: countryData } = await supabase
                            .from('countries')
                            .select('country_name')
                            .eq('country_id', userData.country_id)
                            .single();
                            
                        if (countryData) {
                            profileData.country = countryData.country_name;
                        }
                    }
                } else {
                    console.error('Error fetching user profile data:', userError);
                }
            } else if (authCredential.user_type === 'doctor') {
                // Fetch basic profile data from doctors table
                const { data: doctorData, error: doctorError } = await supabase
                    .from('doctors')
                    .select('fullname, country_id')
                    .eq('doctor_id', authCredential.user_profile_id)
                    .single();
                
                if (!doctorError && doctorData) {
                    // For doctors, we might have fullname instead of first_name/last_name
                    // Extract first and last name from fullname if possible
                    const nameParts = (doctorData.fullname || '').split(' ');
                    const firstName = nameParts[0] || '';
                    const lastName = nameParts.slice(1).join(' ') || '';
                    
                    profileData = {
                        first_name: firstName,
                        last_name: lastName,
                        country_id: doctorData.country_id
                    };
                    
                    // Fetch country name if country_id exists
                    if (doctorData.country_id) {
                        const { data: countryData } = await supabase
                            .from('countries')
                            .select('country_name')
                            .eq('country_id', doctorData.country_id)
                            .single();
                            
                        if (countryData) {
                            profileData.country = countryData.country_name;
                        }
                    }
                } else {
                    console.error('Error fetching doctor profile data:', doctorError);
                }
            }
        } catch (profileError) {
            console.error('Error fetching profile data:', profileError);
            // Continue with login even if profile data fetch fails
        }

        // 6. Return JWT and user data to client
        console.log(`Successful login for email: ${email}, userType: ${authCredential.user_type}, userId: ${authCredential.user_profile_id}`);
        return res.status(200).json({
            success: true,
            message: 'Login successful.',
            token: token,
            user: { 
                userId: authCredential.user_profile_id,
                email: authCredential.email,
                userType: authCredential.user_type as 'patient' | 'doctor',
                ...profileData // Include the additional profile data
            }
        });

    } catch (error: any) {
        console.error('Login API Error:', error);
        return res.status(500).json({ success: false, error: error.message || 'An unexpected server error occurred.' });
    }
}