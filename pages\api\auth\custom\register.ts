import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import nodemailer from 'nodemailer';

// Types
type CustomRegisterRequest = {
  email: string;
    password: string;
  userType: 'doctor' | 'patient';
  profileData: any;
};

type ApiResponse = {
    success: boolean;
  userId?: string | number;
    message?: string;
    error?: string;
};

// Interface for verification tokens table
interface VerificationToken {
  user_id: string; // User ID as string
  token: string;
  email: string;
  expires_at: string;
}

// Initialize Supabase client with service role for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Email verification setup
const setupEmailTransport = () => {
  return nodemailer.createTransport({
    host: 'sandbox.smtp.mailtrap.io',
    port: 2525,
    secure: false,
    auth: {
      user: 'f5849f3bfce859',
      pass: '971bf6348490c1'
    }
  });
};

// Main handler
export default async function handler(
    req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  // Only allow POST method
    if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
    }

    try {
    console.log('=== PATIENT REGISTRATION API START ===');
    console.log('Request body:', JSON.stringify(req.body, null, 2));

    // Check environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Missing environment variables:', {
        SUPABASE_URL: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
        SERVICE_ROLE_KEY: !!process.env.SUPABASE_SERVICE_ROLE_KEY
      });
      return res.status(500).json({
        success: false,
        error: 'Server configuration error - missing environment variables'
      });
    }

    const { email, password, userType, profileData }: CustomRegisterRequest = req.body;

    // Validation
    if (!email || !password || !userType) {
      console.error('Missing required fields:', { email: !!email, password: !!password, userType });
      return res.status(400).json({
        success: false,
        error: 'Missing required fields'
      });
    }

    console.log('Basic validation passed for:', { email, userType });

    // Check if email already exists in auth_credentials
    console.log('Checking if email already exists:', email);
    const { data: existingUser, error: checkError } = await supabaseAdmin
            .from('auth_credentials')
            .select('email')
            .eq('email', email)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 means no rows returned
      console.error('Error checking existing user:', checkError);
      return res.status(500).json({
        success: false,
        error: 'Failed to check if user exists'
      });
    }

    if (existingUser) {
      console.log('Email already exists:', email);
      return res.status(400).json({
        success: false,
        error: 'Email already registered'
      });
    }

    console.log('Email is available:', email);

    // Also check if email exists in users table
    console.log('Checking if email already exists in users table:', email);
    const { data: existingUserInUsers, error: checkUsersError } = await supabaseAdmin
      .from('users')
      .select('email')
      .eq('email', email)
      .single();

    if (checkUsersError && checkUsersError.code !== 'PGRST116') { // PGRST116 means no rows returned
      console.error('Error checking existing user in users table:', checkUsersError);
      return res.status(500).json({
        success: false,
        error: 'Failed to check if user exists in users table'
      });
    }

    if (existingUserInUsers) {
      console.log('Email already exists in users table:', email);
      return res.status(400).json({
        success: false,
        error: 'Email already registered'
      });
    }

    // Generate verification token
    const verificationToken = uuidv4();
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Generate user_id
    const userId = uuidv4();

    // Convert UUID to number for user_profile_id
    // Instead of generating a random ID, we'll fetch the last ID and increment by 1
    let numericId: number;
    
    // Determine which table to query based on user type
    if (userType === 'doctor') {
      // Get the maximum doctor_id from the doctors_registration table
      const { data: maxIdData, error: maxIdError } = await supabaseAdmin
        .from('doctors_registration')
        .select('doctor_id')
        .order('doctor_id', { ascending: false })
        .limit(1)
        .single();
      
      if (maxIdError && maxIdError.code !== 'PGRST116') { // PGRST116 means no rows returned
        console.error('Error getting max doctor_id:', maxIdError);
        return res.status(500).json({
          success: false,
          error: 'Failed to generate sequential ID'
        });
      }
      
      // If no rows returned or error, start with ID 1, otherwise increment the max ID
      numericId = maxIdData && maxIdData.doctor_id !== null ? maxIdData.doctor_id + 1 : 1;
      console.log(`Generated sequential doctor_id: ${numericId}`);
    } else {
      // Get the maximum user_id from the users table
      const { data: maxIdData, error: maxIdError } = await supabaseAdmin
        .from('users')
        .select('user_id')
        .order('user_id', { ascending: false })
        .limit(1)
        .single();
      
      if (maxIdError && maxIdError.code !== 'PGRST116') { // PGRST116 means no rows returned
        console.error('Error getting max user_id:', maxIdError);
        return res.status(500).json({
          success: false,
          error: 'Failed to generate sequential ID'
        });
      }
      
      // If no rows returned or error, start with ID 1, otherwise increment the max ID
      numericId = maxIdData && maxIdData.user_id !== null ? maxIdData.user_id + 1 : 1;
      console.log(`Generated sequential user_id: ${numericId}`);
    }

    // Start a transaction to ensure all operations succeed or fail together
    let profileError = null;
    
    try {
      // 1. Insert into the appropriate profile table based on user type
      if (userType === 'doctor') {
        const { data: doctorRegProfile, error } = await supabaseAdmin
          .from('doctors_registration')
          .insert({
            doctor_id: numericId, // Use numeric ID to match user_profile_id
            email: email,
            fullname: profileData.fullname,
            hospital: profileData.hospital || profileData.hospital_affiliation || '',
            medical_title: profileData.medical_title || '',
            specialty: profileData.medical_specialty,
            educational_background: profileData.educational_board,
            phone_number: profileData.phone_number,
            experience: profileData.experience,
            subspecialty: profileData.subspecialization || '',
            board_certifications: profileData.certifications || '',
            awards_recognitions: profileData.recognitions || '',
            languages_spoken: profileData.languages || '',
            professional_affiliations: profileData.professional_affiliation || '',
            procedures_performed: profileData.procedures_performed || '',
            treatment_services_expertise: profileData.treatment_services || '',
            hospital_id: profileData.facilityId ? parseInt(profileData.facilityId) : null,
            specialty_id: profileData.specialtyId ? parseInt(profileData.specialtyId) : null,
            country_id: profileData.countryId ? parseInt(profileData.countryId) : null,
            auth_id: userId, // Store UUID as auth_id
            status: 'pending',
            // Fix: Add missing image fields to ensure they are saved in doctors_registration table
            image_path: profileData.image_path || null,
            profile_image: profileData.profile_image || null,
            personal_biography: profileData.personal_biography || null,
            work_history: profileData.work_history || null,
            timings: profileData.timings || null
          })
          .select() // Select the inserted row to get all its data
          .single();
          
        if (error) {
          throw new Error(`Failed to create doctor profile: ${error.message}`);
        }

        // Auto-approval logic for doctors
        try {
          // Check if a doctor with the same fullname AND hospital already exists in the 'doctors' table
          const { data: existingDoctor, error: checkDoctorError } = await supabaseAdmin
            .from('doctors')
            .select('doctor_id')
            .eq('fullname', doctorRegProfile.fullname)
            .eq('hospital', doctorRegProfile.hospital)
            .single();

          if (checkDoctorError && checkDoctorError.code !== 'PGRST116') { // PGRST116 means no rows returned
            throw new Error(`Failed to check existing doctor: ${checkDoctorError.message}`);
          }

          if (!existingDoctor) {
            // If no match found, auto-approve: copy data to 'doctors' table
            const doctorProfileToInsert = {
              fullname: doctorRegProfile.fullname,
              hospital: doctorRegProfile.hospital,
              medical_title: doctorRegProfile.medical_title,
              specialty: doctorRegProfile.specialty,
              subspecialty: doctorRegProfile.subspecialty,
              educational_background: doctorRegProfile.educational_background,
              board_certifications: doctorRegProfile.board_certifications,
              experience: doctorRegProfile.experience,
              publications: doctorRegProfile.publications,
              awards_recognitions: doctorRegProfile.awards_recognitions,
              phone_number: doctorRegProfile.phone_number,
              email: doctorRegProfile.email,
              languages_spoken: doctorRegProfile.languages_spoken,
              professional_affiliations: doctorRegProfile.professional_affiliations,
              procedures_performed: doctorRegProfile.procedures_performed,
              treatment_services_expertise: doctorRegProfile.treatment_services_expertise,
              hospital_id: doctorRegProfile.hospital_id,
              image_path: doctorRegProfile.image_path,
              country_id: doctorRegProfile.country_id,
              specialty_id: doctorRegProfile.specialty_id,
              last_updated: doctorRegProfile.last_updated,
              auth_id: doctorRegProfile.auth_id,
              profile_image: doctorRegProfile.profile_image,
              personal_biography: doctorRegProfile.personal_biography,
              work_history: doctorRegProfile.work_history,
              timings: doctorRegProfile.timings,
              // wins, losses, form, rating, review_count, draws will use default values
            };

            const { data: newDoctorRecord, error: insertDoctorError } = await supabaseAdmin
              .from('doctors')
              .insert(doctorProfileToInsert)
              .select('doctor_id') // Select the newly generated doctor_id
              .single();

            if (insertDoctorError) {
              throw new Error(`Failed to insert into doctors table: ${insertDoctorError.message}`);
            }

            // Update numericId to the new doctor_id from the 'doctors' table
            numericId = newDoctorRecord.doctor_id;

            // Delete from doctors_registration table
            const { error: deleteRegError } = await supabaseAdmin
              .from('doctors_registration')
              .delete()
              .eq('doctor_id', doctorRegProfile.doctor_id);

            if (deleteRegError) {
              console.warn(`Failed to delete from doctors_registration: ${deleteRegError.message}`);
              // Do not throw error here, as the main operations (insert into doctors and update auth_credentials) were successful
            }
          }
        } catch (autoApproveError: any) {
          console.error('Error during doctor auto-approval:', autoApproveError);
          // Do not re-throw, allow registration to complete with pending status if auto-approval fails
        }
      } else if (userType === 'patient') {
        console.log('Inserting patient profile with data:', {
          user_id: numericId,
          email: email,
          username: profileData.username,
          first_name: profileData.firstName,
          last_name: profileData.lastName,
          gender: profileData.gender,
          city: profileData.city,
          country: profileData.country,
          age: profileData.age,
          user_type: 'patient',
          medical_condition: profileData.medical_condition || '',
          phone_number: profileData.phone_number || '',
          state: profileData.state_province_region || '',
          auth_id: userId
        });

        // Prepare user data for insertion
        const userInsertData = {
          user_id: numericId, // Use numeric ID to match user_profile_id
          email: email,
          username: profileData.username || `user_${numericId}`, // Fallback username if not provided
          first_name: profileData.firstName,
          last_name: profileData.lastName,
          gender: profileData.gender,
          city: profileData.city,
          country_id: profileData.countryId || null, // Use country_id instead of country
          age: profileData.age,
          user_type: 'patient',
          "medical condition": profileData.medical_condition || '', // Exact column name as per schema docs
          "Phone_Number": profileData.phone_number ? (() => {
            const phoneStr = profileData.phone_number.toString().replace(/[^\d.]/g, ''); // Remove non-numeric characters except decimal
            const phoneNum = parseFloat(phoneStr);
            return isNaN(phoneNum) ? null : phoneNum;
          })() : null, // Convert to numeric safely with validation
          "State": profileData.state_province_region || '', // Exact column name with capital S
          "Registration date": new Date().toISOString().split('T')[0], // Exact column name with space and capital R
          auth_id: userId // Store UUID as auth_id
        };

        console.log('Attempting to insert user data:', JSON.stringify(userInsertData, null, 2));

        const { error } = await supabaseAdmin
          .from('users')
          .insert(userInsertData);

        if (error) {
          console.error('Patient profile creation error:', {
            message: error.message,
            code: error.code,
            details: error.details,
            hint: error.hint,
            fullError: error
          });
          throw new Error(`Failed to create patient profile: ${error.message}`);
        }
      }
      
      // 2. Create auth_credentials record
      const authCredentialsData = {
        user_profile_id: numericId, // Use a numeric ID for user_profile_id
        email: email,
        hashed_password: hashedPassword,
        user_type: userType,
        is_verified: false // Explicitly set to false until verification
        // Don't set created_at and updated_at - let the database handle these with defaults
      };

      console.log('Attempting to insert auth credentials:', JSON.stringify({
        ...authCredentialsData,
        hashed_password: '[REDACTED]' // Don't log the actual password
      }, null, 2));

      const { data: authData, error: authError } = await supabaseAdmin
        .from('auth_credentials')
        .insert(authCredentialsData)
        .select()
        .single();

      if (authError) {
        console.error('Auth credentials creation error:', {
          message: authError.message,
          code: authError.code,
          details: authError.details,
          hint: authError.hint,
          fullError: authError
        });
        throw new Error(`Failed to create authentication record: ${authError.message}`);
      }
      
      // 3. Create a separate verification token entry
      // First try to see if we can directly insert into the table
      let tokenError = null;
      try {
        // Create verification token data object
        const verificationTokenData: VerificationToken = {
          user_id: numericId.toString(), // Convert to string
          token: verificationToken,
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          email: email
        };
        
        const { error: insertError } = await supabaseAdmin
          .from('verification_tokens')
          .insert(verificationTokenData);
          
        tokenError = insertError;
      } catch (insertCatchError: any) {
        // Table likely doesn't exist
        console.error("Error inserting verification token:", insertCatchError?.message);
        tokenError = new Error("Verification tokens table might not exist");
      }
      
      if (tokenError) {
        // We'll ignore this error and continue with the registration process
        // The verification process will need to be handled differently
        console.log("Failed to store verification token, but registration will continue");
      }

      // Send verification email
      try {
        console.log('Setting up email transport with Mailtrap...');
        
        // Explicitly set the site URL to ensure correct verification links
        let siteUrl = process.env.NEXT_PUBLIC_SITE_URL;
        if (!siteUrl) {
          // Fallback to a default URL if the environment variable is not set
          const isDev = process.env.NODE_ENV === 'development';
          siteUrl = isDev ? 'http://localhost:3000' : 'https://doctors-leagues.vercel.app';
          console.log('Using fallback site URL:', siteUrl);
        }
        
        const verificationUrl = `${siteUrl}/api/auth/custom/verify-email?token=${verificationToken}&userId=${numericId}`;
        console.log('Verification URL:', verificationUrl);
        
        const transporter = setupEmailTransport();
        console.log('Email transport created with Mailtrap config:', {
          host: 'sandbox.smtp.mailtrap.io',
          port: 2525,
          user: 'f5849f3bfce859' // Not showing password for security
        });
        console.log('Sending verification email to:', email);
        
        const mailResult = await transporter.sendMail({
          from: '"Doctors Leagues" <<EMAIL>>',
          to: email,
          subject: 'Verify your email address',
          html: `
          <!DOCTYPE html>
          <html lang="en">
          <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>Confirm Your Signup</title>
              <style>
                  body {
                      font-family: Arial, sans-serif;
                      background-color: #e0f7fa;
                      margin: 0;
                      padding: 0;
                  }
                  .container {
                      width: 100%;
                      max-width: 600px;
                      margin: 20px auto;
                      background-color: #ffffff;
                      padding: 20px;
                      border-radius: 8px;
                      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                  }
                  .header {
                      text-align: center;
                      margin-bottom: 20px;
                  }
                  .header img {
                      max-width: 150px;
                  }
                  .content {
                      font-size: 16px;
                      color: #388e3c;
                  }
                  .button {
                      display: inline-block;
                      background-color: ${userType === 'doctor' ? '#4caf50' : '#3b82f6'};
                      color: #ffffff;
                      text-decoration: none;
                      padding: 10px 20px;
                      border-radius: 5px;
                  }
                  .footer {
                      margin-top: 20px;
                      font-size: 14px;
                      color: #777777;
                      text-align: center;
                  }
              </style>
          </head>
          <body>
              <div class="container">
                  <div class="header">
                      <h1 style="color: ${userType === 'doctor' ? '#4caf50' : '#3b82f6'};">Doctors Leagues</h1>
                  </div>
                  <div class="content">
                      <h2 style="color: ${userType === 'doctor' ? '#4caf50' : '#3b82f6'};">Welcome to Doctors Leagues!</h2>
                      <p>Thank you for joining Doctors Leagues! We're excited to have you on board. Please click the button below to confirm your email address and complete your registration:</p>
                      <p style="text-align: center; margin: 30px 0;">
                          <a href="${verificationUrl}" class="button">Confirm Your Email</a>
                      </p>
                      <p>Or copy and paste this link into your browser:</p>
                      <p style="word-break: break-all; color: #3b82f6;">${verificationUrl}</p>
                      <p>If you didn't sign up, please disregard this email.</p>
                  </div>
                  <div class="footer">
                      <p>&copy; ${new Date().getFullYear()} Doctors Leagues. All rights reserved.</p>
                  </div>
              </div>
          </body>
          </html>
          `
        });
        
        console.log('Verification email sent successfully:', mailResult.messageId);
      } catch (emailError) {
        console.error('Error sending verification email:', emailError);
        // Don't fail the registration process if email sending fails
      }

      return res.status(201).json({
        success: true,
        userId: userId, // Return the UUID, not the numeric ID
        numericId: numericId, // Also return numeric ID for reference
        message: 'Registration successful. Please verify your email.'
        });

    } catch (error: any) {
      console.error('Registration transaction error:', error);
      
      // Attempt to clean up if partial registration occurred
      try {
        // Delete auth credentials if it exists
        await supabaseAdmin
          .from('auth_credentials')
          .delete()
          .eq('user_profile_id', numericId); // Use numericId as it's the user_profile_id

        // Delete doctor profile if created (either in doctors or doctors_registration)
        if (userType === 'doctor') {
          // Check if the doctor was auto-approved and moved to 'doctors'
          const { data: doctorInDoctors, error: checkDoctorsError } = await supabaseAdmin
            .from('doctors')
            .select('doctor_id')
            .eq('auth_id', userId)
            .single();

          if (doctorInDoctors) {
            await supabaseAdmin
              .from('doctors')
              .delete()
              .eq('auth_id', userId);
          } else {
            // If not in 'doctors', it must be in 'doctors_registration' (or not created yet)
            await supabaseAdmin
              .from('doctors_registration')
              .delete()
              .eq('auth_id', userId);
          }
        }

        // Delete user profile if created
        if (userType === 'patient') {
          await supabaseAdmin
            .from('users')
            .delete()
            .eq('auth_id', userId);
        }
      } catch (cleanupError) {
        console.error('Error during cleanup:', cleanupError);
      }
      
      return res.status(500).json({
        success: false,
        error: error.message || 'An unexpected error occurred'
      });
    }

  } catch (error) {
    console.error('Registration error:', error);
    return res.status(500).json({
      success: false,
      error: 'An unexpected error occurred'
    });
    }
}