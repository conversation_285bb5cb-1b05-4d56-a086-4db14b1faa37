import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';
import nodemailer from 'nodemailer';

// Types
type ApiResponse = {
  success: boolean;
  message?: string;
  error?: string;
};

// Initialize Supabase client with service role for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Email verification setup
const setupEmailTransport = () => {
  return nodemailer.createTransport({
    host: 'sandbox.smtp.mailtrap.io',
    port: 587,
    secure: false,
    auth: {
      user: 'f5849f3bfce859',
      pass: '971bf6348490c1'
    }
  });
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        error: 'Email is required'
      });
    }

    // Find user in auth_credentials table
    const { data: authUser, error: authError } = await supabaseAdmin
      .from('auth_credentials')
      .select('user_id, user_type, is_verified')
      .eq('email', email)
      .single();

    if (authError) {
      console.error('Error finding user:', authError);
      // For security reasons, don't reveal if email exists or not
      return res.status(200).json({
        success: true,
        message: 'If your email is registered, a new verification link has been sent.'
      });
    }

    // Check if already verified
    if (authUser.is_verified) {
      return res.status(400).json({
        success: false,
        error: 'Your email is already verified. Please login.'
      });
    }

    // Generate new verification token
    const verificationToken = uuidv4();

    // Update auth_credentials with new token
    const { error: updateError } = await supabaseAdmin
      .from('auth_credentials')
      .update({
        verification_token: verificationToken,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', authUser.user_id);

    if (updateError) {
      console.error('Error updating verification token:', updateError);
      return res.status(500).json({
        success: false,
        error: 'Failed to generate a new verification link'
      });
    }

    // Send verification email
    try {
      const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
      const verificationUrl = `${siteUrl}/api/auth/custom/verify-email?token=${verificationToken}&userId=${authUser.user_id}`;
      
      const transporter = setupEmailTransport();
      
      await transporter.sendMail({
        from: '"Doctors Leagues" <<EMAIL>>',
        to: email,
        subject: 'Verify your email address',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #0f172a; color: white; padding: 20px; text-align: center;">
              <h1>Doctors Leagues</h1>
            </div>
            <div style="padding: 20px; border: 1px solid #e2e8f0; border-top: none;">
              <h2>Verify your email address</h2>
              <p>Please click the button below to verify your email address:</p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${verificationUrl}" style="background-color: ${authUser.user_type === 'doctor' ? '#22c55e' : '#3b82f6'}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
                  Verify Email
                </a>
              </div>
              <p>Or copy and paste this link into your browser:</p>
              <p style="word-break: break-all; color: #3b82f6;">${verificationUrl}</p>
              <p>If you did not create this account, you can safely ignore this email.</p>
            </div>
            <div style="background-color: #f8fafc; padding: 15px; text-align: center; font-size: 12px; color: #64748b;">
              &copy; ${new Date().getFullYear()} Doctors Leagues. All rights reserved.
            </div>
          </div>
        `
      });
      
      console.log('Verification email resent successfully');
    } catch (emailError) {
      console.error('Error resending verification email:', emailError);
      return res.status(500).json({
        success: false,
        error: 'Failed to send verification email'
      });
    }

    return res.status(200).json({
      success: true,
      message: 'A new verification link has been sent to your email'
    });

  } catch (error) {
    console.error('Resend verification error:', error);
    return res.status(500).json({
      success: false,
      error: 'An unexpected error occurred'
    });
  }
} 