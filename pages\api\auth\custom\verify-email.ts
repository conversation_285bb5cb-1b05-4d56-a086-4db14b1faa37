import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

// Types
type ApiResponse = {
  success: boolean;
  message?: string;
  error?: string;
};

// Initialize Supabase client with service role for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  // Only allow GET method for verification link clicks
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  // Helper function to redirect to the appropriate verification page
  function redirectToVerifiedPage(verified: boolean, message: string) {
    const acceptHeader = req.headers.accept || '';
    const wantsHtml = acceptHeader.includes('text/html');
    const encodedMessage = encodeURIComponent(message);
    
    // For browsers or any client accepting HTML, redirect to the HTML version
    if (wantsHtml) {
      // Use the static HTML file in public directory
      return res.redirect(302, `/verified.html?verified=${verified}&message=${encodedMessage}`);
    } else {
      // For API clients or programmatic access, use the React component
      return res.redirect(302, `/verified?verified=${verified}&message=${encodedMessage}`);
    }
  }

  try {
    const { token, userId } = req.query;

    if (!token || !userId || typeof token !== 'string' || typeof userId !== 'string') {
      return redirectToVerifiedPage(false, 'Invalid verification link. Missing token or userId.');
    }

    // Get user data from auth_credentials
    const { data: authUser, error: authError } = await supabaseAdmin
      .from('auth_credentials')
      .select('user_type, email, is_verified')
      .eq('user_profile_id', userId)
      .single();

    if (authError || !authUser) {
      console.error('Error finding user:', authError);
      return redirectToVerifiedPage(false, 'User not found. Please check your email link.');
    }

    // Check if user is already verified
    if (authUser.is_verified) {
      return redirectToVerifiedPage(true, 'Your email is already verified. You can now log in.');
    }

    let tokenValid = false;
    
    // Try to find the token in verification_tokens table
    try {
      const { data: tokenData, error: tokenError } = await supabaseAdmin
        .from('verification_tokens')
        .select('*')
        .eq('user_id', userId)
        .eq('token', token)
        .single();
      
      console.log('Token lookup result:', { tokenFound: !!(!tokenError && tokenData), userId, token });

      if (!tokenError && tokenData) {
        // Check if token has expired
        if (new Date(tokenData.expires_at) < new Date()) {
          return redirectToVerifiedPage(false, 'Verification link has expired. Please request a new one.');
        }
        
        tokenValid = true;
        
        // Delete the used token
        const { error: deleteError } = await supabaseAdmin
          .from('verification_tokens')
          .delete()
          .eq('user_id', userId)
          .eq('token', token);

        if (deleteError) {
          console.error('Error deleting verification token:', deleteError);
          // Non-critical error; continue with the verification process
        }
      } else {
        console.log('Token not found in database:', tokenError);
      }
    } catch (tokenCheckError) {
      console.log('Verification tokens table error:', tokenCheckError);
      // For dev testing - allow verification to proceed even without token match
      if (process.env.NODE_ENV === 'development') {
        console.log('DEV MODE: Allowing verification without token match');
        tokenValid = true;
      }
    }
    
    // In development, allow verification even without a valid token (for testing)
    if (process.env.NODE_ENV === 'development' && !tokenValid) {
      console.log('DEV MODE: Overriding token validation');
      tokenValid = true;
    }
    
    if (!tokenValid) {
      console.log('Invalid verification token attempt');
      return redirectToVerifiedPage(false, 'Invalid verification token. The token may have expired or been used already.');
    }

    // Update auth_credentials to mark user as verified
    const { error: authUpdateError } = await supabaseAdmin
      .from('auth_credentials')
      .update({ is_verified: true })
      .eq('user_profile_id', userId);

    if (authUpdateError) {
      console.error('Error updating auth_credentials:', authUpdateError);
      return redirectToVerifiedPage(false, 'Failed to verify your account. Please try again or contact support.');
    }

    // Also update the user/doctor profile if needed
    let profileUpdateError = null;
    if (authUser.user_type === 'patient') {
      const { error } = await supabaseAdmin
        .from('users')
        .update({ verified: true })
        .eq('user_id', userId);
      profileUpdateError = error;
    } else if (authUser.user_type === 'doctor') {
      const { error } = await supabaseAdmin
        .from('doctors')
        .update({ verified: true })
        .eq('doctor_id', userId);
      profileUpdateError = error;
    }

    if (profileUpdateError) {
      console.error(`Error updating ${authUser.user_type} profile:`, profileUpdateError);
      // Don't fail the verification - auth_credentials is the source of truth
      console.log(`Profile update failed for ${authUser.user_type}, but auth verification succeeded.`);
    }

    // Redirect to verified page with success message
    return redirectToVerifiedPage(true, 'Your email has been verified. You can now log in.');

  } catch (error) {
    console.error('Email verification error:', error);
    return redirectToVerifiedPage(false, 'An unexpected error occurred during verification.');
  }
} 