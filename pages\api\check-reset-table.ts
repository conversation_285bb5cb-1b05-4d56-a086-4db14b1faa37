import { NextApiRequest, NextApiResponse } from 'next'
import { createClient } from '@supabase/supabase-js'

// This endpoint checks if the password_reset_tokens table exists
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Initialize Supabase client with service role
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Query to check if table exists
    const { data, error } = await supabaseAdmin
      .from('password_reset_tokens')
      .select('id')
      .limit(1)
    
    if (error) {
      console.error('Error checking table:', error)
      return res.status(200).json({ 
        tableExists: false, 
        error: error.message 
      })
    }

    return res.status(200).json({ 
      tableExists: true,
      message: 'Table exists and is accessible'
    })
  } catch (error: any) {
    console.error('Unhandled error:', error)
    return res.status(500).json({ 
      error: 'An unexpected server error occurred', 
      details: error.message 
    })
  }
} 