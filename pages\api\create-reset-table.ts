import { NextApiRequest, NextApiResponse } from 'next'
import { createClient } from '@supabase/supabase-js'

// This endpoint creates the password_reset_tokens table using direct SQL execution
// IMPORTANT: This should only be run once and then removed for security!
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Get the migration secret from the request to prevent unauthorized access
    const { secret } = req.body
    
    // Verify the secret (use a strong secret in production)
    if (secret !== 'your-secret-migration-key') {
      return res.status(401).json({ error: 'Unauthorized' })
    }

    console.log('Starting password_reset_tokens table creation')

    // Initialize Supabase client with service role
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Run each SQL statement separately for better error handling
    try {
      // Create the table
      console.log('Creating password_reset_tokens table')
      await supabaseAdmin.rpc('execute_sql', {
        sql: `
        CREATE TABLE IF NOT EXISTS public.password_reset_tokens (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL,
            email TEXT NOT NULL,
            token TEXT NOT NULL UNIQUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
            used BOOLEAN DEFAULT FALSE,
            user_type TEXT NOT NULL CHECK (user_type IN ('patient', 'doctor'))
        );`
      })

      // Create indexes
      console.log('Creating indexes')
      await supabaseAdmin.rpc('execute_sql', {
        sql: `
        CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_token ON public.password_reset_tokens(token);
        CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_user_id ON public.password_reset_tokens(user_id);
        CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_email ON public.password_reset_tokens(email);`
      })

      // Enable RLS
      console.log('Enabling Row Level Security')
      await supabaseAdmin.rpc('execute_sql', {
        sql: `ALTER TABLE public.password_reset_tokens ENABLE ROW LEVEL SECURITY;`
      })

      // Create policy
      console.log('Creating access policy')
      await supabaseAdmin.rpc('execute_sql', {
        sql: `
        CREATE POLICY "Password reset tokens are accessible only by service role" 
        ON public.password_reset_tokens
        USING (auth.jwt() IS NOT NULL AND auth.jwt()->>'role' = 'service_role');`
      })

      // Create cleanup function
      console.log('Creating cleanup function')
      await supabaseAdmin.rpc('execute_sql', {
        sql: `
        CREATE OR REPLACE FUNCTION cleanup_expired_reset_tokens()
        RETURNS void AS $$
        BEGIN
            DELETE FROM public.password_reset_tokens
            WHERE expires_at < NOW() OR used = TRUE;
        END;
        $$ LANGUAGE plpgsql;`
      })

      console.log('Migration completed successfully')
      return res.status(200).json({ 
        success: true, 
        message: 'Password reset tokens table created successfully' 
      })
    } catch (sqlError: any) {
      console.error('SQL execution error:', sqlError)
      return res.status(500).json({ 
        error: 'SQL execution failed', 
        details: sqlError.message || 'Unknown error'
      })
    }
  } catch (error: any) {
    console.error('Unhandled error:', error)
    return res.status(500).json({ 
      error: 'An unexpected server error occurred', 
      details: error.message || 'Unknown error' 
    })
  }
} 