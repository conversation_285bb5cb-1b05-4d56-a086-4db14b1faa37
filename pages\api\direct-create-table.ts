import { NextApiRequest, NextApiResponse } from 'next'
import { createClient } from '@supabase/supabase-js'

// This endpoint directly creates the password_reset_tokens table
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Initialize Supabase client with service role
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Using direct SQL query
    console.log('Creating password_reset_tokens table...')
    
    // Create the table
    const { error: tableError } = await supabaseAdmin.rpc('run_sql', {
      query: `
        CREATE TABLE IF NOT EXISTS public.password_reset_tokens (
          id SERIAL PRIMARY KEY,
          user_id INTEGER NOT NULL,
          email TEXT NOT NULL,
          token TEXT NOT NULL UNIQUE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
          used BOOLEAN DEFAULT FALSE,
          user_type TEXT NOT NULL CHECK (user_type IN ('patient', 'doctor'))
        );
      `
    })

    if (tableError) {
      console.error('Error creating table:', tableError)
      return res.status(500).json({ error: 'Failed to create table', details: tableError.message })
    }

    // Create indexes and enable RLS
    const { error: indexesError } = await supabaseAdmin.rpc('run_sql', {
      query: `
        CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_token ON public.password_reset_tokens(token);
        CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_user_id ON public.password_reset_tokens(user_id);
        CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_email ON public.password_reset_tokens(email);
        ALTER TABLE public.password_reset_tokens ENABLE ROW LEVEL SECURITY;
      `
    })

    if (indexesError) {
      console.error('Error creating indexes:', indexesError)
      return res.status(500).json({ 
        error: 'Failed to create indexes', 
        details: indexesError.message 
      })
    }

    return res.status(200).json({ 
      success: true,
      message: 'Password reset tokens table created successfully'
    })
  } catch (error: any) {
    console.error('Unhandled error:', error)
    return res.status(500).json({ 
      error: 'An unexpected server error occurred', 
      details: error.message 
    })
  }
} 