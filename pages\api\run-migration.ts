import { NextApiRequest, NextApiResponse } from 'next'
import { createClient } from '@supabase/supabase-js'
import fs from 'fs'
import path from 'path'

// This endpoint runs a migration script to set up the password_reset_tokens table
// IMPORTANT: This should only be run once and then removed for security!
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Get the migration secret from the request to prevent unauthorized access
    const { secret } = req.body
    
    // Verify the secret (use a strong secret in production)
    if (secret !== 'your-secret-migration-key') {
      return res.status(401).json({ error: 'Unauthorized' })
    }

    console.log('Starting migration to create password_reset_tokens table')

    // Initialize Supabase client with service role
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // SQL for creating password_reset_tokens table
    const createTableSQL = `
    -- Creates the password_reset_tokens table for custom password reset
    CREATE TABLE IF NOT EXISTS public.password_reset_tokens (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        email TEXT NOT NULL,
        token TEXT NOT NULL UNIQUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
        used BOOLEAN DEFAULT FALSE,
        user_type TEXT NOT NULL CHECK (user_type IN ('patient', 'doctor'))
    );

    -- Create indexes for better performance
    CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_token ON public.password_reset_tokens(token);
    CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_user_id ON public.password_reset_tokens(user_id);
    CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_email ON public.password_reset_tokens(email);

    -- Enable Row Level Security
    ALTER TABLE public.password_reset_tokens ENABLE ROW LEVEL SECURITY;

    -- Create policy to restrict access to password_reset_tokens
    DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT FROM pg_policies 
            WHERE tablename = 'password_reset_tokens' 
            AND policyname = 'Password reset tokens are accessible only by service role'
        ) THEN
            CREATE POLICY "Password reset tokens are accessible only by service role" 
            ON public.password_reset_tokens
            USING (auth.jwt() IS NOT NULL AND auth.jwt()->>'role' = 'service_role');
        END IF;
    END
    $$;

    -- Create a function to clean up expired tokens (run as a cron job)
    CREATE OR REPLACE FUNCTION cleanup_expired_reset_tokens()
    RETURNS void AS $$
    BEGIN
        DELETE FROM public.password_reset_tokens
        WHERE expires_at < NOW() OR used = TRUE;
    END;
    $$ LANGUAGE plpgsql;
    `;

    // Execute the SQL statement
    const { error } = await supabaseAdmin.rpc('pgmigrate', { migration: createTableSQL });

    if (error) {
      console.error('Error executing migration:', error);
      
      // Try direct SQL execution if RPC fails
      try {
        const { error: directError } = await supabaseAdmin.rpc('pgmigrate_direct', { query: createTableSQL });
        
        if (directError) {
          console.error('Direct migration also failed:', directError);
          return res.status(500).json({ error: 'Failed to run migration: ' + directError.message });
        }
        
        console.log('Migration executed successfully via direct SQL');
        return res.status(200).json({ success: true, message: 'Migration executed successfully via direct SQL' });
      } catch (err: any) {
        console.error('Exception in direct migration:', err);
        return res.status(500).json({ error: 'Failed to run migration: ' + err.message });
      }
    }

    console.log('Migration executed successfully');
    return res.status(200).json({ success: true, message: 'Migration executed successfully' });
  } catch (error: any) {
    console.error('Unhandled error:', error);
    return res.status(500).json({ error: 'An unexpected server error occurred: ' + error.message });
  }
} 