import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

// Create Supabase admin client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('=== TEST PATIENT REGISTRATION ===');
    console.log('Request body:', JSON.stringify(req.body, null, 2));

    // Test 1: Check if we can connect to Supabase
    const { data: testConnection, error: connectionError } = await supabaseAdmin
      .from('users')
      .select('user_id')
      .limit(1);

    if (connectionError) {
      console.error('Connection test failed:', connectionError);
      return res.status(500).json({
        success: false,
        error: 'Database connection failed',
        details: connectionError
      });
    }

    console.log('✅ Database connection successful');

    // Test 2: Check users table structure
    const { data: tableStructure, error: structureError } = await supabaseAdmin
      .from('users')
      .select('*')
      .limit(1);

    if (structureError) {
      console.error('Table structure check failed:', structureError);
      return res.status(500).json({
        success: false,
        error: 'Failed to check table structure',
        details: structureError
      });
    }

    const columns = tableStructure && tableStructure.length > 0 
      ? Object.keys(tableStructure[0]) 
      : [];

    console.log('✅ Users table columns:', columns);

    // Test 3: Try a simple insert with minimal data
    const testData = {
      username: `test_${Date.now()}`,
      email: `test_${Date.now()}@example.com`,
      first_name: 'Test',
      last_name: 'User',
      user_type: 'patient',
      age: 25
    };

    console.log('Attempting test insert with minimal data:', testData);

    const { data: insertResult, error: insertError } = await supabaseAdmin
      .from('users')
      .insert(testData)
      .select()
      .single();

    if (insertError) {
      console.error('Test insert failed:', insertError);
      return res.status(500).json({
        success: false,
        error: 'Test insert failed',
        details: insertError,
        availableColumns: columns
      });
    }

    console.log('✅ Test insert successful:', insertResult);

    // Clean up test record
    await supabaseAdmin
      .from('users')
      .delete()
      .eq('user_id', insertResult.user_id);

    console.log('✅ Test record cleaned up');

    return res.status(200).json({
      success: true,
      message: 'All tests passed',
      availableColumns: columns,
      testResult: insertResult
    });

  } catch (error: any) {
    console.error('Test endpoint error:', error);
    return res.status(500).json({
      success: false,
      error: 'Unexpected error',
      details: error.message,
      stack: error.stack
    });
  }
}
