import type { NextApiRequest, NextApiResponse } from 'next';
import { withAuth, AuthenticatedUser } from '@/lib/auth/api-auth-utils';
import { createServiceRoleClient } from '@/lib/supabase-client';

interface ProfileResponse {
  success: boolean;
  profile?: any;
  error?: string;
}

async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ProfileResponse>,
  user: AuthenticatedUser
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ success: false, error: `Method ${req.method} Not Allowed` });
  }

  try {
    const supabase = createServiceRoleClient();
    const { userId, userType } = user;

    // Determine which table to query based on user type
    const table = userType === 'patient' ? 'users' : 'doctors';
    
    // The column name for the ID might be different between tables
    const idColumn = userType === 'patient' ? 'id' : 'id'; // Adjust if needed
    
    // Fetch profile data
    const { data, error } = await supabase
      .from(table)
      .select('*')
      .eq(idColumn, userId)
      .single();

    if (error) {
      console.error(`Error fetching ${userType} profile:`, error);
      return res.status(500).json({ 
        success: false, 
        error: `Failed to fetch ${userType} profile data.` 
      });
    }

    if (!data) {
      return res.status(404).json({ 
        success: false, 
        error: `${userType} profile not found.` 
      });
    }

    return res.status(200).json({
      success: true,
      profile: data
    });
  } catch (error: any) {
    console.error('Profile API Error:', error);
    return res.status(500).json({ 
      success: false, 
      error: error.message || 'An unexpected server error occurred.' 
    });
  }
}

export default withAuth(handler); 