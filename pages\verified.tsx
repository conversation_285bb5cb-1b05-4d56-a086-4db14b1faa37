import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import Head from 'next/head';

// Inline styles for the page
const styles = {
  container: {
    minHeight: '100vh',
    backgroundColor: '#0a1126',
    display: 'flex',
    flexDirection: 'column' as 'column',
    justifyContent: 'center',
    padding: '3rem 1rem'
  },
  cardContainer: {
    maxWidth: '28rem',
    margin: '0 auto',
    width: '100%'
  },
  title: {
    marginTop: '1.5rem',
    textAlign: 'center' as 'center',
    fontSize: '1.875rem',
    fontWeight: 800,
    color: '#ffffff'
  },
  card: {
    marginTop: '2rem',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    padding: '2rem 1rem',
    borderRadius: '0.5rem',
    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
    border: '1px solid rgba(255, 255, 255, 0.1)'
  },
  textCenter: {
    textAlign: 'center' as 'center'
  },
  iconContainer: {
    backgroundColor: 'rgba(16, 185, 129, 0.2)',
    padding: '1rem',
    borderRadius: '9999px',
    width: '6rem',
    height: '6rem',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    margin: '0 auto 1rem auto'
  },
  iconContainerWarning: {
    backgroundColor: 'rgba(234, 179, 8, 0.2)',
    padding: '1rem',
    borderRadius: '9999px',
    width: '6rem',
    height: '6rem',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    margin: '0 auto 1rem auto'
  },
  icon: {
    height: '3rem',
    width: '3rem',
    color: 'rgb(16, 185, 129)'
  },
  iconWarning: {
    height: '3rem',
    width: '3rem',
    color: 'rgb(234, 179, 8)'
  },
  message: {
    marginBottom: '1rem',
    fontSize: '1.125rem',
    color: 'rgba(255, 255, 255, 0.9)'
  },
  countdown: {
    fontSize: '0.875rem',
    color: 'rgba(255, 255, 255, 0.6)',
    marginBottom: '1.5rem'
  },
  button: {
    display: 'inline-flex',
    justifyContent: 'center',
    padding: '0.75rem 1.5rem',
    border: '1px solid transparent',
    borderRadius: '0.375rem',
    fontSize: '0.875rem',
    fontWeight: 500,
    color: 'white',
    backgroundColor: '#2563eb',
    cursor: 'pointer',
    textDecoration: 'none'
  },
  buttonSecondary: {
    display: 'inline-flex',
    justifyContent: 'center',
    padding: '0.75rem 1.5rem',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    borderRadius: '0.375rem',
    fontSize: '0.875rem',
    fontWeight: 500,
    color: 'rgba(255, 255, 255, 0.9)',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    cursor: 'pointer',
    textDecoration: 'none',
    marginLeft: '1rem'
  },
  flexCenter: {
    display: 'flex',
    justifyContent: 'center'
  },
  footer: {
    marginTop: '3rem',
    textAlign: 'center' as 'center'
  },
  footerTitle: {
    fontSize: '1.5rem',
    marginBottom: '0.5rem',
    color: 'white',
    fontWeight: 'bold'
  },
  footerText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: '0.875rem'
  }
};

const VerifiedPage = () => {
  const router = useRouter();
  const { verified, message } = router.query;
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    if (verified === 'true') {
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            router.push('/auth/login-choice');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [verified, router]);

  // Make sure we don't render prematurely
  if (typeof verified === 'undefined') {
    return <div style={styles.container}><div style={styles.cardContainer}>Loading...</div></div>;
  }

  return (
    <>
      <Head>
        <title>Email Verified | Doctors Leagues</title>
      </Head>

      {/* Theme-specific background styling */}
      <style jsx>{`
        /* Light theme: Light green background to match homepage */
        @media (prefers-color-scheme: light) {
          .verification-container {
            background: linear-gradient(135deg, hsl(120, 25%, 95%) 0%, hsl(120, 20%, 97%) 100%) !important;
          }
          .verification-title {
            color: hsl(140, 50%, 20%) !important;
          }
          .verification-text {
            color: hsl(140, 30%, 35%) !important;
          }
        }

        /* Dark theme: Keep original blue background */
        @media (prefers-color-scheme: dark) {
          .verification-container {
            background-color: #0a1126 !important;
          }
          .verification-title {
            color: #ffffff !important;
          }
          .verification-text {
            color: rgba(255, 255, 255, 0.8) !important;
          }
        }
      `}</style>

      <div style={{...styles.container}} className="verification-container">
        <div style={styles.cardContainer}>
          <h2 style={styles.title} className="verification-title">
            {verified === 'true' ? 'Email Verified!' : 'Verification Status'}
          </h2>

          <div style={styles.card}>
            {verified === 'true' ? (
              <div style={styles.textCenter}>
                <div style={styles.iconContainer}>
                  <svg
                    style={styles.icon}
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
                <p style={styles.message} className="verification-text">
                  {message || 'Your email has been successfully verified!'}
                </p>
                <p style={styles.countdown} className="verification-text">
                  Redirecting to login in {countdown} {countdown === 1 ? 'second' : 'seconds'}...
                </p>
                <Link href="/auth/login-choice" style={styles.button}>
                  Login Now
                </Link>
              </div>
            ) : (
              <div style={styles.textCenter}>
                <div style={styles.iconContainerWarning}>
                  <svg
                    style={styles.iconWarning}
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                    />
                  </svg>
                </div>
                <p style={styles.message}>{message || 'Verification failed or already completed.'}</p>
                <div style={styles.flexCenter}>
                  <Link href="/auth/login-choice" style={styles.button}>
                    Login
                  </Link>
                  <Link href="/" style={styles.buttonSecondary}>
                    Back to Home
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
        
        <div style={styles.footer}>
          <h1 style={styles.footerTitle}>DOCTORS LEAGUES</h1>
          <p style={styles.footerText}>connecting healthcare professionals through sports</p>
        </div>
      </div>
    </>
  );
};

export default VerifiedPage; 