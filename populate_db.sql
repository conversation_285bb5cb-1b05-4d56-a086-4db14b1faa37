-- First ensure the countries table has Bahrain with ID 1
INSERT INTO countries (country_id, country_name) 
VALUES (1, 'Bahrain')
ON CONFLICT (country_id) DO UPDATE SET country_name = 'Bahrain';

-- Add specialties if they don't exist
INSERT INTO specialties (specialty_id, name, description)
VALUES 
(1, 'Cardiology', 'Diagnosis and treatment of heart disorders'),
(2, 'Neurology', 'Study and treatment of disorders of the nervous system'),
(3, 'Orthopedics', 'Treatment of the musculoskeletal system'),
(4, 'Pediatrics', 'Medical care of infants, children, and adolescents'),
(5, 'Oncology', 'Treatment of cancer'),
(6, 'Dermatology', 'Diagnosis and treatment of skin disorders'),
(7, 'Gastroenterology', 'Diagnosis and treatment of digestive system disorders'),
(8, 'Endocrinology', 'Treatment of hormone-related disorders')
ON CONFLICT (specialty_id) DO NOTHING;

-- Add hospitals
INSERT INTO hospitals (hospital_id, name, address, city, country_id, rating, specialties, hospital_type)
VALUES 
(1, 'Royal Bahrain Hospital', '123 King Faisal Highway', 'Manama', 1, 4.8, ARRAY[1, 2, 3], 'General'),
(2, 'Bahrain Specialist Hospital', '456 Sheikh Isa Road', '<PERSON>ffair', 1, 4.6, ARRAY[1, 4, 5], 'Specialized'),
(3, 'American Mission Hospital', '789 Exhibition Road', 'Manama', 1, 4.7, ARRAY[3, 6, 7], 'General'),
(4, 'King Hamad University Hospital', '101 Shaikh Isa bin Salman Highway', 'Al Sayh', 1, 4.9, ARRAY[1, 2, 3, 4, 5], 'Teaching'),
(5, 'Salmaniya Medical Complex', '202 Road 2904', 'Salmaniya', 1, 4.5, ARRAY[1, 2, 3, 4, 5, 6, 7, 8], 'General'),
(6, 'Ibn Al-Nafees Hospital', '303 Isa Al Kabeer Avenue', 'Manama', 1, 4.4, ARRAY[6, 7, 8], 'Specialized'),
(7, 'Bahrain Defence Force Hospital', '404 Sheikh Isa Road', 'Riffa', 1, 4.7, ARRAY[1, 3, 5], 'Military'),
(8, 'Middle East Hospital', '505 Khalifa Avenue', 'Segaya', 1, 4.3, ARRAY[2, 4, 6], 'General'),
(9, 'Royal Maternity Hospital', '606 King Faisal Road', 'Manama', 1, 4.8, ARRAY[4], 'Specialized'),
(10, 'Al Hilal Hospital', '707 Sheikh Hamad Avenue', 'Muharraq', 1, 4.4, ARRAY[1, 3, 7], 'General'),
(11, 'International Hospital of Bahrain', '808 Diplomatic Area', 'Manama', 1, 4.6, ARRAY[2, 5, 8], 'General'),
(12, 'Awali Hospital', '909 Awali Road', 'Awali', 1, 4.2, ARRAY[1, 3, 7], 'General')
ON CONFLICT (hospital_id) DO UPDATE SET 
  name = EXCLUDED.name,
  address = EXCLUDED.address,
  city = EXCLUDED.city,
  country_id = EXCLUDED.country_id,
  rating = EXCLUDED.rating,
  specialties = EXCLUDED.specialties,
  hospital_type = EXCLUDED.hospital_type;

-- Add doctors
INSERT INTO doctors (doctor_id, auth_id, fullname, username, medical_title, specialty_id, educational_background, experience, address, city, country_id, email, phone_number, rating, wins, draws, losses, hospital_id)
VALUES 
(1, null, 'Dr. Ahmed Al-Haddad', 'dr_ahmed', 'Chief Cardiologist', 1, 'Harvard Medical School', 15, '123 Medical Boulevard', 'Manama', 1, '<EMAIL>', '+973-1234-5678', 4.9, 45, 5, 3, 1),
(2, null, 'Dr. Fatima Al-Khalifa', 'dr_fatima', 'Neurologist', 2, 'Johns Hopkins University', 12, '456 Health Street', 'Manama', 1, '<EMAIL>', '+973-2345-6789', 4.8, 40, 8, 4, 1),
(3, null, 'Dr. Mohammed Al-Bahrani', 'dr_mohammed', 'Orthopedic Surgeon', 3, 'Mayo Clinic College of Medicine', 18, '789 Care Road', 'Riffa', 1, '<EMAIL>', '+973-3456-7890', 4.7, 38, 10, 5, 2),
(4, null, 'Dr. Sara Al-Doseri', 'dr_sara', 'Pediatrician', 4, 'Stanford University School of Medicine', 10, '101 Child Avenue', 'Muharraq', 1, '<EMAIL>', '+973-4567-8901', 4.9, 42, 7, 2, 2),
(5, null, 'Dr. Khalid Al-Zayani', 'dr_khalid', 'Oncologist', 5, 'University of California, San Francisco', 14, '202 Cancer Research Blvd', 'Manama', 1, '<EMAIL>', '+973-5678-9012', 4.8, 39, 9, 4, 3),
(6, null, 'Dr. Aisha Al-Mannai', 'dr_aisha', 'Dermatologist', 6, 'University of Michigan Medical School', 9, '303 Skin Health Road', 'Manama', 1, '<EMAIL>', '+973-6789-0123', 4.6, 35, 12, 6, 3),
(7, null, 'Dr. Jassim Al-Hamer', 'dr_jassim', 'Gastroenterologist', 7, 'Columbia University College of Physicians and Surgeons', 16, '404 Digestive Street', 'Riffa', 1, '<EMAIL>', '+973-7890-1234', 4.7, 37, 11, 5, 4),
(8, null, 'Dr. Layla Al-Sayed', 'dr_layla', 'Endocrinologist', 8, 'Weill Cornell Medical College', 11, '505 Hormone Avenue', 'Isa Town', 1, '<EMAIL>', '+973-8901-2345', 4.5, 33, 14, 7, 4),
(9, null, 'Dr. Yusuf Al-Malood', 'dr_yusuf', 'Cardiologist', 1, 'University of Pennsylvania School of Medicine', 13, '606 Heart Boulevard', 'Manama', 1, '<EMAIL>', '+973-9012-3456', 4.8, 41, 8, 3, 5),
(10, null, 'Dr. Mariam Al-Shirawi', 'dr_mariam', 'Neurologist', 2, 'Washington University School of Medicine', 15, '707 Brain Health Road', 'Muharraq', 1, '<EMAIL>', '+973-0123-4567', 4.7, 38, 10, 4, 5)
ON CONFLICT (doctor_id) DO UPDATE SET
  fullname = EXCLUDED.fullname,
  username = EXCLUDED.username,
  medical_title = EXCLUDED.medical_title,
  specialty_id = EXCLUDED.specialty_id,
  city = EXCLUDED.city,
  country_id = EXCLUDED.country_id,
  rating = EXCLUDED.rating,
  wins = EXCLUDED.wins,
  draws = EXCLUDED.draws,
  losses = EXCLUDED.losses,
  hospital_id = EXCLUDED.hospital_id;

-- Add more doctors (another 35 to reach 45 total)
INSERT INTO doctors (doctor_id, auth_id, fullname, username, medical_title, specialty_id, educational_background, experience, address, city, country_id, email, phone_number, rating, wins, draws, losses, hospital_id)
SELECT 
  i + 10, 
  null,
  'Dr. Bahraini Doctor ' || i,
  'dr_user' || i,
  CASE (i % 8) + 1
    WHEN 1 THEN 'Cardiologist'
    WHEN 2 THEN 'Neurologist'
    WHEN 3 THEN 'Orthopedic Surgeon'
    WHEN 4 THEN 'Pediatrician'
    WHEN 5 THEN 'Oncologist'
    WHEN 6 THEN 'Dermatologist'
    WHEN 7 THEN 'Gastroenterologist'
    WHEN 8 THEN 'Endocrinologist'
  END,
  (i % 8) + 1,
  'Medical University ' || (i % 5 + 1),
  (i % 10) + 5,
  (i * 10) || ' Medical Street',
  CASE i % 5
    WHEN 0 THEN 'Manama'
    WHEN 1 THEN 'Riffa'
    WHEN 2 THEN 'Muharraq'
    WHEN 3 THEN 'Isa Town'
    WHEN 4 THEN 'Hamad Town'
  END,
  1,
  'doctor' || i || '@example.com',
  '+973-1111-' || LPAD(i::text, 4, '0'),
  4.0 + (i % 10) * 0.1,
  (i % 20) + 20,
  (i % 15),
  (i % 10),
  (i % 12) + 1
FROM generate_series(1, 35) i
ON CONFLICT (doctor_id) DO NOTHING; 