/* Critical CSS for initial page render */
body {
  background-color: #000000;
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  margin: 0;
  padding: 0;
}

header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.9));
  border-bottom: 1px solid rgba(0, 128, 0, 0.2);
}

main {
  min-height: calc(100vh - 4rem);
  padding-top: 5rem;
}

.container {
  max-width: 100%;
  margin: 0 auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.brand-logo-container {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 3rem;
  min-height: 3rem;
}

.brand-text-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.brand-text {
  font-size: 1.5rem;
  line-height: 1.75rem;
  font-weight: bold;
  white-space: nowrap;
}

.text-primary {
  color: #22c55e;
}

.text-white {
  color: #ffffff;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-gray-400 {
  color: #9ca3af;
}

.nav-menu-item {
  font-size: 0.95rem;
  line-height: 1.5;
}

.nav-menu-icon {
  width: 0.95rem;
  height: 0.95rem;
} 