<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verified | Doctors Leagues</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #0a1126;
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 100vh;
        }

        /* Theme-aware background - Light theme gets light green, Dark theme keeps blue */
        @media (prefers-color-scheme: light) {
            body {
                background: linear-gradient(135deg, hsl(120, 25%, 95%) 0%, hsl(120, 20%, 97%) 100%) !important;
                color: hsl(140, 50%, 20%) !important;
            }
            .title {
                color: hsl(140, 50%, 20%) !important;
            }
            .message {
                color: hsl(140, 30%, 35%) !important;
            }
            .countdown {
                color: hsl(140, 30%, 35%) !important;
            }
        }
        .container {
            max-width: 28rem;
            margin: 0 auto;
            width: 100%;
            padding: 0 1rem;
        }
        .title {
            margin-top: 1.5rem;
            text-align: center;
            font-size: 1.875rem;
            font-weight: 800;
            color: #ffffff;
        }
        .card {
            margin-top: 2rem;
            background-color: rgba(255, 255, 255, 0.05);
            padding: 2rem 1rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .success-icon {
            background-color: rgba(16, 185, 129, 0.2);
            padding: 1rem;
            border-radius: 9999px;
            width: 6rem;
            height: 6rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem auto;
        }
        .warning-icon {
            background-color: rgba(234, 179, 8, 0.2);
            padding: 1rem;
            border-radius: 9999px;
            width: 6rem;
            height: 6rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem auto;
        }
        .message {
            margin-bottom: 1rem;
            font-size: 1.125rem;
            color: rgba(255, 255, 255, 0.9);
        }
        .countdown {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 1.5rem;
        }
        .button {
            display: inline-flex;
            justify-content: center;
            padding: 0.75rem 1.5rem;
            border: 1px solid transparent;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            color: white;
            background-color: #2563eb;
            cursor: pointer;
            text-decoration: none;
            transition: background-color 0.2s;
        }
        .button:hover {
            background-color: #1d4ed8;
        }
        .button-secondary {
            display: inline-flex;
            justify-content: center;
            padding: 0.75rem 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            background-color: rgba(255, 255, 255, 0.05);
            cursor: pointer;
            text-decoration: none;
            margin-left: 1rem;
            transition: background-color 0.2s;
        }
        .button-secondary:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        .flex-center {
            display: flex;
            justify-content: center;
        }
        .footer {
            margin-top: 3rem;
            text-align: center;
        }
        .footer h1 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }
        .footer p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="title">Email Verified!</h2>
        <div class="card">
            <div class="success-icon">
                <svg style="height: 3rem; width: 3rem; color: rgb(16, 185, 129);" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
            </div>
            <p class="message">Your email has been successfully verified!</p>
            <p class="countdown">Redirecting to login in 5 seconds...</p>
            <a href="/auth/login-choice" class="button">Login Now</a>
        </div>
    </div>
    
    <div class="footer">
        <h1>DOCTORS LEAGUES</h1>
        <p>connecting healthcare professionals through sports</p>
    </div>
    
    <script>
        // Basic script to check URL parameters
        window.onload = function() {
            var urlParams = new URLSearchParams(window.location.search);
            var verified = urlParams.get('verified');
            var message = urlParams.get('message');
            
            // Update title based on verification status
            if (verified === 'false') {
                document.querySelector('.title').innerText = 'Verification Status';
                
                // Replace success icon with warning icon
                var iconContainer = document.querySelector('.success-icon');
                iconContainer.classList.remove('success-icon');
                iconContainer.classList.add('warning-icon');
                
                iconContainer.innerHTML = `
                    <svg style="height: 3rem; width: 3rem; color: rgb(234, 179, 8);" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                `;
                
                // Update message
                document.querySelector('.message').innerText = message || 'Verification failed or already completed.';
                
                // Update countdown
                document.querySelector('.countdown').innerHTML = '';
                
                // Update buttons
                var buttonContainer = document.querySelector('.card');
                buttonContainer.removeChild(document.querySelector('.button'));
                
                var buttonsDiv = document.createElement('div');
                buttonsDiv.className = 'flex-center';
                buttonsDiv.innerHTML = `
                    <a href="/auth/login-choice" class="button">Login</a>
                    <a href="/" class="button-secondary">Back to Home</a>
                `;
                
                buttonContainer.appendChild(buttonsDiv);
            } else if (message) {
                // Update message if provided
                document.querySelector('.message').innerText = message;
            }
            
            // Add countdown if verified
            if (verified === 'true') {
                var countdown = 5;
                var countdownElement = document.querySelector('.countdown');
                
                var timer = setInterval(function() {
                    countdown--;
                    countdownElement.innerText = 'Redirecting to login in ' + countdown + ' ' + (countdown === 1 ? 'second' : 'seconds') + '...';
                    
                    if (countdown <= 0) {
                        clearInterval(timer);
                        window.location.href = '/auth/login-choice';
                    }
                }, 1000);
            }
        };
    </script>
</body>
</html> 