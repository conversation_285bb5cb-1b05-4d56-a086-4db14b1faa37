-- Manual SQL script to add AI generation columns to blog_posts table
-- Run this in your Supabase SQL editor or database console

-- Add meta keywords column for SEO
ALTER TABLE public.blog_posts ADD COLUMN IF NOT EXISTS meta_keywords text[];

-- Add raw JSON data column for storing generation source data
ALTER TABLE public.blog_posts ADD COLUMN IF NOT EXISTS raw_data_json jsonb;

-- Add generation source tracking
ALTER TABLE public.blog_posts ADD COLUMN IF NOT EXISTS generation_source character varying(50) DEFAULT 'manual';

-- Add AI generation job ID for tracking
ALTER TABLE public.blog_posts ADD COLUMN IF NOT EXISTS ai_generation_job_id character varying(255);

-- Create indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_blog_posts_generation_source ON public.blog_posts(generation_source);
CREATE INDEX IF NOT EXISTS idx_blog_posts_ai_job_id ON public.blog_posts(ai_generation_job_id);

-- Verify the columns were added
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'blog_posts' 
AND column_name IN ('meta_keywords', 'raw_data_json', 'generation_source', 'ai_generation_job_id')
ORDER BY column_name; 