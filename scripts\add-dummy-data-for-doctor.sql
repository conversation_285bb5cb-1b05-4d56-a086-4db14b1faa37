-- SQL script to add dummy data for the test doctor (ID 4097)
-- Replace DOCTOR_ID with the actual ID of your test doctor (4097 based on previous output)

-- Set variables
\set doctor_id 4097

-- Add a few appointments for the doctor
INSERT INTO public.appointments (
    doctor_id,
    patient_id,
    appointment_date,
    appointment_time,
    status,
    notes,
    created_at
) VALUES
    (:doctor_id, 1, CURRENT_DATE + INTERVAL '1 day', '10:00:00', 'scheduled', 'Regular checkup', CURRENT_TIMESTAMP),
    (:doctor_id, 2, CURRENT_DATE + INTERVAL '2 days', '14:30:00', 'scheduled', 'Follow-up appointment', CURRENT_TIMESTAMP),
    (:doctor_id, 3, CURRENT_DATE - INTERVAL '5 days', '09:15:00', 'completed', 'Annual physical examination', CURRENT_TIMESTAMP),
    (:doctor_id, 4, CURRENT_DATE - INTERVAL '10 days', '16:00:00', 'cancelled', 'Patient cancelled due to illness', CURRENT_TIMESTAMP);

-- Add some reviews for the doctor
INSERT INTO public.reviews (
    doctor_id,
    patient_id,
    rating,
    comment,
    created_at
) VALUES
    (:doctor_id, 1, 5, 'Dr. Test Doctor is excellent! Very knowledgeable and caring.', CURRENT_TIMESTAMP - INTERVAL '30 days'),
    (:doctor_id, 2, 4, 'Good experience. Doctor was professional but the wait time was long.', CURRENT_TIMESTAMP - INTERVAL '45 days'),
    (:doctor_id, 3, 5, 'Wonderful experience. Highly recommend Dr. Test Doctor for cardiac issues.', CURRENT_TIMESTAMP - INTERVAL '60 days');

-- Update the doctor's stats based on the reviews
UPDATE public.doctors 
SET 
    rating = (SELECT AVG(rating) FROM public.reviews WHERE doctor_id = :doctor_id),
    review_count = (SELECT COUNT(*) FROM public.reviews WHERE doctor_id = :doctor_id),
    last_updated = CURRENT_TIMESTAMP
WHERE doctor_id = :doctor_id;

-- Add some achievements
INSERT INTO public.doctor_achievements (
    doctor_id,
    achievement_name,
    description,
    date_achieved
) VALUES
    (:doctor_id, 'Board Certification', 'Achieved board certification in Cardiology', CURRENT_DATE - INTERVAL '5 years'),
    (:doctor_id, 'Research Excellence', 'Published groundbreaking research in cardiac treatment', CURRENT_DATE - INTERVAL '2 years'),
    (:doctor_id, 'Patient Choice Award', 'Received Patient Choice Award for outstanding care', CURRENT_DATE - INTERVAL '1 year');

-- Add some available time slots for appointments
INSERT INTO public.doctor_availability (
    doctor_id,
    day_of_week,
    start_time,
    end_time,
    is_available
) VALUES
    (:doctor_id, 'Monday', '09:00:00', '17:00:00', true),
    (:doctor_id, 'Tuesday', '09:00:00', '17:00:00', true),
    (:doctor_id, 'Wednesday', '09:00:00', '17:00:00', true),
    (:doctor_id, 'Thursday', '09:00:00', '17:00:00', true),
    (:doctor_id, 'Friday', '09:00:00', '15:00:00', true);

-- Update the doctor with more detailed information
UPDATE public.doctors
SET
    experience = 10,
    publications = 'Journal of Cardiology (2020): "Advances in Treatment of Heart Failure"; American Medical Journal (2021): "Long-term Outcomes of Stent Placement"',
    awards_recognitions = 'Top Doctor Award (2022), Physician of the Year (2020), Research Excellence Award (2019)',
    languages_spoken = 'English, Spanish, French',
    professional_affiliations = 'American Medical Association, American College of Cardiology, Society of Cardiovascular Angiography and Interventions',
    procedures_performed = 'Cardiac Catheterization, Echocardiography, Stress Testing, Coronary Angioplasty, Pacemaker Implantation',
    treatment_services_expertise = 'Heart Disease Treatment, Preventive Cardiology, Heart Failure Management, Hypertension Management, Cardiac Rehabilitation',
    form = 'Active',
    wins = 25,
    losses = 5,
    draws = 2
WHERE doctor_id = :doctor_id;

-- Add education history
INSERT INTO public.doctor_education (
    doctor_id,
    institution,
    degree,
    field_of_study,
    start_date,
    end_date
) VALUES
    (:doctor_id, 'Harvard Medical School', 'Doctor of Medicine', 'Medicine', '2005-09-01', '2009-05-31'),
    (:doctor_id, 'Johns Hopkins University', 'Residency', 'Internal Medicine', '2009-07-01', '2012-06-30'),
    (:doctor_id, 'Cleveland Clinic', 'Fellowship', 'Cardiology', '2012-07-01', '2015-06-30');

-- Note: If any of these tables don't exist, you'll need to modify the script accordingly.
-- You can comment out any sections that reference tables that don't exist in your database. 