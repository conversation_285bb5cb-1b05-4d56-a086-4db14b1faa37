// Script to add flag_url column to the countries table
const { createClient } = require('@supabase/supabase-js');

// Database credentials with service role key
const supabaseUrl = "https://uapbzzscckhtptliynyj.supabase.co";
const serviceRoleKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q";

// Create a Supabase client with the service role key
const supabase = createClient(supabaseUrl, serviceRoleKey);

// Function to add the flag_url column to the countries table
async function addFlagColumn() {
  try {
    console.log("Attempting to add flag_url column to countries table...");
    
    // We can't directly alter the schema using the JavaScript client
    // Let's try a workaround - create new country records with the flag_url
    
    // First, get existing countries
    const { data: countries, error: fetchError } = await supabase
      .from('countries')
      .select('*');
    
    if (fetchError) {
      console.error("Error fetching countries:", fetchError);
      return;
    }
    
    console.log("Current countries in database:", countries);
    
    // Try to update one country with mock data including flag_url to see if the column exists
    const testUpdate = await supabase
      .from('countries')
      .update({ 
        mock_flag_field: 'https://example.com/flag.png' 
      })
      .eq('country_id', 1);
    
    console.log("Test update response:", testUpdate);
    
    // If column doesn't exist, we can't add it directly with the JS client
    // Instead, we can create new countries with all the data including flag URLs
    
    // Since we can't modify the schema directly with the JS client, 
    // let's show instructions for how to add the column via SQL
    console.log("\n*** IMPORTANT: Column 'flag_url' doesn't exist in 'countries' table ***");
    console.log("To fix this, you need to run the following SQL in your Supabase dashboard:");
    console.log("ALTER TABLE countries ADD COLUMN flag_url TEXT;");
    console.log("\nAlternatively, you can use the FALLBACK_COUNTRIES data from lib/fallback-data.ts which already includes flag URLs.");
    
    console.log("\nWorkaround: Use the countries data directly in the header-client.tsx component with hardcoded flag URLs");
    console.log("Example flag URLs:");
    console.log("Bahrain: https://flagcdn.com/w320/bh.png");
    console.log("Kuwait: https://flagcdn.com/w320/kw.png");
    console.log("Oman: https://flagcdn.com/w320/om.png");
    console.log("Qatar: https://flagcdn.com/w320/qa.png");
    console.log("Saudi Arabia: https://flagcdn.com/w320/sa.png");
    console.log("UAE: https://flagcdn.com/w320/ae.png");
    console.log("USA: https://flagcdn.com/w320/us.png");
  } catch (error) {
    console.error("Unexpected error:", error);
  }
}

// Run the function
addFlagColumn(); 