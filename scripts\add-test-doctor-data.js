// <PERSON><PERSON>t to add dummy data for the test doctor
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Constants
const DOCTOR_ID = 4097; // Replace with your doctor's ID if different

async function main() {
  try {
    // Check for required environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Error: NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be defined in .env.local');
      process.exit(1);
    }

    // Create Supabase client with admin privileges
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );
    
    console.log('Connecting to Supabase at:', process.env.NEXT_PUBLIC_SUPABASE_URL);
    console.log(`Adding dummy data for doctor ID: ${DOCTOR_ID}`);
    
    // Update the doctor with more detailed information
    console.log('Updating doctor details...');
    const { data: doctorUpdateData, error: doctorUpdateError } = await supabase
      .from('doctors')
      .update({
        experience: 10,
        publications: 'Journal of Cardiology (2020): "Advances in Treatment of Heart Failure"; American Medical Journal (2021): "Long-term Outcomes of Stent Placement"',
        awards_recognitions: 'Top Doctor Award (2022), Physician of the Year (2020), Research Excellence Award (2019)',
        languages_spoken: 'English, Spanish, French',
        professional_affiliations: 'American Medical Association, American College of Cardiology, Society of Cardiovascular Angiography and Interventions',
        procedures_performed: 'Cardiac Catheterization, Echocardiography, Stress Testing, Coronary Angioplasty, Pacemaker Implantation',
        treatment_services_expertise: 'Heart Disease Treatment, Preventive Cardiology, Heart Failure Management, Hypertension Management, Cardiac Rehabilitation',
        form: 'Active',
        wins: 25,
        losses: 5,
        draws: 2
      })
      .eq('doctor_id', DOCTOR_ID)
      .select();
      
    if (doctorUpdateError) {
      console.error('Error updating doctor details:', doctorUpdateError);
    } else {
      console.log('Doctor details updated successfully');
    }
    
    // Check if appointments table exists
    try {
      console.log('Adding appointments...');
      const currentDate = new Date();
      
      // Add appointments
      const appointmentsData = [
        {
          doctor_id: DOCTOR_ID,
          patient_id: 1,
          appointment_date: new Date(currentDate.getTime() + 24 * 60 * 60 * 1000), // tomorrow
          appointment_time: '10:00:00',
          status: 'scheduled',
          notes: 'Regular checkup',
          created_at: new Date()
        },
        {
          doctor_id: DOCTOR_ID,
          patient_id: 2,
          appointment_date: new Date(currentDate.getTime() + 2 * 24 * 60 * 60 * 1000), // day after tomorrow
          appointment_time: '14:30:00',
          status: 'scheduled',
          notes: 'Follow-up appointment',
          created_at: new Date()
        },
        {
          doctor_id: DOCTOR_ID,
          patient_id: 3,
          appointment_date: new Date(currentDate.getTime() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
          appointment_time: '09:15:00',
          status: 'completed',
          notes: 'Annual physical examination',
          created_at: new Date()
        }
      ];
      
      const { data: appointmentsResult, error: appointmentsError } = await supabase
        .from('appointments')
        .insert(appointmentsData)
        .select();
        
      if (appointmentsError) {
        console.error('Error adding appointments:', appointmentsError);
      } else {
        console.log(`Added ${appointmentsResult.length} appointments successfully`);
      }
    } catch (error) {
      console.error('Error with appointments, table might not exist:', error);
    }
    
    // Check if reviews table exists
    try {
      console.log('Adding reviews...');
      
      // Add reviews
      const reviewsData = [
        {
          doctor_id: DOCTOR_ID,
          patient_id: 1,
          rating: 5,
          comment: 'Dr. Test Doctor is excellent! Very knowledgeable and caring.',
          created_at: new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
        },
        {
          doctor_id: DOCTOR_ID,
          patient_id: 2,
          rating: 4,
          comment: 'Good experience. Doctor was professional but the wait time was long.',
          created_at: new Date(new Date().getTime() - 45 * 24 * 60 * 60 * 1000) // 45 days ago
        },
        {
          doctor_id: DOCTOR_ID,
          patient_id: 3,
          rating: 5,
          comment: 'Wonderful experience. Highly recommend Dr. Test Doctor for cardiac issues.',
          created_at: new Date(new Date().getTime() - 60 * 24 * 60 * 60 * 1000) // 60 days ago
        }
      ];
      
      const { data: reviewsResult, error: reviewsError } = await supabase
        .from('reviews')
        .insert(reviewsData)
        .select();
        
      if (reviewsError) {
        console.error('Error adding reviews:', reviewsError);
      } else {
        console.log(`Added ${reviewsResult.length} reviews successfully`);
        
        // Update doctor stats based on reviews
        console.log('Updating doctor stats based on reviews...');
        
        // Calculate average rating
        const { data: avgRatingData, error: avgRatingError } = await supabase
          .from('reviews')
          .select('rating')
          .eq('doctor_id', DOCTOR_ID);
          
        if (avgRatingError) {
          console.error('Error calculating average rating:', avgRatingError);
        } else {
          const avgRating = avgRatingData.reduce((sum, review) => sum + review.rating, 0) / avgRatingData.length;
          
          // Update doctor with new rating and review count
          const { error: updateStatsError } = await supabase
            .from('doctors')
            .update({
              rating: avgRating,
              review_count: avgRatingData.length,
              last_updated: new Date()
            })
            .eq('doctor_id', DOCTOR_ID);
            
          if (updateStatsError) {
            console.error('Error updating doctor stats:', updateStatsError);
          } else {
            console.log(`Updated doctor stats: rating=${avgRating}, review_count=${avgRatingData.length}`);
          }
        }
      }
    } catch (error) {
      console.error('Error with reviews, table might not exist:', error);
    }
    
    // Check if doctor_achievements table exists
    try {
      console.log('Adding achievements...');
      
      // Add achievements
      const achievementsData = [
        {
          doctor_id: DOCTOR_ID,
          achievement_name: 'Board Certification',
          description: 'Achieved board certification in Cardiology',
          date_achieved: new Date(new Date().getFullYear() - 5, new Date().getMonth(), new Date().getDate())
        },
        {
          doctor_id: DOCTOR_ID,
          achievement_name: 'Research Excellence',
          description: 'Published groundbreaking research in cardiac treatment',
          date_achieved: new Date(new Date().getFullYear() - 2, new Date().getMonth(), new Date().getDate())
        },
        {
          doctor_id: DOCTOR_ID,
          achievement_name: 'Patient Choice Award',
          description: 'Received Patient Choice Award for outstanding care',
          date_achieved: new Date(new Date().getFullYear() - 1, new Date().getMonth(), new Date().getDate())
        }
      ];
      
      const { data: achievementsResult, error: achievementsError } = await supabase
        .from('doctor_achievements')
        .insert(achievementsData)
        .select();
        
      if (achievementsError) {
        console.error('Error adding achievements:', achievementsError);
      } else {
        console.log(`Added ${achievementsResult.length} achievements successfully`);
      }
    } catch (error) {
      console.error('Error with achievements, table might not exist:', error);
    }
    
    // Check if doctor_education table exists
    try {
      console.log('Adding education history...');
      
      // Add education history
      const educationData = [
        {
          doctor_id: DOCTOR_ID,
          institution: 'Harvard Medical School',
          degree: 'Doctor of Medicine',
          field_of_study: 'Medicine',
          start_date: '2005-09-01',
          end_date: '2009-05-31'
        },
        {
          doctor_id: DOCTOR_ID,
          institution: 'Johns Hopkins University',
          degree: 'Residency',
          field_of_study: 'Internal Medicine',
          start_date: '2009-07-01',
          end_date: '2012-06-30'
        },
        {
          doctor_id: DOCTOR_ID,
          institution: 'Cleveland Clinic',
          degree: 'Fellowship',
          field_of_study: 'Cardiology',
          start_date: '2012-07-01',
          end_date: '2015-06-30'
        }
      ];
      
      const { data: educationResult, error: educationError } = await supabase
        .from('doctor_education')
        .insert(educationData)
        .select();
        
      if (educationError) {
        console.error('Error adding education history:', educationError);
      } else {
        console.log(`Added ${educationResult.length} education records successfully`);
      }
    } catch (error) {
      console.error('Error with education history, table might not exist:', error);
    }
    
    // Check if doctor_availability table exists
    try {
      console.log('Adding availability...');
      
      // Add availability
      const availabilityData = [
        {
          doctor_id: DOCTOR_ID,
          day_of_week: 'Monday',
          start_time: '09:00:00',
          end_time: '17:00:00',
          is_available: true
        },
        {
          doctor_id: DOCTOR_ID,
          day_of_week: 'Tuesday',
          start_time: '09:00:00',
          end_time: '17:00:00',
          is_available: true
        },
        {
          doctor_id: DOCTOR_ID,
          day_of_week: 'Wednesday',
          start_time: '09:00:00',
          end_time: '17:00:00',
          is_available: true
        },
        {
          doctor_id: DOCTOR_ID,
          day_of_week: 'Thursday',
          start_time: '09:00:00',
          end_time: '17:00:00',
          is_available: true
        },
        {
          doctor_id: DOCTOR_ID,
          day_of_week: 'Friday',
          start_time: '09:00:00',
          end_time: '15:00:00',
          is_available: true
        }
      ];
      
      const { data: availabilityResult, error: availabilityError } = await supabase
        .from('doctor_availability')
        .insert(availabilityData)
        .select();
        
      if (availabilityError) {
        console.error('Error adding availability:', availabilityError);
      } else {
        console.log(`Added ${availabilityResult.length} availability records successfully`);
      }
    } catch (error) {
      console.error('Error with availability, table might not exist:', error);
    }
    
    console.log('Finished adding dummy data for doctor');
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

main(); 