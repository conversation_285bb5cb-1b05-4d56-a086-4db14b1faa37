#!/usr/bin/env node

/**
 * Bundle Analysis Script
 * This script builds the application and analyzes the bundle size
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('📦 Bundle Analysis for Doctors Leagues Application');
console.log('=================================================\n');

async function buildAndAnalyze() {
    try {
        console.log('🔨 Building application...');
        const buildStart = Date.now();

        // Build the application
        execSync('npm run build', {
            stdio: 'inherit',
            cwd: process.cwd()
        });

        const buildTime = Date.now() - buildStart;
        console.log(`✅ Build completed in ${buildTime}ms\n`);

        // Check if .next directory exists
        if (fs.existsSync('.next')) {
            console.log('📊 Analyzing bundle sizes...\n');

            // Check for build manifest
            const buildManifestPath = '.next/build-manifest.json';
            if (fs.existsSync(buildManifestPath)) {
                const manifest = JSON.parse(fs.readFileSync(buildManifestPath, 'utf8'));

                console.log('📋 Page Bundles:');
                console.log('================');
                Object.entries(manifest.pages).forEach(([page, files]) => {
                    console.log(`${page}:`);
                    files.forEach(file => {
                        const filePath = path.join('.next', file);
                        if (fs.existsSync(filePath)) {
                            const stats = fs.statSync(filePath);
                            const sizeKB = Math.round(stats.size / 1024);
                            console.log(`  - ${file}: ${sizeKB}KB`);
                        }
                    });
                    console.log('');
                });
            }

            // Check static chunks
            const staticPath = '.next/static/chunks';
            if (fs.existsSync(staticPath)) {
                console.log('📦 Static Chunks:');
                console.log('=================');
                const chunks = fs.readdirSync(staticPath)
                    .filter(file => file.endsWith('.js'))
                    .map(file => {
                        const filePath = path.join(staticPath, file);
                        const stats = fs.statSync(filePath);
                        return {
                            name: file,
                            size: stats.size,
                            sizeKB: Math.round(stats.size / 1024)
                        };
                    })
                    .sort((a, b) => b.size - a.size);

                chunks.slice(0, 10).forEach(chunk => {
                    console.log(`  ${chunk.name}: ${chunk.sizeKB}KB`);
                });

                const totalSize = chunks.reduce((sum, chunk) => sum + chunk.size, 0);
                console.log(`\nTotal static chunks: ${Math.round(totalSize / 1024)}KB`);
            }

        } else {
            console.log('❌ .next directory not found. Build may have failed.');
        }

    } catch (error) {
        console.error('❌ Error during build/analysis:', error.message);
    }
}

async function runBundleAnalyzer() {
    try {
        console.log('\n🔍 Running bundle analyzer...');
        console.log('This will open a browser window with detailed bundle analysis.');
        console.log('Press Ctrl+C to stop the analyzer when done.\n');

        execSync('npm run analyze', {
            stdio: 'inherit',
            cwd: process.cwd()
        });

    } catch (error) {
        console.log('⚠️  Bundle analyzer not available or failed to run');
        console.log('To enable bundle analysis, ensure @next/bundle-analyzer is installed');
    }
}

async function main() {
    await buildAndAnalyze();

    console.log('\n🚀 PERFORMANCE RECOMMENDATIONS');
    console.log('==============================');
    console.log('1. Large chunks (>100KB) should be code-split');
    console.log('2. Unused dependencies should be removed');
    console.log('3. Consider lazy loading heavy components');
    console.log('4. Optimize images and static assets');

    // Ask if user wants to run bundle analyzer
    console.log('\n❓ Run detailed bundle analyzer? (y/n)');

    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.on('data', async (key) => {
        const input = key.toString().toLowerCase();
        if (input === 'y' || input === 'yes') {
            await runBundleAnalyzer();
        }
        process.exit(0);
    });
}

main().catch(console.error);