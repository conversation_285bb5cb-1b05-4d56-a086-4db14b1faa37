# Blog Management System

A comprehensive, reusable system for creating blog posts without coding from scratch each time.

## Quick Start

### 1. Validate a blog post
```bash
node blog-helpers.js validate examples/simple-example.json
```

### 2. Create a blog post from JSON file
```bash
node create-blog-post.js examples/simple-example.json
```

### 3. Quick creation from command line
```bash
node blog-helpers.js quick "Blog Title" "<h1>Title</h1><p>Content...</p>"
```

### 4. List available options
```bash
node blog-helpers.js list
```

### 5. Analyze content
```bash
node blog-helpers.js analyze examples/simple-example.json
```

## Files Overview

- **`blog-config.json`** - Configuration with categories, authors, validation rules
- **`create-blog-post.js`** - Main blog creation script (class-based)
- **`blog-helpers.js`** - Helper utilities and CLI commands
- **`blog-post-template.json`** - Template showing all available fields
- **`examples/simple-example.json`** - Working example for testing
- **`examples/coming-soon-example.json`** - Template for "coming soon" posts

## Required Fields

Every blog post needs:
- `title` (10-200 characters)
- `slug` (URL-friendly, lowercase with hyphens)
- `content` (HTML format, minimum 100 characters)
- `category_slug` (one of: ranking-analysis-insights, medical-deep-dives, doctor-institution-spotlights, patient-caregiver-resources, doctor-insights, patient-stories)
- `author_slug` (editorial-team)

## Usage Modes

### Validation Only (no database connection required)
- `node blog-helpers.js validate <file>`
- `node blog-helpers.js analyze <file>`
- `node blog-helpers.js list`

### Full Creation (requires database)
- `node create-blog-post.js <file>`
- `node blog-helpers.js quick "Title" "Content"`

## Documentation

For complete documentation, see: [`docs/blog-management.md`](../../docs/blog-management.md)

## Example Usage

1. Copy the template:
   ```bash
   cp blog-post-template.json my-post.json
   ```

2. Edit your content in `my-post.json`

3. Validate it:
   ```bash
   node blog-helpers.js validate my-post.json
   ```

4. Create the post:
   ```bash
   node create-blog-post.js my-post.json
   ```

## Creating "Coming Soon" Posts

For categories with 0 articles, you can create "coming soon" posts:

```bash
# Create coming soon post for categories without content
node create-blog-post.js examples/coming-soon-example.json
```

Edit the `category_slug` in the example to match the category that needs a "coming soon" post.

## Support

- Check validation errors for specific issues
- Use `node blog-helpers.js list` to see available categories/authors
- See full documentation for troubleshooting and examples 