const BlogPostManager = require('./create-blog-post.js')
const fs = require('fs')
const path = require('path')

/**
 * Blog Management Helper Functions
 * Utility functions for common blog management tasks
 */
class BlogHelpers {
  constructor(validationOnly = false) {
    this.validationOnly = validationOnly
    if (!validationOnly) {
      this.manager = new BlogPostManager()
    } else {
      // Load config for validation without database connection
      this.config = require('./blog-config.json')
    }
  }

  /**
   * Validate blog data using internal validation logic (for validation-only mode)
   */
  validateBlogDataInternal(blogData) {
    const errors = []
    const validation = this.config.validation

    // Check required fields
    for (const field of this.config.required_fields) {
      if (!blogData[field] || blogData[field].toString().trim() === '') {
        errors.push(`Required field missing: ${field}`)
      }
    }

    // Validate field lengths and patterns
    for (const [field, rules] of Object.entries(validation)) {
      if (blogData[field]) {
        const value = blogData[field].toString()
        
        if (rules.min_length && value.length < rules.min_length) {
          errors.push(`${field} must be at least ${rules.min_length} characters`)
        }
        
        if (rules.max_length && value.length > rules.max_length) {
          errors.push(`${field} must be no more than ${rules.max_length} characters`)
        }
        
        if (rules.pattern && !new RegExp(rules.pattern).test(value)) {
          errors.push(`${field} format is invalid`)
        }
      }
    }

    // Validate category exists
    if (blogData.category_slug) {
      const categoryExists = this.config.categories.some(cat => cat.slug === blogData.category_slug)
      if (!categoryExists) {
        errors.push(`Invalid category_slug: ${blogData.category_slug}. Available: ${this.config.categories.map(c => c.slug).join(', ')}`)
      }
    }

    // Validate author exists
    if (blogData.author_slug) {
      const authorExists = this.config.authors.some(auth => auth.slug === blogData.author_slug)
      if (!authorExists) {
        errors.push(`Invalid author_slug: ${blogData.author_slug}. Available: ${this.config.authors.map(a => a.slug).join(', ')}`)
      }
    }

    return errors
  }

  /**
   * Generate a URL-friendly slug from a title
   */
  generateSlug(title) {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-')     // Replace spaces with hyphens
      .replace(/-+/g, '-')      // Replace multiple hyphens with single
      .replace(/^-|-$/g, '')    // Remove leading/trailing hyphens
  }

  /**
   * Validate if a slug is properly formatted
   */
  isValidSlug(slug) {
    const slugPattern = /^[a-z0-9-]+$/
    return slugPattern.test(slug) && slug.length <= 100
  }

  /**
   * Create a basic blog post object from minimal input
   */
  createBasicBlogData(title, content, categorySlug = 'patient-caregiver-resources', authorSlug = 'editorial-team') {
    const slug = this.generateSlug(title)
    
    return {
      title,
      slug,
      content,
      category_slug: categorySlug,
      author_slug: authorSlug
    }
  }

  /**
   * Enhance basic blog data with SEO and metadata
   */
  enhanceBlogData(basicData, options = {}) {
    const enhanced = { ...basicData }
    
    // Auto-generate excerpt from content if not provided
    if (!enhanced.excerpt && options.auto_excerpt !== false) {
      enhanced.excerpt = this.generateExcerpt(enhanced.content)
    }

    // Auto-generate meta title if not provided
    if (!enhanced.meta_title) {
      enhanced.meta_title = enhanced.title.length > 60 
        ? enhanced.title.substring(0, 57) + '...'
        : enhanced.title
    }

    // Auto-generate meta description from excerpt
    if (!enhanced.meta_description && enhanced.excerpt) {
      enhanced.meta_description = enhanced.excerpt.length > 160
        ? enhanced.excerpt.substring(0, 157) + '...'
        : enhanced.excerpt
    }

    // Add default tags if none provided
    if (!enhanced.tags && options.default_tags) {
      enhanced.tags = options.default_tags
    }

    return enhanced
  }

  /**
   * Generate an excerpt from HTML content
   */
  generateExcerpt(htmlContent, maxLength = 250) {
    // Strip HTML tags
    const textContent = htmlContent.replace(/<[^>]*>/g, ' ')
    // Clean up whitespace
    const cleanText = textContent.replace(/\s+/g, ' ').trim()
    
    if (cleanText.length <= maxLength) {
      return cleanText
    }

    // Find the last complete sentence within the limit
    const truncated = cleanText.substring(0, maxLength)
    const lastSentence = truncated.lastIndexOf('.')
    
    if (lastSentence > maxLength * 0.7) {
      return cleanText.substring(0, lastSentence + 1)
    }
    
    // If no good sentence break, truncate at word boundary
    const lastSpace = truncated.lastIndexOf(' ')
    return cleanText.substring(0, lastSpace) + '...'
  }

  /**
   * Validate blog data and provide detailed feedback
   */
  async validateAndReport(blogData) {
    console.log('🔍 Validating blog post data...\n')

    const errors = this.validationOnly 
      ? this.validateBlogDataInternal(blogData)
      : this.manager.validateBlogData(blogData)
    
    if (errors.length === 0) {
      console.log('✅ Validation passed! Blog data is valid.')
      
      // Additional checks and suggestions
      const suggestions = []
      
      if (!blogData.excerpt) {
        suggestions.push('💡 Consider adding an excerpt for better SEO')
      }
      
      if (!blogData.meta_description) {
        suggestions.push('💡 Consider adding a meta description for search engines')
      }
      
      if (!blogData.tags || blogData.tags.length === 0) {
        suggestions.push('💡 Consider adding tags to improve discoverability')
      }
      
      if (!blogData.featured_image_url) {
        suggestions.push('💡 Consider adding a featured image for social sharing')
      }
      
      if (suggestions.length > 0) {
        console.log('\nOptional improvements:')
        suggestions.forEach(suggestion => console.log(suggestion))
      }
      
      return true
    } else {
      console.log('❌ Validation failed with the following errors:')
      errors.forEach(error => console.log(`   • ${error}`))
      return false
    }
  }

  /**
   * Create a blog post with enhanced validation and feedback
   */
  async createWithFeedback(blogData) {
    if (this.validationOnly) {
      throw new Error('Cannot create blog posts in validation-only mode')
    }

    try {
      console.log('🚀 Starting enhanced blog post creation...\n')
      
      // Validate first
      const isValid = await this.validateAndReport(blogData)
      if (!isValid) {
        throw new Error('Validation failed. Please fix the errors above.')
      }
      
      console.log('\n📝 Creating blog post...')
      const post = await this.manager.createBlogPost(blogData)
      
      // Provide detailed success feedback
      console.log('\n🎉 Blog post created successfully!')
      console.log('─'.repeat(50))
      console.log(`📰 Title: ${post.title}`)
      console.log(`🔗 Slug: ${post.slug}`)
      console.log(`📂 Category: ${blogData.category_slug}`)
      console.log(`✍️  Author: ${blogData.author_slug}`)
      console.log(`📊 Reading time: ${post.reading_time_minutes} minutes`)
      console.log(`🌐 URL: http://localhost:3000/blog/${post.slug}`)
      
      if (post.is_featured) console.log('⭐ Featured post')
      if (post.is_trending) console.log('🔥 Trending post')
      
      return post
      
    } catch (error) {
      console.error('\n❌ Error creating blog post:', error.message)
      throw error
    }
  }

  /**
   * Quick blog post creation from minimal input
   */
  async quickCreate(title, content, options = {}) {
    const basicData = this.createBasicBlogData(
      title, 
      content, 
      options.category_slug,
      options.author_slug
    )
    
    const enhancedData = this.enhanceBlogData(basicData, options)
    
    return await this.createWithFeedback(enhancedData)
  }

  /**
   * List available categories and authors
   */
  listAvailableOptions() {
    console.log('📋 Available Blog Options:')
    console.log('─'.repeat(30))
    
    const config = this.validationOnly ? this.config : this.manager.config
    
    console.log('\n📂 Categories:')
    config.categories.forEach(cat => {
      console.log(`   • ${cat.slug} - ${cat.name}`)
    })
    
    console.log('\n✍️  Authors: <AUTHORS>
    config.authors.forEach(author => {
      console.log(`   • ${author.slug} - ${author.name}`)
    })
    
    console.log('\n📏 Validation Rules:')
    console.log('   • Title: 10-200 characters')
    console.log('   • Slug: max 100 characters, lowercase with hyphens')
    console.log('   • Content: minimum 100 characters')
    console.log('   • Excerpt: max 300 characters')
    console.log('   • Meta title: max 60 characters')
    console.log('   • Meta description: max 160 characters')
  }

  /**
   * Analyze content and provide recommendations
   */
  analyzeContent(content) {
    console.log('🔍 Content Analysis:')
    console.log('─'.repeat(20))
    
    // Word count
    const wordCount = content.replace(/<[^>]*>/g, '').split(/\s+/).length
    console.log(`📝 Word count: ${wordCount}`)
    
    // Estimated reading time
    const readingTime = Math.ceil(wordCount * 0.004)
    console.log(`⏱️  Estimated reading time: ${readingTime} minutes`)
    
    // Heading structure
    const headings = content.match(/<h[1-6][^>]*>.*?<\/h[1-6]>/g) || []
    console.log(`📋 Headings found: ${headings.length}`)
    
    // Image count
    const images = content.match(/<img[^>]*>/g) || []
    console.log(`🖼️  Images found: ${images.length}`)
    
    // Link count
    const links = content.match(/<a[^>]*>/g) || []
    console.log(`🔗 Links found: ${links.length}`)
    
    // Recommendations
    const recommendations = []
    
    if (wordCount < 300) {
      recommendations.push('💡 Consider expanding content (very short)')
    } else if (wordCount > 3000) {
      recommendations.push('💡 Consider breaking into multiple posts (very long)')
    }
    
    if (headings.length === 0) {
      recommendations.push('💡 Add headings to improve structure')
    }
    
    if (images.length === 0 && wordCount > 500) {
      recommendations.push('💡 Consider adding images to break up text')
    }
    
    if (recommendations.length > 0) {
      console.log('\n📋 Recommendations:')
      recommendations.forEach(rec => console.log(`   ${rec}`))
    }
    
    return {
      wordCount,
      readingTime,
      headingCount: headings.length,
      imageCount: images.length,
      linkCount: links.length
    }
  }
}

// Export the class
module.exports = BlogHelpers

// CLI usage if run directly
if (require.main === module) {
  const command = process.argv[2]
  const helpers = new BlogHelpers(command === 'validate' || command === 'analyze' || command === 'list')
  const args = process.argv.slice(2)
  
  if (args.length === 0) {
    console.log(`
🛠️  Blog Management Helpers

Usage:
  node blog-helpers.js list                    # List available options
  node blog-helpers.js validate <file.json>   # Validate blog data file
  node blog-helpers.js quick "Title" "Content" # Quick blog creation
  node blog-helpers.js analyze <file.json>    # Analyze content

Examples:
  node blog-helpers.js quick "New Treatment" "<h1>Treatment</h1><p>Content...</p>"
  node blog-helpers.js validate examples/simple-example.json
    `)
    process.exit(0)
  }
  
  switch (command) {
    case 'list':
      helpers.listAvailableOptions()
      break
      
    case 'validate':
      if (args[1]) {
        try {
          const blogData = JSON.parse(fs.readFileSync(args[1], 'utf8'))
          helpers.validateAndReport(blogData)
        } catch (error) {
          console.error('❌ Error reading file:', error.message)
        }
      } else {
        console.error('❌ Please provide a JSON file to validate')
      }
      break
      
    case 'quick':
      if (args[1] && args[2]) {
        helpers.quickCreate(args[1], args[2])
          .then(post => console.log(`✅ Created post with ID: ${post.id}`))
          .catch(error => console.error('❌ Failed:', error.message))
      } else {
        console.error('❌ Please provide title and content')
      }
      break
      
    case 'analyze':
      if (args[1]) {
        try {
          const blogData = JSON.parse(fs.readFileSync(args[1], 'utf8'))
          helpers.analyzeContent(blogData.content)
        } catch (error) {
          console.error('❌ Error reading file:', error.message)
        }
      } else {
        console.error('❌ Please provide a JSON file to analyze')
      }
      break
      
    default:
      console.error('❌ Unknown command. Use without arguments to see usage.')
  }
} 