const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')
require('dotenv').config({ path: '.env.local' })

// Import configuration
const config = require('./blog-config.json')

/**
 * Blog Management System - Create Blog Post
 * A comprehensive, reusable system for creating blog posts
 */
class BlogPostManager {
  constructor() {
    this.initializeSupabase()
    this.loadConfig()
  }

  initializeSupabase() {
    const supabaseUrl = process.env[config.database.url_env_var]
    const supabaseServiceKey = process.env[config.database.service_key_env_var]

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error(`Missing required environment variables: ${config.database.url_env_var} or ${config.database.service_key_env_var}`)
    }

    this.supabase = createClient(supabaseUrl, supabaseServiceKey)
    console.log('✅ Supabase client initialized')
  }

  loadConfig() {
    this.config = config
    console.log('✅ Configuration loaded')
  }

  /**
   * Validate blog post data against configuration rules
   */
  validateBlogData(blogData) {
    const errors = []
    const validation = this.config.validation

    // Check required fields
    for (const field of this.config.required_fields) {
      if (!blogData[field] || blogData[field].toString().trim() === '') {
        errors.push(`Required field missing: ${field}`)
      }
    }

    // Validate field lengths and patterns
    for (const [field, rules] of Object.entries(validation)) {
      if (blogData[field]) {
        const value = blogData[field].toString()
        
        if (rules.min_length && value.length < rules.min_length) {
          errors.push(`${field} must be at least ${rules.min_length} characters`)
        }
        
        if (rules.max_length && value.length > rules.max_length) {
          errors.push(`${field} must be no more than ${rules.max_length} characters`)
        }
        
        if (rules.pattern && !new RegExp(rules.pattern).test(value)) {
          errors.push(`${field} format is invalid`)
        }
      }
    }

    // Validate category exists
    if (blogData.category_slug) {
      const categoryExists = this.config.categories.some(cat => cat.slug === blogData.category_slug)
      if (!categoryExists) {
        errors.push(`Invalid category_slug: ${blogData.category_slug}. Available: ${this.config.categories.map(c => c.slug).join(', ')}`)
      }
    }

    // Validate author exists
    if (blogData.author_slug) {
      const authorExists = this.config.authors.some(auth => auth.slug === blogData.author_slug)
      if (!authorExists) {
        errors.push(`Invalid author_slug: ${blogData.author_slug}. Available: ${this.config.authors.map(a => a.slug).join(', ')}`)
      }
    }

    return errors
  }

  /**
   * Process and enhance blog content
   */
  processContent(content, images = []) {
    let processedContent = content

    // Insert images at specified positions
    if (images && images.length > 0) {
      images.forEach(image => {
        if (image.position && image.url) {
          const imageHtml = this.generateImageHtml(image)
          processedContent = this.insertImageAtPosition(processedContent, imageHtml, image.position)
        }
      })
    }

    return processedContent
  }

  /**
   * Generate responsive image HTML
   */
  generateImageHtml(image) {
    return `
<div style="text-align: center; margin: 2rem 0;">
  <img src="${image.url}" alt="${image.alt || ''}" style="max-width: 100%; height: auto; border-radius: 8px;" />
  ${image.caption ? `<p style="font-style: italic; color: rgba(255, 255, 255, 0.7); margin-top: 0.5rem;">${image.caption}</p>` : ''}
</div>`
  }

  /**
   * Insert image at specified position in content
   */
  insertImageAtPosition(content, imageHtml, position) {
    if (position.type === 'after_heading') {
      const headingRegex = new RegExp(`<h[1-6][^>]*>.*?${position.heading}.*?</h[1-6]>`, 'i')
      const match = content.match(headingRegex)
      if (match) {
        const insertPoint = content.indexOf(match[0]) + match[0].length
        return content.slice(0, insertPoint) + imageHtml + content.slice(insertPoint)
      }
    }
    return content
  }

  /**
   * Calculate reading time based on content
   */
  calculateReadingTime(content) {
    const wordCount = content.replace(/<[^>]*>/g, '').split(/\s+/).length
    return Math.ceil(wordCount * this.config.default_settings.reading_time_per_word)
  }

  /**
   * Get category ID from slug
   */
  async getCategoryId(categorySlug) {
    const { data, error } = await this.supabase
      .from('blog_categories')
      .select('id')
      .eq('slug', categorySlug)
      .single()

    if (error || !data) {
      throw new Error(`Category not found: ${categorySlug}`)
    }

    return data.id
  }

  /**
   * Get author ID from slug
   */
  async getAuthorId(authorSlug) {
    const { data, error } = await this.supabase
      .from('blog_authors')
      .select('id')
      .eq('slug', authorSlug)
      .single()

    if (error || !data) {
      throw new Error(`Author not found: ${authorSlug}`)
    }

    return data.id
  }

  /**
   * Create or get tags and return their IDs
   */
  async processTagsAndReturnIds(tags) {
    if (!tags || tags.length === 0) return []

    const tagIds = []

    for (const tagName of tags) {
      const slug = tagName.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '')
      
      // Try to find existing tag
      let { data: existingTag, error } = await this.supabase
        .from('blog_tags')
        .select('id')
        .eq('slug', slug)
        .single()

      if (error && error.code !== 'PGRST116') {
        console.warn(`Warning: Error checking tag ${tagName}:`, error)
        continue
      }

      if (existingTag) {
        tagIds.push(existingTag.id)
      } else {
        // Create new tag
        const { data: newTag, error: createError } = await this.supabase
          .from('blog_tags')
          .insert({ name: tagName, slug })
          .select()
          .single()

        if (createError) {
          console.warn(`Warning: Could not create tag ${tagName}:`, createError)
          continue
        }

        tagIds.push(newTag.id)
        console.log(`✅ Created new tag: ${tagName}`)
      }
    }

    return tagIds
  }

  /**
   * Link tags to blog post
   */
  async linkTagsToPost(postId, tagIds) {
    if (!tagIds || tagIds.length === 0) return

    const tagLinks = tagIds.map(tagId => ({
      post_id: postId,
      tag_id: tagId
    }))

    const { error } = await this.supabase
      .from('blog_post_tags')
      .insert(tagLinks)

    if (error) {
      console.warn('Warning: Could not link some tags to post:', error)
    } else {
      console.log(`✅ Linked ${tagIds.length} tags to post`)
    }
  }

  /**
   * Main function to create a blog post
   */
  async createBlogPost(blogData) {
    try {
      console.log('🚀 Starting blog post creation...')

      // Validate input data
      const validationErrors = this.validateBlogData(blogData)
      if (validationErrors.length > 0) {
        throw new Error(`Validation failed:\n${validationErrors.join('\n')}`)
      }

      // Get category and author IDs
      const categoryId = await this.getCategoryId(blogData.category_slug)
      const authorId = await this.getAuthorId(blogData.author_slug)

      // Process content with images
      const processedContent = this.processContent(blogData.content, blogData.images)

      // Calculate reading time if not provided
      const readingTime = blogData.reading_time_minutes || this.calculateReadingTime(processedContent)

      // Prepare blog post data
      const postData = {
        title: blogData.title,
        slug: blogData.slug,
        excerpt: blogData.excerpt || '',
        content: processedContent,
        category_id: categoryId,
        author_id: authorId,
        medical_reviewer_id: authorId, // Default to same as author
        status: blogData.status || this.config.default_settings.status,
        published_at: new Date().toISOString(),
        meta_title: blogData.meta_title || blogData.title,
        meta_description: blogData.meta_description || blogData.excerpt || '',
        reading_time_minutes: readingTime,
        is_featured: blogData.is_featured || this.config.default_settings.is_featured,
        is_trending: blogData.is_trending || this.config.default_settings.is_trending,
        featured_image_url: blogData.featured_image_url || null,
        featured_image_alt: blogData.featured_image_alt || '',
        meta_keywords: blogData.meta_keywords || [],
        generation_source: this.config.default_settings.generation_source
      }

      // Create the blog post
      const { data: post, error: postError } = await this.supabase
        .from('blog_posts')
        .insert(postData)
        .select()
        .single()

      if (postError) {
        throw new Error(`Failed to create blog post: ${postError.message}`)
      }

      console.log(`✅ Blog post created successfully: ${post.id}`)

      // Process and link tags
      if (blogData.tags && blogData.tags.length > 0) {
        const tagIds = await this.processTagsAndReturnIds(blogData.tags)
        await this.linkTagsToPost(post.id, tagIds)
      }

      // Log success
      console.log('🎉 Blog post creation completed successfully!')
      console.log(`📝 Title: ${post.title}`)
      console.log(`🔗 Slug: ${post.slug}`)
      console.log(`📊 Reading time: ${post.reading_time_minutes} minutes`)
      console.log(`🌐 URL: http://localhost:3000/blog/${post.slug}`)

      return post

    } catch (error) {
      console.error('❌ Error creating blog post:', error.message)
      throw error
    }
  }

  /**
   * Utility method to create post from JSON file
   */
  async createFromFile(filePath) {
    try {
      const fullPath = path.resolve(filePath)
      const fileContent = fs.readFileSync(fullPath, 'utf8')
      const blogData = JSON.parse(fileContent)
      
      return await this.createBlogPost(blogData)
    } catch (error) {
      console.error('❌ Error reading blog data file:', error.message)
      throw error
    }
  }
}

// Export for use as module
module.exports = BlogPostManager

// CLI usage if run directly
if (require.main === module) {
  const args = process.argv.slice(2)
  
  if (args.length === 0) {
    console.log(`
📝 Blog Post Manager Usage:

1. From JSON file:
   node create-blog-post.js path/to/blog-data.json

2. Programmatic usage:
   const BlogPostManager = require('./create-blog-post.js')
   const manager = new BlogPostManager()
   await manager.createBlogPost(blogData)

For detailed usage instructions, see: docs/blog-management.md
    `)
    process.exit(0)
  }

  const manager = new BlogPostManager()
  manager.createFromFile(args[0])
    .then(post => {
      console.log('✅ Success! Blog post created with ID:', post.id)
      process.exit(0)
    })
    .catch(error => {
      console.error('❌ Failed:', error.message)
      process.exit(1)
    })
} 