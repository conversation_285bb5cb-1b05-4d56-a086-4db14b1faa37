// Script to check countries in the database using service role key
const { createClient } = require('@supabase/supabase-js');

// Database credentials with service role key
const supabaseUrl = "https://uapbzzscckhtptliynyj.supabase.co";
const serviceRoleKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q";

// Create a Supabase client with the service role key
const supabase = createClient(supabaseUrl, serviceRoleKey);

// Log countries from the database
async function checkCountries() {
  console.log("Checking countries in the database using service role key...");
  
  try {
    const { data: countries, error } = await supabase
      .from('countries')
      .select('*')
      .order('country_name');
    
    if (error) {
      console.error("Error fetching countries:", error);
      return;
    }
    
    if (!countries || countries.length === 0) {
      console.log("No countries found in the database.");
      
      // Try to insert some test countries
      console.log("Attempting to insert test countries...");
      
      const testCountries = [
        { country_name: "Bahrain", flag_url: "https://flagcdn.com/w320/bh.png" },
        { country_name: "Kuwait", flag_url: "https://flagcdn.com/w320/kw.png" },
        { country_name: "Oman", flag_url: "https://flagcdn.com/w320/om.png" },
        { country_name: "Qatar", flag_url: "https://flagcdn.com/w320/qa.png" },
        { country_name: "Saudi Arabia", flag_url: "https://flagcdn.com/w320/sa.png" },
        { country_name: "UAE", flag_url: "https://flagcdn.com/w320/ae.png" },
        { country_name: "Egypt", flag_url: "https://flagcdn.com/w320/eg.png" }
      ];
      
      const { data: insertResult, error: insertError } = await supabase
        .from('countries')
        .insert(testCountries)
        .select();
      
      if (insertError) {
        console.error("Error inserting test countries:", insertError);
      } else {
        console.log("Successfully inserted test countries:", insertResult);
      }
      
      // Fetch again to verify
      const { data: updatedCountries, error: refetchError } = await supabase
        .from('countries')
        .select('*')
        .order('country_name');
        
      if (refetchError) {
        console.error("Error refetching countries:", refetchError);
      } else {
        console.log("Countries after insert:", updatedCountries);
      }
    } else {
      console.log(`Found ${countries.length} countries in the database:`);
      countries.forEach((country, index) => {
        console.log(`${index + 1}. ID: ${country.country_id}, Name: ${country.country_name}`);
      });
    }
  } catch (error) {
    console.error("Unexpected error:", error);
  }
}

// Run the check
checkCountries(); 