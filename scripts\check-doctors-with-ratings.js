// Script to check doctors with non-null ratings in the database
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Validate environment variables
if (!supabaseUrl || !serviceRoleKey) {
  console.error("Missing environment variables");
  process.exit(1);
}

// Create a Supabase client
const supabase = createClient(supabaseUrl, serviceRoleKey);

async function checkDoctorsWithRatings() {
  console.log("\n===== CHECKING DOCTORS WITH RATINGS =====");
  
  try {
    // Query doctors with non-null community_ratings
    const { data: doctorsWithRatings, error: ratingsError } = await supabase
      .from('doctors')
      .select('doctor_id, fullname, specialty, specialty_id, community_rating, country_id')
      .not('community_rating', 'is', null)
      .order('community_rating', { ascending: false })
      .limit(20);
    
    if (ratingsError) {
      console.error("ERROR: Could not fetch doctors with community_ratings:", ratingsError);
      return;
    }

    if (!doctorsWithRatings || doctorsWithRatings.length === 0) {
      console.log("WARNING: No doctors found with non-null community_ratings");
      
      // Check if there are ANY doctors at all
      const { count: totalCount, error: countError } = await supabase
        .from('doctors')
        .select('*', { count: 'exact', head: true });
      
      if (countError) {
        console.error("ERROR: Could not count total doctors:", countError);
      } else {
        console.log(`There are a total of ${totalCount} doctors in the database, all with null community_ratings`);
      }
      
      return;
    }
    
    console.log(`SUCCESS: Found ${doctorsWithRatings.length} doctors with non-null community_ratings:`);
    doctorsWithRatings.forEach(doctor => {
      console.log(`  - ID: ${doctor.doctor_id}, Name: ${doctor.fullname}, Specialty: ${doctor.specialty || 'Unknown'} (ID: ${doctor.specialty_id || 'Unknown'}), Country ID: ${doctor.country_id}, Community Rating: ${doctor.community_rating}`);
    });

    // Group by specialty to see distribution
    console.log("\n===== DOCTORS WITH COMMUNITY_RATINGS BY SPECIALTY =====");
    const specialtyGroups = {};
    
    doctorsWithRatings.forEach(doctor => {
      const specialtyId = doctor.specialty_id || 'unknown';
      if (!specialtyGroups[specialtyId]) {
        specialtyGroups[specialtyId] = [];
      }
      specialtyGroups[specialtyId].push(doctor);
    });
    
    for (const [specialtyId, doctors] of Object.entries(specialtyGroups)) {
      const specialtyName = doctors[0].specialty || 'Unknown Specialty';
      console.log(`Specialty ID ${specialtyId} (${specialtyName}): ${doctors.length} doctors with ratings`);
      doctors.forEach((doctor, index) => {
        if (index < 3) { // Show only top 3 to avoid excessive output
          console.log(`  - ${doctor.fullname} - Rating: ${doctor.rating}`);
        }
      });
      if (doctors.length > 3) {
        console.log(`  - ... and ${doctors.length - 3} more`);
      }
    }
    
    // Group by country to see distribution
    console.log("\n===== DOCTORS WITH COMMUNITY_RATINGS BY COUNTRY =====");
    const countryGroups = {};
    
    doctorsWithRatings.forEach(doctor => {
      const countryId = doctor.country_id || 'unknown';
      if (!countryGroups[countryId]) {
        countryGroups[countryId] = [];
      }
      countryGroups[countryId].push(doctor);
    });
    
    for (const [countryId, doctors] of Object.entries(countryGroups)) {
      console.log(`Country ID ${countryId}: ${doctors.length} doctors with community_ratings`);
      doctors.forEach((doctor, index) => {
        if (index < 3) { // Show only top 3 to avoid excessive output
          console.log(`  - ${doctor.fullname} - Community Rating: ${doctor.community_rating}`);
        }
      });
      if (doctors.length > 3) {
        console.log(`  - ... and ${doctors.length - 3} more`);
      }
    }
  } catch (error) {
    console.error("CRITICAL ERROR checking doctors with ratings:", error);
  }
}

// Run the check
checkDoctorsWithRatings(); 