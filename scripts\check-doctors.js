// <PERSON>ript to check doctors in the database
const { createClient } = require('@supabase/supabase-js');

// Require dotenv to load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Validate environment variables
if (!supabaseUrl) {
  console.error("ERROR: Missing NEXT_PUBLIC_SUPABASE_URL environment variable");
  process.exit(1);
}

if (!serviceRoleKey) {
  console.error("ERROR: Missing SUPABASE_SERVICE_ROLE_KEY environment variable");
  process.exit(1);
}

console.log("Environment variables loaded successfully");
console.log(`Supabase URL: ${supabaseUrl}`);
console.log(`Service Role Key: ${serviceRoleKey.substring(0, 10)}...`);

// Create a Supabase client with the service role key
const supabase = createClient(supabaseUrl, serviceRoleKey);

async function checkDoctors() {
  console.log("\n===== CHECKING DOCTORS TABLE =====");
  
  try {
    // Query the doctors table with a limit
    const { data, error } = await supabase
      .from('doctors')
      .select('doctor_id, fullname, specialty, specialty_id, rating, country_id')
      .limit(10);
    
    if (error) {
      console.error("ERROR: Could not fetch doctors:", error);
      return;
    }
    
    if (!data || data.length === 0) {
      console.log("WARNING: No doctors found in the database");
      return;
    }
    
    console.log(`SUCCESS: Found ${data.length} doctors in database:`);
    data.forEach(doctor => {
      console.log(`  - ID: ${doctor.doctor_id}, Name: ${doctor.fullname}, Specialty: ${doctor.specialty}, Country ID: ${doctor.country_id}, Rating: ${doctor.rating || 'N/A'}`);
    });

    // Check counts by specialty
    console.log("\n===== CHECKING DOCTORS BY SPECIALTY =====");
    const { data: specialties, error: specialtiesError } = await supabase
      .from('specialties')
      .select('specialty_id, specialty_name');
    
    if (specialtiesError) {
      console.error("ERROR: Could not fetch specialties:", specialtiesError);
      return;
    }

    for (const specialty of specialties) {
      const { count, error: countError } = await supabase
        .from('doctors')
        .select('*', { count: 'exact', head: true })
        .eq('specialty_id', specialty.specialty_id);
      
      if (countError) {
        console.error(`ERROR: Could not count doctors for specialty ${specialty.specialty_name}:`, countError);
      } else {
        console.log(`  - Specialty: ${specialty.specialty_name} (ID: ${specialty.specialty_id}) - ${count} doctors`);
      }
    }
  } catch (error) {
    console.error("CRITICAL ERROR checking doctors:", error);
  }
}

// Run the check
checkDoctors(); 