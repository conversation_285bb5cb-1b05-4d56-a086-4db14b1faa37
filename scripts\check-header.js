// Script to simulate the header component's behavior
const { createClient } = require('@supabase/supabase-js');

// Database credentials with service role key
const supabaseUrl = "https://uapbzzscckhtptliynyj.supabase.co";
const serviceRoleKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q";

// Create a Supabase client with the service role key
const supabase = createClient(supabaseUrl, serviceRoleKey);

// Function to simulate the header component
async function simulateHeader() {
  console.log("Simulating Header component fetching countries...");
  
  try {
    // This is similar to what the Header component is doing
    const { data: countries, error } = await supabase
      .from("countries")
      .select("*")
      .order("country_name");
    
    if (error) {
      console.error("Error fetching countries:", error);
      console.log("Would fall back to static data in the actual component");
      return;
    }
    
    if (!countries || countries.length === 0) {
      console.log("No countries found in the database. Would use fallback data.");
      return;
    }
    
    console.log(`Found ${countries.length} countries that would be displayed in the dropdown:`);
    countries.forEach((country, index) => {
      console.log(`${index + 1}. ID: ${country.country_id}, Name: ${country.country_name}, Flag URL: ${country.flag_url || 'No flag'}`);
    });
    
    // Simulate a user clicking on a country in the dropdown
    const firstCountry = countries[0];
    console.log(`\nSimulating a user clicking on: ${firstCountry.country_name}`);
    console.log(`This would navigate to: /divisions/${firstCountry.country_id}`);
  } catch (error) {
    console.error("Unexpected error:", error);
  }
}

// Run the simulation
simulateHeader(); 