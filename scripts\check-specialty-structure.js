// Script to check specialty table structure
const { createClient } = require('@supabase/supabase-js');

// Database credentials with service role key
const supabaseUrl = "https://uapbzzscckhtptliynyj.supabase.co";
const serviceRoleKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q";

// Create a Supabase client with the service role key
const supabase = createClient(supabaseUrl, serviceRoleKey);

async function checkSpecialtyStructure() {
  console.log("Checking specialty table structure...");
  
  try {
    // Get specialty data
    const { data: specialties, error } = await supabase
      .from('specialties')
      .select('*')
      .limit(5);
    
    if (error) {
      console.error("Error fetching specialties:", error);
      return;
    }
    
    if (!specialties || specialties.length === 0) {
      console.log("No specialties found in the database");
      return;
    }
    
    // Check the structure of the first specialty
    const specialty = specialties[0];
    console.log("Specialty columns:", Object.keys(specialty));
    console.log("First specialty data:", specialty);
    
    // Check other specialties to understand relationships
    console.log("\nSample specialties:");
    specialties.forEach((s, index) => {
      console.log(`${index + 1}. ID: ${s.specialty_id}, Name: ${s.specialty_name}`);
    });
    
    // Since specialties don't have country_id, check doctors table to see if it links countries and specialties
    console.log("\nChecking doctors table for relationships...");
    const { data: doctors, error: doctorsError } = await supabase
      .from('doctors')
      .select('*')
      .limit(5);
    
    if (doctorsError) {
      console.error("Error fetching doctors:", doctorsError);
    } else if (!doctors || doctors.length === 0) {
      console.log("No doctors found in the database");
    } else {
      // Check the structure of the first doctor
      const doctor = doctors[0];
      console.log("Doctor columns:", Object.keys(doctor));
      console.log("First doctor data:", doctor);
      
      // Check if doctors have both country_id and specialty_id
      const hasCountryId = doctor.hasOwnProperty('country_id');
      const hasSpecialtyId = doctor.hasOwnProperty('specialty_id');
      
      if (hasCountryId && hasSpecialtyId) {
        console.log("\nIMPORTANT: Doctors table links countries and specialties");
        console.log("To get specialties for a country, you need to query doctors table like this:");
        console.log("SELECT DISTINCT specialty_id FROM doctors WHERE country_id = [country_id]");
      } else {
        console.log("\nDoctors table does not link countries and specialties");
      }
    }
  } catch (error) {
    console.error("Unexpected error:", error);
  }
}

// Run the check
checkSpecialtyStructure(); 