// <PERSON>ript to check the query for the standings page
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Validate environment variables
if (!supabaseUrl || !serviceRoleKey) {
  console.error("Missing environment variables");
  process.exit(1);
}

// Create a Supabase client
const supabase = createClient(supabaseUrl, serviceRoleKey);

// Function to format community_ratings for display
function formatRating(community_rating) {
  if (community_rating === null || community_rating === undefined) return 'N/A';
  return community_rating.toFixed(1);
}

async function testStandingsQuery() {
  try {
    // Test specific specialties that should have doctors
    const specialtyIds = [4, 8, 10, 17, 30]; // Cardiology, Emergency Medicine, Family Medicine, Internal Medicine, Psychiatry
    
    // Set country filter (can be changed for testing)
    const countryId = 1; // Using Bahrain (ID: 1) for this test
    
    console.log(`Testing standings query for country ID: ${countryId}`);
    
    // Get specialty names first
    const { data: specialties, error: specialtiesError } = await supabase
      .from('specialties')
      .select('specialty_id, specialty_name')
      .in('specialty_id', specialtyIds);
    
    if (specialtiesError) {
      console.error("Error fetching specialties:", specialtiesError);
      return;
    }
    
    const specialtyMap = specialties.reduce((map, specialty) => {
      map[specialty.specialty_id] = specialty.specialty_name;
      return map;
    }, {});
    
    // Loop through each specialty ID to test the query
    for (const specialtyId of specialtyIds) {
      const specialtyName = specialtyMap[specialtyId];
      console.log(`\n===== Testing Specialty: ${specialtyName} (ID: ${specialtyId}) =====`);
      
      console.log("QUERY 1: Standard query with both specialty_id and country_id filters");
      // This is the query that should be used in the standings page
      const { data: doctors, error: doctorsError } = await supabase
        .from('doctors')
        .select('*')
        .eq('specialty_id', specialtyId)
        .eq('country_id', countryId)
        .not('community_rating', 'is', null)
        .order('community_rating', { ascending: false })
        .limit(3);
      
      if (doctorsError) {
        console.error(`Error fetching doctors for specialty ${specialtyName}:`, doctorsError);
        continue;
      }
      
      // Log the doctors found
      if (doctors && doctors.length > 0) {
        console.log(`Found ${doctors.length} top doctors for ${specialtyName} in country ${countryId}:`);
        doctors.forEach((doctor, index) => {
          console.log(`  ${index + 1}. ${doctor.fullname} - Rating: ${formatRating(doctor.rating)}`);
        });
      } else {
        console.log(`No doctors found for specialty: ${specialtyName} in country ${countryId}`);
      }
      
      // Try with just specialty filter
      console.log("\nQUERY 2: Query with only specialty_id filter (no country filter)");
      const { data: specialtyDoctors, error: specialtyDoctorsError } = await supabase
        .from('doctors')
        .select('*')
        .eq('specialty_id', specialtyId)
        .not('rating', 'is', null)
        .order('rating', { ascending: false })
        .limit(3);
      
      if (specialtyDoctorsError) {
        console.error(`Error fetching doctors for specialty ${specialtyName}:`, specialtyDoctorsError);
      } else if (specialtyDoctors && specialtyDoctors.length > 0) {
        console.log(`Found ${specialtyDoctors.length} top doctors for ${specialtyName} (any country):`);
        specialtyDoctors.forEach((doctor, index) => {
          console.log(`  ${index + 1}. ${doctor.fullname} - Rating: ${formatRating(doctor.rating)} - Country ID: ${doctor.country_id}`);
        });
      } else {
        console.log(`No doctors found for specialty: ${specialtyName} in any country with ratings`);
      }
      
      // Try with all doctors in this specialty (with or without ratings)
      console.log("\nQUERY 3: All doctors in this specialty (with or without ratings)");
      const { data: allDoctors, error: allDoctorsError } = await supabase
        .from('doctors')
        .select('*')
        .eq('specialty_id', specialtyId)
        .limit(3);
      
      if (allDoctorsError) {
        console.error(`Error fetching all doctors for specialty ${specialtyName}:`, allDoctorsError);
      } else if (allDoctors && allDoctors.length > 0) {
        console.log(`Found ${allDoctors.length} doctors for ${specialtyName} (any rating):`);
        allDoctors.forEach((doctor, index) => {
          console.log(`  ${index + 1}. ${doctor.fullname} - Rating: ${formatRating(doctor.rating)} - Country ID: ${doctor.country_id}`);
        });
      } else {
        console.log(`No doctors found for specialty: ${specialtyName} at all`);
      }
      
      // Get total count of doctors in this specialty and country
      const { count, error: countError } = await supabase
        .from('doctors')
        .select('*', { count: 'exact', head: true })
        .eq('specialty_id', specialtyId)
        .eq('country_id', countryId);
      
      if (!countError) {
        console.log(`\nTotal doctors in ${specialtyName} for country ${countryId}: ${count}`);
      }
    }
  } catch (error) {
    console.error("Unexpected error testing standings query:", error);
  }
}

// Run the test
testStandingsQuery(); 