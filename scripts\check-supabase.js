// Script to check Supabase connection and data
const { createClient } = require('@supabase/supabase-js');

// Get environment variables
const supabaseUrl = "https://uapbzzscckhtptliynyj.supabase.co";
const supabaseAnonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwOTgzOTMsImV4cCI6MjA1NjY3NDM5M30.J7vt4r-V_ulpJCINWIm6xdDpNmQjqeO2klh4HPut3Dk";

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function main() {
  console.log('Checking Supabase connection...');
  
  try {
    // Check countries table
    console.log('\nChecking countries table:');
    const { data: countries, error: countriesError } = await supabase.from('countries').select('*');
    
    if (countriesError) {
      console.error('Error fetching countries:', countriesError);
    } else {
      console.log(`Found ${countries.length} countries:`);
      countries.forEach(country => {
        console.log(`- ID: ${country.country_id}, Name: ${country.country_name}`);
      });
    }

    // Try to insert a test country if the table is empty
    if (!countriesError && (!countries || countries.length === 0)) {
      console.log('\nAttempting to insert test countries...');
      
      const testCountries = [
        { country_name: "Bahrain" },
        { country_name: "Kuwait" },
        { country_name: "Oman" },
        { country_name: "Qatar" },
        { country_name: "Saudi Arabia" },
        { country_name: "UAE" },
        { country_name: "Egypt" }
      ];
      
      const { data: insertResult, error: insertError } = await supabase
        .from('countries')
        .insert(testCountries)
        .select();
      
      if (insertError) {
        console.error('Error inserting test countries:', insertError);
      } else {
        console.log('Successfully inserted test countries:', insertResult);
      }
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

main().catch(console.error); 