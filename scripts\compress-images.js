#!/usr/bin/env node

/**
 * Image Compression Script
 * This script identifies and provides recommendations for compressing large images
 */

const fs = require('fs');
const path = require('path');

console.log('🖼️  Image Compression Analysis for Doctors Leagues');
console.log('==================================================\n');

function analyzeImages() {
    const publicDir = 'public';

    if (!fs.existsSync(publicDir)) {
        console.log('❌ Public directory not found');
        return;
    }

    console.log('📊 Analyzing images in public directory...\n');

    const largeImages = [];
    const totalImages = [];

    function scanDirectory(dir, relativePath = '') {
        const items = fs.readdirSync(dir);

        items.forEach(item => {
            const fullPath = path.join(dir, item);
            const relativeItemPath = path.join(relativePath, item);
            const stats = fs.statSync(fullPath);

            if (stats.isDirectory()) {
                scanDirectory(fullPath, relativeItemPath);
            } else if (/\.(jpg|jpeg|png|gif|webp|svg)$/i.test(item)) {
                const sizeKB = Math.round(stats.size / 1024);
                totalImages.push({ path: relativeItemPath, size: sizeKB });

                if (stats.size > 500000) { // 500KB
                    largeImages.push({
                        path: relativeItemPath,
                        size: sizeKB,
                        sizeMB: (stats.size / (1024 * 1024)).toFixed(2)
                    });
                }
            }
        });
    }

    scanDirectory(publicDir);

    console.log(`📋 Total images found: ${totalImages.length}`);
    console.log(`⚠️  Large images (>500KB): ${largeImages.length}\n`);

    if (largeImages.length > 0) {
        console.log('🔍 Large Images Analysis:');
        console.log('========================');

        largeImages
            .sort((a, b) => b.size - a.size)
            .forEach((img, index) => {
                console.log(`${index + 1}. ${img.path}`);
                console.log(`   Size: ${img.sizeKB}KB (${img.sizeMB}MB)`);
                console.log(`   Recommendation: ${getCompressionRecommendation(img)}`);
                console.log('');
            });

        const totalLargeSize = largeImages.reduce((sum, img) => sum + img.size, 0);
        console.log(`📊 Total size of large images: ${Math.round(totalLargeSize / 1024)}MB`);
        console.log(`💡 Potential savings: ${Math.round(totalLargeSize * 0.6 / 1024)}MB (60% compression)`);
    }

    console.log('\n🛠️  COMPRESSION RECOMMENDATIONS');
    console.log('===============================');
    console.log('1. Use Next.js Image component with quality={60-75}');
    console.log('2. Convert large PNGs to WebP format');
    console.log('3. Resize images to actual display dimensions');
    console.log('4. Use progressive JPEG for large photos');
    console.log('5. Consider lazy loading for below-fold images');

    console.log('\n📝 NEXT STEPS');
    console.log('=============');
    console.log('1. Replace large images with optimized versions');
    console.log('2. Update components to use OptimizedImage component');
    console.log('3. Add proper alt text for accessibility');
    console.log('4. Test loading performance after optimization');
}

function getCompressionRecommendation(img) {
    const ext = path.extname(img.path).toLowerCase();
    const sizeKB = img.size;

    if (ext === '.png' && sizeKB > 1000) {
        return 'Convert to WebP, reduce quality to 75%';
    } else if (ext === '.jpg' || ext === '.jpeg') {
        return 'Reduce quality to 60-70%, consider WebP';
    } else if (ext === '.webp' && sizeKB > 1000) {
        return 'Reduce quality to 60%, resize if oversized';
    } else {
        return 'Reduce quality and/or resize dimensions';
    }
}

// Run the analysis
analyzeImages();