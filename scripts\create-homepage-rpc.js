#!/usr/bin/env node

/**
 * <PERSON>rip<PERSON> to create the get_doctors_for_homepage RPC function
 * This function returns rated doctors first, then fills with random unrated doctors
 */

const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const createRPCFunction = async () => {
  console.log('Creating get_doctors_for_homepage RPC function...');
  
  const functionSQL = `
    CREATE OR REPLACE FUNCTION get_doctors_for_homepage(limit_count INTEGER DEFAULT 16)
    RETURNS TABLE(
        doctor_id INTEGER,
        fullname TEXT,
        hospital TEXT,
        medical_title TEXT,
        specialty TEXT,
        subspecialty TEXT,
        educational_background TEXT,
        board_certifications TEXT,
        experience INTEGER,
        publications TEXT,
        awards_recognitions TEXT,
        phone_number TEXT,
        email TEXT,
        languages_spoken TEXT,
        professional_affiliations TEXT,
        procedures_performed TEXT,
        treatment_services_expertise TEXT,
        hospital_id INTEGER,
        image_path TEXT,
        wins INTEGER,
        losses INTEGER,
        form TEXT,
        community_rating NUMERIC,
        review_count INTEGER,
        country_id INTEGER,
        specialty_id INTEGER,
        last_updated DATE,
        auth_id UUID,
        draws INTEGER,
        profile_image TEXT,
        personal_biography TEXT,
        work_history TEXT,
        timings TEXT
    ) AS $$
    BEGIN
        RETURN QUERY
        WITH rated_doctors AS (
            -- Get doctors with community_ratings, ordered by community_rating descending
            SELECT d.*
            FROM doctors d
            WHERE d.community_rating IS NOT NULL AND d.community_rating > 0
            ORDER BY d.community_rating DESC, d.review_count DESC
            LIMIT limit_count
        ),
        random_doctors AS (
            -- Get random doctors without community_ratings to fill remaining slots
            SELECT d.*
            FROM doctors d
            WHERE (d.community_rating IS NULL OR d.community_rating = 0)
            ORDER BY RANDOM()
            LIMIT GREATEST(0, limit_count - (SELECT COUNT(*) FROM rated_doctors))
        )
        -- Combine rated doctors first, then random doctors
        SELECT * FROM rated_doctors
        UNION ALL
        SELECT * FROM random_doctors
        ORDER BY
            CASE WHEN community_rating IS NOT NULL AND community_rating > 0 THEN 0 ELSE 1 END,
            community_rating DESC NULLS LAST,
            review_count DESC NULLS LAST;
    END;
    $$ LANGUAGE plpgsql STABLE;
  `;

  try {
    const { error } = await supabase.rpc('exec_sql', { sql: functionSQL });
    
    if (error) {
      console.error('Error creating RPC function:', error);
      return;
    }
    
    console.log('✅ Successfully created get_doctors_for_homepage RPC function');
    
    // Grant permissions
    const permissionsSQL = `
      GRANT EXECUTE ON FUNCTION get_doctors_for_homepage(INTEGER) TO authenticated;
      GRANT EXECUTE ON FUNCTION get_doctors_for_homepage(INTEGER) TO anon;
    `;
    
    const { error: permError } = await supabase.rpc('exec_sql', { sql: permissionsSQL });
    
    if (permError) {
      console.error('Error granting permissions:', permError);
      return;
    }
    
    console.log('✅ Successfully granted permissions to RPC function');
    
    // Test the function
    console.log('Testing the function...');
    const { data: testData, error: testError } = await supabase.rpc('get_doctors_for_homepage', { limit_count: 10 });
    
    if (testError) {
      console.error('Error testing function:', testError);
      return;
    }
    
    console.log(`✅ Function test successful! Returned ${testData?.length || 0} doctors`);
    
  } catch (error) {
    console.error('Exception during RPC creation:', error);
  }
};

// Run the script
createRPCFunction()
  .then(() => {
    console.log('Script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
