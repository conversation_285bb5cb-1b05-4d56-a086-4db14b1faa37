// Debug script to test the homepage RPC function and check column names
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Validate environment variables
if (!supabaseUrl || !serviceRoleKey) {
  console.error("Missing environment variables");
  process.exit(1);
}

// Create a Supabase client
const supabase = createClient(supabaseUrl, serviceRoleKey);

async function debugHomepageRPC() {
  console.log("=== DEBUGGING HOMEPAGE RPC FUNCTION ===");
  
  try {
    // Test 1: Check if the RPC function exists and what it returns
    console.log("\n1. Testing get_doctors_for_homepage RPC function...");
    const { data: rpcData, error: rpcError } = await supabase.rpc('get_doctors_for_homepage', {
      limit_count: 5
    });
    
    if (rpcError) {
      console.error("RPC Error:", rpcError);
    } else {
      console.log(`RPC returned ${rpcData?.length || 0} doctors`);
      if (rpcData && rpcData.length > 0) {
        console.log("Sample doctor from RPC:");
        console.log("- Doctor ID:", rpcData[0].doctor_id);
        console.log("- Full Name:", rpcData[0].fullname);
        console.log("- Rating field:", rpcData[0].rating);
        console.log("- Community Rating field:", rpcData[0].community_rating);
        console.log("- All fields:", Object.keys(rpcData[0]));
      }
    }
    
    // Test 2: Direct query to doctors table to check column names
    console.log("\n2. Testing direct query to doctors table...");
    const { data: directData, error: directError } = await supabase
      .from('doctors')
      .select('doctor_id, fullname, rating, community_rating, review_count')
      .limit(3);
    
    if (directError) {
      console.error("Direct query error:", directError);
    } else {
      console.log(`Direct query returned ${directData?.length || 0} doctors`);
      if (directData && directData.length > 0) {
        console.log("Sample doctor from direct query:");
        directData.forEach((doctor, index) => {
          console.log(`Doctor ${index + 1}:`);
          console.log("- Doctor ID:", doctor.doctor_id);
          console.log("- Full Name:", doctor.fullname);
          console.log("- Rating field:", doctor.rating);
          console.log("- Community Rating field:", doctor.community_rating);
          console.log("- Review Count:", doctor.review_count);
        });
      }
    }
    
    // Test 3: Check table schema
    console.log("\n3. Checking doctors table schema...");
    const { data: schemaData, error: schemaError } = await supabase
      .from('doctors')
      .select('*')
      .limit(1);
    
    if (schemaError) {
      console.error("Schema check error:", schemaError);
    } else if (schemaData && schemaData.length > 0) {
      console.log("Available columns in doctors table:");
      console.log(Object.keys(schemaData[0]).sort());
    }
    
    // Test 4: Check if there are any doctors with non-null community_rating
    console.log("\n4. Checking for doctors with non-null community_rating...");
    const { data: ratedDoctors, error: ratedError } = await supabase
      .from('doctors')
      .select('doctor_id, fullname, community_rating')
      .not('community_rating', 'is', null)
      .gt('community_rating', 0)
      .order('community_rating', { ascending: false })
      .limit(5);
    
    if (ratedError) {
      console.error("Rated doctors query error:", ratedError);
    } else {
      console.log(`Found ${ratedDoctors?.length || 0} doctors with non-null community_rating > 0`);
      if (ratedDoctors && ratedDoctors.length > 0) {
        ratedDoctors.forEach(doctor => {
          console.log(`- ${doctor.fullname}: ${doctor.community_rating}`);
        });
      }
    }
    
    // Test 5: Check if there are any doctors with non-null rating (old column)
    console.log("\n5. Checking for doctors with non-null rating (old column)...");
    const { data: oldRatedDoctors, error: oldRatedError } = await supabase
      .from('doctors')
      .select('doctor_id, fullname, rating')
      .not('rating', 'is', null)
      .gt('rating', 0)
      .order('rating', { ascending: false })
      .limit(5);
    
    if (oldRatedError) {
      console.error("Old rated doctors query error:", oldRatedError);
    } else {
      console.log(`Found ${oldRatedDoctors?.length || 0} doctors with non-null rating > 0`);
      if (oldRatedDoctors && oldRatedDoctors.length > 0) {
        oldRatedDoctors.forEach(doctor => {
          console.log(`- ${doctor.fullname}: ${doctor.rating}`);
        });
      }
    }
    
  } catch (error) {
    console.error("Unexpected error:", error);
  }
}

// Run the debug function
debugHomepageRPC().then(() => {
  console.log("\n=== DEBUG COMPLETE ===");
  process.exit(0);
}).catch(error => {
  console.error("Script error:", error);
  process.exit(1);
});
