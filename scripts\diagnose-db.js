// Script to diagnose database connection issues
const { createClient } = require('@supabase/supabase-js');

// Database credentials with service role key
const supabaseUrl = "https://uapbzzscckhtptliynyj.supabase.co";
const serviceRoleKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q";

// Create a Supabase client with the service role key
const supabase = createClient(supabaseUrl, serviceRoleKey);

// Function to check database tables
async function diagnoseDatabase() {
  console.log("DIAGNOSTIC: Checking database connection and tables...");
  
  try {
    // First, check if we can connect to database with basic query
    console.log("\n===== CHECKING BASIC CONNECTION =====");
    const { data: healthCheck, error: healthError } = await supabase.from('countries').select('count');
    
    if (healthError) {
      console.error("ERROR: Cannot connect to database:", healthError);
    } else {
      console.log("SUCCESS: Basic connection to database is working");
    }
    
    // Check if countries table exists and what columns it has
    console.log("\n===== CHECKING COUNTRIES TABLE STRUCTURE =====");
    try {
      // Trying to get table info - we can't do this directly, so we'll query the table
      const { data: countries, error: countriesError } = await supabase.from('countries').select('*').limit(1);
      
      if (countriesError) {
        console.error("ERROR: Problem with countries table:", countriesError);
      } else if (!countries || countries.length === 0) {
        console.log("NOTE: Countries table exists but is empty or you don't have permission to see records");
      } else {
        console.log("SUCCESS: Countries table exists with the following columns:", Object.keys(countries[0]));
      }
    } catch (e) {
      console.error("ERROR: Cannot query countries table:", e);
    }
    
    // Check what tables we have access to
    console.log("\n===== CHECKING ACCESSIBLE TABLES =====");
    try {
      // We can't directly list tables with JS client, but we can try some known tables
      const tables = ['countries', 'specialties', 'doctors', 'hospitals'];
      
      for (const table of tables) {
        const { data, error } = await supabase.from(table).select('count');
        
        if (error) {
          console.log(`Table '${table}': ERROR - ${error.message}`);
        } else {
          console.log(`Table '${table}': ✓ Accessible`);
        }
      }
    } catch (e) {
      console.error("ERROR: Problem checking tables:", e);
    }
    
    // Get actual countries data
    console.log("\n===== FETCHING COUNTRIES DATA =====");
    const { data: actualCountries, error: actualError } = await supabase
      .from('countries')
      .select('*');
    
    if (actualError) {
      console.error("ERROR: Problem fetching countries:", actualError);
    } else if (!actualCountries || actualCountries.length === 0) {
      console.log("WARNING: No countries found in the database - Is the table empty?");
    } else {
      console.log(`SUCCESS: Found ${actualCountries.length} countries in database:`);
      actualCountries.forEach(c => console.log(`  - ID: ${c.country_id}, Name: ${c.country_name}`));
    }

    // Try a different query approach
    console.log("\n===== TRYING ALTERNATIVE QUERY APPROACH =====");
    const { data: rawData, error: rawError } = await supabase.rpc('get_all_countries');
    
    if (!rawError) {
      console.log("SUCCESS: Alternative query worked");
      console.log(rawData);
    } else {
      console.log("NOTE: Alternative query not available (expected):", rawError.message);

      // Try one last approach - just getting all IDs
      const { data: idsOnly, error: idsError } = await supabase
        .from('countries')
        .select('country_id');
      
      if (!idsError && idsOnly && idsOnly.length > 0) {
        console.log("SUCCESS: Could query just IDs. Found:", idsOnly.map(c => c.country_id).join(', '));
      } else {
        console.log("ERROR: Even querying just IDs failed:", idsError);
      }
    }
  } catch (error) {
    console.error("CRITICAL ERROR in diagnosis:", error);
  }
}

// Run the diagnosis
diagnoseDatabase(); 