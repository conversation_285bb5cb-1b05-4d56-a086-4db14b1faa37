#!/usr/bin/env node

/**
 * Frontend Performance Check Script
 * This script analyzes the frontend codebase for performance issues
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Frontend Performance Analysis for Doctors Leagues Application');
console.log('================================================================\n');

// Check bundle size and dependencies
function checkPackageJson() {
    console.log('📦 CHECKING PACKAGE.JSON DEPENDENCIES');
    console.log('=====================================');

    try {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        const dependencies = packageJson.dependencies || {};
        const devDependencies = packageJson.devDependencies || {};

        console.log(`Total dependencies: ${Object.keys(dependencies).length}`);
        console.log(`Total dev dependencies: ${Object.keys(devDependencies).length}`);

        // Check for heavy dependencies
        const heavyDeps = [
            'framer-motion',
            '@radix-ui',
            'recharts',
            'lucide-react',
            'next'
        ];

        console.log('\n🔍 Heavy Dependencies Found:');
        heavyDeps.forEach(dep => {
            const found = Object.keys(dependencies).filter(key => key.includes(dep));
            if (found.length > 0) {
                found.forEach(f => console.log(`  ✓ ${f}: ${dependencies[f]}`));
            }
        });

        // Check for bundle analyzer
        if (devDependencies['@next/bundle-analyzer']) {
            console.log('\n✅ Bundle analyzer is available');
        } else {
            console.log('\n❌ Bundle analyzer not found - recommend adding @next/bundle-analyzer');
        }

    } catch (error) {
        console.error('❌ Error reading package.json:', error.message);
    }
}

// Check Next.js configuration
function checkNextConfig() {
    console.log('\n⚙️  CHECKING NEXT.JS CONFIGURATION');
    console.log('==================================');

    try {
        if (fs.existsSync('next.config.mjs')) {
            const config = fs.readFileSync('next.config.mjs', 'utf8');

            // Check for performance optimizations
            const optimizations = [
                { name: 'Image optimization', pattern: /images.*unoptimized.*false/ },
                { name: 'Compression enabled', pattern: /compress.*true/ },
                { name: 'Bundle analyzer', pattern: /BundleAnalyzerPlugin/ },
                { name: 'Experimental features', pattern: /experimental/ },
                { name: 'Webpack build worker', pattern: /webpackBuildWorker/ },
                { name: 'Parallel builds', pattern: /parallelServerBuildTraces/ }
            ];

            optimizations.forEach(opt => {
                if (opt.pattern.test(config)) {
                    console.log(`  ✅ ${opt.name} - ENABLED`);
                } else {
                    console.log(`  ❌ ${opt.name} - NOT FOUND`);
                }
            });

        } else {
            console.log('❌ next.config.mjs not found');
        }
    } catch (error) {
        console.error('❌ Error reading next.config.mjs:', error.message);
    }
}

// Check homepage component structure
function checkHomepageStructure() {
    console.log('\n🏠 CHECKING HOMEPAGE COMPONENT STRUCTURE');
    console.log('========================================');

    try {
        if (fs.existsSync('app/page.tsx')) {
            const homepage = fs.readFileSync('app/page.tsx', 'utf8');

            // Check for performance issues
            const checks = [
                { name: 'Client component', pattern: /"use client"/, issue: true },
                { name: 'useEffect for data fetching', pattern: /useEffect.*fetch/, issue: true },
                { name: 'Multiple API calls', pattern: /supabase\.rpc.*supabase\.rpc/s, issue: true },
                { name: 'Framer Motion usage', pattern: /motion\./, issue: true },
                { name: 'Dynamic imports', pattern: /dynamic.*import/, good: true },
                { name: 'Image optimization', pattern: /next\/image/, good: true }
            ];

            checks.forEach(check => {
                if (check.pattern.test(homepage)) {
                    if (check.issue) {
                        console.log(`  ⚠️  ${check.name} - PERFORMANCE CONCERN`);
                    } else if (check.good) {
                        console.log(`  ✅ ${check.name} - GOOD`);
                    }
                } else {
                    if (check.good) {
                        console.log(`  ❌ ${check.name} - NOT FOUND`);
                    }
                }
            });

        } else {
            console.log('❌ app/page.tsx not found');
        }
    } catch (error) {
        console.error('❌ Error reading app/page.tsx:', error.message);
    }
}

// Check for caching implementation
function checkCachingImplementation() {
    console.log('\n💾 CHECKING CACHING IMPLEMENTATION');
    console.log('==================================');

    try {
        if (fs.existsSync('lib/hybrid-data-service.ts')) {
            const dataService = fs.readFileSync('lib/hybrid-data-service.ts', 'utf8');

            const cachingChecks = [
                { name: 'unstable_cache usage', pattern: /unstable_cache/, good: true },
                { name: 'Cache configuration', pattern: /CACHE_TIMES/, good: true },
                { name: 'Revalidation times', pattern: /revalidate/, good: true }
            ];

            cachingChecks.forEach(check => {
                if (check.pattern.test(dataService)) {
                    console.log(`  ✅ ${check.name} - IMPLEMENTED`);
                } else {
                    console.log(`  ❌ ${check.name} - NOT FOUND`);
                }
            });
        } else {
            console.log('❌ lib/hybrid-data-service.ts not found');
        }
    } catch (error) {
        console.error('❌ Error checking caching:', error.message);
    }
}

// Check for image optimization
function checkImageOptimization() {
    console.log('\n🖼️  CHECKING IMAGE OPTIMIZATION');
    console.log('===============================');

    try {
        // Check for image files in public directory
        if (fs.existsSync('public')) {
            const files = fs.readdirSync('public', { recursive: true });
            const imageFiles = files.filter(file =>
                /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(file)
            );

            console.log(`Total image files found: ${imageFiles.length}`);

            // Check for large images
            const largeImages = [];
            imageFiles.forEach(file => {
                try {
                    const filePath = path.join('public', file);
                    const stats = fs.statSync(filePath);
                    if (stats.size > 500000) { // 500KB
                        largeImages.push({
                            file,
                            size: Math.round(stats.size / 1024) + 'KB'
                        });
                    }
                } catch (e) {
                    // Skip if file doesn't exist
                }
            });

            if (largeImages.length > 0) {
                console.log('\n⚠️  Large images found (>500KB):');
                largeImages.forEach(img => {
                    console.log(`  - ${img.file}: ${img.size}`);
                });
            } else {
                console.log('\n✅ No large images found');
            }
        }
    } catch (error) {
        console.error('❌ Error checking images:', error.message);
    }
}

// Main execution
function runAnalysis() {
    checkPackageJson();
    checkNextConfig();
    checkHomepageStructure();
    checkCachingImplementation();
    checkImageOptimization();

    console.log('\n📊 PERFORMANCE RECOMMENDATIONS');
    console.log('==============================');
    console.log('1. Run the database audit script: scripts/performance-audit.sql');
    console.log('2. Check bundle size: npm run build && npm run analyze');
    console.log('3. Test homepage loading time in development vs production');
    console.log('4. Monitor Core Web Vitals in browser dev tools');
    console.log('5. Consider converting homepage to Server Component');
    console.log('\n✅ Analysis complete!');
}

// Run the analysis
runAnalysis();