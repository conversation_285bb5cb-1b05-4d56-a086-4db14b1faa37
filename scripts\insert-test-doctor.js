// <PERSON><PERSON>t to insert a test doctor and add their auth credentials
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');

// Constants
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'password123';
const SALT_ROUNDS = 10;

async function main() {
  try {
    // Check for required environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Error: NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be defined in .env.local');
      process.exit(1);
    }

    // Create Supabase client with admin privileges
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );
    
    console.log('Connecting to Supabase at:', process.env.NEXT_PUBLIC_SUPABASE_URL);
    
    // First, let's check the table structure to confirm column names
    console.log('Checking table structure...');
    const { data: tableInfo, error: tableError } = await supabase
      .from('doctors')
      .select('*')
      .limit(1);
      
    if (tableError) {
      console.error('Error checking table structure:', tableError);
      return;
    }
    
    if (tableInfo && tableInfo.length > 0) {
      console.log('Table columns:', Object.keys(tableInfo[0]));
    } else {
      console.log('No existing doctors found, but we can still create one');
    }
    
    // Check if doctor with this email already exists
    const { data: existingDoctor, error: checkError } = await supabase
      .from('doctors')
      .select('doctor_id')
      .eq('email', TEST_EMAIL)
      .maybeSingle();
      
    if (checkError) {
      console.error('Error checking for existing doctor:', checkError);
      return;
    }
    
    if (existingDoctor) {
      console.log(`Doctor with email ${TEST_EMAIL} already exists with ID ${existingDoctor.doctor_id}`);
      // Use the existing doctor ID for auth credentials
      await insertAuthCredentials(supabase, existingDoctor.doctor_id);
      return;
    }
    
    // Hash the password for the doctor table
    const hashedPassword = await bcrypt.hash(TEST_PASSWORD, SALT_ROUNDS);
    console.log('Password hashed');
    
    // Insert the doctor using the correct column names and data types
    console.log('Inserting doctor...');
    const doctorData = {
      fullname: "Dr. Test Doctor",
      facility: "Test Hospital",
      medical_title: "Cardiologist",
      specialty: "Cardiology",
      subspecialty: "Interventional Cardiology",
      educational_background: "American Board of Cardiology",
      board_certifications: "Board Certified",
      experience: 10, // Integer
      publications: "Journal of Cardiology Research",
      awards_recognitions: "Top Doctor Award",
      phone_number: "+**********",
      email: TEST_EMAIL,
      languages_spoken: "English, Spanish",
      professional_affiliations: "American Medical Association",
      procedures_performed: "Cardiac Catheterization, Echocardiography",
      treatment_services_expertise: "Heart Disease Treatment, Preventive Cardiology",
      hospital_id: null, // This would be a proper hospital ID if available
      image_path: null,
      wins: 0,
      losses: 0,
      draws: 0,
      form: "New",
      community_rating: 5.0, // Numeric
      review_count: 0,
      country_id: null,
      specialty_id: null,
      password: hashedPassword // Save the hashed password in the doctors table too
    };
    
    const { data: insertedDoctor, error: insertError } = await supabase
      .from('doctors')
      .insert([doctorData])
      .select()
      .single();
      
    if (insertError) {
      console.error('Error inserting doctor:', insertError);
      return;
    }
    
    console.log('Doctor inserted with ID:', insertedDoctor.doctor_id);
    
    // Now insert the auth credentials
    await insertAuthCredentials(supabase, insertedDoctor.doctor_id);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

async function insertAuthCredentials(supabase, doctorId) {
  try {
    // Hash the password for auth credentials
    const hashedPassword = await bcrypt.hash(TEST_PASSWORD, SALT_ROUNDS);
    
    console.log('Inserting auth credentials...');
    const { data: authData, error: authError } = await supabase
      .from('auth_credentials')
      .insert([{
        email: TEST_EMAIL,
        hashed_password: hashedPassword,
        user_profile_id: doctorId,
        user_type: 'doctor'
      }]);
      
    if (authError) {
      console.error('Error inserting auth credentials:', authError);
      return false;
    }
    
    console.log('Success! Test doctor created with the following credentials:');
    console.log('Email:', TEST_EMAIL);
    console.log('Password:', TEST_PASSWORD);
    console.log('You can now log in with these credentials.');
    return true;
  } catch (error) {
    console.error('Error in insertAuthCredentials:', error);
    return false;
  }
}

main(); 