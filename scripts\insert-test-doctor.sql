-- SQL script to insert a test doctor directly

-- Step 1: Insert the doctor
INSERT INTO public.doctors (
    "Fullname",
    "Hospital",
    "Medical Specialty",
    "Educational Board",
    "Phone Number",
    "Experience",
    "Subspecialization",
    "Certifications",
    "Recognitions",
    "Languages",
    "Professional affiliation",
    "Procedures performed",
    "Treatment services",
    "Hospital affiliation",
    email
) VALUES (
    'Dr. Test Doctor',
    'Test Hospital',
    'Cardiology',
    'American Board of Cardiology',
    '+**********',
    '10 years',
    'Interventional Cardiology',
    'Board Certified',
    'Top Doctor Award',
    'English, Spanish',
    'American Medical Association',
    'Cardiac Catheterization, Echocardiography',
    'Heart Disease Treatment, Preventive Cardiology',
    'Test Hospital Network',
    '<EMAIL>'
)
RETURNING id;

-- Note the ID returned from the above query
-- Replace NEW_DOCTOR_ID with the actual ID number

-- Step 2: Insert authentication credentials
INSERT INTO public.auth_credentials (
    email,
    hashed_password,
    user_profile_id,
    user_type
) VALUES (
    '<EMAIL>',
    -- This is the bcrypt hash of 'password123'
    '$2a$10$xZtxkVxfZxnPUjqMDK.VCep8MNAOLUwMqDS3TD3oYmSVJ3m9DNpUe',
    -- Replace NEW_DOCTOR_ID with the actual ID from the first query
    NEW_DOCTOR_ID,
    'doctor'
); 