// <PERSON>ript to insert a test patient and add their auth credentials
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');

// Constants
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'password123';
const SALT_ROUNDS = 10;

// Initialize Supabase client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function main() {
  try {
    console.log('Starting test patient insertion...');
    console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
    console.log('Service role key available:', !!process.env.SUPABASE_SERVICE_ROLE_KEY);
    
    // Check if patient already exists
    const { data: existingPatient, error: checkError } = await supabaseAdmin
      .from('users')
      .select('user_id')
      .eq('email', TEST_EMAIL)
      .single();
    
    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking for existing patient:', checkError);
      return;
    }
    
    if (existingPatient) {
      console.log(`Patient with email ${TEST_EMAIL} already exists with ID ${existingPatient.user_id}`);
      // Use the existing patient ID for auth credentials
      await insertAuthCredentials(supabaseAdmin, existingPatient.user_id);
      return;
    }
    
    // Hash the password
    const hashedPassword = await bcrypt.hash(TEST_PASSWORD, SALT_ROUNDS);
    console.log('Password hashed');
    
    // Insert the patient into users table
    const patientData = {
      username: 'testpatient',
      email: TEST_EMAIL,
      first_name: 'Test',
      last_name: 'Patient',
      gender: 'Other',
      age: 30,
      city: 'Test City',
      country: 'Test Country',
      user_type: 'patient',
      "Medical Condition": 'None',
      "Phone_Number": '+**********',
      "State": 'Test State',
      "Registration date": new Date().toISOString(),
      password: hashedPassword // Save the hashed password in the users table too
    };
    
    const { data: insertedPatient, error: insertError } = await supabaseAdmin
      .from('users')
      .insert([patientData])
      .select()
      .single();
      
    if (insertError) {
      console.error('Error inserting patient:', insertError);
      return;
    }
    
    console.log('Patient inserted with ID:', insertedPatient.user_id);
    
    // Now insert the auth credentials
    await insertAuthCredentials(supabaseAdmin, insertedPatient.user_id);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

async function insertAuthCredentials(supabase, patientId) {
  try {
    // Check if auth credentials already exist
    const { data: existingAuth, error: checkAuthError } = await supabase
      .from('auth_credentials')
      .select('email')
      .eq('email', TEST_EMAIL)
      .single();
    
    if (checkAuthError && checkAuthError.code !== 'PGRST116') {
      console.error('Error checking for existing auth credentials:', checkAuthError);
      return false;
    }
    
    if (existingAuth) {
      console.log(`Auth credentials for ${TEST_EMAIL} already exist`);
      return true;
    }
    
    // Hash the password for auth credentials
    const hashedPassword = await bcrypt.hash(TEST_PASSWORD, SALT_ROUNDS);
    
    console.log('Inserting auth credentials...');
    const { data: authData, error: authError } = await supabase
      .from('auth_credentials')
      .insert([{
        email: TEST_EMAIL,
        hashed_password: hashedPassword,
        user_profile_id: patientId,
        user_type: 'patient',
        is_verified: true // Set as verified for testing
      }]);
      
    if (authError) {
      console.error('Error inserting auth credentials:', authError);
      return false;
    }
    
    console.log('✅ Auth credentials inserted successfully');
    console.log(`Test patient account created:`);
    console.log(`Email: ${TEST_EMAIL}`);
    console.log(`Password: ${TEST_PASSWORD}`);
    console.log(`Patient ID: ${patientId}`);
    
    return true;
  } catch (error) {
    console.error('Error in insertAuthCredentials:', error);
    return false;
  }
}

// Run the script
main().catch(console.error);
