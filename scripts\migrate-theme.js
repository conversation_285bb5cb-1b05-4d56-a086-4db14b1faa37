#!/usr/bin/env node

/**
 * Theme Migration Script
 * 
 * This script helps migrate hardcoded dark theme styles to semantic theme-aware classes.
 * It can be run on individual files or entire directories.
 * 
 * Usage:
 * node scripts/migrate-theme.js <file-or-directory>
 * 
 * Example:
 * node scripts/migrate-theme.js app/admin/blog/
 * node scripts/migrate-theme.js components/header.tsx
 */

const fs = require('fs');
const path = require('path');

// Theme class mappings - same as in lib/theme-utils.ts but for Node.js
const themeClassMappings = {
  // Text colors
  'text-white': 'text-foreground',
  'text-white/70': 'text-muted-foreground',
  'text-white/60': 'text-muted-foreground',
  'text-white/90': 'text-foreground',
  'text-white/80': 'text-foreground/80',
  'text-white/50': 'text-muted-foreground',
  
  // Background colors
  'bg-white/10': 'bg-card',
  'bg-white/5': 'bg-muted/50',
  'bg-white/20': 'bg-accent',
  'bg-black': 'bg-background',
  'bg-black/90': 'bg-background/90',
  'bg-black/95': 'bg-background/95',
  
  // Border colors
  'border-white/20': 'border-border',
  'border-white/30': 'border-border',
  'border-white/10': 'border-border/50',
  
  // Hover states
  'hover:bg-white/10': 'hover:bg-accent',
  'hover:bg-white/20': 'hover:bg-accent',
  'hover:text-white': 'hover:text-foreground',
  
  // Combined patterns
  'border-white/30 text-white hover:bg-white/10': 'border-border text-foreground hover:bg-accent',
  'bg-white/10 border-white/20 text-white': 'bg-card border-border text-card-foreground',
};

/**
 * Replace hardcoded theme classes with semantic equivalents
 */
function replaceThemeClasses(content) {
  let result = content;
  
  // Replace combined patterns first (more specific)
  const combinedPatterns = [
    'border-white/30 text-white hover:bg-white/10',
    'bg-white/10 border-white/20 text-white',
    'text-white border-white/20 hover:bg-white/5',
    'text-white/70 hover:text-white hover:bg-white/10',
  ];
  
  combinedPatterns.forEach(pattern => {
    if (themeClassMappings[pattern]) {
      const regex = new RegExp(pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      result = result.replace(regex, themeClassMappings[pattern]);
    }
  });
  
  // Then replace individual classes
  Object.entries(themeClassMappings).forEach(([hardcoded, themeAware]) => {
    // Skip combined patterns as they're already handled
    if (combinedPatterns.includes(hardcoded)) return;
    
    // Use word boundaries to avoid partial matches
    const regex = new RegExp(`\\b${hardcoded.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g');
    result = result.replace(regex, themeAware);
  });
  
  return result;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const updatedContent = replaceThemeClasses(content);
    
    // Only write if content changed
    if (content !== updatedContent) {
      fs.writeFileSync(filePath, updatedContent);
      console.log(`✅ Updated: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No changes: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Process a directory recursively
 */
function processDirectory(dirPath) {
  const items = fs.readdirSync(dirPath);
  let updatedCount = 0;
  
  items.forEach(item => {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // Skip node_modules, .git, and other ignored directories
      if (!['node_modules', '.git', '.next', 'dist', 'build'].includes(item)) {
        updatedCount += processDirectory(fullPath);
      }
    } else if (stat.isFile()) {
      // Process TypeScript, JavaScript, and TSX files
      if (/\.(ts|tsx|js|jsx)$/.test(item)) {
        if (processFile(fullPath)) {
          updatedCount++;
        }
      }
    }
  });
  
  return updatedCount;
}

/**
 * Main function
 */
function main() {
  const targetPath = process.argv[2];
  
  if (!targetPath) {
    console.error('Usage: node scripts/migrate-theme.js <file-or-directory>');
    process.exit(1);
  }
  
  const fullPath = path.resolve(targetPath);
  
  if (!fs.existsSync(fullPath)) {
    console.error(`Error: Path does not exist: ${fullPath}`);
    process.exit(1);
  }
  
  console.log(`🎨 Starting theme migration for: ${fullPath}`);
  console.log('');
  
  const stat = fs.statSync(fullPath);
  let updatedCount = 0;
  
  if (stat.isFile()) {
    updatedCount = processFile(fullPath) ? 1 : 0;
  } else if (stat.isDirectory()) {
    updatedCount = processDirectory(fullPath);
  }
  
  console.log('');
  console.log(`🎉 Migration complete! Updated ${updatedCount} file(s).`);
  
  if (updatedCount > 0) {
    console.log('');
    console.log('📝 Next steps:');
    console.log('1. Test the application to ensure everything works correctly');
    console.log('2. Check the theme toggle functionality');
    console.log('3. Verify both light and dark themes display properly');
    console.log('4. Commit the changes when satisfied');
  }
}

// Run the script
main(); 