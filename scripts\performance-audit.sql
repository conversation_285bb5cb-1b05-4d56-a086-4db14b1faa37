-- Performance Audit Script for Doctors Leagues Application
-- This script checks the current state of database functions, indexes, and performance-related elements

-- =====================================================
-- 1. CHECK FOR EXISTING RPC FUNCTIONS
-- =====================================================
SELECT
    routine_name as function_name,
    routine_type,
    data_type as return_type,
    routine_definition
FROM information_schema.routines
WHERE routine_schema = 'public'
    AND (routine_name LIKE '%doctor%'
    OR routine_name LIKE '%homepage%')
ORDER BY routine_name;

-- Check specifically for the get_doctors_for_homepage function
SELECT
    proname as function_name,
    pg_get_function_result(oid) as return_type,
    pg_get_function_arguments(oid) as arguments,
    prosrc as function_body
FROM pg_proc
WHERE proname = 'get_doctors_for_homepage';

-- =====================================================
-- 2. CHECK EXISTING INDEXES ON CRITICAL TABLES
-- =====================================================

-- Check indexes on doctors table
SELECT
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes
WHERE tablename = 'doctors'
    AND schemaname = 'public'
ORDER BY indexname;

-- Check indexes on reviews table
SELECT
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes
WHERE tablename = 'reviews'
    AND schemaname = 'public'
ORDER BY indexname;

-- Check indexes on countries table
SELECT
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes
WHERE tablename = 'countries'
    AND schemaname = 'public'
ORDER BY indexname;

-- Check indexes on specialties table
SELECT
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes
WHERE tablename = 'specialties'
    AND schemaname = 'public'
ORDER BY indexname;

-- =====================================================
-- 3. ANALYZE TABLE STATISTICS AND PERFORMANCE
-- =====================================================

-- Check table sizes and row counts
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_stat_get_tuples_returned(c.oid) as tuples_returned,
    pg_stat_get_tuples_fetched(c.oid) as tuples_fetched,
    pg_stat_get_tuples_inserted(c.oid) as tuples_inserted,
    pg_stat_get_tuples_updated(c.oid) as tuples_updated,
    pg_stat_get_tuples_deleted(c.oid) as tuples_deleted
FROM pg_tables pt
JOIN pg_class c ON c.relname = pt.tablename
WHERE schemaname = 'public'
    AND tablename IN ('doctors', 'reviews', 'countries', 'specialties', 'hospitals')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Check for missing indexes (slow queries)
SELECT
    schemaname,
    tablename,
    attname as column_name,
    n_distinct,
    correlation
FROM pg_stats
WHERE schemaname = 'public'
    AND tablename IN ('doctors', 'reviews')
    AND n_distinct > 100  -- Columns with high cardinality that might need indexes
ORDER BY tablename, n_distinct DESC;

-- =====================================================
-- 4. CHECK CURRENT QUERY PERFORMANCE
-- =====================================================

-- Simulate the homepage query to check performance
EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
SELECT *
FROM doctors
WHERE community_rating IS NOT NULL
ORDER BY community_rating DESC
LIMIT 10;

-- Check if there are doctors with community_ratings
SELECT
    COUNT(*) as total_doctors,
    COUNT(community_rating) as doctors_with_community_rating,
    AVG(community_rating) as avg_community_rating,
    MIN(community_rating) as min_community_rating,
    MAX(community_rating) as max_community_rating
FROM doctors;

-- Check distribution of ratings
SELECT
    CASE
        WHEN rating IS NULL THEN 'NULL'
        WHEN rating = 0 THEN '0'
        WHEN rating > 0 AND rating <= 1 THEN '0-1'
        WHEN rating > 1 AND rating <= 2 THEN '1-2'
        WHEN rating > 2 AND rating <= 3 THEN '2-3'
        WHEN rating > 3 AND rating <= 4 THEN '3-4'
        WHEN rating > 4 AND rating <= 5 THEN '4-5'
        ELSE 'Other'
    END as rating_range,
    COUNT(*) as count
FROM doctors
GROUP BY
    CASE
        WHEN rating IS NULL THEN 'NULL'
        WHEN rating = 0 THEN '0'
        WHEN rating > 0 AND rating <= 1 THEN '0-1'
        WHEN rating > 1 AND rating <= 2 THEN '1-2'
        WHEN rating > 2 AND rating <= 3 THEN '2-3'
        WHEN rating > 3 AND rating <= 4 THEN '3-4'
        WHEN rating > 4 AND rating <= 5 THEN '4-5'
        ELSE 'Other'
    END
ORDER BY rating_range;

-- =====================================================
-- 5. CHECK CURRENT HOMEPAGE LOGIC IMPLEMENTATION
-- =====================================================

-- Test the current homepage logic manually
-- First: Try to get doctors with community_rating > 0
SELECT
    doctor_id,
    fullname,
    community_rating,
    wins,
    losses,
    specialty,
    country_id
FROM doctors
WHERE community_rating > 0
ORDER BY community_rating DESC, wins DESC, losses ASC, fullname ASC
LIMIT 10;

-- If no doctors with community_rating > 0, get random doctors
SELECT
    doctor_id,
    fullname,
    community_rating,
    wins,
    losses,
    specialty,
    country_id
FROM doctors
ORDER BY RANDOM()
LIMIT 10;

-- =====================================================
-- 6. CHECK FOR SLOW QUERIES IN pg_stat_statements
-- =====================================================

-- Check if pg_stat_statements extension is enabled
SELECT * FROM pg_extension WHERE extname = 'pg_stat_statements';

-- If enabled, check for slow queries
SELECT
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements
WHERE query LIKE '%doctors%'
ORDER BY mean_time DESC
LIMIT 10;