#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to run the verification and moderation migrations
 * This script will create the necessary tables for review verification and moderation
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { autoRefreshToken: false, persistSession: false }
});

// Migration files to run in order
const migrationFiles = [
  '20250118000001_create_verification_proofs_table.sql',
  '20250118000002_create_review_flags_table.sql', 
  '20250118000003_add_verification_status_to_reviews.sql',
  '20250118000004_create_appointment_verification_bucket.sql'
];

async function runMigration(filename) {
  console.log(`\n🔄 Running migration: ${filename}`);

  try {
    const migrationPath = path.join(__dirname, '..', 'migrations', filename);

    if (!fs.existsSync(migrationPath)) {
      console.error(`❌ Migration file not found: ${migrationPath}`);
      return false;
    }

    const sql = fs.readFileSync(migrationPath, 'utf8');
    console.log(`📄 Read migration file: ${migrationPath}`);

    // For storage bucket creation, skip and provide manual instructions
    if (filename.includes('create_appointment_verification_bucket')) {
      console.log(`📦 Storage bucket migration detected`);
      console.log(`⚠️  Please create the 'appointment-verification' bucket manually in Supabase dashboard`);
      console.log(`   - Set bucket to private (not public)`);
      console.log(`   - File size limit: 5MB`);
      console.log(`   - Allowed MIME types: image/jpeg, image/jpg, image/png, image/webp`);
      return true;
    }

    // For table creation, we'll try to create them using direct table operations
    if (filename.includes('verification_proofs')) {
      console.log(`📋 Creating verification_proofs table...`);
      // Check if table exists by trying to query it
      const { error: checkError } = await supabase
        .from('verification_proofs')
        .select('id')
        .limit(1);

      if (!checkError) {
        console.log(`✅ Table verification_proofs already exists`);
        return true;
      }

      console.log(`⚠️  Table verification_proofs needs to be created manually`);
      console.log(`   Please run this SQL in Supabase SQL Editor:`);
      console.log(`   ${sql.substring(0, 200)}...`);
      return true;
    }

    if (filename.includes('review_flags')) {
      console.log(`📋 Creating review_flags table...`);
      const { error: checkError } = await supabase
        .from('review_flags')
        .select('id')
        .limit(1);

      if (!checkError) {
        console.log(`✅ Table review_flags already exists`);
        return true;
      }

      console.log(`⚠️  Table review_flags needs to be created manually`);
      console.log(`   Please run this SQL in Supabase SQL Editor:`);
      console.log(`   ${sql.substring(0, 200)}...`);
      return true;
    }

    if (filename.includes('verification_status')) {
      console.log(`📋 Adding verification_status column to reviews...`);
      // Check if column exists by trying to select it
      const { error: checkError } = await supabase
        .from('reviews')
        .select('verification_status')
        .limit(1);

      if (!checkError) {
        console.log(`✅ Column verification_status already exists in reviews table`);
        return true;
      }

      console.log(`⚠️  Column verification_status needs to be added manually`);
      console.log(`   Please run this SQL in Supabase SQL Editor:`);
      console.log(`   ${sql.substring(0, 200)}...`);
      return true;
    }

    console.log(`✅ Migration completed: ${filename}`);
    return true;

  } catch (error) {
    console.error(`❌ Error running migration ${filename}:`, error.message);
    return false;
  }
}

async function runAllMigrations() {
  console.log('🚀 Starting verification and moderation migrations...');
  console.log(`📍 Supabase URL: ${supabaseUrl.substring(0, 30)}...`);
  
  let successCount = 0;
  
  for (const filename of migrationFiles) {
    const success = await runMigration(filename);
    if (success) {
      successCount++;
    } else {
      console.error(`❌ Migration failed: ${filename}`);
      console.error('🛑 Stopping migration process due to error');
      break;
    }
  }
  
  console.log(`\n📊 Migration Summary:`);
  console.log(`   ✅ Successful: ${successCount}/${migrationFiles.length}`);
  console.log(`   ❌ Failed: ${migrationFiles.length - successCount}/${migrationFiles.length}`);
  
  if (successCount === migrationFiles.length) {
    console.log('\n🎉 All migrations completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Create the "appointment-verification" storage bucket in Supabase dashboard');
    console.log('   2. Set bucket to private (not public)');
    console.log('   3. Configure bucket policies for security');
    console.log('   4. Test the verification workflow');
  } else {
    console.log('\n⚠️  Some migrations failed. Please check the errors above.');
    process.exit(1);
  }
}

// Test database connection first
async function testConnection() {
  console.log('🔍 Testing database connection...');
  
  try {
    const { data, error } = await supabase
      .from('reviews')
      .select('review_id')
      .limit(1);
    
    if (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }
    
    console.log('✅ Database connection successful');
    return true;
    
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    return false;
  }
}

// Main execution
async function main() {
  const connectionOk = await testConnection();
  
  if (!connectionOk) {
    console.error('🛑 Cannot proceed without database connection');
    process.exit(1);
  }
  
  await runAllMigrations();
}

// Run the script
main().catch(error => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
