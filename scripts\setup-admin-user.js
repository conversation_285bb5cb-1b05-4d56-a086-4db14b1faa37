#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to set up admin users for the verification and moderation system
 * This script will help you configure existing users as admins
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables from .env.local
const envPath = path.join(__dirname, '..', '.env.local');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');
  
  envLines.forEach(line => {
    if (line.trim() && !line.startsWith('#')) {
      const [key, value] = line.split('=');
      if (key && value) {
        process.env[key.trim()] = value.trim();
      }
    }
  });
}

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

// Create Supabase client with service role
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { autoRefreshToken: false, persistSession: false }
});

async function listExistingUsers() {
  console.log('📋 Fetching existing users...\n');
  
  try {
    // Check auth_credentials table for custom auth users
    const { data: authUsers, error: authError } = await supabase
      .from('auth_credentials')
      .select('id, email, user_type, user_profile_id')
      .order('id');
    
    if (authError) {
      console.error('❌ Error fetching auth_credentials:', authError.message);
    } else if (authUsers && authUsers.length > 0) {
      console.log('🔐 Custom Auth Users (auth_credentials table):');
      authUsers.forEach((user, index) => {
        console.log(`   ${index + 1}. Email: ${user.email}`);
        console.log(`      Type: ${user.user_type}`);
        console.log(`      Profile ID: ${user.user_profile_id}`);
        console.log(`      Auth ID: ${user.id}\n`);
      });
    }
    
    // Check if there are Supabase Auth users
    try {
      const { data: supabaseUsers, error: supabaseError } = await supabase.auth.admin.listUsers();
      
      if (!supabaseError && supabaseUsers?.users?.length > 0) {
        console.log('👤 Supabase Auth Users:');
        supabaseUsers.users.forEach((user, index) => {
          console.log(`   ${index + 1}. Email: ${user.email}`);
          console.log(`      ID: ${user.id}`);
          console.log(`      Role: ${user.user_metadata?.role || 'none'}`);
          console.log(`      User Type: ${user.user_metadata?.userType || 'none'}\n`);
        });
      }
    } catch (supabaseAuthError) {
      console.log('ℹ️  Supabase Auth users not accessible (this is normal for custom auth)');
    }
    
    return { authUsers, customAuthOnly: true };
    
  } catch (error) {
    console.error('❌ Error listing users:', error.message);
    return { authUsers: [], customAuthOnly: true };
  }
}

async function makeUserAdmin(email) {
  console.log(`🔧 Setting up admin role for: ${email}`);
  
  try {
    // For custom auth system, we need to add an admin flag to the user
    // First, find the user in auth_credentials
    const { data: authUser, error: findError } = await supabase
      .from('auth_credentials')
      .select('*')
      .eq('email', email)
      .single();
    
    if (findError || !authUser) {
      console.error(`❌ User not found in auth_credentials: ${email}`);
      return false;
    }
    
    console.log(`✅ Found user: ${authUser.email} (ID: ${authUser.id})`);
    
    // Check if admin_role column exists in auth_credentials
    const { error: checkColumnError } = await supabase
      .from('auth_credentials')
      .select('admin_role')
      .limit(1);
    
    if (checkColumnError) {
      console.log('📝 Adding admin_role column to auth_credentials table...');
      
      // Add admin_role column
      const addColumnSQL = `
        ALTER TABLE public.auth_credentials 
        ADD COLUMN IF NOT EXISTS admin_role BOOLEAN DEFAULT false;
        
        COMMENT ON COLUMN public.auth_credentials.admin_role IS 'Indicates if the user has admin privileges for moderation';
      `;
      
      console.log('⚠️  Please run this SQL in your Supabase SQL Editor:');
      console.log(addColumnSQL);
      console.log('\nThen run this script again.');
      return false;
    }
    
    // Update user to have admin role
    const { error: updateError } = await supabase
      .from('auth_credentials')
      .update({ admin_role: true })
      .eq('email', email);
    
    if (updateError) {
      console.error(`❌ Error setting admin role: ${updateError.message}`);
      return false;
    }
    
    console.log(`🎉 Successfully set admin role for: ${email}`);
    
    // Also try to update Supabase Auth user metadata if it exists
    try {
      const { data: supabaseUsers } = await supabase.auth.admin.listUsers();
      const supabaseUser = supabaseUsers?.users?.find(u => u.email === email);
      
      if (supabaseUser) {
        await supabase.auth.admin.updateUserById(supabaseUser.id, {
          user_metadata: {
            ...supabaseUser.user_metadata,
            role: 'admin',
            admin_role: true
          }
        });
        console.log(`✅ Also updated Supabase Auth metadata for: ${email}`);
      }
    } catch (supabaseError) {
      // This is fine, we're using custom auth primarily
      console.log('ℹ️  Supabase Auth metadata update skipped (using custom auth)');
    }
    
    return true;
    
  } catch (error) {
    console.error(`❌ Error making user admin: ${error.message}`);
    return false;
  }
}

async function verifyAdminSetup(email) {
  console.log(`🔍 Verifying admin setup for: ${email}`);
  
  try {
    const { data: user, error } = await supabase
      .from('auth_credentials')
      .select('email, user_type, admin_role')
      .eq('email', email)
      .single();
    
    if (error || !user) {
      console.error(`❌ Could not verify user: ${email}`);
      return false;
    }
    
    console.log(`📊 User Status:`);
    console.log(`   Email: ${user.email}`);
    console.log(`   User Type: ${user.user_type}`);
    console.log(`   Admin Role: ${user.admin_role ? '✅ YES' : '❌ NO'}`);
    
    return user.admin_role === true;
    
  } catch (error) {
    console.error(`❌ Error verifying admin setup: ${error.message}`);
    return false;
  }
}

async function main() {
  console.log('🚀 Admin User Setup Script');
  console.log('==========================\n');
  
  // Test database connection
  console.log('🔍 Testing database connection...');
  try {
    const { error } = await supabase
      .from('auth_credentials')
      .select('id')
      .limit(1);
    
    if (error) {
      console.error('❌ Database connection failed:', error.message);
      process.exit(1);
    }
    console.log('✅ Database connection successful\n');
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    process.exit(1);
  }
  
  // List existing users
  const { authUsers } = await listExistingUsers();
  
  if (!authUsers || authUsers.length === 0) {
    console.log('❌ No users found. Please create a user account first.');
    process.exit(1);
  }
  
  // Get email from command line argument or prompt
  const email = process.argv[2];
  
  if (!email) {
    console.log('📝 Usage: node scripts/setup-admin-user.js <email>');
    console.log('\nExample: node scripts/setup-admin-user.js <EMAIL>');
    console.log('\nAvailable users:');
    authUsers.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.email}`);
    });
    process.exit(1);
  }
  
  // Validate email exists
  const userExists = authUsers.some(user => user.email === email);
  if (!userExists) {
    console.error(`❌ User with email "${email}" not found.`);
    console.log('\nAvailable users:');
    authUsers.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.email}`);
    });
    process.exit(1);
  }
  
  // Make user admin
  const success = await makeUserAdmin(email);
  
  if (success) {
    console.log('\n' + '='.repeat(50));
    await verifyAdminSetup(email);
    console.log('\n🎉 Admin setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. The user can now access admin moderation functions');
    console.log('   2. Test the admin actions in your application');
    console.log('   3. Create the database tables if you haven\'t already');
  } else {
    console.log('\n❌ Admin setup failed. Please check the errors above.');
    process.exit(1);
  }
}

// Run the script
main().catch(error => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
