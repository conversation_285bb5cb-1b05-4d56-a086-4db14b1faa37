require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

console.log('🧪 Simple RPC Test');
console.log('==================');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('URL:', supabaseUrl ? 'Found' : 'Missing');
console.log('Key:', supabaseKey ? 'Found' : 'Missing');

if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing environment variables');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testRPC() {
    try {
        console.log('\n⏱️  Testing get_doctors_for_homepage...');
        const startTime = Date.now();

        const { data, error } = await supabase.rpc('get_doctors_for_homepage', {
            limit_count: 5
        });

        const endTime = Date.now();

        if (error) {
            console.error('❌ RPC Error:', error);
            return;
        }

        console.log(`✅ Success in ${endTime - startTime}ms`);
        console.log(`📊 Returned ${data?.length || 0} doctors`);

        if (data && data.length > 0) {
            console.log('\n📋 Sample:');
            data.slice(0, 2).forEach(doctor => {
                console.log(`- ${doctor.fullname} (Rating: ${doctor.rating || 'N/A'})`);
            });
        }

    } catch (error) {
        console.error('❌ Exception:', error.message);
    }
}

testRPC().then(() => {
    console.log('\n✅ Test complete');
    process.exit(0);
}).catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
});