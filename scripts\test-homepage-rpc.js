#!/usr/bin/env node

/**
 * Test Homepage RPC Function
 * This script tests the get_doctors_for_homepage RPC function to see if it exists and works
 */

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing Supabase environment variables');
    console.log('Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testHomepageRPC() {
    console.log('🧪 Testing get_doctors_for_homepage RPC Function');
    console.log('================================================\n');

    try {
        console.log('⏱️  Testing RPC function call...');
        const startTime = Date.now();

        const { data, error } = await supabase.rpc('get_doctors_for_homepage', {
            limit_count: 10
        });

        const endTime = Date.now();
        const duration = endTime - startTime;

        if (error) {
            console.error('❌ RPC Function Error:', error);
            console.log('\n🔍 This suggests the function does not exist or has issues');
            console.log('📝 Check the database for the function definition');
            return false;
        }

        console.log(`✅ RPC function executed successfully in ${duration}ms`);
        console.log(`📊 Returned ${data?.length || 0} doctors`);

        if (data && data.length > 0) {
            console.log('\n📋 Sample data:');
            console.log('Doctor ID | Name | Rating | Specialty');
            console.log('---------|------|--------|----------');
            data.slice(0, 3).forEach(doctor => {
                console.log(`${doctor.doctor_id} | ${doctor.fullname || 'N/A'} | ${doctor.rating || 'N/A'} | ${doctor.specialty || 'N/A'}`);
            });
        }

        return true;

    } catch (error) {
        console.error('❌ Exception during RPC test:', error.message);
        return false;
    }
}

async function testAlternativeQueries() {
    console.log('\n🔄 Testing Alternative Query Methods');
    console.log('====================================\n');

    try {
        // Test direct query for doctors with ratings
        console.log('⏱️  Testing direct query for doctors with ratings...');
        const startTime1 = Date.now();

        const { data: ratedDoctors, error: ratedError } = await supabase
            .from('doctors')
            .select('doctor_id, fullname, rating, specialty, wins, losses')
            .not('rating', 'is', null)
            .gt('rating', 0)
            .order('rating', { ascending: false })
            .limit(10);

        const endTime1 = Date.now();
        console.log(`✅ Rated doctors query: ${endTime1 - startTime1}ms, found ${ratedDoctors?.length || 0} doctors`);

        // Test random doctors query
        console.log('⏱️  Testing random doctors query...');
        const startTime2 = Date.now();

        const { data: randomDoctors, error: randomError } = await supabase
            .from('doctors')
            .select('doctor_id, fullname, rating, specialty, wins, losses')
            .limit(10);

        const endTime2 = Date.now();
        console.log(`✅ Random doctors query: ${endTime2 - startTime2}ms, found ${randomDoctors?.length || 0} doctors`);

        // Test total doctor count
        console.log('⏱️  Testing total doctor count...');
        const startTime3 = Date.now();

        const { count, error: countError } = await supabase
            .from('doctors')
            .select('*', { count: 'exact', head: true });

        const endTime3 = Date.now();
        console.log(`✅ Doctor count query: ${endTime3 - startTime3}ms, total doctors: ${count || 0}`);

        return {
            ratedDoctors: ratedDoctors?.length || 0,
            randomDoctors: randomDoctors?.length || 0,
            totalDoctors: count || 0
        };

    } catch (error) {
        console.error('❌ Exception during alternative queries:', error.message);
        return null;
    }
}

async function main() {
    console.log('🚀 Starting Homepage Performance Test');
    console.log('====================================\n');

    const rpcSuccess = await testHomepageRPC();
    const altResults = await testAlternativeQueries();

    console.log('\n📊 SUMMARY');
    console.log('==========');
    console.log(`RPC Function Status: ${rpcSuccess ? '✅ Working' : '❌ Not Working'}`);

    if (altResults) {
        console.log(`Doctors with ratings: ${altResults.ratedDoctors}`);
        console.log(`Total doctors: ${altResults.totalDoctors}`);

        if (altResults.ratedDoctors === 0 && altResults.totalDoctors > 0) {
            console.log('\n⚠️  WARNING: No doctors have ratings - this could cause slow random queries');
        }
    }

    if (!rpcSuccess) {
        console.log('\n🛠️  NEXT STEPS:');
        console.log('1. Run the database audit script: scripts/performance-audit.sql');
        console.log('2. Check if get_doctors_for_homepage function exists in database');
        console.log('3. Create the function if missing');
        console.log('4. Add proper indexes for performance');
    }

    console.log('\n✅ Test complete!');
}

main().catch(console.error);