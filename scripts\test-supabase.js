// Import the Supabase client
const { createClient } = require('@supabase/supabase-js');

// Create a Supabase client with the service role key
const supabase = createClient(
  'https://uapbzzscckhtptliynyj.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q'
);

// Function to test each table in our database
async function testDatabase() {
  console.log('==========================================');
  console.log('SUPABASE CONNECTION TEST');
  console.log('==========================================');
  
  try {
    // Test 1: Check countries table
    console.log('\n📊 TESTING COUNTRIES TABLE:');
    const { data: countries, error: countriesError } = await supabase
      .from('countries')
      .select('*')
      .order('country_name');
    
    if (countriesError) {
      console.error('❌ Error fetching countries:', countriesError);
    } else {
      console.log(`✅ Successfully fetched ${countries.length} countries`);
      console.log('Countries:');
      countries.forEach(country => {
        console.log(`  - ${country.country_id}: ${country.country_name}`);
      });
    }
    
    // Test 2: Check specialties table
    console.log('\n📊 TESTING SPECIALTIES TABLE:');
    const { data: specialties, error: specialtiesError } = await supabase
      .from('specialties')
      .select('*')
      .order('specialty_name');
    
    if (specialtiesError) {
      console.error('❌ Error fetching specialties:', specialtiesError);
    } else {
      console.log(`✅ Successfully fetched ${specialties.length} specialties`);
      console.log('First 5 specialties:');
      specialties.slice(0, 5).forEach(specialty => {
        console.log(`  - ${specialty.specialty_id}: ${specialty.specialty_name}`);
      });
    }
    
    // Test 3: Check doctors table
    console.log('\n📊 TESTING DOCTORS TABLE:');
    const { data: doctors, error: doctorsError } = await supabase
      .from('doctors')
      .select('*')
      .limit(5);
    
    if (doctorsError) {
      console.error('❌ Error fetching doctors:', doctorsError);
    } else {
      console.log(`✅ Successfully fetched sample of ${doctors.length} doctors`);
      console.log('Sample doctors:');
      doctors.forEach(doctor => {
        console.log(`  - Doctor ID: ${doctor.doctor_id}, Country ID: ${doctor.country_id}, Specialty ID: ${doctor.specialty_id}`);
      });
    }
    
    // Test 4: Join test between doctors and countries
    console.log('\n📊 TESTING JOIN BETWEEN DOCTORS AND COUNTRIES:');
    const { data: doctorsWithCountry, error: joinError } = await supabase
      .from('doctors')
      .select(`
        doctor_id,
        countries(country_id, country_name)
      `)
      .limit(5);
    
    if (joinError) {
      console.error('❌ Error performing join query:', joinError);
    } else {
      console.log(`✅ Successfully performed join query`);
      console.log('Sample results:');
      doctorsWithCountry.forEach(item => {
        console.log(`  - Doctor ID: ${item.doctor_id}, Country: ${item.countries?.country_name || 'Unknown'}`);
      });
    }
    
    // Test 5: Check RLS bypass by trying to delete a record (and rolling back)
    console.log('\n📊 TESTING RLS BYPASS (WITH ROLLBACK):');
    
    // Start a transaction
    const { error: txError } = await supabase.rpc('begin_transaction');
    if (txError) {
      console.error('❌ Error starting transaction:', txError);
    } else {
      // Try to delete something (would fail with regular key if RLS is set up)
      const { error: deleteError } = await supabase
        .from('countries')
        .delete()
        .eq('country_id', -999); // Using a non-existent ID for safety
      
      if (deleteError && deleteError.code !== 'PGRST116') { // PGRST116 is expected for "no rows returned"
        console.error('❌ Delete operation error:', deleteError);
      } else {
        console.log('✅ Delete operation successful (RLS bypassed)');
      }
      
      // Roll back the transaction (don't actually delete anything)
      const { error: rollbackError } = await supabase.rpc('rollback_transaction');
      if (rollbackError) {
        console.error('❌ Error rolling back transaction:', rollbackError);
      } else {
        console.log('✅ Transaction rolled back successfully');
      }
    }
    
  } catch (error) {
    console.error('❌ Unexpected error during tests:', error);
  }
  
  console.log('\n==========================================');
  console.log('TEST COMPLETE');
  console.log('==========================================');
}

// Run the tests
testDatabase().catch(err => {
  console.error('Fatal error:', err);
  process.exit(1);
}); 