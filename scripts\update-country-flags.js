// Script to update country flag URLs in the database
const { createClient } = require('@supabase/supabase-js');

// Database credentials with service role key
const supabaseUrl = "https://uapbzzscckhtptliynyj.supabase.co";
const serviceRoleKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q";

// Create a Supabase client with the service role key
const supabase = createClient(supabaseUrl, serviceRoleKey);

// Country flag data
const countryFlagData = [
  { id: 1, name: "Bahrain", flag: "https://flagcdn.com/w320/bh.png" },
  { id: 5, name: "Kuwait", flag: "https://flagcdn.com/w320/kw.png" },
  { id: 6, name: "Oman", flag: "https://flagcdn.com/w320/om.png" },
  { id: 4, name: "Qatar", flag: "https://flagcdn.com/w320/qa.png" },
  { id: 2, name: "Saudi Arabia", flag: "https://flagcdn.com/w320/sa.png" },
  { id: 3, name: "UAE", flag: "https://flagcdn.com/w320/ae.png" },
  { id: 7, name: "USA", flag: "https://flagcdn.com/w320/us.png" }
];

// Function to update flag URLs
async function updateCountryFlags() {
  console.log("Updating country flag URLs in the database...");
  
  try {
    // First get the current countries
    const { data: countries, error: fetchError } = await supabase
      .from('countries')
      .select('*');
    
    if (fetchError) {
      console.error("Error fetching countries:", fetchError);
      return;
    }
    
    console.log("Current countries in database:", countries);
    
    // Update each country with its flag URL
    for (const countryData of countryFlagData) {
      console.log(`Updating flag URL for ${countryData.name} (ID: ${countryData.id})...`);
      
      const { error: updateError } = await supabase
        .from('countries')
        .update({ flag_url: countryData.flag })
        .eq('country_id', countryData.id);
      
      if (updateError) {
        console.error(`Error updating flag URL for ${countryData.name}:`, updateError);
      } else {
        console.log(`Successfully updated flag URL for ${countryData.name}`);
      }
    }
    
    // Verify the updates
    const { data: updatedCountries, error: verifyError } = await supabase
      .from('countries')
      .select('*');
    
    if (verifyError) {
      console.error("Error verifying updates:", verifyError);
      return;
    }
    
    console.log("\nUpdated countries in database:");
    updatedCountries.forEach(country => {
      console.log(`ID: ${country.country_id}, Name: ${country.country_name}, Flag URL: ${country.flag_url || 'No flag'}`);
    });
  } catch (error) {
    console.error("Unexpected error:", error);
  }
}

// Run the update
updateCountryFlags(); 