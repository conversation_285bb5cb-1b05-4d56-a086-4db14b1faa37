-- This script updates the 'hospital' column in the 'doctors' table with the
-- corresponding hospital name from the 'hospitals' table.
-- It joins the two tables on 'hospital_id' and sets the 'hospital'
-- for all doctors who have a valid hospital ID and where the hospital column is currently null.

UPDATE public.doctors
SET hospital = h.hospital_name
FROM public.hospitals h
WHERE public.doctors.hospital_id = h.hospital_id
  AND public.doctors.hospital IS NULL;

-- Log a message to confirm completion
SELECT 'Doctor hospitals updated successfully.' as status; 