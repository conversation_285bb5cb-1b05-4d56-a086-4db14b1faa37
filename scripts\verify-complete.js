// Script to verify that all components are working properly with the database
const { createClient } = require('@supabase/supabase-js');

// Database credentials with service role key
const supabaseUrl = "https://uapbzzscckhtptliynyj.supabase.co";
const serviceRoleKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q";

// Create a Supabase client with the service role key
const supabase = createClient(supabaseUrl, serviceRoleKey);

// Function to verify all components
async function verifyAllComponents() {
  console.log("VERIFICATION: Checking if all data required by components is available");
  
  // 1. Check countries for Header component
  console.log("\n===== CHECKING COUNTRIES FOR HEADER COMPONENT =====");
  try {
    const { data: countries, error } = await supabase.from('countries').select('*');
    
    if (error) {
      console.error("❌ ERROR: Cannot fetch countries:", error.message);
    } else if (!countries || countries.length === 0) {
      console.error("❌ ERROR: No countries found in database");
    } else {
      console.log(`✅ SUCCESS: Found ${countries.length} countries`);
      countries.forEach(c => console.log(`  - ID: ${c.country_id}, Name: ${c.country_name}`));
      
      // Check navigation path to country divisions
      const firstCountry = countries[0];
      console.log(`\nNavigation check: Users should be able to navigate to /divisions/${firstCountry.country_id}`);
    }
  } catch (e) {
    console.error("❌ ERROR: Unexpected error checking countries:", e);
  }
  
  // 2. Check specialties for Country Divisions page
  console.log("\n===== CHECKING SPECIALTIES FOR DIVISIONS PAGE =====");
  try {
    const { data: specialties, error } = await supabase.from('specialties').select('*');
    
    if (error) {
      console.error("❌ ERROR: Cannot fetch specialties:", error.message);
    } else if (!specialties || specialties.length === 0) {
      console.warn("⚠️ WARNING: No specialties found in database");
      console.log("NOTE: This means divisions pages won't display specialty cards, but the page should still load");
    } else {
      console.log(`✅ SUCCESS: Found ${specialties.length} specialties`);
      
      // Check if specialties are linked to countries
      const hasCountryIds = specialties.some(s => s.country_id !== undefined);
      if (hasCountryIds) {
        console.log("✅ SUCCESS: Specialties have country_id field");
      } else {
        console.warn("⚠️ WARNING: Specialties don't have country_id field - filtering by country may not work");
      }
    }
  } catch (e) {
    console.error("❌ ERROR: Unexpected error checking specialties:", e);
  }
  
  // 3. Verify country details page path for a specific country
  console.log("\n===== VERIFYING COUNTRY DETAILS PAGE =====");
  try {
    // Get first country
    const { data: firstCountry, error: countryError } = await supabase
      .from('countries')
      .select('*')
      .limit(1)
      .single();
    
    if (countryError || !firstCountry) {
      console.error("❌ ERROR: Could not get a country to test:", countryError?.message || "No countries found");
    } else {
      console.log(`✅ SUCCESS: Country details path: /divisions/${firstCountry.country_id}`);
      console.log(`  - When users navigate to this path, they should see details for: ${firstCountry.country_name}`);
      
      // Get specialties for this country (if the country_id field exists)
      try {
        const { data: countrySpecialties, error: specialtiesError } = await supabase
          .from('specialties')
          .select('*')
          .eq('country_id', firstCountry.country_id);
        
        if (specialtiesError) {
          console.warn(`⚠️ WARNING: Error fetching specialties for country ${firstCountry.country_id}:`, specialtiesError.message);
        } else if (!countrySpecialties || countrySpecialties.length === 0) {
          console.warn(`⚠️ WARNING: No specialties found for country ${firstCountry.country_id} (${firstCountry.country_name})`);
        } else {
          console.log(`✅ SUCCESS: Found ${countrySpecialties.length} specialties for ${firstCountry.country_name}`);
        }
      } catch (e) {
        console.error("❌ ERROR: Unexpected error checking country specialties:", e);
      }
    }
  } catch (e) {
    console.error("❌ ERROR: Unexpected error verifying country details page:", e);
  }
  
  console.log("\n===== VERIFICATION SUMMARY =====");
  console.log("If all checks passed, your application should be working properly with the database");
  console.log("If there were warnings, some features might not work perfectly but the app should load");
  console.log("If there were errors, you'll need to fix them for the app to work properly");
}

// Run the verification
verifyAllComponents(); 