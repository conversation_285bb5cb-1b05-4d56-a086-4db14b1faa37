const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://uapbzzscckhtptliynyj.supabase.co'; // Replace with your Supabase URL
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q'; // Replace with your Supabase service role key
const supabase = createClient(supabaseUrl, supabaseKey);

async function insertUser(email) {
  console.log('Starting user insertion process...');
  const { data, error } = await supabase.admin.auth.api.createUser({
    email,
    password: 'securepassword', // Set a default password
  });

  if (error) {
    console.error('Error inserting user:', error);
  } else {
    console.log('User inserted successfully:', data);
  }
}

// Replace with the email you want to insert
insertUser('<EMAIL>')
  .then(() => console.log('Script execution completed.'))
  .catch(err => console.error('Unexpected error:', err));
