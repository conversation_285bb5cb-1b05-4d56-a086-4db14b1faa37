-- SETUP SCRIPT: Configure Supabase for Storage Deletion
-- Run this BEFORE the main trigger script

-- STEP 1: Enable required extensions
CREATE EXTENSION IF NOT EXISTS "http";

-- STEP 2: Create custom settings for Supabase configuration
-- You'll need to replace these with your actual values
ALTER DATABASE postgres SET "app.supabase_url" = 'https://your-project-id.supabase.co';
ALTER DATABASE postgres SET "app.supabase_service_role_key" = 'your-service-role-key-here';

-- STEP 3: Grant necessary permissions for storage operations
-- Grant access to storage schema (if needed)
GRANT USAGE ON SCHEMA storage TO postgres;
GRANT ALL ON storage.objects TO postgres;
GRANT ALL ON storage.buckets TO postgres;

-- STEP 4: Create a test function to verify storage access
CREATE OR REPLACE FUNCTION test_storage_access()
RETURNS TABLE(bucket_name TEXT, object_count BIGINT)
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT 
        b.name as bucket_name,
        COUNT(o.id) as object_count
    FROM storage.buckets b
    LEFT JOIN storage.objects o ON b.id = o.bucket_id
    GROUP BY b.name
    ORDER BY b.name;
$$;

-- STEP 5: Test storage access
SELECT 'Testing storage access...' as status;
SELECT * FROM test_storage_access();

-- STEP 6: Instructions for configuration
SELECT 'IMPORTANT: Update the following in delete-doctor-images-trigger.sql:' as instructions;
SELECT '1. Change bucket_name from "doctor-images" to your actual bucket name' as step1;
SELECT '2. Update app.supabase_url with your project URL' as step2;
SELECT '3. Update app.supabase_service_role_key with your service role key' as step3;
SELECT '4. Ensure the http extension is enabled' as step4;
