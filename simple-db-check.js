const { createClient } = require('@supabase/supabase-js');

// Direct values from .env.local
const SUPABASE_URL = "https://uapbzzscckhtptliynyj.supabase.co";
const SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q";

async function checkTables() {
  console.log('🔍 Checking verification tables...\n');
  
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

  try {
    // Test connection
    console.log('Testing connection...');
    const { data: testData, error: testError } = await supabase
      .from('reviews')
      .select('review_id')
      .limit(1);

    if (testError) {
      console.error('❌ Connection failed:', testError.message);
      return;
    }
    console.log('✅ Connection successful');

    // Check verification_proofs table
    console.log('\nChecking verification_proofs table...');
    const { data: proofsData, error: proofsError } = await supabase
      .from('verification_proofs')
      .select('*')
      .limit(5);

    if (proofsError) {
      console.log('❌ verification_proofs table error:', proofsError.message);
      console.log('   This table probably doesn\'t exist yet');
    } else {
      console.log(`✅ verification_proofs table exists with ${proofsData.length} records`);
      if (proofsData.length > 0) {
        console.log('   Sample data:', JSON.stringify(proofsData[0], null, 2));
      }
    }

    // Check review_flags table
    console.log('\nChecking review_flags table...');
    const { data: flagsData, error: flagsError } = await supabase
      .from('review_flags')
      .select('*')
      .limit(5);

    if (flagsError) {
      console.log('❌ review_flags table error:', flagsError.message);
      console.log('   This table probably doesn\'t exist yet');
    } else {
      console.log(`✅ review_flags table exists with ${flagsData.length} records`);
    }

    // Check reviews table for verification_status column
    console.log('\nChecking reviews table for verification_status...');
    const { data: reviewsData, error: reviewsError } = await supabase
      .from('reviews')
      .select('review_id, verification_status')
      .limit(5);

    if (reviewsError) {
      console.log('❌ reviews table error:', reviewsError.message);
      if (reviewsError.message.includes('verification_status')) {
        console.log('   The verification_status column probably doesn\'t exist yet');
      }
    } else {
      console.log(`✅ reviews table has verification_status column`);
      console.log('   Sample data:', JSON.stringify(reviewsData, null, 2));
    }

    // Check storage bucket
    console.log('\nChecking appointment-verification storage bucket...');
    const { data: bucketData, error: bucketError } = await supabase.storage
      .from('appointment-verification')
      .list('', { limit: 5 });

    if (bucketError) {
      console.log('❌ Storage bucket error:', bucketError.message);
      console.log('   The appointment-verification bucket probably doesn\'t exist yet');
    } else {
      console.log(`✅ Storage bucket exists with ${bucketData.length} items`);
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

checkTables();
