@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    /* Beautiful Modern Light Theme - Professional Medical Design - ORIGINAL VALUES RESTORED */
    --background: 128 35% 88%;  /* Light green background - User Request */
    --foreground: 142 60% 20%;  /* Dark green for excellent readability */
    --card: 0 0% 100%;          /* Pure white cards for clean contrast */
    --card-foreground: 142 60% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 142 60% 20%;
    --primary: 142 76% 36%;     /* Medical green for trust and health */
    --primary-foreground: 0 0% 98%;
    --secondary: 210 40% 96%;   /* Light blue-gray for subtle elements */
    --secondary-foreground: 142 50% 25%;
    --muted: 210 40% 94%;       /* Soft muted backgrounds */
    --muted-foreground: 142 80% 20%;  /* Darker green for high visibility */
    --accent: 199 89% 96%;      /* Light cyan accent for highlights */
    --accent-foreground: 142 60% 20%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 40% 88%;      /* Soft borders with subtle color */
    --input: 210 40% 96%;       /* Light input backgrounds */
    --ring: 142 76% 36%;        /* Focus rings match primary */
    --chart-1: 142 76% 36%;     /* Medical green */
    --chart-2: 199 89% 48%;     /* Medical blue */
    --chart-3: 32 95% 44%;      /* Medical orange */
    --chart-4: 271 81% 56%;     /* Medical purple */
    --chart-5: 15 80% 60%;      /* Medical red */
    --radius: 0.75rem;          /* Slightly more rounded for friendliness */

    /* Sidebar colors for light theme - ORIGINAL VALUES RESTORED */
    --sidebar-background: 142 70% 25%;  /* Dark green for contrast with light green background */
    --sidebar-foreground: 0 0% 98%;     /* White text on dark green sidebar */
    --sidebar-primary: 142 76% 36%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 210 40% 94%;
    --sidebar-accent-foreground: 0 0% 98%;  /* White text on dark green sidebar */
    --sidebar-border: 210 40% 90%;
    --sidebar-ring: 142 76% 36%;

    /* Semantic colors for medical application */
    --success: 142 76% 36%;     /* Medical green */
    --success-foreground: 0 0% 98%;
    --warning: 32 95% 44%;      /* Medical orange */
    --warning-foreground: 0 0% 98%;
    --error: 0 84% 60%;         /* Medical red */
    --error-foreground: 0 0% 98%;
    --info: 199 89% 48%;        /* Medical blue */
    --info-foreground: 0 0% 98%;
  }
  .dark {
    /* Dark theme colors - Updated background to dark green */
    --background: 142 70% 15%;  /* Dark green background for dark theme */
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 123 41% 49%; /* Keep consistent green primary */
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 123 41% 49%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Dark theme semantic colors */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --warning: 32 95% 44%;
    --warning-foreground: 0 0% 98%;
    --error: 0 62.8% 30.6%;
    --error-foreground: 0 0% 98%;
    --info: 199 89% 48%;
    --info-foreground: 0 0% 98%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom classes for navigation menu */
.nav-menu-item {
  font-size: 0.95rem;
  letter-spacing: -0.01em;
}

.nav-button {
  padding-left: 0.6rem;
  padding-right: 0.6rem;
  margin-left: -0.1rem;
  margin-right: -0.1rem;
}

.action-button {
  white-space: nowrap;
  font-size: 0.9rem;
}

/* Navigation spacing adjustments */
.nav-menu-icon {
  transform: scale(0.9);
}

.action-buttons {
  margin-left: 0.5rem;
}

/* Fix for join match button/action buttons */
@media (min-width: 768px) and (max-width: 1023px) {
  .action-button {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    font-size: 0.85rem;
  }

  .nav-button {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}

/* Beautiful Theme-Aware Hero Text Styling with Maximum Visibility */
.light .hero-text {
  color: #ffffff !important;
  text-shadow:
    3px 3px 12px rgba(0, 0, 0, 0.95),
    0 0 8px rgba(0, 0, 0, 0.9),
    2px 2px 6px rgba(0, 0, 0, 1),
    1px 1px 3px rgba(0, 0, 0, 1);
  font-weight: 700;
  -webkit-text-stroke: 1px rgba(0, 0, 0, 0.6);
  letter-spacing: 0.025em;
}

.dark .hero-text {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  font-weight: 500;
}

/* Hero section title with maximum visibility */
.light .hero-title {
  color: #ffffff !important;
  text-shadow:
    4px 4px 15px rgba(0, 0, 0, 0.98),
    0 0 12px rgba(0, 0, 0, 0.95),
    3px 3px 8px rgba(0, 0, 0, 1),
    2px 2px 4px rgba(0, 0, 0, 1);
  font-weight: 800;
  -webkit-text-stroke: 1.2px rgba(0, 0, 0, 0.7);
  letter-spacing: 0.05em;
}

.dark .hero-title {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  font-weight: 600;
}

/* Standings and other overlaid titles */
.light .standings-title {
  color: #ffffff !important;
  text-shadow:
    3px 3px 10px rgba(0, 0, 0, 0.95),
    0 0 8px rgba(0, 0, 0, 0.9),
    2px 2px 4px rgba(0, 0, 0, 1);
  font-weight: 800;
  -webkit-text-stroke: 0.8px rgba(0, 0, 0, 0.5);
}

.dark .standings-title {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  font-weight: 600;
}

/* Improve light theme readability for overlays */
.light .text-overlay {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .text-overlay {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Better contrast for cards in light theme */
.light .hero-card {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dark .hero-card {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
}

/* Beautiful Soft Image Overlays */
.light .image-overlay {
  background: linear-gradient(180deg,
    rgba(240, 248, 255, 0.1) 0%,
    rgba(230, 245, 255, 0.4) 30%,
    rgba(220, 242, 255, 0.6) 70%,
    rgba(210, 239, 255, 0.8) 100%);
}

.dark .image-overlay {
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.4) 100%);
}

/* Beautiful Friendly Form Styling for Light Theme */
.light .form-friendly {
  background: rgba(255, 255, 255, 0.96) !important;
  border: 2px solid rgba(142, 176, 136, 0.25) !important;
  border-radius: 1rem !important;
  box-shadow:
    0 10px 40px rgba(0, 0, 0, 0.05),
    0 4px 20px rgba(142, 176, 136, 0.08) !important;
  backdrop-filter: blur(12px);
}

.dark .form-friendly {
  background: rgba(0, 0, 0, 0.7);
  border: 2px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Soft, inviting input styling */
.light .input-friendly {
  background: rgba(250, 253, 255, 0.95) !important;
  border: 1.5px solid rgba(142, 176, 136, 0.2) !important;
  border-radius: 0.75rem !important;
  color: #2c3e50 !important;
  transition: all 0.3s ease;
}

.light .input-friendly:focus {
  border-color: rgba(142, 176, 136, 0.5) !important;
  box-shadow: 0 0 0 4px rgba(142, 176, 136, 0.1) !important;
  background: rgba(255, 255, 255, 0.98) !important;
  outline: none;
}

.dark .input-friendly {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Professional, warm button styling */
.light .button-friendly {
  background: linear-gradient(135deg,
    rgba(142, 176, 136, 0.92) 0%,
    rgba(125, 165, 120, 0.96) 100%) !important;
  border: 1px solid rgba(142, 176, 136, 0.3) !important;
  color: #ffffff !important;
  font-weight: 600;
  box-shadow:
    0 6px 20px rgba(142, 176, 136, 0.15),
    0 2px 10px rgba(0, 0, 0, 0.04) !important;
  backdrop-filter: blur(6px);
  transition: all 0.3s ease;
}

.light .button-friendly:hover {
  background: linear-gradient(135deg,
    rgba(132, 166, 126, 0.96) 0%,
    rgba(115, 155, 110, 1) 100%) !important;
  box-shadow:
    0 8px 25px rgba(142, 176, 136, 0.25),
    0 4px 15px rgba(0, 0, 0, 0.06) !important;
  transform: translateY(-1px);
}

/* Enhanced text readability */
.light .text-overlay {
  color: #ffffff !important;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.85), 0 0 6px rgba(0, 0, 0, 0.7);
  font-weight: 600;
}

.dark .text-overlay {
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6);
  font-weight: 500;
}

/* Navigation Menu Dark Green Theme for Light Green Background */
.light .nav-menu-item {
  color: hsl(142, 70%, 20%) !important; /* Dark green for better contrast on light green background */
  font-weight: 600;
}

.dark .nav-menu-item {
  color: hsl(0, 0%, 98%); /* White for dark theme */
}

/* Dark theme hero button styles - Make Join as Doctor button green */
.dark .hero-button-secondary {
  background: linear-gradient(135deg, hsl(142, 76%, 36%) 0%, hsl(142, 76%, 32%) 100%) !important;
  color: #ffffff !important;
  border: none !important;
}

.dark .hero-button-secondary:hover {
  background: linear-gradient(135deg, hsl(142, 76%, 32%) 0%, hsl(142, 76%, 28%) 100%) !important;
  color: #ffffff !important;
}

/* Dark theme navigation menu text color - WHITE */
.dark .nav-menu-item {
  color: #ffffff !important;
}

/* Dark theme page titles */
.dark h1[style*="hsl(142, 76%, 25%)"] {
  color: #ffffff !important;
}

/* Dark theme card backgrounds and light green borders */
.dark .card,
.dark [class*="bg-background/40"],
.dark [class*="bg-background/60"],
.dark [class*="bg-background/80"],
.dark .bg-gradient-to-b {
  background: hsl(142, 70%, 15%) !important;
  border: 2px solid hsl(142, 50%, 50%) !important;
}

/* Dark theme text visibility */
.dark [class*="text-foreground/70"],
.dark [class*="text-foreground/80"],
.dark [class*="text-foreground/60"],
.dark .text-yellow-500,
.dark .text-green-500,
.dark .text-green-600 {
  color: #ffffff !important;
}

/* Dark theme links visibility */
.dark a:not(.nav-menu-item) {
  color: #ffffff !important;
}

/* Remove unwanted lines/borders near navigation items */
.dark .nav-menu-item::after,
.dark .nav-menu-item::before,
.dark nav a::after,
.dark nav a::before,
.dark nav button::after,
.dark nav button::before {
  display: none !important;
}

/* Patient forms and popups - BLUE theme styling */
.dark .patient-registration-dialog,
.dark .patient-form,
.dark .patient-popup {
  background: linear-gradient(135deg, hsl(220, 80%, 25%) 0%, hsl(220, 70%, 20%) 100%) !important;
  border: 2px solid hsl(220, 60%, 50%) !important;
}

/* Footer dark theme styling */
.dark .footer-dark-theme {
  background: hsl(142, 70%, 15%) !important;
  border-color: hsl(142, 50%, 50%) !important;
}

/* Hero Section Button Fixes */
.light .hero-button-primary {
  background: linear-gradient(135deg, hsl(142, 76%, 36%) 0%, hsl(142, 76%, 32%) 100%) !important;
  color: #ffffff !important;
  border: none !important;
}

.light .hero-button-secondary {
  border: 2px solid hsl(142, 76%, 36%) !important;
  color: hsl(142, 76%, 36%) !important;
  background: rgba(255, 255, 255, 0.9) !important;
}

.light .hero-button-secondary:hover {
  background: hsl(142, 76%, 36%) !important;
  color: #ffffff !important;
}

/* League Standings Title Fix */
.light .standings-header {
  color: hsl(142, 76%, 30%) !important;
  text-shadow: none !important;
  font-weight: 800 !important;
  background: none !important;
  -webkit-background-clip: unset !important;
  background-clip: unset !important;
}

.dark .standings-header {
  color: #ffffff;
  background: linear-gradient(to right, #ffffff, hsl(142, 76%, 36%), #ffffff);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* About Page Text with Green Outline */
.light .about-overlay-text {
  color: #ffffff !important;
  text-shadow:
    3px 3px 8px rgba(0, 0, 0, 0.9),
    0 0 6px rgba(0, 0, 0, 0.8);
  -webkit-text-stroke: 1px hsl(142, 76%, 36%);
  font-weight: 700;
}

.dark .about-overlay-text {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Thick Green Borders for Cards */
.light .card-thick-border {
  border: 4px solid hsl(142, 76%, 36%) !important;
  border-radius: 1rem !important;
  box-shadow: 0 4px 20px rgba(142, 176, 136, 0.15) !important;
}

.dark .card-thick-border {
  border: 2px solid hsl(142, 76%, 36%);
}

/* Replace Gray with Dark Green */
.light .text-muted-green {
  color: hsl(142, 76%, 25%) !important;
}

.light .bg-muted-green-green {
  background-color: hsl(142, 76%, 95%) !important;
}

/* Registration Form Light Theme Override */
.light [role="dialog"],
.light .registration-dialog {
  background: rgba(255, 255, 255, 0.98) !important;
  border: 2px solid hsl(142, 76%, 36%) !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1) !important;
}

.light .registration-dialog .bg-gradient-to-b,
.light .registration-dialog .bg-gradient-to-r,
.light .registration-dialog .bg-green-800,
.light .registration-dialog .bg-green-900,
.light .registration-dialog .bg-green-950 {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 253, 248, 0.98) 100%) !important;
}

.light .registration-dialog .border-green-700,
.light .registration-dialog .border-green-600,
.light .registration-dialog .border-green-500 {
  border-color: hsl(142, 76%, 36%) !important;
}

.light .registration-dialog .text-foreground,
.light .registration-dialog .text-white {
  color: hsl(210, 24%, 16%) !important;
}

.light .registration-dialog input,
.light .registration-dialog textarea,
.light .registration-dialog select {
  background: rgba(250, 253, 255, 0.95) !important;
  border: 1.5px solid hsl(142, 76%, 36%) !important;
  color: hsl(210, 24%, 16%) !important;
}

.light .registration-dialog input:focus,
.light .registration-dialog textarea:focus,
.light .registration-dialog select:focus {
  border-color: hsl(142, 76%, 30%) !important;
  box-shadow: 0 0 0 3px rgba(142, 176, 136, 0.2) !important;
  background: rgba(255, 255, 255, 0.98) !important;
}

.light .registration-dialog button {
  background: linear-gradient(135deg,
    hsl(142, 76%, 36%) 0%,
    hsl(142, 76%, 32%) 100%) !important;
  color: #ffffff !important;
  border: 1px solid hsl(142, 76%, 30%) !important;
}

.light .registration-dialog button:hover {
  background: linear-gradient(135deg,
    hsl(142, 76%, 32%) 0%,
    hsl(142, 76%, 28%) 100%) !important;
}

/* Head-to-Head Section Title Fix */
.light .head-to-head-title {
  color: hsl(142, 76%, 30%) !important;
  text-shadow: none !important;
  background: none !important;
  -webkit-background-clip: unset !important;
  background-clip: unset !important;
}

/* Thicker Borders for Cards and Boxes */
.light .card,
.light [data-card],
.light .specialty-box,
.light .stats-card,
.light .division-card,
.light .team-card {
  border-width: 2.5px !important;
  border-color: hsl(142, 76%, 36%) !important;
  box-shadow: 0 4px 15px rgba(142, 176, 136, 0.1) !important;
}

.light .card:hover,
.light [data-card]:hover,
.light .specialty-box:hover,
.light .stats-card:hover,
.light .division-card:hover,
.light .team-card:hover {
  border-color: hsl(142, 76%, 30%) !important;
  box-shadow: 0 6px 25px rgba(142, 176, 136, 0.2) !important;
}

/* League/Division Page Specialty Cards */
.light .specialty-card,
.light .division-specialty-card {
  border: 3px solid hsl(142, 76%, 36%) !important;
  border-radius: 1rem !important;
}

/* Standings Page Enhanced Cards */
.light .standings-card,
.light .table-container {
  border: 2.5px solid hsl(142, 76%, 36%) !important;
  border-radius: 0.75rem !important;
}

/* Teams Page Cards Enhanced */
.light .team-stats-card {
  border: 3px solid hsl(142, 76%, 36%) !important;
  border-radius: 1rem !important;
  background: rgba(248, 253, 248, 0.8) !important;
}

/* ULTRA-HIGH SPECIFICITY NUCLEAR OVERRIDES - GUARANTEED VISIBILITY */

/* 1. Hero Section Text - NUCLEAR OPTION */
html[data-theme="light"] .hero-title,
html.light .hero-title,
body.light .hero-title {
  color: #ffffff !important;
  text-shadow: 4px 4px 15px rgba(0, 0, 0, 0.98) !important, 0 0 12px rgba(0, 0, 0, 0.95) !important, 3px 3px 8px rgba(0, 0, 0, 1) !important, 2px 2px 4px rgba(0, 0, 0, 1) !important;
  font-weight: 800 !important;
  -webkit-text-stroke: 1.2px rgba(0, 0, 0, 0.7) !important;
  letter-spacing: 0.05em !important;
}

html[data-theme="light"] .hero-text,
html.light .hero-text,
body.light .hero-text {
  color: #ffffff !important;
  text-shadow: 3px 3px 12px rgba(0, 0, 0, 0.95) !important, 0 0 8px rgba(0, 0, 0, 0.9) !important, 2px 2px 6px rgba(0, 0, 0, 1) !important, 1px 1px 3px rgba(0, 0, 0, 1) !important;
  font-weight: 700 !important;
  -webkit-text-stroke: 1px rgba(0, 0, 0, 0.6) !important;
  letter-spacing: 0.025em !important;
}

/* 2. Navigation Menu Dark Green - NUCLEAR OPTION */
html[data-theme="light"] .nav-menu-item,
html.light .nav-menu-item,
body.light .nav-menu-item {
  color: hsl(142, 70%, 20%) !important;
  font-weight: 600 !important;
}

/* 3. Join Match Button White Text - NUCLEAR OPTION */
html[data-theme="light"] [data-join-match-button="true"],
html.light [data-join-match-button="true"],
body.light [data-join-match-button="true"] {
  color: #ffffff !important;
  background: linear-gradient(135deg, hsl(142, 76%, 36%) 0%, hsl(142, 76%, 32%) 100%) !important;
}

/* 4. League Standings Title - NUCLEAR OPTION */
html[data-theme="light"] .standings-header,
html.light .standings-header,
body.light .standings-header {
  color: hsl(142, 76%, 30%) !important;
  text-shadow: none !important;
  background: none !important;
  -webkit-background-clip: unset !important;
  background-clip: unset !important;
  font-weight: 800 !important;
}

/* 5. Featured Cards Thick Borders - NUCLEAR OPTION */
html[data-theme="light"] .card-thick-border,
html.light .card-thick-border,
body.light .card-thick-border {
  border: 3px solid hsl(142, 76%, 36%) !important;
  border-radius: 1rem !important;
  box-shadow: 0 4px 20px rgba(142, 176, 136, 0.15) !important;
}

/* 6. Registration Dialog Light Theme - NUCLEAR OPTION */
html[data-theme="light"] .registration-dialog,
html.light .registration-dialog,
body.light .registration-dialog {
  background: rgba(255, 255, 255, 0.98) !important;
  border: 2px solid hsl(142, 76%, 36%) !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1) !important;
}

html[data-theme="light"] .registration-dialog input,
html[data-theme="light"] .registration-dialog textarea,
html[data-theme="light"] .registration-dialog select,
html.light .registration-dialog input,
html.light .registration-dialog textarea,
html.light .registration-dialog select {
  background: rgba(250, 253, 255, 0.95) !important;
  border: 1.5px solid hsl(142, 76%, 36%) !important;
  color: hsl(210, 24%, 16%) !important;
}

/* 7. About Page Text Overlay - NUCLEAR OPTION */
html[data-theme="light"] .about-overlay-text,
html.light .about-overlay-text,
body.light .about-overlay-text {
  color: #ffffff !important;
  text-shadow: 3px 3px 8px rgba(0, 0, 0, 0.9) !important, 0 0 6px rgba(0, 0, 0, 0.8) !important;
  -webkit-text-stroke: 1px hsl(142, 76%, 36%) !important;
  font-weight: 700 !important;
}

/* 8. All Cards Thick Borders - NUCLEAR OPTION */
html[data-theme="light"] .card,
html.light .card,
body.light .card {
  border-width: 2.5px !important;
  border-color: hsl(142, 76%, 36%) !important;
  box-shadow: 0 4px 15px rgba(142, 176, 136, 0.1) !important;
}

/* 9. Head-to-Head Title - NUCLEAR OPTION */
html[data-theme="light"] .head-to-head-title,
html.light .head-to-head-title,
body.light .head-to-head-title {
  color: hsl(142, 76%, 30%) !important;
  text-shadow: none !important;
  background: none !important;
  -webkit-background-clip: unset !important;
  background-clip: unset !important;
}

/* 10. Teams Page Cards - NUCLEAR OPTION */
html[data-theme="light"] .team-stats-card,
html.light .team-stats-card,
body.light .team-stats-card {
  border: 3px solid hsl(142, 76%, 36%) !important;
  border-radius: 1rem !important;
  background: rgba(248, 253, 248, 0.8) !important;
}

/* 11. Gray to Green Replacement - NUCLEAR OPTION */
html[data-theme="light"] .text-muted-green,
html.light .text-muted-green,
body.light .text-muted-green {
  color: hsl(142, 76%, 25%) !important;
}

html[data-theme="light"] .bg-muted-green,
html.light .bg-muted-green,
body.light .bg-muted-green {
  background-color: hsl(142, 76%, 95%) !important;
}

/* Debug indicator - remove after testing */
html[data-theme="light"]::after {
  content: "LIGHT THEME ACTIVE";
  position: fixed;
  top: 0;
  right: 0;
  background: hsl(142, 76%, 36%);
  color: white;
  padding: 4px 8px;
  font-size: 10px;
  z-index: 9999;
  border-radius: 0 0 0 4px;
}

/* Comprehensive Header Color Changes for Light Theme */
.light h1,
.light h2,
.light h3,
.light h4,
.light h5,
.light h6,
html[data-theme="light"] h1,
html[data-theme="light"] h2,
html[data-theme="light"] h3,
html[data-theme="light"] h4,
html[data-theme="light"] h5,
html[data-theme="light"] h6 {
  color: hsl(142, 76%, 25%) !important;
}

/* Ensure headers maintain proper contrast and styling */
.light .card h1,
.light .card h2,
.light .card h3,
.light .card h4,
.light .card h5,
.light .card h6,
html[data-theme="light"] .card h1,
html[data-theme="light"] .card h2,
html[data-theme="light"] .card h3,
html[data-theme="light"] .card h4,
html[data-theme="light"] .card h5,
html[data-theme="light"] .card h6 {
  color: hsl(142, 76%, 25%) !important;
  text-shadow: none !important;
  background: none !important;
  -webkit-background-clip: unset !important;
  background-clip: unset !important;
}

/* Page titles and section headers */
.light .page-title,
.light .section-title,
html[data-theme="light"] .page-title,
html[data-theme="light"] .section-title {
  color: hsl(142, 76%, 25%) !important;
}

/* Medical blog and help center border standardization */
.light .blog-card,
.light .help-card,
.light .content-card,
html[data-theme="light"] .blog-card,
html[data-theme="light"] .help-card,
html[data-theme="light"] .content-card {
  border: 3px solid hsl(142, 76%, 36%) !important;
  border-radius: 1rem !important;
  box-shadow: 0 4px 20px rgba(142, 176, 136, 0.15) !important;
}

/* Blog post cards and featured posts */
.light [data-blog-card],
.light .featured-post-card,
html[data-theme="light"] [data-blog-card],
html[data-theme="light"] .featured-post-card {
  border: 3px solid hsl(142, 76%, 36%) !important;
  border-radius: 1rem !important;
  box-shadow: 0 4px 20px rgba(142, 176, 136, 0.15) !important;
}



/* Help center and FAQ cards */
.light .faq-card,
.light .help-section-card,
html[data-theme="light"] .faq-card,
html[data-theme="light"] .help-section-card {
  border: 3px solid hsl(142, 76%, 36%) !important;
  border-radius: 1rem !important;
  box-shadow: 0 4px 20px rgba(142, 176, 136, 0.15) !important;
}

/* Admin blog management cards */
.light .admin-card,
.light .blog-admin-card,
html[data-theme="light"] .admin-card,
html[data-theme="light"] .blog-admin-card {
  border: 3px solid hsl(142, 76%, 36%) !important;
  border-radius: 1rem !important;
  box-shadow: 0 4px 20px rgba(142, 176, 136, 0.15) !important;
}







/* Patient forms pure blue styling for dark theme - ultra specific */
.dark [class*="PatientRegistration"] * {
  color: hsl(220, 70%, 85%) !important;
}

.dark [class*="PatientRegistration"] input {
  background: hsl(220, 70%, 15%) !important;
  border: 1px solid hsl(220, 50%, 40%) !important;
  color: #ffffff !important;
}

.dark [class*="PatientRegistration"] button {
  background: linear-gradient(135deg, hsl(220, 70%, 45%) 0%, hsl(220, 75%, 40%) 100%) !important;
  border: 1px solid hsl(220, 60%, 50%) !important;
  color: #ffffff !important;
}

/* Remove navigation lines completely in dark theme */
.dark header *,
.dark nav *,
.dark [class*="HeaderClient"] * {
  border-bottom: none !important;
  border-image: none !important;
  text-decoration: none !important;
}

.dark header *::before,
.dark header *::after,
.dark nav *::before,
.dark nav *::after {
  display: none !important;
  content: none !important;
  border: none !important;
  background: none !important;
}

/* Specific standings link fix */
.dark a[href="/standings"],
.dark [href="/standings"] {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.dark a[href="/standings"] *,
.dark [href="/standings"] * {
  border-bottom: none !important;
  text-decoration: none !important;
}

.dark a[href="/standings"] *,
.dark .text-sm[style*="hsl(120, 4.8%, 95.9%)"] {
  color: hsl(120, 4.8%, 95.9%) !important;
}

.light .doctor-specialty {
  color: hsl(142, 60%, 20%) !important; /* dark green */
}

/* Light theme dashboard button */
.light .dashboard-button-light {
  background-color: hsl(220, 80%, 60%) !important;
  color: white !important;
  border-color: hsl(220, 80%, 50%) !important;
}

.light .dashboard-button-light:hover {
  background-color: hsl(220, 80%, 55%) !important;
}

.main-nav-links {
  gap: 0.25rem;
}

.light .main-nav-links {
  gap: 0.15rem;
}



.purple-dialog-bg {
  background: linear-gradient(145deg, hsl(0, 0%, 100%) 0%, hsl(0, 0%, 98%) 100%) !important;
  border: 1px solid hsl(0, 0%, 85%) !important;
  opacity: 1 !important;
}
.dark .purple-dialog-bg {
  background: linear-gradient(145deg, hsl(142, 20%, 15%) 0%, hsl(142, 25%, 12%) 100%) !important;
  border: 1px solid hsl(142, 30%, 25%) !important;
  opacity: 1 !important;
}

.dark .input-group-container {
  border: 1px solid hsla(142, 76%, 36%, 0.3) !important;
  border-radius: 1rem !important;
  padding: 1.5rem !important;
  background-color: hsla(142, 70%, 15%, 0.2) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
