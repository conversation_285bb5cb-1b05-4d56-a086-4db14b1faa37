-- =====================================================
-- SUPABASE DOCTOR ID REUSE SOLUTION
-- =====================================================
-- This SQL script creates functions and triggers to reuse deleted doctor IDs
-- instead of always incrementing from the highest ID.
--
-- Problem: If we have IDs 1-100, delete ID 50, new record gets ID 101
-- Solution: New record should get ID 50 (filling the gap)
-- =====================================================

-- Step 1: Create a function to find the next available (lowest unused) doctor_id
CREATE OR REPLACE FUNCTION get_next_available_doctor_id()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    next_id INTEGER;
BEGIN
    -- Find the first gap in the sequence by looking for the smallest positive integer
    -- that doesn't exist in either doctors or doctors_registration tables
    WITH combined_ids AS (
        -- Get all existing IDs from both tables
        SELECT doctor_id FROM doctors WHERE doctor_id IS NOT NULL
        UNION
        SELECT doctor_id FROM doctors_registration WHERE doctor_id IS NOT NULL
    ),
    -- Generate a series from 1 to the maximum ID + 1
    id_series AS (
        SELECT generate_series(1, COALESCE((SELECT MAX(doctor_id) FROM combined_ids), 0) + 1) AS id
    )
    -- Find the first missing ID
    SELECT id INTO next_id
    FROM id_series
    WHERE id NOT IN (SELECT doctor_id FROM combined_ids)
    ORDER BY id
    LIMIT 1;
    
    -- If no gap found (shouldn't happen with the logic above), fallback to max + 1
    IF next_id IS NULL THEN
        SELECT COALESCE(MAX(doctor_id), 0) + 1 INTO next_id
        FROM (
            SELECT doctor_id FROM doctors WHERE doctor_id IS NOT NULL
            UNION
            SELECT doctor_id FROM doctors_registration WHERE doctor_id IS NOT NULL
        ) AS all_ids;
    END IF;
    
    RETURN next_id;
END;
$$;

-- Step 2: Create a function to set the next available doctor_id before insert
CREATE OR REPLACE FUNCTION set_next_doctor_id()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Only set the doctor_id if it's not already provided or is NULL
    IF NEW.doctor_id IS NULL THEN
        NEW.doctor_id := get_next_available_doctor_id();
    END IF;
    
    RETURN NEW;
END;
$$;

-- Step 3: Create triggers for both tables to automatically assign IDs

-- Trigger for doctors_registration table
DROP TRIGGER IF EXISTS trigger_set_doctor_id_registration ON doctors_registration;
CREATE TRIGGER trigger_set_doctor_id_registration
    BEFORE INSERT ON doctors_registration
    FOR EACH ROW
    EXECUTE FUNCTION set_next_doctor_id();

-- Trigger for doctors table  
DROP TRIGGER IF EXISTS trigger_set_doctor_id_doctors ON doctors;
CREATE TRIGGER trigger_set_doctor_id_doctors
    BEFORE INSERT ON doctors
    FOR EACH ROW
    EXECUTE FUNCTION set_next_doctor_id();

-- Step 4: Create a helper function for manual ID assignment (optional)
-- This can be called from application code when you need to get the next ID
CREATE OR REPLACE FUNCTION reserve_next_doctor_id()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    reserved_id INTEGER;
BEGIN
    -- Get the next available ID
    reserved_id := get_next_available_doctor_id();
    
    -- Return the reserved ID
    -- Note: The actual reservation happens when the record is inserted
    -- This function just tells you what the next ID will be
    RETURN reserved_id;
END;
$$;

-- =====================================================
-- TESTING THE SOLUTION
-- =====================================================
-- You can test this solution with the following commands:

-- Test 1: Check what the next available ID would be
-- SELECT get_next_available_doctor_id();

-- Test 2: Insert a test record and see if it gets the right ID
-- INSERT INTO doctors_registration (fullname, email, status) 
-- VALUES ('Test Doctor', '<EMAIL>', 'pending');

-- Test 3: Check the assigned ID
-- SELECT doctor_id, fullname FROM doctors_registration WHERE fullname = 'Test Doctor';

-- Test 4: Delete a record and insert a new one to see if the gap is filled
-- DELETE FROM doctors_registration WHERE fullname = 'Test Doctor';
-- INSERT INTO doctors_registration (fullname, email, status) 
-- VALUES ('Another Test Doctor', '<EMAIL>', 'pending');

-- =====================================================
-- IMPORTANT NOTES
-- =====================================================
-- 1. This solution works for both doctors and doctors_registration tables
-- 2. The triggers automatically assign IDs, so your application code doesn't need to change
-- 3. If you manually specify a doctor_id during INSERT, the trigger won't override it
-- 4. The function considers IDs from BOTH tables to avoid conflicts
-- 5. This is safe for concurrent operations as PostgreSQL handles the locking
-- 6. Performance: For large datasets, you might want to add an index on doctor_id columns

-- =====================================================
-- ROLLBACK (if needed)
-- =====================================================
-- If you need to remove this solution and go back to the old behavior:
-- 
-- DROP TRIGGER IF EXISTS trigger_set_doctor_id_registration ON doctors_registration;
-- DROP TRIGGER IF EXISTS trigger_set_doctor_id_doctors ON doctors;
-- DROP FUNCTION IF EXISTS set_next_doctor_id();
-- DROP FUNCTION IF EXISTS get_next_available_doctor_id();
-- DROP FUNCTION IF EXISTS reserve_next_doctor_id();

-- =====================================================
-- PERFORMANCE OPTIMIZATION (for large datasets)
-- =====================================================
-- If you have a very large number of doctors (10,000+), you might want to 
-- create indexes to speed up the ID lookup:

-- CREATE INDEX IF NOT EXISTS idx_doctors_doctor_id ON doctors(doctor_id);
-- CREATE INDEX IF NOT EXISTS idx_doctors_registration_doctor_id ON doctors_registration(doctor_id);

-- =====================================================
-- APPLICATION CODE CHANGES (OPTIONAL)
-- =====================================================
-- Your existing application code should work without changes because the triggers
-- handle ID assignment automatically. However, if you want to get the next ID
-- before inserting (for logging or other purposes), you can call:
-- 
-- SELECT reserve_next_doctor_id();
-- 
-- This will tell you what ID will be assigned to the next record.
