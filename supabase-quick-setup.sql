-- QUICK SETUP: Copy and paste this into Supabase SQL Editor
-- This creates TRUE SEQUENTIAL numbering (no gap filling, no reusing deleted IDs)

-- 1. Function to get next sequential doctor_id (MAX + 1)
CREATE OR REPLACE FUNCTION get_next_sequential_doctor_id()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    next_id INTEGER;
BEGIN
    -- Get the maximum doctor_id from both tables and add 1
    -- This ensures true sequential numbering without reusing deleted IDs
    SELECT COALESCE(MAX(doctor_id), 0) + 1 INTO next_id
    FROM (
        SELECT doctor_id FROM doctors WHERE doctor_id IS NOT NULL
        UNION
        SELECT doctor_id FROM doctors_registration WHERE doctor_id IS NOT NULL
    ) AS all_ids;

    RETURN next_id;
END;
$$;

-- 2. Trigger function for sequential numbering
CREATE OR REPLACE FUNCTION set_next_sequential_doctor_id()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    IF NEW.doctor_id IS NULL THEN
        NEW.doctor_id := get_next_sequential_doctor_id();
    END IF;
    RETURN NEW;
END;
$$;

-- 3. <PERSON>reate triggers for sequential numbering
DROP TRIGGER IF EXISTS trigger_set_doctor_id_registration ON doctors_registration;
CREATE TRIGGER trigger_set_doctor_id_registration
    BEFORE INSERT ON doctors_registration
    FOR EACH ROW
    EXECUTE FUNCTION set_next_sequential_doctor_id();

DROP TRIGGER IF EXISTS trigger_set_doctor_id_doctors ON doctors;
CREATE TRIGGER trigger_set_doctor_id_doctors
    BEFORE INSERT ON doctors
    FOR EACH ROW
    EXECUTE FUNCTION set_next_sequential_doctor_id();

-- 4. Helper function for application code
CREATE OR REPLACE FUNCTION reserve_next_sequential_doctor_id()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN get_next_sequential_doctor_id();
END;
$$;
