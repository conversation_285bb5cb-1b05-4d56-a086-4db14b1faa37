import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { 
          status: 405, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse the incoming JSON body
    const { specialtyName } = await req.json()

    // Validate required parameters
    if (!specialtyName) {
      return new Response(
        JSON.stringify({ error: 'specialtyName is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Create Supabase admin client (bypasses RLS for server-side operations)
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    // Data aggregation logic
    console.log(`Starting data aggregation for specialty: ${specialtyName}`)

    // Fetch Top Doctors (top 5 by rating)
    const { data: topDoctors, error: topDoctorsError } = await supabase
      .from('doctors')
      .select('doctor_id, fullname, facility, specialty, rating, review_count, wins, losses')
      .eq('specialty', specialtyName)
      .order('rating', { ascending: false })
      .limit(5)

    if (topDoctorsError) {
      console.error('Error fetching top doctors:', topDoctorsError)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch top doctors data' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Fetch Rising Stars (doctors with good ratings and fewer losses - approximating rising trend)
    const { data: risingStars, error: risingStarsError } = await supabase
      .from('doctors')
      .select('doctor_id, fullname, facility, specialty, rating, review_count, wins, losses')
      .eq('specialty', specialtyName)
      .gte('rating', 4.0) // Good rating threshold
      .order('wins', { ascending: false })
      .order('losses', { ascending: true })
      .limit(3)

    if (risingStarsError) {
      console.error('Error fetching rising stars:', risingStarsError)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch rising stars data' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get specialty_id for reviews count
    const { data: specialtyData, error: specialtyError } = await supabase
      .from('specialties')
      .select('specialty_id')
      .eq('specialty_name', specialtyName)
      .single()

    if (specialtyError) {
      console.error('Error fetching specialty:', specialtyError)
      return new Response(
        JSON.stringify({ error: 'Specialty not found' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Count recent reviews (last 30 days) for this specialty
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    const { count: recentReviewsCount, error: reviewsCountError } = await supabase
      .from('reviews')
      .select('review_id', { count: 'exact', head: true })
      .gte('review_date', thirtyDaysAgo.toISOString().split('T')[0])
      .in('doctor_id', topDoctors?.map(d => d.doctor_id) || [])

    if (reviewsCountError) {
      console.error('Error counting recent reviews:', reviewsCountError)
    }

    // Get total doctors count for this specialty
    const { count: totalDoctorsCount, error: doctorsCountError } = await supabase
      .from('doctors')
      .select('doctor_id', { count: 'exact', head: true })
      .eq('specialty', specialtyName)

    if (doctorsCountError) {
      console.error('Error counting doctors:', doctorsCountError)
    }

    // Structure the aggregated data
    const aggregatedData = {
      specialty: specialtyName,
      specialty_id: specialtyData.specialty_id,
      topDoctors: topDoctors || [],
      risingStars: risingStars || [],
      stats: {
        totalDoctors: totalDoctorsCount || 0,
        recentReviews: recentReviewsCount || 0,
        averageRating: topDoctors?.reduce((sum, doc) => sum + (doc.rating || 0), 0) / (topDoctors?.length || 1),
        totalWins: topDoctors?.reduce((sum, doc) => sum + (doc.wins || 0), 0) || 0
      },
      generatedAt: new Date().toISOString()
    }

    console.log('Data aggregation completed successfully')

    // LLM Master Prompt Template
    const masterPrompt = `You are a medical content expert writing for "Doctors League," a professional platform that ranks and reviews medical professionals. Your task is to create an engaging, informative article about the ${specialtyName} specialty.

## Context Data:
- Specialty: ${specialtyName}
- Total Doctors: ${aggregatedData.stats.totalDoctors}
- Recent Reviews (30 days): ${aggregatedData.stats.recentReviews}
- Average Rating: ${aggregatedData.stats.averageRating.toFixed(1)}/5.0

## Top Performing Doctors:
${aggregatedData.topDoctors.map((doc, index) => 
  `${index + 1}. Dr. ${doc.fullname} at ${doc.facility} - Rating: ${doc.rating}/5 (${doc.review_count} reviews, ${doc.wins} wins)`
).join('\n')}

## Rising Stars:
${aggregatedData.risingStars.map((doc, index) => 
  `${index + 1}. Dr. ${doc.fullname} at ${doc.facility} - Rating: ${doc.rating}/5 (Strong performance: ${doc.wins} wins, ${doc.losses} losses)`
).join('\n')}

## Article Requirements:
1. **Title**: Create a compelling, SEO-friendly title (60-80 characters)
2. **Structure**: Use clear headings (H2, H3) and well-organized paragraphs
3. **Content**: 800-1200 words covering:
   - Overview of ${specialtyName} field
   - Current trends and developments
   - What patients should know when choosing a ${specialtyName} specialist
   - Factors that make a great ${specialtyName} doctor
   - Reference the top performers (without being promotional)
4. **Meta Description**: 150-160 character summary for SEO
5. **Keywords**: Suggest 5-7 relevant SEO keywords

## Output Format:
Please respond with a JSON object containing:
{
  "title": "Article title here",
  "content": "Full HTML article content with proper heading tags",
  "excerpt": "Brief 2-3 sentence summary",
  "metaDescription": "SEO meta description",
  "keywords": ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"]
}

Write in a professional, authoritative tone that builds trust. Focus on providing value to patients seeking ${specialtyName} care while subtly highlighting the excellence of top-rated doctors on the platform.`

    // Google Gemini configuration
    const REQUESTED_GEMINI_MODEL_NAME = 'gemini-2.0-flash-lite'
    const GEMINI_API_KEYS = [
      "AIzaSyBKqNyPAfJTLRquozSPJ39vyAYnDNFfkyE",
      "AIzaSyB_80pfqi3gVcw7sp3zKb6UJnRmLo_9HzE",
      "AIzaSyBIy2EpVp6rAEtZsFibarpvrigeVRnWl1I",
      "AIzaSyDSS4oeyFa34Iul0hcFrFq6g08WnJ1uS68",  
      "AIzaSyAGybugGjgFGZDFvENFOGsShlnEyBQ1oBk",
      "AIzaSyAIPkh8YF4uAOdk8RpyawH9FXEdc3CX0DA",
      "AIzaSyB2H52zO4ly4GGkhmwINoFqRvNr4OqKqBk",
      "AIzaSyAyd5Gh9hQFuMMpzGrk-SLNysvgT-Zsnec",
      "AIzaSyBQNHqAoV7ok-zh7TfL3HLfhlHShAkLtWU",
    ]

    // Select random API key for load balancing
    const selectedApiKey = GEMINI_API_KEYS[Math.floor(Math.random() * GEMINI_API_KEYS.length)]
    
    console.log(`Calling Google Gemini API with model: ${REQUESTED_GEMINI_MODEL_NAME}`)

    // Make API call to Google Gemini
    const geminiResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${REQUESTED_GEMINI_MODEL_NAME}:generateContent?key=${selectedApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: masterPrompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 2000,
          topP: 0.8,
          topK: 40
        }
      }),
    })

    let geminiData: any
    
    if (!geminiResponse.ok) {
      const errorText = await geminiResponse.text()
      console.error('Gemini API error:', errorText)
      
      // Try with a different API key if the first one fails
      if (GEMINI_API_KEYS.length > 1) {
        const backupApiKey = GEMINI_API_KEYS.find(key => key !== selectedApiKey)
        console.log('Retrying with backup API key...')
        
        const retryResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${REQUESTED_GEMINI_MODEL_NAME}:generateContent?key=${backupApiKey}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [
              {
                parts: [
                  {
                    text: masterPrompt
                  }
                ]
              }
            ],
            generationConfig: {
              temperature: 0.7,
              maxOutputTokens: 2000,
              topP: 0.8,
              topK: 40
            }
          }),
        })

        if (retryResponse.ok) {
          geminiData = await retryResponse.json()
        } else {
          return new Response(
            JSON.stringify({ error: 'Failed to generate article content with Gemini API' }),
            { 
              status: 500, 
              headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
            }
          )
        }
      } else {
        return new Response(
          JSON.stringify({ error: 'Failed to generate article content with Gemini API' }),
          { 
            status: 500, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }
    } else {
      geminiData = await geminiResponse.json()
    }
    const generatedContent = geminiData.candidates?.[0]?.content?.parts?.[0]?.text

    if (!generatedContent) {
      return new Response(
        JSON.stringify({ error: 'No content generated from Gemini API' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse the generated article content
    let articleData
    try {
      articleData = JSON.parse(generatedContent)
    } catch (parseError) {
      console.error('Error parsing OpenAI response:', parseError)
      return new Response(
        JSON.stringify({ error: 'Failed to parse generated content' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Create slug from title
    const slug = articleData.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')

    // Get a default category and author for AI-generated content
    const { data: categoryData } = await supabase
      .from('blog_categories')
      .select('id')
      .eq('name', 'Ranking Analysis & Insights')
      .single()

    const { data: authorData } = await supabase
      .from('blog_authors')
      .select('id')
      .eq('name', 'AI Content Assistant')
      .single()

    // Create default category/author if they don't exist
    let categoryId = categoryData?.id
    let authorId = authorData?.id

    if (!categoryId) {
      const { data: newCategory } = await supabase
        .from('blog_categories')
        .insert({
          name: 'AI Generated Content',
          slug: 'ai-generated-content',
          description: 'Articles generated by AI content assistant',
          is_active: true
        })
        .select('id')
        .single()
      categoryId = newCategory?.id
    }

    if (!authorId) {
      const { data: newAuthor } = await supabase
        .from('blog_authors')
        .insert({
          name: 'AI Content Assistant',
          slug: 'ai-content-assistant',
          bio: 'Automated content generation system for Doctors League',
          is_active: true
        })
        .select('id')
        .single()
      authorId = newAuthor?.id
    }

    // Generate a unique job ID for this generation
    const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Insert the new blog post into the blog_posts table
    const { data: newBlogPost, error: insertError } = await supabase
      .from('blog_posts')
      .insert({
        title: articleData.title,
        slug: slug,
        content: articleData.content,
        excerpt: articleData.excerpt,
        category_id: categoryId,
        author_id: authorId,
        status: 'draft',
        meta_title: articleData.title,
        meta_description: articleData.metaDescription,
        meta_keywords: articleData.keywords,
        generation_source: 'ai_generated',
        ai_generation_job_id: jobId,
        raw_data_json: aggregatedData
      })
      .select('id, title, slug')
      .single()

    if (insertError) {
      console.error('Error inserting blog post:', insertError)
      return new Response(
        JSON.stringify({ error: 'Failed to save article to database' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('Article generated and saved successfully')

    // Return success response with article details
    return new Response(
      JSON.stringify({ 
        success: true,
        message: 'Article generated successfully',
        article: {
          id: newBlogPost.id,
          title: newBlogPost.title,
          slug: newBlogPost.slug,
          jobId: jobId,
          specialty: specialtyName
        }
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in generate-article function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
}) 