DROP FUNCTION IF EXISTS get_specialties_by_country_with_doctor_count(p_country_id BIGINT);

CREATE OR REPLACE FUNCTION get_specialties_by_country_with_doctor_count(p_country_id BIGINT)
RETURNS TABLE(
  specialty_id INTEGER,
  specialty_name TEXT,
  doctor_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    s.specialty_id,
    s.specialty_name,
    COUNT(d.doctor_id) AS doctor_count
  FROM
    public.specialties s
  LEFT JOIN
    public.doctors d ON s.specialty_id = d.specialty_id AND d.country_id = p_country_id
  GROUP BY
    s.specialty_id, s.specialty_name
  ORDER BY
    s.specialty_name;
END;
$$ LANGUAGE plpgsql;
