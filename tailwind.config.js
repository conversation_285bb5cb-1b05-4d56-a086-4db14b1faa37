/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    // Define responsive breakpoints for three-tier system
    screens: {
      'sm': '640px',   // Small devices (landscape phones)
      'md': '768px',   // Tablets (portrait) - Start of tablet breakpoint
      'lg': '1024px',  // Tablets (landscape) and small laptops - Start of desktop breakpoint
      'xl': '1280px',  // Large laptops and desktops
      '2xl': '1536px', // Extra large screens
      // Custom breakpoint for laptop-specific adjustments
      'laptop': '1366px', // 15-inch laptops and similar
    },
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      // Responsive spacing utilities for three-tier system
      spacing: {
        'mobile': '1rem',    // 16px - Mobile spacing
        'tablet': '1.5rem',  // 24px - Tablet spacing
        'desktop': '2rem',   // 32px - Desktop spacing
      },
      fontFamily: {
        sans: ["var(--font-inter)", "sans-serif"],
        heading: ["var(--font-oswald)", "sans-serif"],
        mono: ["monospace"],
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
          50: "hsl(210, 100%, 95%)",
          100: "hsl(210, 100%, 90%)",
          200: "hsl(210, 100%, 80%)",
          300: "hsl(210, 100%, 70%)",
          400: "hsl(210, 100%, 60%)",
          500: "hsl(210, 100%, 50%)",
          600: "hsl(210, 100%, 40%)",
          700: "hsl(210, 100%, 30%)",
          800: "hsl(210, 100%, 20%)",
          900: "hsl(210, 100%, 10%)",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
          50: "hsl(200, 60%, 95%)",
          100: "hsl(200, 60%, 90%)",
          200: "hsl(200, 60%, 80%)",
          300: "hsl(200, 60%, 70%)",
          400: "hsl(200, 60%, 60%)",
          500: "hsl(200, 60%, 50%)",
          600: "hsl(200, 60%, 40%)",
          700: "hsl(200, 60%, 30%)",
          800: "hsl(200, 60%, 20%)",
          900: "hsl(200, 60%, 10%)",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
          50: "hsl(35, 100%, 95%)",
          100: "hsl(35, 100%, 90%)",
          200: "hsl(35, 100%, 80%)",
          300: "hsl(35, 100%, 70%)",
          400: "hsl(35, 100%, 60%)",
          500: "hsl(35, 100%, 50%)",
          600: "hsl(35, 100%, 40%)",
          700: "hsl(35, 100%, 30%)",
          800: "hsl(35, 100%, 20%)",
          900: "hsl(35, 100%, 10%)",
        },
        destructive: {
          DEFAULT: "hsl(var(--error))",
          foreground: "hsl(var(--error-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        success: {
          DEFAULT: "hsl(var(--success))",
          foreground: "hsl(var(--success-foreground))",
        },
        warning: {
          DEFAULT: "hsl(var(--warning))",
          foreground: "hsl(var(--warning-foreground))",
        },
        error: {
          DEFAULT: "hsl(var(--error))",
          foreground: "hsl(var(--error-foreground))",
        },
        info: {
          DEFAULT: "hsl(var(--info))",
          foreground: "hsl(var(--info-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        // New dropdown colors
        dropdown: {
          highlight: "rgba(34, 197, 94, 0.1)",
          text: "rgba(34, 197, 94, 0.8)",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        slideUp: {
          "0%": { transform: "translateY(20px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        pulse: {
          "0%, 100%": { opacity: "0.5", transform: "scale(0.8)" },
          "50%": { opacity: "1", transform: "scale(1.2)" },
        },
        scoreboard: {
          "0%": { opacity: "0.8" },
          "50%": { opacity: "1" },
          "100%": { opacity: "0.8" },
        },
        highlight: {
          "0%": { backgroundColor: "rgba(var(--accent), 0.2)" },
          "100%": { backgroundColor: "transparent" },
        },
        "success-pulse": {
          "0%, 100%": { boxShadow: "0 0 0 0 rgba(var(--success), 0.4)" },
          "50%": { boxShadow: "0 0 0 10px rgba(var(--success), 0)" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        fadeIn: "fadeIn 0.5s ease-out",
        slideUp: "slideUp 0.5s ease-out",
        pulse: "pulse 1.5s infinite",
        scoreboard: "scoreboard 2s infinite",
        highlight: "highlight 2s ease-out",
        "success-pulse": "success-pulse 2s infinite",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
