-- COMPREHENSIVE TEST: Bulletproof Sequential Numbering
-- Run this after installing the bulletproof solution

-- STEP 1: Check current state
SELECT 'BEFORE TEST - Current State:' as status;

SELECT 
    'doctors' as table_name,
    MAX(doctor_id) as current_max,
    COUNT(*) as total_records
FROM doctors
UNION ALL
SELECT 
    'doctors_registration' as table_name,
    MAX(doctor_id) as current_max,
    COUNT(*) as total_records
FROM doctors_registration;

-- STEP 2: Test function consistency
SELECT 'Function Test Results:' as status;
SELECT 
    'Test 1' as test,
    get_bulletproof_sequential_doctor_id() as result
UNION ALL
SELECT 
    'Test 2' as test,
    get_bulletproof_sequential_doctor_id() as result
UNION ALL
SELECT 
    'Test 3' as test,
    get_bulletproof_sequential_doctor_id() as result;

-- STEP 3: Test actual insertion into doctors_registration
INSERT INTO doctors_registration (
    fullname,
    email,
    status
) VALUES (
    'Test Bulletproof Doctor 1',
    '<EMAIL>',
    'pending'
);

-- Check what ID was assigned
SELECT 'First Insertion Result:' as status;
SELECT doctor_id, fullname, email 
FROM doctors_registration 
WHERE fullname = 'Test Bulletproof Doctor 1';

-- STEP 4: Test second insertion
INSERT INTO doctors_registration (
    fullname,
    email,
    status
) VALUES (
    'Test Bulletproof Doctor 2',
    '<EMAIL>',
    'pending'
);

-- Check what ID was assigned
SELECT 'Second Insertion Result:' as status;
SELECT doctor_id, fullname, email 
FROM doctors_registration 
WHERE fullname = 'Test Bulletproof Doctor 2';

-- STEP 5: Test third insertion
INSERT INTO doctors_registration (
    fullname,
    email,
    status
) VALUES (
    'Test Bulletproof Doctor 3',
    '<EMAIL>',
    'pending'
);

-- Check what ID was assigned
SELECT 'Third Insertion Result:' as status;
SELECT doctor_id, fullname, email 
FROM doctors_registration 
WHERE fullname = 'Test Bulletproof Doctor 3';

-- STEP 6: Verify sequential numbering
SELECT 'Sequential Verification:' as status;
WITH test_records AS (
    SELECT doctor_id, fullname
    FROM doctors_registration 
    WHERE fullname LIKE 'Test Bulletproof Doctor%'
    ORDER BY doctor_id
),
expected_sequence AS (
    SELECT 
        doctor_id,
        fullname,
        LAG(doctor_id) OVER (ORDER BY doctor_id) as prev_id,
        doctor_id - LAG(doctor_id) OVER (ORDER BY doctor_id) as gap
    FROM test_records
)
SELECT 
    doctor_id,
    fullname,
    prev_id,
    gap,
    CASE 
        WHEN gap = 1 OR gap IS NULL THEN 'SEQUENTIAL ✓'
        ELSE 'GAP DETECTED ✗'
    END as sequence_status
FROM expected_sequence;

-- STEP 7: Test deletion and re-insertion
DELETE FROM doctors_registration WHERE fullname = 'Test Bulletproof Doctor 2';

-- Insert a new record after deletion
INSERT INTO doctors_registration (
    fullname,
    email,
    status
) VALUES (
    'Test After Deletion',
    '<EMAIL>',
    'pending'
);

-- Check if it continues sequentially (should NOT reuse deleted ID)
SELECT 'After Deletion Test:' as status;
SELECT doctor_id, fullname 
FROM doctors_registration 
WHERE fullname IN ('Test Bulletproof Doctor 1', 'Test Bulletproof Doctor 3', 'Test After Deletion')
ORDER BY doctor_id;

-- STEP 8: Final verification
SELECT 'FINAL VERIFICATION:' as status;
WITH all_test_records AS (
    SELECT doctor_id
    FROM doctors_registration 
    WHERE fullname LIKE 'Test%' OR fullname = 'Test After Deletion'
    ORDER BY doctor_id
)
SELECT 
    COUNT(*) as total_test_records,
    MIN(doctor_id) as min_id,
    MAX(doctor_id) as max_id,
    CASE 
        WHEN MAX(doctor_id) - MIN(doctor_id) + 1 = COUNT(*) + 1 THEN 'PERFECT SEQUENTIAL (with 1 deletion) ✓'
        ELSE 'SEQUENCE ISSUE ✗'
    END as final_result
FROM all_test_records;

-- STEP 9: Clean up test records
DELETE FROM doctors_registration WHERE fullname LIKE 'Test%' OR fullname = 'Test After Deletion';

SELECT 'Test completed and cleaned up!' as final_status;
