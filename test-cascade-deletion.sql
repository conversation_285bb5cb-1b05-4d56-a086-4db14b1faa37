-- TEST SCRIPT: Verify cascade deletion works correctly
-- Run these commands step by step to test the cascade deletion functionality

-- STEP 1: Create test records
-- Insert a test doctor
INSERT INTO doctors (
    "Fullname",
    email,
    facility,
    medical_title,
    specialty,
    experience
) VALUES (
    'Dr. Test Cascade Delete',
    '<EMAIL>',
    'Test Hospital',
    'MD',
    'Cardiology',
    5
);

-- Insert a test user
INSERT INTO users (
    username,
    email,
    first_name,
    last_name,
    user_type
) VALUES (
    'testcascadeuser',
    '<EMAIL>',
    'Test',
    'User',
    'patient'
);

-- Insert corresponding auth_credentials records
INSERT INTO auth_credentials (
    email,
    hashed_password,
    user_profile_id,
    user_type
) VALUES 
(
    '<EMAIL>',
    'hashed_password_doctor',
    (SELECT doctor_id FROM doctors WHERE email = '<EMAIL>'),
    'doctor'
),
(
    '<EMAIL>',
    'hashed_password_user',
    (SELECT user_id FROM users WHERE email = '<EMAIL>'),
    'patient'
);

-- STEP 2: Verify test records were created
SELECT 'BEFORE DELETION - Records created:' as status;

SELECT 'Doctor:' as type, doctor_id, "Fullname", email 
FROM doctors 
WHERE email = '<EMAIL>';

SELECT 'User:' as type, user_id, username, email 
FROM users 
WHERE email = '<EMAIL>';

SELECT 'Auth Credentials:' as type, id, email, user_type 
FROM auth_credentials 
WHERE email IN ('<EMAIL>', '<EMAIL>');

-- STEP 3: Test doctor deletion (should cascade to auth_credentials)
DELETE FROM doctors WHERE email = '<EMAIL>';

-- STEP 4: Verify doctor's auth_credentials was deleted
SELECT 'AFTER DOCTOR DELETION:' as status;

SELECT 'Remaining Auth Credentials:' as type, id, email, user_type 
FROM auth_credentials 
WHERE email IN ('<EMAIL>', '<EMAIL>');

-- STEP 5: Test user deletion (should cascade to auth_credentials)
DELETE FROM users WHERE email = '<EMAIL>';

-- STEP 6: Verify user's auth_credentials was deleted
SELECT 'AFTER USER DELETION:' as status;

SELECT 'Remaining Auth Credentials:' as type, id, email, user_type 
FROM auth_credentials 
WHERE email IN ('<EMAIL>', '<EMAIL>');

-- STEP 7: Final verification (should show no records)
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'SUCCESS: All auth_credentials were properly deleted!'
        ELSE 'FAILURE: Some auth_credentials were not deleted'
    END as test_result
FROM auth_credentials 
WHERE email IN ('<EMAIL>', '<EMAIL>');

-- Clean up any remaining test data (just in case)
DELETE FROM auth_credentials WHERE email IN ('<EMAIL>', '<EMAIL>');
DELETE FROM doctors WHERE email = '<EMAIL>';
DELETE FROM users WHERE email = '<EMAIL>';

SELECT 'Test completed and cleaned up!' as final_status;
