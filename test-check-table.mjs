// Test script to check if password_reset_tokens table exists
import fetch from 'node-fetch';

async function main() {
  console.log('Checking if password_reset_tokens table exists...');
  
  try {
    const response = await fetch('http://localhost:3000/api/check-reset-table');
    const result = await response.json();
    
    console.log('API Response Status:', response.status);
    console.log('API Response:', result);
    
    if (result.tableExists) {
      console.log('✅ Table exists and is accessible!');
    } else {
      console.error('❌ Table does not exist or is not accessible.');
      if (result.error) {
        console.error('Error:', result.error);
      }
    }
  } catch (error) {
    console.error('Error checking table:', error);
  }
}

main().catch(console.error); 