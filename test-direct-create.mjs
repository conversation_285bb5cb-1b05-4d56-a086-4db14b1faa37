// Test script for direct table creation
import fetch from 'node-fetch';

async function main() {
  console.log('Testing direct table creation...');
  
  try {
    const response = await fetch('http://localhost:3000/api/direct-create-table', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({})
    });
    
    const result = await response.json();
    
    console.log('API Response Status:', response.status);
    console.log('API Response:', result);
    
    if (response.ok) {
      console.log('Table creation successful!');
    } else {
      console.error('Table creation failed:', result.error || 'Unknown error');
      if (result.details) {
        console.error('Details:', result.details);
      }
    }
  } catch (error) {
    console.error('Error creating table:', error);
  }
}

main().catch(console.error); 