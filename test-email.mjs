// Test script for Mailtrap email sending
import nodemailer from 'nodemailer';

// Mailtrap configuration with TLS options
const transporter = nodemailer.createTransport({
  host: 'sandbox.smtp.mailtrap.io',
  port: 587,
  secure: false,
  auth: {
    user: 'f5849f3bfce859',
    pass: '971bf6348490c1'
  },
  tls: {
    // Do not fail on invalid certs
    rejectUnauthorized: false
  }
});

async function main() {
  console.log('Starting email test...');
  
  try {
    console.log('Sending test email via Mailtrap...');
    
    // Email content
    const mailOptions = {
      from: '"Doctors League Test" <<EMAIL>>',
      to: '<EMAIL>', // This can be any email since it's captured by Mailtrap
      subject: 'Test Email - Mailtrap Connection',
      text: 'This is a test email to verify Mailtrap connectivity.',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2>Mailtrap Test</h2>
          <p>This is a test email to verify that Mailtrap is properly configured.</p>
          <p>If you're seeing this in your Mailtrap inbox, the connection is working correctly!</p>
        </div>
      `
    };
    
    // Send mail
    const info = await transporter.sendMail(mailOptions);
    
    console.log('Email sent successfully!');
    console.log('Message ID:', info.messageId);
    
  } catch (error) {
    console.error('Error sending email:', error);
  }
}

main().catch(console.error); 