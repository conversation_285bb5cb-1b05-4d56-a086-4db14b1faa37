// Environment Variable Diagnostic Tool
// Run with: node test-env-vars.js
// This will help diagnose issues with Supabase environment variables

const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

console.log('========================================');
console.log('ENVIRONMENT VARIABLE DIAGNOSTIC REPORT');
console.log('========================================\n');

// Check for .env files
console.log('CHECKING FOR ENV FILES:');
const envFiles = ['.env', '.env.local', '.env.development', '.env.production'];
envFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} exists`);
    
    // Read file content
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    // Count variables and check for specific ones
    let hasSupabaseUrl = false;
    let hasSupabaseAnonKey = false;
    let hasServiceRoleKey = false;
    let hasPublicServiceRole = false;
    
    lines.forEach(line => {
      if (line.trim() && !line.startsWith('#')) {
        if (line.startsWith('NEXT_PUBLIC_SUPABASE_URL=')) hasSupabaseUrl = true;
        if (line.startsWith('NEXT_PUBLIC_SUPABASE_ANON_KEY=')) hasSupabaseAnonKey = true;
        if (line.startsWith('SUPABASE_SERVICE_ROLE_KEY=')) hasServiceRoleKey = true;
        if (line.startsWith('NEXT_PUBLIC_service_role=')) hasPublicServiceRole = true;
      }
    });
    
    console.log(`  - Contains ${lines.filter(l => l.trim() && !l.startsWith('#')).length} variables`);
    console.log(`  - NEXT_PUBLIC_SUPABASE_URL: ${hasSupabaseUrl ? '✅' : '❌'}`);
    console.log(`  - NEXT_PUBLIC_SUPABASE_ANON_KEY: ${hasSupabaseAnonKey ? '✅' : '❌'}`);
    console.log(`  - SUPABASE_SERVICE_ROLE_KEY: ${hasServiceRoleKey ? '✅' : '❌'}`);
    console.log(`  - NEXT_PUBLIC_service_role: ${hasPublicServiceRole ? '✅' : '❌'}`);
  } else {
    console.log(`❌ ${file} does not exist`);
  }
  console.log('');
});

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env.local') });

// Check loaded variables
console.log('CHECKING LOADED VARIABLES:');
const variables = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
  'NEXT_PUBLIC_service_role'
];

variables.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName} is set (length: ${value.length})`);
    // Show first and last few characters for verification
    const firstChars = value.substring(0, 10);
    const lastChars = value.substring(value.length - 10);
    console.log(`  Value: ${firstChars}...${lastChars}`);
  } else {
    console.log(`❌ ${varName} is not set`);
  }
});

console.log('\n========================================');
console.log('RECOMMENDATIONS:');
console.log('========================================');

// Make recommendations based on findings
if (process.env.NEXT_PUBLIC_service_role) {
  console.log('⚠️ WARNING: NEXT_PUBLIC_service_role is set, but should not be used for security reasons.');
  console.log('  Service role keys should NEVER be exposed to the client.');
  console.log('  Remove this from .env.local and use SUPABASE_SERVICE_ROLE_KEY instead.');
}

if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.log('❌ CRITICAL: SUPABASE_SERVICE_ROLE_KEY is not set.');
  console.log('  This is required for server-side operations that bypass RLS.');
  if (process.env.NEXT_PUBLIC_service_role) {
    console.log('  Copy the value from NEXT_PUBLIC_service_role to SUPABASE_SERVICE_ROLE_KEY.');
  }
}

// Ensure all files use the correct environment variable
console.log('\nFiles to check for environment variable references:');
console.log('1. actions/review-actions.ts');
console.log('2. actions/doctor-registration-actions.ts');
console.log('3. actions/patient-registration-actions.ts');
console.log('4. components/registration/doctor-registration-dialog.tsx');
console.log('5. app/doctor/profile/edit/page.tsx');

console.log('\nReplace all instances of process.env.NEXT_PUBLIC_service_role');
console.log('with process.env.SUPABASE_SERVICE_ROLE_KEY'); 