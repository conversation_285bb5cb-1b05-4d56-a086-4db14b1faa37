// Test script for forgot-password API
import fetch from 'node-fetch';

async function main() {
  const testEmail = '<EMAIL>'; // Replace with an email in your auth_credentials table
  
  console.log(`Testing forgot-password API with email: ${testEmail}`);
  
  try {
    const response = await fetch('http://localhost:3000/api/auth/custom/forgot-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail
      }),
    });
    
    const result = await response.json();
    
    console.log('API Response Status:', response.status);
    console.log('API Response:', result);
    
    if (response.ok) {
      console.log(`Request successful! Check your Mailtrap inbox for the reset email sent to ${testEmail}.`);
      console.log('Mailtrap Inbox URL: https://mailtrap.io/inboxes');
    } else {
      console.error('Request failed:', result.error || 'Unknown error');
      if (result.details) {
        console.error('Error details:', result.details);
      }
    }
  } catch (error) {
    console.error('Error testing forgot-password API:', error);
  }
}

main().catch(console.error); 