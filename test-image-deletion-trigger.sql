-- TEST SCRIPT: Verify Image Deletion Trigger Works
-- Run this after setting up the image deletion trigger

-- STEP 1: Create a test doctor record with an image
INSERT INTO doctors (
    "Fullname",
    email,
    profile_image,
    facility,
    medical_title,
    specialty,
    experience
) VALUES (
    'Dr. Test Image Delete',
    '<EMAIL>',
    'test-folder/test-doctor-image.jpg', -- Test image path
    'Test Hospital',
    'MD',
    'Cardiology',
    5
);

-- STEP 2: Verify the test record was created
SELECT 'Test doctor created:' as status;
SELECT doctor_id, "Fullname", email, profile_image 
FROM doctors 
WHERE "Fullname" = 'Dr. Test Image Delete';

-- STEP 3: Check current triggers on doctors table
SELECT 'Current DELETE triggers on doctors table:' as status;
SELECT 
    trigger_name,
    action_timing,
    action_order,
    action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'doctors' 
AND event_manipulation = 'DELETE'
ORDER BY action_order;

-- STEP 4: Test the deletion (this should trigger image deletion)
-- Note: Watch the logs/notices for deletion messages
DELETE FROM doctors WHERE "Fullname" = 'Dr. Test Image Delete';

-- STEP 5: Verify the record was deleted
SELECT 'Verification - should show no results:' as status;
SELECT doctor_id, "Fullname", email, profile_image 
FROM doctors 
WHERE "Fullname" = 'Dr. Test Image Delete';

-- STEP 6: Test with NULL image path
INSERT INTO doctors (
    "Fullname",
    email,
    profile_image, -- This will be NULL
    facility,
    medical_title,
    specialty,
    experience
) VALUES (
    'Dr. Test No Image',
    '<EMAIL>',
    NULL, -- NULL image path
    'Test Hospital',
    'MD',
    'Cardiology',
    5
);

-- Delete record with NULL image (should handle gracefully)
DELETE FROM doctors WHERE "Fullname" = 'Dr. Test No Image';

-- STEP 7: Test with empty string image path
INSERT INTO doctors (
    "Fullname",
    email,
    profile_image,
    facility,
    medical_title,
    specialty,
    experience
) VALUES (
    'Dr. Test Empty Image',
    '<EMAIL>',
    '', -- Empty string image path
    'Test Hospital',
    'MD',
    'Cardiology',
    5
);

-- Delete record with empty image path (should handle gracefully)
DELETE FROM doctors WHERE "Fullname" = 'Dr. Test Empty Image';

-- STEP 8: Test with full URL image path
INSERT INTO doctors (
    "Fullname",
    email,
    profile_image,
    facility,
    medical_title,
    specialty,
    experience
) VALUES (
    'Dr. Test Full URL',
    '<EMAIL>',
    'https://your-project.supabase.co/storage/v1/object/public/doctor-images/profiles/doctor-123.jpg',
    'Test Hospital',
    'MD',
    'Cardiology',
    5
);

-- Delete record with full URL (should extract path correctly)
DELETE FROM doctors WHERE "Fullname" = 'Dr. Test Full URL';

-- STEP 9: Final cleanup and status
SELECT 'Image deletion trigger test completed!' as final_status;
SELECT 'Check the PostgreSQL logs for deletion notices and any errors' as note;

-- Clean up any remaining test records
DELETE FROM doctors WHERE "Fullname" LIKE 'Dr. Test%';
