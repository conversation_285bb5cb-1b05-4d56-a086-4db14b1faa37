/**
 * Test script to specifically test the updateDoctorRegistrationImage function
 * This will help us identify if the issue is in the database update logic
 */

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Import the function we want to test
async function updateDoctorRegistrationImage(userId, imagePath) {
  console.log("=== updateDoctorRegistrationImage START ===");
  console.log("User ID:", userId);
  console.log("Image path:", imagePath);

  try {
    // First, let's check if the registration record exists
    console.log("Checking if registration record exists...");
    const { data: existingRecord, error: checkError } = await supabaseAdmin
      .from('doctors_registration')
      .select('doctor_id, auth_id, fullname, profile_image')
      .eq('auth_id', userId)
      .single();

    if (checkError) {
      console.error("Error checking existing registration record:", checkError);
      return { success: false, error: { message: "Registration record not found", details: checkError } };
    }

    console.log("Found registration record:", existingRecord);
    console.log("Current profile_image value:", existingRecord.profile_image);

    // Use type assertion to tell TypeScript this is a valid update
    const updateData = { profile_image: imagePath };
    console.log("Update data:", updateData);

    // First try to update by auth_id (UUID)
    console.log("Attempting to update by auth_id...");
    const { data, error } = await supabaseAdmin
      .from('doctors_registration')
      .update(updateData)
      .eq('auth_id', userId)
      .select();

    if (error) {
      console.error("Error updating doctor registration image by auth_id:", error);
      return { success: false, error: { message: "Failed to update registration image in database", details: error } };
    }

    console.log("Successfully updated doctor registration image by auth_id");
    console.log("Updated record:", data);
    console.log("=== updateDoctorRegistrationImage SUCCESS ===");
    return { success: true, error: null };
  } catch (e) {
    console.error("=== updateDoctorRegistrationImage EXCEPTION ===");
    console.error("Exception details:", e);
    return { success: false, error: { message: e instanceof Error ? e.message : "An unexpected error occurred.", details: e } };
  }
}

async function testImageUpdate() {
  console.log('=== TESTING IMAGE UPDATE FUNCTION ===\n');

  try {
    // Step 1: Find a recent registration without an image
    console.log('1. Finding a recent registration without an image...');
    const { data: recentReg, error: fetchError } = await supabaseAdmin
      .from('doctors_registration')
      .select('doctor_id, auth_id, fullname, email, profile_image')
      .is('profile_image', null)
      .order('doctor_id', { ascending: false })
      .limit(1)
      .single();

    if (fetchError || !recentReg) {
      console.error('No registration found without image:', fetchError);
      return;
    }

    console.log('Found registration to test:');
    console.log(`  - ID: ${recentReg.doctor_id}`);
    console.log(`  - Auth ID: ${recentReg.auth_id}`);
    console.log(`  - Name: ${recentReg.fullname}`);
    console.log(`  - Email: ${recentReg.email}`);
    console.log(`  - Current Image: ${recentReg.profile_image || 'NULL'}`);

    // Step 2: Test updating with a fake image path
    console.log('\n2. Testing image path update...');
    const testImagePath = `real_photos/${recentReg.auth_id}-${Date.now()}.webp`;
    console.log(`Test image path: ${testImagePath}`);

    const updateResult = await updateDoctorRegistrationImage(recentReg.auth_id, testImagePath);

    if (updateResult.success) {
      console.log('\n✅ UPDATE SUCCESSFUL!');
      
      // Step 3: Verify the update
      console.log('\n3. Verifying the update...');
      const { data: verifyData, error: verifyError } = await supabaseAdmin
        .from('doctors_registration')
        .select('profile_image')
        .eq('auth_id', recentReg.auth_id)
        .single();

      if (verifyError) {
        console.error('Error verifying update:', verifyError);
      } else {
        console.log('Verification result:');
        console.log(`  - New profile_image value: ${verifyData.profile_image}`);
        if (verifyData.profile_image === testImagePath) {
          console.log('✅ SUCCESS: Image path was saved correctly!');
        } else {
          console.log('❌ FAILURE: Image path was not saved correctly!');
        }
      }

      // Step 4: Clean up - reset to NULL
      console.log('\n4. Cleaning up (resetting to NULL)...');
      const { error: cleanupError } = await supabaseAdmin
        .from('doctors_registration')
        .update({ profile_image: null })
        .eq('auth_id', recentReg.auth_id);

      if (cleanupError) {
        console.error('Error cleaning up:', cleanupError);
      } else {
        console.log('✅ Cleanup successful');
      }

    } else {
      console.log('\n❌ UPDATE FAILED!');
      console.error('Update error:', updateResult.error);
    }

    console.log('\n=== TEST COMPLETE ===');

  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testImageUpdate().catch(error => {
  console.error('Unhandled error in test:', error);
  process.exit(1);
});
