// Test script for Mailtrap email sending
const nodemailer = require('nodemailer');

// Mailtrap configuration
const transporter = nodemailer.createTransport({
  host: 'sandbox.smtp.mailtrap.io',
  port: 2525,
  secure: false,
  auth: {
    user: 'f5849f3bfce859',
    pass: '971bf6348490c1'
  }
});

async function sendTestEmail() {
  console.log('Sending test email via Mailtrap...');
  
  try {
    const info = await transporter.sendMail({
      from: '"Doctor Leagues Test" <<EMAIL>>',
      to: '<EMAIL>', // This can be any email since it's captured by Mailtrap
      subject: 'Test Email - Mailtrap Connection',
      text: 'This is a test email to verify Mailtrap connectivity.',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Mailtrap Test</h2>
          <p>This is a test email to verify that Mailtra<PERSON> is properly configured.</p>
          <p>If you're seeing this in your Mailtrap inbox, the connection is working correctly!</p>
          <p>Timestamp: ${new Date().toISOString()}</p>
        </div>
      `
    });
    
    console.log('Test email sent successfully!');
    console.log('Message ID:', info.messageId);
    console.log('Preview URL:', nodemailer.getTestMessageUrl(info));
  } catch (error) {
    console.error('Error sending test email:', error);
  }
}

sendTestEmail(); 