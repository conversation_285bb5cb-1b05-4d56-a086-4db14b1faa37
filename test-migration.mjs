// Test script for running the migration to create password_reset_tokens table
import fetch from 'node-fetch';

async function main() {
  console.log('Running password_reset_tokens table migration...');
  
  try {
    const response = await fetch('http://localhost:3000/api/create-reset-table', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        secret: 'your-secret-migration-key'
      }),
    });
    
    const result = await response.json();
    
    console.log('API Response Status:', response.status);
    console.log('API Response:', result);
    
    if (response.ok) {
      console.log('Migration completed successfully!');
    } else {
      console.error('Migration failed:', result.error || 'Unknown error');
      if (result.details) {
        console.error('Details:', result.details);
      }
    }
  } catch (error) {
    console.error('Error running migration:', error);
  }
}

main().catch(console.error); 