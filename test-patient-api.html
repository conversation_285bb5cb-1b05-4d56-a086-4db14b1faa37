<!DOCTYPE html>
<html>
<head>
    <title>Test Patient Registration API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        pre { background-color: #f8f9fa; padding: 10px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Patient Registration API Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Database Connection & Structure</h3>
        <button onclick="testConnection()">Test Connection</button>
        <div id="connectionResult"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 2: Full Patient Registration</h3>
        <button onclick="testFullRegistration()">Test Full Registration</button>
        <div id="registrationResult"></div>
    </div>

    <script>
        async function testConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<p>Testing connection...</p>';
            
            try {
                const response = await fetch('/api/test-patient-registration', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ test: 'connection' })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'test-section success';
                    resultDiv.innerHTML = `
                        <h4>✅ Connection Test Successful</h4>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-section error';
                    resultDiv.innerHTML = `
                        <h4>❌ Connection Test Failed</h4>
                        <p>Status: ${response.status}</p>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `
                    <h4>❌ Connection Test Error</h4>
                    <pre>${error.message}</pre>
                `;
            }
        }
        
        async function testFullRegistration() {
            const resultDiv = document.getElementById('registrationResult');
            resultDiv.innerHTML = '<p>Testing full registration...</p>';
            
            const testData = {
                email: `test.patient.${Date.now()}@example.com`,
                password: 'testPassword123',
                userType: 'patient',
                profileData: {
                    username: `testpatient${Date.now()}`,
                    firstName: 'Test',
                    lastName: 'Patient',
                    gender: 'Other',
                    age: 25,
                    city: 'Test City',
                    country: 'Test Country',
                    medical_condition: 'No conditions',
                    phone_number: '**********',
                    state_province_region: 'Test State'
                }
            };
            
            try {
                const response = await fetch('/api/auth/custom/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'test-section success';
                    resultDiv.innerHTML = `
                        <h4>✅ Registration Test Successful</h4>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-section error';
                    resultDiv.innerHTML = `
                        <h4>❌ Registration Test Failed</h4>
                        <p>Status: ${response.status}</p>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `
                    <h4>❌ Registration Test Error</h4>
                    <pre>${error.message}</pre>
                `;
            }
        }
    </script>
</body>
</html>
