-- TEST SCRIPT: Verify Patient Registration Column Names
-- Run this to check the actual column names in the users table

-- STEP 1: Check the actual structure of the users table
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- STEP 2: Check if there are any existing patient records to see the format
SELECT 'Sample patient records:' as info;
SELECT 
    user_id,
    username,
    email,
    first_name,
    last_name,
    gender,
    city,
    country,
    user_type,
    age,
    auth_id,
    "Medical Condition",
    "Registration date",
    "Phone_Number",
    "State"
FROM users 
WHERE user_type = 'patient' 
LIMIT 3;

-- STEP 3: Test insert with correct column names
INSERT INTO users (
    username,
    email,
    first_name,
    last_name,
    gender,
    city,
    country,
    user_type,
    age,
    "Medical Condition",
    "Phone_Number",
    "State",
    "Registration date",
    auth_id
) VALUES (
    'test_patient_fix',
    '<EMAIL>',
    'Test',
    'Patient',
    'Other',
    'Test City',
    'Test Country',
    'patient',
    25,
    'No conditions',
    '**********',
    'Test State',
    CURRENT_DATE,
    gen_random_uuid()
);

-- STEP 4: Verify the test record was inserted
SELECT 'Test record verification:' as info;
SELECT 
    user_id,
    username,
    email,
    first_name,
    last_name,
    "Medical Condition",
    "Registration date",
    "Phone_Number",
    "State"
FROM users 
WHERE username = 'test_patient_fix';

-- STEP 5: Clean up test record
DELETE FROM users WHERE username = 'test_patient_fix';

SELECT 'Column name test completed!' as status;
