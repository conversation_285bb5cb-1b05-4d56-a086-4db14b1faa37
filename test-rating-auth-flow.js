/**
 * Test script to verify the rating authentication flow fix
 * This script tests the redirect functionality after patient login
 */

const puppeteer = require('puppeteer');

async function testRatingAuthFlow() {
  console.log('🚀 Starting Rating Authentication Flow Test');
  console.log('============================================\n');

  let browser;
  let page;

  try {
    // Launch browser
    browser = await puppeteer.launch({ 
      headless: false, // Set to true for headless mode
      defaultViewport: null,
      args: ['--start-maximized']
    });
    
    page = await browser.newPage();
    
    // Navigate to the application
    console.log('📍 Navigating to application homepage...');
    await page.goto('http://localhost:3001', { waitUntil: 'networkidle0' });
    
    // Look for a doctor profile link or navigate to a specific doctor
    console.log('🔍 Looking for doctor profiles...');
    
    // Try to find a doctor link - this might need adjustment based on your homepage structure
    const doctorLinks = await page.$$eval('a[href*="/doctors/"]', links => 
      links.map(link => link.href).filter(href => href.match(/\/doctors\/\d+$/))
    );
    
    if (doctorLinks.length === 0) {
      console.log('⚠️  No doctor profile links found on homepage. Trying direct navigation...');
      // Try navigating to a known doctor ID (you might need to adjust this)
      await page.goto('http://localhost:3001/doctors/1', { waitUntil: 'networkidle0' });
    } else {
      console.log(`✅ Found ${doctorLinks.length} doctor profile links. Navigating to first one...`);
      await page.goto(doctorLinks[0], { waitUntil: 'networkidle0' });
    }
    
    // Look for the "Rate This Doctor" button
    console.log('🎯 Looking for "Rate This Doctor" button...');
    
    const rateButton = await page.$('button:has-text("Rate"), button:has-text("Rate This Doctor"), button:has-text("Rate & Review")');
    
    if (!rateButton) {
      console.log('❌ Could not find "Rate This Doctor" button');
      return false;
    }
    
    console.log('✅ Found "Rate This Doctor" button. Clicking...');
    
    // Click the rate button
    await rateButton.click();
    await page.waitForTimeout(2000); // Wait for navigation/modal
    
    // Check if we're redirected to authentication page
    const currentUrl = page.url();
    console.log(`📍 Current URL after clicking rate button: ${currentUrl}`);
    
    if (currentUrl.includes('/patient/login')) {
      console.log('✅ Correctly redirected to patient login page');
      
      // Check if redirectTo parameter is present
      const url = new URL(currentUrl);
      const redirectTo = url.searchParams.get('redirectTo');
      
      if (redirectTo) {
        console.log(`✅ redirectTo parameter found: ${redirectTo}`);
        
        // Simulate login (you'll need to adjust credentials)
        console.log('🔐 Attempting to login...');
        
        // Fill in login form
        await page.type('input[type="email"]', '<EMAIL>');
        await page.type('input[type="password"]', 'TestPassword123!');
        
        // Click login button
        const loginButton = await page.$('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")');
        if (loginButton) {
          await loginButton.click();
          await page.waitForTimeout(3000); // Wait for login process
          
          // Check if we're redirected back to the rating page
          const finalUrl = page.url();
          console.log(`📍 Final URL after login: ${finalUrl}`);
          
          if (finalUrl.includes('/rate') || finalUrl === redirectTo) {
            console.log('✅ SUCCESS: Correctly redirected to rating page after login!');
            return true;
          } else {
            console.log('❌ FAILED: Not redirected to rating page after login');
            return false;
          }
        } else {
          console.log('❌ Could not find login button');
          return false;
        }
      } else {
        console.log('❌ FAILED: redirectTo parameter not found in URL');
        return false;
      }
    } else if (currentUrl.includes('/rate')) {
      console.log('✅ Already authenticated - directly navigated to rating page');
      return true;
    } else {
      console.log('❌ FAILED: Unexpected redirect behavior');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    return false;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test
async function main() {
  const success = await testRatingAuthFlow();
  
  console.log('\n📊 TEST RESULT');
  console.log('===============');
  if (success) {
    console.log('✅ PASSED: Rating authentication flow works correctly');
  } else {
    console.log('❌ FAILED: Rating authentication flow has issues');
  }
  
  process.exit(success ? 0 : 1);
}

// Check if puppeteer is available
try {
  require.resolve('puppeteer');
  main().catch(console.error);
} catch (e) {
  console.log('⚠️  Puppeteer not installed. To run this test, install it with:');
  console.log('npm install puppeteer');
  console.log('\nFor now, please test manually:');
  console.log('1. Navigate to a doctor profile page');
  console.log('2. Click "Rate This Doctor" button');
  console.log('3. Verify you are redirected to patient login with redirectTo parameter');
  console.log('4. Login and verify you are redirected back to the rating page');
}
