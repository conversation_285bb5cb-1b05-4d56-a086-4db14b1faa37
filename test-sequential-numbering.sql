-- TEST SCRIPT: Verify the sequential numbering works correctly
-- Run these commands to test the new sequential numbering system

-- 1. Check what the next ID will be
SELECT get_next_sequential_doctor_id() as next_doctor_id;

-- 2. Check current max ID in both tables
SELECT 
    'doctors' as table_name,
    MAX(doctor_id) as max_id,
    COUNT(*) as total_records
FROM doctors 
WHERE doctor_id IS NOT NULL
UNION ALL
SELECT 
    'doctors_registration' as table_name,
    MAX(doctor_id) as max_id,
    COUNT(*) as total_records
FROM doctors_registration 
WHERE doctor_id IS NOT NULL
ORDER BY table_name;

-- 3. Test insertion (this should get the next sequential ID)
INSERT INTO doctors_registration (
    fullname, 
    email, 
    status
) VALUES (
    'Test Sequential Doctor', 
    '<EMAIL>', 
    'pending'
);

-- 4. Check what ID was assigned
SELECT 
    doctor_id, 
    fullname, 
    email 
FROM doctors_registration 
WHERE fullname = 'Test Sequential Doctor';

-- 5. Verify it's truly sequential (should be max_id + 1)
WITH max_before AS (
    SELECT MAX(doctor_id) as max_id
    FROM (
        SELECT doctor_id FROM doctors WHERE doctor_id IS NOT NULL
        UNION
        SELECT doctor_id FROM doctors_registration WHERE doctor_id IS NOT NULL AND fullname != 'Test Sequential Doctor'
    ) AS all_ids
),
new_record AS (
    SELECT doctor_id
    FROM doctors_registration 
    WHERE fullname = 'Test Sequential Doctor'
)
SELECT 
    max_before.max_id as previous_max_id,
    new_record.doctor_id as new_assigned_id,
    (new_record.doctor_id = max_before.max_id + 1) as is_sequential
FROM max_before, new_record;

-- 6. Clean up test record
DELETE FROM doctors_registration WHERE fullname = 'Test Sequential Doctor';

-- 7. Final verification message
SELECT 'Sequential numbering test completed' as status;
