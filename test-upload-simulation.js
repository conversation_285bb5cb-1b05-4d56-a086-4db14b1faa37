/**
 * Test script to simulate the uploadDoctorProfileImage function call
 * This will help us identify if the issue is in the upload function logic
 */

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Initialize Supabase client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Simulate the updateDoctorRegistrationImage function
async function updateDoctorRegistrationImage(userId, imagePath) {
  console.log("=== updateDoctorRegistrationImage START ===");
  console.log("User ID:", userId);
  console.log("Image path:", imagePath);

  try {
    // Check if the registration record exists
    const { data: existingRecord, error: checkError } = await supabaseAdmin
      .from('doctors_registration')
      .select('doctor_id, auth_id, fullname, profile_image')
      .eq('auth_id', userId)
      .single();

    if (checkError) {
      console.error("Error checking existing registration record:", checkError);
      return { success: false, error: { message: "Registration record not found", details: checkError } };
    }

    console.log("Found registration record:", existingRecord);

    // Update the record
    const updateData = { profile_image: imagePath };
    const { data, error } = await supabaseAdmin
      .from('doctors_registration')
      .update(updateData)
      .eq('auth_id', userId)
      .select();

    if (error) {
      console.error("Error updating doctor registration image:", error);
      return { success: false, error: { message: "Failed to update registration image", details: error } };
    }

    console.log("Successfully updated doctor registration image");
    console.log("=== updateDoctorRegistrationImage SUCCESS ===");
    return { success: true, error: null };
  } catch (e) {
    console.error("Exception in updateDoctorRegistrationImage:", e);
    return { success: false, error: { message: e.message, details: e } };
  }
}

// Simulate the uploadDoctorProfileImage function (simplified version)
async function simulateUploadDoctorProfileImage(userId) {
  console.log("=== SIMULATING uploadDoctorProfileImage ===");
  console.log("User ID:", userId);
  
  const BUCKET_NAME = 'doctor-profiles';
  const SUB_FOLDER = 'real_photos';

  try {
    // Simulate file upload (create a fake path)
    const timestamp = new Date().getTime();
    const fileName = `${userId}-${timestamp}.webp`;
    const filePath = `${SUB_FOLDER}/${fileName}`;
    
    console.log(`Simulated upload path: ${filePath}`);
    
    // Simulate successful upload
    const uploadData = { path: filePath };
    console.log("Simulated upload successful");
    
    // Try to update the doctors_registration table
    console.log("Calling updateDoctorRegistrationImage...");
    const registrationUpdateResult = await updateDoctorRegistrationImage(userId, uploadData.path);
    
    if (registrationUpdateResult.error) {
      console.error("❌ CRITICAL: Failed to update registration image:", registrationUpdateResult.error);
      return { success: false, error: registrationUpdateResult.error };
    } else {
      console.log("✅ SUCCESS: Registration image updated successfully");
      return { success: true, error: null };
    }
    
  } catch (e) {
    console.error("Exception in simulateUploadDoctorProfileImage:", e);
    return { success: false, error: { message: e.message, details: e } };
  }
}

async function testUploadSimulation() {
  console.log('=== TESTING UPLOAD SIMULATION ===\n');

  try {
    // Find a recent registration without an image
    console.log('1. Finding a recent registration without an image...');
    const { data: recentReg, error: fetchError } = await supabaseAdmin
      .from('doctors_registration')
      .select('doctor_id, auth_id, fullname, email, profile_image')
      .is('profile_image', null)
      .order('doctor_id', { ascending: false })
      .limit(1)
      .single();

    if (fetchError || !recentReg) {
      console.error('No registration found without image:', fetchError);
      return;
    }

    console.log('Found registration to test:');
    console.log(`  - ID: ${recentReg.doctor_id}`);
    console.log(`  - Auth ID: ${recentReg.auth_id}`);
    console.log(`  - Name: ${recentReg.fullname}`);

    // Test the simulated upload process
    console.log('\n2. Testing simulated upload process...');
    const result = await simulateUploadDoctorProfileImage(recentReg.auth_id);

    if (result.success) {
      console.log('\n✅ SIMULATION SUCCESSFUL!');
      
      // Verify the result
      console.log('\n3. Verifying the result...');
      const { data: verifyData, error: verifyError } = await supabaseAdmin
        .from('doctors_registration')
        .select('profile_image')
        .eq('auth_id', recentReg.auth_id)
        .single();

      if (verifyError) {
        console.error('Error verifying result:', verifyError);
      } else {
        console.log('Verification result:');
        console.log(`  - Profile image: ${verifyData.profile_image}`);
        if (verifyData.profile_image) {
          console.log('✅ SUCCESS: Image path was saved to database!');
        } else {
          console.log('❌ FAILURE: Image path was NOT saved to database!');
        }
      }

      // Clean up
      console.log('\n4. Cleaning up...');
      await supabaseAdmin
        .from('doctors_registration')
        .update({ profile_image: null })
        .eq('auth_id', recentReg.auth_id);
      console.log('✅ Cleanup complete');

    } else {
      console.log('\n❌ SIMULATION FAILED!');
      console.error('Error:', result.error);
    }

    console.log('\n=== TEST COMPLETE ===');

  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testUploadSimulation().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
