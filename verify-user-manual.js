// Script for manually verifying a user by email or ID
const { createClient } = require('@supabase/supabase-js');

// Create Supabase client
const supabaseUrl = 'https://uapbzzscckhtptliynyj.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcGJ6enNjY2todHB0bGl5bnlqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5ODM5MywiZXhwIjoyMDU2Njc0MzkzfQ.y2M-8bfREe1w_FKP9KBU3M-T95byescooDJywkZ5J6Q';
const supabase = createClient(supabaseUrl, supabaseKey);

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length < 1) {
  console.log('Usage: node verify-user-manual.js [email|id] <value>');
  console.log('Example: node verify-user-manual.<NAME_EMAIL>');
  console.log('Example: node verify-user-manual.js id 1234567');
  process.exit(1);
}

const type = args[0].toLowerCase();
const value = args[1];

if (!['email', 'id'].includes(type) || !value) {
  console.log('Invalid arguments. Use "email" or "id" followed by the value.');
  process.exit(1);
}

async function verifyUser() {
  try {
    console.log(`Attempting to verify user by ${type}: ${value}`);
    
    // Find the user in auth_credentials
    let authQuery = supabase.from('auth_credentials').select('*');
    
    if (type === 'email') {
      authQuery = authQuery.eq('email', value);
    } else if (type === 'id') {
      authQuery = authQuery.eq('user_profile_id', value);
    }
    
    const { data: authData, error: authError } = await authQuery.single();
    
    if (authError || !authData) {
      console.error('Error finding user:', authError || 'No user found');
      return;
    }
    
    console.log('Found user:');
    console.log('  ID:', authData.id);
    console.log('  Email:', authData.email);
    console.log('  User Type:', authData.user_type);
    console.log('  Is Verified:', authData.is_verified || false);
    
    if (authData.is_verified) {
      console.log('User is already verified. No action needed.');
      return;
    }
    
    // Update auth_credentials to mark as verified
    const { data: updateData, error: updateError } = await supabase
      .from('auth_credentials')
      .update({ is_verified: true })
      .eq('id', authData.id)
      .select();
      
    if (updateError) {
      console.error('Error updating auth_credentials:', updateError);
      return;
    }
    
    console.log('Successfully verified user in auth_credentials!');
    
    // Also update the profile table
    const profileId = authData.user_profile_id;
    const userType = authData.user_type;
    
    if (userType === 'doctor') {
      const { error: doctorError } = await supabase
        .from('doctors')
        .update({ verified: true })
        .eq('doctor_id', profileId);
        
      if (doctorError) {
        console.error('Error updating doctor profile:', doctorError);
      } else {
        console.log('Successfully updated doctor profile!');
      }
    } else if (userType === 'patient') {
      const { error: userError } = await supabase
        .from('users')
        .update({ verified: true })
        .eq('user_id', profileId);
        
      if (userError) {
        console.error('Error updating user profile:', userError);
      } else {
        console.log('Successfully updated user profile!');
      }
    }
    
    console.log('Verification complete!');
  } catch (error) {
    console.error('Error in verification process:', error);
  }
}

verifyUser(); 